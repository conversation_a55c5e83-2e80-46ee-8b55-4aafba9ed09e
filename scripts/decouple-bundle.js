const fs = require('fs');
const path = require('path');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generator = require('@babel/generator').default;
const t = require('@babel/types'); // Babel's AST node builder

const INPUT_FILE = '../cli_67.js';
const OUTPUT_DIR = 'output_final';
const MODULE_FACTORY_NAME = 'E';

/**
 * 生成唯一的文件名，避免大小写不敏感的文件系统冲突
 * @param {string} outputDir - 输出目录
 * @param {string} fileName - 原始文件名
 * @returns {string} 唯一的文件名
 */
function getUniqueFileName(outputDir, fileName) {
    const fullPath = path.join(outputDir, fileName);

    // 检查文件是否已存在（大小写不敏感）
    if (!fs.existsSync(fullPath)) {
        return fileName;
    }

    // 如果存在，则在文件名后添加 _d
    const ext = path.extname(fileName);
    const baseName = path.basename(fileName, ext);
    const newFileName = `${baseName}_d${ext}`;

    // 递归检查，以防 _d 版本也存在
    return getUniqueFileName(outputDir, newFileName);
}

/**
 * @typedef {object} ModuleInfo
 * @property {string} name
 * @property {Set<string>} dependencies
 * @property {Set<string>} dependents
 * @property {[number, number]} range
 * @property {object} path - The Babel path object for the module definition's declarator.
 * @property {object} statementPath - The Babel path object for the variable declaration statement.
 */

try {
    // --- 1. 读取和解析源代码 ---
    if (!fs.existsSync(INPUT_FILE)) {
        throw new Error(`Input file not found: ${INPUT_FILE}`);
    }
    const sourceCode = fs.readFileSync(INPUT_FILE, 'utf-8');
    const ast = parser.parse(sourceCode, { sourceType: 'script', errorRecovery: true });

    /** @type {Map<string, ModuleInfo>} */
    const moduleGraph = new Map();

    // --- 2. Pass 1: 识别所有模块定义和变量依赖 ---

    /** @type {Map<string, VariableInfo>} */
    const variableGraph = new Map();

    /**
     * @typedef {object} VariableInfo
     * @property {string} name
     * @property {string} type - 'E_module' | 'F1_import' | 'direct_assignment' | 'property_access'
     * @property {Set<string>} dependencies
     * @property {Set<string>} dependents
     * @property {[number, number]} range
     * @property {object} path - The Babel path object for the variable declarator
     * @property {object} statementPath - The Babel path object for the variable declaration statement
     * @property {string} sourceModule - For F1_import, the module being imported
     */

    traverse(ast, {
        VariableDeclarator(path) {
            const { node } = path;
            const statementPath = path.findParent((p) => p.isVariableDeclaration());
            if (!statementPath || !node.id || node.id.type !== 'Identifier') return;

            const varName = node.id.name;
            let range = null;
            if (statementPath.node.start !== undefined && statementPath.node.end !== undefined) {
                range = [statementPath.node.start, statementPath.node.end];
            } else if (statementPath.node.range) {
                range = statementPath.node.range;
            }

            // 识别 E(xxx) 模块定义
            if (
                node.init &&
                node.init.type === 'CallExpression' &&
                node.init.callee.type === 'Identifier' &&
                node.init.callee.name === MODULE_FACTORY_NAME
            ) {
                console.log(`🔍 Found E() module ${varName}, range: ${range ? `[${range[0]}, ${range[1]}]` : 'undefined'}`);

                moduleGraph.set(varName, {
                    name: varName,
                    dependencies: new Set(),
                    dependents: new Set(),
                    path: path,
                    range: range,
                    statementPath: statementPath,
                });

                variableGraph.set(varName, {
                    name: varName,
                    type: 'E_module',
                    dependencies: new Set(),
                    dependents: new Set(),
                    range: range,
                    path: path,
                    statementPath: statementPath,
                    sourceModule: null
                });
            }
            // 识别 F1(xxx) 导入
            else if (
                node.init &&
                node.init.type === 'CallExpression' &&
                node.init.callee.type === 'Identifier' &&
                node.init.callee.name === 'F1' &&
                node.init.arguments.length > 0 &&
                node.init.arguments[0].type === 'CallExpression' &&
                node.init.arguments[0].callee.type === 'Identifier'
            ) {
                const sourceModule = node.init.arguments[0].callee.name;
                console.log(`🔍 Found F1() import ${varName} = F1(${sourceModule}(), ...), range: ${range ? `[${range[0]}, ${range[1]}]` : 'undefined'}`);

                variableGraph.set(varName, {
                    name: varName,
                    type: 'F1_import',
                    dependencies: new Set([sourceModule]),
                    dependents: new Set(),
                    range: range,
                    path: path,
                    statementPath: statementPath,
                    sourceModule: sourceModule
                });
            }
            // 识别直接赋值 var A = B
            else if (
                node.init &&
                node.init.type === 'Identifier'
            ) {
                const sourceVar = node.init.name;
                console.log(`🔍 Found direct assignment ${varName} = ${sourceVar}, range: ${range ? `[${range[0]}, ${range[1]}]` : 'undefined'}`);

                variableGraph.set(varName, {
                    name: varName,
                    type: 'direct_assignment',
                    dependencies: new Set([sourceVar]),
                    dependents: new Set(),
                    range: range,
                    path: path,
                    statementPath: statementPath,
                    sourceModule: null
                });
            }
            // 识别属性访问 var A = B.something
            else if (
                node.init &&
                node.init.type === 'MemberExpression' &&
                node.init.object.type === 'Identifier'
            ) {
                const sourceVar = node.init.object.name;
                console.log(`🔍 Found property access ${varName} = ${sourceVar}.${node.init.property.name || '?'}, range: ${range ? `[${range[0]}, ${range[1]}]` : 'undefined'}`);

                variableGraph.set(varName, {
                    name: varName,
                    type: 'property_access',
                    dependencies: new Set([sourceVar]),
                    dependents: new Set(),
                    range: range,
                    path: path,
                    statementPath: statementPath,
                    sourceModule: null
                });
            }
        },
    });

    console.log(`🔍 Found ${moduleGraph.size} E() modules and ${variableGraph.size} total variables`);

    // --- 3. Pass 2: 构建变量依赖关系 ---
    // 首先建立变量之间的依赖关系
    for (const [varName, varInfo] of variableGraph.entries()) {
        for (const depName of varInfo.dependencies) {
            const depInfo = variableGraph.get(depName);
            if (depInfo) {
                depInfo.dependents.add(varName);
            }
        }
    }

    // 然后构建 E() 模块之间的依赖图
    if (!moduleGraph || moduleGraph.size === 0) {
        console.warn('⚠️ No E() modules found in moduleGraph');
    } else {
        for (const [moduleName, moduleInfo] of moduleGraph.entries()) {
            if (!moduleInfo) {
                console.warn(`⚠️ moduleInfo is undefined for ${moduleName}`);
                continue;
            }

            if (!moduleInfo.path) {
                console.warn(`⚠️ moduleInfo.path is undefined for ${moduleName}`);
                continue;
            }

            const moduleBodyPath = moduleInfo.path.get('init.arguments.0');
            if (!moduleBodyPath) {
                console.warn(`⚠️ moduleBodyPath is undefined for ${moduleName}`);
                continue;
            }

            try {
                moduleBodyPath.traverse({
                    CallExpression(path) {
                        if (path.node.callee.type === 'Identifier' && moduleGraph.has(path.node.callee.name)) {
                            const dependencyName = path.node.callee.name;
                            if (moduleInfo.dependencies) {
                                moduleInfo.dependencies.add(dependencyName);
                            }
                            const dependencyInfo = moduleGraph.get(dependencyName);
                            if (dependencyInfo && dependencyInfo.dependents) {
                                dependencyInfo.dependents.add(moduleName);
                            }
                        }
                    },
                });
            } catch (error) {
                console.error(`❌ Error traversing module ${moduleName}:`, error.message);
            }
        }
    }

    // --- 4. 构建完整的依赖链 ---
    /**
     * 从 E() 模块开始，向上追踪到最顶层的调用者
     * @param {string} eModuleName - E() 模块名称
     * @returns {Array<string>} 完整的依赖链，从最顶层到 E() 模块
     */
    function buildDependencyChain(eModuleName) {
        const chain = [];
        const visited = new Set();

        function traceUpwards(varName) {
            if (visited.has(varName)) return;
            visited.add(varName);

            const varInfo = variableGraph.get(varName);
            if (!varInfo) return;

            // 如果这个变量有依赖者，继续向上追踪
            if (varInfo.dependents.size > 0) {
                for (const dependent of varInfo.dependents) {
                    traceUpwards(dependent);
                }
            } else {
                // 这是一个顶层变量（没有依赖者）
                chain.push(varName);
            }
        }

        // 从 E() 模块开始，找到所有通过 F1 导入它的变量
        const eModuleInfo = variableGraph.get(eModuleName);
        if (eModuleInfo && eModuleInfo.dependents.size > 0) {
            for (const dependent of eModuleInfo.dependents) {
                const depInfo = variableGraph.get(dependent);
                if (depInfo && depInfo.type === 'F1_import') {
                    traceUpwards(dependent);
                }
            }
        }

        return chain;
    }

    /**
     * 获取从顶层变量到 E() 模块的完整路径
     * @param {string} topLevelVar - 顶层变量名
     * @param {string} targetEModule - 目标 E() 模块名
     * @returns {Array<string>} 从顶层到 E() 模块的路径
     */
    function getPathToEModule(topLevelVar, targetEModule) {
        const path = [];
        const visited = new Set();

        function findPath(currentVar, target) {
            if (visited.has(currentVar)) return false;
            visited.add(currentVar);
            path.push(currentVar);

            if (currentVar === target) return true;

            const varInfo = variableGraph.get(currentVar);
            if (!varInfo) {
                path.pop();
                return false;
            }

            // 检查直接依赖
            for (const dep of varInfo.dependencies) {
                if (findPath(dep, target)) return true;
            }

            path.pop();
            return false;
        }

        if (findPath(topLevelVar, targetEModule)) {
            return path;
        }
        return [];
    }

    // --- 5. 识别包，提取代码，并添加 `module.exports` ---
    // 首先处理传统的 E() 模块依赖
    const allModules = Array.from(moduleGraph.values()).filter(info => info && info.dependents);
    const traditionalEntryPoints = allModules
        .filter(info => info.dependents.size === 0)
        .map(info => info.name);

    console.log(`📦 Found ${traditionalEntryPoints.length} traditional E() entry points:`, traditionalEntryPoints);

    // 然后处理依赖链：找到所有通过 F1 导入 E() 模块的顶层变量
    const dependencyChains = new Map(); // Map<topLevelVar, {eModule, chain}>

    // 遍历所有 E() 模块，找到通过 F1 导入它们的依赖链
    for (const [eModuleName] of moduleGraph.entries()) {
        const chains = buildDependencyChain(eModuleName);
        for (const topLevelVar of chains) {
            const path = getPathToEModule(topLevelVar, eModuleName);
            if (path.length > 1) { // 确保有依赖链
                dependencyChains.set(topLevelVar, {
                    eModule: eModuleName,
                    chain: path
                });
                console.log(`🔗 Found dependency chain: ${path.join(' → ')}`);
            }
        }
    }

    console.log(`📦 Found ${dependencyChains.size} dependency chains with top-level variables`);

    const visited = new Set();
    const modulesToReplace = new Map(); // Map<entryPoint, allMembers>
    const chainPackages = new Map(); // Map<topLevelVar, chainMembers>

    if (!fs.existsSync(OUTPUT_DIR)) {
        fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    }

    // 处理传统的 E() 模块包
    if (traditionalEntryPoints && traditionalEntryPoints.length > 0) {
        traditionalEntryPoints.forEach(entryName => {
            if (!entryName) {
                console.warn('⚠️ Entry name is undefined, skipping');
                return;
            }

            const packageMembers = new Set();
            (function findPackageMembers(name) {
                if (visited.has(name) || !moduleGraph.has(name)) return;
                visited.add(name);
                packageMembers.add(name);
                const moduleInfo = moduleGraph.get(name);
                if (moduleInfo && moduleInfo.dependencies) {
                    try {
                        moduleInfo.dependencies.forEach(dep => findPackageMembers(dep));
                    } catch (error) {
                        console.error(`❌ Error processing dependencies for ${name}:`, error.message);
                    }
                }
            })(entryName);

            if (packageMembers.size > 0) {
                modulesToReplace.set(entryName, packageMembers);
                generatePackageFile(entryName, packageMembers, 'E_module');
            }
        });
    } else {
        console.warn('⚠️ No traditional E() entry points found');
    }

    // 处理依赖链包
    for (const [topLevelVar, chainInfo] of dependencyChains.entries()) {
        const chainMembers = new Set();

        // 将依赖链中的所有变量（除了最顶层的）添加到包中
        for (let i = 1; i < chainInfo.chain.length; i++) {
            const varName = chainInfo.chain[i];
            const varInfo = variableGraph.get(varName);
            if (varInfo) {
                chainMembers.add(varName);
            }
        }

        // 添加 E() 模块及其依赖
        const eModuleInfo = moduleGraph.get(chainInfo.eModule);
        if (eModuleInfo) {
            chainMembers.add(chainInfo.eModule);
            // 递归添加 E() 模块的依赖
            (function addEModuleDeps(name) {
                const moduleInfo = moduleGraph.get(name);
                if (moduleInfo && moduleInfo.dependencies) {
                    moduleInfo.dependencies.forEach(dep => {
                        if (!chainMembers.has(dep)) {
                            chainMembers.add(dep);
                            addEModuleDeps(dep);
                        }
                    });
                }
            })(chainInfo.eModule);
        }

        if (chainMembers.size > 0) {
            chainPackages.set(topLevelVar, chainMembers);
            generatePackageFile(topLevelVar, chainMembers, 'dependency_chain');
        }
    }

    /**
     * 生成包文件的通用函数
     * @param {string} entryName - 包的入口名称
     * @param {Set<string>} members - 包成员
     * @param {string} packageType - 包类型：'E_module' 或 'dependency_chain'
     */
    function generatePackageFile(entryName, members, packageType) {
        const sortedMembers = Array.from(members).sort();

        console.log(`� ${packageType} package '${entryName}' contains ${sortedMembers.length} members:`, sortedMembers);

        let packageCode = `// ${packageType} package extracted with entry point: ${entryName}\n\n`;

        if (sortedMembers && sortedMembers.length > 0) {
            sortedMembers.forEach(memberName => {
                if (!memberName) {
                    console.warn('⚠️ Member name is undefined, skipping');
                    return;
                }

                // 尝试从 E() 模块获取代码
                const moduleInfo = moduleGraph.get(memberName);
                if (moduleInfo) {
                    let codeToAdd = extractCodeFromInfo(moduleInfo, memberName);
                    if (codeToAdd) {
                        packageCode += codeToAdd;
                        if (!codeToAdd.endsWith(';') && !codeToAdd.endsWith('\n')) {
                            packageCode += ';\n';
                        } else if (!codeToAdd.endsWith('\n')) {
                            packageCode += '\n';
                        }
                        return;
                    }
                }

                // 尝试从变量图获取代码
                const varInfo = variableGraph.get(memberName);
                if (varInfo) {
                    let codeToAdd = extractCodeFromInfo(varInfo, memberName);
                    if (codeToAdd) {
                        packageCode += codeToAdd;
                        if (!codeToAdd.endsWith(';') && !codeToAdd.endsWith('\n')) {
                            packageCode += ';\n';
                        } else if (!codeToAdd.endsWith('\n')) {
                            packageCode += '\n';
                        }
                    } else {
                        console.warn(`⚠️ Could not extract code for variable ${memberName}`);
                    }
                } else {
                    console.warn(`⚠️ Variable info not found for ${memberName}`);
                }
            });
        }

        // ⭐ 关键步骤：在包的末尾添加导出语句
        packageCode += `\nmodule.exports = ${entryName};\n`;

        const originalPackageFileName = `package_${entryName}.js`;
        const packageFileName = getUniqueFileName(OUTPUT_DIR, originalPackageFileName);
        fs.writeFileSync(path.join(OUTPUT_DIR, packageFileName), packageCode, 'utf-8');
        console.log(`✅ Extracted ${packageType} package '${entryName}' to '${path.join(OUTPUT_DIR, packageFileName)}'`);
    }

    /**
     * 从模块或变量信息中提取代码
     * @param {object} info - 模块或变量信息
     * @param {string} name - 名称
     * @returns {string} 提取的代码
     */
    function extractCodeFromInfo(info, name) {
        let codeToAdd = '';

        // 尝试多种方式获取代码
        if (info.range && Array.isArray(info.range) && info.range.length >= 2) {
            const [start, end] = info.range;
            if (typeof start === 'number' && typeof end === 'number') {
                codeToAdd = sourceCode.substring(start, end);
                console.log(`📄 Extracted code for ${name} using range [${start}, ${end}]`);
            }
        }

        // 如果range方式失败，尝试从AST生成代码
        if (!codeToAdd && info.statementPath) {
            try {
                const { code } = generator(info.statementPath.node, {
                    retainLines: false,
                    compact: false,
                });
                codeToAdd = code;
                console.log(`📄 Generated code for ${name} from AST`);
            } catch (error) {
                console.error(`❌ Error generating code for ${name}:`, error.message);
            }
        }

        return codeToAdd;
    }

    // --- 6. 修改主 AST：用 `require()` 替换整个包的根入口定义 ---
    // 首先收集所有需要删除的模块路径
    const pathsToRemove = new Set();

    traverse(ast, {
        VariableDeclarator(path) {
            const { node } = path;

            // 处理 E() 模块
            if (
                node.init &&
                node.init.type === 'CallExpression' &&
                node.init.callee.type === 'Identifier' &&
                node.init.callee.name === MODULE_FACTORY_NAME &&
                node.id.type === 'Identifier'
            ) {
                const moduleName = node.id.name;

                // 检查这个模块是不是我们确定的一个包的入口点
                if (modulesToReplace && modulesToReplace.has(moduleName)) {
                    const packageFileName = `./package_${moduleName}.js`;

                    // ⭐ 关键步骤：构建 `require('./package_xxx.js')` AST 节点
                    const requireCall = t.callExpression(t.identifier('require'), [t.stringLiteral(packageFileName)]);

                    // 用 `require` 调用替换原来的 `E(...)` 调用
                    const initPath = path.get('init');
                    if (initPath) {
                        initPath.replaceWith(requireCall);
                        console.log(`🔄 Replaced E() module ${moduleName} with require('${packageFileName}')`);
                    }

                    // 标记这个包里的其他成员需要删除
                    const membersToRemove = modulesToReplace.get(moduleName);
                    if (membersToRemove && typeof membersToRemove.forEach === 'function') {
                        try {
                            membersToRemove.forEach(memberName => {
                                if (memberName && memberName !== moduleName) { // 不要删除入口自身，因为它要被替换
                                    const memberInfo = moduleGraph.get(memberName);
                                    if (memberInfo && memberInfo.path) {
                                        const statementPath = memberInfo.path.findParent((p) => p.isVariableDeclaration());
                                        if (statementPath) {
                                            pathsToRemove.add(statementPath);
                                        }
                                    }
                                }
                            });
                        } catch (error) {
                            console.error(`❌ Error processing members to remove for ${moduleName}:`, error.message);
                        }
                    }
                } else if (modulesToReplace) {
                    // 检查这个模块是否属于某个包的成员（非入口点）
                    try {
                        for (const [entryName, members] of modulesToReplace.entries()) {
                            if (members && typeof members.has === 'function' && members.has(moduleName) && moduleName !== entryName) {
                                const statementPath = path.findParent((p) => p.isVariableDeclaration());
                                if (statementPath) {
                                    pathsToRemove.add(statementPath);
                                }
                                break;
                            }
                        }
                    } catch (error) {
                        console.error(`❌ Error checking module membership for ${moduleName}:`, error.message);
                    }
                }
            }

            // 处理依赖链变量
            if (node.id && node.id.type === 'Identifier') {
                const varName = node.id.name;

                // 检查这个变量是不是依赖链的顶层变量
                if (chainPackages.has(varName)) {
                    const packageFileName = `./package_${varName}.js`;

                    // ⭐ 关键步骤：构建 `require('./package_xxx.js')` AST 节点
                    const requireCall = t.callExpression(t.identifier('require'), [t.stringLiteral(packageFileName)]);

                    // 用 `require` 调用替换原来的变量初始化
                    const initPath = path.get('init');
                    if (initPath) {
                        initPath.replaceWith(requireCall);
                        console.log(`🔄 Replaced dependency chain variable ${varName} with require('${packageFileName}')`);
                    }

                    // 标记这个依赖链里的其他成员需要删除
                    const membersToRemove = chainPackages.get(varName);
                    if (membersToRemove && typeof membersToRemove.forEach === 'function') {
                        try {
                            membersToRemove.forEach(memberName => {
                                if (memberName && memberName !== varName) { // 不要删除顶层变量自身，因为它要被替换
                                    // 尝试从变量图中获取信息
                                    const varInfo = variableGraph.get(memberName);
                                    if (varInfo && varInfo.path) {
                                        const statementPath = varInfo.path.findParent((p) => p.isVariableDeclaration());
                                        if (statementPath) {
                                            pathsToRemove.add(statementPath);
                                        }
                                    }

                                    // 也尝试从模块图中获取信息（如果是 E() 模块）
                                    const moduleInfo = moduleGraph.get(memberName);
                                    if (moduleInfo && moduleInfo.path) {
                                        const statementPath = moduleInfo.path.findParent((p) => p.isVariableDeclaration());
                                        if (statementPath) {
                                            pathsToRemove.add(statementPath);
                                        }
                                    }
                                }
                            });
                        } catch (error) {
                            console.error(`❌ Error processing chain members to remove for ${varName}:`, error.message);
                        }
                    }
                } else if (chainPackages.size > 0) {
                    // 检查这个变量是否属于某个依赖链的成员（非顶层变量）
                    try {
                        for (const [topLevelVar, members] of chainPackages.entries()) {
                            if (members && typeof members.has === 'function' && members.has(varName) && varName !== topLevelVar) {
                                const statementPath = path.findParent((p) => p.isVariableDeclaration());
                                if (statementPath) {
                                    pathsToRemove.add(statementPath);
                                }
                                break;
                            }
                        }
                    } catch (error) {
                        console.error(`❌ Error checking variable membership for ${varName}:`, error.message);
                    }
                }
            }
        },
    });

    // 删除标记的路径
    if (pathsToRemove && typeof pathsToRemove.forEach === 'function') {
        try {
            pathsToRemove.forEach(pathToRemove => {
                if (pathToRemove && !pathToRemove.removed) {
                    pathToRemove.remove();
                }
            });
        } catch (error) {
            console.error('❌ Error removing paths:', error.message);
        }
    }

    // --- 6. 生成新的主文件 ---
    const { code: newMainCode } = generator(ast, {
        retainLines: false, // 让代码更紧凑
        compact: false,
    });

    const originalMainFileName = 'main.js';
    const mainFileName = getUniqueFileName(OUTPUT_DIR, originalMainFileName);
    fs.writeFileSync(path.join(OUTPUT_DIR, mainFileName), newMainCode, 'utf-8');
    console.log(`✅ Created new decoupled main file at '${path.join(OUTPUT_DIR, mainFileName)}'`);

    // --- 生成依赖图 JSON 文件 ---
    const allEntryPoints = [...traditionalEntryPoints, ...Array.from(dependencyChains.keys())];
    const dependencyGraph = {
        metadata: {
            totalModules: moduleGraph ? moduleGraph.size : 0,
            traditionalEntryPoints: traditionalEntryPoints ? traditionalEntryPoints.length : 0,
            dependencyChains: dependencyChains ? dependencyChains.size : 0,
            totalEntryPoints: allEntryPoints.length,
            packagesCreated: (modulesToReplace ? modulesToReplace.size : 0) + (chainPackages ? chainPackages.size : 0),
            generatedAt: new Date().toISOString(),
        },
        modules: {},
        packages: {},
        traditionalEntryPoints: traditionalEntryPoints || [],
        dependencyChains: Array.from(dependencyChains.entries()).map(([topVar, info]) => ({
            topLevelVariable: topVar,
            targetEModule: info.eModule,
            chain: info.chain
        })),
    };

    // 添加所有模块信息
    if (moduleGraph) {
        for (const [moduleName, moduleInfo] of moduleGraph.entries()) {
            dependencyGraph.modules[moduleName] = {
                name: moduleName,
                dependencies: Array.from(moduleInfo.dependencies || []),
                dependents: Array.from(moduleInfo.dependents || []),
                isEntryPoint: traditionalEntryPoints ? traditionalEntryPoints.includes(moduleName) : false,
                packageName: null, // 稍后填充
            };
        }
    }

    // 添加包信息
    if (modulesToReplace) {
        for (const [entryName, members] of modulesToReplace.entries()) {
            const membersList = Array.from(members || []);
            dependencyGraph.packages[entryName] = {
                entryPoint: entryName,
                members: membersList,
                memberCount: membersList.length,
                fileName: `package_${entryName}.js`,
            };

            // 更新模块信息中的包名
            membersList.forEach(memberName => {
                if (dependencyGraph.modules[memberName]) {
                    dependencyGraph.modules[memberName].packageName = entryName;
                }
            });
        }
    }

    // 计算一些统计信息
    const stats = {
        totalDependencyCount: 0,
        averageDependenciesPerModule: 0,
        maxDependencies: 0,
        moduleWithMostDependencies: null,
    };

    let totalDeps = 0;
    let maxDeps = 0;
    let maxDepsModule = null;

    Object.values(dependencyGraph.modules).forEach(module => {
        const depCount = module.dependencies.length;
        totalDeps += depCount;
        if (depCount > maxDeps) {
            maxDeps = depCount;
            maxDepsModule = module.name;
        }
    });

    stats.totalDependencyCount = totalDeps;
    stats.averageDependenciesPerModule = Object.keys(dependencyGraph.modules).length > 0 
        ? (totalDeps / Object.keys(dependencyGraph.modules).length).toFixed(2) 
        : 0;
    stats.maxDependencies = maxDeps;
    stats.moduleWithMostDependencies = maxDepsModule;

    dependencyGraph.statistics = stats;

    const originalDependencyGraphFileName = 'dependency-graph.json';
    const dependencyGraphFileName = getUniqueFileName(OUTPUT_DIR, originalDependencyGraphFileName);
    fs.writeFileSync(
        path.join(OUTPUT_DIR, dependencyGraphFileName),
        JSON.stringify(dependencyGraph, null, 2),
        'utf-8'
    );
    console.log(`✅ Generated dependency graph at '${path.join(OUTPUT_DIR, dependencyGraphFileName)}'`);

    // --- 7. 输出统计信息 ---
    console.log('\n📊 Summary:');
    console.log(`- Total E() modules found: ${moduleGraph ? moduleGraph.size : 0}`);
    console.log(`- Total variables found: ${variableGraph ? variableGraph.size : 0}`);
    console.log(`- Traditional E() entry points: ${traditionalEntryPoints ? traditionalEntryPoints.length : 0}`);
    console.log(`- Dependency chains found: ${dependencyChains ? dependencyChains.size : 0}`);
    console.log(`- Total entry points: ${allEntryPoints.length}`);
    console.log(`- Traditional packages created: ${modulesToReplace ? modulesToReplace.size : 0}`);
    console.log(`- Dependency chain packages created: ${chainPackages ? chainPackages.size : 0}`);

    let totalExtracted = 0;
    if (modulesToReplace && typeof modulesToReplace.forEach === 'function') {
        try {
            modulesToReplace.forEach((members) => {
                if (members && typeof members.size === 'number') {
                    totalExtracted += members.size;
                }
            });
        } catch (error) {
            console.error('❌ Error calculating total extracted:', error.message);
        }
    }

    let totalChainExtracted = 0;
    if (chainPackages && typeof chainPackages.forEach === 'function') {
        try {
            chainPackages.forEach((members) => {
                if (members && typeof members.size === 'number') {
                    totalChainExtracted += members.size;
                }
            });
        } catch (error) {
            console.error('❌ Error calculating total chain extracted:', error.message);
        }
    }

    console.log(`- Total traditional modules extracted: ${totalExtracted}`);
    console.log(`- Total chain variables extracted: ${totalChainExtracted}`);
    const remainingModules = (moduleGraph ? moduleGraph.size : 0) - totalExtracted + (traditionalEntryPoints ? traditionalEntryPoints.length : 0);
    const remainingVariables = (variableGraph ? variableGraph.size : 0) - totalChainExtracted + (dependencyChains ? dependencyChains.size : 0);
    console.log(`- Remaining E() modules in main: ${remainingModules}`);
    console.log(`- Remaining variables in main: ${remainingVariables}`);

} catch (e) {
    console.error('❌ An error occurred during the decoupling process:', e);
    console.error('Stack trace:', e.stack);
}