// dependency_chain package extracted with entry point: qv

var A8B=E((Fn5,e6B)=>{var hC0=AJ(),CP6=NE(),t6B=Z71(),KP6=(A,B)=>{A=new CP6(A,B);let Q=new hC0("0.0.0");if(A.test(Q))return Q;if(Q=new hC0("0.0.0-0"),A.test(Q))return Q;Q=null;for(let D=0;D<A.set.length;++D){let Z=A.set[D],G=null;if(Z.forEach((F)=>{let I=new hC0(F.semver.version);switch(F.operator){case">":if(I.prerelease.length===0)I.patch++;else I.prerelease.push(0);I.raw=I.format();case"":case">=":if(!G||t6B(I,G))G=I;break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${F.operator}`)}}),G&&(!Q||t6B(Q,G)))Q=G}if(Q&&A.test(Q))return Q;return null};e6B.exports=KP6});
var AJ=E((kg5,i0B)=>{var oP1=B71(),{MAX_LENGTH:c0B,MAX_SAFE_INTEGER:tP1}=Q71(),{safeRe:l0B,safeSrc:p0B,t:eP1}=St(),Lw6=rP1(),{compareIdentifiers:jt}=HX0();class Q${constructor(A,B){if(B=Lw6(B),A instanceof Q$)if(A.loose===!!B.loose&&A.includePrerelease===!!B.includePrerelease)return A;else A=A.version;else if(typeof A!=="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof A}".`);if(A.length>c0B)throw new TypeError(`version is longer than ${c0B} characters`);oP1("SemVer",A,B),this.options=B,this.loose=!!B.loose,this.includePrerelease=!!B.includePrerelease;let Q=A.trim().match(B.loose?l0B[eP1.LOOSE]:l0B[eP1.FULL]);if(!Q)throw new TypeError(`Invalid Version: ${A}`);if(this.raw=A,this.major=+Q[1],this.minor=+Q[2],this.patch=+Q[3],this.major>tP1||this.major<0)throw new TypeError("Invalid major version");if(this.minor>tP1||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>tP1||this.patch<0)throw new TypeError("Invalid patch version");if(!Q[4])this.prerelease=[];else this.prerelease=Q[4].split(".").map((D)=>{if(/^[0-9]+$/.test(D)){let Z=+D;if(Z>=0&&Z<tP1)return Z}return D});this.build=Q[5]?Q[5].split("."):[],this.format()}format(){if(this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length)this.version+=`-${this.prerelease.join(".")}`;return this.version}toString(){return this.version}compare(A){if(oP1("SemVer.compare",this.version,this.options,A),!(A instanceof Q$)){if(typeof A==="string"&&A===this.version)return 0;A=new Q$(A,this.options)}if(A.version===this.version)return 0;return this.compareMain(A)||this.comparePre(A)}compareMain(A){if(!(A instanceof Q$))A=new Q$(A,this.options);return jt(this.major,A.major)||jt(this.minor,A.minor)||jt(this.patch,A.patch)}comparePre(A){if(!(A instanceof Q$))A=new Q$(A,this.options);if(this.prerelease.length&&!A.prerelease.length)return-1;else if(!this.prerelease.length&&A.prerelease.length)return 1;else if(!this.prerelease.length&&!A.prerelease.length)return 0;let B=0;do{let Q=this.prerelease[B],D=A.prerelease[B];if(oP1("prerelease compare",B,Q,D),Q===void 0&&D===void 0)return 0;else if(D===void 0)return 1;else if(Q===void 0)return-1;else if(Q===D)continue;else return jt(Q,D)}while(++B)}compareBuild(A){if(!(A instanceof Q$))A=new Q$(A,this.options);let B=0;do{let Q=this.build[B],D=A.build[B];if(oP1("build compare",B,Q,D),Q===void 0&&D===void 0)return 0;else if(D===void 0)return 1;else if(Q===void 0)return-1;else if(Q===D)continue;else return jt(Q,D)}while(++B)}inc(A,B,Q){if(A.startsWith("pre")){if(!B&&Q===!1)throw new Error("invalid increment argument: identifier is empty");if(B){let D=new RegExp(`^${this.options.loose?p0B[eP1.PRERELEASELOOSE]:p0B[eP1.PRERELEASE]}$`),Z=`-${B}`.match(D);if(!Z||Z[1]!==B)throw new Error(`invalid identifier: ${B}`)}}switch(A){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",B,Q);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",B,Q);break;case"prepatch":this.prerelease.length=0,this.inc("patch",B,Q),this.inc("pre",B,Q);break;case"prerelease":if(this.prerelease.length===0)this.inc("patch",B,Q);this.inc("pre",B,Q);break;case"release":if(this.prerelease.length===0)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":if(this.minor!==0||this.patch!==0||this.prerelease.length===0)this.major++;this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":if(this.patch!==0||this.prerelease.length===0)this.minor++;this.patch=0,this.prerelease=[];break;case"patch":if(this.prerelease.length===0)this.patch++;this.prerelease=[];break;case"pre":{let D=Number(Q)?1:0;if(this.prerelease.length===0)this.prerelease=[D];else{let Z=this.prerelease.length;while(--Z>=0)if(typeof this.prerelease[Z]==="number")this.prerelease[Z]++,Z=-2;if(Z===-1){if(B===this.prerelease.join(".")&&Q===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(D)}}if(B){let Z=[B,D];if(Q===!1)Z=[B];if(jt(this.prerelease[0],B)===0){if(isNaN(this.prerelease[1]))this.prerelease=Z}else this.prerelease=Z}break}default:throw new Error(`invalid increment argument: ${A}`)}if(this.raw=this.format(),this.build.length)this.raw+=`+${this.build.join(".")}`;return this}}i0B.exports=Q$});
var B71=E((Tg5,b0B)=>{var Iw6=typeof process==="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...A)=>console.error("SEMVER",...A):()=>{};b0B.exports=Iw6});
var BAB=E((bg5,AAB)=>{class e0B{constructor(){this.max=1000,this.map=new Map}get(A){let B=this.map.get(A);if(B===void 0)return;else return this.map.delete(A),this.map.set(A,B),B}delete(A){return this.map.delete(A)}set(A,B){if(!this.delete(A)&&B!==void 0){if(this.map.size>=this.max){let D=this.map.keys().next().value;this.delete(D)}this.map.set(A,B)}return this}}AAB.exports=e0B});
var C8B=E((Xn5,V8B)=>{var X8B=NE(),PP6=(A,B,Q)=>{return A=new X8B(A,Q),B=new X8B(B,Q),A.intersects(B,Q)};V8B.exports=PP6});
var D71=E((vg5,t0B)=>{var Sw6=qE(),jw6=(A,B,Q)=>Sw6(A,B,Q)>=0;t0B.exports=jw6});
var DS1=E((mg5,FAB)=>{var gw6=qE(),uw6=(A,B,Q)=>gw6(A,B,Q)<=0;FAB.exports=uw6});
var EX0=E((fg5,QAB)=>{var kw6=qE(),yw6=(A,B,Q)=>kw6(A,B,Q)===0;QAB.exports=yw6});
var F71=E((cg5,CAB)=>{var G71=Symbol("SemVer ANY");class ZS1{static get ANY(){return G71}constructor(A,B){if(B=YAB(B),A instanceof ZS1)if(A.loose===!!B.loose)return A;else A=A.value;if(A=A.trim().split(/\s+/).join(" "),qX0("comparator",A,B),this.options=B,this.loose=!!B.loose,this.parse(A),this.semver===G71)this.value="";else this.value=this.operator+this.semver.version;qX0("comp",this)}parse(A){let B=this.options.loose?WAB[JAB.COMPARATORLOOSE]:WAB[JAB.COMPARATOR],Q=A.match(B);if(!Q)throw new TypeError(`Invalid comparator: ${A}`);if(this.operator=Q[1]!==void 0?Q[1]:"",this.operator==="=")this.operator="";if(!Q[2])this.semver=G71;else this.semver=new XAB(Q[2],this.options.loose)}toString(){return this.value}test(A){if(qX0("Comparator.test",A,this.options.loose),this.semver===G71||A===G71)return!0;if(typeof A==="string")try{A=new XAB(A,this.options)}catch(B){return!1}return $X0(A,this.operator,this.semver,this.options)}intersects(A,B){if(!(A instanceof ZS1))throw new TypeError("a Comparator is required");if(this.operator===""){if(this.value==="")return!0;return new VAB(A.value,B).test(this.value)}else if(A.operator===""){if(A.value==="")return!0;return new VAB(this.value,B).test(A.semver)}if(B=YAB(B),B.includePrerelease&&(this.value==="<0.0.0-0"||A.value==="<0.0.0-0"))return!1;if(!B.includePrerelease&&(this.value.startsWith("<0.0.0")||A.value.startsWith("<0.0.0")))return!1;if(this.operator.startsWith(">")&&A.operator.startsWith(">"))return!0;if(this.operator.startsWith("<")&&A.operator.startsWith("<"))return!0;if(this.semver.version===A.semver.version&&this.operator.includes("=")&&A.operator.includes("="))return!0;if($X0(this.semver,"<",A.semver,B)&&this.operator.startsWith(">")&&A.operator.startsWith("<"))return!0;if($X0(this.semver,">",A.semver,B)&&this.operator.startsWith("<")&&A.operator.startsWith(">"))return!0;return!1}}CAB.exports=ZS1;var YAB=rP1(),{safeRe:WAB,t:JAB}=St(),$X0=wX0(),qX0=B71(),XAB=AJ(),VAB=NE()});
var H8B=E((Vn5,K8B)=>{var SP6=kt(),jP6=qE();K8B.exports=(A,B,Q)=>{let D=[],Z=null,G=null,F=A.sort((J,X)=>jP6(J,X,Q));for(let J of F)if(SP6(J,B,Q)){if(G=J,!Z)Z=J}else{if(G)D.push([Z,G]);G=null,Z=null}if(Z)D.push([Z,null]);let I=[];for(let[J,X]of D)if(J===X)I.push(J);else if(!X&&J===F[0])I.push("*");else if(!X)I.push(`>=${J}`);else if(J===F[0])I.push(`<=${X}`);else I.push(`${J} - ${X}`);let Y=I.join(" || "),W=typeof B.raw==="string"?B.raw:String(B);return Y.length<W.length?Y:B}});
var HX0=E((jg5,d0B)=>{var u0B=/^[0-9]+$/,m0B=(A,B)=>{let Q=u0B.test(A),D=u0B.test(B);if(Q&&D)A=+A,B=+B;return A===B?0:Q&&!D?-1:D&&!Q?1:A<B?-1:1},Nw6=(A,B)=>m0B(B,A);d0B.exports={compareIdentifiers:m0B,rcompareIdentifiers:Nw6}});
var J8B=E((Jn5,W8B)=>{var OP6=gj1(),TP6=(A,B,Q)=>OP6(A,B,"<",Q);W8B.exports=TP6});
var M6B=E((ii5,L6B)=>{var N6B=AJ(),uT6=(A,B,Q,D,Z)=>{if(typeof Q==="string")Z=D,D=Q,Q=void 0;try{return new N6B(A instanceof N6B?A.version:A,Q).inc(B,D,Z).version}catch(G){return null}};L6B.exports=uT6});
var NE=E((lg5,EAB)=>{var aw6=/\s+/g;class I71{constructor(A,B){if(B=rw6(B),A instanceof I71)if(A.loose===!!B.loose&&A.includePrerelease===!!B.includePrerelease)return A;else return new I71(A.raw,B);if(A instanceof NX0)return this.raw=A.value,this.set=[[A]],this.formatted=void 0,this;if(this.options=B,this.loose=!!B.loose,this.includePrerelease=!!B.includePrerelease,this.raw=A.trim().replace(aw6," "),this.set=this.raw.split("||").map((Q)=>this.parseRange(Q.trim())).filter((Q)=>Q.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let Q=this.set[0];if(this.set=this.set.filter((D)=>!HAB(D[0])),this.set.length===0)this.set=[Q];else if(this.set.length>1){for(let D of this.set)if(D.length===1&&D$6(D[0])){this.set=[D];break}}}this.formatted=void 0}get range(){if(this.formatted===void 0){this.formatted="";for(let A=0;A<this.set.length;A++){if(A>0)this.formatted+="||";let B=this.set[A];for(let Q=0;Q<B.length;Q++){if(Q>0)this.formatted+=" ";this.formatted+=B[Q].toString().trim()}}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(A){let Q=((this.options.includePrerelease&&B$6)|(this.options.loose&&Q$6))+":"+A,D=KAB.get(Q);if(D)return D;let Z=this.options.loose,G=Z?_V[oJ.HYPHENRANGELOOSE]:_V[oJ.HYPHENRANGE];A=A.replace(G,C$6(this.options.includePrerelease)),N7("hyphen replace",A),A=A.replace(_V[oJ.COMPARATORTRIM],tw6),N7("comparator trim",A),A=A.replace(_V[oJ.TILDETRIM],ew6),N7("tilde trim",A),A=A.replace(_V[oJ.CARETTRIM],A$6),N7("caret trim",A);let F=A.split(" ").map((J)=>Z$6(J,this.options)).join(" ").split(/\s+/).map((J)=>V$6(J,this.options));if(Z)F=F.filter((J)=>{return N7("loose invalid filter",J,this.options),!!J.match(_V[oJ.COMPARATORLOOSE])});N7("range list",F);let I=new Map,Y=F.map((J)=>new NX0(J,this.options));for(let J of Y){if(HAB(J))return[J];I.set(J.value,J)}if(I.size>1&&I.has(""))I.delete("");let W=[...I.values()];return KAB.set(Q,W),W}intersects(A,B){if(!(A instanceof I71))throw new TypeError("a Range is required");return this.set.some((Q)=>{return zAB(Q,B)&&A.set.some((D)=>{return zAB(D,B)&&Q.every((Z)=>{return D.every((G)=>{return Z.intersects(G,B)})})})})}test(A){if(!A)return!1;if(typeof A==="string")try{A=new ow6(A,this.options)}catch(B){return!1}for(let B=0;B<this.set.length;B++)if(K$6(this.set[B],A,this.options))return!0;return!1}}EAB.exports=I71;var sw6=BAB(),KAB=new sw6,rw6=rP1(),NX0=F71(),N7=B71(),ow6=AJ(),{safeRe:_V,t:oJ,comparatorTrimReplace:tw6,tildeTrimReplace:ew6,caretTrimReplace:A$6}=St(),{FLAG_INCLUDE_PRERELEASE:B$6,FLAG_LOOSE:Q$6}=Q71(),HAB=(A)=>A.value==="<0.0.0-0",D$6=(A)=>A.value==="",zAB=(A,B)=>{let Q=!0,D=A.slice(),Z=D.pop();while(Q&&D.length)Q=D.every((G)=>{return Z.intersects(G,B)}),Z=D.pop();return Q},Z$6=(A,B)=>{return N7("comp",A,B),A=I$6(A,B),N7("caret",A),A=G$6(A,B),N7("tildes",A),A=W$6(A,B),N7("xrange",A),A=X$6(A,B),N7("stars",A),A},tJ=(A)=>!A||A.toLowerCase()==="x"||A==="*",G$6=(A,B)=>{return A.trim().split(/\s+/).map((Q)=>F$6(Q,B)).join(" ")},F$6=(A,B)=>{let Q=B.loose?_V[oJ.TILDELOOSE]:_V[oJ.TILDE];return A.replace(Q,(D,Z,G,F,I)=>{N7("tilde",A,D,Z,G,F,I);let Y;if(tJ(Z))Y="";else if(tJ(G))Y=`>=${Z}.0.0 <${+Z+1}.0.0-0`;else if(tJ(F))Y=`>=${Z}.${G}.0 <${Z}.${+G+1}.0-0`;else if(I)N7("replaceTilde pr",I),Y=`>=${Z}.${G}.${F}-${I} <${Z}.${+G+1}.0-0`;else Y=`>=${Z}.${G}.${F} <${Z}.${+G+1}.0-0`;return N7("tilde return",Y),Y})},I$6=(A,B)=>{return A.trim().split(/\s+/).map((Q)=>Y$6(Q,B)).join(" ")},Y$6=(A,B)=>{N7("caret",A,B);let Q=B.loose?_V[oJ.CARETLOOSE]:_V[oJ.CARET],D=B.includePrerelease?"-0":"";return A.replace(Q,(Z,G,F,I,Y)=>{N7("caret",A,Z,G,F,I,Y);let W;if(tJ(G))W="";else if(tJ(F))W=`>=${G}.0.0${D} <${+G+1}.0.0-0`;else if(tJ(I))if(G==="0")W=`>=${G}.${F}.0${D} <${G}.${+F+1}.0-0`;else W=`>=${G}.${F}.0${D} <${+G+1}.0.0-0`;else if(Y)if(N7("replaceCaret pr",Y),G==="0")if(F==="0")W=`>=${G}.${F}.${I}-${Y} <${G}.${F}.${+I+1}-0`;else W=`>=${G}.${F}.${I}-${Y} <${G}.${+F+1}.0-0`;else W=`>=${G}.${F}.${I}-${Y} <${+G+1}.0.0-0`;else if(N7("no pr"),G==="0")if(F==="0")W=`>=${G}.${F}.${I}${D} <${G}.${F}.${+I+1}-0`;else W=`>=${G}.${F}.${I}${D} <${G}.${+F+1}.0-0`;else W=`>=${G}.${F}.${I} <${+G+1}.0.0-0`;return N7("caret return",W),W})},W$6=(A,B)=>{return N7("replaceXRanges",A,B),A.split(/\s+/).map((Q)=>J$6(Q,B)).join(" ")},J$6=(A,B)=>{A=A.trim();let Q=B.loose?_V[oJ.XRANGELOOSE]:_V[oJ.XRANGE];return A.replace(Q,(D,Z,G,F,I,Y)=>{N7("xRange",A,D,Z,G,F,I,Y);let W=tJ(G),J=W||tJ(F),X=J||tJ(I),V=X;if(Z==="="&&V)Z="";if(Y=B.includePrerelease?"-0":"",W)if(Z===">"||Z==="<")D="<0.0.0-0";else D="*";else if(Z&&V){if(J)F=0;if(I=0,Z===">")if(Z=">=",J)G=+G+1,F=0,I=0;else F=+F+1,I=0;else if(Z==="<=")if(Z="<",J)G=+G+1;else F=+F+1;if(Z==="<")Y="-0";D=`${Z+G}.${F}.${I}${Y}`}else if(J)D=`>=${G}.0.0${Y} <${+G+1}.0.0-0`;else if(X)D=`>=${G}.${F}.0${Y} <${G}.${+F+1}.0-0`;return N7("xRange return",D),D})},X$6=(A,B)=>{return N7("replaceStars",A,B),A.trim().replace(_V[oJ.STAR],"")},V$6=(A,B)=>{return N7("replaceGTE0",A,B),A.trim().replace(_V[B.includePrerelease?oJ.GTE0PRE:oJ.GTE0],"")},C$6=(A)=>(B,Q,D,Z,G,F,I,Y,W,J,X,V)=>{if(tJ(D))Q="";else if(tJ(Z))Q=`>=${D}.0.0${A?"-0":""}`;else if(tJ(G))Q=`>=${D}.${Z}.0${A?"-0":""}`;else if(F)Q=`>=${Q}`;else Q=`>=${Q}${A?"-0":""}`;if(tJ(W))Y="";else if(tJ(J))Y=`<${+W+1}.0.0-0`;else if(tJ(X))Y=`<${W}.${+J+1}.0-0`;else if(V)Y=`<=${W}.${J}.${X}-${V}`;else if(A)Y=`<${W}.${J}.${+X+1}-0`;else Y=`<=${Y}`;return`${Q} ${Y}`.trim()},K$6=(A,B,Q)=>{for(let D=0;D<A.length;D++)if(!A[D].test(B))return!1;if(B.prerelease.length&&!Q.includePrerelease){for(let D=0;D<A.length;D++){if(N7(A[D].semver),A[D].semver===NX0.ANY)continue;if(A[D].semver.prerelease.length>0){let Z=A[D].semver;if(Z.major===B.major&&Z.minor===B.minor&&Z.patch===B.patch)return!0}}return!1}return!0}});
var Ox=E((Kn5,M8B)=>{var dC0=St(),N8B=Q71(),xP6=AJ(),L8B=HX0(),vP6=ru(),bP6=w6B(),fP6=q6B(),hP6=M6B(),gP6=T6B(),uP6=S6B(),mP6=k6B(),dP6=_6B(),cP6=v6B(),lP6=qE(),pP6=f6B(),iP6=g6B(),nP6=hj1(),aP6=c6B(),sP6=p6B(),rP6=Z71(),oP6=QS1(),tP6=EX0(),eP6=UX0(),AS6=D71(),BS6=DS1(),QS6=wX0(),DS6=zX0(),ZS6=F71(),GS6=NE(),FS6=kt(),IS6=n6B(),YS6=s6B(),WS6=o6B(),JS6=A8B(),XS6=Q8B(),VS6=gj1(),CS6=Y8B(),KS6=J8B(),HS6=C8B(),zS6=H8B(),ES6=q8B();M8B.exports={parse:vP6,valid:bP6,clean:fP6,inc:hP6,diff:gP6,major:uP6,minor:mP6,patch:dP6,prerelease:cP6,compare:lP6,rcompare:pP6,compareLoose:iP6,compareBuild:nP6,sort:aP6,rsort:sP6,gt:rP6,lt:oP6,eq:tP6,neq:eP6,gte:AS6,lte:BS6,cmp:QS6,coerce:DS6,Comparator:ZS6,Range:GS6,satisfies:FS6,toComparators:IS6,maxSatisfying:YS6,minSatisfying:WS6,minVersion:JS6,validRange:XS6,outside:VS6,gtr:CS6,ltr:KS6,intersects:HS6,simplifyRange:zS6,subset:ES6,SemVer:xP6,re:dC0.re,src:dC0.src,tokens:dC0.t,SEMVER_SPEC_VERSION:N8B.SEMVER_SPEC_VERSION,RELEASE_TYPES:N8B.RELEASE_TYPES,compareIdentifiers:L8B.compareIdentifiers,rcompareIdentifiers:L8B.rcompareIdentifiers}});
var Q71=E((Pg5,f0B)=>{var Yw6=Number.MAX_SAFE_INTEGER||9007199254740991,Ww6=["major","premajor","minor","preminor","patch","prepatch","prerelease"];f0B.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:Yw6,RELEASE_TYPES:Ww6,SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}});
var Q8B=E((In5,B8B)=>{var HP6=NE(),zP6=(A,B)=>{try{return new HP6(A,B).range||"*"}catch(Q){return null}};B8B.exports=zP6});
var QS1=E((ug5,GAB)=>{var fw6=qE(),hw6=(A,B,Q)=>fw6(A,B,Q)<0;GAB.exports=hw6});
var S6B=E((ai5,P6B)=>{var dT6=AJ(),cT6=(A,B)=>new dT6(A,B).major;P6B.exports=cT6});
var St=E((XM,h0B)=>{var{MAX_SAFE_COMPONENT_LENGTH:CX0,MAX_SAFE_BUILD_LENGTH:Jw6,MAX_LENGTH:Xw6}=Q71(),Vw6=B71();XM=h0B.exports={};var Cw6=XM.re=[],Kw6=XM.safeRe=[],CB=XM.src=[],Hw6=XM.safeSrc=[],KB=XM.t={},zw6=0,KX0="[a-zA-Z0-9-]",Ew6=[["\\s",1],["\\d",Xw6],[KX0,Jw6]],Uw6=(A)=>{for(let[B,Q]of Ew6)A=A.split(`${B}*`).join(`${B}{0,${Q}}`).split(`${B}+`).join(`${B}{1,${Q}}`);return A},iQ=(A,B,Q)=>{let D=Uw6(B),Z=zw6++;Vw6(A,Z,B),KB[A]=Z,CB[Z]=B,Hw6[Z]=D,Cw6[Z]=new RegExp(B,Q?"g":void 0),Kw6[Z]=new RegExp(D,Q?"g":void 0)};iQ("NUMERICIDENTIFIER","0|[1-9]\\d*");iQ("NUMERICIDENTIFIERLOOSE","\\d+");iQ("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${KX0}*`);iQ("MAINVERSION",`(${CB[KB.NUMERICIDENTIFIER]})\\.(${CB[KB.NUMERICIDENTIFIER]})\\.(${CB[KB.NUMERICIDENTIFIER]})`);iQ("MAINVERSIONLOOSE",`(${CB[KB.NUMERICIDENTIFIERLOOSE]})\\.(${CB[KB.NUMERICIDENTIFIERLOOSE]})\\.(${CB[KB.NUMERICIDENTIFIERLOOSE]})`);iQ("PRERELEASEIDENTIFIER",`(?:${CB[KB.NUMERICIDENTIFIER]}|${CB[KB.NONNUMERICIDENTIFIER]})`);iQ("PRERELEASEIDENTIFIERLOOSE",`(?:${CB[KB.NUMERICIDENTIFIERLOOSE]}|${CB[KB.NONNUMERICIDENTIFIER]})`);iQ("PRERELEASE",`(?:-(${CB[KB.PRERELEASEIDENTIFIER]}(?:\\.${CB[KB.PRERELEASEIDENTIFIER]})*))`);iQ("PRERELEASELOOSE",`(?:-?(${CB[KB.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${CB[KB.PRERELEASEIDENTIFIERLOOSE]})*))`);iQ("BUILDIDENTIFIER",`${KX0}+`);iQ("BUILD",`(?:\\+(${CB[KB.BUILDIDENTIFIER]}(?:\\.${CB[KB.BUILDIDENTIFIER]})*))`);iQ("FULLPLAIN",`v?${CB[KB.MAINVERSION]}${CB[KB.PRERELEASE]}?${CB[KB.BUILD]}?`);iQ("FULL",`^${CB[KB.FULLPLAIN]}$`);iQ("LOOSEPLAIN",`[v=\\s]*${CB[KB.MAINVERSIONLOOSE]}${CB[KB.PRERELEASELOOSE]}?${CB[KB.BUILD]}?`);iQ("LOOSE",`^${CB[KB.LOOSEPLAIN]}$`);iQ("GTLT","((?:<|>)?=?)");iQ("XRANGEIDENTIFIERLOOSE",`${CB[KB.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`);iQ("XRANGEIDENTIFIER",`${CB[KB.NUMERICIDENTIFIER]}|x|X|\\*`);iQ("XRANGEPLAIN",`[v=\\s]*(${CB[KB.XRANGEIDENTIFIER]})(?:\\.(${CB[KB.XRANGEIDENTIFIER]})(?:\\.(${CB[KB.XRANGEIDENTIFIER]})(?:${CB[KB.PRERELEASE]})?${CB[KB.BUILD]}?)?)?`);iQ("XRANGEPLAINLOOSE",`[v=\\s]*(${CB[KB.XRANGEIDENTIFIERLOOSE]})(?:\\.(${CB[KB.XRANGEIDENTIFIERLOOSE]})(?:\\.(${CB[KB.XRANGEIDENTIFIERLOOSE]})(?:${CB[KB.PRERELEASELOOSE]})?${CB[KB.BUILD]}?)?)?`);iQ("XRANGE",`^${CB[KB.GTLT]}\\s*${CB[KB.XRANGEPLAIN]}$`);iQ("XRANGELOOSE",`^${CB[KB.GTLT]}\\s*${CB[KB.XRANGEPLAINLOOSE]}$`);iQ("COERCEPLAIN",`(^|[^\\d])(\\d{1,${CX0}})(?:\\.(\\d{1,${CX0}}))?(?:\\.(\\d{1,${CX0}}))?`);iQ("COERCE",`${CB[KB.COERCEPLAIN]}(?:$|[^\\d])`);iQ("COERCEFULL",CB[KB.COERCEPLAIN]+`(?:${CB[KB.PRERELEASE]})?(?:${CB[KB.BUILD]})?(?:$|[^\\d])`);iQ("COERCERTL",CB[KB.COERCE],!0);iQ("COERCERTLFULL",CB[KB.COERCEFULL],!0);iQ("LONETILDE","(?:~>?)");iQ("TILDETRIM",`(\\s*)${CB[KB.LONETILDE]}\\s+`,!0);XM.tildeTrimReplace="$1~";iQ("TILDE",`^${CB[KB.LONETILDE]}${CB[KB.XRANGEPLAIN]}$`);iQ("TILDELOOSE",`^${CB[KB.LONETILDE]}${CB[KB.XRANGEPLAINLOOSE]}$`);iQ("LONECARET","(?:\\^)");iQ("CARETTRIM",`(\\s*)${CB[KB.LONECARET]}\\s+`,!0);XM.caretTrimReplace="$1^";iQ("CARET",`^${CB[KB.LONECARET]}${CB[KB.XRANGEPLAIN]}$`);iQ("CARETLOOSE",`^${CB[KB.LONECARET]}${CB[KB.XRANGEPLAINLOOSE]}$`);iQ("COMPARATORLOOSE",`^${CB[KB.GTLT]}\\s*(${CB[KB.LOOSEPLAIN]})$|^$`);iQ("COMPARATOR",`^${CB[KB.GTLT]}\\s*(${CB[KB.FULLPLAIN]})$|^$`);iQ("COMPARATORTRIM",`(\\s*)${CB[KB.GTLT]}\\s*(${CB[KB.LOOSEPLAIN]}|${CB[KB.XRANGEPLAIN]})`,!0);XM.comparatorTrimReplace="$1$2$3";iQ("HYPHENRANGE",`^\\s*(${CB[KB.XRANGEPLAIN]})\\s+-\\s+(${CB[KB.XRANGEPLAIN]})\\s*$`);iQ("HYPHENRANGELOOSE",`^\\s*(${CB[KB.XRANGEPLAINLOOSE]})\\s+-\\s+(${CB[KB.XRANGEPLAINLOOSE]})\\s*$`);iQ("STAR","(<|>)?=?\\s*\\*");iQ("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$");iQ("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")});
var T6B=E((ni5,O6B)=>{var R6B=ru(),mT6=(A,B)=>{let Q=R6B(A,null,!0),D=R6B(B,null,!0),Z=Q.compare(D);if(Z===0)return null;let G=Z>0,F=G?Q:D,I=G?D:Q,Y=!!F.prerelease.length;if(!!I.prerelease.length&&!Y){if(!I.patch&&!I.minor)return"major";if(I.compareMain(F)===0){if(I.minor&&!I.patch)return"minor";return"patch"}}let J=Y?"pre":"";if(Q.major!==D.major)return J+"major";if(Q.minor!==D.minor)return J+"minor";if(Q.patch!==D.patch)return J+"patch";return"prerelease"};O6B.exports=mT6});
var UX0=E((hg5,DAB)=>{var _w6=qE(),xw6=(A,B,Q)=>_w6(A,B,Q)!==0;DAB.exports=xw6});
var Y8B=E((Wn5,I8B)=>{var MP6=gj1(),RP6=(A,B,Q)=>MP6(A,B,">",Q);I8B.exports=RP6});
var Z71=E((gg5,ZAB)=>{var vw6=qE(),bw6=(A,B,Q)=>vw6(A,B,Q)>0;ZAB.exports=bw6});
var _6B=E((ri5,y6B)=>{var iT6=AJ(),nT6=(A,B)=>new iT6(A,B).patch;y6B.exports=nT6});
var c6B=E((Bn5,d6B)=>{var BP6=hj1(),QP6=(A,B)=>A.sort((Q,D)=>BP6(Q,D,B));d6B.exports=QP6});
var f6B=E((ti5,b6B)=>{var rT6=qE(),oT6=(A,B,Q)=>rT6(B,A,Q);b6B.exports=oT6});
var g6B=E((ei5,h6B)=>{var tT6=qE(),eT6=(A,B)=>tT6(A,B,!0);h6B.exports=eT6});
var gj1=E((Yn5,F8B)=>{var EP6=AJ(),G8B=F71(),{ANY:UP6}=G8B,wP6=NE(),$P6=kt(),D8B=Z71(),Z8B=QS1(),qP6=DS1(),NP6=D71(),LP6=(A,B,Q,D)=>{A=new EP6(A,D),B=new wP6(B,D);let Z,G,F,I,Y;switch(Q){case">":Z=D8B,G=qP6,F=Z8B,I=">",Y=">=";break;case"<":Z=Z8B,G=NP6,F=D8B,I="<",Y="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if($P6(A,B,D))return!1;for(let W=0;W<B.set.length;++W){let J=B.set[W],X=null,V=null;if(J.forEach((C)=>{if(C.semver===UP6)C=new G8B(">=0.0.0");if(X=X||C,V=V||C,Z(C.semver,X.semver,D))X=C;else if(F(C.semver,V.semver,D))V=C}),X.operator===I||X.operator===Y)return!1;if((!V.operator||V.operator===I)&&G(A,V.semver))return!1;else if(V.operator===Y&&F(A,V.semver))return!1}return!0};F8B.exports=LP6});
var hj1=E((An5,m6B)=>{var u6B=AJ(),AP6=(A,B,Q)=>{let D=new u6B(A,Q),Z=new u6B(B,Q);return D.compare(Z)||D.compareBuild(Z)};m6B.exports=AP6});
var k6B=E((si5,j6B)=>{var lT6=AJ(),pT6=(A,B)=>new lT6(A,B).minor;j6B.exports=pT6});
var kt=E((pg5,UAB)=>{var H$6=NE(),z$6=(A,B,Q)=>{try{B=new H$6(B,Q)}catch(D){return!1}return B.test(A)};UAB.exports=z$6});
var n6B=E((Dn5,i6B)=>{var GP6=NE(),FP6=(A,B)=>new GP6(A,B).set.map((Q)=>Q.map((D)=>D.value).join(" ").trim().split(" "));i6B.exports=FP6});
var o6B=E((Gn5,r6B)=>{var JP6=AJ(),XP6=NE(),VP6=(A,B,Q)=>{let D=null,Z=null,G=null;try{G=new XP6(B,Q)}catch(F){return null}return A.forEach((F)=>{if(G.test(F)){if(!D||Z.compare(F)===1)D=F,Z=new JP6(D,Q)}}),D};r6B.exports=VP6});
var p6B=E((Qn5,l6B)=>{var DP6=hj1(),ZP6=(A,B)=>A.sort((Q,D)=>DP6(D,Q,B));l6B.exports=ZP6});
var q6B=E((pi5,$6B)=>{var hT6=ru(),gT6=(A,B)=>{let Q=hT6(A.trim().replace(/^[=v]+/,""),B);return Q?Q.version:null};$6B.exports=gT6});
var q8B=E((Cn5,$8B)=>{var z8B=NE(),uC0=F71(),{ANY:gC0}=uC0,CD1=kt(),mC0=qE(),kP6=(A,B,Q={})=>{if(A===B)return!0;A=new z8B(A,Q),B=new z8B(B,Q);let D=!1;A:for(let Z of A.set){for(let G of B.set){let F=_P6(Z,G,Q);if(D=D||F!==null,F)continue A}if(D)return!1}return!0},yP6=[new uC0(">=0.0.0-0")],E8B=[new uC0(">=0.0.0")],_P6=(A,B,Q)=>{if(A===B)return!0;if(A.length===1&&A[0].semver===gC0)if(B.length===1&&B[0].semver===gC0)return!0;else if(Q.includePrerelease)A=yP6;else A=E8B;if(B.length===1&&B[0].semver===gC0)if(Q.includePrerelease)return!0;else B=E8B;let D=new Set,Z,G;for(let C of A)if(C.operator===">"||C.operator===">=")Z=U8B(Z,C,Q);else if(C.operator==="<"||C.operator==="<=")G=w8B(G,C,Q);else D.add(C.semver);if(D.size>1)return null;let F;if(Z&&G){if(F=mC0(Z.semver,G.semver,Q),F>0)return null;else if(F===0&&(Z.operator!==">="||G.operator!=="<="))return null}for(let C of D){if(Z&&!CD1(C,String(Z),Q))return null;if(G&&!CD1(C,String(G),Q))return null;for(let K of B)if(!CD1(C,String(K),Q))return!1;return!0}let I,Y,W,J,X=G&&!Q.includePrerelease&&G.semver.prerelease.length?G.semver:!1,V=Z&&!Q.includePrerelease&&Z.semver.prerelease.length?Z.semver:!1;if(X&&X.prerelease.length===1&&G.operator==="<"&&X.prerelease[0]===0)X=!1;for(let C of B){if(J=J||C.operator===">"||C.operator===">=",W=W||C.operator==="<"||C.operator==="<=",Z){if(V){if(C.semver.prerelease&&C.semver.prerelease.length&&C.semver.major===V.major&&C.semver.minor===V.minor&&C.semver.patch===V.patch)V=!1}if(C.operator===">"||C.operator===">="){if(I=U8B(Z,C,Q),I===C&&I!==Z)return!1}else if(Z.operator===">="&&!CD1(Z.semver,String(C),Q))return!1}if(G){if(X){if(C.semver.prerelease&&C.semver.prerelease.length&&C.semver.major===X.major&&C.semver.minor===X.minor&&C.semver.patch===X.patch)X=!1}if(C.operator==="<"||C.operator==="<="){if(Y=w8B(G,C,Q),Y===C&&Y!==G)return!1}else if(G.operator==="<="&&!CD1(G.semver,String(C),Q))return!1}if(!C.operator&&(G||Z)&&F!==0)return!1}if(Z&&W&&!G&&F!==0)return!1;if(G&&J&&!Z&&F!==0)return!1;if(V||X)return!1;return!0},U8B=(A,B,Q)=>{if(!A)return B;let D=mC0(A.semver,B.semver,Q);return D>0?A:D<0?B:B.operator===">"&&A.operator===">="?B:A},w8B=(A,B,Q)=>{if(!A)return B;let D=mC0(A.semver,B.semver,Q);return D<0?A:D>0?B:B.operator==="<"&&A.operator==="<="?B:A};$8B.exports=kP6});
var qE=E((xg5,o0B)=>{var r0B=AJ(),Pw6=(A,B,Q)=>new r0B(A,Q).compare(new r0B(B,Q));o0B.exports=Pw6});
var rP1=E((Sg5,g0B)=>{var ww6=Object.freeze({loose:!0}),$w6=Object.freeze({}),qw6=(A)=>{if(!A)return $w6;if(typeof A!=="object")return ww6;return A};g0B.exports=qw6});
var ru=E((yg5,a0B)=>{var n0B=AJ(),Mw6=(A,B,Q=!1)=>{if(A instanceof n0B)return A;try{return new n0B(A,B)}catch(D){if(!Q)return null;throw D}};a0B.exports=Mw6});
var s6B=E((Zn5,a6B)=>{var IP6=AJ(),YP6=NE(),WP6=(A,B,Q)=>{let D=null,Z=null,G=null;try{G=new YP6(B,Q)}catch(F){return null}return A.forEach((F)=>{if(G.test(F)){if(!D||Z.compare(F)===-1)D=F,Z=new IP6(D,Q)}}),D};a6B.exports=WP6});
var v6B=E((oi5,x6B)=>{var aT6=ru(),sT6=(A,B)=>{let Q=aT6(A,B);return Q&&Q.prerelease.length?Q.prerelease:null};x6B.exports=sT6});
var w6B=E((li5,U6B)=>{var bT6=ru(),fT6=(A,B)=>{let Q=bT6(A,B);return Q?Q.version:null};U6B.exports=fT6});
var wX0=E((dg5,IAB)=>{var mw6=EX0(),dw6=UX0(),cw6=Z71(),lw6=D71(),pw6=QS1(),iw6=DS1(),nw6=(A,B,Q,D)=>{switch(B){case"===":if(typeof A==="object")A=A.version;if(typeof Q==="object")Q=Q.version;return A===Q;case"!==":if(typeof A==="object")A=A.version;if(typeof Q==="object")Q=Q.version;return A!==Q;case"":case"=":case"==":return mw6(A,Q,D);case"!=":return dw6(A,Q,D);case">":return cw6(A,Q,D);case">=":return lw6(A,Q,D);case"<":return pw6(A,Q,D);case"<=":return iw6(A,Q,D);default:throw new TypeError(`Invalid operator: ${B}`)}};IAB.exports=nw6});
var zX0=E((_g5,s0B)=>{var Rw6=AJ(),Ow6=ru(),{safeRe:AS1,t:BS1}=St(),Tw6=(A,B)=>{if(A instanceof Rw6)return A;if(typeof A==="number")A=String(A);if(typeof A!=="string")return null;B=B||{};let Q=null;if(!B.rtl)Q=A.match(B.includePrerelease?AS1[BS1.COERCEFULL]:AS1[BS1.COERCE]);else{let Y=B.includePrerelease?AS1[BS1.COERCERTLFULL]:AS1[BS1.COERCERTL],W;while((W=Y.exec(A))&&(!Q||Q.index+Q[0].length!==A.length)){if(!Q||W.index+W[0].length!==Q.index+Q[0].length)Q=W;Y.lastIndex=W.index+W[1].length+W[2].length}Y.lastIndex=-1}if(Q===null)return null;let D=Q[2],Z=Q[3]||"0",G=Q[4]||"0",F=B.includePrerelease&&Q[5]?`-${Q[5]}`:"",I=B.includePrerelease&&Q[6]?`+${Q[6]}`:"";return Ow6(`${D}.${Z}.${G}${F}${I}`,B)};s0B.exports=Tw6});

module.exports = qv;
