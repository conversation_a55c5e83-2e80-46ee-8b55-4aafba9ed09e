// dependency_chain package extracted with entry point: dI

var Ws0=E((sS9)=>{function Vp1(A,B){var Q=A.length;A.push(B);A:for(;0<Q;){var D=Q-1>>>1,Z=A[D];if(0<EV1(Z,B))A[D]=B,A[Q]=Z,Q=D;else break A}}function Aw(A){return A.length===0?null:A[0]}function qV1(A){if(A.length===0)return null;var B=A[0],Q=A.pop();if(Q!==B){A[0]=Q;A:for(var D=0,Z=A.length,G=Z>>>1;D<G;){var F=2*(D+1)-1,I=A[F],Y=F+1,W=A[Y];if(0>EV1(I,Q))Y<Z&&0>EV1(W,I)?(A[D]=W,A[Y]=Q,D=Y):(A[D]=I,A[F]=Q,D=F);else if(Y<Z&&0>EV1(W,Q))A[D]=W,A[Y]=Q,D=Y;else break A}}return B}function EV1(A,B){var Q=A.sortIndex-B.sortIndex;return Q!==0?Q:A.id-B.id}if(typeof performance==="object"&&typeof performance.now==="function")Cp1=performance,sS9.unstable_now=function(){return Cp1.now()};else UV1=Date,Kp1=UV1.now(),sS9.unstable_now=function(){return UV1.now()-Kp1};var Cp1,UV1,Kp1,IN=[],tj=[],aS9=1,lH=null,kW=3,NV1=!1,uf=!1,yB1=!1,Zs0=typeof setTimeout==="function"?setTimeout:null,Gs0=typeof clearTimeout==="function"?clearTimeout:null,Ds0=typeof setImmediate!=="undefined"?setImmediate:null;typeof navigator!=="undefined"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function Hp1(A){for(var B=Aw(tj);B!==null;){if(B.callback===null)qV1(tj);else if(B.startTime<=A)qV1(tj),B.sortIndex=B.expirationTime,Vp1(IN,B);else break;B=Aw(tj)}}function Ep1(A){if(yB1=!1,Hp1(A),!uf)if(Aw(IN)!==null)uf=!0,wp1(Up1);else{var B=Aw(tj);B!==null&&$p1(Ep1,B.startTime-A)}}function Up1(A,B){uf=!1,yB1&&(yB1=!1,Gs0(_B1),_B1=-1),NV1=!0;var Q=kW;try{Hp1(B);for(lH=Aw(IN);lH!==null&&(!(lH.expirationTime>B)||A&&!Ys0());){var D=lH.callback;if(typeof D==="function"){lH.callback=null,kW=lH.priorityLevel;var Z=D(lH.expirationTime<=B);B=sS9.unstable_now(),typeof Z==="function"?lH.callback=Z:lH===Aw(IN)&&qV1(IN),Hp1(B)}else qV1(IN);lH=Aw(IN)}if(lH!==null)var G=!0;else{var F=Aw(tj);F!==null&&$p1(Ep1,F.startTime-B),G=!1}return G}finally{lH=null,kW=Q,NV1=!1}}var LV1=!1,wV1=null,_B1=-1,Fs0=5,Is0=-1;function Ys0(){return sS9.unstable_now()-Is0<Fs0?!1:!0}function Xp1(){if(wV1!==null){var A=sS9.unstable_now();Is0=A;var B=!0;try{B=wV1(!0,A)}finally{B?kB1():(LV1=!1,wV1=null)}}else LV1=!1}var kB1;if(typeof Ds0==="function")kB1=function(){Ds0(Xp1)};else if(typeof MessageChannel!=="undefined")$V1=new MessageChannel,zp1=$V1.port2,$V1.port1.onmessage=Xp1,kB1=function(){zp1.postMessage(null)};else kB1=function(){Zs0(Xp1,0)};var $V1,zp1;function wp1(A){wV1=A,LV1||(LV1=!0,kB1())}function $p1(A,B){_B1=Zs0(function(){A(sS9.unstable_now())},B)}sS9.unstable_IdlePriority=5;sS9.unstable_ImmediatePriority=1;sS9.unstable_LowPriority=4;sS9.unstable_NormalPriority=3;sS9.unstable_Profiling=null;sS9.unstable_UserBlockingPriority=2;sS9.unstable_cancelCallback=function(A){A.callback=null};sS9.unstable_continueExecution=function(){uf||NV1||(uf=!0,wp1(Up1))};sS9.unstable_forceFrameRate=function(A){0>A||125<A?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Fs0=0<A?Math.floor(1000/A):5};sS9.unstable_getCurrentPriorityLevel=function(){return kW};sS9.unstable_getFirstCallbackNode=function(){return Aw(IN)};sS9.unstable_next=function(A){switch(kW){case 1:case 2:case 3:var B=3;break;default:B=kW}var Q=kW;kW=B;try{return A()}finally{kW=Q}};sS9.unstable_pauseExecution=function(){};sS9.unstable_requestPaint=function(){};sS9.unstable_runWithPriority=function(A,B){switch(A){case 1:case 2:case 3:case 4:case 5:break;default:A=3}var Q=kW;kW=A;try{return B()}finally{kW=Q}};sS9.unstable_scheduleCallback=function(A,B,Q){var D=sS9.unstable_now();switch(typeof Q==="object"&&Q!==null?(Q=Q.delay,Q=typeof Q==="number"&&0<Q?D+Q:D):Q=D,A){case 1:var Z=-1;break;case 2:Z=250;break;case 5:Z=1073741823;break;case 4:Z=1e4;break;default:Z=5000}return Z=Q+Z,A={id:aS9++,callback:B,priorityLevel:A,startTime:Q,expirationTime:Z,sortIndex:-1},Q>D?(A.sortIndex=Q,Vp1(tj,A),Aw(IN)===null&&A===Aw(tj)&&(yB1?(Gs0(_B1),_B1=-1):yB1=!0,$p1(Ep1,Q-D))):(A.sortIndex=Z,Vp1(IN,A),uf||NV1||(uf=!0,wp1(Up1))),A};sS9.unstable_shouldYield=Ys0;sS9.unstable_wrapCallback=function(A){var B=kW;return function(){var Q=kW;kW=B;try{return A.apply(this,arguments)}finally{kW=Q}}}});
var qp1=F1(z1(),1),zF=F1(Ws0(),1);

module.exports = dI;
