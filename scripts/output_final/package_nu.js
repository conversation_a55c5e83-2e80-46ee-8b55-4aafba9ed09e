// dependency_chain package extracted with entry point: nu

var N1B=E((pf5,q1B)=>{var WU6="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";q1B.exports=WU6});
var O1B=E((if5,R1B)=>{var JU6=N1B();function L1B(){}function M1B(){}M1B.resetWarningCache=L1B;R1B.exports=function(){function A(D,Z,G,F,I,Y){if(Y===JU6)return;var W=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw W.name="Invariant Violation",W}A.isRequired=A;function B(){return A}var Q={array:A,bigint:A,bool:A,func:A,number:A,object:A,string:A,symbol:A,any:A,arrayOf:B,element:A,elementType:A,instanceOf:B,node:A,objectOf:B,oneOf:B,oneOfType:B,shape:B,exact:B,checkPropTypes:M1B,resetWarningCache:L1B};return Q.PropTypes=Q,Q}});
var P1B=E((nf5,T1B)=>{T1B.exports=O1B()();var XU6,VU6});

module.exports = nu;
