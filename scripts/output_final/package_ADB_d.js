// dependency_chain package extracted with entry point: ADB

var CK0=E((Ra5,u3B)=>{var{defineProperty:Zk1,getOwnPropertyDescriptor:Ck6,getOwnPropertyNames:Kk6}=Object,Hk6=Object.prototype.hasOwnProperty,Gk1=(A,B)=>Zk1(A,"name",{value:B,configurable:!0}),zk6=(A,B)=>{for(var Q in B)Zk1(A,Q,{get:B[Q],enumerable:!0})},Ek6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Kk6(B))if(!Hk6.call(A,Z)&&Z!==Q)Zk1(A,Z,{get:()=>B[Z],enumerable:!(D=Ck6(B,Z))||D.enumerable})}return A},Uk6=(A)=>Ek6(Zk1({},"__esModule",{value:!0}),A),y3B={};zk6(y3B,{AlgorithmId:()=>b3B,EndpointURLScheme:()=>v3B,FieldPosition:()=>f3B,HttpApiKeyAuthLocation:()=>x3B,HttpAuthLocation:()=>_3B,IniSectionType:()=>h3B,RequestHandlerProtocol:()=>g3B,SMITHY_CONTEXT_KEY:()=>Lk6,getDefaultClientConfiguration:()=>qk6,resolveDefaultRuntimeConfig:()=>Nk6});u3B.exports=Uk6(y3B);var _3B=((A)=>{return A.HEADER="header",A.QUERY="query",A})(_3B||{}),x3B=((A)=>{return A.HEADER="header",A.QUERY="query",A})(x3B||{}),v3B=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(v3B||{}),b3B=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(b3B||{}),wk6=Gk1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{_checksumAlgorithms:B,addChecksumAlgorithm(Q){this._checksumAlgorithms.push(Q)},checksumAlgorithms(){return this._checksumAlgorithms}}},"getChecksumConfiguration"),$k6=Gk1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),qk6=Gk1((A)=>{return{...wk6(A)}},"getDefaultClientConfiguration"),Nk6=Gk1((A)=>{return{...$k6(A)}},"resolveDefaultRuntimeConfig"),f3B=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(f3B||{}),Lk6="__smithy_context",h3B=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(h3B||{}),g3B=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(g3B||{})});
var KK0=E((Oa5,a3B)=>{var{defineProperty:Fk1,getOwnPropertyDescriptor:Mk6,getOwnPropertyNames:Rk6}=Object,Ok6=Object.prototype.hasOwnProperty,Px=(A,B)=>Fk1(A,"name",{value:B,configurable:!0}),Tk6=(A,B)=>{for(var Q in B)Fk1(A,Q,{get:B[Q],enumerable:!0})},Pk6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Rk6(B))if(!Ok6.call(A,Z)&&Z!==Q)Fk1(A,Z,{get:()=>B[Z],enumerable:!(D=Mk6(B,Z))||D.enumerable})}return A},Sk6=(A)=>Pk6(Fk1({},"__esModule",{value:!0}),A),m3B={};Tk6(m3B,{Field:()=>_k6,Fields:()=>xk6,HttpRequest:()=>vk6,HttpResponse:()=>bk6,getHttpHandlerExtensionConfiguration:()=>jk6,isValidHostname:()=>n3B,resolveHttpHandlerRuntimeConfig:()=>kk6});a3B.exports=Sk6(m3B);var jk6=Px((A)=>{let B=A.httpHandler;return{setHttpHandler(Q){B=Q},httpHandler(){return B},updateHttpClientConfig(Q,D){B.updateHttpClientConfig(Q,D)},httpHandlerConfigs(){return B.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),kk6=Px((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),yk6=CK0(),d3B=class A{constructor({name:B,kind:Q=yk6.FieldPosition.HEADER,values:D=[]}){this.name=B,this.kind=Q,this.values=D}add(B){this.values.push(B)}set(B){this.values=B}remove(B){this.values=this.values.filter((Q)=>Q!==B)}toString(){return this.values.map((B)=>B.includes(",")||B.includes(" ")?`"${B}"`:B).join(", ")}get(){return this.values}};Px(d3B,"Field");var _k6=d3B,c3B=class A{constructor({fields:B=[],encoding:Q="utf-8"}){this.entries={},B.forEach(this.setField.bind(this)),this.encoding=Q}setField(B){this.entries[B.name.toLowerCase()]=B}getField(B){return this.entries[B.toLowerCase()]}removeField(B){delete this.entries[B.toLowerCase()]}getByType(B){return Object.values(this.entries).filter((Q)=>Q.kind===B)}};Px(c3B,"Fields");var xk6=c3B,l3B=class A{constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){let B=new A({...this,headers:{...this.headers}});if(B.query)B.query=p3B(B.query);return B}};Px(l3B,"HttpRequest");var vk6=l3B;function p3B(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}Px(p3B,"cloneQuery");var i3B=class A{constructor(B){this.statusCode=B.statusCode,this.reason=B.reason,this.headers=B.headers||{},this.body=B.body}static isInstance(B){if(!B)return!1;let Q=B;return typeof Q.statusCode==="number"&&typeof Q.headers==="object"}};Px(i3B,"HttpResponse");var bk6=i3B;function n3B(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}Px(n3B,"isValidHostname")});

module.exports = ADB;
