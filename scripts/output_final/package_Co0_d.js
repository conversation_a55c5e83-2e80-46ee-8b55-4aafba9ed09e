// E_module package extracted with entry point: Co0

var CC1=E((Zp8,Wo0)=>{var Rk9=J1("events"),Ok9=J1("https"),Tk9=J1("http"),Ao0=J1("net"),Pk9=J1("tls"),{randomBytes:Sk9,createHash:jk9}=J1("crypto"),{Duplex:Qp8,Readable:Dp8}=J1("stream"),{URL:dp1}=J1("url"),Fk=aB1(),kk9=fp1(),yk9=gp1(),{isBlob:_k9}=Hp(),{BINARY_TYPES:tr0,EMPTY_BUFFER:JC1,GUID:xk9,kForOnEventAttribute:cp1,kListener:vk9,kStatusCode:bk9,kWebSocket:eF,NOOP:Bo0}=KO(),{EventTarget:{addEventListener:fk9,removeEventListener:hk9}}=rr0(),{format:gk9,parse:uk9}=mp1(),{toBuffer:mk9}=pB1(),Qo0=Symbol("kAborted"),lp1=[8,13],zO=["CONNECTING","OPEN","CLOSING","CLOSED"],dk9=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class cQ extends Rk9{constructor(A,B,Q){super();if(this._binaryType=tr0[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=JC1,this._closeTimer=null,this._errorEmitted=!1,this._extensions={},this._paused=!1,this._protocol="",this._readyState=cQ.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,A!==null){if(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,B===void 0)B=[];else if(!Array.isArray(B))if(typeof B==="object"&&B!==null)Q=B,B=[];else B=[B];Do0(this,A,B,Q)}else this._autoPong=Q.autoPong,this._isServer=!0}get binaryType(){return this._binaryType}set binaryType(A){if(!tr0.includes(A))return;if(this._binaryType=A,this._receiver)this._receiver._binaryType=A}get bufferedAmount(){if(!this._socket)return this._bufferedAmount;return this._socket._writableState.length+this._sender._bufferedBytes}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(A,B,Q){let D=new kk9({allowSynchronousEvents:Q.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:Q.maxPayload,skipUTF8Validation:Q.skipUTF8Validation}),Z=new yk9(A,this._extensions,Q.generateMask);if(this._receiver=D,this._sender=Z,this._socket=A,D[eF]=this,Z[eF]=this,A[eF]=this,D.on("conclude",pk9),D.on("drain",ik9),D.on("error",nk9),D.on("message",ak9),D.on("ping",sk9),D.on("pong",rk9),Z.onerror=ok9,A.setTimeout)A.setTimeout(0);if(A.setNoDelay)A.setNoDelay();if(B.length>0)A.unshift(B);A.on("close",Fo0),A.on("data",VC1),A.on("end",Io0),A.on("error",Yo0),this._readyState=cQ.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=cQ.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}if(this._extensions[Fk.extensionName])this._extensions[Fk.extensionName].cleanup();this._receiver.removeAllListeners(),this._readyState=cQ.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(A,B){if(this.readyState===cQ.CLOSED)return;if(this.readyState===cQ.CONNECTING){hC(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===cQ.CLOSING){if(this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted))this._socket.end();return}this._readyState=cQ.CLOSING,this._sender.close(A,B,!this._isServer,(Q)=>{if(Q)return;if(this._closeFrameSent=!0,this._closeFrameReceived||this._receiver._writableState.errorEmitted)this._socket.end()}),Go0(this)}pause(){if(this.readyState===cQ.CONNECTING||this.readyState===cQ.CLOSED)return;this._paused=!0,this._socket.pause()}ping(A,B,Q){if(this.readyState===cQ.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof A==="function")Q=A,A=B=void 0;else if(typeof B==="function")Q=B,B=void 0;if(typeof A==="number")A=A.toString();if(this.readyState!==cQ.OPEN){pp1(this,A,Q);return}if(B===void 0)B=!this._isServer;this._sender.ping(A||JC1,B,Q)}pong(A,B,Q){if(this.readyState===cQ.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof A==="function")Q=A,A=B=void 0;else if(typeof B==="function")Q=B,B=void 0;if(typeof A==="number")A=A.toString();if(this.readyState!==cQ.OPEN){pp1(this,A,Q);return}if(B===void 0)B=!this._isServer;this._sender.pong(A||JC1,B,Q)}resume(){if(this.readyState===cQ.CONNECTING||this.readyState===cQ.CLOSED)return;if(this._paused=!1,!this._receiver._writableState.needDrain)this._socket.resume()}send(A,B,Q){if(this.readyState===cQ.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof B==="function")Q=B,B={};if(typeof A==="number")A=A.toString();if(this.readyState!==cQ.OPEN){pp1(this,A,Q);return}let D={binary:typeof A!=="string",mask:!this._isServer,compress:!0,fin:!0,...B};if(!this._extensions[Fk.extensionName])D.compress=!1;this._sender.send(A||JC1,D,Q)}terminate(){if(this.readyState===cQ.CLOSED)return;if(this.readyState===cQ.CONNECTING){hC(this,this._req,"WebSocket was closed before the connection was established");return}if(this._socket)this._readyState=cQ.CLOSING,this._socket.destroy()}}Object.defineProperty(cQ,"CONNECTING",{enumerable:!0,value:zO.indexOf("CONNECTING")});Object.defineProperty(cQ.prototype,"CONNECTING",{enumerable:!0,value:zO.indexOf("CONNECTING")});Object.defineProperty(cQ,"OPEN",{enumerable:!0,value:zO.indexOf("OPEN")});Object.defineProperty(cQ.prototype,"OPEN",{enumerable:!0,value:zO.indexOf("OPEN")});Object.defineProperty(cQ,"CLOSING",{enumerable:!0,value:zO.indexOf("CLOSING")});Object.defineProperty(cQ.prototype,"CLOSING",{enumerable:!0,value:zO.indexOf("CLOSING")});Object.defineProperty(cQ,"CLOSED",{enumerable:!0,value:zO.indexOf("CLOSED")});Object.defineProperty(cQ.prototype,"CLOSED",{enumerable:!0,value:zO.indexOf("CLOSED")});["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach((A)=>{Object.defineProperty(cQ.prototype,A,{enumerable:!0})});["open","error","close","message"].forEach((A)=>{Object.defineProperty(cQ.prototype,`on${A}`,{enumerable:!0,get(){for(let B of this.listeners(A))if(B[cp1])return B[vk9];return null},set(B){for(let Q of this.listeners(A))if(Q[cp1]){this.removeListener(A,Q);break}if(typeof B!=="function")return;this.addEventListener(A,B,{[cp1]:!0})}})});cQ.prototype.addEventListener=fk9;cQ.prototype.removeEventListener=hk9;Wo0.exports=cQ;function Do0(A,B,Q,D){let Z={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:lp1[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...D,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(A._autoPong=Z.autoPong,!lp1.includes(Z.protocolVersion))throw new RangeError(`Unsupported protocol version: ${Z.protocolVersion} (supported versions: ${lp1.join(", ")})`);let G;if(B instanceof dp1)G=B;else try{G=new dp1(B)}catch(H){throw new SyntaxError(`Invalid URL: ${B}`)}if(G.protocol==="http:")G.protocol="ws:";else if(G.protocol==="https:")G.protocol="wss:";A._url=G.href;let F=G.protocol==="wss:",I=G.protocol==="ws+unix:",Y;if(G.protocol!=="ws:"&&!F&&!I)Y=`The URL's protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"`;else if(I&&!G.pathname)Y="The URL's pathname is empty";else if(G.hash)Y="The URL contains a fragment identifier";if(Y){let H=new SyntaxError(Y);if(A._redirects===0)throw H;else{XC1(A,H);return}}let W=F?443:80,J=Sk9(16).toString("base64"),X=F?Ok9.request:Tk9.request,V=new Set,C;if(Z.createConnection=Z.createConnection||(F?lk9:ck9),Z.defaultPort=Z.defaultPort||W,Z.port=G.port||W,Z.host=G.hostname.startsWith("[")?G.hostname.slice(1,-1):G.hostname,Z.headers={...Z.headers,"Sec-WebSocket-Version":Z.protocolVersion,"Sec-WebSocket-Key":J,Connection:"Upgrade",Upgrade:"websocket"},Z.path=G.pathname+G.search,Z.timeout=Z.handshakeTimeout,Z.perMessageDeflate)C=new Fk(Z.perMessageDeflate!==!0?Z.perMessageDeflate:{},!1,Z.maxPayload),Z.headers["Sec-WebSocket-Extensions"]=gk9({[Fk.extensionName]:C.offer()});if(Q.length){for(let H of Q){if(typeof H!=="string"||!dk9.test(H)||V.has(H))throw new SyntaxError("An invalid or duplicated subprotocol was specified");V.add(H)}Z.headers["Sec-WebSocket-Protocol"]=Q.join(",")}if(Z.origin)if(Z.protocolVersion<13)Z.headers["Sec-WebSocket-Origin"]=Z.origin;else Z.headers.Origin=Z.origin;if(G.username||G.password)Z.auth=`${G.username}:${G.password}`;if(I){let H=Z.path.split(":");Z.socketPath=H[0],Z.path=H[1]}let K;if(Z.followRedirects){if(A._redirects===0){A._originalIpc=I,A._originalSecure=F,A._originalHostOrSocketPath=I?Z.socketPath:G.host;let H=D&&D.headers;if(D={...D,headers:{}},H)for(let[z,$]of Object.entries(H))D.headers[z.toLowerCase()]=$}else if(A.listenerCount("redirect")===0){let H=I?A._originalIpc?Z.socketPath===A._originalHostOrSocketPath:!1:A._originalIpc?!1:G.host===A._originalHostOrSocketPath;if(!H||A._originalSecure&&!F){if(delete Z.headers.authorization,delete Z.headers.cookie,!H)delete Z.headers.host;Z.auth=void 0}}if(Z.auth&&!D.headers.authorization)D.headers.authorization="Basic "+Buffer.from(Z.auth).toString("base64");if(K=A._req=X(Z),A._redirects)A.emit("redirect",A.url,K)}else K=A._req=X(Z);if(Z.timeout)K.on("timeout",()=>{hC(A,K,"Opening handshake has timed out")});if(K.on("error",(H)=>{if(K===null||K[Qo0])return;K=A._req=null,XC1(A,H)}),K.on("response",(H)=>{let z=H.headers.location,$=H.statusCode;if(z&&Z.followRedirects&&$>=300&&$<400){if(++A._redirects>Z.maxRedirects){hC(A,K,"Maximum redirects exceeded");return}K.abort();let L;try{L=new dp1(z,B)}catch(N){let O=new SyntaxError(`Invalid URL: ${z}`);XC1(A,O);return}Do0(A,L,Q,D)}else if(!A.emit("unexpected-response",K,H))hC(A,K,`Unexpected server response: ${H.statusCode}`)}),K.on("upgrade",(H,z,$)=>{if(A.emit("upgrade",H),A.readyState!==cQ.CONNECTING)return;K=A._req=null;let L=H.headers.upgrade;if(L===void 0||L.toLowerCase()!=="websocket"){hC(A,z,"Invalid Upgrade header");return}let N=jk9("sha1").update(J+xk9).digest("base64");if(H.headers["sec-websocket-accept"]!==N){hC(A,z,"Invalid Sec-WebSocket-Accept header");return}let O=H.headers["sec-websocket-protocol"],R;if(O!==void 0){if(!V.size)R="Server sent a subprotocol but none was requested";else if(!V.has(O))R="Server sent an invalid subprotocol"}else if(V.size)R="Server sent no subprotocol";if(R){hC(A,z,R);return}if(O)A._protocol=O;let T=H.headers["sec-websocket-extensions"];if(T!==void 0){if(!C){hC(A,z,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}let j;try{j=uk9(T)}catch(k){hC(A,z,"Invalid Sec-WebSocket-Extensions header");return}let f=Object.keys(j);if(f.length!==1||f[0]!==Fk.extensionName){hC(A,z,"Server indicated an extension that was not requested");return}try{C.accept(j[Fk.extensionName])}catch(k){hC(A,z,"Invalid Sec-WebSocket-Extensions header");return}A._extensions[Fk.extensionName]=C}A.setSocket(z,$,{allowSynchronousEvents:Z.allowSynchronousEvents,generateMask:Z.generateMask,maxPayload:Z.maxPayload,skipUTF8Validation:Z.skipUTF8Validation})}),Z.finishRequest)Z.finishRequest(K,A);else K.end()}function XC1(A,B){A._readyState=cQ.CLOSING,A._errorEmitted=!0,A.emit("error",B),A.emitClose()}function ck9(A){return A.path=A.socketPath,Ao0.connect(A)}function lk9(A){if(A.path=void 0,!A.servername&&A.servername!=="")A.servername=Ao0.isIP(A.host)?"":A.host;return Pk9.connect(A)}function hC(A,B,Q){A._readyState=cQ.CLOSING;let D=new Error(Q);if(Error.captureStackTrace(D,hC),B.setHeader){if(B[Qo0]=!0,B.abort(),B.socket&&!B.socket.destroyed)B.socket.destroy();process.nextTick(XC1,A,D)}else B.destroy(D),B.once("error",A.emit.bind(A,"error")),B.once("close",A.emitClose.bind(A))}function pp1(A,B,Q){if(B){let D=_k9(B)?B.size:mk9(B).length;if(A._socket)A._sender._bufferedBytes+=D;else A._bufferedAmount+=D}if(Q){let D=new Error(`WebSocket is not open: readyState ${A.readyState} (${zO[A.readyState]})`);process.nextTick(Q,D)}}function pk9(A,B){let Q=this[eF];if(Q._closeFrameReceived=!0,Q._closeMessage=B,Q._closeCode=A,Q._socket[eF]===void 0)return;if(Q._socket.removeListener("data",VC1),process.nextTick(Zo0,Q._socket),A===1005)Q.close();else Q.close(A,B)}function ik9(){let A=this[eF];if(!A.isPaused)A._socket.resume()}function nk9(A){let B=this[eF];if(B._socket[eF]!==void 0)B._socket.removeListener("data",VC1),process.nextTick(Zo0,B._socket),B.close(A[bk9]);if(!B._errorEmitted)B._errorEmitted=!0,B.emit("error",A)}function er0(){this[eF].emitClose()}function ak9(A,B){this[eF].emit("message",A,B)}function sk9(A){let B=this[eF];if(B._autoPong)B.pong(A,!this._isServer,Bo0);B.emit("ping",A)}function rk9(A){this[eF].emit("pong",A)}function Zo0(A){A.resume()}function ok9(A){let B=this[eF];if(B.readyState===cQ.CLOSED)return;if(B.readyState===cQ.OPEN)B._readyState=cQ.CLOSING,Go0(B);if(this._socket.end(),!B._errorEmitted)B._errorEmitted=!0,B.emit("error",A)}function Go0(A){A._closeTimer=setTimeout(A._socket.destroy.bind(A._socket),30000)}function Fo0(){let A=this[eF];this.removeListener("close",Fo0),this.removeListener("data",VC1),this.removeListener("end",Io0),A._readyState=cQ.CLOSING;let B;if(!this._readableState.endEmitted&&!A._closeFrameReceived&&!A._receiver._writableState.errorEmitted&&(B=A._socket.read())!==null)A._receiver.write(B);if(A._receiver.end(),this[eF]=void 0,clearTimeout(A._closeTimer),A._receiver._writableState.finished||A._receiver._writableState.errorEmitted)A.emitClose();else A._receiver.on("error",er0),A._receiver.on("finish",er0)}function VC1(A){if(!this[eF]._receiver.write(A))this.pause()}function Io0(){let A=this[eF];A._readyState=cQ.CLOSING,A._receiver.end(),this.end()}function Yo0(){let A=this[eF];if(this.removeListener("error",Yo0),this.on("error",Bo0),A)A._readyState=cQ.CLOSING,this.destroy()}});
var Co0=E((Fp8,Vo0)=>{var Gp8=CC1(),{Duplex:tk9}=J1("stream");function Jo0(A){A.emit("close")}function ek9(){if(!this.destroyed&&this._writableState.finished)this.destroy()}function Xo0(A){if(this.removeListener("error",Xo0),this.destroy(),this.listenerCount("error")===0)this.emit("error",A)}function Ay9(A,B){let Q=!0,D=new tk9({...B,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return A.on("message",function Z(G,F){let I=!F&&D._readableState.objectMode?G.toString():G;if(!D.push(I))A.pause()}),A.once("error",function Z(G){if(D.destroyed)return;Q=!1,D.destroy(G)}),A.once("close",function Z(){if(D.destroyed)return;D.push(null)}),D._destroy=function(Z,G){if(A.readyState===A.CLOSED){G(Z),process.nextTick(Jo0,D);return}let F=!1;if(A.once("error",function I(Y){F=!0,G(Y)}),A.once("close",function I(){if(!F)G(Z);process.nextTick(Jo0,D)}),Q)A.terminate()},D._final=function(Z){if(A.readyState===A.CONNECTING){A.once("open",function G(){D._final(Z)});return}if(A._socket===null)return;if(A._socket._writableState.finished){if(Z(),D._readableState.endEmitted)D.destroy()}else A._socket.once("finish",function G(){Z()}),A.close()},D._read=function(){if(A.isPaused)A.resume()},D._write=function(Z,G,F){if(A.readyState===A.CONNECTING){A.once("open",function I(){D._write(Z,G,F)});return}A.send(Z,F)},D.on("end",ek9),D.on("error",Xo0),D}Vo0.exports=Ay9});
var Hp=E((rl8,FC1)=>{var{isUtf8:_r0}=J1("buffer"),{hasBlob:Qk9}=KO(),Dk9=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0];function Zk9(A){return A>=1000&&A<=1014&&A!==1004&&A!==1005&&A!==1006||A>=3000&&A<=4999}function vp1(A){let B=A.length,Q=0;while(Q<B)if((A[Q]&128)===0)Q++;else if((A[Q]&224)===192){if(Q+1===B||(A[Q+1]&192)!==128||(A[Q]&254)===192)return!1;Q+=2}else if((A[Q]&240)===224){if(Q+2>=B||(A[Q+1]&192)!==128||(A[Q+2]&192)!==128||A[Q]===224&&(A[Q+1]&224)===128||A[Q]===237&&(A[Q+1]&224)===160)return!1;Q+=3}else if((A[Q]&248)===240){if(Q+3>=B||(A[Q+1]&192)!==128||(A[Q+2]&192)!==128||(A[Q+3]&192)!==128||A[Q]===240&&(A[Q+1]&240)===128||A[Q]===244&&A[Q+1]>143||A[Q]>244)return!1;Q+=4}else return!1;return!0}function Gk9(A){return Qk9&&typeof A==="object"&&typeof A.arrayBuffer==="function"&&typeof A.type==="string"&&typeof A.stream==="function"&&(A[Symbol.toStringTag]==="Blob"||A[Symbol.toStringTag]==="File")}FC1.exports={isBlob:Gk9,isValidStatusCode:Zk9,isValidUTF8:vp1,tokenChars:Dk9};if(_r0)FC1.exports.isValidUTF8=function(A){return A.length<24?vp1(A):_r0(A)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let A=(()=>{throw new Error("Cannot require module "+"utf-8-validate");})();FC1.exports.isValidUTF8=function(B){return B.length<32?vp1(B):A(B)}}catch(A){}});
var KO=E((il8,qr0)=>{var wr0=["nodebuffer","arraybuffer","fragments"],$r0=typeof Blob!=="undefined";if($r0)wr0.push("blob");qr0.exports={BINARY_TYPES:wr0,EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",hasBlob:$r0,kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}});
var Tr0=E((al8,Or0)=>{var Mr0=Symbol("kDone"),xp1=Symbol("kRun");class Rr0{constructor(A){this[Mr0]=()=>{this.pending--,this[xp1]()},this.concurrency=A||1/0,this.jobs=[],this.pending=0}add(A){this.jobs.push(A),this[xp1]()}[xp1](){if(this.pending===this.concurrency)return;if(this.jobs.length){let A=this.jobs.shift();this.pending++,A(this[Mr0])}}}Or0.exports=Rr0});
var aB1=E((sl8,yr0)=>{var iB1=J1("zlib"),Pr0=pB1(),oj9=Tr0(),{kStatusCode:Sr0}=KO(),tj9=Buffer[Symbol.species],ej9=Buffer.from([0,0,255,255]),GC1=Symbol("permessage-deflate"),HO=Symbol("total-length"),nB1=Symbol("callback"),Dk=Symbol("buffers"),ZC1=Symbol("error"),DC1;class jr0{constructor(A,B,Q){if(this._maxPayload=Q|0,this._options=A||{},this._threshold=this._options.threshold!==void 0?this._options.threshold:1024,this._isServer=!!B,this._deflate=null,this._inflate=null,this.params=null,!DC1){let D=this._options.concurrencyLimit!==void 0?this._options.concurrencyLimit:10;DC1=new oj9(D)}}static get extensionName(){return"permessage-deflate"}offer(){let A={};if(this._options.serverNoContextTakeover)A.server_no_context_takeover=!0;if(this._options.clientNoContextTakeover)A.client_no_context_takeover=!0;if(this._options.serverMaxWindowBits)A.server_max_window_bits=this._options.serverMaxWindowBits;if(this._options.clientMaxWindowBits)A.client_max_window_bits=this._options.clientMaxWindowBits;else if(this._options.clientMaxWindowBits==null)A.client_max_window_bits=!0;return A}accept(A){return A=this.normalizeParams(A),this.params=this._isServer?this.acceptAsServer(A):this.acceptAsClient(A),this.params}cleanup(){if(this._inflate)this._inflate.close(),this._inflate=null;if(this._deflate){let A=this._deflate[nB1];if(this._deflate.close(),this._deflate=null,A)A(new Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(A){let B=this._options,Q=A.find((D)=>{if(B.serverNoContextTakeover===!1&&D.server_no_context_takeover||D.server_max_window_bits&&(B.serverMaxWindowBits===!1||typeof B.serverMaxWindowBits==="number"&&B.serverMaxWindowBits>D.server_max_window_bits)||typeof B.clientMaxWindowBits==="number"&&!D.client_max_window_bits)return!1;return!0});if(!Q)throw new Error("None of the extension offers can be accepted");if(B.serverNoContextTakeover)Q.server_no_context_takeover=!0;if(B.clientNoContextTakeover)Q.client_no_context_takeover=!0;if(typeof B.serverMaxWindowBits==="number")Q.server_max_window_bits=B.serverMaxWindowBits;if(typeof B.clientMaxWindowBits==="number")Q.client_max_window_bits=B.clientMaxWindowBits;else if(Q.client_max_window_bits===!0||B.clientMaxWindowBits===!1)delete Q.client_max_window_bits;return Q}acceptAsClient(A){let B=A[0];if(this._options.clientNoContextTakeover===!1&&B.client_no_context_takeover)throw new Error('Unexpected parameter "client_no_context_takeover"');if(!B.client_max_window_bits){if(typeof this._options.clientMaxWindowBits==="number")B.client_max_window_bits=this._options.clientMaxWindowBits}else if(this._options.clientMaxWindowBits===!1||typeof this._options.clientMaxWindowBits==="number"&&B.client_max_window_bits>this._options.clientMaxWindowBits)throw new Error('Unexpected or invalid parameter "client_max_window_bits"');return B}normalizeParams(A){return A.forEach((B)=>{Object.keys(B).forEach((Q)=>{let D=B[Q];if(D.length>1)throw new Error(`Parameter "${Q}" must have only a single value`);if(D=D[0],Q==="client_max_window_bits"){if(D!==!0){let Z=+D;if(!Number.isInteger(Z)||Z<8||Z>15)throw new TypeError(`Invalid value for parameter "${Q}": ${D}`);D=Z}else if(!this._isServer)throw new TypeError(`Invalid value for parameter "${Q}": ${D}`)}else if(Q==="server_max_window_bits"){let Z=+D;if(!Number.isInteger(Z)||Z<8||Z>15)throw new TypeError(`Invalid value for parameter "${Q}": ${D}`);D=Z}else if(Q==="client_no_context_takeover"||Q==="server_no_context_takeover"){if(D!==!0)throw new TypeError(`Invalid value for parameter "${Q}": ${D}`)}else throw new Error(`Unknown parameter "${Q}"`);B[Q]=D})}),A}decompress(A,B,Q){DC1.add((D)=>{this._decompress(A,B,(Z,G)=>{D(),Q(Z,G)})})}compress(A,B,Q){DC1.add((D)=>{this._compress(A,B,(Z,G)=>{D(),Q(Z,G)})})}_decompress(A,B,Q){let D=this._isServer?"client":"server";if(!this._inflate){let Z=`${D}_max_window_bits`,G=typeof this.params[Z]!=="number"?iB1.Z_DEFAULT_WINDOWBITS:this.params[Z];this._inflate=iB1.createInflateRaw({...this._options.zlibInflateOptions,windowBits:G}),this._inflate[GC1]=this,this._inflate[HO]=0,this._inflate[Dk]=[],this._inflate.on("error",Bk9),this._inflate.on("data",kr0)}if(this._inflate[nB1]=Q,this._inflate.write(A),B)this._inflate.write(ej9);this._inflate.flush(()=>{let Z=this._inflate[ZC1];if(Z){this._inflate.close(),this._inflate=null,Q(Z);return}let G=Pr0.concat(this._inflate[Dk],this._inflate[HO]);if(this._inflate._readableState.endEmitted)this._inflate.close(),this._inflate=null;else if(this._inflate[HO]=0,this._inflate[Dk]=[],B&&this.params[`${D}_no_context_takeover`])this._inflate.reset();Q(null,G)})}_compress(A,B,Q){let D=this._isServer?"server":"client";if(!this._deflate){let Z=`${D}_max_window_bits`,G=typeof this.params[Z]!=="number"?iB1.Z_DEFAULT_WINDOWBITS:this.params[Z];this._deflate=iB1.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:G}),this._deflate[HO]=0,this._deflate[Dk]=[],this._deflate.on("data",Ak9)}this._deflate[nB1]=Q,this._deflate.write(A),this._deflate.flush(iB1.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let Z=Pr0.concat(this._deflate[Dk],this._deflate[HO]);if(B)Z=new tj9(Z.buffer,Z.byteOffset,Z.length-4);if(this._deflate[nB1]=null,this._deflate[HO]=0,this._deflate[Dk]=[],B&&this.params[`${D}_no_context_takeover`])this._deflate.reset();Q(null,Z)})}}yr0.exports=jr0;function Ak9(A){this[Dk].push(A),this[HO]+=A.length}function kr0(A){if(this[HO]+=A.length,this[GC1]._maxPayload<1||this[HO]<=this[GC1]._maxPayload){this[Dk].push(A);return}this[ZC1]=new RangeError("Max payload size exceeded"),this[ZC1].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[ZC1][Sr0]=1009,this.removeListener("data",kr0),this.reset()}function Bk9(A){this[GC1]._inflate=null,A[Sr0]=1007,this[nB1](A)}});
var fp1=E((ol8,hr0)=>{var{Writable:Fk9}=J1("stream"),xr0=aB1(),{BINARY_TYPES:Ik9,EMPTY_BUFFER:vr0,kStatusCode:Yk9,kWebSocket:Wk9}=KO(),{concat:bp1,toArrayBuffer:Jk9,unmask:Xk9}=pB1(),{isValidStatusCode:Vk9,isValidUTF8:br0}=Hp(),IC1=Buffer[Symbol.species];class fr0 extends Fk9{constructor(A={}){super();this._allowSynchronousEvents=A.allowSynchronousEvents!==void 0?A.allowSynchronousEvents:!0,this._binaryType=A.binaryType||Ik9[0],this._extensions=A.extensions||{},this._isServer=!!A.isServer,this._maxPayload=A.maxPayload|0,this._skipUTF8Validation=!!A.skipUTF8Validation,this[Wk9]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(A,B,Q){if(this._opcode===8&&this._state==0)return Q();this._bufferedBytes+=A.length,this._buffers.push(A),this.startLoop(Q)}consume(A){if(this._bufferedBytes-=A,A===this._buffers[0].length)return this._buffers.shift();if(A<this._buffers[0].length){let Q=this._buffers[0];return this._buffers[0]=new IC1(Q.buffer,Q.byteOffset+A,Q.length-A),new IC1(Q.buffer,Q.byteOffset,A)}let B=Buffer.allocUnsafe(A);do{let Q=this._buffers[0],D=B.length-A;if(A>=Q.length)B.set(this._buffers.shift(),D);else B.set(new Uint8Array(Q.buffer,Q.byteOffset,A),D),this._buffers[0]=new IC1(Q.buffer,Q.byteOffset+A,Q.length-A);A-=Q.length}while(A>0);return B}startLoop(A){this._loop=!0;do switch(this._state){case 0:this.getInfo(A);break;case 1:this.getPayloadLength16(A);break;case 2:this.getPayloadLength64(A);break;case 3:this.getMask();break;case 4:this.getData(A);break;case 5:case 6:this._loop=!1;return}while(this._loop);if(!this._errored)A()}getInfo(A){if(this._bufferedBytes<2){this._loop=!1;return}let B=this.consume(2);if((B[0]&48)!==0){let D=this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3");A(D);return}let Q=(B[0]&64)===64;if(Q&&!this._extensions[xr0.extensionName]){let D=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");A(D);return}if(this._fin=(B[0]&128)===128,this._opcode=B[0]&15,this._payloadLength=B[1]&127,this._opcode===0){if(Q){let D=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");A(D);return}if(!this._fragmented){let D=this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE");A(D);return}this._opcode=this._fragmented}else if(this._opcode===1||this._opcode===2){if(this._fragmented){let D=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");A(D);return}this._compressed=Q}else if(this._opcode>7&&this._opcode<11){if(!this._fin){let D=this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN");A(D);return}if(Q){let D=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");A(D);return}if(this._payloadLength>125||this._opcode===8&&this._payloadLength===1){let D=this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH");A(D);return}}else{let D=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");A(D);return}if(!this._fin&&!this._fragmented)this._fragmented=this._opcode;if(this._masked=(B[1]&128)===128,this._isServer){if(!this._masked){let D=this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK");A(D);return}}else if(this._masked){let D=this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK");A(D);return}if(this._payloadLength===126)this._state=1;else if(this._payloadLength===127)this._state=2;else this.haveLength(A)}getPayloadLength16(A){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(A)}getPayloadLength64(A){if(this._bufferedBytes<8){this._loop=!1;return}let B=this.consume(8),Q=B.readUInt32BE(0);if(Q>Math.pow(2,21)-1){let D=this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH");A(D);return}this._payloadLength=Q*Math.pow(2,32)+B.readUInt32BE(4),this.haveLength(A)}haveLength(A){if(this._payloadLength&&this._opcode<8){if(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0){let B=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");A(B);return}}if(this._masked)this._state=3;else this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(A){let B=vr0;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}if(B=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!==0)Xk9(B,this._mask)}if(this._opcode>7){this.controlMessage(B,A);return}if(this._compressed){this._state=5,this.decompress(B,A);return}if(B.length)this._messageLength=this._totalPayloadLength,this._fragments.push(B);this.dataMessage(A)}decompress(A,B){this._extensions[xr0.extensionName].decompress(A,this._fin,(D,Z)=>{if(D)return B(D);if(Z.length){if(this._messageLength+=Z.length,this._messageLength>this._maxPayload&&this._maxPayload>0){let G=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");B(G);return}this._fragments.push(Z)}if(this.dataMessage(B),this._state===0)this.startLoop(B)})}dataMessage(A){if(!this._fin){this._state=0;return}let B=this._messageLength,Q=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],this._opcode===2){let D;if(this._binaryType==="nodebuffer")D=bp1(Q,B);else if(this._binaryType==="arraybuffer")D=Jk9(bp1(Q,B));else if(this._binaryType==="blob")D=new Blob(Q);else D=Q;if(this._allowSynchronousEvents)this.emit("message",D,!0),this._state=0;else this._state=6,setImmediate(()=>{this.emit("message",D,!0),this._state=0,this.startLoop(A)})}else{let D=bp1(Q,B);if(!this._skipUTF8Validation&&!br0(D)){let Z=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");A(Z);return}if(this._state===5||this._allowSynchronousEvents)this.emit("message",D,!1),this._state=0;else this._state=6,setImmediate(()=>{this.emit("message",D,!1),this._state=0,this.startLoop(A)})}}controlMessage(A,B){if(this._opcode===8){if(A.length===0)this._loop=!1,this.emit("conclude",1005,vr0),this.end();else{let Q=A.readUInt16BE(0);if(!Vk9(Q)){let Z=this.createError(RangeError,`invalid status code ${Q}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE");B(Z);return}let D=new IC1(A.buffer,A.byteOffset+2,A.length-2);if(!this._skipUTF8Validation&&!br0(D)){let Z=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");B(Z);return}this._loop=!1,this.emit("conclude",Q,D),this.end()}this._state=0;return}if(this._allowSynchronousEvents)this.emit(this._opcode===9?"ping":"pong",A),this._state=0;else this._state=6,setImmediate(()=>{this.emit(this._opcode===9?"ping":"pong",A),this._state=0,this.startLoop(B)})}createError(A,B,Q,D,Z){this._loop=!1,this._errored=!0;let G=new A(Q?`Invalid WebSocket frame: ${B}`:B);return Error.captureStackTrace(G,this.createError),G.code=Z,G[Yk9]=D,G}}hr0.exports=fr0});
var gp1=E((el8,mr0)=>{var{Duplex:tl8}=J1("stream"),{randomFillSync:Ck9}=J1("crypto"),gr0=aB1(),{EMPTY_BUFFER:Kk9,kWebSocket:Hk9,NOOP:zk9}=KO(),{isBlob:zp,isValidStatusCode:Ek9}=Hp(),{mask:ur0,toBuffer:df}=pB1(),pH=Symbol("kByteLength"),Uk9=Buffer.alloc(4),cf,Ep=8192,Bw=0,wk9=1,$k9=2;class Zk{constructor(A,B,Q){if(this._extensions=B||{},Q)this._generateMask=Q,this._maskBuffer=Buffer.alloc(4);this._socket=A,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._queue=[],this._state=Bw,this.onerror=zk9,this[Hk9]=void 0}static frame(A,B){let Q,D=!1,Z=2,G=!1;if(B.mask){if(Q=B.maskBuffer||Uk9,B.generateMask)B.generateMask(Q);else{if(Ep===8192){if(cf===void 0)cf=Buffer.alloc(8192);Ck9(cf,0,8192),Ep=0}Q[0]=cf[Ep++],Q[1]=cf[Ep++],Q[2]=cf[Ep++],Q[3]=cf[Ep++]}G=(Q[0]|Q[1]|Q[2]|Q[3])===0,Z=6}let F;if(typeof A==="string")if((!B.mask||G)&&B[pH]!==void 0)F=B[pH];else A=Buffer.from(A),F=A.length;else F=A.length,D=B.mask&&B.readOnly&&!G;let I=F;if(F>=65536)Z+=8,I=127;else if(F>125)Z+=2,I=126;let Y=Buffer.allocUnsafe(D?F+Z:Z);if(Y[0]=B.fin?B.opcode|128:B.opcode,B.rsv1)Y[0]|=64;if(Y[1]=I,I===126)Y.writeUInt16BE(F,2);else if(I===127)Y[2]=Y[3]=0,Y.writeUIntBE(F,4,6);if(!B.mask)return[Y,A];if(Y[1]|=128,Y[Z-4]=Q[0],Y[Z-3]=Q[1],Y[Z-2]=Q[2],Y[Z-1]=Q[3],G)return[Y,A];if(D)return ur0(A,Q,Y,Z,F),[Y];return ur0(A,Q,A,0,F),[Y,A]}close(A,B,Q,D){let Z;if(A===void 0)Z=Kk9;else if(typeof A!=="number"||!Ek9(A))throw new TypeError("First argument must be a valid error code number");else if(B===void 0||!B.length)Z=Buffer.allocUnsafe(2),Z.writeUInt16BE(A,0);else{let F=Buffer.byteLength(B);if(F>123)throw new RangeError("The message must not be greater than 123 bytes");if(Z=Buffer.allocUnsafe(2+F),Z.writeUInt16BE(A,0),typeof B==="string")Z.write(B,2);else Z.set(B,2)}let G={[pH]:Z.length,fin:!0,generateMask:this._generateMask,mask:Q,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};if(this._state!==Bw)this.enqueue([this.dispatch,Z,!1,G,D]);else this.sendFrame(Zk.frame(Z,G),D)}ping(A,B,Q){let D,Z;if(typeof A==="string")D=Buffer.byteLength(A),Z=!1;else if(zp(A))D=A.size,Z=!1;else A=df(A),D=A.length,Z=df.readOnly;if(D>125)throw new RangeError("The data size must not be greater than 125 bytes");let G={[pH]:D,fin:!0,generateMask:this._generateMask,mask:B,maskBuffer:this._maskBuffer,opcode:9,readOnly:Z,rsv1:!1};if(zp(A))if(this._state!==Bw)this.enqueue([this.getBlobData,A,!1,G,Q]);else this.getBlobData(A,!1,G,Q);else if(this._state!==Bw)this.enqueue([this.dispatch,A,!1,G,Q]);else this.sendFrame(Zk.frame(A,G),Q)}pong(A,B,Q){let D,Z;if(typeof A==="string")D=Buffer.byteLength(A),Z=!1;else if(zp(A))D=A.size,Z=!1;else A=df(A),D=A.length,Z=df.readOnly;if(D>125)throw new RangeError("The data size must not be greater than 125 bytes");let G={[pH]:D,fin:!0,generateMask:this._generateMask,mask:B,maskBuffer:this._maskBuffer,opcode:10,readOnly:Z,rsv1:!1};if(zp(A))if(this._state!==Bw)this.enqueue([this.getBlobData,A,!1,G,Q]);else this.getBlobData(A,!1,G,Q);else if(this._state!==Bw)this.enqueue([this.dispatch,A,!1,G,Q]);else this.sendFrame(Zk.frame(A,G),Q)}send(A,B,Q){let D=this._extensions[gr0.extensionName],Z=B.binary?2:1,G=B.compress,F,I;if(typeof A==="string")F=Buffer.byteLength(A),I=!1;else if(zp(A))F=A.size,I=!1;else A=df(A),F=A.length,I=df.readOnly;if(this._firstFragment){if(this._firstFragment=!1,G&&D&&D.params[D._isServer?"server_no_context_takeover":"client_no_context_takeover"])G=F>=D._threshold;this._compress=G}else G=!1,Z=0;if(B.fin)this._firstFragment=!0;let Y={[pH]:F,fin:B.fin,generateMask:this._generateMask,mask:B.mask,maskBuffer:this._maskBuffer,opcode:Z,readOnly:I,rsv1:G};if(zp(A))if(this._state!==Bw)this.enqueue([this.getBlobData,A,this._compress,Y,Q]);else this.getBlobData(A,this._compress,Y,Q);else if(this._state!==Bw)this.enqueue([this.dispatch,A,this._compress,Y,Q]);else this.dispatch(A,this._compress,Y,Q)}getBlobData(A,B,Q,D){this._bufferedBytes+=Q[pH],this._state=$k9,A.arrayBuffer().then((Z)=>{if(this._socket.destroyed){let F=new Error("The socket was closed while the blob was being read");process.nextTick(hp1,this,F,D);return}this._bufferedBytes-=Q[pH];let G=df(Z);if(!B)this._state=Bw,this.sendFrame(Zk.frame(G,Q),D),this.dequeue();else this.dispatch(G,B,Q,D)}).catch((Z)=>{process.nextTick(qk9,this,Z,D)})}dispatch(A,B,Q,D){if(!B){this.sendFrame(Zk.frame(A,Q),D);return}let Z=this._extensions[gr0.extensionName];this._bufferedBytes+=Q[pH],this._state=wk9,Z.compress(A,Q.fin,(G,F)=>{if(this._socket.destroyed){let I=new Error("The socket was closed while data was being compressed");hp1(this,I,D);return}this._bufferedBytes-=Q[pH],this._state=Bw,Q.readOnly=!1,this.sendFrame(Zk.frame(F,Q),D),this.dequeue()})}dequeue(){while(this._state===Bw&&this._queue.length){let A=this._queue.shift();this._bufferedBytes-=A[3][pH],Reflect.apply(A[0],this,A.slice(1))}}enqueue(A){this._bufferedBytes+=A[3][pH],this._queue.push(A)}sendFrame(A,B){if(A.length===2)this._socket.cork(),this._socket.write(A[0]),this._socket.write(A[1],B),this._socket.uncork();else this._socket.write(A[0],B)}}mr0.exports=Zk;function hp1(A,B,Q){if(typeof Q==="function")Q(B);for(let D=0;D<A._queue.length;D++){let Z=A._queue[D],G=Z[Z.length-1];if(typeof G==="function")G(B)}}function qk9(A,B,Q){hp1(A,B,Q),A.onerror(B)}});
var mp1=E((Bp8,or0)=>{var{tokenChars:oB1}=Hp();function XN(A,B,Q){if(A[B]===void 0)A[B]=[Q];else A[B].push(Q)}function Lk9(A){let B=Object.create(null),Q=Object.create(null),D=!1,Z=!1,G=!1,F,I,Y=-1,W=-1,J=-1,X=0;for(;X<A.length;X++)if(W=A.charCodeAt(X),F===void 0)if(J===-1&&oB1[W]===1){if(Y===-1)Y=X}else if(X!==0&&(W===32||W===9)){if(J===-1&&Y!==-1)J=X}else if(W===59||W===44){if(Y===-1)throw new SyntaxError(`Unexpected character at index ${X}`);if(J===-1)J=X;let C=A.slice(Y,J);if(W===44)XN(B,C,Q),Q=Object.create(null);else F=C;Y=J=-1}else throw new SyntaxError(`Unexpected character at index ${X}`);else if(I===void 0)if(J===-1&&oB1[W]===1){if(Y===-1)Y=X}else if(W===32||W===9){if(J===-1&&Y!==-1)J=X}else if(W===59||W===44){if(Y===-1)throw new SyntaxError(`Unexpected character at index ${X}`);if(J===-1)J=X;if(XN(Q,A.slice(Y,J),!0),W===44)XN(B,F,Q),Q=Object.create(null),F=void 0;Y=J=-1}else if(W===61&&Y!==-1&&J===-1)I=A.slice(Y,X),Y=J=-1;else throw new SyntaxError(`Unexpected character at index ${X}`);else if(Z){if(oB1[W]!==1)throw new SyntaxError(`Unexpected character at index ${X}`);if(Y===-1)Y=X;else if(!D)D=!0;Z=!1}else if(G)if(oB1[W]===1){if(Y===-1)Y=X}else if(W===34&&Y!==-1)G=!1,J=X;else if(W===92)Z=!0;else throw new SyntaxError(`Unexpected character at index ${X}`);else if(W===34&&A.charCodeAt(X-1)===61)G=!0;else if(J===-1&&oB1[W]===1){if(Y===-1)Y=X}else if(Y!==-1&&(W===32||W===9)){if(J===-1)J=X}else if(W===59||W===44){if(Y===-1)throw new SyntaxError(`Unexpected character at index ${X}`);if(J===-1)J=X;let C=A.slice(Y,J);if(D)C=C.replace(/\\/g,""),D=!1;if(XN(Q,I,C),W===44)XN(B,F,Q),Q=Object.create(null),F=void 0;I=void 0,Y=J=-1}else throw new SyntaxError(`Unexpected character at index ${X}`);if(Y===-1||G||W===32||W===9)throw new SyntaxError("Unexpected end of input");if(J===-1)J=X;let V=A.slice(Y,J);if(F===void 0)XN(B,V,Q);else{if(I===void 0)XN(Q,V,!0);else if(D)XN(Q,I,V.replace(/\\/g,""));else XN(Q,I,V);XN(B,F,Q)}return B}function Mk9(A){return Object.keys(A).map((B)=>{let Q=A[B];if(!Array.isArray(Q))Q=[Q];return Q.map((D)=>{return[B].concat(Object.keys(D).map((Z)=>{let G=D[Z];if(!Array.isArray(G))G=[G];return G.map((F)=>F===!0?Z:`${Z}=${F}`).join("; ")})).join("; ")}).join(", ")}).join(", ")}or0.exports={format:Mk9,parse:Lk9}});
var pB1=E((nl8,QC1)=>{var{EMPTY_BUFFER:aj9}=KO(),yp1=Buffer[Symbol.species];function sj9(A,B){if(A.length===0)return aj9;if(A.length===1)return A[0];let Q=Buffer.allocUnsafe(B),D=0;for(let Z=0;Z<A.length;Z++){let G=A[Z];Q.set(G,D),D+=G.length}if(D<B)return new yp1(Q.buffer,Q.byteOffset,D);return Q}function Nr0(A,B,Q,D,Z){for(let G=0;G<Z;G++)Q[D+G]=A[G]^B[G&3]}function Lr0(A,B){for(let Q=0;Q<A.length;Q++)A[Q]^=B[Q&3]}function rj9(A){if(A.length===A.buffer.byteLength)return A.buffer;return A.buffer.slice(A.byteOffset,A.byteOffset+A.length)}function _p1(A){if(_p1.readOnly=!0,Buffer.isBuffer(A))return A;let B;if(A instanceof ArrayBuffer)B=new yp1(A);else if(ArrayBuffer.isView(A))B=new yp1(A.buffer,A.byteOffset,A.byteLength);else B=Buffer.from(A),_p1.readOnly=!1;return B}QC1.exports={concat:sj9,mask:Nr0,toArrayBuffer:rj9,toBuffer:_p1,unmask:Lr0};if(!process.env.WS_NO_BUFFER_UTIL)try{let A=(()=>{throw new Error("Cannot require module "+"bufferutil");})();QC1.exports.mask=function(B,Q,D,Z,G){if(G<48)Nr0(B,Q,D,Z,G);else A.mask(B,Q,D,Z,G)},QC1.exports.unmask=function(B,Q){if(B.length<32)Lr0(B,Q);else A.unmask(B,Q)}}catch(A){}});
var rr0=E((Ap8,sr0)=>{var{kForOnEventAttribute:sB1,kListener:up1}=KO(),dr0=Symbol("kCode"),cr0=Symbol("kData"),lr0=Symbol("kError"),pr0=Symbol("kMessage"),ir0=Symbol("kReason"),Up=Symbol("kTarget"),nr0=Symbol("kType"),ar0=Symbol("kWasClean");class Gk{constructor(A){this[Up]=null,this[nr0]=A}get target(){return this[Up]}get type(){return this[nr0]}}Object.defineProperty(Gk.prototype,"target",{enumerable:!0});Object.defineProperty(Gk.prototype,"type",{enumerable:!0});class wp extends Gk{constructor(A,B={}){super(A);this[dr0]=B.code===void 0?0:B.code,this[ir0]=B.reason===void 0?"":B.reason,this[ar0]=B.wasClean===void 0?!1:B.wasClean}get code(){return this[dr0]}get reason(){return this[ir0]}get wasClean(){return this[ar0]}}Object.defineProperty(wp.prototype,"code",{enumerable:!0});Object.defineProperty(wp.prototype,"reason",{enumerable:!0});Object.defineProperty(wp.prototype,"wasClean",{enumerable:!0});class rB1 extends Gk{constructor(A,B={}){super(A);this[lr0]=B.error===void 0?null:B.error,this[pr0]=B.message===void 0?"":B.message}get error(){return this[lr0]}get message(){return this[pr0]}}Object.defineProperty(rB1.prototype,"error",{enumerable:!0});Object.defineProperty(rB1.prototype,"message",{enumerable:!0});class WC1 extends Gk{constructor(A,B={}){super(A);this[cr0]=B.data===void 0?null:B.data}get data(){return this[cr0]}}Object.defineProperty(WC1.prototype,"data",{enumerable:!0});var Nk9={addEventListener(A,B,Q={}){for(let Z of this.listeners(A))if(!Q[sB1]&&Z[up1]===B&&!Z[sB1])return;let D;if(A==="message")D=function Z(G,F){let I=new WC1("message",{data:F?G:G.toString()});I[Up]=this,YC1(B,this,I)};else if(A==="close")D=function Z(G,F){let I=new wp("close",{code:G,reason:F.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});I[Up]=this,YC1(B,this,I)};else if(A==="error")D=function Z(G){let F=new rB1("error",{error:G,message:G.message});F[Up]=this,YC1(B,this,F)};else if(A==="open")D=function Z(){let G=new Gk("open");G[Up]=this,YC1(B,this,G)};else return;if(D[sB1]=!!Q[sB1],D[up1]=B,Q.once)this.once(A,D);else this.on(A,D)},removeEventListener(A,B){for(let Q of this.listeners(A))if(Q[up1]===B&&!Q[sB1]){this.removeListener(A,Q);break}}};sr0.exports={CloseEvent:wp,ErrorEvent:rB1,Event:Gk,EventTarget:Nk9,MessageEvent:WC1};function YC1(A,B,Q){if(typeof A==="object"&&A.handleEvent)A.handleEvent.call(A,Q);else A.call(B,Q)}});

module.exports = Co0;
