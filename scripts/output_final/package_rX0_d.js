// dependency_chain package extracted with entry point: rX0

var BBB=E(($m5,ABB)=>{var VL6="Expected a function",t2B=NaN,CL6="[object Symbol]",KL6=/^\s+|\s+$/g,HL6=/^[-+]0x[0-9a-f]+$/i,zL6=/^0b[01]+$/i,EL6=/^0o[0-7]+$/i,UL6=parseInt,wL6=typeof global=="object"&&global&&global.Object===Object&&global,$L6=typeof self=="object"&&self&&self.Object===Object&&self,qL6=wL6||$L6||Function("return this")(),NL6=Object.prototype,LL6=NL6.toString,ML6=Math.max,RL6=Math.min,aX0=function(){return qL6.Date.now()};function OL6(A,B,Q){var D,Z,G,F,I,Y,W=0,J=!1,X=!1,V=!0;if(typeof A!="function")throw new TypeError(VL6);if(B=e2B(B)||0,sX0(Q))J=!!Q.leading,X="maxWait"in Q,G=X?ML6(e2B(Q.maxWait)||0,B):G,V="trailing"in Q?!!Q.trailing:V;function C(T){var j=D,f=Z;return D=Z=void 0,W=T,F=A.apply(f,j),F}function K(T){return W=T,I=setTimeout($,B),J?C(T):F}function H(T){var j=T-Y,f=T-W,k=B-j;return X?RL6(k,G-f):k}function z(T){var j=T-Y,f=T-W;return Y===void 0||j>=B||j<0||X&&f>=G}function $(){var T=aX0();if(z(T))return L(T);I=setTimeout($,H(T))}function L(T){if(I=void 0,V&&D)return C(T);return D=Z=void 0,F}function N(){if(I!==void 0)clearTimeout(I);W=0,D=Y=Z=I=void 0}function O(){return I===void 0?F:L(aX0())}function R(){var T=aX0(),j=z(T);if(D=arguments,Z=this,Y=T,j){if(I===void 0)return K(Y);if(X)return I=setTimeout($,B),C(Y)}if(I===void 0)I=setTimeout($,B);return F}return R.cancel=N,R.flush=O,R}function sX0(A){var B=typeof A;return!!A&&(B=="object"||B=="function")}function TL6(A){return!!A&&typeof A=="object"}function PL6(A){return typeof A=="symbol"||TL6(A)&&LL6.call(A)==CL6}function e2B(A){if(typeof A=="number")return A;if(PL6(A))return t2B;if(sX0(A)){var B=typeof A.valueOf=="function"?A.valueOf():A;A=sX0(B)?B+"":B}if(typeof A!="string")return A===0?A:+A;A=A.replace(KL6,"");var Q=zL6.test(A);return Q||EL6.test(A)?UL6(A.slice(2),Q?2:8):HL6.test(A)?t2B:+A}ABB.exports=OL6});

module.exports = rX0;
