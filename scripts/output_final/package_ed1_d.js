// E_module package extracted with entry point: ed1

var $A=E((Jm1)=>{Object.defineProperty(Jm1,"__esModule",{value:!0});var o09=Aj0(),NW1=Yu1(),Gm1=Vu1(),t09=Cu1(),Fm1=RW(),e09=Bk0(),OW=vH(),AA9=Fk0(),LW1=lU(),BA9=mu1(),yj=eA1(),Im1=uu1(),MW1=D21(),tR=bH(),eb=Ek0(),QA9=$k0(),Af=Sk0(),Ym1=_k0(),G21=FW1(),F21=oA1(),_j=Ou1(),Wm1=lu1(),I21=au1(),Y21=ik0(),Sy0=gu1(),cq=eu1(),DA9=ok0(),W21=Qy0(),ec=ru1(),RW1=iu1(),ZA9=Fy0(),GA9=Yy0(),OW1=Vy0(),jy0=Ky0(),FA9=Ey0(),IA9=$y0(),YA9=Dm1(),WA9=Ly0(),JA9=Qm1(),XA9=Zm1(),VA9=Oy0(),CA9=Uu1(),KA9=Lu1(),ky0=fu1(),HA9=Pu1(),zA9=bu1(),EA9=ku1(),UA9=xu1(),wA9=rR(),$A9=GW1(),qA9=Py0(),NA9=vu1();Jm1.applyAggregateErrorsToEvent=o09.applyAggregateErrorsToEvent;Jm1.getComponentName=NW1.getComponentName;Jm1.getDomElement=NW1.getDomElement;Jm1.getLocationHref=NW1.getLocationHref;Jm1.htmlTreeAsString=NW1.htmlTreeAsString;Jm1.dsnFromString=Gm1.dsnFromString;Jm1.dsnToString=Gm1.dsnToString;Jm1.makeDsn=Gm1.makeDsn;Jm1.SentryError=t09.SentryError;Jm1.GLOBAL_OBJ=Fm1.GLOBAL_OBJ;Jm1.getGlobalObject=Fm1.getGlobalObject;Jm1.getGlobalSingleton=Fm1.getGlobalSingleton;Jm1.addInstrumentationHandler=e09.addInstrumentationHandler;Jm1.isDOMError=OW.isDOMError;Jm1.isDOMException=OW.isDOMException;Jm1.isElement=OW.isElement;Jm1.isError=OW.isError;Jm1.isErrorEvent=OW.isErrorEvent;Jm1.isEvent=OW.isEvent;Jm1.isInstanceOf=OW.isInstanceOf;Jm1.isNaN=OW.isNaN;Jm1.isParameterizedString=OW.isParameterizedString;Jm1.isPlainObject=OW.isPlainObject;Jm1.isPrimitive=OW.isPrimitive;Jm1.isRegExp=OW.isRegExp;Jm1.isString=OW.isString;Jm1.isSyntheticEvent=OW.isSyntheticEvent;Jm1.isThenable=OW.isThenable;Jm1.isVueViewModel=OW.isVueViewModel;Jm1.isBrowser=AA9.isBrowser;Jm1.CONSOLE_LEVELS=LW1.CONSOLE_LEVELS;Jm1.consoleSandbox=LW1.consoleSandbox;Jm1.logger=LW1.logger;Jm1.originalConsoleMethods=LW1.originalConsoleMethods;Jm1.memoBuilder=BA9.memoBuilder;Jm1.addContextToFrame=yj.addContextToFrame;Jm1.addExceptionMechanism=yj.addExceptionMechanism;Jm1.addExceptionTypeValue=yj.addExceptionTypeValue;Jm1.arrayify=yj.arrayify;Jm1.checkOrSetAlreadyCaught=yj.checkOrSetAlreadyCaught;Jm1.getEventDescription=yj.getEventDescription;Jm1.parseSemver=yj.parseSemver;Jm1.uuid4=yj.uuid4;Jm1.dynamicRequire=Im1.dynamicRequire;Jm1.isNodeEnv=Im1.isNodeEnv;Jm1.loadModule=Im1.loadModule;Jm1.normalize=MW1.normalize;Jm1.normalizeToSize=MW1.normalizeToSize;Jm1.normalizeUrlToBase=MW1.normalizeUrlToBase;Jm1.walk=MW1.walk;Jm1.addNonEnumerableProperty=tR.addNonEnumerableProperty;Jm1.convertToPlainObject=tR.convertToPlainObject;Jm1.dropUndefinedKeys=tR.dropUndefinedKeys;Jm1.extractExceptionKeysForMessage=tR.extractExceptionKeysForMessage;Jm1.fill=tR.fill;Jm1.getOriginalFunction=tR.getOriginalFunction;Jm1.markFunctionWrapped=tR.markFunctionWrapped;Jm1.objectify=tR.objectify;Jm1.urlEncode=tR.urlEncode;Jm1.basename=eb.basename;Jm1.dirname=eb.dirname;Jm1.isAbsolute=eb.isAbsolute;Jm1.join=eb.join;Jm1.normalizePath=eb.normalizePath;Jm1.relative=eb.relative;Jm1.resolve=eb.resolve;Jm1.makePromiseBuffer=QA9.makePromiseBuffer;Jm1.DEFAULT_USER_INCLUDES=Af.DEFAULT_USER_INCLUDES;Jm1.addRequestDataToEvent=Af.addRequestDataToEvent;Jm1.addRequestDataToTransaction=Af.addRequestDataToTransaction;Jm1.extractPathForTransaction=Af.extractPathForTransaction;Jm1.extractRequestData=Af.extractRequestData;Jm1.winterCGHeadersToDict=Af.winterCGHeadersToDict;Jm1.winterCGRequestToRequestData=Af.winterCGRequestToRequestData;Jm1.severityFromString=Ym1.severityFromString;Jm1.severityLevelFromString=Ym1.severityLevelFromString;Jm1.validSeverityLevels=Ym1.validSeverityLevels;Jm1.createStackParser=G21.createStackParser;Jm1.getFunctionName=G21.getFunctionName;Jm1.nodeStackLineParser=G21.nodeStackLineParser;Jm1.stackParserFromStackParserOptions=G21.stackParserFromStackParserOptions;Jm1.stripSentryFramesAndReverse=G21.stripSentryFramesAndReverse;Jm1.isMatchingPattern=F21.isMatchingPattern;Jm1.safeJoin=F21.safeJoin;Jm1.snipLine=F21.snipLine;Jm1.stringMatchesSomePattern=F21.stringMatchesSomePattern;Jm1.truncate=F21.truncate;Jm1.isNativeFetch=_j.isNativeFetch;Jm1.supportsDOMError=_j.supportsDOMError;Jm1.supportsDOMException=_j.supportsDOMException;Jm1.supportsErrorEvent=_j.supportsErrorEvent;Jm1.supportsFetch=_j.supportsFetch;Jm1.supportsNativeFetch=_j.supportsNativeFetch;Jm1.supportsReferrerPolicy=_j.supportsReferrerPolicy;Jm1.supportsReportingObserver=_j.supportsReportingObserver;Jm1.SyncPromise=Wm1.SyncPromise;Jm1.rejectedSyncPromise=Wm1.rejectedSyncPromise;Jm1.resolvedSyncPromise=Wm1.resolvedSyncPromise;Object.defineProperty(Jm1,"_browserPerformanceTimeOriginMode",{enumerable:!0,get:()=>I21._browserPerformanceTimeOriginMode});Jm1.browserPerformanceTimeOrigin=I21.browserPerformanceTimeOrigin;Jm1.dateTimestampInSeconds=I21.dateTimestampInSeconds;Jm1.timestampInSeconds=I21.timestampInSeconds;Jm1.timestampWithMs=I21.timestampWithMs;Jm1.TRACEPARENT_REGEXP=Y21.TRACEPARENT_REGEXP;Jm1.extractTraceparentData=Y21.extractTraceparentData;Jm1.generateSentryTraceHeader=Y21.generateSentryTraceHeader;Jm1.propagationContextFromHeaders=Y21.propagationContextFromHeaders;Jm1.tracingContextFromHeaders=Y21.tracingContextFromHeaders;Jm1.getSDKSource=Sy0.getSDKSource;Jm1.isBrowserBundle=Sy0.isBrowserBundle;Jm1.addItemToEnvelope=cq.addItemToEnvelope;Jm1.createAttachmentEnvelopeItem=cq.createAttachmentEnvelopeItem;Jm1.createEnvelope=cq.createEnvelope;Jm1.createEventEnvelopeHeaders=cq.createEventEnvelopeHeaders;Jm1.envelopeContainsItemType=cq.envelopeContainsItemType;Jm1.envelopeItemTypeToDataCategory=cq.envelopeItemTypeToDataCategory;Jm1.forEachEnvelopeItem=cq.forEachEnvelopeItem;Jm1.getSdkMetadataForEnvelopeHeader=cq.getSdkMetadataForEnvelopeHeader;Jm1.parseEnvelope=cq.parseEnvelope;Jm1.serializeEnvelope=cq.serializeEnvelope;Jm1.createClientReportEnvelope=DA9.createClientReportEnvelope;Jm1.DEFAULT_RETRY_AFTER=W21.DEFAULT_RETRY_AFTER;Jm1.disabledUntil=W21.disabledUntil;Jm1.isRateLimited=W21.isRateLimited;Jm1.parseRetryAfterHeader=W21.parseRetryAfterHeader;Jm1.updateRateLimits=W21.updateRateLimits;Jm1.BAGGAGE_HEADER_NAME=ec.BAGGAGE_HEADER_NAME;Jm1.MAX_BAGGAGE_STRING_LENGTH=ec.MAX_BAGGAGE_STRING_LENGTH;Jm1.SENTRY_BAGGAGE_KEY_PREFIX=ec.SENTRY_BAGGAGE_KEY_PREFIX;Jm1.SENTRY_BAGGAGE_KEY_PREFIX_REGEX=ec.SENTRY_BAGGAGE_KEY_PREFIX_REGEX;Jm1.baggageHeaderToDynamicSamplingContext=ec.baggageHeaderToDynamicSamplingContext;Jm1.dynamicSamplingContextToSentryBaggageHeader=ec.dynamicSamplingContextToSentryBaggageHeader;Jm1.getNumberOfUrlSegments=RW1.getNumberOfUrlSegments;Jm1.getSanitizedUrlString=RW1.getSanitizedUrlString;Jm1.parseUrl=RW1.parseUrl;Jm1.stripUrlQueryAndFragment=RW1.stripUrlQueryAndFragment;Jm1.addOrUpdateIntegration=ZA9.addOrUpdateIntegration;Jm1.makeFifoCache=GA9.makeFifoCache;Jm1.eventFromMessage=OW1.eventFromMessage;Jm1.eventFromUnknownInput=OW1.eventFromUnknownInput;Jm1.exceptionFromError=OW1.exceptionFromError;Jm1.parseStackFrames=OW1.parseStackFrames;Jm1.callFrameToStackFrame=jy0.callFrameToStackFrame;Jm1.watchdogTimer=jy0.watchdogTimer;Jm1.LRUMap=FA9.LRUMap;Jm1._asyncNullishCoalesce=IA9._asyncNullishCoalesce;Jm1._asyncOptionalChain=YA9._asyncOptionalChain;Jm1._asyncOptionalChainDelete=WA9._asyncOptionalChainDelete;Jm1._nullishCoalesce=JA9._nullishCoalesce;Jm1._optionalChain=XA9._optionalChain;Jm1._optionalChainDelete=VA9._optionalChainDelete;Jm1.addConsoleInstrumentationHandler=CA9.addConsoleInstrumentationHandler;Jm1.addClickKeypressInstrumentationHandler=KA9.addClickKeypressInstrumentationHandler;Jm1.SENTRY_XHR_DATA_KEY=ky0.SENTRY_XHR_DATA_KEY;Jm1.addXhrInstrumentationHandler=ky0.addXhrInstrumentationHandler;Jm1.addFetchInstrumentationHandler=HA9.addFetchInstrumentationHandler;Jm1.addHistoryInstrumentationHandler=zA9.addHistoryInstrumentationHandler;Jm1.addGlobalErrorInstrumentationHandler=EA9.addGlobalErrorInstrumentationHandler;Jm1.addGlobalUnhandledRejectionInstrumentationHandler=UA9.addGlobalUnhandledRejectionInstrumentationHandler;Jm1.resetInstrumentationHandlers=wA9.resetInstrumentationHandlers;Jm1.filenameIsInApp=$A9.filenameIsInApp;Jm1.escapeStringForRegex=qA9.escapeStringForRegex;Jm1.supportsHistory=NA9.supportsHistory});
var $b0=E((wb0)=>{Object.defineProperty(wb0,"__esModule",{value:!0});var Wd1=OQ(),Ub0=$A(),OF9=oX(),TF9=vj();function PF9(A){return!!A&&!!A.$use}class KJ1{static __initStatic(){this.id="Prisma"}constructor(A={}){if(this.name=KJ1.id,PF9(A.client)&&!A.client._sentryInstrumented){Ub0.addNonEnumerableProperty(A.client,"_sentryInstrumented",!0);let B={};try{let Q=A.client._engineConfig;if(Q){let{activeProvider:D,clientVersion:Z}=Q;if(D)B["db.system"]=D;if(Z)B["db.prisma.version"]=Z}}catch(Q){}A.client.$use((Q,D)=>{if(TF9.shouldDisableAutoInstrumentation(Wd1.getCurrentHub))return D(Q);let{action:Z,model:G}=Q;return Wd1.startSpan({name:G?`${G} ${Z}`:Z,onlyIfParent:!0,op:"db.prisma",attributes:{[Wd1.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]:"auto.db.prisma"},data:{...B,"db.operation":Z}},()=>D(Q))})}else OF9.DEBUG_BUILD&&Ub0.logger.warn("Unsupported Prisma client provided to PrismaIntegration. Provided client:",A.client)}setupOnce(){}}KJ1.__initStatic();wb0.Prisma=KJ1});
var $d1=E((Mf0)=>{Object.defineProperty(Mf0,"__esModule",{value:!0});var ZO=OQ(),L3=$A(),_C=oX(),Jf=wl(),GO=yC(),PY9=qJ1(),FO=wf0(),SY9=S21(),jY9=**********;function HF(A){return A/1000}function wd1(){return GO.WINDOW&&GO.WINDOW.addEventListener&&GO.WINDOW.performance}var $f0=0,rD={},aq,y21;function kY9(){let A=wd1();if(A&&L3.browserPerformanceTimeOrigin){if(A.mark)GO.WINDOW.performance.mark("sentry-tracing-init");let B=fY9(),Q=vY9(),D=bY9(),Z=hY9();return()=>{B(),Q(),D(),Z()}}return()=>{return}}function yY9(){Jf.addPerformanceInstrumentationHandler("longtask",({entries:A})=>{for(let B of A){let Q=ZO.getActiveTransaction();if(!Q)return;let D=HF(L3.browserPerformanceTimeOrigin+B.startTime),Z=HF(B.duration);Q.startChild({description:"Main UI thread blocked",op:"ui.long-task",origin:"auto.ui.browser.metrics",startTimestamp:D,endTimestamp:D+Z})}})}function _Y9(){Jf.addPerformanceInstrumentationHandler("event",({entries:A})=>{for(let B of A){let Q=ZO.getActiveTransaction();if(!Q)return;if(B.name==="click"){let D=HF(L3.browserPerformanceTimeOrigin+B.startTime),Z=HF(B.duration),G={description:L3.htmlTreeAsString(B.target),op:`ui.interaction.${B.name}`,origin:"auto.ui.browser.metrics",startTimestamp:D,endTimestamp:D+Z},F=L3.getComponentName(B.target);if(F)G.attributes={"ui.component_name":F};Q.startChild(G)}}})}function xY9(A,B){if(wd1()&&L3.browserPerformanceTimeOrigin){let D=gY9(A,B);return()=>{D()}}return()=>{return}}function vY9(){return Jf.addClsInstrumentationHandler(({metric:A})=>{let B=A.entries[A.entries.length-1];if(!B)return;_C.DEBUG_BUILD&&L3.logger.log("[Measurements] Adding CLS"),rD.cls={value:A.value,unit:""},y21=B},!0)}function bY9(){return Jf.addLcpInstrumentationHandler(({metric:A})=>{let B=A.entries[A.entries.length-1];if(!B)return;_C.DEBUG_BUILD&&L3.logger.log("[Measurements] Adding LCP"),rD.lcp={value:A.value,unit:"millisecond"},aq=B},!0)}function fY9(){return Jf.addFidInstrumentationHandler(({metric:A})=>{let B=A.entries[A.entries.length-1];if(!B)return;let Q=HF(L3.browserPerformanceTimeOrigin),D=HF(B.startTime);_C.DEBUG_BUILD&&L3.logger.log("[Measurements] Adding FID"),rD.fid={value:A.value,unit:"millisecond"},rD["mark.fid"]={value:Q+D,unit:"second"}})}function hY9(){return Jf.addTtfbInstrumentationHandler(({metric:A})=>{if(!A.entries[A.entries.length-1])return;_C.DEBUG_BUILD&&L3.logger.log("[Measurements] Adding TTFB"),rD.ttfb={value:A.value,unit:"millisecond"}})}var qf0={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function gY9(A,B){return Jf.addInpInstrumentationHandler(({metric:Q})=>{if(Q.value===void 0)return;let D=Q.entries.find((N)=>N.duration===Q.value&&qf0[N.name]!==void 0),Z=ZO.getClient();if(!D||!Z)return;let G=qf0[D.name],F=Z.getOptions(),I=HF(L3.browserPerformanceTimeOrigin+D.startTime),Y=HF(Q.value),W=D.interactionId!==void 0?A[D.interactionId]:void 0;if(W===void 0)return;let{routeName:J,parentContext:X,activeTransaction:V,user:C,replayId:K}=W,H=C!==void 0?C.email||C.id||C.ip_address:void 0,z=V!==void 0?V.getProfileId():void 0,$=new ZO.Span({startTimestamp:I,endTimestamp:I+Y,op:`ui.interaction.${G}`,name:L3.htmlTreeAsString(D.target),attributes:{release:F.release,environment:F.environment,transaction:J,...H!==void 0&&H!==""?{user:H}:{},...z!==void 0?{profile_id:z}:{},...K!==void 0?{replay_id:K}:{}},exclusiveTime:Q.value,measurements:{inp:{value:Q.value,unit:"millisecond"}}}),L=iY9(X,F,B);if(!L)return;if(Math.random()<L){let N=$?ZO.createSpanEnvelope([$],Z.getDsn()):void 0,O=Z&&Z.getTransport();if(O&&N)O.send(N).then(null,(R)=>{_C.DEBUG_BUILD&&L3.logger.error("Error while sending interaction:",R)});return}})}function uY9(A){let B=wd1();if(!B||!GO.WINDOW.performance.getEntries||!L3.browserPerformanceTimeOrigin)return;_C.DEBUG_BUILD&&L3.logger.log("[Tracing] Adding & adjusting spans using Performance API");let Q=HF(L3.browserPerformanceTimeOrigin),D=B.getEntries(),{op:Z,start_timestamp:G}=ZO.spanToJSON(A);if(D.slice($f0).forEach((F)=>{let I=HF(F.startTime),Y=HF(F.duration);if(A.op==="navigation"&&G&&Q+I<G)return;switch(F.entryType){case"navigation":{mY9(A,F,Q);break}case"mark":case"paint":case"measure":{Nf0(A,F,I,Y,Q);let W=PY9.getVisibilityWatcher(),J=F.startTime<W.firstHiddenTime;if(F.name==="first-paint"&&J)_C.DEBUG_BUILD&&L3.logger.log("[Measurements] Adding FP"),rD.fp={value:F.startTime,unit:"millisecond"};if(F.name==="first-contentful-paint"&&J)_C.DEBUG_BUILD&&L3.logger.log("[Measurements] Adding FCP"),rD.fcp={value:F.startTime,unit:"millisecond"};break}case"resource":{Lf0(A,F,F.name,I,Y,Q);break}}}),$f0=Math.max(D.length-1,0),cY9(A),Z==="pageload"){pY9(rD),["fcp","fp","lcp"].forEach((I)=>{if(!rD[I]||!G||Q>=G)return;let Y=rD[I].value,W=Q+HF(Y),J=Math.abs((W-G)*1000),X=J-Y;_C.DEBUG_BUILD&&L3.logger.log(`[Measurements] Normalized ${I} from ${Y} to ${J} (${X})`),rD[I].value=J});let F=rD["mark.fid"];if(F&&rD.fid)FO._startChild(A,{description:"first input delay",endTimestamp:F.value+HF(rD.fid.value),op:"ui.action",origin:"auto.ui.browser.metrics",startTimestamp:F.value}),delete rD["mark.fid"];if(!("fcp"in rD))delete rD.cls;Object.keys(rD).forEach((I)=>{ZO.setMeasurement(I,rD[I].value,rD[I].unit)}),lY9(A)}aq=void 0,y21=void 0,rD={}}function Nf0(A,B,Q,D,Z){let G=Z+Q,F=G+D;return FO._startChild(A,{description:B.name,endTimestamp:F,op:B.entryType,origin:"auto.resource.browser.metrics",startTimestamp:G}),G}function mY9(A,B,Q){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach((D)=>{MJ1(A,B,D,Q)}),MJ1(A,B,"secureConnection",Q,"TLS/SSL","connectEnd"),MJ1(A,B,"fetch",Q,"cache","domainLookupStart"),MJ1(A,B,"domainLookup",Q,"DNS"),dY9(A,B,Q)}function MJ1(A,B,Q,D,Z,G){let F=G?B[G]:B[`${Q}End`],I=B[`${Q}Start`];if(!I||!F)return;FO._startChild(A,{op:"browser",origin:"auto.browser.browser.metrics",description:Z||Q,startTimestamp:D+HF(I),endTimestamp:D+HF(F)})}function dY9(A,B,Q){if(B.responseEnd)FO._startChild(A,{op:"browser",origin:"auto.browser.browser.metrics",description:"request",startTimestamp:Q+HF(B.requestStart),endTimestamp:Q+HF(B.responseEnd)}),FO._startChild(A,{op:"browser",origin:"auto.browser.browser.metrics",description:"response",startTimestamp:Q+HF(B.responseStart),endTimestamp:Q+HF(B.responseEnd)})}function Lf0(A,B,Q,D,Z,G){if(B.initiatorType==="xmlhttprequest"||B.initiatorType==="fetch")return;let F=L3.parseUrl(Q),I={};if(Ud1(I,B,"transferSize","http.response_transfer_size"),Ud1(I,B,"encodedBodySize","http.response_content_length"),Ud1(I,B,"decodedBodySize","http.decoded_response_content_length"),"renderBlockingStatus"in B)I["resource.render_blocking_status"]=B.renderBlockingStatus;if(F.protocol)I["url.scheme"]=F.protocol.split(":").pop();if(F.host)I["server.address"]=F.host;I["url.same_origin"]=Q.includes(GO.WINDOW.location.origin);let Y=G+D,W=Y+Z;FO._startChild(A,{description:Q.replace(GO.WINDOW.location.origin,""),endTimestamp:W,op:B.initiatorType?`resource.${B.initiatorType}`:"resource.other",origin:"auto.resource.browser.metrics",startTimestamp:Y,data:I})}function cY9(A){let B=GO.WINDOW.navigator;if(!B)return;let Q=B.connection;if(Q){if(Q.effectiveType)A.setTag("effectiveConnectionType",Q.effectiveType);if(Q.type)A.setTag("connectionType",Q.type);if(FO.isMeasurementValue(Q.rtt))rD["connection.rtt"]={value:Q.rtt,unit:"millisecond"}}if(FO.isMeasurementValue(B.deviceMemory))A.setTag("deviceMemory",`${B.deviceMemory} GB`);if(FO.isMeasurementValue(B.hardwareConcurrency))A.setTag("hardwareConcurrency",String(B.hardwareConcurrency))}function lY9(A){if(aq){if(_C.DEBUG_BUILD&&L3.logger.log("[Measurements] Adding LCP Data"),aq.element)A.setTag("lcp.element",L3.htmlTreeAsString(aq.element));if(aq.id)A.setTag("lcp.id",aq.id);if(aq.url)A.setTag("lcp.url",aq.url.trim().slice(0,200));A.setTag("lcp.size",aq.size)}if(y21&&y21.sources)_C.DEBUG_BUILD&&L3.logger.log("[Measurements] Adding CLS Data"),y21.sources.forEach((B,Q)=>A.setTag(`cls.source.${Q+1}`,L3.htmlTreeAsString(B.node)))}function Ud1(A,B,Q,D){let Z=B[Q];if(Z!=null&&Z<jY9)A[D]=Z}function pY9(A){let B=SY9.getNavigationEntry();if(!B)return;let{responseStart:Q,requestStart:D}=B;if(D<=Q)_C.DEBUG_BUILD&&L3.logger.log("[Measurements] Adding TTFB Request Time"),A["ttfb.requestTime"]={value:Q-D,unit:"millisecond"}}function iY9(A,B,Q){if(!ZO.hasTracingEnabled(B))return!1;let D;if(A!==void 0&&typeof B.tracesSampler==="function")D=B.tracesSampler({transactionContext:A,name:A.name,parentSampled:A.parentSampled,attributes:{...A.data,...A.attributes},location:GO.WINDOW.location});else if(A!==void 0&&A.sampled!==void 0)D=A.sampled;else if(typeof B.tracesSampleRate!=="undefined")D=B.tracesSampleRate;else D=1;if(!ZO.isValidSampleRate(D))return _C.DEBUG_BUILD&&L3.logger.warn("[Tracing] Discarding interaction span because of invalid sample rate."),!1;if(D===!0)return Q;else if(D===!1)return 0;return D*Q}Mf0._addMeasureSpans=Nf0;Mf0._addResourceSpans=Lf0;Mf0.addPerformanceEntries=uY9;Mf0.startTrackingINP=xY9;Mf0.startTrackingInteractions=_Y9;Mf0.startTrackingLongTasks=yY9;Mf0.startTrackingWebVitals=kY9});
var $h0=E((wh0)=>{var{_nullishCoalesce:dJ9,_optionalChain:cJ9}=$A();Object.defineProperty(wh0,"__esModule",{value:!0});var h21=J1("net"),Eh0=J1("tls"),lJ9=J1("url"),pJ9=$A(),iJ9=Kh0(),nJ9=zh0();function g21(...A){pJ9.logger.log("[https-proxy-agent]",...A)}class Pd1 extends iJ9.Agent{static __initStatic(){this.protocols=["http","https"]}constructor(A,B){super(B);this.options={},this.proxy=typeof A==="string"?new lJ9.URL(A):A,this.proxyHeaders=dJ9(cJ9([B,"optionalAccess",(Z)=>Z.headers]),()=>({})),g21("Creating new HttpsProxyAgent instance: %o",this.proxy.href);let Q=(this.proxy.hostname||this.proxy.host).replace(/^\[|\]$/g,""),D=this.proxy.port?parseInt(this.proxy.port,10):this.proxy.protocol==="https:"?443:80;this.connectOpts={ALPNProtocols:["http/1.1"],...B?Uh0(B,"headers"):null,host:Q,port:D}}async connect(A,B){let{proxy:Q}=this;if(!B.host)throw new TypeError('No "host" provided');let D;if(Q.protocol==="https:"){g21("Creating `tls.Socket`: %o",this.connectOpts);let X=this.connectOpts.servername||this.connectOpts.host;D=Eh0.connect({...this.connectOpts,servername:X&&h21.isIP(X)?void 0:X})}else g21("Creating `net.Socket`: %o",this.connectOpts),D=h21.connect(this.connectOpts);let Z=typeof this.proxyHeaders==="function"?this.proxyHeaders():{...this.proxyHeaders},G=h21.isIPv6(B.host)?`[${B.host}]`:B.host,F=`CONNECT ${G}:${B.port} HTTP/1.1\r
`;if(Q.username||Q.password){let X=`${decodeURIComponent(Q.username)}:${decodeURIComponent(Q.password)}`;Z["Proxy-Authorization"]=`Basic ${Buffer.from(X).toString("base64")}`}if(Z.Host=`${G}:${B.port}`,!Z["Proxy-Connection"])Z["Proxy-Connection"]=this.keepAlive?"Keep-Alive":"close";for(let X of Object.keys(Z))F+=`${X}: ${Z[X]}\r
`;let I=nJ9.parseProxyResponse(D);D.write(`${F}\r
`);let{connect:Y,buffered:W}=await I;if(A.emit("proxyConnect",Y),this.emit("proxyConnect",Y,A),Y.statusCode===200){if(A.once("socket",aJ9),B.secureEndpoint){g21("Upgrading socket connection to TLS");let X=B.servername||B.host;return Eh0.connect({...Uh0(B,"host","path","port"),socket:D,servername:h21.isIP(X)?void 0:X})}return D}D.destroy();let J=new h21.Socket({writable:!1});return J.readable=!0,A.once("socket",(X)=>{g21("Replaying proxy buffer for failed request"),X.push(W),X.push(null)}),J}}Pd1.__initStatic();function aJ9(A){A.resume()}function Uh0(A,...B){let Q={},D;for(D in A)if(!B.includes(D))Q[D]=A[D];return Q}wh0.HttpsProxyAgent=Pd1});
var $k0=E((wk0)=>{Object.defineProperty(wk0,"__esModule",{value:!0});var LeB=Cu1(),pu1=lu1();function MeB(A){let B=[];function Q(){return A===void 0||B.length<A}function D(F){return B.splice(B.indexOf(F),1)[0]}function Z(F){if(!Q())return pu1.rejectedSyncPromise(new LeB.SentryError("Not adding Promise because buffer limit was reached."));let I=F();if(B.indexOf(I)===-1)B.push(I);return I.then(()=>D(I)).then(null,()=>D(I).then(null,()=>{})),I}function G(F){return new pu1.SyncPromise((I,Y)=>{let W=B.length;if(!W)return I(!0);let J=setTimeout(()=>{if(F&&F>0)I(!1)},F);B.forEach((X)=>{pu1.resolvedSyncPromise(X).then(()=>{if(!--W)clearTimeout(J),I(!0)},Y)})})}return{$:B,add:Z,drain:G}}wk0.makePromiseBuffer=MeB});
var $y0=E((wy0)=>{Object.defineProperty(wy0,"__esModule",{value:!0});var b09=Qm1();async function f09(A,B){return b09._nullishCoalesce(A,B)}wy0._asyncNullishCoalesce=f09});
var AX1=E((lm0)=>{Object.defineProperty(lm0,"__esModule",{value:!0});var LE9=[["january","1"],["february","2"],["march","3"],["april","4"],["may","5"],["june","6"],["july","7"],["august","8"],["september","9"],["october","10"],["november","11"],["december","12"],["jan","1"],["feb","2"],["mar","3"],["apr","4"],["may","5"],["jun","6"],["jul","7"],["aug","8"],["sep","9"],["oct","10"],["nov","11"],["dec","12"],["sunday","0"],["monday","1"],["tuesday","2"],["wednesday","3"],["thursday","4"],["friday","5"],["saturday","6"],["sun","0"],["mon","1"],["tue","2"],["wed","3"],["thu","4"],["fri","5"],["sat","6"]];function ME9(A){return LE9.reduce((B,[Q,D])=>B.replace(new RegExp(Q,"gi"),D),A)}lm0.replaceCronNames=ME9});
var Ad0=E((em0)=>{Object.defineProperty(em0,"__esModule",{value:!0});var yE9=OQ(),_E9=AX1();function xE9(A){return new Proxy(A,{get(B,Q){if(Q==="scheduleJob")return new Proxy(B.scheduleJob,{apply(D,Z,G){let[F,I]=G;if(typeof F!=="string"||typeof I!=="string")throw new Error("Automatic instrumentation of 'node-schedule' requires the first parameter of 'scheduleJob' to be a job name string and the second parameter to be a crontab string");let Y=F,W=I;return yE9.withMonitor(Y,()=>{return D.apply(Z,G)},{schedule:{type:"crontab",value:_E9.replaceCronNames(W)}})}});return B[Q]}})}em0.instrumentNodeSchedule=xE9});
var Ad1=E((Wv0)=>{Object.defineProperty(Wv0,"__esModule",{value:!0});var sF=$A(),Ff=qG(),Fv0=BO(),P79=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/],S79=[/^.*\/healthcheck$/,/^.*\/healthy$/,/^.*\/live$/,/^.*\/ready$/,/^.*\/heartbeat$/,/^.*\/health$/,/^.*\/healthz$/],Iv0="InboundFilters",j79=(A={})=>{return{name:Iv0,setupOnce(){},processEvent(B,Q,D){let Z=D.getOptions(),G=y79(A,Z);return _79(B,G)?null:B}}},Yv0=Fv0.defineIntegration(j79),k79=Fv0.convertIntegrationFnToClass(Iv0,Yv0);function y79(A={},B={}){return{allowUrls:[...A.allowUrls||[],...B.allowUrls||[]],denyUrls:[...A.denyUrls||[],...B.denyUrls||[]],ignoreErrors:[...A.ignoreErrors||[],...B.ignoreErrors||[],...A.disableErrorDefaults?[]:P79],ignoreTransactions:[...A.ignoreTransactions||[],...B.ignoreTransactions||[],...A.disableTransactionDefaults?[]:S79],ignoreInternal:A.ignoreInternal!==void 0?A.ignoreInternal:!0}}function _79(A,B){if(B.ignoreInternal&&g79(A))return Ff.DEBUG_BUILD&&sF.logger.warn(`Event dropped due to being internal Sentry Error.
Event: ${sF.getEventDescription(A)}`),!0;if(x79(A,B.ignoreErrors))return Ff.DEBUG_BUILD&&sF.logger.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${sF.getEventDescription(A)}`),!0;if(v79(A,B.ignoreTransactions))return Ff.DEBUG_BUILD&&sF.logger.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${sF.getEventDescription(A)}`),!0;if(b79(A,B.denyUrls))return Ff.DEBUG_BUILD&&sF.logger.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${sF.getEventDescription(A)}.
Url: ${QJ1(A)}`),!0;if(!f79(A,B.allowUrls))return Ff.DEBUG_BUILD&&sF.logger.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${sF.getEventDescription(A)}.
Url: ${QJ1(A)}`),!0;return!1}function x79(A,B){if(A.type||!B||!B.length)return!1;return h79(A).some((Q)=>sF.stringMatchesSomePattern(Q,B))}function v79(A,B){if(A.type!=="transaction"||!B||!B.length)return!1;let Q=A.transaction;return Q?sF.stringMatchesSomePattern(Q,B):!1}function b79(A,B){if(!B||!B.length)return!1;let Q=QJ1(A);return!Q?!1:sF.stringMatchesSomePattern(Q,B)}function f79(A,B){if(!B||!B.length)return!0;let Q=QJ1(A);return!Q?!0:sF.stringMatchesSomePattern(Q,B)}function h79(A){let B=[];if(A.message)B.push(A.message);let Q;try{Q=A.exception.values[A.exception.values.length-1]}catch(D){}if(Q){if(Q.value){if(B.push(Q.value),Q.type)B.push(`${Q.type}: ${Q.value}`)}}if(Ff.DEBUG_BUILD&&B.length===0)sF.logger.error(`Could not extract message for event ${sF.getEventDescription(A)}`);return B}function g79(A){try{return A.exception.values[0].type==="SentryError"}catch(B){}return!1}function u79(A=[]){for(let B=A.length-1;B>=0;B--){let Q=A[B];if(Q&&Q.filename!=="<anonymous>"&&Q.filename!=="[native code]")return Q.filename||null}return null}function QJ1(A){try{let B;try{B=A.exception.values[0].stacktrace.frames}catch(Q){}return B?u79(B):null}catch(B){return Ff.DEBUG_BUILD&&sF.logger.error(`Cannot extract url for event ${sF.getEventDescription(A)}`),null}}Wv0.InboundFilters=k79;Wv0.inboundFiltersIntegration=Yv0});
var Aj0=E((eS0)=>{Object.defineProperty(eS0,"__esModule",{value:!0});var Gu1=vH(),NsB=oA1();function LsB(A,B,Q=250,D,Z,G,F){if(!G.exception||!G.exception.values||!F||!Gu1.isInstanceOf(F.originalException,Error))return;let I=G.exception.values.length>0?G.exception.values[G.exception.values.length-1]:void 0;if(I)G.exception.values=MsB(Fu1(A,B,Z,F.originalException,D,G.exception.values,I,0),Q)}function Fu1(A,B,Q,D,Z,G,F,I){if(G.length>=Q+1)return G;let Y=[...G];if(Gu1.isInstanceOf(D[Z],Error)){oS0(F,I);let W=A(B,D[Z]),J=Y.length;tS0(W,Z,J,I),Y=Fu1(A,B,Q,D[Z],Z,[W,...Y],W,J)}if(Array.isArray(D.errors))D.errors.forEach((W,J)=>{if(Gu1.isInstanceOf(W,Error)){oS0(F,I);let X=A(B,W),V=Y.length;tS0(X,`errors[${J}]`,V,I),Y=Fu1(A,B,Q,W,Z,[X,...Y],X,V)}});return Y}function oS0(A,B){A.mechanism=A.mechanism||{type:"generic",handled:!0},A.mechanism={...A.mechanism,...A.type==="AggregateError"&&{is_exception_group:!0},exception_id:B}}function tS0(A,B,Q,D){A.mechanism=A.mechanism||{type:"generic",handled:!0},A.mechanism={...A.mechanism,type:"chained",source:B,exception_id:Q,parent_id:D}}function MsB(A,B){return A.map((Q)=>{if(Q.value)Q.value=NsB.truncate(Q.value,B);return Q})}eS0.applyAggregateErrorsToEvent=LsB});
var Al=E((_y0)=>{Object.defineProperty(_y0,"__esModule",{value:!0});var R99="production";_y0.DEFAULT_ENVIRONMENT=R99});
var BO=E((s_0)=>{Object.defineProperty(s_0,"__esModule",{value:!0});var oW1=$A(),fm1=qG(),o89=J21(),t89=hH(),e89=lq(),hm1=[];function A59(A){let B={};return A.forEach((Q)=>{let{name:D}=Q,Z=B[D];if(Z&&!Z.isDefaultInstance&&Q.isDefaultInstance)return;B[D]=Q}),Object.keys(B).map((Q)=>B[Q])}function B59(A){let B=A.defaultIntegrations||[],Q=A.integrations;B.forEach((F)=>{F.isDefaultInstance=!0});let D;if(Array.isArray(Q))D=[...B,...Q];else if(typeof Q==="function")D=oW1.arrayify(Q(B));else D=B;let Z=A59(D),G=G59(Z,(F)=>F.name==="Debug");if(G!==-1){let[F]=Z.splice(G,1);Z.push(F)}return Z}function Q59(A,B){let Q={};return B.forEach((D)=>{if(D)a_0(A,D,Q)}),Q}function D59(A,B){for(let Q of B)if(Q&&Q.afterAllSetup)Q.afterAllSetup(A)}function a_0(A,B,Q){if(Q[B.name]){fm1.DEBUG_BUILD&&oW1.logger.log(`Integration skipped because it was already installed: ${B.name}`);return}if(Q[B.name]=B,hm1.indexOf(B.name)===-1)B.setupOnce(o89.addGlobalEventProcessor,e89.getCurrentHub),hm1.push(B.name);if(B.setup&&typeof B.setup==="function")B.setup(A);if(A.on&&typeof B.preprocessEvent==="function"){let D=B.preprocessEvent.bind(B);A.on("preprocessEvent",(Z,G)=>D(Z,G,A))}if(A.addEventProcessor&&typeof B.processEvent==="function"){let D=B.processEvent.bind(B),Z=Object.assign((G,F)=>D(G,F,A),{id:B.name});A.addEventProcessor(Z)}fm1.DEBUG_BUILD&&oW1.logger.log(`Integration installed: ${B.name}`)}function Z59(A){let B=t89.getClient();if(!B||!B.addIntegration){fm1.DEBUG_BUILD&&oW1.logger.warn(`Cannot add integration "${A.name}" because no SDK Client is available.`);return}B.addIntegration(A)}function G59(A,B){for(let Q=0;Q<A.length;Q++)if(B(A[Q])===!0)return Q;return-1}function F59(A,B){return Object.assign(function Q(...D){return B(...D)},{id:A})}function I59(A){return A}s_0.addIntegration=Z59;s_0.afterSetupIntegrations=D59;s_0.convertIntegrationFnToClass=F59;s_0.defineIntegration=I59;s_0.getIntegrationsToSetup=B59;s_0.installedIntegrations=hm1;s_0.setupIntegration=a_0;s_0.setupIntegrations=Q59});
var Bd1=E((Hv0)=>{Object.defineProperty(Hv0,"__esModule",{value:!0});var c79=$A(),l79=hH(),Vv0=BO(),Jv0,Cv0="FunctionToString",Xv0=new WeakMap,p79=()=>{return{name:Cv0,setupOnce(){Jv0=Function.prototype.toString;try{Function.prototype.toString=function(...A){let B=c79.getOriginalFunction(this),Q=Xv0.has(l79.getClient())&&B!==void 0?B:this;return Jv0.apply(Q,A)}}catch(A){}},setup(A){Xv0.set(A,!0)}}},Kv0=Vv0.defineIntegration(p79),i79=Vv0.convertIntegrationFnToClass(Cv0,Kv0);Hv0.FunctionToString=i79;Hv0.functionToStringIntegration=Kv0});
var Bk0=E((Ak0)=>{Object.defineProperty(Ak0,"__esModule",{value:!0});var wtB=mq(),$tB=lU(),aj0=Uu1(),sj0=Lu1(),rj0=Pu1(),oj0=ku1(),tj0=xu1(),ej0=bu1(),hu1=fu1();function qtB(A,B){switch(A){case"console":return aj0.addConsoleInstrumentationHandler(B);case"dom":return sj0.addClickKeypressInstrumentationHandler(B);case"xhr":return hu1.addXhrInstrumentationHandler(B);case"fetch":return rj0.addFetchInstrumentationHandler(B);case"history":return ej0.addHistoryInstrumentationHandler(B);case"error":return oj0.addGlobalErrorInstrumentationHandler(B);case"unhandledrejection":return tj0.addGlobalUnhandledRejectionInstrumentationHandler(B);default:wtB.DEBUG_BUILD&&$tB.logger.warn("unknown instrumentation type:",A)}}Ak0.addConsoleInstrumentationHandler=aj0.addConsoleInstrumentationHandler;Ak0.addClickKeypressInstrumentationHandler=sj0.addClickKeypressInstrumentationHandler;Ak0.addFetchInstrumentationHandler=rj0.addFetchInstrumentationHandler;Ak0.addGlobalErrorInstrumentationHandler=oj0.addGlobalErrorInstrumentationHandler;Ak0.addGlobalUnhandledRejectionInstrumentationHandler=tj0.addGlobalUnhandledRejectionInstrumentationHandler;Ak0.addHistoryInstrumentationHandler=ej0.addHistoryInstrumentationHandler;Ak0.SENTRY_XHR_DATA_KEY=hu1.SENTRY_XHR_DATA_KEY;Ak0.addXhrInstrumentationHandler=hu1.addXhrInstrumentationHandler;Ak0.addInstrumentationHandler=qtB});
var Bl=E((by0)=>{Object.defineProperty(by0,"__esModule",{value:!0});var X21=$A();function y99(A){let B=X21.timestampInSeconds(),Q={sid:X21.uuid4(),init:!0,timestamp:B,started:B,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>x99(Q)};if(A)Vm1(Q,A);return Q}function Vm1(A,B={}){if(B.user){if(!A.ipAddress&&B.user.ip_address)A.ipAddress=B.user.ip_address;if(!A.did&&!B.did)A.did=B.user.id||B.user.email||B.user.username}if(A.timestamp=B.timestamp||X21.timestampInSeconds(),B.abnormal_mechanism)A.abnormal_mechanism=B.abnormal_mechanism;if(B.ignoreDuration)A.ignoreDuration=B.ignoreDuration;if(B.sid)A.sid=B.sid.length===32?B.sid:X21.uuid4();if(B.init!==void 0)A.init=B.init;if(!A.did&&B.did)A.did=`${B.did}`;if(typeof B.started==="number")A.started=B.started;if(A.ignoreDuration)A.duration=void 0;else if(typeof B.duration==="number")A.duration=B.duration;else{let Q=A.timestamp-A.started;A.duration=Q>=0?Q:0}if(B.release)A.release=B.release;if(B.environment)A.environment=B.environment;if(!A.ipAddress&&B.ipAddress)A.ipAddress=B.ipAddress;if(!A.userAgent&&B.userAgent)A.userAgent=B.userAgent;if(typeof B.errors==="number")A.errors=B.errors;if(B.status)A.status=B.status}function _99(A,B){let Q={};if(B)Q={status:B};else if(A.status==="ok")Q={status:"exited"};Vm1(A,Q)}function x99(A){return X21.dropUndefinedKeys({sid:`${A.sid}`,init:A.init,started:new Date(A.started*1000).toISOString(),timestamp:new Date(A.timestamp*1000).toISOString(),status:A.status,errors:A.errors,did:typeof A.did==="number"||typeof A.did==="string"?`${A.did}`:void 0,duration:A.duration,abnormal_mechanism:A.abnormal_mechanism,attrs:{release:A.release,environment:A.environment,ip_address:A.ipAddress,user_agent:A.userAgent}})}by0.closeSession=_99;by0.makeSession=y99;by0.updateSession=Vm1});
var Cf=E((Mh0)=>{Object.defineProperty(Mh0,"__esModule",{value:!0});var YX9=$A(),WX9=YX9.parseSemver(process.versions.node);Mh0.NODE_VERSION=WX9});
var Cu1=E((Jj0)=>{Object.defineProperty(Jj0,"__esModule",{value:!0});class Wj0 extends Error{constructor(A,B="warn"){super(A);this.message=A,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=B}}Jj0.SentryError=Wj0});
var Cx0=E((Vx0)=>{Object.defineProperty(Vx0,"__esModule",{value:!0});var Jx0=$A(),L21=N21(),U39=im1(),w39=U21(),AJ1=q21();class Xx0{constructor(A){if(this._client=A,this._buckets=new Map,this._bucketsTotalWeight=0,this._interval=setInterval(()=>this._flush(),L21.DEFAULT_FLUSH_INTERVAL),this._interval.unref)this._interval.unref();this._flushShift=Math.floor(Math.random()*L21.DEFAULT_FLUSH_INTERVAL/1000),this._forceFlush=!1}add(A,B,Q,D="none",Z={},G=Jx0.timestampInSeconds()){let F=Math.floor(G),I=AJ1.sanitizeMetricKey(B),Y=AJ1.sanitizeTags(Z),W=AJ1.sanitizeUnit(D),J=AJ1.getBucketKey(A,I,W,Y),X=this._buckets.get(J),V=X&&A===L21.SET_METRIC_TYPE?X.metric.weight:0;if(X){if(X.metric.add(Q),X.timestamp<F)X.timestamp=F}else X={metric:new U39.METRIC_MAP[A](Q),timestamp:F,metricType:A,name:I,unit:W,tags:Y},this._buckets.set(J,X);let C=typeof Q==="string"?X.metric.weight-V:Q;if(w39.updateMetricSummaryOnActiveSpan(A,I,C,W,Z,J),this._bucketsTotalWeight+=X.metric.weight,this._bucketsTotalWeight>=L21.MAX_WEIGHT)this.flush()}flush(){this._forceFlush=!0,this._flush()}close(){this._forceFlush=!0,clearInterval(this._interval),this._flush()}_flush(){if(this._forceFlush){this._forceFlush=!1,this._bucketsTotalWeight=0,this._captureMetrics(this._buckets),this._buckets.clear();return}let A=Math.floor(Jx0.timestampInSeconds())-L21.DEFAULT_FLUSH_INTERVAL/1000-this._flushShift,B=new Map;for(let[Q,D]of this._buckets)if(D.timestamp<=A)B.set(Q,D),this._bucketsTotalWeight-=D.metric.weight;for(let[Q]of B)this._buckets.delete(Q);this._captureMetrics(B)}_captureMetrics(A){if(A.size>0&&this._client.captureAggregateMetrics){let B=Array.from(A).map(([,Q])=>Q);this._client.captureAggregateMetrics(B)}}}Vx0.MetricsAggregator=Xx0});
var D21=E((Jk0)=>{Object.defineProperty(Jk0,"__esModule",{value:!0});var du1=vH(),ntB=mu1(),atB=bH(),stB=FW1();function Yk0(A,B=100,Q=1/0){try{return $W1("",A,B,Q)}catch(D){return{ERROR:`**non-serializable** (${D})`}}}function Wk0(A,B=3,Q=102400){let D=Yk0(A,B);if(etB(D)>Q)return Wk0(A,B-1,Q);return D}function $W1(A,B,Q=1/0,D=1/0,Z=ntB.memoBuilder()){let[G,F]=Z;if(B==null||["number","boolean","string"].includes(typeof B)&&!du1.isNaN(B))return B;let I=rtB(A,B);if(!I.startsWith("[object "))return I;if(B.__sentry_skip_normalization__)return B;let Y=typeof B.__sentry_override_normalization_depth__==="number"?B.__sentry_override_normalization_depth__:Q;if(Y===0)return I.replace("object ","");if(G(B))return"[Circular ~]";let W=B;if(W&&typeof W.toJSON==="function")try{let C=W.toJSON();return $W1("",C,Y-1,D,Z)}catch(C){}let J=Array.isArray(B)?[]:{},X=0,V=atB.convertToPlainObject(B);for(let C in V){if(!Object.prototype.hasOwnProperty.call(V,C))continue;if(X>=D){J[C]="[MaxProperties ~]";break}let K=V[C];J[C]=$W1(C,K,Y-1,D,Z),X++}return F(B),J}function rtB(A,B){try{if(A==="domain"&&B&&typeof B==="object"&&B._events)return"[Domain]";if(A==="domainEmitter")return"[DomainEmitter]";if(typeof global!=="undefined"&&B===global)return"[Global]";if(typeof window!=="undefined"&&B===window)return"[Window]";if(typeof document!=="undefined"&&B===document)return"[Document]";if(du1.isVueViewModel(B))return"[VueViewModel]";if(du1.isSyntheticEvent(B))return"[SyntheticEvent]";if(typeof B==="number"&&B!==B)return"[NaN]";if(typeof B==="function")return`[Function: ${stB.getFunctionName(B)}]`;if(typeof B==="symbol")return`[${String(B)}]`;if(typeof B==="bigint")return`[BigInt: ${String(B)}]`;let Q=otB(B);if(/^HTML(\w*)Element$/.test(Q))return`[HTMLElement: ${Q}]`;return`[object ${Q}]`}catch(Q){return`**non-serializable** (${Q})`}}function otB(A){let B=Object.getPrototypeOf(A);return B?B.constructor.name:"null prototype"}function ttB(A){return~-encodeURI(A).split(/%..|./).length}function etB(A){return ttB(JSON.stringify(A))}function AeB(A,B){let Q=B.replace(/\\/g,"/").replace(/[|\\{}()[\]^$+*?.]/g,"\\$&"),D=A;try{D=decodeURI(A)}catch(Z){}return D.replace(/\\/g,"/").replace(/webpack:\/?/g,"").replace(new RegExp(`(file://)?/*${Q}/*`,"ig"),"app:///")}Jk0.normalize=Yk0;Jk0.normalizeToSize=Wk0;Jk0.normalizeUrlToBase=AeB;Jk0.walk=$W1});
var Df0=E((Qf0)=>{Object.defineProperty(Qf0,"__esModule",{value:!0});var kI9=Hl(),yI9=zl(),_I9=Wf(),xI9=El(),Af0=ob0(),Bf0=()=>{return Af0.getInteractionCount()},tb0=10,DO=[],Hd1={},eb0=(A)=>{let B=DO[DO.length-1],Q=Hd1[A.interactionId];if(Q||DO.length<tb0||A.duration>B.latency){if(Q)Q.entries.push(A),Q.latency=Math.max(Q.latency,A.duration);else{let D={id:A.interactionId,latency:A.duration,entries:[A]};Hd1[D.id]=D,DO.push(D)}DO.sort((D,Z)=>Z.latency-D.latency),DO.splice(tb0).forEach((D)=>{delete Hd1[D.id]})}},vI9=()=>{let A=Math.min(DO.length-1,Math.floor(Bf0()/50));return DO[A]},bI9=(A,B)=>{B=B||{},Af0.initInteractionCountPolyfill();let Q=yI9.initMetric("INP"),D,Z=(F)=>{F.forEach((Y)=>{if(Y.interactionId)eb0(Y);if(Y.entryType==="first-input"){if(!DO.some((J)=>{return J.entries.some((X)=>{return Y.duration===X.duration&&Y.startTime===X.startTime})}))eb0(Y)}});let I=vI9();if(I&&I.latency!==Q.value)Q.value=I.latency,Q.entries=I.entries,D()},G=_I9.observe("event",Z,{durationThreshold:B.durationThreshold||40});if(D=kI9.bindReporter(A,Q,B.reportAllChanges),G)G.observe({type:"first-input",buffered:!0}),xI9.onHidden(()=>{if(Z(G.takeRecords()),Q.value<0&&Bf0()>0)Q.value=0,Q.entries=[];D(!0)})};Qf0.onINP=bI9});
var Dm1=E((qy0)=>{Object.defineProperty(qy0,"__esModule",{value:!0});async function g09(A){let B=void 0,Q=A[0],D=1;while(D<A.length){let Z=A[D],G=A[D+1];if(D+=2,(Z==="optionalAccess"||Z==="optionalCall")&&Q==null)return;if(Z==="access"||Z==="optionalAccess")B=Q,Q=await G(Q);else if(Z==="call"||Z==="optionalCall")Q=await G((...F)=>Q.call(B,...F)),B=void 0}return Q}qy0._asyncOptionalChain=g09});
var Du0=E((Qu0)=>{Object.defineProperty(Qu0,"__esModule",{value:!0});var TK9=OQ(),PK9=rJ1();function SK9(A){let B=TK9.getClient();return new PK9.Anr(A).setup(B),Promise.resolve()}Qu0.enableAnrDetection=SK9});
var Eb0=E((zb0)=>{var{_optionalChain:bj}=$A();Object.defineProperty(zb0,"__esModule",{value:!0});var O21=$A(),Hb0=oX(),qF9=vj(),NF9=["aggregate","bulkWrite","countDocuments","createIndex","createIndexes","deleteMany","deleteOne","distinct","drop","dropIndex","dropIndexes","estimatedDocumentCount","find","findOne","findOneAndDelete","findOneAndReplace","findOneAndUpdate","indexes","indexExists","indexInformation","initializeOrderedBulkOp","insertMany","insertOne","isCapped","mapReduce","options","parallelCollectionScan","rename","replaceOne","stats","updateMany","updateOne"],LF9={bulkWrite:["operations"],countDocuments:["query"],createIndex:["fieldOrSpec"],createIndexes:["indexSpecs"],deleteMany:["filter"],deleteOne:["filter"],distinct:["key","query"],dropIndex:["indexName"],find:["query"],findOne:["query"],findOneAndDelete:["filter"],findOneAndReplace:["filter","replacement"],findOneAndUpdate:["filter","update"],indexExists:["indexes"],insertMany:["docs"],insertOne:["doc"],mapReduce:["map","reduce"],rename:["newName"],replaceOne:["filter","doc"],updateMany:["filter","update"],updateOne:["filter","update"]};function MF9(A){return A&&typeof A==="object"&&A.once&&typeof A.once==="function"}class CJ1{static __initStatic(){this.id="Mongo"}constructor(A={}){this.name=CJ1.id,this._operations=Array.isArray(A.operations)?A.operations:NF9,this._describeOperations="describeOperations"in A?A.describeOperations:!0,this._useMongoose=!!A.useMongoose}loadDependency(){let A=this._useMongoose?"mongoose":"mongodb";return this._module=this._module||O21.loadModule(A)}setupOnce(A,B){if(qF9.shouldDisableAutoInstrumentation(B)){Hb0.DEBUG_BUILD&&O21.logger.log("Mongo Integration is skipped because of instrumenter configuration.");return}let Q=this.loadDependency();if(!Q){let D=this._useMongoose?"mongoose":"mongodb";Hb0.DEBUG_BUILD&&O21.logger.error(`Mongo Integration was unable to require \`${D}\` package.`);return}this._instrumentOperations(Q.Collection,this._operations,B)}_instrumentOperations(A,B,Q){B.forEach((D)=>this._patchOperation(A,D,Q))}_patchOperation(A,B,Q){if(!(B in A.prototype))return;let D=this._getSpanContextFromOperationArguments.bind(this);O21.fill(A.prototype,B,function(Z){return function(...G){let F=G[G.length-1],I=Q(),Y=I.getScope(),W=I.getClient(),J=Y.getSpan(),X=bj([W,"optionalAccess",(C)=>C.getOptions,"call",(C)=>C(),"access",(C)=>C.sendDefaultPii]);if(typeof F!=="function"||B==="mapReduce"&&G.length===2){let C=bj([J,"optionalAccess",(H)=>H.startChild,"call",(H)=>H(D(this,B,G,X))]),K=Z.call(this,...G);if(O21.isThenable(K))return K.then((H)=>{return bj([C,"optionalAccess",(z)=>z.end,"call",(z)=>z()]),H});else if(MF9(K)){let H=K;try{H.once("close",()=>{bj([C,"optionalAccess",(z)=>z.end,"call",(z)=>z()])})}catch(z){bj([C,"optionalAccess",($)=>$.end,"call",($)=>$()])}return H}else return bj([C,"optionalAccess",(H)=>H.end,"call",(H)=>H()]),K}let V=bj([J,"optionalAccess",(C)=>C.startChild,"call",(C)=>C(D(this,B,G.slice(0,-1)))]);return Z.call(this,...G.slice(0,-1),function(C,K){bj([V,"optionalAccess",(H)=>H.end,"call",(H)=>H()]),F(C,K)})}})}_getSpanContextFromOperationArguments(A,B,Q,D=!1){let Z={"db.system":"mongodb","db.name":A.dbName,"db.operation":B,"db.mongodb.collection":A.collectionName},G={op:"db",origin:"auto.db.mongo",description:B,data:Z},F=LF9[B],I=Array.isArray(this._describeOperations)?this._describeOperations.includes(B):this._describeOperations;if(!F||!I||!D)return G;try{if(B==="mapReduce"){let[Y,W]=Q;Z[F[0]]=typeof Y==="string"?Y:Y.name||"<anonymous>",Z[F[1]]=typeof W==="string"?W:W.name||"<anonymous>"}else for(let Y=0;Y<F.length;Y++)Z[`db.mongodb.${F[Y]}`]=JSON.stringify(Q[Y])}catch(Y){}return G}}CJ1.__initStatic();zb0.Mongo=CJ1});
var Ek0=E((zk0)=>{Object.defineProperty(zk0,"__esModule",{value:!0});function Vk0(A,B){let Q=0;for(let D=A.length-1;D>=0;D--){let Z=A[D];if(Z===".")A.splice(D,1);else if(Z==="..")A.splice(D,1),Q++;else if(Q)A.splice(D,1),Q--}if(B)for(;Q--;Q)A.unshift("..");return A}var GeB=/^(\S+:\\|\/?)([\s\S]*?)((?:\.{1,2}|[^/\\]+?|)(\.[^./\\]*|))(?:[/\\]*)$/;function Ck0(A){let B=A.length>1024?`<truncated>${A.slice(-1024)}`:A,Q=GeB.exec(B);return Q?Q.slice(1):[]}function cu1(...A){let B="",Q=!1;for(let D=A.length-1;D>=-1&&!Q;D--){let Z=D>=0?A[D]:"/";if(!Z)continue;B=`${Z}/${B}`,Q=Z.charAt(0)==="/"}return B=Vk0(B.split("/").filter((D)=>!!D),!Q).join("/"),(Q?"/":"")+B||"."}function Xk0(A){let B=0;for(;B<A.length;B++)if(A[B]!=="")break;let Q=A.length-1;for(;Q>=0;Q--)if(A[Q]!=="")break;if(B>Q)return[];return A.slice(B,Q-B+1)}function FeB(A,B){A=cu1(A).slice(1),B=cu1(B).slice(1);let Q=Xk0(A.split("/")),D=Xk0(B.split("/")),Z=Math.min(Q.length,D.length),G=Z;for(let I=0;I<Z;I++)if(Q[I]!==D[I]){G=I;break}let F=[];for(let I=G;I<Q.length;I++)F.push("..");return F=F.concat(D.slice(G)),F.join("/")}function Kk0(A){let B=Hk0(A),Q=A.slice(-1)==="/",D=Vk0(A.split("/").filter((Z)=>!!Z),!B).join("/");if(!D&&!B)D=".";if(D&&Q)D+="/";return(B?"/":"")+D}function Hk0(A){return A.charAt(0)==="/"}function IeB(...A){return Kk0(A.join("/"))}function YeB(A){let B=Ck0(A),Q=B[0],D=B[1];if(!Q&&!D)return".";if(D)D=D.slice(0,D.length-1);return Q+D}function WeB(A,B){let Q=Ck0(A)[2];if(B&&Q.slice(B.length*-1)===B)Q=Q.slice(0,Q.length-B.length);return Q}zk0.basename=WeB;zk0.dirname=YeB;zk0.isAbsolute=Hk0;zk0.join=IeB;zk0.normalizePath=Kk0;zk0.relative=FeB;zk0.resolve=cu1});
var El=E((cb0)=>{Object.defineProperty(cb0,"__esModule",{value:!0});var db0=yC(),GI9=(A,B)=>{let Q=(D)=>{if(D.type==="pagehide"||db0.WINDOW.document.visibilityState==="hidden"){if(A(D),B)removeEventListener("visibilitychange",Q,!0),removeEventListener("pagehide",Q,!0)}};if(db0.WINDOW.document)addEventListener("visibilitychange",Q,!0),addEventListener("pagehide",Q,!0)};cb0.onHidden=GI9});
var Ex0=E((zx0)=>{Object.defineProperty(zx0,"__esModule",{value:!0});var QO=$A(),q39=gm1(),N39=mm1(),BJ1=qG(),L39=hH(),M39=Cx0(),R39=vm1(),O39=_m1(),T39=aX(),P39=Ql();Gl();var Kx0=Qf();class Hx0 extends q39.BaseClient{constructor(A){O39.addTracingExtensions();super(A);if(A._experiments&&A._experiments.metricsAggregator)this.metricsAggregator=new M39.MetricsAggregator(this)}eventFromException(A,B){return QO.resolvedSyncPromise(QO.eventFromUnknownInput(L39.getClient(),this._options.stackParser,A,B))}eventFromMessage(A,B="info",Q){return QO.resolvedSyncPromise(QO.eventFromMessage(this._options.stackParser,A,B,Q,this._options.attachStacktrace))}captureException(A,B,Q){if(this._options.autoSessionTracking&&this._sessionFlusher&&Q){let D=Q.getRequestSession();if(D&&D.status==="ok")D.status="errored"}return super.captureException(A,B,Q)}captureEvent(A,B,Q){if(this._options.autoSessionTracking&&this._sessionFlusher&&Q){if((A.type||"exception")==="exception"&&A.exception&&A.exception.values&&A.exception.values.length>0){let G=Q.getRequestSession();if(G&&G.status==="ok")G.status="errored"}}return super.captureEvent(A,B,Q)}close(A){if(this._sessionFlusher)this._sessionFlusher.close();return super.close(A)}initSessionFlusher(){let{release:A,environment:B}=this._options;if(!A)BJ1.DEBUG_BUILD&&QO.logger.warn("Cannot initialise an instance of SessionFlusher if no release is provided!");else this._sessionFlusher=new R39.SessionFlusher(this,{release:A,environment:B})}captureCheckIn(A,B,Q){let D="checkInId"in A&&A.checkInId?A.checkInId:QO.uuid4();if(!this._isEnabled())return BJ1.DEBUG_BUILD&&QO.logger.warn("SDK not enabled, will not capture checkin."),D;let Z=this.getOptions(),{release:G,environment:F,tunnel:I}=Z,Y={check_in_id:D,monitor_slug:A.monitorSlug,status:A.status,release:G,environment:F};if("duration"in A)Y.duration=A.duration;if(B)Y.monitor_config={schedule:B.schedule,checkin_margin:B.checkinMargin,max_runtime:B.maxRuntime,timezone:B.timezone};let[W,J]=this._getTraceInfoFromScope(Q);if(J)Y.contexts={trace:J};let X=N39.createCheckInEnvelope(Y,W,this.getSdkMetadata(),I,this.getDsn());return BJ1.DEBUG_BUILD&&QO.logger.info("Sending checkin:",A.monitorSlug,A.status),this._sendEnvelope(X),D}_captureRequestSession(){if(!this._sessionFlusher)BJ1.DEBUG_BUILD&&QO.logger.warn("Discarded request mode session because autoSessionTracking option was disabled");else this._sessionFlusher.incrementSessionStatusCount()}_prepareEvent(A,B,Q,D){if(this._options.platform)A.platform=A.platform||this._options.platform;if(this._options.runtime)A.contexts={...A.contexts,runtime:(A.contexts||{}).runtime||this._options.runtime};if(this._options.serverName)A.server_name=A.server_name||this._options.serverName;return super._prepareEvent(A,B,Q,D)}_getTraceInfoFromScope(A){if(!A)return[void 0,void 0];let B=A.getSpan();if(B)return[P39.getRootSpan(B)?Kx0.getDynamicSamplingContextFromSpan(B):void 0,T39.spanToTraceContext(B)];let{traceId:Q,spanId:D,parentSpanId:Z,dsc:G}=A.getPropagationContext(),F={trace_id:Q,span_id:D,parent_span_id:Z};if(G)return[G,F];return[Kx0.getDynamicSamplingContextFromClient(Q,this,A),F]}}zx0.ServerRuntimeClient=Hx0});
var Ey0=E((zy0)=>{Object.defineProperty(zy0,"__esModule",{value:!0});class Hy0{constructor(A){this._maxSize=A,this._cache=new Map}get size(){return this._cache.size}get(A){let B=this._cache.get(A);if(B===void 0)return;return this._cache.delete(A),this._cache.set(A,B),B}set(A,B){if(this._cache.size>=this._maxSize)this._cache.delete(this._cache.keys().next().value);this._cache.set(A,B)}remove(A){let B=this._cache.get(A);if(B)this._cache.delete(A);return B}clear(){this._cache.clear()}keys(){return Array.from(this._cache.keys())}values(){let A=[];return this._cache.forEach((B)=>A.push(B)),A}}zy0.LRUMap=Hy0});
var FW1=E((Oj0)=>{Object.defineProperty(Oj0,"__esModule",{value:!0});var Nj0=GW1(),Lj0=50,$j0=/\(error: (.*)\)/,qj0=/captureMessage|captureException/;function Mj0(...A){let B=A.sort((Q,D)=>Q[0]-D[0]).map((Q)=>Q[1]);return(Q,D=0)=>{let Z=[],G=Q.split(`
`);for(let F=D;F<G.length;F++){let I=G[F];if(I.length>1024)continue;let Y=$j0.test(I)?I.replace($j0,"$1"):I;if(Y.match(/\S*Error: /))continue;for(let W of B){let J=W(Y);if(J){Z.push(J);break}}if(Z.length>=Lj0)break}return Rj0(Z)}}function krB(A){if(Array.isArray(A))return Mj0(...A);return A}function Rj0(A){if(!A.length)return[];let B=Array.from(A);if(/sentryWrapped/.test(B[B.length-1].function||""))B.pop();if(B.reverse(),qj0.test(B[B.length-1].function||"")){if(B.pop(),qj0.test(B[B.length-1].function||""))B.pop()}return B.slice(0,Lj0).map((Q)=>({...Q,filename:Q.filename||B[B.length-1].filename,function:Q.function||"?"}))}var Hu1="<anonymous>";function yrB(A){try{if(!A||typeof A!=="function")return Hu1;return A.name||Hu1}catch(B){return Hu1}}function _rB(A){return[90,Nj0.node(A)]}Oj0.filenameIsInApp=Nj0.filenameIsInApp;Oj0.createStackParser=Mj0;Oj0.getFunctionName=yrB;Oj0.nodeStackLineParser=_rB;Oj0.stackParserFromStackParserOptions=krB;Oj0.stripSentryFramesAndReverse=Rj0});
var Ff0=E((Gf0)=>{Object.defineProperty(Gf0,"__esModule",{value:!0});var hI9=yC(),gI9=Hl(),uI9=UJ1(),mI9=qJ1(),dI9=zl(),cI9=Wf(),lI9=El(),Zf0={},pI9=(A)=>{let B=mI9.getVisibilityWatcher(),Q=dI9.initMetric("LCP"),D,Z=(F)=>{let I=F[F.length-1];if(I){let Y=Math.max(I.startTime-uI9.getActivationStart(),0);if(Y<B.firstHiddenTime)Q.value=Y,Q.entries=[I],D()}},G=cI9.observe("largest-contentful-paint",Z);if(G){D=gI9.bindReporter(A,Q);let F=()=>{if(!Zf0[Q.id])Z(G.takeRecords()),G.disconnect(),Zf0[Q.id]=!0,D(!0)};return["keydown","click"].forEach((I)=>{if(hI9.WINDOW.document)addEventListener(I,F,{once:!0,capture:!0})}),lI9.onHidden(F,!0),F}return};Gf0.onLCP=pI9});
var Fh0=E((Gh0)=>{Object.defineProperty(Gh0,"__esModule",{value:!0});var kJ9=Od1(),yJ9=$A();function _J9(){let A=kJ9.lazyLoadedNodePerformanceMonitoringIntegrations.map((B)=>{try{return B()}catch(Q){return}}).filter((B)=>!!B);if(A.length===0)yJ9.logger.warn("Performance monitoring integrations could not be automatically loaded.");return A.filter((B)=>!!B.loadDependency())}Gh0.autoDiscoverNodePerformanceMonitoringIntegrations=_J9});
var Fk0=E((Gk0)=>{Object.defineProperty(Gk0,"__esModule",{value:!0});var mtB=uu1(),Zk0=RW();function dtB(){return typeof window!=="undefined"&&(!mtB.isNodeEnv()||ctB())}function ctB(){return Zk0.GLOBAL_OBJ.process!==void 0&&Zk0.GLOBAL_OBJ.process.type==="renderer"}Gk0.isBrowser=dtB});
var Fy0=E((Gy0)=>{Object.defineProperty(Gy0,"__esModule",{value:!0});function Dy0(A,B,Q){let D=B.match(/([a-z_]+)\.(.*)/i);if(D===null)A[B]=Q;else{let Z=A[D[1]];Dy0(Z,D[2],Q)}}function C09(A,B,Q={}){return Array.isArray(B)?Zy0(A,B,Q):K09(A,B,Q)}function Zy0(A,B,Q){let D=B.find((Z)=>Z.name===A.name);if(D){for(let[Z,G]of Object.entries(Q))Dy0(D,Z,G);return B}return[...B,A]}function K09(A,B,Q){return(Z)=>{let G=B(Z);if(A.allowExclusionByUser){if(!G.find((I)=>I.name===A.name))return G}return Zy0(A,G,Q)}}Gy0.addOrUpdateIntegration=C09});
var GW1=E((wj0)=>{Object.defineProperty(wj0,"__esModule",{value:!0});function Uj0(A,B=!1){return!(B||A&&!A.startsWith("/")&&!A.match(/^[A-Z]:/)&&!A.startsWith(".")&&!A.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&A!==void 0&&!A.includes("node_modules/")}function PrB(A){let B=/^\s*[-]{4,}$/,Q=/at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;return(D)=>{let Z=D.match(Q);if(Z){let G,F,I,Y,W;if(Z[1]){I=Z[1];let V=I.lastIndexOf(".");if(I[V-1]===".")V--;if(V>0){G=I.slice(0,V),F=I.slice(V+1);let C=G.indexOf(".Module");if(C>0)I=I.slice(C+1),G=G.slice(0,C)}Y=void 0}if(F)Y=G,W=F;if(F==="<anonymous>")W=void 0,I=void 0;if(I===void 0)W=W||"<anonymous>",I=Y?`${Y}.${W}`:W;let J=Z[2]&&Z[2].startsWith("file://")?Z[2].slice(7):Z[2],X=Z[5]==="native";if(J&&J.match(/\/[A-Z]:/))J=J.slice(1);if(!J&&Z[5]&&!X)J=Z[5];return{filename:J,module:A?A(J):void 0,function:I,lineno:parseInt(Z[3],10)||void 0,colno:parseInt(Z[4],10)||void 0,in_app:Uj0(J,X)}}if(D.match(B))return{filename:D};return}}wj0.filenameIsInApp=Uj0;wj0.node=PrB});
var Gl=E((H_0)=>{Object.defineProperty(H_0,"__esModule",{value:!0});H_0.SpanStatus=void 0;(function(A){A.Ok="ok";let Q="deadline_exceeded";A.DeadlineExceeded=Q;let D="unauthenticated";A.Unauthenticated=D;let Z="permission_denied";A.PermissionDenied=Z;let G="not_found";A.NotFound=G;let F="resource_exhausted";A.ResourceExhausted=F;let I="invalid_argument";A.InvalidArgument=I;let Y="unimplemented";A.Unimplemented=Y;let W="unavailable";A.Unavailable=W;let J="internal_error";A.InternalError=J;let X="unknown_error";A.UnknownError=X;let V="cancelled";A.Cancelled=V;let C="already_exists";A.AlreadyExists=C;let K="failed_precondition";A.FailedPrecondition=K;let H="aborted";A.Aborted=H;let z="out_of_range";A.OutOfRange=z;let $="data_loss";A.DataLoss=$})(H_0.SpanStatus||(H_0.SpanStatus={}));function Om1(A){if(A<400&&A>=100)return"ok";if(A>=400&&A<500)switch(A){case 401:return"unauthenticated";case 403:return"permission_denied";case 404:return"not_found";case 409:return"already_exists";case 413:return"failed_precondition";case 429:return"resource_exhausted";default:return"invalid_argument"}if(A>=500&&A<600)switch(A){case 501:return"unimplemented";case 503:return"unavailable";case 504:return"deadline_exceeded";default:return"internal_error"}return"unknown_error"}var J69=Om1;function X69(A,B){A.setTag("http.status_code",String(B)),A.setData("http.response.status_code",B);let Q=Om1(B);if(Q!=="unknown_error")A.setStatus(Q)}H_0.getSpanStatusFromHttpCode=Om1;H_0.setHttpStatus=X69;H_0.spanStatusfromHttpCode=J69});
var Gv0=E((Zv0)=>{Object.defineProperty(Zv0,"__esModule",{value:!0});var Av0=$A(),Bv0=BO(),q79=aX(),em1={include:{cookies:!0,data:!0,headers:!0,ip:!1,query_string:!0,url:!0,user:{id:!0,username:!0,email:!0}},transactionNamingScheme:"methodPath"},Qv0="RequestData",N79=(A={})=>{let B=Av0.addRequestDataToEvent,Q={...em1,...A,include:{method:!0,...em1.include,...A.include,user:A.include&&typeof A.include.user==="boolean"?A.include.user:{...em1.include.user,...(A.include||{}).user}}};return{name:Qv0,setupOnce(){},processEvent(D,Z,G){let{transactionNamingScheme:F}=Q,{sdkProcessingMetadata:I={}}=D,Y=I.request;if(!Y)return D;let W=I.requestDataOptionsFromExpressHandler||I.requestDataOptionsFromGCPWrapper||M79(Q),J=B(D,Y,W);if(D.type==="transaction"||F==="handler")return J;let V=Y._sentryTransaction;if(V){let C=q79.spanToJSON(V).description||"",K=R79(G)==="sentry.javascript.nextjs"?C.startsWith("/api"):F!=="path",[H]=Av0.extractPathForTransaction(Y,{path:!0,method:K,customRoute:C});J.transaction=H}return J}}},Dv0=Bv0.defineIntegration(N79),L79=Bv0.convertIntegrationFnToClass(Qv0,Dv0);function M79(A){let{transactionNamingScheme:B,include:{ip:Q,user:D,...Z}}=A,G=[];for(let[I,Y]of Object.entries(Z))if(Y)G.push(I);let F;if(D===void 0)F=!0;else if(typeof D==="boolean")F=D;else{let I=[];for(let[Y,W]of Object.entries(D))if(W)I.push(Y);F=I}return{include:{ip:Q,user:F,request:G.length!==0?G:void 0,transaction:B}}}function R79(A){try{return A.getOptions()._metadata.sdk.name}catch(B){return}}Zv0.RequestData=L79;Zv0.requestDataIntegration=Dv0});
var Hl=E((xb0)=>{Object.defineProperty(xb0,"__esModule",{value:!0});var dF9=(A,B,Q)=>{let D,Z;return(G)=>{if(B.value>=0){if(G||Q){if(Z=B.value-(D||0),Z||D===void 0)D=B.value,B.delta=Z,A(B)}}}};xb0.bindReporter=dF9});
var Hm0=E((Km0)=>{Object.defineProperty(Km0,"__esModule",{value:!0});var Xm0=OQ(),Vm0="SessionTiming",Mz9=()=>{let A=Date.now();return{name:Vm0,setupOnce(){},processEvent(B){let Q=Date.now();return{...B,extra:{...B.extra,["session:start"]:A,["session:duration"]:Q-A,["session:end"]:Q}}}}},Cm0=Xm0.defineIntegration(Mz9),Rz9=Xm0.convertIntegrationFnToClass(Vm0,Cm0);Km0.SessionTiming=Rz9;Km0.sessionTimingIntegration=Cm0});
var J21=E((vy0)=>{Object.defineProperty(vy0,"__esModule",{value:!0});var TW1=$A(),T99=qG();function xy0(){return TW1.getGlobalSingleton("globalEventProcessors",()=>[])}function P99(A){xy0().push(A)}function Xm1(A,B,Q,D=0){return new TW1.SyncPromise((Z,G)=>{let F=A[D];if(B===null||typeof F!=="function")Z(B);else{let I=F({...B},Q);if(T99.DEBUG_BUILD&&F.id&&I===null&&TW1.logger.log(`Event processor "${F.id}" dropped event`),TW1.isThenable(I))I.then((Y)=>Xm1(A,Y,Q,D+1).then(Z)).then(null,G);else Xm1(A,I,Q,D+1).then(Z).then(null,G)}})}vy0.addGlobalEventProcessor=P99;vy0.getGlobalEventProcessors=xy0;vy0.notifyEventProcessors=Xm1});
var Jb0=E((Wb0)=>{var{_optionalChain:pU}=$A();Object.defineProperty(Wb0,"__esModule",{value:!0});var Fd1=OQ(),tX=$A(),WJ1=oX(),GF9=vj();class JJ1{static __initStatic(){this.id="Express"}constructor(A={}){this.name=JJ1.id,this._router=A.router||A.app,this._methods=(Array.isArray(A.methods)?A.methods:[]).concat("use")}setupOnce(A,B){if(!this._router){WJ1.DEBUG_BUILD&&tX.logger.error("ExpressIntegration is missing an Express instance");return}if(GF9.shouldDisableAutoInstrumentation(B)){WJ1.DEBUG_BUILD&&tX.logger.log("Express Integration is skipped because of instrumenter configuration.");return}YF9(this._router,this._methods),WF9(this._router)}}JJ1.__initStatic();function Fb0(A,B){let Q=A.length;switch(Q){case 2:return function(D,Z){let G=Z.__sentry_transaction;if(G){let F=G.startChild({description:A.name,op:`middleware.express.${B}`,origin:"auto.middleware.express"});Z.once("finish",()=>{F.end()})}return A.call(this,D,Z)};case 3:return function(D,Z,G){let F=Z.__sentry_transaction,I=pU([F,"optionalAccess",(Y)=>Y.startChild,"call",(Y)=>Y({description:A.name,op:`middleware.express.${B}`,origin:"auto.middleware.express"})]);A.call(this,D,Z,function(...Y){pU([I,"optionalAccess",(W)=>W.end,"call",(W)=>W()]),G.call(this,...Y)})};case 4:return function(D,Z,G,F){let I=G.__sentry_transaction,Y=pU([I,"optionalAccess",(W)=>W.startChild,"call",(W)=>W({description:A.name,op:`middleware.express.${B}`,origin:"auto.middleware.express"})]);A.call(this,D,Z,G,function(...W){pU([Y,"optionalAccess",(J)=>J.end,"call",(J)=>J()]),F.call(this,...W)})};default:throw new Error(`Express middleware takes 2-4 arguments. Got: ${Q}`)}}function FF9(A,B){return A.map((Q)=>{if(typeof Q==="function")return Fb0(Q,B);if(Array.isArray(Q))return Q.map((D)=>{if(typeof D==="function")return Fb0(D,B);return D});return Q})}function IF9(A,B){let Q=A[B];return A[B]=function(...D){return Q.call(this,...FF9(D,B))},A}function YF9(A,B=[]){B.forEach((Q)=>IF9(A,Q))}function WF9(A){let B="settings"in A;if(B&&A._router===void 0&&A.lazyrouter)A.lazyrouter();let Q=B?A._router:A;if(!Q){WJ1.DEBUG_BUILD&&tX.logger.debug("Cannot instrument router for URL Parameterization (did not find a valid router)."),WJ1.DEBUG_BUILD&&tX.logger.debug("Routing instrumentation is currently only supported in Express 4.");return}let D=Object.getPrototypeOf(Q),Z=D.process_params;D.process_params=function G(F,I,Y,W,J){if(!Y._reconstructedRoute)Y._reconstructedRoute="";let{layerRoutePath:X,isRegex:V,isArray:C,numExtraSegments:K}=JF9(F);if(X||V||C)Y._hasParameters=!0;let H;if(X)H=X;else H=Yb0(Y.originalUrl,Y._reconstructedRoute,F.path)||"";let z=H.split("/").filter((N)=>N.length>0&&(V||C||!N.includes("*"))).join("/");if(z&&z.length>0)Y._reconstructedRoute+=`/${z}${V?"/":""}`;let $=tX.getNumberOfUrlSegments(tX.stripUrlQueryAndFragment(Y.originalUrl||""))+K,L=tX.getNumberOfUrlSegments(Y._reconstructedRoute);if($===L){if(!Y._hasParameters){if(Y._reconstructedRoute!==Y.originalUrl)Y._reconstructedRoute=Y.originalUrl?tX.stripUrlQueryAndFragment(Y.originalUrl):Y.originalUrl}let N=W.__sentry_transaction,O=N&&Fd1.spanToJSON(N).data||{};if(N&&O[Fd1.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]!=="custom"){let R=Y._reconstructedRoute||"/",[T,j]=tX.extractPathForTransaction(Y,{path:!0,method:!0,customRoute:R});N.updateName(T),N.setAttribute(Fd1.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,j)}}return Z.call(this,F,I,Y,W,J)}}var Ib0=(A,B,Q)=>{if(!A||!B||!Q||Object.keys(Q).length===0||pU([Q,"access",(W)=>W[0],"optionalAccess",(W)=>W.offset])===void 0||pU([Q,"access",(W)=>W[0],"optionalAccess",(W)=>W.offset])===null)return;let D=Q.sort((W,J)=>W.offset-J.offset),G=new RegExp(B,`${B.flags}d`).exec(A);if(!G||!G.indices)return;let[,...F]=G.indices;if(F.length!==D.length)return;let I=A,Y=0;return F.forEach((W,J)=>{if(W){let[X,V]=W,C=I.substring(0,X-Y),K=`:${D[J].name}`,H=I.substring(V-Y);I=C+K+H,Y=Y+(V-X-K.length)}}),I};function JF9(A){let B=pU([A,"access",(F)=>F.route,"optionalAccess",(F)=>F.path]),Q=tX.isRegExp(B),D=Array.isArray(B);if(!B){let[F]=tX.GLOBAL_OBJ.process.versions.node.split(".").map(Number);if(F>=16)B=Ib0(A.path,A.regexp,A.keys)}if(!B)return{isRegex:Q,isArray:D,numExtraSegments:0};let Z=D?Math.max(XF9(B)-tX.getNumberOfUrlSegments(A.path||""),0):0;return{layerRoutePath:VF9(D,B),isRegex:Q,isArray:D,numExtraSegments:Z}}function XF9(A){return A.reduce((B,Q)=>{return B+tX.getNumberOfUrlSegments(Q.toString())},0)}function VF9(A,B){if(A)return B.map((Q)=>Q.toString()).join(",");return B&&B.toString()}function Yb0(A,B,Q){let D=tX.stripUrlQueryAndFragment(A||""),Z=pU([D,"optionalAccess",(Y)=>Y.split,"call",(Y)=>Y("/"),"access",(Y)=>Y.filter,"call",(Y)=>Y((W)=>!!W)]),G=0,F=pU([B,"optionalAccess",(Y)=>Y.split,"call",(Y)=>Y("/"),"access",(Y)=>Y.filter,"call",(Y)=>Y((W)=>!!W),"access",(Y)=>Y.length])||0;return pU([Q,"optionalAccess",(Y)=>Y.split,"call",(Y)=>Y("/"),"access",(Y)=>Y.filter,"call",(Y)=>Y((W)=>{if(pU([Z,"optionalAccess",(J)=>J[F+G]])===W)return G+=1,!0;return!1}),"access",(Y)=>Y.join,"call",(Y)=>Y("/")])}Wb0.Express=JJ1;Wb0.extractOriginalRoute=Ib0;Wb0.preventDuplicateSegments=Yb0});
var Jm0=E((Wm0)=>{Object.defineProperty(Wm0,"__esModule",{value:!0});var Fm0=OQ(),Gm0=$A(),Im0="RewriteFrames",$z9=(A={})=>{let B=A.root,Q=A.prefix||"app:///",D=A.iteratee||((F)=>{if(!F.filename)return F;let I=/^[a-zA-Z]:\\/.test(F.filename)||F.filename.includes("\\")&&!F.filename.includes("/"),Y=/^\//.test(F.filename);if(I||Y){let W=I?F.filename.replace(/^[a-zA-Z]:/,"").replace(/\\/g,"/"):F.filename,J=B?Gm0.relative(B,W):Gm0.basename(W);F.filename=`${Q}${J}`}return F});function Z(F){try{return{...F,exception:{...F.exception,values:F.exception.values.map((I)=>({...I,...I.stacktrace&&{stacktrace:G(I.stacktrace)}}))}}}catch(I){return F}}function G(F){return{...F,frames:F&&F.frames&&F.frames.map((I)=>D(I))}}return{name:Im0,setupOnce(){},processEvent(F){let I=F;if(F.exception&&Array.isArray(F.exception.values))I=Z(I);return I}}},Ym0=Fm0.defineIntegration($z9),qz9=Fm0.convertIntegrationFnToClass(Im0,Ym0);Wm0.RewriteFrames=qz9;Wm0.rewriteFramesIntegration=Ym0});
var Kb0=E((Cb0)=>{var{_optionalChain:UF9}=$A();Object.defineProperty(Cb0,"__esModule",{value:!0});var R21=$A(),Yd1=oX(),wF9=vj();class VJ1{static __initStatic(){this.id="Mysql"}constructor(){this.name=VJ1.id}loadDependency(){return this._module=this._module||R21.loadModule("mysql/lib/Connection.js")}setupOnce(A,B){if(wF9.shouldDisableAutoInstrumentation(B)){Yd1.DEBUG_BUILD&&R21.logger.log("Mysql Integration is skipped because of instrumenter configuration.");return}let Q=this.loadDependency();if(!Q){Yd1.DEBUG_BUILD&&R21.logger.error("Mysql Integration was unable to require `mysql` package.");return}let D=void 0;try{Q.prototype.connect=new Proxy(Q.prototype.connect,{apply(F,I,Y){if(!D)D=I.config;return F.apply(I,Y)}})}catch(F){Yd1.DEBUG_BUILD&&R21.logger.error("Mysql Integration was unable to instrument `mysql` config.")}function Z(){if(!D)return{};return{"server.address":D.host,"server.port":D.port,"db.user":D.user}}function G(F){if(!F)return;let I=Z();Object.keys(I).forEach((Y)=>{F.setAttribute(Y,I[Y])}),F.end()}R21.fill(Q,"createQuery",function(F){return function(I,Y,W){let X=B().getScope().getSpan(),V=UF9([X,"optionalAccess",(K)=>K.startChild,"call",(K)=>K({description:typeof I==="string"?I:I.sql,op:"db",origin:"auto.db.mysql",data:{"db.system":"mysql"}})]);if(typeof W==="function")return F.call(this,I,Y,function(K,H,z){G(V),W(K,H,z)});if(typeof Y==="function")return F.call(this,I,function(K,H,z){G(V),Y(K,H,z)});let C=F.call(this,I,Y);return C.on("end",()=>{G(V)}),C}})}}VJ1.__initStatic();Cb0.Mysql=VJ1});
var Kg0=E((Cg0)=>{var{_optionalChain:oD}=$A();Object.defineProperty(Cg0,"__esModule",{value:!0});var fd1=OQ(),bJ1=$A(),bV9=Cf(),fJ1=Wg0();function hd1(A){let B=[],Q=!1;function D(F){if(B=[],Q)return;Q=!0,A(F)}B.push(D);function Z(F){B.push(F)}function G(F){let I=B.pop()||D;try{I(F)}catch(Y){D(F)}}return{add:Z,next:G}}class Jg0{constructor(){let{Session:A}=J1("inspector");this._session=new A}configureAndConnect(A,B){this._session.connect(),this._session.on("Debugger.paused",(Q)=>{A(Q,()=>{this._session.post("Debugger.resume")})}),this._session.post("Debugger.enable"),this._session.post("Debugger.setPauseOnExceptions",{state:B?"all":"uncaught"})}setPauseOnExceptions(A){this._session.post("Debugger.setPauseOnExceptions",{state:A?"all":"uncaught"})}getLocalVariables(A,B){this._getProperties(A,(Q)=>{let{add:D,next:Z}=hd1(B);for(let G of Q)if(oD([G,"optionalAccess",(F)=>F.value,"optionalAccess",(F)=>F.objectId])&&oD([G,"optionalAccess",(F)=>F.value,"access",(F)=>F.className])==="Array"){let F=G.value.objectId;D((I)=>this._unrollArray(F,G.name,I,Z))}else if(oD([G,"optionalAccess",(F)=>F.value,"optionalAccess",(F)=>F.objectId])&&oD([G,"optionalAccess",(F)=>F.value,"optionalAccess",(F)=>F.className])==="Object"){let F=G.value.objectId;D((I)=>this._unrollObject(F,G.name,I,Z))}else if(oD([G,"optionalAccess",(F)=>F.value,"optionalAccess",(F)=>F.value])!=null||oD([G,"optionalAccess",(F)=>F.value,"optionalAccess",(F)=>F.description])!=null)D((F)=>this._unrollOther(G,F,Z));Z({})})}_getProperties(A,B){this._session.post("Runtime.getProperties",{objectId:A,ownProperties:!0},(Q,D)=>{if(Q)B([]);else B(D.result)})}_unrollArray(A,B,Q,D){this._getProperties(A,(Z)=>{Q[B]=Z.filter((G)=>G.name!=="length"&&!isNaN(parseInt(G.name,10))).sort((G,F)=>parseInt(G.name,10)-parseInt(F.name,10)).map((G)=>oD([G,"optionalAccess",(F)=>F.value,"optionalAccess",(F)=>F.value])),D(Q)})}_unrollObject(A,B,Q,D){this._getProperties(A,(Z)=>{Q[B]=Z.map((G)=>[G.name,oD([G,"optionalAccess",(F)=>F.value,"optionalAccess",(F)=>F.value])]).reduce((G,[F,I])=>{return G[F]=I,G},{}),D(Q)})}_unrollOther(A,B,Q){if(oD([A,"optionalAccess",(D)=>D.value,"optionalAccess",(D)=>D.value])!=null)B[A.name]=A.value.value;else if(oD([A,"optionalAccess",(D)=>D.value,"optionalAccess",(D)=>D.description])!=null&&oD([A,"optionalAccess",(D)=>D.value,"optionalAccess",(D)=>D.type])!=="function")B[A.name]=`<${A.value.description}>`;Q(B)}}function fV9(){try{return new Jg0}catch(A){return}}var Xg0="LocalVariables",hV9=(A={},B=fV9())=>{let Q=new bJ1.LRUMap(20),D,Z=!1;function G(Y,{params:{reason:W,data:J,callFrames:X}},V){if(W!=="exception"&&W!=="promiseRejection"){V();return}oD([D,"optionalCall",(z)=>z()]);let C=fJ1.hashFromStack(Y,oD([J,"optionalAccess",(z)=>z.description]));if(C==null){V();return}let{add:K,next:H}=hd1((z)=>{Q.set(C,z),V()});for(let z=0;z<Math.min(X.length,5);z++){let{scopeChain:$,functionName:L,this:N}=X[z],O=$.find((T)=>T.type==="local"),R=N.className==="global"||!N.className?L:`${N.className}.${L}`;if(oD([O,"optionalAccess",(T)=>T.object,"access",(T)=>T.objectId])===void 0)K((T)=>{T[z]={function:R},H(T)});else{let T=O.object.objectId;K((j)=>oD([B,"optionalAccess",(f)=>f.getLocalVariables,"call",(f)=>f(T,(k)=>{j[z]={function:R,vars:k},H(j)})]))}}H([])}function F(Y){let W=fJ1.hashFrames(oD([Y,"optionalAccess",(V)=>V.stacktrace,"optionalAccess",(V)=>V.frames]));if(W===void 0)return;let J=Q.remove(W);if(J===void 0)return;let X=(oD([Y,"access",(V)=>V.stacktrace,"optionalAccess",(V)=>V.frames])||[]).filter((V)=>V.function!=="new Promise");for(let V=0;V<X.length;V++){let C=X.length-V-1;if(!X[C]||!J[V])break;if(J[V].vars===void 0||X[C].in_app===!1||!fJ1.functionNamesMatch(X[C].function,J[V].function))continue;X[C].vars=J[V].vars}}function I(Y){for(let W of oD([Y,"optionalAccess",(J)=>J.exception,"optionalAccess",(J)=>J.values])||[])F(W);return Y}return{name:Xg0,setupOnce(){let Y=fd1.getClient(),W=oD([Y,"optionalAccess",(J)=>J.getOptions,"call",(J)=>J()]);if(B&&oD([W,"optionalAccess",(J)=>J.includeLocalVariables])){if(bV9.NODE_VERSION.major<18){bJ1.logger.log("The `LocalVariables` integration is only supported on Node >= v18.");return}let X=A.captureAllExceptions!==!1;if(B.configureAndConnect((V,C)=>G(W.stackParser,V,C),X),X){let V=A.maxExceptionsPerSecond||50;D=fJ1.createRateLimiter(V,()=>{bJ1.logger.log("Local variables rate-limit lifted."),oD([B,"optionalAccess",(C)=>C.setPauseOnExceptions,"call",(C)=>C(!0)])},(C)=>{bJ1.logger.log(`Local variables rate-limit exceeded. Disabling capturing of caught exceptions for ${C} seconds.`),oD([B,"optionalAccess",(K)=>K.setPauseOnExceptions,"call",(K)=>K(!1)])})}Z=!0}},processEvent(Y){if(Z)return I(Y);return Y},_getCachedFramesCount(){return Q.size},_getFirstCachedFrame(){return Q.values()[0]}}},Vg0=fd1.defineIntegration(hV9),gV9=fd1.convertIntegrationFnToClass(Xg0,Vg0);Cg0.LocalVariablesSync=gV9;Cg0.createCallbackList=hd1;Cg0.localVariablesSyncIntegration=Vg0});
var Kh0=E((Ch0)=>{var{_nullishCoalesce:Jh0}=$A();Object.defineProperty(Ch0,"__esModule",{value:!0});var Xh0=J1("http");J1("https");var oq=Symbol("AgentBaseInternalState");class Vh0 extends Xh0.Agent{constructor(A){super(A);this[oq]={}}isSecureEndpoint(A){if(A){if(typeof A.secureEndpoint==="boolean")return A.secureEndpoint;if(typeof A.protocol==="string")return A.protocol==="https:"}let{stack:B}=new Error;if(typeof B!=="string")return!1;return B.split(`
`).some((Q)=>Q.indexOf("(https.js:")!==-1||Q.indexOf("node:https:")!==-1)}createSocket(A,B,Q){let D={...B,secureEndpoint:this.isSecureEndpoint(B)};Promise.resolve().then(()=>this.connect(A,D)).then((Z)=>{if(Z instanceof Xh0.Agent)return Z.addRequest(A,D);this[oq].currentSocket=Z,super.createSocket(A,B,Q)},Q)}createConnection(){let A=this[oq].currentSocket;if(this[oq].currentSocket=void 0,!A)throw new Error("No socket was returned in the `connect()` function");return A}get defaultPort(){return Jh0(this[oq].defaultPort,()=>this.protocol==="https:"?443:80)}set defaultPort(A){if(this[oq])this[oq].defaultPort=A}get protocol(){return Jh0(this[oq].protocol,()=>this.isSecureEndpoint()?"https:":"http:")}set protocol(A){if(this[oq])this[oq].protocol=A}}Ch0.Agent=Vh0});
var Ky0=E((Cy0)=>{Object.defineProperty(Cy0,"__esModule",{value:!0});var T09=bH(),P09=GW1();function S09(A,B,Q,D){let Z=A(),G=!1,F=!0;return setInterval(()=>{let I=Z.getTimeMs();if(G===!1&&I>B+Q){if(G=!0,F)D()}if(I<B+Q)G=!1},20),{poll:()=>{Z.reset()},enabled:(I)=>{F=I}}}function j09(A,B,Q){let D=B?B.replace(/^file:\/\//,""):void 0,Z=A.location.columnNumber?A.location.columnNumber+1:void 0,G=A.location.lineNumber?A.location.lineNumber+1:void 0;return T09.dropUndefinedKeys({filename:D,module:Q(D),function:A.functionName||"?",colno:Z,lineno:G,in_app:D?P09.filenameIsInApp(D):void 0})}Cy0.callFrameToStackFrame=j09;Cy0.watchdogTimer=S09});
var Lb0=E((Nb0)=>{var{_optionalChain:Kl}=$A();Object.defineProperty(Nb0,"__esModule",{value:!0});var T21=$A(),qb0=oX(),jF9=vj();class HJ1{static __initStatic(){this.id="GraphQL"}constructor(){this.name=HJ1.id}loadDependency(){return this._module=this._module||T21.loadModule("graphql/execution/execute.js")}setupOnce(A,B){if(jF9.shouldDisableAutoInstrumentation(B)){qb0.DEBUG_BUILD&&T21.logger.log("GraphQL Integration is skipped because of instrumenter configuration.");return}let Q=this.loadDependency();if(!Q){qb0.DEBUG_BUILD&&T21.logger.error("GraphQL Integration was unable to require graphql/execution package.");return}T21.fill(Q,"execute",function(D){return function(...Z){let G=B().getScope(),F=G.getSpan(),I=Kl([F,"optionalAccess",(W)=>W.startChild,"call",(W)=>W({description:"execute",op:"graphql.execute",origin:"auto.graphql.graphql"})]);Kl([G,"optionalAccess",(W)=>W.setSpan,"call",(W)=>W(I)]);let Y=D.call(this,...Z);if(T21.isThenable(Y))return Y.then((W)=>{return Kl([I,"optionalAccess",(J)=>J.end,"call",(J)=>J()]),Kl([G,"optionalAccess",(J)=>J.setSpan,"call",(J)=>J(F)]),W});return Kl([I,"optionalAccess",(W)=>W.end,"call",(W)=>W()]),Kl([G,"optionalAccess",(W)=>W.setSpan,"call",(W)=>W(F)]),Y}})}}HJ1.__initStatic();Nb0.GraphQL=HJ1});
var Lu0=E((Nu0)=>{Object.defineProperty(Nu0,"__esModule",{value:!0});var wf=Od1();Nu0.Apollo=wf.Apollo;Nu0.Express=wf.Express;Nu0.GraphQL=wf.GraphQL;Nu0.Mongo=wf.Mongo;Nu0.Mysql=wf.Mysql;Nu0.Postgres=wf.Postgres;Nu0.Prisma=wf.Prisma});
var Lu1=E((vj0)=>{Object.defineProperty(vj0,"__esModule",{value:!0});var $oB=eA1(),YW1=bH(),qoB=RW(),$u1=rR(),tc=qoB.GLOBAL_OBJ,NoB=1000,yj0,qu1,Nu1;function LoB(A){$u1.addHandler("dom",A),$u1.maybeInstrument("dom",xj0)}function xj0(){if(!tc.document)return;let A=$u1.triggerHandlers.bind(null,"dom"),B=_j0(A,!0);tc.document.addEventListener("click",B,!1),tc.document.addEventListener("keypress",B,!1),["EventTarget","Node"].forEach((Q)=>{let D=tc[Q]&&tc[Q].prototype;if(!D||!D.hasOwnProperty||!D.hasOwnProperty("addEventListener"))return;YW1.fill(D,"addEventListener",function(Z){return function(G,F,I){if(G==="click"||G=="keypress")try{let Y=this,W=Y.__sentry_instrumentation_handlers__=Y.__sentry_instrumentation_handlers__||{},J=W[G]=W[G]||{refCount:0};if(!J.handler){let X=_j0(A);J.handler=X,Z.call(this,G,X,I)}J.refCount++}catch(Y){}return Z.call(this,G,F,I)}}),YW1.fill(D,"removeEventListener",function(Z){return function(G,F,I){if(G==="click"||G=="keypress")try{let Y=this,W=Y.__sentry_instrumentation_handlers__||{},J=W[G];if(J){if(J.refCount--,J.refCount<=0)Z.call(this,G,J.handler,I),J.handler=void 0,delete W[G];if(Object.keys(W).length===0)delete Y.__sentry_instrumentation_handlers__}}catch(Y){}return Z.call(this,G,F,I)}})})}function MoB(A){if(A.type!==qu1)return!1;try{if(!A.target||A.target._sentryId!==Nu1)return!1}catch(B){}return!0}function RoB(A,B){if(A!=="keypress")return!1;if(!B||!B.tagName)return!0;if(B.tagName==="INPUT"||B.tagName==="TEXTAREA"||B.isContentEditable)return!1;return!0}function _j0(A,B=!1){return(Q)=>{if(!Q||Q._sentryCaptured)return;let D=OoB(Q);if(RoB(Q.type,D))return;if(YW1.addNonEnumerableProperty(Q,"_sentryCaptured",!0),D&&!D._sentryId)YW1.addNonEnumerableProperty(D,"_sentryId",$oB.uuid4());let Z=Q.type==="keypress"?"input":Q.type;if(!MoB(Q))A({event:Q,name:Z,global:B}),qu1=Q.type,Nu1=D?D._sentryId:void 0;clearTimeout(yj0),yj0=tc.setTimeout(()=>{Nu1=void 0,qu1=void 0},NoB)}}function OoB(A){try{return A.target}catch(B){return null}}vj0.addClickKeypressInstrumentationHandler=LoB;vj0.instrumentDOM=xj0});
var Ly0=E((Ny0)=>{Object.defineProperty(Ny0,"__esModule",{value:!0});var m09=Dm1();async function d09(A){let B=await m09._asyncOptionalChain(A);return B==null?!0:B}Ny0._asyncOptionalChainDelete=d09});
var N21=E((Yx0)=>{Object.defineProperty(Yx0,"__esModule",{value:!0});var o59="c",t59="g",e59="s",A39="d",B39=5000,Q39=1e4,D39=1e4;Yx0.COUNTER_METRIC_TYPE=o59;Yx0.DEFAULT_BROWSER_FLUSH_INTERVAL=B39;Yx0.DEFAULT_FLUSH_INTERVAL=Q39;Yx0.DISTRIBUTION_METRIC_TYPE=A39;Yx0.GAUGE_METRIC_TYPE=t59;Yx0.MAX_WEIGHT=D39;Yx0.SET_METRIC_TYPE=e59});
var Nk0=E((qk0)=>{Object.defineProperty(qk0,"__esModule",{value:!0});function OeB(A){let B={},Q=0;while(Q<A.length){let D=A.indexOf("=",Q);if(D===-1)break;let Z=A.indexOf(";",Q);if(Z===-1)Z=A.length;else if(Z<D){Q=A.lastIndexOf(";",D-1)+1;continue}let G=A.slice(Q,D).trim();if(B[G]===void 0){let F=A.slice(D+1,Z).trim();if(F.charCodeAt(0)===34)F=F.slice(1,-1);try{B[G]=F.indexOf("%")!==-1?decodeURIComponent(F):F}catch(I){B[G]=F}}Q=Z+1}return B}qk0.parseCookie=OeB});
var Nv0=E((qv0)=>{Object.defineProperty(qv0,"__esModule",{value:!0});var BD9=Bd1(),QD9=Ad1(),DD9=Qd1();qv0.FunctionToString=BD9.FunctionToString;qv0.InboundFilters=QD9.InboundFilters;qv0.LinkedErrors=DD9.LinkedErrors});
var OJ1=E((yf0)=>{Object.defineProperty(yf0,"__esModule",{value:!0});var iU=OQ(),nU=$A(),GW9=qd1(),FW9=wl(),IW9=yC(),RJ1=["localhost",/^\/(?!\/)/],Nd1={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,tracingOrigins:RJ1,tracePropagationTargets:RJ1};function YW9(A){let{traceFetch:B,traceXHR:Q,tracePropagationTargets:D,tracingOrigins:Z,shouldCreateSpanForRequest:G,enableHTTPTimings:F}={traceFetch:Nd1.traceFetch,traceXHR:Nd1.traceXHR,...A},I=typeof G==="function"?G:(J)=>!0,Y=(J)=>Sf0(J,D||Z),W={};if(B)nU.addFetchInstrumentationHandler((J)=>{let X=GW9.instrumentFetchRequest(J,I,Y,W);if(X){let V=kf0(J.fetchData.url),C=V?nU.parseUrl(V).host:void 0;X.setAttributes({"http.url":V,"server.address":C})}if(F&&X)Tf0(X)});if(Q)nU.addXhrInstrumentationHandler((J)=>{let X=jf0(J,I,Y,W);if(F&&X)Tf0(X)})}function WW9(A){return A.entryType==="resource"&&"initiatorType"in A&&typeof A.nextHopProtocol==="string"&&(A.initiatorType==="fetch"||A.initiatorType==="xmlhttprequest")}function Tf0(A){let{url:B}=iU.spanToJSON(A).data||{};if(!B||typeof B!=="string")return;let Q=FW9.addPerformanceInstrumentationHandler("resource",({entries:D})=>{D.forEach((Z)=>{if(WW9(Z)&&Z.name.endsWith(B))JW9(Z).forEach((F)=>A.setAttribute(...F)),setTimeout(Q)})})}function Pf0(A){let B="unknown",Q="unknown",D="";for(let Z of A){if(Z==="/"){[B,Q]=A.split("/");break}if(!isNaN(Number(Z))){B=D==="h"?"http":D,Q=A.split(D)[1];break}D+=Z}if(D===A)B=D;return{name:B,version:Q}}function rq(A=0){return((nU.browserPerformanceTimeOrigin||performance.timeOrigin)+A)/1000}function JW9(A){let{name:B,version:Q}=Pf0(A.nextHopProtocol),D=[];if(D.push(["network.protocol.version",Q],["network.protocol.name",B]),!nU.browserPerformanceTimeOrigin)return D;return[...D,["http.request.redirect_start",rq(A.redirectStart)],["http.request.fetch_start",rq(A.fetchStart)],["http.request.domain_lookup_start",rq(A.domainLookupStart)],["http.request.domain_lookup_end",rq(A.domainLookupEnd)],["http.request.connect_start",rq(A.connectStart)],["http.request.secure_connection_start",rq(A.secureConnectionStart)],["http.request.connection_end",rq(A.connectEnd)],["http.request.request_start",rq(A.requestStart)],["http.request.response_start",rq(A.responseStart)],["http.request.response_end",rq(A.responseEnd)]]}function Sf0(A,B){return nU.stringMatchesSomePattern(A,B||RJ1)}function jf0(A,B,Q,D){let Z=A.xhr,G=Z&&Z[nU.SENTRY_XHR_DATA_KEY];if(!iU.hasTracingEnabled()||!Z||Z.__sentry_own_request__||!G)return;let F=B(G.url);if(A.endTimestamp&&F){let C=Z.__sentry_xhr_span_id__;if(!C)return;let K=D[C];if(K&&G.status_code!==void 0)iU.setHttpStatus(K,G.status_code),K.end(),delete D[C];return}let I=iU.getCurrentScope(),Y=iU.getIsolationScope(),W=kf0(G.url),J=W?nU.parseUrl(W).host:void 0,X=F?iU.startInactiveSpan({name:`${G.method} ${G.url}`,onlyIfParent:!0,attributes:{type:"xhr","http.method":G.method,"http.url":W,url:G.url,"server.address":J,[iU.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]:"auto.http.browser"},op:"http.client"}):void 0;if(X)Z.__sentry_xhr_span_id__=X.spanContext().spanId,D[Z.__sentry_xhr_span_id__]=X;let V=iU.getClient();if(Z.setRequestHeader&&Q(G.url)&&V){let{traceId:C,spanId:K,sampled:H,dsc:z}={...Y.getPropagationContext(),...I.getPropagationContext()},$=X?iU.spanToTraceHeader(X):nU.generateSentryTraceHeader(C,K,H),L=nU.dynamicSamplingContextToSentryBaggageHeader(z||(X?iU.getDynamicSamplingContextFromSpan(X):iU.getDynamicSamplingContextFromClient(C,V,I)));XW9(Z,$,L)}return X}function XW9(A,B,Q){try{if(A.setRequestHeader("sentry-trace",B),Q)A.setRequestHeader(nU.BAGGAGE_HEADER_NAME,Q)}catch(D){}}function kf0(A){try{return new URL(A,IW9.WINDOW.location.origin).href}catch(B){return}}yf0.DEFAULT_TRACE_PROPAGATION_TARGETS=RJ1;yf0.defaultRequestInstrumentationOptions=Nd1;yf0.extractNetworkProtocol=Pf0;yf0.instrumentOutgoingRequests=YW9;yf0.shouldAttachHeaders=Sf0;yf0.xhrCallback=jf0});
var OQ=E((Gd1)=>{Object.defineProperty(Gd1,"__esModule",{value:!0});var dv0=_m1(),cv0=km1(),LD9=cW1(),MD9=iW1(),lv0=bW1(),FJ1=Gl(),If=dW1(),pv0=Qf(),RD9=d_0(),OD9=ym1(),M21=w21(),iv0=xm1(),s3=hH(),nq=lq(),Dd1=Bl(),TD9=vm1(),Zd1=jW1(),nv0=J21(),av0=rW1(),sv0=gm1(),PD9=Ex0(),rv0=qx0(),SD9=Ox0(),jD9=Sx0(),kD9=kx0(),yD9=_W1(),IJ1=BO(),ov0=SW1(),_D9=PW1(),xD9=mm1(),vD9=xx0(),bD9=hW1(),fD9=fx0(),hD9=Tm1(),gD9=gx0(),YJ1=aX(),uD9=Ql(),mD9=dx0(),dD9=Al(),tv0=ex0(),ev0=Gv0(),Ab0=Ad1(),Bb0=Bd1(),Qb0=Qd1(),cD9=Nv0(),lD9=mv0(),pD9=cD9;Gd1.addTracingExtensions=dv0.addTracingExtensions;Gd1.startIdleTransaction=dv0.startIdleTransaction;Gd1.IdleTransaction=cv0.IdleTransaction;Gd1.TRACING_DEFAULTS=cv0.TRACING_DEFAULTS;Gd1.Span=LD9.Span;Gd1.Transaction=MD9.Transaction;Gd1.extractTraceparentData=lv0.extractTraceparentData;Gd1.getActiveTransaction=lv0.getActiveTransaction;Object.defineProperty(Gd1,"SpanStatus",{enumerable:!0,get:()=>FJ1.SpanStatus});Gd1.getSpanStatusFromHttpCode=FJ1.getSpanStatusFromHttpCode;Gd1.setHttpStatus=FJ1.setHttpStatus;Gd1.spanStatusfromHttpCode=FJ1.spanStatusfromHttpCode;Gd1.continueTrace=If.continueTrace;Gd1.getActiveSpan=If.getActiveSpan;Gd1.startActiveSpan=If.startActiveSpan;Gd1.startInactiveSpan=If.startInactiveSpan;Gd1.startSpan=If.startSpan;Gd1.startSpanManual=If.startSpanManual;Gd1.trace=If.trace;Gd1.getDynamicSamplingContextFromClient=pv0.getDynamicSamplingContextFromClient;Gd1.getDynamicSamplingContextFromSpan=pv0.getDynamicSamplingContextFromSpan;Gd1.setMeasurement=RD9.setMeasurement;Gd1.isValidSampleRate=OD9.isValidSampleRate;Gd1.SEMANTIC_ATTRIBUTE_PROFILE_ID=M21.SEMANTIC_ATTRIBUTE_PROFILE_ID;Gd1.SEMANTIC_ATTRIBUTE_SENTRY_OP=M21.SEMANTIC_ATTRIBUTE_SENTRY_OP;Gd1.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN=M21.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN;Gd1.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE=M21.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE;Gd1.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE=M21.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE;Gd1.createEventEnvelope=iv0.createEventEnvelope;Gd1.createSessionEnvelope=iv0.createSessionEnvelope;Gd1.addBreadcrumb=s3.addBreadcrumb;Gd1.captureCheckIn=s3.captureCheckIn;Gd1.captureEvent=s3.captureEvent;Gd1.captureException=s3.captureException;Gd1.captureMessage=s3.captureMessage;Gd1.captureSession=s3.captureSession;Gd1.close=s3.close;Gd1.configureScope=s3.configureScope;Gd1.endSession=s3.endSession;Gd1.flush=s3.flush;Gd1.getClient=s3.getClient;Gd1.getCurrentScope=s3.getCurrentScope;Gd1.isInitialized=s3.isInitialized;Gd1.lastEventId=s3.lastEventId;Gd1.setContext=s3.setContext;Gd1.setExtra=s3.setExtra;Gd1.setExtras=s3.setExtras;Gd1.setTag=s3.setTag;Gd1.setTags=s3.setTags;Gd1.setUser=s3.setUser;Gd1.startSession=s3.startSession;Gd1.startTransaction=s3.startTransaction;Gd1.withActiveSpan=s3.withActiveSpan;Gd1.withIsolationScope=s3.withIsolationScope;Gd1.withMonitor=s3.withMonitor;Gd1.withScope=s3.withScope;Gd1.Hub=nq.Hub;Gd1.ensureHubOnCarrier=nq.ensureHubOnCarrier;Gd1.getCurrentHub=nq.getCurrentHub;Gd1.getHubFromCarrier=nq.getHubFromCarrier;Gd1.getIsolationScope=nq.getIsolationScope;Gd1.getMainCarrier=nq.getMainCarrier;Gd1.makeMain=nq.makeMain;Gd1.runWithAsyncContext=nq.runWithAsyncContext;Gd1.setAsyncContextStrategy=nq.setAsyncContextStrategy;Gd1.setHubOnCarrier=nq.setHubOnCarrier;Gd1.closeSession=Dd1.closeSession;Gd1.makeSession=Dd1.makeSession;Gd1.updateSession=Dd1.updateSession;Gd1.SessionFlusher=TD9.SessionFlusher;Gd1.Scope=Zd1.Scope;Gd1.getGlobalScope=Zd1.getGlobalScope;Gd1.setGlobalScope=Zd1.setGlobalScope;Gd1.addGlobalEventProcessor=nv0.addGlobalEventProcessor;Gd1.notifyEventProcessors=nv0.notifyEventProcessors;Gd1.getEnvelopeEndpointWithUrlEncodedAuth=av0.getEnvelopeEndpointWithUrlEncodedAuth;Gd1.getReportDialogEndpoint=av0.getReportDialogEndpoint;Gd1.BaseClient=sv0.BaseClient;Gd1.addEventProcessor=sv0.addEventProcessor;Gd1.ServerRuntimeClient=PD9.ServerRuntimeClient;Gd1.initAndBind=rv0.initAndBind;Gd1.setCurrentClient=rv0.setCurrentClient;Gd1.createTransport=SD9.createTransport;Gd1.makeOfflineTransport=jD9.makeOfflineTransport;Gd1.makeMultiplexedTransport=kD9.makeMultiplexedTransport;Gd1.SDK_VERSION=yD9.SDK_VERSION;Gd1.addIntegration=IJ1.addIntegration;Gd1.convertIntegrationFnToClass=IJ1.convertIntegrationFnToClass;Gd1.defineIntegration=IJ1.defineIntegration;Gd1.getIntegrationsToSetup=IJ1.getIntegrationsToSetup;Gd1.applyScopeDataToEvent=ov0.applyScopeDataToEvent;Gd1.mergeScopeData=ov0.mergeScopeData;Gd1.prepareEvent=_D9.prepareEvent;Gd1.createCheckInEnvelope=xD9.createCheckInEnvelope;Gd1.createSpanEnvelope=vD9.createSpanEnvelope;Gd1.hasTracingEnabled=bD9.hasTracingEnabled;Gd1.isSentryRequestUrl=fD9.isSentryRequestUrl;Gd1.handleCallbackErrors=hD9.handleCallbackErrors;Gd1.parameterize=gD9.parameterize;Gd1.spanIsSampled=YJ1.spanIsSampled;Gd1.spanToJSON=YJ1.spanToJSON;Gd1.spanToTraceContext=YJ1.spanToTraceContext;Gd1.spanToTraceHeader=YJ1.spanToTraceHeader;Gd1.getRootSpan=uD9.getRootSpan;Gd1.applySdkMetadata=mD9.applySdkMetadata;Gd1.DEFAULT_ENVIRONMENT=dD9.DEFAULT_ENVIRONMENT;Gd1.ModuleMetadata=tv0.ModuleMetadata;Gd1.moduleMetadataIntegration=tv0.moduleMetadataIntegration;Gd1.RequestData=ev0.RequestData;Gd1.requestDataIntegration=ev0.requestDataIntegration;Gd1.InboundFilters=Ab0.InboundFilters;Gd1.inboundFiltersIntegration=Ab0.inboundFiltersIntegration;Gd1.FunctionToString=Bb0.FunctionToString;Gd1.functionToStringIntegration=Bb0.functionToStringIntegration;Gd1.LinkedErrors=Qb0.LinkedErrors;Gd1.linkedErrorsIntegration=Qb0.linkedErrorsIntegration;Gd1.metrics=lD9.metrics;Gd1.Integrations=pD9});
var Ob0=E((Rb0)=>{var{_optionalChain:Jd1}=$A();Object.defineProperty(Rb0,"__esModule",{value:!0});var PW=$A(),zJ1=oX(),yF9=vj();class EJ1{static __initStatic(){this.id="Apollo"}constructor(A={useNestjs:!1}){this.name=EJ1.id,this._useNest=!!A.useNestjs}loadDependency(){if(this._useNest)this._module=this._module||PW.loadModule("@nestjs/graphql");else this._module=this._module||PW.loadModule("apollo-server-core");return this._module}setupOnce(A,B){if(yF9.shouldDisableAutoInstrumentation(B)){zJ1.DEBUG_BUILD&&PW.logger.log("Apollo Integration is skipped because of instrumenter configuration.");return}if(this._useNest){let Q=this.loadDependency();if(!Q){zJ1.DEBUG_BUILD&&PW.logger.error("Apollo-NestJS Integration was unable to require @nestjs/graphql package.");return}PW.fill(Q.GraphQLFactory.prototype,"mergeWithSchema",function(D){return function(...Z){return PW.fill(this.resolversExplorerService,"explore",function(G){return function(){let F=PW.arrayify(G.call(this));return Mb0(F,B)}}),D.call(this,...Z)}})}else{let Q=this.loadDependency();if(!Q){zJ1.DEBUG_BUILD&&PW.logger.error("Apollo Integration was unable to require apollo-server-core package.");return}PW.fill(Q.ApolloServerBase.prototype,"constructSchema",function(D){return function(){if(!this.config.resolvers){if(zJ1.DEBUG_BUILD){if(this.config.schema)PW.logger.warn("Apollo integration is not able to trace `ApolloServer` instances constructed via `schema` property.If you are using NestJS with Apollo, please use `Sentry.Integrations.Apollo({ useNestjs: true })` instead."),PW.logger.warn();else if(this.config.modules)PW.logger.warn("Apollo integration is not able to trace `ApolloServer` instances constructed via `modules` property.");PW.logger.error("Skipping tracing as no resolvers found on the `ApolloServer` instance.")}return D.call(this)}let Z=PW.arrayify(this.config.resolvers);return this.config.resolvers=Mb0(Z,B),D.call(this)}})}}}EJ1.__initStatic();function Mb0(A,B){return A.map((Q)=>{return Object.keys(Q).forEach((D)=>{Object.keys(Q[D]).forEach((Z)=>{if(typeof Q[D][Z]!=="function")return;_F9(Q,D,Z,B)})}),Q})}function _F9(A,B,Q,D){PW.fill(A[B],Q,function(Z){return function(...G){let I=D().getScope().getSpan(),Y=Jd1([I,"optionalAccess",(J)=>J.startChild,"call",(J)=>J({description:`${B}.${Q}`,op:"graphql.resolve",origin:"auto.graphql.apollo"})]),W=Z.call(this,...G);if(PW.isThenable(W))return W.then((J)=>{return Jd1([Y,"optionalAccess",(X)=>X.end,"call",(X)=>X()]),J});return Jd1([Y,"optionalAccess",(J)=>J.end,"call",(J)=>J()]),W}})}Rb0.Apollo=EJ1});
var Od1=E((Zh0)=>{Object.defineProperty(Zh0,"__esModule",{value:!0});var YO=OQ(),Ah0=$A(),cW9=Jb0(),lW9=Vb0(),pW9=Kb0(),iW9=Eb0(),nW9=$b0(),aW9=Lb0(),sW9=Ob0(),rW9=Pb0(),Bh0=df0(),Rd1=rf0(),Qh0=OJ1(),TJ1=wl(),Dh0=qd1(),oW9=ef0();Zh0.IdleTransaction=YO.IdleTransaction;Zh0.Span=YO.Span;Zh0.SpanStatus=YO.SpanStatus;Zh0.Transaction=YO.Transaction;Zh0.extractTraceparentData=YO.extractTraceparentData;Zh0.getActiveTransaction=YO.getActiveTransaction;Zh0.hasTracingEnabled=YO.hasTracingEnabled;Zh0.spanStatusfromHttpCode=YO.spanStatusfromHttpCode;Zh0.startIdleTransaction=YO.startIdleTransaction;Zh0.TRACEPARENT_REGEXP=Ah0.TRACEPARENT_REGEXP;Zh0.stripUrlQueryAndFragment=Ah0.stripUrlQueryAndFragment;Zh0.Express=cW9.Express;Zh0.Postgres=lW9.Postgres;Zh0.Mysql=pW9.Mysql;Zh0.Mongo=iW9.Mongo;Zh0.Prisma=nW9.Prisma;Zh0.GraphQL=aW9.GraphQL;Zh0.Apollo=sW9.Apollo;Zh0.lazyLoadedNodePerformanceMonitoringIntegrations=rW9.lazyLoadedNodePerformanceMonitoringIntegrations;Zh0.BROWSER_TRACING_INTEGRATION_ID=Bh0.BROWSER_TRACING_INTEGRATION_ID;Zh0.BrowserTracing=Bh0.BrowserTracing;Zh0.browserTracingIntegration=Rd1.browserTracingIntegration;Zh0.startBrowserTracingNavigationSpan=Rd1.startBrowserTracingNavigationSpan;Zh0.startBrowserTracingPageLoadSpan=Rd1.startBrowserTracingPageLoadSpan;Zh0.defaultRequestInstrumentationOptions=Qh0.defaultRequestInstrumentationOptions;Zh0.instrumentOutgoingRequests=Qh0.instrumentOutgoingRequests;Zh0.addClsInstrumentationHandler=TJ1.addClsInstrumentationHandler;Zh0.addFidInstrumentationHandler=TJ1.addFidInstrumentationHandler;Zh0.addLcpInstrumentationHandler=TJ1.addLcpInstrumentationHandler;Zh0.addPerformanceInstrumentationHandler=TJ1.addPerformanceInstrumentationHandler;Zh0.addTracingHeadersToFetchRequest=Dh0.addTracingHeadersToFetchRequest;Zh0.instrumentFetchRequest=Dh0.instrumentFetchRequest;Zh0.addExtensionMethods=oW9.addExtensionMethods});
var Om0=E((Rm0)=>{Object.defineProperty(Rm0,"__esModule",{value:!0});var JO=OQ(),BN=$A(),eJ1=d21(),wm0="HttpClient",xz9=(A={})=>{let B={failedRequestStatusCodes:[[500,599]],failedRequestTargets:[/.*/],...A};return{name:wm0,setupOnce(){},setup(Q){cz9(Q,B),lz9(Q,B)}}},$m0=JO.defineIntegration(xz9),vz9=JO.convertIntegrationFnToClass(wm0,$m0);function bz9(A,B,Q,D){if(Nm0(A,Q.status,Q.url)){let Z=pz9(B,D),G,F,I,Y;if(Mm0())[{headers:G,cookies:I},{headers:F,cookies:Y}]=[{cookieHeader:"Cookie",obj:Z},{cookieHeader:"Set-Cookie",obj:Q}].map(({cookieHeader:J,obj:X})=>{let V=gz9(X.headers),C;try{let K=V[J]||V[J.toLowerCase()]||void 0;if(K)C=qm0(K)}catch(K){eJ1.DEBUG_BUILD&&BN.logger.log(`Could not extract cookies from header ${J}`)}return{headers:V,cookies:C}});let W=Lm0({url:Z.url,method:Z.method,status:Q.status,requestHeaders:G,responseHeaders:F,requestCookies:I,responseCookies:Y});JO.captureEvent(W)}}function fz9(A,B,Q,D){if(Nm0(A,B.status,B.responseURL)){let Z,G,F;if(Mm0()){try{let Y=B.getResponseHeader("Set-Cookie")||B.getResponseHeader("set-cookie")||void 0;if(Y)G=qm0(Y)}catch(Y){eJ1.DEBUG_BUILD&&BN.logger.log("Could not extract cookies from response headers")}try{F=uz9(B)}catch(Y){eJ1.DEBUG_BUILD&&BN.logger.log("Could not extract headers from response")}Z=D}let I=Lm0({url:B.responseURL,method:Q,status:B.status,requestHeaders:Z,responseHeaders:F,responseCookies:G});JO.captureEvent(I)}}function hz9(A){if(A){let B=A["Content-Length"]||A["content-length"];if(B)return parseInt(B,10)}return}function qm0(A){return A.split("; ").reduce((B,Q)=>{let[D,Z]=Q.split("=");return B[D]=Z,B},{})}function gz9(A){let B={};return A.forEach((Q,D)=>{B[D]=Q}),B}function uz9(A){let B=A.getAllResponseHeaders();if(!B)return{};return B.split(`\r
`).reduce((Q,D)=>{let[Z,G]=D.split(": ");return Q[Z]=G,Q},{})}function mz9(A,B){return A.some((Q)=>{if(typeof Q==="string")return B.includes(Q);return Q.test(B)})}function dz9(A,B){return A.some((Q)=>{if(typeof Q==="number")return Q===B;return B>=Q[0]&&B<=Q[1]})}function cz9(A,B){if(!BN.supportsNativeFetch())return;BN.addFetchInstrumentationHandler((Q)=>{if(JO.getClient()!==A)return;let{response:D,args:Z}=Q,[G,F]=Z;if(!D)return;bz9(B,G,D,F)})}function lz9(A,B){if(!("XMLHttpRequest"in BN.GLOBAL_OBJ))return;BN.addXhrInstrumentationHandler((Q)=>{if(JO.getClient()!==A)return;let D=Q.xhr,Z=D[BN.SENTRY_XHR_DATA_KEY];if(!Z)return;let{method:G,request_headers:F}=Z;try{fz9(B,D,G,F)}catch(I){eJ1.DEBUG_BUILD&&BN.logger.warn("Error while extracting response event form XHR response",I)}})}function Nm0(A,B,Q){return dz9(A.failedRequestStatusCodes,B)&&mz9(A.failedRequestTargets,Q)&&!JO.isSentryRequestUrl(Q,JO.getClient())}function Lm0(A){let B=`HTTP Client Error with status code: ${A.status}`,Q={message:B,exception:{values:[{type:"Error",value:B}]},request:{url:A.url,method:A.method,headers:A.requestHeaders,cookies:A.requestCookies},contexts:{response:{status_code:A.status,headers:A.responseHeaders,cookies:A.responseCookies,body_size:hz9(A.responseHeaders)}}};return BN.addExceptionMechanism(Q,{type:"http.client",handled:!1}),Q}function pz9(A,B){if(!B&&A instanceof Request)return A;if(A instanceof Request&&A.bodyUsed)return A;return new Request(A,B)}function Mm0(){let A=JO.getClient();return A?Boolean(A.getOptions().sendDefaultPii):!1}Rm0.HttpClient=vz9;Rm0.httpClientIntegration=$m0});
var Ou1=E((bj0)=>{Object.defineProperty(bj0,"__esModule",{value:!0});var SoB=mq(),joB=lU(),koB=RW(),WW1=koB.getGlobalObject();function yoB(){try{return new ErrorEvent(""),!0}catch(A){return!1}}function _oB(){try{return new DOMError(""),!0}catch(A){return!1}}function xoB(){try{return new DOMException(""),!0}catch(A){return!1}}function Ru1(){if(!("fetch"in WW1))return!1;try{return new Request("http://www.example.com"),!0}catch(A){return!1}}function Mu1(A){return A&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(A.toString())}function voB(){if(typeof EdgeRuntime==="string")return!0;if(!Ru1())return!1;if(Mu1(WW1.fetch))return!0;let A=!1,B=WW1.document;if(B&&typeof B.createElement==="function")try{let Q=B.createElement("iframe");if(Q.hidden=!0,B.head.appendChild(Q),Q.contentWindow&&Q.contentWindow.fetch)A=Mu1(Q.contentWindow.fetch);B.head.removeChild(Q)}catch(Q){SoB.DEBUG_BUILD&&joB.logger.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",Q)}return A}function boB(){return"ReportingObserver"in WW1}function foB(){if(!Ru1())return!1;try{return new Request("_",{referrerPolicy:"origin"}),!0}catch(A){return!1}}bj0.isNativeFetch=Mu1;bj0.supportsDOMError=_oB;bj0.supportsDOMException=xoB;bj0.supportsErrorEvent=yoB;bj0.supportsFetch=Ru1;bj0.supportsNativeFetch=voB;bj0.supportsReferrerPolicy=foB;bj0.supportsReportingObserver=boB});
var Ov0=E((Rv0)=>{Object.defineProperty(Rv0,"__esModule",{value:!0});var ID9=$A(),Lv0=N21(),YD9=im1(),WD9=U21(),DJ1=q21();class Mv0{constructor(A){this._client=A,this._buckets=new Map,this._interval=setInterval(()=>this.flush(),Lv0.DEFAULT_BROWSER_FLUSH_INTERVAL)}add(A,B,Q,D="none",Z={},G=ID9.timestampInSeconds()){let F=Math.floor(G),I=DJ1.sanitizeMetricKey(B),Y=DJ1.sanitizeTags(Z),W=DJ1.sanitizeUnit(D),J=DJ1.getBucketKey(A,I,W,Y),X=this._buckets.get(J),V=X&&A===Lv0.SET_METRIC_TYPE?X.metric.weight:0;if(X){if(X.metric.add(Q),X.timestamp<F)X.timestamp=F}else X={metric:new YD9.METRIC_MAP[A](Q),timestamp:F,metricType:A,name:I,unit:W,tags:Y},this._buckets.set(J,X);let C=typeof Q==="string"?X.metric.weight-V:Q;WD9.updateMetricSummaryOnActiveSpan(A,I,C,W,Z,J)}flush(){if(this._buckets.size===0)return;if(this._client.captureAggregateMetrics){let A=Array.from(this._buckets).map(([,B])=>B);this._client.captureAggregateMetrics(A)}this._buckets.clear()}close(){clearInterval(this._interval),this.flush()}}Rv0.BrowserMetricsAggregator=Mv0});
var Ox0=E((Rx0)=>{Object.defineProperty(Rx0,"__esModule",{value:!0});var rX=$A(),Nx0=qG(),Mx0=30;function f39(A,B,Q=rX.makePromiseBuffer(A.bufferSize||Mx0)){let D={},Z=(F)=>Q.drain(F);function G(F){let I=[];if(rX.forEachEnvelopeItem(F,(X,V)=>{let C=rX.envelopeItemTypeToDataCategory(V);if(rX.isRateLimited(D,C)){let K=Lx0(X,V);A.recordDroppedEvent("ratelimit_backoff",C,K)}else I.push(X)}),I.length===0)return rX.resolvedSyncPromise();let Y=rX.createEnvelope(F[0],I),W=(X)=>{rX.forEachEnvelopeItem(Y,(V,C)=>{let K=Lx0(V,C);A.recordDroppedEvent(X,rX.envelopeItemTypeToDataCategory(C),K)})},J=()=>B({body:rX.serializeEnvelope(Y,A.textEncoder)}).then((X)=>{if(X.statusCode!==void 0&&(X.statusCode<200||X.statusCode>=300))Nx0.DEBUG_BUILD&&rX.logger.warn(`Sentry responded with status code ${X.statusCode} to sent event.`);return D=rX.updateRateLimits(D,X),X},(X)=>{throw W("network_error"),X});return Q.add(J).then((X)=>X,(X)=>{if(X instanceof rX.SentryError)return Nx0.DEBUG_BUILD&&rX.logger.error("Skipped sending event because buffer is full."),W("queue_overflow"),rX.resolvedSyncPromise();else throw X})}return G.__sentry__baseTransport__=!0,{send:G,flush:Z}}function Lx0(A,B){if(B!=="event"&&B!=="transaction")return;return Array.isArray(A)?A[1]:void 0}Rx0.DEFAULT_TRANSPORT_BUFFER_SIZE=Mx0;Rx0.createTransport=f39});
var Oy0=E((Ry0)=>{Object.defineProperty(Ry0,"__esModule",{value:!0});var i09=Zm1();function n09(A){let B=i09._optionalChain(A);return B==null?!0:B}Ry0._optionalChainDelete=n09});
var PW1=E((iy0)=>{Object.defineProperty(iy0,"__esModule",{value:!0});var kC=$A(),r99=Al(),dy0=J21(),Hm1=jW1(),Km1=SW1(),o99=aX();function t99(A,B,Q,D,Z,G){let{normalizeDepth:F=3,normalizeMaxBreadth:I=1000}=A,Y={...B,event_id:B.event_id||Q.event_id||kC.uuid4(),timestamp:B.timestamp||kC.dateTimestampInSeconds()},W=Q.integrations||A.integrations.map((z)=>z.name);if(e99(Y,A),AQ9(Y,W),B.type===void 0)ly0(Y,A.stackParser);let J=QQ9(D,Q.captureContext);if(Q.mechanism)kC.addExceptionMechanism(Y,Q.mechanism);let X=Z&&Z.getEventProcessors?Z.getEventProcessors():[],V=Hm1.getGlobalScope().getScopeData();if(G){let z=G.getScopeData();Km1.mergeScopeData(V,z)}if(J){let z=J.getScopeData();Km1.mergeScopeData(V,z)}let C=[...Q.attachments||[],...V.attachments];if(C.length)Q.attachments=C;Km1.applyScopeDataToEvent(Y,V);let K=[...X,...dy0.getGlobalEventProcessors(),...V.eventProcessors];return dy0.notifyEventProcessors(K,Y,Q).then((z)=>{if(z)py0(z);if(typeof F==="number"&&F>0)return BQ9(z,F,I);return z})}function e99(A,B){let{environment:Q,release:D,dist:Z,maxValueLength:G=250}=B;if(!("environment"in A))A.environment="environment"in B?Q:r99.DEFAULT_ENVIRONMENT;if(A.release===void 0&&D!==void 0)A.release=D;if(A.dist===void 0&&Z!==void 0)A.dist=Z;if(A.message)A.message=kC.truncate(A.message,G);let F=A.exception&&A.exception.values&&A.exception.values[0];if(F&&F.value)F.value=kC.truncate(F.value,G);let I=A.request;if(I&&I.url)I.url=kC.truncate(I.url,G)}var cy0=new WeakMap;function ly0(A,B){let Q=kC.GLOBAL_OBJ._sentryDebugIds;if(!Q)return;let D,Z=cy0.get(B);if(Z)D=Z;else D=new Map,cy0.set(B,D);let G=Object.keys(Q).reduce((F,I)=>{let Y,W=D.get(I);if(W)Y=W;else Y=B(I),D.set(I,Y);for(let J=Y.length-1;J>=0;J--){let X=Y[J];if(X.filename){F[X.filename]=Q[I];break}}return F},{});try{A.exception.values.forEach((F)=>{F.stacktrace.frames.forEach((I)=>{if(I.filename)I.debug_id=G[I.filename]})})}catch(F){}}function py0(A){let B={};try{A.exception.values.forEach((D)=>{D.stacktrace.frames.forEach((Z)=>{if(Z.debug_id){if(Z.abs_path)B[Z.abs_path]=Z.debug_id;else if(Z.filename)B[Z.filename]=Z.debug_id;delete Z.debug_id}})})}catch(D){}if(Object.keys(B).length===0)return;A.debug_meta=A.debug_meta||{},A.debug_meta.images=A.debug_meta.images||[];let Q=A.debug_meta.images;Object.keys(B).forEach((D)=>{Q.push({type:"sourcemap",code_file:D,debug_id:B[D]})})}function AQ9(A,B){if(B.length>0)A.sdk=A.sdk||{},A.sdk.integrations=[...A.sdk.integrations||[],...B]}function BQ9(A,B,Q){if(!A)return null;let D={...A,...A.breadcrumbs&&{breadcrumbs:A.breadcrumbs.map((Z)=>({...Z,...Z.data&&{data:kC.normalize(Z.data,B,Q)}}))},...A.user&&{user:kC.normalize(A.user,B,Q)},...A.contexts&&{contexts:kC.normalize(A.contexts,B,Q)},...A.extra&&{extra:kC.normalize(A.extra,B,Q)}};if(A.contexts&&A.contexts.trace&&D.contexts){if(D.contexts.trace=A.contexts.trace,A.contexts.trace.data)D.contexts.trace.data=kC.normalize(A.contexts.trace.data,B,Q)}if(A.spans)D.spans=A.spans.map((Z)=>{let G=o99.spanToJSON(Z).data;if(G)Z.data=kC.normalize(G,B,Q);return Z});return D}function QQ9(A,B){if(!B)return A;let Q=A?A.clone():new Hm1.Scope;return Q.update(B),Q}function DQ9(A){if(!A)return;if(ZQ9(A))return{captureContext:A};if(FQ9(A))return{captureContext:A};return A}function ZQ9(A){return A instanceof Hm1.Scope||typeof A==="function"}var GQ9=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"];function FQ9(A){return Object.keys(A).some((B)=>GQ9.includes(B))}iy0.applyDebugIds=ly0;iy0.applyDebugMeta=py0;iy0.parseEventHintOrCaptureContext=DQ9;iy0.prepareEvent=t99});
var Pb0=E((Tb0,fj)=>{Object.defineProperty(Tb0,"__esModule",{value:!0});var Yf=$A(),vF9=[()=>{return new(Yf.dynamicRequire(fj,"./apollo")).Apollo},()=>{return new(Yf.dynamicRequire(fj,"./apollo")).Apollo({useNestjs:!0})},()=>{return new(Yf.dynamicRequire(fj,"./graphql")).GraphQL},()=>{return new(Yf.dynamicRequire(fj,"./mongo")).Mongo},()=>{return new(Yf.dynamicRequire(fj,"./mongo")).Mongo({mongoose:!0})},()=>{return new(Yf.dynamicRequire(fj,"./mysql")).Mysql},()=>{return new(Yf.dynamicRequire(fj,"./postgres")).Postgres}];Tb0.lazyLoadedNodePerformanceMonitoringIntegrations=vF9});
var Ph0=E((Th0)=>{var{_optionalChain:XX9}=$A();Object.defineProperty(Th0,"__esModule",{value:!0});var Rh0=J1("domain"),Kf=OQ();function Oh0(){return Rh0.active}function VX9(){let A=Oh0();if(!A)return;return Kf.ensureHubOnCarrier(A),Kf.getHubFromCarrier(A)}function CX9(A){let B={};return Kf.ensureHubOnCarrier(B,A),Kf.getHubFromCarrier(B)}function KX9(A,B){let Q=Oh0();if(Q&&XX9([B,"optionalAccess",(F)=>F.reuseExisting]))return A();let D=Rh0.create(),Z=Q?Kf.getHubFromCarrier(Q):void 0,G=CX9(Z);return Kf.setHubOnCarrier(D,G),D.bind(()=>{return A()})()}function HX9(){Kf.setAsyncContextStrategy({getCurrentHub:VX9,runWithAsyncContext:KX9})}Th0.setDomainAsyncContextStrategy=HX9});
var Pu1=E((uj0)=>{Object.defineProperty(uj0,"__esModule",{value:!0});var ioB=bH(),noB=Ou1(),fj0=RW(),A21=rR();function aoB(A){A21.addHandler("fetch",A),A21.maybeInstrument("fetch",soB)}function soB(){if(!noB.supportsNativeFetch())return;ioB.fill(fj0.GLOBAL_OBJ,"fetch",function(A){return function(...B){let{method:Q,url:D}=gj0(B),Z={args:B,fetchData:{method:Q,url:D},startTimestamp:Date.now()};return A21.triggerHandlers("fetch",{...Z}),A.apply(fj0.GLOBAL_OBJ,B).then((G)=>{let F={...Z,endTimestamp:Date.now(),response:G};return A21.triggerHandlers("fetch",F),G},(G)=>{let F={...Z,endTimestamp:Date.now(),error:G};throw A21.triggerHandlers("fetch",F),G})}})}function Tu1(A,B){return!!A&&typeof A==="object"&&!!A[B]}function hj0(A){if(typeof A==="string")return A;if(!A)return"";if(Tu1(A,"url"))return A.url;if(A.toString)return A.toString();return""}function gj0(A){if(A.length===0)return{method:"GET",url:""};if(A.length===2){let[Q,D]=A;return{url:hj0(Q),method:Tu1(D,"method")?String(D.method).toUpperCase():"GET"}}let B=A[0];return{url:hj0(B),method:Tu1(B,"method")?String(B.method).toUpperCase():"GET"}}uj0.addFetchInstrumentationHandler=aoB;uj0.parseFetchArgs=gj0});
var Py0=E((Ty0)=>{Object.defineProperty(Ty0,"__esModule",{value:!0});function s09(A){return A.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}Ty0.escapeStringForRegex=s09});
var Qd1=E(($v0)=>{Object.defineProperty($v0,"__esModule",{value:!0});var zv0=$A(),Ev0=BO(),s79="cause",r79=5,Uv0="LinkedErrors",o79=(A={})=>{let B=A.limit||r79,Q=A.key||s79;return{name:Uv0,setupOnce(){},preprocessEvent(D,Z,G){let F=G.getOptions();zv0.applyAggregateErrorsToEvent(zv0.exceptionFromError,F.stackParser,F.maxValueLength,Q,B,D,Z)}}},wv0=Ev0.defineIntegration(o79),t79=Ev0.convertIntegrationFnToClass(Uv0,wv0);$v0.LinkedErrors=t79;$v0.linkedErrorsIntegration=wv0});
var Qf=E((ey0)=>{Object.defineProperty(ey0,"__esModule",{value:!0});var Y49=$A(),W49=Al(),oy0=hH(),J49=Ql(),wm1=aX();function ty0(A,B,Q){let D=B.getOptions(),{publicKey:Z}=B.getDsn()||{},{segment:G}=Q&&Q.getUser()||{},F=Y49.dropUndefinedKeys({environment:D.environment||W49.DEFAULT_ENVIRONMENT,release:D.release,user_segment:G,public_key:Z,trace_id:A});return B.emit&&B.emit("createDsc",F),F}function X49(A){let B=oy0.getClient();if(!B)return{};let Q=ty0(wm1.spanToJSON(A).trace_id||"",B,oy0.getCurrentScope()),D=J49.getRootSpan(A);if(!D)return Q;let Z=D&&D._frozenDynamicSamplingContext;if(Z)return Z;let{sampleRate:G,source:F}=D.metadata;if(G!=null)Q.sample_rate=`${G}`;let I=wm1.spanToJSON(D);if(F&&F!=="url")Q.transaction=I.description;return Q.sampled=String(wm1.spanIsSampled(D)),B.emit&&B.emit("createDsc",Q),Q}ey0.getDynamicSamplingContextFromClient=ty0;ey0.getDynamicSamplingContextFromSpan=X49});
var Qg0=E((Bg0)=>{var{_optionalChain:eq}=$A();Object.defineProperty(Bg0,"__esModule",{value:!0});var _d1=J1("url"),YV9=Cf();function WV9(A){let{protocol:B,hostname:Q,port:D}=Ag0(A),Z=A.path?A.path:"/";return`${B}//${Q}${D}${Z}`}function eh0(A){let{protocol:B,hostname:Q,port:D}=Ag0(A),Z=A.pathname||"/",G=A.auth?JV9(A.auth):"";return`${B}//${G}${Q}${D}${Z}`}function JV9(A){let[B,Q]=A.split(":");return`${B?"[Filtered]":""}:${Q?"[Filtered]":""}@`}function XV9(A,B,Q){if(!A)return A;let[D,Z]=A.split(" ");if(B.host&&!B.protocol)B.protocol=eq([Q,"optionalAccess",(G)=>G.agent,"optionalAccess",(G)=>G.protocol]),Z=eh0(B);if(eq([Z,"optionalAccess",(G)=>G.startsWith,"call",(G)=>G("///")]))Z=Z.slice(2);return`${D} ${Z}`}function xd1(A){let B={protocol:A.protocol,hostname:typeof A.hostname==="string"&&A.hostname.startsWith("[")?A.hostname.slice(1,-1):A.hostname,hash:A.hash,search:A.search,pathname:A.pathname,path:`${A.pathname||""}${A.search||""}`,href:A.href};if(A.port!=="")B.port=Number(A.port);if(A.username||A.password)B.auth=`${A.username}:${A.password}`;return B}function VV9(A,B){let Q,D;if(typeof B[B.length-1]==="function")Q=B.pop();if(typeof B[0]==="string")D=xd1(new _d1.URL(B[0]));else if(B[0]instanceof _d1.URL)D=xd1(B[0]);else{D=B[0];try{let Z=new _d1.URL(D.path||"",`${D.protocol||"http:"}//${D.hostname}`);D={pathname:Z.pathname,search:Z.search,hash:Z.hash,...D}}catch(Z){}}if(B.length===2)D={...D,...B[1]};if(D.protocol===void 0)if(YV9.NODE_VERSION.major>8)D.protocol=eq([eq([A,"optionalAccess",(Z)=>Z.globalAgent]),"optionalAccess",(Z)=>Z.protocol])||eq([D.agent,"optionalAccess",(Z)=>Z.protocol])||eq([D._defaultAgent,"optionalAccess",(Z)=>Z.protocol]);else D.protocol=eq([D.agent,"optionalAccess",(Z)=>Z.protocol])||eq([D._defaultAgent,"optionalAccess",(Z)=>Z.protocol])||eq([eq([A,"optionalAccess",(Z)=>Z.globalAgent]),"optionalAccess",(Z)=>Z.protocol]);if(Q)return[D,Q];else return[D]}function Ag0(A){let B=A.protocol||"",Q=A.hostname||A.host||"",D=!A.port||A.port===80||A.port===443||/^(.*):(\d+)$/.test(Q)?"":`:${A.port}`;return{protocol:B,hostname:Q,port:D}}Bg0.cleanSpanDescription=XV9;Bg0.extractRawUrl=WV9;Bg0.extractUrl=eh0;Bg0.normalizeRequestArgs=VV9;Bg0.urlToOptions=xd1});
var Ql=E((ry0)=>{Object.defineProperty(ry0,"__esModule",{value:!0});function F49(A){return A.transaction}ry0.getRootSpan=F49});
var Qm1=E((Uy0)=>{Object.defineProperty(Uy0,"__esModule",{value:!0});function x09(A,B){return A!=null?A:B()}Uy0._nullishCoalesce=x09});
var Qy0=E((By0)=>{Object.defineProperty(By0,"__esModule",{value:!0});var tk0=60000;function ek0(A,B=Date.now()){let Q=parseInt(`${A}`,10);if(!isNaN(Q))return Q*1000;let D=Date.parse(`${A}`);if(!isNaN(D))return D-B;return tk0}function Ay0(A,B){return A[B]||A.all||0}function F09(A,B,Q=Date.now()){return Ay0(A,B)>Q}function I09(A,{statusCode:B,headers:Q},D=Date.now()){let Z={...A},G=Q&&Q["x-sentry-rate-limits"],F=Q&&Q["retry-after"];if(G)for(let I of G.trim().split(",")){let[Y,W,,,J]=I.split(":",5),X=parseInt(Y,10),V=(!isNaN(X)?X:60)*1000;if(!W)Z.all=D+V;else for(let C of W.split(";"))if(C==="metric_bucket"){if(!J||J.split(";").includes("custom"))Z[C]=D+V}else Z[C]=D+V}else if(F)Z.all=D+ek0(F,D);else if(B===429)Z.all=D+60000;return Z}By0.DEFAULT_RETRY_AFTER=tk0;By0.disabledUntil=Ay0;By0.isRateLimited=F09;By0.parseRetryAfterHeader=ek0;By0.updateRateLimits=I09});
var RW=E((Bj0)=>{Object.defineProperty(Bj0,"__esModule",{value:!0});function ZW1(A){return A&&A.Math==Math?A:void 0}var Iu1=typeof globalThis=="object"&&ZW1(globalThis)||typeof window=="object"&&ZW1(window)||typeof self=="object"&&ZW1(self)||typeof global=="object"&&ZW1(global)||function(){return this}()||{};function OsB(){return Iu1}function TsB(A,B,Q){let D=Q||Iu1,Z=D.__SENTRY__=D.__SENTRY__||{};return Z[A]||(Z[A]=B())}Bj0.GLOBAL_OBJ=Iu1;Bj0.getGlobalObject=OsB;Bj0.getGlobalSingleton=TsB});
var S21=E((fb0)=>{Object.defineProperty(fb0,"__esModule",{value:!0});var P21=yC(),iF9=()=>{let A=P21.WINDOW.performance.timing,B=P21.WINDOW.performance.navigation.type,Q={entryType:"navigation",startTime:0,type:B==2?"back_forward":B===1?"reload":"navigate"};for(let D in A)if(D!=="navigationStart"&&D!=="toJSON")Q[D]=Math.max(A[D]-A.navigationStart,0);return Q},nF9=()=>{if(P21.WINDOW.__WEB_VITALS_POLYFILL__)return P21.WINDOW.performance&&(performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]||iF9());else return P21.WINDOW.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]};fb0.getNavigationEntry=nF9});
var SW1=E((B_0)=>{Object.defineProperty(B_0,"__esModule",{value:!0});var C21=$A(),K49=Qf(),H49=Ql(),A_0=aX();function z49(A,B){let{fingerprint:Q,span:D,breadcrumbs:Z,sdkProcessingMetadata:G}=B;if(U49(A,B),D)q49(A,D);N49(A,Q),w49(A,Z),$49(A,G)}function E49(A,B){let{extra:Q,tags:D,user:Z,contexts:G,level:F,sdkProcessingMetadata:I,breadcrumbs:Y,fingerprint:W,eventProcessors:J,attachments:X,propagationContext:V,transactionName:C,span:K}=B;if(Dl(A,"extra",Q),Dl(A,"tags",D),Dl(A,"user",Z),Dl(A,"contexts",G),Dl(A,"sdkProcessingMetadata",I),F)A.level=F;if(C)A.transactionName=C;if(K)A.span=K;if(Y.length)A.breadcrumbs=[...A.breadcrumbs,...Y];if(W.length)A.fingerprint=[...A.fingerprint,...W];if(J.length)A.eventProcessors=[...A.eventProcessors,...J];if(X.length)A.attachments=[...A.attachments,...X];A.propagationContext={...A.propagationContext,...V}}function Dl(A,B,Q){if(Q&&Object.keys(Q).length){A[B]={...A[B]};for(let D in Q)if(Object.prototype.hasOwnProperty.call(Q,D))A[B][D]=Q[D]}}function U49(A,B){let{extra:Q,tags:D,user:Z,contexts:G,level:F,transactionName:I}=B,Y=C21.dropUndefinedKeys(Q);if(Y&&Object.keys(Y).length)A.extra={...Y,...A.extra};let W=C21.dropUndefinedKeys(D);if(W&&Object.keys(W).length)A.tags={...W,...A.tags};let J=C21.dropUndefinedKeys(Z);if(J&&Object.keys(J).length)A.user={...J,...A.user};let X=C21.dropUndefinedKeys(G);if(X&&Object.keys(X).length)A.contexts={...X,...A.contexts};if(F)A.level=F;if(I)A.transaction=I}function w49(A,B){let Q=[...A.breadcrumbs||[],...B];A.breadcrumbs=Q.length?Q:void 0}function $49(A,B){A.sdkProcessingMetadata={...A.sdkProcessingMetadata,...B}}function q49(A,B){A.contexts={trace:A_0.spanToTraceContext(B),...A.contexts};let Q=H49.getRootSpan(B);if(Q){A.sdkProcessingMetadata={dynamicSamplingContext:K49.getDynamicSamplingContextFromSpan(B),...A.sdkProcessingMetadata};let D=A_0.spanToJSON(Q).description;if(D)A.tags={transaction:D,...A.tags}}}function N49(A,B){if(A.fingerprint=A.fingerprint?C21.arrayify(A.fingerprint):[],B)A.fingerprint=A.fingerprint.concat(B);if(A.fingerprint&&!A.fingerprint.length)delete A.fingerprint}B_0.applyScopeDataToEvent=z49;B_0.mergeAndOverwriteScopeData=Dl;B_0.mergeScopeData=E49});
var Sk0=E((Pk0)=>{Object.defineProperty(Pk0,"__esModule",{value:!0});var beB=Nk0(),feB=mq(),Mk0=vH(),heB=lU(),geB=D21(),ueB=iu1(),meB={ip:!1,request:!0,transaction:!0,user:!0},deB=["cookies","data","headers","method","query_string","url"],Rk0=["id","username","email"];function ceB(A,B,Q){if(!A)return;if(!A.metadata.source||A.metadata.source==="url"){let[D,Z]=qW1(B,{path:!0,method:!0});A.updateName(D),A.setMetadata({source:Z})}if(A.setAttribute("url",B.originalUrl||B.url),B.baseUrl)A.setAttribute("baseUrl",B.baseUrl);A.setData("query",Ok0(B,Q))}function qW1(A,B={}){let Q=A.method&&A.method.toUpperCase(),D="",Z="url";if(B.customRoute||A.route)D=B.customRoute||`${A.baseUrl||""}${A.route&&A.route.path}`,Z="route";else if(A.originalUrl||A.url)D=ueB.stripUrlQueryAndFragment(A.originalUrl||A.url||"");let G="";if(B.method&&Q)G+=Q;if(B.method&&B.path)G+=" ";if(B.path&&D)G+=D;return[G,Z]}function leB(A,B){switch(B){case"path":return qW1(A,{path:!0})[0];case"handler":return A.route&&A.route.stack&&A.route.stack[0]&&A.route.stack[0].name||"<anonymous>";case"methodPath":default:{let Q=A._reconstructedRoute?A._reconstructedRoute:void 0;return qW1(A,{path:!0,method:!0,customRoute:Q})[0]}}}function peB(A,B){let Q={};return(Array.isArray(B)?B:Rk0).forEach((Z)=>{if(A&&Z in A)Q[Z]=A[Z]}),Q}function nu1(A,B){let{include:Q=deB,deps:D}=B||{},Z={},G=A.headers||{},F=A.method,I=G.host||A.hostname||A.host||"<no host>",Y=A.protocol==="https"||A.socket&&A.socket.encrypted?"https":"http",W=A.originalUrl||A.url||"",J=W.startsWith(Y)?W:`${Y}://${I}${W}`;return Q.forEach((X)=>{switch(X){case"headers":{if(Z.headers=G,!Q.includes("cookies"))delete Z.headers.cookie;break}case"method":{Z.method=F;break}case"url":{Z.url=J;break}case"cookies":{Z.cookies=A.cookies||G.cookie&&beB.parseCookie(G.cookie)||{};break}case"query_string":{Z.query_string=Ok0(A,D);break}case"data":{if(F==="GET"||F==="HEAD")break;if(A.body!==void 0)Z.data=Mk0.isString(A.body)?A.body:JSON.stringify(geB.normalize(A.body));break}default:if({}.hasOwnProperty.call(A,X))Z[X]=A[X]}}),Z}function ieB(A,B,Q){let D={...meB,...Q&&Q.include};if(D.request){let Z=Array.isArray(D.request)?nu1(B,{include:D.request,deps:Q&&Q.deps}):nu1(B,{deps:Q&&Q.deps});A.request={...A.request,...Z}}if(D.user){let Z=B.user&&Mk0.isPlainObject(B.user)?peB(B.user,D.user):{};if(Object.keys(Z).length)A.user={...A.user,...Z}}if(D.ip){let Z=B.ip||B.socket&&B.socket.remoteAddress;if(Z)A.user={...A.user,ip_address:Z}}if(D.transaction&&!A.transaction)A.transaction=leB(B,D.transaction);return A}function Ok0(A,B){let Q=A.originalUrl||A.url||"";if(!Q)return;if(Q.startsWith("/"))Q=`http://dogs.are.great${Q}`;try{return A.query||typeof URL!=="undefined"&&new URL(Q).search.slice(1)||B&&B.url&&B.url.parse(Q).query||void 0}catch(D){return}}function Tk0(A){let B={};try{A.forEach((Q,D)=>{if(typeof Q==="string")B[D]=Q})}catch(Q){feB.DEBUG_BUILD&&heB.logger.warn("Sentry failed extracting headers from a request object. If you see this, please file an issue.")}return B}function neB(A){let B=Tk0(A.headers);return{method:A.method,url:A.url,headers:B}}Pk0.DEFAULT_USER_INCLUDES=Rk0;Pk0.addRequestDataToEvent=ieB;Pk0.addRequestDataToTransaction=ceB;Pk0.extractPathForTransaction=qW1;Pk0.extractRequestData=nu1;Pk0.winterCGHeadersToDict=Tk0;Pk0.winterCGRequestToRequestData=neB});
var Sx0=E((Px0)=>{Object.defineProperty(Px0,"__esModule",{value:!0});var am1=$A(),u39=qG(),Tx0=100,sm1=5000,m39=3600000;function nm1(A,B){u39.DEBUG_BUILD&&am1.logger.info(`[Offline]: ${A}`,B)}function d39(A){return(B)=>{let Q=A(B),D=B.createStore?B.createStore(B):void 0,Z=sm1,G;function F(J,X,V){if(am1.envelopeContainsItemType(J,["replay_event","replay_recording","client_report"]))return!1;if(B.shouldStore)return B.shouldStore(J,X,V);return!0}function I(J){if(!D)return;if(G)clearTimeout(G);if(G=setTimeout(async()=>{G=void 0;let X=await D.pop();if(X)nm1("Attempting to send previously queued event"),W(X).catch((V)=>{nm1("Failed to retry sending",V)})},J),typeof G!=="number"&&G.unref)G.unref()}function Y(){if(G)return;I(Z),Z=Math.min(Z*2,m39)}async function W(J){try{let X=await Q.send(J),V=Tx0;if(X){if(X.headers&&X.headers["retry-after"])V=am1.parseRetryAfterHeader(X.headers["retry-after"]);else if((X.statusCode||0)>=400)return X}return I(V),Z=sm1,X}catch(X){if(D&&await F(J,X,Z))return await D.insert(J),Y(),nm1("Error sending. Event queued",X),{};else throw X}}if(B.flushAtStartup)Y();return{send:W,flush:(J)=>Q.flush(J)}}}Px0.MIN_DELAY=Tx0;Px0.START_DELAY=sm1;Px0.makeOfflineTransport=d39});
var Td1=E((Wh0)=>{Object.defineProperty(Wh0,"__esModule",{value:!0});var vJ9=J1("os"),bJ9=J1("util"),Ih0=OQ();class Yh0 extends Ih0.ServerRuntimeClient{constructor(A){Ih0.applySdkMetadata(A,"node"),A.transportOptions={textEncoder:new bJ9.TextEncoder,...A.transportOptions};let B={...A,platform:"node",runtime:{name:"node",version:global.process.version},serverName:A.serverName||global.process.env.SENTRY_NAME||vJ9.hostname()};super(B)}}Wh0.NodeClient=Yh0});
var Tm1=E((z_0)=>{Object.defineProperty(z_0,"__esModule",{value:!0});var H69=$A();function z69(A,B,Q=()=>{}){let D;try{D=A()}catch(Z){throw B(Z),Q(),Z}return E69(D,B,Q)}function E69(A,B,Q){if(H69.isThenable(A))return A.then((D)=>{return Q(),D},(D)=>{throw B(D),Q(),D});return Q(),A}z_0.handleCallbackErrors=z69});
var Tu0=E((Ou0)=>{Object.defineProperty(Ou0,"__esModule",{value:!0});var $f=OQ(),qf=$A(),Mu0="CaptureConsole",gH9=(A={})=>{let B=A.levels||qf.CONSOLE_LEVELS;return{name:Mu0,setupOnce(){},setup(Q){if(!("console"in qf.GLOBAL_OBJ))return;qf.addConsoleInstrumentationHandler(({args:D,level:Z})=>{if($f.getClient()!==Q||!B.includes(Z))return;mH9(D,Z)})}}},Ru0=$f.defineIntegration(gH9),uH9=$f.convertIntegrationFnToClass(Mu0,Ru0);function mH9(A,B){let Q={level:qf.severityLevelFromString(B),extra:{arguments:A}};$f.withScope((D)=>{if(D.addEventProcessor((F)=>{return F.logger="console",qf.addExceptionMechanism(F,{handled:!1,type:"console"}),F}),B==="assert"&&A[0]===!1){let F=`Assertion failed: ${qf.safeJoin(A.slice(1)," ")||"console.assert"}`;D.setExtra("arguments",A.slice(1)),$f.captureMessage(F,Q);return}let Z=A.find((F)=>F instanceof Error);if(B==="error"&&Z){$f.captureException(Z,Q);return}let G=qf.safeJoin(A," ");$f.captureMessage(G,Q)})}Ou0.CaptureConsole=uH9;Ou0.captureConsoleIntegration=Ru0});
var U21=E((R_0)=>{Object.defineProperty(R_0,"__esModule",{value:!0});var g69=$A();qG();fW1();Gl();var u69=dW1(),E21;function M_0(A){return E21?E21.get(A):void 0}function m69(A){let B=M_0(A);if(!B)return;let Q={};for(let[,[D,Z]]of B){if(!Q[D])Q[D]=[];Q[D].push(g69.dropUndefinedKeys(Z))}return Q}function d69(A,B,Q,D,Z,G){let F=u69.getActiveSpan();if(F){let I=M_0(F)||new Map,Y=`${A}:${B}@${D}`,W=I.get(G);if(W){let[,J]=W;I.set(G,[Y,{min:Math.min(J.min,Q),max:Math.max(J.max,Q),count:J.count+=1,sum:J.sum+=Q,tags:J.tags}])}else I.set(G,[Y,{min:Q,max:Q,count:1,sum:Q,tags:Z}]);if(!E21)E21=new WeakMap;E21.set(F,I)}}R_0.getMetricSummaryJsonForSpan=m69;R_0.updateMetricSummaryOnActiveSpan=d69});
var UJ1=E((hb0)=>{Object.defineProperty(hb0,"__esModule",{value:!0});var sF9=S21(),rF9=()=>{let A=sF9.getNavigationEntry();return A&&A.activationStart||0};hb0.getActivationStart=rF9});
var Um0=E((Em0)=>{Object.defineProperty(Em0,"__esModule",{value:!0});var Pz9=OQ(),zm0="Transaction",Sz9=()=>{return{name:zm0,setupOnce(){},processEvent(A){let B=kz9(A);for(let Q=B.length-1;Q>=0;Q--){let D=B[Q];if(D.in_app===!0){A.transaction=yz9(D);break}}return A}}},jz9=Pz9.convertIntegrationFnToClass(zm0,Sz9);function kz9(A){let B=A.exception&&A.exception.values&&A.exception.values[0];return B&&B.stacktrace&&B.stacktrace.frames||[]}function yz9(A){return A.module||A.function?`${A.module||"?"}/${A.function||"?"}`:"<unknown>"}Em0.Transaction=jz9});
var Uu1=E((Sj0)=>{Object.defineProperty(Sj0,"__esModule",{value:!0});var zu1=lU(),orB=bH(),IW1=RW(),Eu1=rR();function trB(A){Eu1.addHandler("console",A),Eu1.maybeInstrument("console",erB)}function erB(){if(!("console"in IW1.GLOBAL_OBJ))return;zu1.CONSOLE_LEVELS.forEach(function(A){if(!(A in IW1.GLOBAL_OBJ.console))return;orB.fill(IW1.GLOBAL_OBJ.console,A,function(B){return zu1.originalConsoleMethods[A]=B,function(...Q){let D={args:Q,level:A};Eu1.triggerHandlers("console",D);let Z=zu1.originalConsoleMethods[A];Z&&Z.apply(IW1.GLOBAL_OBJ.console,Q)}})})}Sj0.addConsoleInstrumentationHandler=trB});
var Vb0=E((Xb0)=>{var{_optionalChain:Vl}=$A();Object.defineProperty(Xb0,"__esModule",{value:!0});var Cl=$A(),Id1=oX(),zF9=vj();class XJ1{static __initStatic(){this.id="Postgres"}constructor(A={}){this.name=XJ1.id,this._usePgNative=!!A.usePgNative,this._module=A.module}loadDependency(){return this._module=this._module||Cl.loadModule("pg")}setupOnce(A,B){if(zF9.shouldDisableAutoInstrumentation(B)){Id1.DEBUG_BUILD&&Cl.logger.log("Postgres Integration is skipped because of instrumenter configuration.");return}let Q=this.loadDependency();if(!Q){Id1.DEBUG_BUILD&&Cl.logger.error("Postgres Integration was unable to require `pg` package.");return}let D=this._usePgNative?Vl([Q,"access",(Z)=>Z.native,"optionalAccess",(Z)=>Z.Client]):Q.Client;if(!D){Id1.DEBUG_BUILD&&Cl.logger.error("Postgres Integration was unable to access 'pg-native' bindings.");return}Cl.fill(D.prototype,"query",function(Z){return function(G,F,I){let W=B().getScope().getSpan(),J={"db.system":"postgresql"};try{if(this.database)J["db.name"]=this.database;if(this.host)J["server.address"]=this.host;if(this.port)J["server.port"]=this.port;if(this.user)J["db.user"]=this.user}catch(C){}let X=Vl([W,"optionalAccess",(C)=>C.startChild,"call",(C)=>C({description:typeof G==="string"?G:G.text,op:"db",origin:"auto.db.postgres",data:J})]);if(typeof I==="function")return Z.call(this,G,F,function(C,K){Vl([X,"optionalAccess",(H)=>H.end,"call",(H)=>H()]),I(C,K)});if(typeof F==="function")return Z.call(this,G,function(C,K){Vl([X,"optionalAccess",(H)=>H.end,"call",(H)=>H()]),F(C,K)});let V=typeof F!=="undefined"?Z.call(this,G,F):Z.call(this,G);if(Cl.isThenable(V))return V.then((C)=>{return Vl([X,"optionalAccess",(K)=>K.end,"call",(K)=>K()]),C});return Vl([X,"optionalAccess",(C)=>C.end,"call",(C)=>C()]),V}})}}XJ1.__initStatic();Xb0.Postgres=XJ1});
var Vd1=E((_b0)=>{Object.defineProperty(_b0,"__esModule",{value:!0});var jb0=OQ(),kb0=$A(),yb0=oX(),Xd1=yC();function uF9(){if(Xd1.WINDOW.document)Xd1.WINDOW.document.addEventListener("visibilitychange",()=>{let A=jb0.getActiveTransaction();if(Xd1.WINDOW.document.hidden&&A){let{op:Q,status:D}=jb0.spanToJSON(A);if(yb0.DEBUG_BUILD&&kb0.logger.log(`[Tracing] Transaction: cancelled -> since tab moved to the background, op: ${Q}`),!D)A.setStatus("cancelled");A.setTag("visibilitychange","document.hidden"),A.end()}});else yb0.DEBUG_BUILD&&kb0.logger.warn("[Tracing] Could not set up background tab detection due to lack of global document")}_b0.registerBackgroundTabDetection=uF9});
var Vu0=E((Xu0)=>{var{_optionalChain:oJ1}=$A();Object.defineProperty(Xu0,"__esModule",{value:!0});var rF=OQ(),Rl=$A(),fK9=u21(),tJ1=cd1(),hK9=nd1(),Ju0=Wu0();function gK9(){return function A(B,Q,D){let Z=oJ1([rF.getClient,"call",(J)=>J(),"optionalAccess",(J)=>J.getOptions,"call",(J)=>J()]);if(!Z||Z.instrumenter!=="sentry"||oJ1([B,"access",(J)=>J.method,"optionalAccess",(J)=>J.toUpperCase,"call",(J)=>J()])==="OPTIONS"||oJ1([B,"access",(J)=>J.method,"optionalAccess",(J)=>J.toUpperCase,"call",(J)=>J()])==="HEAD")return D();let G=B.headers&&Rl.isString(B.headers["sentry-trace"])?B.headers["sentry-trace"]:void 0,F=oJ1([B,"access",(J)=>J.headers,"optionalAccess",(J)=>J.baggage]);if(!rF.hasTracingEnabled(Z))return D();let[I,Y]=Rl.extractPathForTransaction(B,{path:!0,method:!0}),W=rF.continueTrace({sentryTrace:G,baggage:F},(J)=>rF.startTransaction({name:I,op:"http.server",origin:"auto.http.node.tracingHandler",...J,data:{[rF.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]:Y},metadata:{...J.metadata,request:B}},{request:Rl.extractRequestData(B)}));rF.getCurrentScope().setSpan(W),Q.__sentry_transaction=W,Q.once("finish",()=>{setImmediate(()=>{Rl.addRequestDataToTransaction(W,B),rF.setHttpStatus(W,Q.statusCode),W.end()})}),D()}}function uK9(A={}){let B;if("include"in A)B={include:A.include};else{let{ip:Q,request:D,transaction:Z,user:G}=A;if(Q||D||Z||G)B={include:Rl.dropUndefinedKeys({ip:Q,request:D,transaction:Z,user:G})}}return B}function mK9(A){let B=uK9(A),Q=rF.getClient();if(Q&&tJ1.isAutoSessionTrackingEnabled(Q)){Q.initSessionFlusher();let D=rF.getCurrentScope();if(D.getSession())D.setSession()}return function D(Z,G,F){if(A&&A.flushTimeout&&A.flushTimeout>0){let I=G.end;G.end=function(Y,W,J){rF.flush(A.flushTimeout).then(()=>{I.call(this,Y,W,J)}).then(null,(X)=>{fK9.DEBUG_BUILD&&Rl.logger.error(X),I.call(this,Y,W,J)})}}rF.runWithAsyncContext(()=>{let I=rF.getCurrentScope();I.setSDKProcessingMetadata({request:Z,requestDataOptionsFromExpressHandler:B});let Y=rF.getClient();if(tJ1.isAutoSessionTrackingEnabled(Y))I.setRequestSession({status:"ok"});G.once("finish",()=>{let W=rF.getClient();if(tJ1.isAutoSessionTrackingEnabled(W))setImmediate(()=>{if(W&&W._captureRequestSession)W._captureRequestSession()})}),F()})}}function dK9(A){let B=A.status||A.statusCode||A.status_code||A.output&&A.output.statusCode;return B?parseInt(B,10):500}function cK9(A){return dK9(A)>=500}function lK9(A){return function B(Q,D,Z,G){if((A&&A.shouldHandleError||cK9)(Q)){rF.withScope((I)=>{I.setSDKProcessingMetadata({request:D});let Y=Z.__sentry_transaction;if(Y&&!rF.getActiveSpan())I.setSpan(Y);let W=rF.getClient();if(W&&tJ1.isAutoSessionTrackingEnabled(W)){if(W._sessionFlusher!==void 0){let V=I.getRequestSession();if(V&&V.status!==void 0)V.status="crashed"}}let J=rF.captureException(Q,{mechanism:{type:"middleware",handled:!1}});Z.sentry=J,G(Q)});return}G(Q)}}var pK9=hK9.trpcMiddleware;Xu0.extractRequestData=Ju0.extractRequestData;Xu0.parseRequest=Ju0.parseRequest;Xu0.errorHandler=lK9;Xu0.requestHandler=mK9;Xu0.tracingHandler=gK9;Xu0.trpcMiddleware=pK9});
var Vu1=E((Yj0)=>{Object.defineProperty(Yj0,"__esModule",{value:!0});var esB=mq(),tA1=lU(),ArB=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function BrB(A){return A==="http"||A==="https"}function QrB(A,B=!1){let{host:Q,path:D,pass:Z,port:G,projectId:F,protocol:I,publicKey:Y}=A;return`${I}://${Y}${B&&Z?`:${Z}`:""}@${Q}${G?`:${G}`:""}/${D?`${D}/`:D}${F}`}function Fj0(A){let B=ArB.exec(A);if(!B){tA1.consoleSandbox(()=>{console.error(`Invalid Sentry Dsn: ${A}`)});return}let[Q,D,Z="",G,F="",I]=B.slice(1),Y="",W=I,J=W.split("/");if(J.length>1)Y=J.slice(0,-1).join("/"),W=J.pop();if(W){let X=W.match(/^\d+/);if(X)W=X[0]}return Ij0({host:G,pass:Z,path:Y,projectId:W,port:F,protocol:Q,publicKey:D})}function Ij0(A){return{protocol:A.protocol,publicKey:A.publicKey||"",pass:A.pass||"",host:A.host,port:A.port||"",path:A.path||"",projectId:A.projectId}}function DrB(A){if(!esB.DEBUG_BUILD)return!0;let{port:B,projectId:Q,protocol:D}=A;if(["protocol","publicKey","host","projectId"].find((F)=>{if(!A[F])return tA1.logger.error(`Invalid Sentry Dsn: ${F} missing`),!0;return!1}))return!1;if(!Q.match(/^\d+$/))return tA1.logger.error(`Invalid Sentry Dsn: Invalid projectId ${Q}`),!1;if(!BrB(D))return tA1.logger.error(`Invalid Sentry Dsn: Invalid protocol ${D}`),!1;if(B&&isNaN(parseInt(B,10)))return tA1.logger.error(`Invalid Sentry Dsn: Invalid port ${B}`),!1;return!0}function ZrB(A){let B=typeof A==="string"?Fj0(A):Ij0(A);if(!B||!DrB(B))return;return B}Yj0.dsnFromString=Fj0;Yj0.dsnToString=QrB;Yj0.makeDsn=ZrB});
var Vy0=E((Xy0)=>{Object.defineProperty(Xy0,"__esModule",{value:!0});var Am1=vH(),Wy0=eA1(),U09=D21(),w09=bH();function Bm1(A,B){return A(B.stack||"",1)}function Jy0(A,B){let Q={type:B.name||B.constructor.name,value:B.message},D=Bm1(A,B);if(D.length)Q.stacktrace={frames:D};return Q}function $09(A){if("name"in A&&typeof A.name==="string"){let B=`'${A.name}' captured as exception`;if("message"in A&&typeof A.message==="string")B+=` with message '${A.message}'`;return B}else if("message"in A&&typeof A.message==="string")return A.message;else return`Object captured as exception with keys: ${w09.extractExceptionKeysForMessage(A)}`}function q09(A,B,Q,D){let Z=typeof A==="function"?A().getClient():A,G=Q,I=D&&D.data&&D.data.mechanism||{handled:!0,type:"generic"},Y;if(!Am1.isError(Q)){if(Am1.isPlainObject(Q)){let J=Z&&Z.getOptions().normalizeDepth;Y={["__serialized__"]:U09.normalizeToSize(Q,J)};let X=$09(Q);G=D&&D.syntheticException||new Error(X),G.message=X}else G=D&&D.syntheticException||new Error(Q),G.message=Q;I.synthetic=!0}let W={exception:{values:[Jy0(B,G)]}};if(Y)W.extra=Y;return Wy0.addExceptionTypeValue(W,void 0,void 0),Wy0.addExceptionMechanism(W,I),{...W,event_id:D&&D.event_id}}function N09(A,B,Q="info",D,Z){let G={event_id:D&&D.event_id,level:Q};if(Z&&D&&D.syntheticException){let F=Bm1(A,D.syntheticException);if(F.length)G.exception={values:[{value:B,stacktrace:{frames:F}}]}}if(Am1.isParameterizedString(B)){let{__sentry_template_string__:F,__sentry_template_values__:I}=B;return G.logentry={message:F,params:I},G}return G.message=B,G}Xy0.eventFromMessage=N09;Xy0.eventFromUnknownInput=q09;Xy0.exceptionFromError=Jy0;Xy0.parseStackFrames=Bm1});
var Wf=E((mb0)=>{Object.defineProperty(mb0,"__esModule",{value:!0});var DI9=(A,B,Q)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(A)){let D=new PerformanceObserver((Z)=>{B(Z.getEntries())});return D.observe(Object.assign({type:A,buffered:!0},Q||{})),D}}catch(D){}return};mb0.observe=DI9});
var Wg0=E((Yg0)=>{Object.defineProperty(Yg0,"__esModule",{value:!0});function PV9(A,B,Q){let D=0,Z=5,G=0;return setInterval(()=>{if(G===0){if(D>A){if(Z*=2,Q(Z),Z>86400)Z=86400;G=Z}}else if(G-=1,G===0)B();D=0},1000).unref(),()=>{D+=1}}function bd1(A){return A!==void 0&&(A.length===0||A==="?"||A==="<anonymous>")}function SV9(A,B){return A===B||bd1(A)&&bd1(B)}function Ig0(A){if(A===void 0)return;return A.slice(-10).reduce((B,Q)=>`${B},${Q.function},${Q.lineno},${Q.colno}`,"")}function jV9(A,B){if(B===void 0)return;return Ig0(A(B,1))}Yg0.createRateLimiter=PV9;Yg0.functionNamesMatch=SV9;Yg0.hashFrames=Ig0;Yg0.hashFromStack=jV9;Yg0.isAnonymous=bd1});
var Wu0=E((Yu0)=>{Object.defineProperty(Yu0,"__esModule",{value:!0});var Iu0=$A();function _K9(A,B){return Iu0.extractRequestData(A,{include:B})}function xK9(A,B,Q={}){return Iu0.addRequestDataToEvent(A,B,{include:Q})}Yu0.extractRequestData=_K9;Yu0.parseRequest=xK9});
var Yf0=E((If0)=>{Object.defineProperty(If0,"__esModule",{value:!0});var zd1=yC(),nI9=Hl(),aI9=UJ1(),sI9=S21(),rI9=zl(),Ed1=(A)=>{if(!zd1.WINDOW.document)return;if(zd1.WINDOW.document.prerendering)addEventListener("prerenderingchange",()=>Ed1(A),!0);else if(zd1.WINDOW.document.readyState!=="complete")addEventListener("load",()=>Ed1(A),!0);else setTimeout(A,0)},oI9=(A,B)=>{B=B||{};let Q=rI9.initMetric("TTFB"),D=nI9.bindReporter(A,Q,B.reportAllChanges);Ed1(()=>{let Z=sI9.getNavigationEntry();if(Z){if(Q.value=Math.max(Z.responseStart-aI9.getActivationStart(),0),Q.value<0||Q.value>performance.now())return;Q.entries=[Z],D(!0)}})};If0.onTTFB=oI9});
var Yu1=E((Qj0)=>{Object.defineProperty(Qj0,"__esModule",{value:!0});var ksB=vH(),ysB=RW(),sc=ysB.getGlobalObject(),_sB=80;function xsB(A,B={}){if(!A)return"<unknown>";try{let Q=A,D=5,Z=[],G=0,F=0,I=" > ",Y=I.length,W,J=Array.isArray(B)?B:B.keyAttrs,X=!Array.isArray(B)&&B.maxStringLength||_sB;while(Q&&G++<D){if(W=vsB(Q,J),W==="html"||G>1&&F+Z.length*Y+W.length>=X)break;Z.push(W),F+=W.length,Q=Q.parentNode}return Z.reverse().join(I)}catch(Q){return"<unknown>"}}function vsB(A,B){let Q=A,D=[],Z,G,F,I,Y;if(!Q||!Q.tagName)return"";if(sc.HTMLElement){if(Q instanceof HTMLElement&&Q.dataset&&Q.dataset.sentryComponent)return Q.dataset.sentryComponent}D.push(Q.tagName.toLowerCase());let W=B&&B.length?B.filter((X)=>Q.getAttribute(X)).map((X)=>[X,Q.getAttribute(X)]):null;if(W&&W.length)W.forEach((X)=>{D.push(`[${X[0]}="${X[1]}"]`)});else{if(Q.id)D.push(`#${Q.id}`);if(Z=Q.className,Z&&ksB.isString(Z)){G=Z.split(/\s+/);for(Y=0;Y<G.length;Y++)D.push(`.${G[Y]}`)}}let J=["aria-label","type","name","title","alt"];for(Y=0;Y<J.length;Y++)if(F=J[Y],I=Q.getAttribute(F),I)D.push(`[${F}="${I}"]`);return D.join("")}function bsB(){try{return sc.document.location.href}catch(A){return""}}function fsB(A){if(sc.document&&sc.document.querySelector)return sc.document.querySelector(A);return null}function hsB(A){if(!sc.HTMLElement)return null;let B=A,Q=5;for(let D=0;D<Q;D++){if(!B)return null;if(B instanceof HTMLElement&&B.dataset.sentryComponent)return B.dataset.sentryComponent;B=B.parentNode}return null}Qj0.getComponentName=hsB;Qj0.getDomElement=fsB;Qj0.getLocationHref=bsB;Qj0.htmlTreeAsString=xsB});
var Yy0=E((Iy0)=>{Object.defineProperty(Iy0,"__esModule",{value:!0});function z09(A){let B=[],Q={};return{add(D,Z){while(B.length>=A){let G=B.shift();if(G!==void 0)delete Q[G]}if(Q[D])this.delete(D);B.push(D),Q[D]=Z},clear(){Q={},B=[]},get(D){return Q[D]},size(){return B.length},delete(D){if(!Q[D])return!1;delete Q[D];for(let Z=0;Z<B.length;Z++)if(B[Z]===D){B.splice(Z,1);break}return!0}}}Iy0.makeFifoCache=z09});
var Zm0=E((Dm0)=>{Object.defineProperty(Dm0,"__esModule",{value:!0});var l21=OQ(),Am0=$A(),Hz9=Am0.GLOBAL_OBJ,Bm0="ReportingObserver",eu0=new WeakMap,zz9=(A={})=>{let B=A.types||["crash","deprecation","intervention"];function Q(D){if(!eu0.has(l21.getClient()))return;for(let Z of D)l21.withScope((G)=>{G.setExtra("url",Z.url);let F=`ReportingObserver [${Z.type}]`,I="No details available";if(Z.body){let Y={};for(let W in Z.body)Y[W]=Z.body[W];if(G.setExtra("body",Y),Z.type==="crash"){let W=Z.body;I=[W.crashId||"",W.reason||""].join(" ").trim()||I}else I=Z.body.message||I}l21.captureMessage(`${F}: ${I}`)})}return{name:Bm0,setupOnce(){if(!Am0.supportsReportingObserver())return;new Hz9.ReportingObserver(Q,{buffered:!0,types:B}).observe()},setup(D){eu0.set(D,!0)}}},Qm0=l21.defineIntegration(zz9),Ez9=l21.convertIntegrationFnToClass(Bm0,Qm0);Dm0.ReportingObserver=Ez9;Dm0.reportingObserverIntegration=Qm0});
var Zm1=E((My0)=>{Object.defineProperty(My0,"__esModule",{value:!0});function l09(A){let B=void 0,Q=A[0],D=1;while(D<A.length){let Z=A[D],G=A[D+1];if(D+=2,(Z==="optionalAccess"||Z==="optionalCall")&&Q==null)return;if(Z==="access"||Z==="optionalAccess")B=Q,Q=G(Q);else if(Z==="call"||Z==="optionalCall")Q=G((...F)=>Q.call(B,...F)),B=void 0}return Q}My0._optionalChain=l09});
var _W1=E((G_0)=>{Object.defineProperty(G_0,"__esModule",{value:!0});var x49="7.120.3";G_0.SDK_VERSION=x49});
var _k0=E((yk0)=>{Object.defineProperty(yk0,"__esModule",{value:!0});var jk0=["fatal","error","warning","log","info","debug"];function B19(A){return kk0(A)}function kk0(A){return A==="warn"?"warning":jk0.includes(A)?A:"log"}yk0.severityFromString=B19;yk0.severityLevelFromString=kk0;yk0.validSeverityLevels=jk0});
var _m1=E((u_0)=>{Object.defineProperty(u_0,"__esModule",{value:!0});var $89=$A(),q89=qG(),N89=lq(),L89=aX(),M89=fW1(),R89=km1(),g_0=ym1(),O89=iW1();function T89(){let B=this.getScope().getSpan();return B?{"sentry-trace":L89.spanToTraceHeader(B)}:{}}function P89(A,B){let Q=this.getClient(),D=Q&&Q.getOptions()||{},Z=D.instrumenter||"sentry",G=A.instrumenter||"sentry";if(Z!==G)q89.DEBUG_BUILD&&$89.logger.error(`A transaction was started with instrumenter=\`${G}\`, but the SDK is configured with the \`${Z}\` instrumenter.
The transaction will not be sampled. Please use the ${Z} instrumentation to start transactions.`),A.sampled=!1;let F=new O89.Transaction(A,this);if(F=g_0.sampleTransaction(F,D,{name:A.name,parentSampled:A.parentSampled,transactionContext:A,attributes:{...A.data,...A.attributes},...B}),F.isRecording())F.initSpanRecorder(D._experiments&&D._experiments.maxSpans);if(Q&&Q.emit)Q.emit("startTransaction",F);return F}function S89(A,B,Q,D,Z,G,F,I=!1){let Y=A.getClient(),W=Y&&Y.getOptions()||{},J=new R89.IdleTransaction(B,A,Q,D,F,Z,I);if(J=g_0.sampleTransaction(J,W,{name:B.name,parentSampled:B.parentSampled,transactionContext:B,attributes:{...B.data,...B.attributes},...G}),J.isRecording())J.initSpanRecorder(W._experiments&&W._experiments.maxSpans);if(Y&&Y.emit)Y.emit("startTransaction",J);return J}function j89(){let A=N89.getMainCarrier();if(!A.__SENTRY__)return;if(A.__SENTRY__.extensions=A.__SENTRY__.extensions||{},!A.__SENTRY__.extensions.startTransaction)A.__SENTRY__.extensions.startTransaction=P89;if(!A.__SENTRY__.extensions.traceHeaders)A.__SENTRY__.extensions.traceHeaders=T89;M89.registerErrorInstrumentation()}u_0.addTracingExtensions=j89;u_0.startIdleTransaction=S89});
var aX=E((my0)=>{Object.defineProperty(my0,"__esModule",{value:!0});var Cm1=$A(),h99=0,hy0=1;function g99(A){let{spanId:B,traceId:Q}=A.spanContext(),{data:D,op:Z,parent_span_id:G,status:F,tags:I,origin:Y}=gy0(A);return Cm1.dropUndefinedKeys({data:D,op:Z,parent_span_id:G,span_id:B,status:F,tags:I,trace_id:Q,origin:Y})}function u99(A){let{traceId:B,spanId:Q}=A.spanContext(),D=uy0(A);return Cm1.generateSentryTraceHeader(B,Q,D)}function m99(A){if(typeof A==="number")return fy0(A);if(Array.isArray(A))return A[0]+A[1]/1e9;if(A instanceof Date)return fy0(A.getTime());return Cm1.timestampInSeconds()}function fy0(A){return A>9999999999?A/1000:A}function gy0(A){if(d99(A))return A.getSpanJSON();if(typeof A.toJSON==="function")return A.toJSON();return{}}function d99(A){return typeof A.getSpanJSON==="function"}function uy0(A){let{traceFlags:B}=A.spanContext();return Boolean(B&hy0)}my0.TRACE_FLAG_NONE=h99;my0.TRACE_FLAG_SAMPLED=hy0;my0.spanIsSampled=uy0;my0.spanTimeInputToSeconds=m99;my0.spanToJSON=gy0;my0.spanToTraceContext=g99;my0.spanToTraceHeader=u99});
var ab0=E((nb0)=>{Object.defineProperty(nb0,"__esModule",{value:!0});var UI9=Hl(),wI9=qJ1(),$I9=zl(),qI9=Wf(),NI9=El(),LI9=(A)=>{let B=wI9.getVisibilityWatcher(),Q=$I9.initMetric("FID"),D,Z=(I)=>{if(I.startTime<B.firstHiddenTime)Q.value=I.processingStart-I.startTime,Q.entries.push(I),D(!0)},G=(I)=>{I.forEach(Z)},F=qI9.observe("first-input",G);if(D=UI9.bindReporter(A,Q),F)NI9.onHidden(()=>{G(F.takeRecords()),F.disconnect()},!0)};nb0.onFID=LI9});
var ad1=E((wu0)=>{Object.defineProperty(wu0,"__esModule",{value:!0});var AV=OQ(),Ku0=$A();function Cu0(A){return A&&A.statusCode!==void 0}function tK9(A){return A&&A.error!==void 0}function eK9(A){AV.captureException(A,{mechanism:{type:"hapi",handled:!1,data:{function:"hapiErrorPlugin"}}})}var Hu0={name:"SentryHapiErrorPlugin",version:AV.SDK_VERSION,register:async function(A){A.events.on("request",(Q,D)=>{let Z=AV.getActiveTransaction();if(tK9(D))eK9(D.error);if(Z)Z.setStatus("internal_error"),Z.end()})}},zu0={name:"SentryHapiTracingPlugin",version:AV.SDK_VERSION,register:async function(A){let B=A;B.ext("onPreHandler",(Q,D)=>{let Z=AV.continueTrace({sentryTrace:Q.headers["sentry-trace"]||void 0,baggage:Q.headers.baggage||void 0},(G)=>{return AV.startTransaction({...G,op:"hapi.request",name:Q.route.path,description:`${Q.route.method} ${Q.path}`})});return AV.getCurrentScope().setSpan(Z),D.continue}),B.ext("onPreResponse",(Q,D)=>{let Z=AV.getActiveTransaction();if(Q.response&&Cu0(Q.response)&&Z){let G=Q.response;G.header("sentry-trace",AV.spanToTraceHeader(Z));let F=Ku0.dynamicSamplingContextToSentryBaggageHeader(AV.getDynamicSamplingContextFromSpan(Z));if(F)G.header("baggage",F)}return D.continue}),B.ext("onPostHandler",(Q,D)=>{let Z=AV.getActiveTransaction();if(Z){if(Q.response&&Cu0(Q.response))AV.setHttpStatus(Z,Q.response.statusCode);Z.end()}return D.continue})}},Eu0="Hapi",AH9=(A={})=>{let B=A.server;return{name:Eu0,setupOnce(){if(!B)return;Ku0.fill(B,"start",(Q)=>{return async function(){return await this.register(zu0),await this.register(Hu0),Q.apply(this)}})}}},Uu0=AV.defineIntegration(AH9),BH9=AV.convertIntegrationFnToClass(Eu0,Uu0);wu0.Hapi=BH9;wu0.hapiErrorPlugin=Hu0;wu0.hapiIntegration=Uu0;wu0.hapiTracingPlugin=zu0});
var au0=E((nu0)=>{Object.defineProperty(nu0,"__esModule",{value:!0});var lu0=OQ(),uj=$A(),Fz9=d21(),pu0="ExtraErrorData",Iz9=(A={})=>{let B=A.depth||3,Q=A.captureErrorCause||!1;return{name:pu0,setupOnce(){},processEvent(D,Z){return Wz9(D,Z,B,Q)}}},iu0=lu0.defineIntegration(Iz9),Yz9=lu0.convertIntegrationFnToClass(pu0,iu0);function Wz9(A,B={},Q,D){if(!B.originalException||!uj.isError(B.originalException))return A;let Z=B.originalException.name||B.originalException.constructor.name,G=Jz9(B.originalException,D);if(G){let F={...A.contexts},I=uj.normalize(G,Q);if(uj.isPlainObject(I))uj.addNonEnumerableProperty(I,"__sentry_skip_normalization__",!0),F[Z]=I;return{...A,contexts:F}}return A}function Jz9(A,B){try{let Q=["name","message","stack","line","column","fileName","lineNumber","columnNumber","toJSON"],D={};for(let Z of Object.keys(A)){if(Q.indexOf(Z)!==-1)continue;let G=A[Z];D[Z]=uj.isError(G)?G.toString():G}if(B&&A.cause!==void 0)D.cause=uj.isError(A.cause)?A.cause.toString():A.cause;if(typeof A.toJSON==="function"){let Z=A.toJSON();for(let G of Object.keys(Z)){let F=Z[G];D[G]=uj.isError(F)?F.toString():F}}return D}catch(Q){Fz9.DEBUG_BUILD&&uj.logger.error("Unable to extract extra data from the Error object:",Q)}return null}nu0.ExtraErrorData=Yz9;nu0.extraErrorDataIntegration=iu0});
var au1=E((hk0)=>{Object.defineProperty(hk0,"__esModule",{value:!0});var xk0=RW(),vk0=1000;function bk0(){return Date.now()/vk0}function G19(){let{performance:A}=xk0.GLOBAL_OBJ;if(!A||!A.now)return bk0;let B=Date.now()-A.now(),Q=A.timeOrigin==null?B:A.timeOrigin;return()=>{return(Q+A.now())/vk0}}var fk0=G19(),F19=fk0;hk0._browserPerformanceTimeOriginMode=void 0;var I19=(()=>{let{performance:A}=xk0.GLOBAL_OBJ;if(!A||!A.now){hk0._browserPerformanceTimeOriginMode="none";return}let B=3600000,Q=A.now(),D=Date.now(),Z=A.timeOrigin?Math.abs(A.timeOrigin+Q-D):B,G=Z<B,F=A.timing&&A.timing.navigationStart,Y=typeof F==="number"?Math.abs(F+Q-D):B,W=Y<B;if(G||W)if(Z<=Y)return hk0._browserPerformanceTimeOriginMode="timeOrigin",A.timeOrigin;else return hk0._browserPerformanceTimeOriginMode="navigationStart",F;return hk0._browserPerformanceTimeOriginMode="dateNow",D})();hk0.browserPerformanceTimeOrigin=I19;hk0.dateTimestampInSeconds=bk0;hk0.timestampInSeconds=fk0;hk0.timestampWithMs=F19});
var bH=E((Ej0)=>{Object.defineProperty(Ej0,"__esModule",{value:!0});var WrB=Yu1(),JrB=mq(),rc=vH(),XrB=lU(),Xj0=oA1();function VrB(A,B,Q){if(!(B in A))return;let D=A[B],Z=Q(D);if(typeof Z==="function")Hj0(Z,D);A[B]=Z}function Kj0(A,B,Q){try{Object.defineProperty(A,B,{value:Q,writable:!0,configurable:!0})}catch(D){JrB.DEBUG_BUILD&&XrB.logger.log(`Failed to add non-enumerable property "${B}" to object`,A)}}function Hj0(A,B){try{let Q=B.prototype||{};A.prototype=B.prototype=Q,Kj0(A,"__sentry_original__",B)}catch(Q){}}function CrB(A){return A.__sentry_original__}function KrB(A){return Object.keys(A).map((B)=>`${encodeURIComponent(B)}=${encodeURIComponent(A[B])}`).join("&")}function zj0(A){if(rc.isError(A))return{message:A.message,name:A.name,stack:A.stack,...Cj0(A)};else if(rc.isEvent(A)){let B={type:A.type,target:Vj0(A.target),currentTarget:Vj0(A.currentTarget),...Cj0(A)};if(typeof CustomEvent!=="undefined"&&rc.isInstanceOf(A,CustomEvent))B.detail=A.detail;return B}else return A}function Vj0(A){try{return rc.isElement(A)?WrB.htmlTreeAsString(A):Object.prototype.toString.call(A)}catch(B){return"<unknown>"}}function Cj0(A){if(typeof A==="object"&&A!==null){let B={};for(let Q in A)if(Object.prototype.hasOwnProperty.call(A,Q))B[Q]=A[Q];return B}else return{}}function HrB(A,B=40){let Q=Object.keys(zj0(A));if(Q.sort(),!Q.length)return"[object has no keys]";if(Q[0].length>=B)return Xj0.truncate(Q[0],B);for(let D=Q.length;D>0;D--){let Z=Q.slice(0,D).join(", ");if(Z.length>B)continue;if(D===Q.length)return Z;return Xj0.truncate(Z,B)}return""}function zrB(A){return Ku1(A,new Map)}function Ku1(A,B){if(ErB(A)){let Q=B.get(A);if(Q!==void 0)return Q;let D={};B.set(A,D);for(let Z of Object.keys(A))if(typeof A[Z]!=="undefined")D[Z]=Ku1(A[Z],B);return D}if(Array.isArray(A)){let Q=B.get(A);if(Q!==void 0)return Q;let D=[];return B.set(A,D),A.forEach((Z)=>{D.push(Ku1(Z,B))}),D}return A}function ErB(A){if(!rc.isPlainObject(A))return!1;try{let B=Object.getPrototypeOf(A).constructor.name;return!B||B==="Object"}catch(B){return!0}}function UrB(A){let B;switch(!0){case(A===void 0||A===null):B=new String(A);break;case(typeof A==="symbol"||typeof A==="bigint"):B=Object(A);break;case rc.isPrimitive(A):B=new A.constructor(A);break;default:B=A;break}return B}Ej0.addNonEnumerableProperty=Kj0;Ej0.convertToPlainObject=zj0;Ej0.dropUndefinedKeys=zrB;Ej0.extractExceptionKeysForMessage=HrB;Ej0.fill=VrB;Ej0.getOriginalFunction=CrB;Ej0.markFunctionWrapped=Hj0;Ej0.objectify=UrB;Ej0.urlEncode=KrB});
var bW1=E((V_0)=>{Object.defineProperty(V_0,"__esModule",{value:!0});var X_0=$A(),A69=lq();function B69(A){return(A||A69.getCurrentHub()).getScope().getTransaction()}var Q69=X_0.extractTraceparentData;V_0.stripUrlQueryAndFragment=X_0.stripUrlQueryAndFragment;V_0.extractTraceparentData=Q69;V_0.getActiveTransaction=B69});
var bb0=E((vb0)=>{Object.defineProperty(vb0,"__esModule",{value:!0});var lF9=()=>{return`v3-${Date.now()}-${Math.floor(Math.random()*8999999999999)+1000000000000}`};vb0.generateUniqueID=lF9});
var bu1=E((pj0)=>{Object.defineProperty(pj0,"__esModule",{value:!0});var lj0=bH();mq();lU();var ItB=RW(),YtB=vu1(),KW1=rR(),B21=ItB.GLOBAL_OBJ,CW1;function WtB(A){KW1.addHandler("history",A),KW1.maybeInstrument("history",JtB)}function JtB(){if(!YtB.supportsHistory())return;let A=B21.onpopstate;B21.onpopstate=function(...Q){let D=B21.location.href,Z=CW1;CW1=D;let G={from:Z,to:D};if(KW1.triggerHandlers("history",G),A)try{return A.apply(this,Q)}catch(F){}};function B(Q){return function(...D){let Z=D.length>2?D[2]:void 0;if(Z){let G=CW1,F=String(Z);CW1=F;let I={from:G,to:F};KW1.triggerHandlers("history",I)}return Q.apply(this,D)}}lj0.fill(B21.history,"pushState",B),lj0.fill(B21.history,"replaceState",B)}pj0.addHistoryInstrumentationHandler=WtB});
var cW1=E((j_0)=>{Object.defineProperty(j_0,"__esModule",{value:!0});var Zf=$A(),T_0=qG(),B89=U21(),AO=w21(),P_0=Ql(),Il=aX(),Q89=Gl();class S_0{constructor(A=1000){this._maxlen=A,this.spans=[]}add(A){if(this.spans.length>this._maxlen)A.spanRecorder=void 0;else this.spans.push(A)}}class Sm1{constructor(A={}){if(this._traceId=A.traceId||Zf.uuid4(),this._spanId=A.spanId||Zf.uuid4().substring(16),this._startTime=A.startTimestamp||Zf.timestampInSeconds(),this.tags=A.tags?{...A.tags}:{},this.data=A.data?{...A.data}:{},this.instrumenter=A.instrumenter||"sentry",this._attributes={},this.setAttributes({[AO.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]:A.origin||"manual",[AO.SEMANTIC_ATTRIBUTE_SENTRY_OP]:A.op,...A.attributes}),this._name=A.name||A.description,A.parentSpanId)this._parentSpanId=A.parentSpanId;if("sampled"in A)this._sampled=A.sampled;if(A.status)this._status=A.status;if(A.endTimestamp)this._endTime=A.endTimestamp;if(A.exclusiveTime!==void 0)this._exclusiveTime=A.exclusiveTime;this._measurements=A.measurements?{...A.measurements}:{}}get name(){return this._name||""}set name(A){this.updateName(A)}get description(){return this._name}set description(A){this._name=A}get traceId(){return this._traceId}set traceId(A){this._traceId=A}get spanId(){return this._spanId}set spanId(A){this._spanId=A}set parentSpanId(A){this._parentSpanId=A}get parentSpanId(){return this._parentSpanId}get sampled(){return this._sampled}set sampled(A){this._sampled=A}get attributes(){return this._attributes}set attributes(A){this._attributes=A}get startTimestamp(){return this._startTime}set startTimestamp(A){this._startTime=A}get endTimestamp(){return this._endTime}set endTimestamp(A){this._endTime=A}get status(){return this._status}set status(A){this._status=A}get op(){return this._attributes[AO.SEMANTIC_ATTRIBUTE_SENTRY_OP]}set op(A){this.setAttribute(AO.SEMANTIC_ATTRIBUTE_SENTRY_OP,A)}get origin(){return this._attributes[AO.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]}set origin(A){this.setAttribute(AO.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,A)}spanContext(){let{_spanId:A,_traceId:B,_sampled:Q}=this;return{spanId:A,traceId:B,traceFlags:Q?Il.TRACE_FLAG_SAMPLED:Il.TRACE_FLAG_NONE}}startChild(A){let B=new Sm1({...A,parentSpanId:this._spanId,sampled:this._sampled,traceId:this._traceId});if(B.spanRecorder=this.spanRecorder,B.spanRecorder)B.spanRecorder.add(B);let Q=P_0.getRootSpan(this);if(B.transaction=Q,T_0.DEBUG_BUILD&&Q){let D=A&&A.op||"< unknown op >",Z=Il.spanToJSON(B).description||"< unknown name >",G=Q.spanContext().spanId,F=`[Tracing] Starting '${D}' span on transaction '${Z}' (${G}).`;Zf.logger.log(F),this._logMessage=F}return B}setTag(A,B){return this.tags={...this.tags,[A]:B},this}setData(A,B){return this.data={...this.data,[A]:B},this}setAttribute(A,B){if(B===void 0)delete this._attributes[A];else this._attributes[A]=B}setAttributes(A){Object.keys(A).forEach((B)=>this.setAttribute(B,A[B]))}setStatus(A){return this._status=A,this}setHttpStatus(A){return Q89.setHttpStatus(this,A),this}setName(A){this.updateName(A)}updateName(A){return this._name=A,this}isSuccess(){return this._status==="ok"}finish(A){return this.end(A)}end(A){if(this._endTime)return;let B=P_0.getRootSpan(this);if(T_0.DEBUG_BUILD&&B&&B.spanContext().spanId!==this._spanId){let Q=this._logMessage;if(Q)Zf.logger.log(Q.replace("Starting","Finishing"))}this._endTime=Il.spanTimeInputToSeconds(A)}toTraceparent(){return Il.spanToTraceHeader(this)}toContext(){return Zf.dropUndefinedKeys({data:this._getData(),description:this._name,endTimestamp:this._endTime,op:this.op,parentSpanId:this._parentSpanId,sampled:this._sampled,spanId:this._spanId,startTimestamp:this._startTime,status:this._status,tags:this.tags,traceId:this._traceId})}updateWithContext(A){return this.data=A.data||{},this._name=A.name||A.description,this._endTime=A.endTimestamp,this.op=A.op,this._parentSpanId=A.parentSpanId,this._sampled=A.sampled,this._spanId=A.spanId||this._spanId,this._startTime=A.startTimestamp||this._startTime,this._status=A.status,this.tags=A.tags||{},this._traceId=A.traceId||this._traceId,this}getTraceContext(){return Il.spanToTraceContext(this)}getSpanJSON(){return Zf.dropUndefinedKeys({data:this._getData(),description:this._name,op:this._attributes[AO.SEMANTIC_ATTRIBUTE_SENTRY_OP],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:this._status,tags:Object.keys(this.tags).length>0?this.tags:void 0,timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[AO.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN],_metrics_summary:B89.getMetricSummaryJsonForSpan(this),profile_id:this._attributes[AO.SEMANTIC_ATTRIBUTE_PROFILE_ID],exclusive_time:this._exclusiveTime,measurements:Object.keys(this._measurements).length>0?this._measurements:void 0})}isRecording(){return!this._endTime&&!!this._sampled}toJSON(){return this.getSpanJSON()}_getData(){let{data:A,_attributes:B}=this,Q=Object.keys(A).length>0,D=Object.keys(B).length>0;if(!Q&&!D)return;if(Q&&D)return{...A,...B};return Q?A:B}}j_0.Span=Sm1;j_0.SpanRecorder=S_0});
var cd1=E((ng0)=>{var{_optionalChain:hC9}=$A();Object.defineProperty(ng0,"__esModule",{value:!0});var mH=OQ(),Uf=$A(),gC9=yh0(),uC9=Td1(),mC9=kJ1(),dC9=yJ1(),cC9=xJ1(),lC9=vJ1(),pC9=hJ1(),iC9=gJ1(),nC9=dJ1(),aC9=lJ1(),sC9=pJ1(),rC9=nJ1(),oC9=dd1(),tC9=jd1(),cg0=[mH.inboundFiltersIntegration(),mH.functionToStringIntegration(),mH.linkedErrorsIntegration(),mH.requestDataIntegration(),mC9.consoleIntegration(),lC9.httpIntegration(),rC9.nativeNodeFetchintegration(),nC9.onUncaughtExceptionIntegration(),aC9.onUnhandledRejectionIntegration(),cC9.contextLinesIntegration(),pC9.localVariablesIntegration(),dC9.nodeContextIntegration(),iC9.modulesIntegration()];function lg0(A){let B=mH.getMainCarrier(),Q=hC9([B,"access",(D)=>D.__SENTRY__,"optionalAccess",(D)=>D.integrations])||[];return[...cg0,...Q]}function eC9(A={}){if(gC9.setNodeAsyncContextStrategy(),A.defaultIntegrations===void 0)A.defaultIntegrations=lg0();if(A.dsn===void 0&&process.env.SENTRY_DSN)A.dsn=process.env.SENTRY_DSN;let B=process.env.SENTRY_TRACES_SAMPLE_RATE;if(A.tracesSampleRate===void 0&&B){let D=parseFloat(B);if(isFinite(D))A.tracesSampleRate=D}if(A.release===void 0){let D=pg0();if(D!==void 0)A.release=D;else A.autoSessionTracking=!1}if(A.environment===void 0&&process.env.SENTRY_ENVIRONMENT)A.environment=process.env.SENTRY_ENVIRONMENT;if(A.autoSessionTracking===void 0&&A.dsn!==void 0)A.autoSessionTracking=!0;if(A.instrumenter===void 0)A.instrumenter="sentry";let Q={...A,stackParser:Uf.stackParserFromStackParserOptions(A.stackParser||ig0),integrations:mH.getIntegrationsToSetup(A),transport:A.transport||tC9.makeNodeTransport};if(mH.initAndBind(A.clientClass||uC9.NodeClient,Q),A.autoSessionTracking)BK9();if(QK9(),A.spotlight){let D=mH.getClient();if(D&&D.addIntegration){let Z=D.getOptions().integrations;for(let G of Z)D.addIntegration(G);D.addIntegration(sC9.spotlightIntegration({sidecarUrl:typeof A.spotlight==="string"?A.spotlight:void 0}))}}}function AK9(A){if(A===void 0)return!1;let B=A&&A.getOptions();if(B&&B.autoSessionTracking!==void 0)return B.autoSessionTracking;return!1}function pg0(A){if(process.env.SENTRY_RELEASE)return process.env.SENTRY_RELEASE;if(Uf.GLOBAL_OBJ.SENTRY_RELEASE&&Uf.GLOBAL_OBJ.SENTRY_RELEASE.id)return Uf.GLOBAL_OBJ.SENTRY_RELEASE.id;return process.env.GITHUB_SHA||process.env.COMMIT_REF||process.env.VERCEL_GIT_COMMIT_SHA||process.env.VERCEL_GITHUB_COMMIT_SHA||process.env.VERCEL_GITLAB_COMMIT_SHA||process.env.VERCEL_BITBUCKET_COMMIT_SHA||process.env.ZEIT_GITHUB_COMMIT_SHA||process.env.ZEIT_GITLAB_COMMIT_SHA||process.env.ZEIT_BITBUCKET_COMMIT_SHA||process.env.CF_PAGES_COMMIT_SHA||A}var ig0=Uf.createStackParser(Uf.nodeStackLineParser(oC9.createGetModuleFromFilename()));function BK9(){mH.startSession(),process.on("beforeExit",()=>{let A=mH.getIsolationScope().getSession();if(A&&!["exited","crashed"].includes(A.status))mH.endSession()})}function QK9(){let A=(process.env.SENTRY_USE_ENVIRONMENT||"").toLowerCase();if(!["false","n","no","off","0"].includes(A)){let B=process.env.SENTRY_TRACE,Q=process.env.SENTRY_BAGGAGE,D=Uf.propagationContextFromHeaders(B,Q);mH.getCurrentScope().setPropagationContext(D)}}ng0.defaultIntegrations=cg0;ng0.defaultStackParser=ig0;ng0.getDefaultIntegrations=lg0;ng0.getSentryRelease=pg0;ng0.init=eC9;ng0.isAutoSessionTrackingEnabled=AK9});
var cm0=E((dm0)=>{Object.defineProperty(dm0,"__esModule",{value:!0});var _m0=Tu0(),xm0=yu0(),vm0=cu0(),bm0=au0(),BE9=tu0(),fm0=Zm0(),hm0=Jm0(),gm0=Hm0(),QE9=Um0(),um0=Om0(),mm0=ym0();dm0.CaptureConsole=_m0.CaptureConsole;dm0.captureConsoleIntegration=_m0.captureConsoleIntegration;dm0.Debug=xm0.Debug;dm0.debugIntegration=xm0.debugIntegration;dm0.Dedupe=vm0.Dedupe;dm0.dedupeIntegration=vm0.dedupeIntegration;dm0.ExtraErrorData=bm0.ExtraErrorData;dm0.extraErrorDataIntegration=bm0.extraErrorDataIntegration;dm0.Offline=BE9.Offline;dm0.ReportingObserver=fm0.ReportingObserver;dm0.reportingObserverIntegration=fm0.reportingObserverIntegration;dm0.RewriteFrames=hm0.RewriteFrames;dm0.rewriteFramesIntegration=hm0.rewriteFramesIntegration;dm0.SessionTiming=gm0.SessionTiming;dm0.sessionTimingIntegration=gm0.sessionTimingIntegration;dm0.Transaction=QE9.Transaction;dm0.HttpClient=um0.HttpClient;dm0.httpClientIntegration=um0.httpClientIntegration;dm0.ContextLines=mm0.ContextLines;dm0.contextLinesIntegration=mm0.contextLinesIntegration});
var cu0=E((du0)=>{Object.defineProperty(du0,"__esModule",{value:!0});var bu0=OQ(),oH9=$A(),tH9=d21(),fu0="Dedupe",eH9=()=>{let A;return{name:fu0,setupOnce(){},processEvent(B){if(B.type)return B;try{if(gu0(B,A))return tH9.DEBUG_BUILD&&oH9.logger.warn("Event dropped due to being a duplicate of previously captured event."),null}catch(Q){}return A=B}}},hu0=bu0.defineIntegration(eH9),Az9=bu0.convertIntegrationFnToClass(fu0,hu0);function gu0(A,B){if(!B)return!1;if(Bz9(A,B))return!0;if(Qz9(A,B))return!0;return!1}function Bz9(A,B){let Q=A.message,D=B.message;if(!Q&&!D)return!1;if(Q&&!D||!Q&&D)return!1;if(Q!==D)return!1;if(!mu0(A,B))return!1;if(!uu0(A,B))return!1;return!0}function Qz9(A,B){let Q=xu0(B),D=xu0(A);if(!Q||!D)return!1;if(Q.type!==D.type||Q.value!==D.value)return!1;if(!mu0(A,B))return!1;if(!uu0(A,B))return!1;return!0}function uu0(A,B){let Q=vu0(A),D=vu0(B);if(!Q&&!D)return!0;if(Q&&!D||!Q&&D)return!1;if(Q=Q,D=D,D.length!==Q.length)return!1;for(let Z=0;Z<D.length;Z++){let G=D[Z],F=Q[Z];if(G.filename!==F.filename||G.lineno!==F.lineno||G.colno!==F.colno||G.function!==F.function)return!1}return!0}function mu0(A,B){let Q=A.fingerprint,D=B.fingerprint;if(!Q&&!D)return!0;if(Q&&!D||!Q&&D)return!1;Q=Q,D=D;try{return Q.join("")===D.join("")}catch(Z){return!1}}function xu0(A){return A.exception&&A.exception.values&&A.exception.values[0]}function vu0(A){let B=A.exception;if(B)try{return B.values[0].stacktrace.frames}catch(Q){return}return}du0.Dedupe=Az9;du0._shouldDropEvent=gu0;du0.dedupeIntegration=hu0});
var d21=E((_u0)=>{Object.defineProperty(_u0,"__esModule",{value:!0});var sH9=typeof __SENTRY_DEBUG__==="undefined"||__SENTRY_DEBUG__;_u0.DEBUG_BUILD=sH9});
var dJ1=E((Pg0)=>{Object.defineProperty(Pg0,"__esModule",{value:!0});var mJ1=OQ(),ZC9=$A(),GC9=u21(),Mg0=md1(),Rg0="OnUncaughtException",FC9=(A={})=>{let B={exitEvenIfOtherHandlersAreRegistered:!0,...A};return{name:Rg0,setupOnce(){},setup(Q){global.process.on("uncaughtException",Tg0(Q,B))}}},Og0=mJ1.defineIntegration(FC9),IC9=mJ1.convertIntegrationFnToClass(Rg0,Og0);function Tg0(A,B){let D=!1,Z=!1,G=!1,F,I=A.getOptions();return Object.assign((Y)=>{let W=Mg0.logAndExitProcess;if(B.onFatalError)W=B.onFatalError;else if(I.onFatalError)W=I.onFatalError;let X=global.process.listeners("uncaughtException").reduce((C,K)=>{if(K.name==="domainUncaughtExceptionClear"||K.tag&&K.tag==="sentry_tracingErrorCallback"||K._errorHandler)return C;else return C+1},0)===0,V=B.exitEvenIfOtherHandlersAreRegistered||X;if(!D){if(F=Y,D=!0,mJ1.getClient()===A)mJ1.captureException(Y,{originalException:Y,captureContext:{level:"fatal"},mechanism:{handled:!1,type:"onuncaughtexception"}});if(!G&&V)G=!0,W(Y)}else if(V){if(G)GC9.DEBUG_BUILD&&ZC9.logger.warn("uncaught exception after calling fatal error shutdown callback - this is bad! forcing shutdown"),Mg0.logAndExitProcess(Y);else if(!Z)Z=!0,setTimeout(()=>{if(!G)G=!0,W(F,Y)},2000)}},{_errorHandler:!0})}Pg0.OnUncaughtException=IC9;Pg0.makeErrorHandler=Tg0;Pg0.onUncaughtExceptionIntegration=Og0});
var dW1=E((L_0)=>{Object.defineProperty(L_0,"__esModule",{value:!0});var z21=$A(),N69=qG(),xj=lq(),gW1=aX();fW1();Gl();var L69=Qf(),Fl=hH(),Pm1=Tm1(),U_0=hW1();function M69(A,B,Q=()=>{},D=()=>{}){let Z=xj.getCurrentHub(),G=Fl.getCurrentScope(),F=G.getSpan(),I=mW1(A),Y=uW1(Z,{parentSpan:F,spanContext:I,forceTransaction:!1,scope:G});return G.setSpan(Y),Pm1.handleCallbackErrors(()=>B(Y),(W)=>{Y&&Y.setStatus("internal_error"),Q(W,Y)},()=>{Y&&Y.end(),G.setSpan(F),D()})}function w_0(A,B){let Q=mW1(A);return xj.runWithAsyncContext(()=>{return Fl.withScope(A.scope,(D)=>{let Z=xj.getCurrentHub(),G=D.getSpan(),I=A.onlyIfParent&&!G?void 0:uW1(Z,{parentSpan:G,spanContext:Q,forceTransaction:A.forceTransaction,scope:D});return Pm1.handleCallbackErrors(()=>B(I),()=>{if(I){let{status:Y}=gW1.spanToJSON(I);if(!Y||Y==="ok")I.setStatus("internal_error")}},()=>I&&I.end())})})}var R69=w_0;function O69(A,B){let Q=mW1(A);return xj.runWithAsyncContext(()=>{return Fl.withScope(A.scope,(D)=>{let Z=xj.getCurrentHub(),G=D.getSpan(),I=A.onlyIfParent&&!G?void 0:uW1(Z,{parentSpan:G,spanContext:Q,forceTransaction:A.forceTransaction,scope:D});function Y(){I&&I.end()}return Pm1.handleCallbackErrors(()=>B(I,Y),()=>{if(I&&I.isRecording()){let{status:W}=gW1.spanToJSON(I);if(!W||W==="ok")I.setStatus("internal_error")}})})})}function T69(A){if(!U_0.hasTracingEnabled())return;let B=mW1(A),Q=xj.getCurrentHub(),D=A.scope?A.scope.getSpan():$_0();if(A.onlyIfParent&&!D)return;let F=(A.scope||Fl.getCurrentScope()).clone();return uW1(Q,{parentSpan:D,spanContext:B,forceTransaction:A.forceTransaction,scope:F})}function $_0(){return Fl.getCurrentScope().getSpan()}var P69=({sentryTrace:A,baggage:B},Q)=>{let D=Fl.getCurrentScope(),{traceparentData:Z,dynamicSamplingContext:G,propagationContext:F}=z21.tracingContextFromHeaders(A,B);if(D.setPropagationContext(F),N69.DEBUG_BUILD&&Z)z21.logger.log(`[Tracing] Continuing trace ${Z.traceId}.`);let I={...Z,metadata:z21.dropUndefinedKeys({dynamicSamplingContext:G})};if(!Q)return I;return xj.runWithAsyncContext(()=>{return Q(I)})};function uW1(A,{parentSpan:B,spanContext:Q,forceTransaction:D,scope:Z}){if(!U_0.hasTracingEnabled())return;let G=xj.getIsolationScope(),F;if(B&&!D)F=B.startChild(Q);else if(B){let I=L69.getDynamicSamplingContextFromSpan(B),{traceId:Y,spanId:W}=B.spanContext(),J=gW1.spanIsSampled(B);F=A.startTransaction({traceId:Y,parentSpanId:W,parentSampled:J,...Q,metadata:{dynamicSamplingContext:I,...Q.metadata}})}else{let{traceId:I,dsc:Y,parentSpanId:W,sampled:J}={...G.getPropagationContext(),...Z.getPropagationContext()};F=A.startTransaction({traceId:I,parentSpanId:W,parentSampled:J,...Q,metadata:{dynamicSamplingContext:Y,...Q.metadata}})}return Z.setSpan(F),S69(F,Z,G),F}function mW1(A){if(A.startTime){let B={...A};return B.startTimestamp=gW1.spanTimeInputToSeconds(A.startTime),delete B.startTime,B}return A}var q_0="_sentryScope",N_0="_sentryIsolationScope";function S69(A,B,Q){if(A)z21.addNonEnumerableProperty(A,N_0,Q),z21.addNonEnumerableProperty(A,q_0,B)}function j69(A){return{scope:A[q_0],isolationScope:A[N_0]}}L_0.continueTrace=P69;L_0.getActiveSpan=$_0;L_0.getCapturedScopesOnSpan=j69;L_0.startActiveSpan=R69;L_0.startInactiveSpan=T69;L_0.startSpan=w_0;L_0.startSpanManual=O69;L_0.trace=M69});
var d_0=E((m_0)=>{Object.defineProperty(m_0,"__esModule",{value:!0});var _89=bW1();function x89(A,B,Q){let D=_89.getActiveTransaction();if(D)D.setMeasurement(A,B,Q)}m_0.setMeasurement=x89});
var dd1=E((dg0)=>{Object.defineProperty(dg0,"__esModule",{value:!0});var ug0=J1("path"),vC9=$A();function mg0(A){return A.replace(/^[A-Z]:/,"").replace(/\\/g,"/")}function bC9(A=process.argv[1]?vC9.dirname(process.argv[1]):process.cwd(),B=ug0.sep==="\\"){let Q=B?mg0(A):A;return(D)=>{if(!D)return;let Z=B?mg0(D):D,{dir:G,base:F,ext:I}=ug0.posix.parse(Z);if(I===".js"||I===".mjs"||I===".cjs")F=F.slice(0,I.length*-1);if(!G)G=".";let Y=G.lastIndexOf("/node_modules");if(Y>-1)return`${G.slice(Y+14).replace(/\//g,".")}:${F}`;if(G.startsWith(Q)){let W=G.slice(Q.length+1).replace(/\//g,".");if(W)W+=":";return W+=F,W}return F}}dg0.createGetModuleFromFilename=bC9});
var df0=E((mf0)=>{Object.defineProperty(mf0,"__esModule",{value:!0});var aU=OQ(),IO=$A(),hj=oX(),$W9=Vd1(),bf0=wl(),v21=$d1(),hf0=OJ1(),qW9=vf0(),Vf=yC(),gf0="BrowserTracing",NW9={...aU.TRACING_DEFAULTS,markBackgroundTransactions:!0,routingInstrumentation:qW9.instrumentRoutingWithDefaults,startTransactionOnLocationChange:!0,startTransactionOnPageLoad:!0,enableLongTask:!0,enableInp:!1,interactionsSampleRate:1,_experiments:{},...hf0.defaultRequestInstrumentationOptions},ff0=10;class uf0{constructor(A){if(this.name=gf0,this._hasSetTracePropagationTargets=!1,aU.addTracingExtensions(),hj.DEBUG_BUILD)this._hasSetTracePropagationTargets=!!(A&&(A.tracePropagationTargets||A.tracingOrigins));if(this.options={...NW9,...A},this.options._experiments.enableLongTask!==void 0)this.options.enableLongTask=this.options._experiments.enableLongTask;if(A&&!A.tracePropagationTargets&&A.tracingOrigins)this.options.tracePropagationTargets=A.tracingOrigins;if(this._collectWebVitals=v21.startTrackingWebVitals(),this._interactionIdToRouteNameMapping={},this.options.enableInp)v21.startTrackingINP(this._interactionIdToRouteNameMapping,this.options.interactionsSampleRate);if(this.options.enableLongTask)v21.startTrackingLongTasks();if(this.options._experiments.enableInteractions)v21.startTrackingInteractions();this._latestRoute={name:void 0,context:void 0}}setupOnce(A,B){this._getCurrentHub=B;let D=B().getClient(),Z=D&&D.getOptions(),{routingInstrumentation:G,startTransactionOnLocationChange:F,startTransactionOnPageLoad:I,markBackgroundTransactions:Y,traceFetch:W,traceXHR:J,shouldCreateSpanForRequest:X,enableHTTPTimings:V,_experiments:C}=this.options,K=Z&&Z.tracePropagationTargets,H=K||this.options.tracePropagationTargets;if(hj.DEBUG_BUILD&&this._hasSetTracePropagationTargets&&K)IO.logger.warn("[Tracing] The `tracePropagationTargets` option was set in the BrowserTracing integration and top level `Sentry.init`. The top level `Sentry.init` value is being used.");if(G((z)=>{let $=this._createRouteTransaction(z);return this.options._experiments.onStartRouteTransaction&&this.options._experiments.onStartRouteTransaction($,z,B),$},I,F),Y)$W9.registerBackgroundTabDetection();if(C.enableInteractions)this._registerInteractionListener();if(this.options.enableInp)this._registerInpInteractionListener();hf0.instrumentOutgoingRequests({traceFetch:W,traceXHR:J,tracePropagationTargets:H,shouldCreateSpanForRequest:X,enableHTTPTimings:V})}_createRouteTransaction(A){if(!this._getCurrentHub){hj.DEBUG_BUILD&&IO.logger.warn(`[Tracing] Did not create ${A.op} transaction because _getCurrentHub is invalid.`);return}let B=this._getCurrentHub(),{beforeNavigate:Q,idleTimeout:D,finalTimeout:Z,heartbeatInterval:G}=this.options,F=A.op==="pageload",I;if(F){let V=F?Ld1("sentry-trace"):"",C=F?Ld1("baggage"):void 0,{traceId:K,dsc:H,parentSpanId:z,sampled:$}=IO.propagationContextFromHeaders(V,C);I={traceId:K,parentSpanId:z,parentSampled:$,...A,metadata:{...A.metadata,dynamicSamplingContext:H},trimEnd:!0}}else I={trimEnd:!0,...A};let Y=typeof Q==="function"?Q(I):I,W=Y===void 0?{...I,sampled:!1}:Y;if(W.metadata=W.name!==I.name?{...W.metadata,source:"custom"}:W.metadata,this._latestRoute.name=W.name,this._latestRoute.context=W,W.sampled===!1)hj.DEBUG_BUILD&&IO.logger.log(`[Tracing] Will not send ${W.op} transaction because of beforeNavigate.`);hj.DEBUG_BUILD&&IO.logger.log(`[Tracing] Starting ${W.op} transaction on scope`);let{location:J}=Vf.WINDOW,X=aU.startIdleTransaction(B,W,D,Z,!0,{location:J},G,F);if(F){if(Vf.WINDOW.document){if(Vf.WINDOW.document.addEventListener("readystatechange",()=>{if(["interactive","complete"].includes(Vf.WINDOW.document.readyState))X.sendAutoFinishSignal()}),["interactive","complete"].includes(Vf.WINDOW.document.readyState))X.sendAutoFinishSignal()}}return X.registerBeforeFinishCallback((V)=>{this._collectWebVitals(),v21.addPerformanceEntries(V)}),X}_registerInteractionListener(){let A,B=()=>{let{idleTimeout:Q,finalTimeout:D,heartbeatInterval:Z}=this.options,G="ui.action.click",F=aU.getActiveTransaction();if(F&&F.op&&["navigation","pageload"].includes(F.op)){hj.DEBUG_BUILD&&IO.logger.warn("[Tracing] Did not create ui.action.click transaction because a pageload or navigation transaction is in progress.");return}if(A)A.setFinishReason("interactionInterrupted"),A.end(),A=void 0;if(!this._getCurrentHub){hj.DEBUG_BUILD&&IO.logger.warn("[Tracing] Did not create ui.action.click transaction because _getCurrentHub is invalid.");return}if(!this._latestRoute.name){hj.DEBUG_BUILD&&IO.logger.warn("[Tracing] Did not create ui.action.click transaction because _latestRouteName is missing.");return}let I=this._getCurrentHub(),{location:Y}=Vf.WINDOW,W={name:this._latestRoute.name,op:"ui.action.click",trimEnd:!0,data:{[aU.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]:this._latestRoute.context?LW9(this._latestRoute.context):"url"}};A=aU.startIdleTransaction(I,W,Q,D,!0,{location:Y},Z)};["click"].forEach((Q)=>{if(Vf.WINDOW.document)addEventListener(Q,B,{once:!1,capture:!0})})}_registerInpInteractionListener(){let A=({entries:B})=>{let Q=aU.getClient(),D=Q!==void 0&&Q.getIntegrationByName!==void 0?Q.getIntegrationByName("Replay"):void 0,Z=D!==void 0?D.getReplayId():void 0,G=aU.getActiveTransaction(),F=aU.getCurrentScope(),I=F!==void 0?F.getUser():void 0;B.forEach((Y)=>{if(MW9(Y)){let W=Y.interactionId;if(W===void 0)return;let J=this._interactionIdToRouteNameMapping[W],X=Y.duration,V=Y.startTime,C=Object.keys(this._interactionIdToRouteNameMapping),K=C.length>0?C.reduce((H,z)=>{return this._interactionIdToRouteNameMapping[H].duration<this._interactionIdToRouteNameMapping[z].duration?H:z}):void 0;if(Y.entryType==="first-input"){if(C.map((z)=>this._interactionIdToRouteNameMapping[z]).some((z)=>{return z.duration===X&&z.startTime===V}))return}if(!W)return;if(J)J.duration=Math.max(J.duration,X);else if(C.length<ff0||K===void 0||X>this._interactionIdToRouteNameMapping[K].duration){let H=this._latestRoute.name,z=this._latestRoute.context;if(H&&z){if(K&&Object.keys(this._interactionIdToRouteNameMapping).length>=ff0)delete this._interactionIdToRouteNameMapping[K];this._interactionIdToRouteNameMapping[W]={routeName:H,duration:X,parentContext:z,user:I,activeTransaction:G,replayId:Z,startTime:V}}}}})};bf0.addPerformanceInstrumentationHandler("event",A),bf0.addPerformanceInstrumentationHandler("first-input",A)}}function Ld1(A){let B=IO.getDomElement(`meta[name=${A}]`);return B?B.getAttribute("content"):void 0}function LW9(A){let B=A.attributes&&A.attributes[aU.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE],Q=A.data&&A.data[aU.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE],D=A.metadata&&A.metadata.source;return B||Q||D}function MW9(A){return"duration"in A}mf0.BROWSER_TRACING_INTEGRATION_ID=gf0;mf0.BrowserTracing=uf0;mf0.getMetaContent=Ld1});
var dx0=E((mx0)=>{Object.defineProperty(mx0,"__esModule",{value:!0});var ux0=_W1();function Y79(A,B,Q=[B],D="npm"){let Z=A._metadata||{};if(!Z.sdk)Z.sdk={name:`sentry.javascript.${B}`,packages:Q.map((G)=>({name:`${D}:@sentry/${G}`,version:ux0.SDK_VERSION})),version:ux0.SDK_VERSION};A._metadata=Z}mx0.applySdkMetadata=Y79});
var eA1=E((kj0)=>{Object.defineProperty(kj0,"__esModule",{value:!0});var BoB=bH(),wu1=oA1(),QoB=RW();function DoB(){let A=QoB.GLOBAL_OBJ,B=A.crypto||A.msCrypto,Q=()=>Math.random()*16;try{if(B&&B.randomUUID)return B.randomUUID().replace(/-/g,"");if(B&&B.getRandomValues)Q=()=>{let D=new Uint8Array(1);return B.getRandomValues(D),D[0]}}catch(D){}return([1e7]+1000+4000+8000+100000000000).replace(/[018]/g,(D)=>(D^(Q()&15)>>D/4).toString(16))}function jj0(A){return A.exception&&A.exception.values?A.exception.values[0]:void 0}function ZoB(A){let{message:B,event_id:Q}=A;if(B)return B;let D=jj0(A);if(D){if(D.type&&D.value)return`${D.type}: ${D.value}`;return D.type||D.value||Q||"<unknown>"}return Q||"<unknown>"}function GoB(A,B,Q){let D=A.exception=A.exception||{},Z=D.values=D.values||[],G=Z[0]=Z[0]||{};if(!G.value)G.value=B||"";if(!G.type)G.type=Q||"Error"}function FoB(A,B){let Q=jj0(A);if(!Q)return;let D={type:"generic",handled:!0},Z=Q.mechanism;if(Q.mechanism={...D,...Z,...B},B&&"data"in B){let G={...Z&&Z.data,...B.data};Q.mechanism.data=G}}var IoB=/^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/;function YoB(A){let B=A.match(IoB)||[],Q=parseInt(B[1],10),D=parseInt(B[2],10),Z=parseInt(B[3],10);return{buildmetadata:B[5],major:isNaN(Q)?void 0:Q,minor:isNaN(D)?void 0:D,patch:isNaN(Z)?void 0:Z,prerelease:B[4]}}function WoB(A,B,Q=5){if(B.lineno===void 0)return;let D=A.length,Z=Math.max(Math.min(D-1,B.lineno-1),0);B.pre_context=A.slice(Math.max(0,Z-Q),Z).map((G)=>wu1.snipLine(G,0)),B.context_line=wu1.snipLine(A[Math.min(D-1,Z)],B.colno||0),B.post_context=A.slice(Math.min(Z+1,D),Z+1+Q).map((G)=>wu1.snipLine(G,0))}function JoB(A){if(A&&A.__sentry_captured__)return!0;try{BoB.addNonEnumerableProperty(A,"__sentry_captured__",!0)}catch(B){}return!1}function XoB(A){return Array.isArray(A)?A:[A]}kj0.addContextToFrame=WoB;kj0.addExceptionMechanism=FoB;kj0.addExceptionTypeValue=GoB;kj0.arrayify=XoB;kj0.checkOrSetAlreadyCaught=JoB;kj0.getEventDescription=ZoB;kj0.parseSemver=YoB;kj0.uuid4=DoB});
var e_0=E((t_0)=>{Object.defineProperty(t_0,"__esModule",{value:!0});var o_0=$A(),_59=q21();function x59(A,B,Q,D){let Z={sent_at:new Date().toISOString()};if(Q&&Q.sdk)Z.sdk={name:Q.sdk.name,version:Q.sdk.version};if(!!D&&B)Z.dsn=o_0.dsnToString(B);let G=v59(A);return o_0.createEnvelope(Z,[G])}function v59(A){let B=_59.serializeMetricBuckets(A);return[{type:"statsd",length:B.length},B]}t_0.createMetricEnvelope=x59});
var ed1=E((Gd0)=>{Object.defineProperty(Gd0,"__esModule",{value:!0});var dB=OQ(),bE9=Fh0(),fE9=Td1(),hE9=jd1(),p21=cd1(),td1=$A(),gE9=sg0(),Bd0=dd1(),uE9=Du0(),mE9=Vu0(),dE9=qu0(),cE9=Lu0(),dj=cm0(),lE9=kJ1(),pE9=dJ1(),iE9=lJ1(),nE9=gJ1(),aE9=xJ1(),sE9=yJ1(),rE9=hJ1(),oE9=pJ1(),tE9=rJ1(),Qd0=ad1(),Dd0=nJ1(),Zd0=vJ1(),eE9=nd1(),AU9=sm0(),BU9=tm0(),QU9=Ad0(),DU9=Bd0.createGetModuleFromFilename(),ZU9={...dB.Integrations,...dE9,...cE9},GU9={instrumentCron:AU9.instrumentCron,instrumentNodeCron:BU9.instrumentNodeCron,instrumentNodeSchedule:QU9.instrumentNodeSchedule};Gd0.Hub=dB.Hub;Gd0.SDK_VERSION=dB.SDK_VERSION;Gd0.SEMANTIC_ATTRIBUTE_SENTRY_OP=dB.SEMANTIC_ATTRIBUTE_SENTRY_OP;Gd0.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN=dB.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN;Gd0.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE=dB.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE;Gd0.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE=dB.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE;Gd0.Scope=dB.Scope;Gd0.addBreadcrumb=dB.addBreadcrumb;Gd0.addEventProcessor=dB.addEventProcessor;Gd0.addGlobalEventProcessor=dB.addGlobalEventProcessor;Gd0.addIntegration=dB.addIntegration;Gd0.captureCheckIn=dB.captureCheckIn;Gd0.captureEvent=dB.captureEvent;Gd0.captureException=dB.captureException;Gd0.captureMessage=dB.captureMessage;Gd0.captureSession=dB.captureSession;Gd0.close=dB.close;Gd0.configureScope=dB.configureScope;Gd0.continueTrace=dB.continueTrace;Gd0.createTransport=dB.createTransport;Gd0.endSession=dB.endSession;Gd0.extractTraceparentData=dB.extractTraceparentData;Gd0.flush=dB.flush;Gd0.functionToStringIntegration=dB.functionToStringIntegration;Gd0.getActiveSpan=dB.getActiveSpan;Gd0.getActiveTransaction=dB.getActiveTransaction;Gd0.getClient=dB.getClient;Gd0.getCurrentHub=dB.getCurrentHub;Gd0.getCurrentScope=dB.getCurrentScope;Gd0.getGlobalScope=dB.getGlobalScope;Gd0.getHubFromCarrier=dB.getHubFromCarrier;Gd0.getIsolationScope=dB.getIsolationScope;Gd0.getSpanStatusFromHttpCode=dB.getSpanStatusFromHttpCode;Gd0.inboundFiltersIntegration=dB.inboundFiltersIntegration;Gd0.isInitialized=dB.isInitialized;Gd0.lastEventId=dB.lastEventId;Gd0.linkedErrorsIntegration=dB.linkedErrorsIntegration;Gd0.makeMain=dB.makeMain;Gd0.metrics=dB.metrics;Gd0.parameterize=dB.parameterize;Gd0.requestDataIntegration=dB.requestDataIntegration;Gd0.runWithAsyncContext=dB.runWithAsyncContext;Gd0.setContext=dB.setContext;Gd0.setCurrentClient=dB.setCurrentClient;Gd0.setExtra=dB.setExtra;Gd0.setExtras=dB.setExtras;Gd0.setHttpStatus=dB.setHttpStatus;Gd0.setMeasurement=dB.setMeasurement;Gd0.setTag=dB.setTag;Gd0.setTags=dB.setTags;Gd0.setUser=dB.setUser;Gd0.spanStatusfromHttpCode=dB.spanStatusfromHttpCode;Gd0.startActiveSpan=dB.startActiveSpan;Gd0.startInactiveSpan=dB.startInactiveSpan;Gd0.startSession=dB.startSession;Gd0.startSpan=dB.startSpan;Gd0.startSpanManual=dB.startSpanManual;Gd0.startTransaction=dB.startTransaction;Gd0.trace=dB.trace;Gd0.withActiveSpan=dB.withActiveSpan;Gd0.withIsolationScope=dB.withIsolationScope;Gd0.withMonitor=dB.withMonitor;Gd0.withScope=dB.withScope;Gd0.autoDiscoverNodePerformanceMonitoringIntegrations=bE9.autoDiscoverNodePerformanceMonitoringIntegrations;Gd0.NodeClient=fE9.NodeClient;Gd0.makeNodeTransport=hE9.makeNodeTransport;Gd0.defaultIntegrations=p21.defaultIntegrations;Gd0.defaultStackParser=p21.defaultStackParser;Gd0.getDefaultIntegrations=p21.getDefaultIntegrations;Gd0.getSentryRelease=p21.getSentryRelease;Gd0.init=p21.init;Gd0.DEFAULT_USER_INCLUDES=td1.DEFAULT_USER_INCLUDES;Gd0.addRequestDataToEvent=td1.addRequestDataToEvent;Gd0.extractRequestData=td1.extractRequestData;Gd0.deepReadDirSync=gE9.deepReadDirSync;Gd0.createGetModuleFromFilename=Bd0.createGetModuleFromFilename;Gd0.enableAnrDetection=uE9.enableAnrDetection;Gd0.Handlers=mE9;Gd0.captureConsoleIntegration=dj.captureConsoleIntegration;Gd0.debugIntegration=dj.debugIntegration;Gd0.dedupeIntegration=dj.dedupeIntegration;Gd0.extraErrorDataIntegration=dj.extraErrorDataIntegration;Gd0.httpClientIntegration=dj.httpClientIntegration;Gd0.reportingObserverIntegration=dj.reportingObserverIntegration;Gd0.rewriteFramesIntegration=dj.rewriteFramesIntegration;Gd0.sessionTimingIntegration=dj.sessionTimingIntegration;Gd0.consoleIntegration=lE9.consoleIntegration;Gd0.onUncaughtExceptionIntegration=pE9.onUncaughtExceptionIntegration;Gd0.onUnhandledRejectionIntegration=iE9.onUnhandledRejectionIntegration;Gd0.modulesIntegration=nE9.modulesIntegration;Gd0.contextLinesIntegration=aE9.contextLinesIntegration;Gd0.nodeContextIntegration=sE9.nodeContextIntegration;Gd0.localVariablesIntegration=rE9.localVariablesIntegration;Gd0.spotlightIntegration=oE9.spotlightIntegration;Gd0.anrIntegration=tE9.anrIntegration;Gd0.hapiErrorPlugin=Qd0.hapiErrorPlugin;Gd0.hapiIntegration=Qd0.hapiIntegration;Gd0.Undici=Dd0.Undici;Gd0.nativeNodeFetchintegration=Dd0.nativeNodeFetchintegration;Gd0.Http=Zd0.Http;Gd0.httpIntegration=Zd0.httpIntegration;Gd0.trpcMiddleware=eE9.trpcMiddleware;Gd0.Integrations=ZU9;Gd0.cron=GU9;Gd0.getModuleFromFilename=DU9});
var ef0=E((tf0,f21)=>{Object.defineProperty(tf0,"__esModule",{value:!0});var of0=OQ(),$l=$A();function uW9(){let A=of0.getMainCarrier();if(!A.__SENTRY__)return;let B={mongodb(){return new($l.dynamicRequire(f21,"./node/integrations/mongo")).Mongo},mongoose(){return new($l.dynamicRequire(f21,"./node/integrations/mongo")).Mongo},mysql(){return new($l.dynamicRequire(f21,"./node/integrations/mysql")).Mysql},pg(){return new($l.dynamicRequire(f21,"./node/integrations/postgres")).Postgres}},Q=Object.keys(B).filter((D)=>!!$l.loadModule(D)).map((D)=>{try{return B[D]()}catch(Z){return}}).filter((D)=>D);if(Q.length>0)A.__SENTRY__.integrations=[...A.__SENTRY__.integrations||[],...Q]}function mW9(){if(of0.addTracingExtensions(),$l.isNodeEnv())uW9()}tf0.addExtensionMethods=mW9});
var eu1=E((sk0)=>{Object.defineProperty(sk0,"__esModule",{value:!0});var _19=Vu1(),x19=D21(),nk0=bH();function v19(A,B=[]){return[A,B]}function b19(A,B){let[Q,D]=A;return[Q,[...D,B]]}function ak0(A,B){let Q=A[1];for(let D of Q){let Z=D[0].type;if(B(D,Z))return!0}return!1}function f19(A,B){return ak0(A,(Q,D)=>B.includes(D))}function tu1(A,B){return(B||new TextEncoder).encode(A)}function h19(A,B){let[Q,D]=A,Z=JSON.stringify(Q);function G(F){if(typeof Z==="string")Z=typeof F==="string"?Z+F:[tu1(Z,B),F];else Z.push(typeof F==="string"?tu1(F,B):F)}for(let F of D){let[I,Y]=F;if(G(`
${JSON.stringify(I)}
`),typeof Y==="string"||Y instanceof Uint8Array)G(Y);else{let W;try{W=JSON.stringify(Y)}catch(J){W=JSON.stringify(x19.normalize(Y))}G(W)}}return typeof Z==="string"?Z:g19(Z)}function g19(A){let B=A.reduce((Z,G)=>Z+G.length,0),Q=new Uint8Array(B),D=0;for(let Z of A)Q.set(Z,D),D+=Z.length;return Q}function u19(A,B,Q){let D=typeof A==="string"?B.encode(A):A;function Z(Y){let W=D.subarray(0,Y);return D=D.subarray(Y+1),W}function G(){let Y=D.indexOf(10);if(Y<0)Y=D.length;return JSON.parse(Q.decode(Z(Y)))}let F=G(),I=[];while(D.length){let Y=G(),W=typeof Y.length==="number"?Y.length:void 0;I.push([Y,W?Z(W):G()])}return[F,I]}function m19(A,B){let Q=typeof A.data==="string"?tu1(A.data,B):A.data;return[nk0.dropUndefinedKeys({type:"attachment",length:Q.length,filename:A.filename,content_type:A.contentType,attachment_type:A.attachmentType}),Q]}var d19={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket"};function c19(A){return d19[A]}function l19(A){if(!A||!A.sdk)return;let{name:B,version:Q}=A.sdk;return{name:B,version:Q}}function p19(A,B,Q,D){let Z=A.sdkProcessingMetadata&&A.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:A.event_id,sent_at:new Date().toISOString(),...B&&{sdk:B},...!!Q&&D&&{dsn:_19.dsnToString(D)},...Z&&{trace:nk0.dropUndefinedKeys({...Z})}}}sk0.addItemToEnvelope=b19;sk0.createAttachmentEnvelopeItem=m19;sk0.createEnvelope=v19;sk0.createEventEnvelopeHeaders=p19;sk0.envelopeContainsItemType=f19;sk0.envelopeItemTypeToDataCategory=c19;sk0.forEachEnvelopeItem=ak0;sk0.getSdkMetadataForEnvelopeHeader=l19;sk0.parseEnvelope=u19;sk0.serializeEnvelope=h19});
var ex0=E((tx0)=>{Object.defineProperty(tx0,"__esModule",{value:!0});var z79=$A(),sx0=BO(),ax0=nx0(),rx0="ModuleMetadata",E79=()=>{return{name:rx0,setupOnce(){},setup(A){if(typeof A.on!=="function")return;A.on("beforeEnvelope",(B)=>{z79.forEachEnvelopeItem(B,(Q,D)=>{if(D==="event"){let Z=Array.isArray(Q)?Q[1]:void 0;if(Z)ax0.stripMetadataFromStackFrames(Z),Q[1]=Z}})})},processEvent(A,B,Q){let D=Q.getOptions().stackParser;return ax0.addMetadataToStackFrames(D,A),A}}},ox0=sx0.defineIntegration(E79),U79=sx0.convertIntegrationFnToClass(rx0,ox0);tx0.ModuleMetadata=U79;tx0.moduleMetadataIntegration=ox0});
var fW1=E((K_0)=>{Object.defineProperty(K_0,"__esModule",{value:!0});var Lm1=$A(),F69=qG(),I69=bW1(),C_0=!1;function Y69(){if(C_0)return;C_0=!0,Lm1.addGlobalErrorInstrumentationHandler(Mm1),Lm1.addGlobalUnhandledRejectionInstrumentationHandler(Mm1)}function Mm1(){let A=I69.getActiveTransaction();if(A)F69.DEBUG_BUILD&&Lm1.logger.log("[Tracing] Transaction: internal_error -> Global error occured"),A.setStatus("internal_error")}Mm1.tag="sentry_tracingErrorCallback";K_0.registerErrorInstrumentation=Y69});
var fu1=E((nj0)=>{Object.defineProperty(nj0,"__esModule",{value:!0});var zW1=vH(),HW1=bH(),VtB=RW(),EW1=rR(),CtB=VtB.GLOBAL_OBJ,Q21="__sentry_xhr_v3__";function KtB(A){EW1.addHandler("xhr",A),EW1.maybeInstrument("xhr",ij0)}function ij0(){if(!CtB.XMLHttpRequest)return;let A=XMLHttpRequest.prototype;HW1.fill(A,"open",function(B){return function(...Q){let D=Date.now(),Z=zW1.isString(Q[0])?Q[0].toUpperCase():void 0,G=HtB(Q[1]);if(!Z||!G)return B.apply(this,Q);if(this[Q21]={method:Z,url:G,request_headers:{}},Z==="POST"&&G.match(/sentry_key/))this.__sentry_own_request__=!0;let F=()=>{let I=this[Q21];if(!I)return;if(this.readyState===4){try{I.status_code=this.status}catch(W){}let Y={args:[Z,G],endTimestamp:Date.now(),startTimestamp:D,xhr:this};EW1.triggerHandlers("xhr",Y)}};if("onreadystatechange"in this&&typeof this.onreadystatechange==="function")HW1.fill(this,"onreadystatechange",function(I){return function(...Y){return F(),I.apply(this,Y)}});else this.addEventListener("readystatechange",F);return HW1.fill(this,"setRequestHeader",function(I){return function(...Y){let[W,J]=Y,X=this[Q21];if(X&&zW1.isString(W)&&zW1.isString(J))X.request_headers[W.toLowerCase()]=J;return I.apply(this,Y)}}),B.apply(this,Q)}}),HW1.fill(A,"send",function(B){return function(...Q){let D=this[Q21];if(!D)return B.apply(this,Q);if(Q[0]!==void 0)D.body=Q[0];let Z={args:[D.method,D.url],startTimestamp:Date.now(),xhr:this};return EW1.triggerHandlers("xhr",Z),B.apply(this,Q)}})}function HtB(A){if(zW1.isString(A))return A;try{return A.toString()}catch(B){}return}nj0.SENTRY_XHR_DATA_KEY=Q21;nj0.addXhrInstrumentationHandler=KtB;nj0.instrumentXHR=ij0});
var fx0=E((bx0)=>{Object.defineProperty(bx0,"__esModule",{value:!0});function B79(A,B){let Q=B&&Z79(B)?B.getClient():B,D=Q&&Q.getDsn(),Z=Q&&Q.getOptions().tunnel;return D79(A,D)||Q79(A,Z)}function Q79(A,B){if(!B)return!1;return vx0(A)===vx0(B)}function D79(A,B){return B?A.includes(B.host):!1}function vx0(A){return A[A.length-1]==="/"?A.slice(0,-1):A}function Z79(A){return A.getClient!==void 0}bx0.isSentryRequestUrl=B79});
var gJ1=E((Ng0)=>{Object.defineProperty(Ng0,"__esModule",{value:!0});var Eg0=J1("fs"),Ug0=J1("path"),wg0=OQ(),gd1,$g0="Modules";function nV9(){try{return J1.cache?Object.keys(J1.cache):[]}catch(A){return[]}}function aV9(){let A=J1.main&&J1.main.paths||[],B=nV9(),Q={},D={};return B.forEach((Z)=>{let G=Z,F=()=>{let I=G;if(G=Ug0.dirname(I),!G||I===G||D[I])return;if(A.indexOf(G)<0)return F();let Y=Ug0.join(I,"package.json");if(D[I]=!0,!Eg0.existsSync(Y))return F();try{let W=JSON.parse(Eg0.readFileSync(Y,"utf8"));Q[W.name]=W.version}catch(W){}};F()}),Q}function sV9(){if(!gd1)gd1=aV9();return gd1}var rV9=()=>{return{name:$g0,setupOnce(){},processEvent(A){return A.modules={...A.modules,...sV9()},A}}},qg0=wg0.defineIntegration(rV9),oV9=wg0.convertIntegrationFnToClass($g0,qg0);Ng0.Modules=oV9;Ng0.modulesIntegration=qg0});
var gm1=E((Fx0)=>{Object.defineProperty(Fx0,"__esModule",{value:!0});var k8=$A(),f59=rW1(),iq=qG(),Ax0=xm1(),h59=hH(),g59=lq(),tW1=BO(),u59=e_0(),Bx0=Bl(),m59=Qf(),d59=PW1(),Qx0="Not capturing exception because it's already been captured.";class Dx0{constructor(A){if(this._options=A,this._integrations={},this._integrationsInitialized=!1,this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],A.dsn)this._dsn=k8.makeDsn(A.dsn);else iq.DEBUG_BUILD&&k8.logger.warn("No DSN provided, client will not send events.");if(this._dsn){let B=f59.getEnvelopeEndpointWithUrlEncodedAuth(this._dsn,A);this._transport=A.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...A.transportOptions,url:B})}}captureException(A,B,Q){if(k8.checkOrSetAlreadyCaught(A)){iq.DEBUG_BUILD&&k8.logger.log(Qx0);return}let D=B&&B.event_id;return this._process(this.eventFromException(A,B).then((Z)=>this._captureEvent(Z,B,Q)).then((Z)=>{D=Z})),D}captureMessage(A,B,Q,D){let Z=Q&&Q.event_id,G=k8.isParameterizedString(A)?A:String(A),F=k8.isPrimitive(A)?this.eventFromMessage(G,B,Q):this.eventFromException(A,Q);return this._process(F.then((I)=>this._captureEvent(I,Q,D)).then((I)=>{Z=I})),Z}captureEvent(A,B,Q){if(B&&B.originalException&&k8.checkOrSetAlreadyCaught(B.originalException)){iq.DEBUG_BUILD&&k8.logger.log(Qx0);return}let D=B&&B.event_id,G=(A.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(A,B,G||Q).then((F)=>{D=F})),D}captureSession(A){if(typeof A.release!=="string")iq.DEBUG_BUILD&&k8.logger.warn("Discarded session because of missing or non-string release");else this.sendSession(A),Bx0.updateSession(A,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(A){let B=this._transport;if(B){if(this.metricsAggregator)this.metricsAggregator.flush();return this._isClientDoneProcessing(A).then((Q)=>{return B.flush(A).then((D)=>Q&&D)})}else return k8.resolvedSyncPromise(!0)}close(A){return this.flush(A).then((B)=>{if(this.getOptions().enabled=!1,this.metricsAggregator)this.metricsAggregator.close();return B})}getEventProcessors(){return this._eventProcessors}addEventProcessor(A){this._eventProcessors.push(A)}setupIntegrations(A){if(A&&!this._integrationsInitialized||this._isEnabled()&&!this._integrationsInitialized)this._setupIntegrations()}init(){if(this._isEnabled())this._setupIntegrations()}getIntegrationById(A){return this.getIntegrationByName(A)}getIntegrationByName(A){return this._integrations[A]}getIntegration(A){try{return this._integrations[A.id]||null}catch(B){return iq.DEBUG_BUILD&&k8.logger.warn(`Cannot retrieve integration ${A.id} from the current Client`),null}}addIntegration(A){let B=this._integrations[A.name];if(tW1.setupIntegration(this,A,this._integrations),!B)tW1.afterSetupIntegrations(this,[A])}sendEvent(A,B={}){this.emit("beforeSendEvent",A,B);let Q=Ax0.createEventEnvelope(A,this._dsn,this._options._metadata,this._options.tunnel);for(let Z of B.attachments||[])Q=k8.addItemToEnvelope(Q,k8.createAttachmentEnvelopeItem(Z,this._options.transportOptions&&this._options.transportOptions.textEncoder));let D=this._sendEnvelope(Q);if(D)D.then((Z)=>this.emit("afterSendEvent",A,Z),null)}sendSession(A){let B=Ax0.createSessionEnvelope(A,this._dsn,this._options._metadata,this._options.tunnel);this._sendEnvelope(B)}recordDroppedEvent(A,B,Q){if(this._options.sendClientReports){let D=typeof Q==="number"?Q:1,Z=`${A}:${B}`;iq.DEBUG_BUILD&&k8.logger.log(`Recording outcome: "${Z}"${D>1?` (${D} times)`:""}`),this._outcomes[Z]=(this._outcomes[Z]||0)+D}}captureAggregateMetrics(A){iq.DEBUG_BUILD&&k8.logger.log(`Flushing aggregated metrics, number of metrics: ${A.length}`);let B=u59.createMetricEnvelope(A,this._dsn,this._options._metadata,this._options.tunnel);this._sendEnvelope(B)}on(A,B){if(!this._hooks[A])this._hooks[A]=[];this._hooks[A].push(B)}emit(A,...B){if(this._hooks[A])this._hooks[A].forEach((Q)=>Q(...B))}_setupIntegrations(){let{integrations:A}=this._options;this._integrations=tW1.setupIntegrations(this,A),tW1.afterSetupIntegrations(this,A),this._integrationsInitialized=!0}_updateSessionFromEvent(A,B){let Q=!1,D=!1,Z=B.exception&&B.exception.values;if(Z){D=!0;for(let I of Z){let Y=I.mechanism;if(Y&&Y.handled===!1){Q=!0;break}}}let G=A.status==="ok";if(G&&A.errors===0||G&&Q)Bx0.updateSession(A,{...Q&&{status:"crashed"},errors:A.errors||Number(D||Q)}),this.captureSession(A)}_isClientDoneProcessing(A){return new k8.SyncPromise((B)=>{let Q=0,D=1,Z=setInterval(()=>{if(this._numProcessing==0)clearInterval(Z),B(!0);else if(Q+=D,A&&Q>=A)clearInterval(Z),B(!1)},D)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(A,B,Q,D=g59.getIsolationScope()){let Z=this.getOptions(),G=Object.keys(this._integrations);if(!B.integrations&&G.length>0)B.integrations=G;return this.emit("preprocessEvent",A,B),d59.prepareEvent(Z,A,B,Q,this,D).then((F)=>{if(F===null)return F;let I={...D.getPropagationContext(),...Q?Q.getPropagationContext():void 0};if(!(F.contexts&&F.contexts.trace)&&I){let{traceId:W,spanId:J,parentSpanId:X,dsc:V}=I;F.contexts={trace:{trace_id:W,span_id:J,parent_span_id:X},...F.contexts};let C=V?V:m59.getDynamicSamplingContextFromClient(W,this,Q);F.sdkProcessingMetadata={dynamicSamplingContext:C,...F.sdkProcessingMetadata}}return F})}_captureEvent(A,B={},Q){return this._processEvent(A,B,Q).then((D)=>{return D.event_id},(D)=>{if(iq.DEBUG_BUILD){let Z=D;if(Z.logLevel==="log")k8.logger.log(Z.message);else k8.logger.warn(Z)}return})}_processEvent(A,B,Q){let D=this.getOptions(),{sampleRate:Z}=D,G=Gx0(A),F=Zx0(A),I=A.type||"error",Y=`before send for type \`${I}\``;if(F&&typeof Z==="number"&&Math.random()>Z)return this.recordDroppedEvent("sample_rate","error",A),k8.rejectedSyncPromise(new k8.SentryError(`Discarding event because it's not included in the random sample (sampling rate = ${Z})`,"log"));let W=I==="replay_event"?"replay":I,X=(A.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(A,B,Q,X).then((V)=>{if(V===null)throw this.recordDroppedEvent("event_processor",W,A),new k8.SentryError("An event processor returned `null`, will not send event.","log");if(B.data&&B.data.__sentry__===!0)return V;let K=l59(D,V,B);return c59(K,Y)}).then((V)=>{if(V===null){if(this.recordDroppedEvent("before_send",W,A),G){let z=1+(A.spans||[]).length;this.recordDroppedEvent("before_send","span",z)}throw new k8.SentryError(`${Y} returned \`null\`, will not send event.`,"log")}let C=Q&&Q.getSession();if(!G&&C)this._updateSessionFromEvent(C,V);if(G){let H=V.sdkProcessingMetadata&&V.sdkProcessingMetadata.spanCountBeforeProcessing||0,z=V.spans?V.spans.length:0,$=H-z;if($>0)this.recordDroppedEvent("before_send","span",$)}let K=V.transaction_info;if(G&&K&&V.transaction!==A.transaction)V.transaction_info={...K,source:"custom"};return this.sendEvent(V,B),V}).then(null,(V)=>{if(V instanceof k8.SentryError)throw V;throw this.captureException(V,{data:{__sentry__:!0},originalException:V}),new k8.SentryError(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${V}`)})}_process(A){this._numProcessing++,A.then((B)=>{return this._numProcessing--,B},(B)=>{return this._numProcessing--,B})}_sendEnvelope(A){if(this.emit("beforeEnvelope",A),this._isEnabled()&&this._transport)return this._transport.send(A).then(null,(B)=>{iq.DEBUG_BUILD&&k8.logger.error("Error while sending event:",B)});else iq.DEBUG_BUILD&&k8.logger.error("Transport disabled")}_clearOutcomes(){let A=this._outcomes;return this._outcomes={},Object.keys(A).map((B)=>{let[Q,D]=B.split(":");return{reason:Q,category:D,quantity:A[B]}})}}function c59(A,B){let Q=`${B} must return \`null\` or a valid event.`;if(k8.isThenable(A))return A.then((D)=>{if(!k8.isPlainObject(D)&&D!==null)throw new k8.SentryError(Q);return D},(D)=>{throw new k8.SentryError(`${B} rejected with ${D}`)});else if(!k8.isPlainObject(A)&&A!==null)throw new k8.SentryError(Q);return A}function l59(A,B,Q){let{beforeSend:D,beforeSendTransaction:Z}=A;if(Zx0(B)&&D)return D(B,Q);if(Gx0(B)&&Z){if(B.spans){let G=B.spans.length;B.sdkProcessingMetadata={...B.sdkProcessingMetadata,spanCountBeforeProcessing:G}}return Z(B,Q)}return B}function Zx0(A){return A.type===void 0}function Gx0(A){return A.type==="transaction"}function p59(A){let B=h59.getClient();if(!B||!B.addEventProcessor)return;B.addEventProcessor(A)}Fx0.BaseClient=Dx0;Fx0.addEventProcessor=p59});
var gu1=E((Qk0)=>{Object.defineProperty(Qk0,"__esModule",{value:!0});function ktB(){return typeof __SENTRY_BROWSER_BUNDLE__!=="undefined"&&!!__SENTRY_BROWSER_BUNDLE__}function ytB(){return"npm"}Qk0.getSDKSource=ytB;Qk0.isBrowserBundle=ktB});
var gx0=E((hx0)=>{Object.defineProperty(hx0,"__esModule",{value:!0});function F79(A,...B){let Q=new String(String.raw(A,...B));return Q.__sentry_template_string__=A.join("\x00").replace(/%/g,"%%").replace(/\0/g,"%s"),Q.__sentry_template_values__=B,Q}hx0.parameterize=F79});
var hH=E((sy0)=>{Object.defineProperty(sy0,"__esModule",{value:!0});var eR=$A(),XQ9=Al(),kW1=qG(),kZ=lq(),zm1=Bl(),VQ9=PW1();function CQ9(A,B){return kZ.getCurrentHub().captureException(A,VQ9.parseEventHintOrCaptureContext(B))}function KQ9(A,B){let Q=typeof B==="string"?B:void 0,D=typeof B!=="string"?{captureContext:B}:void 0;return kZ.getCurrentHub().captureMessage(A,Q,D)}function HQ9(A,B){return kZ.getCurrentHub().captureEvent(A,B)}function zQ9(A){kZ.getCurrentHub().configureScope(A)}function EQ9(A,B){kZ.getCurrentHub().addBreadcrumb(A,B)}function UQ9(A,B){kZ.getCurrentHub().setContext(A,B)}function wQ9(A){kZ.getCurrentHub().setExtras(A)}function $Q9(A,B){kZ.getCurrentHub().setExtra(A,B)}function qQ9(A){kZ.getCurrentHub().setTags(A)}function NQ9(A,B){kZ.getCurrentHub().setTag(A,B)}function LQ9(A){kZ.getCurrentHub().setUser(A)}function ny0(...A){let B=kZ.getCurrentHub();if(A.length===2){let[Q,D]=A;if(!Q)return B.withScope(D);return B.withScope(()=>{return B.getStackTop().scope=Q,D(Q)})}return B.withScope(A[0])}function MQ9(A){return kZ.runWithAsyncContext(()=>{return A(kZ.getIsolationScope())})}function RQ9(A,B){return ny0((Q)=>{return Q.setSpan(A),B(Q)})}function OQ9(A,B){return kZ.getCurrentHub().startTransaction({...A},B)}function Em1(A,B){let Q=V21(),D=Bf();if(!D)kW1.DEBUG_BUILD&&eR.logger.warn("Cannot capture check-in. No client defined.");else if(!D.captureCheckIn)kW1.DEBUG_BUILD&&eR.logger.warn("Cannot capture check-in. Client does not support sending check-ins.");else return D.captureCheckIn(A,B,Q);return eR.uuid4()}function TQ9(A,B,Q){let D=Em1({monitorSlug:A,status:"in_progress"},Q),Z=eR.timestampInSeconds();function G(I){Em1({monitorSlug:A,status:I,checkInId:D,duration:eR.timestampInSeconds()-Z})}let F;try{F=B()}catch(I){throw G("error"),I}if(eR.isThenable(F))Promise.resolve(F).then(()=>{G("ok")},()=>{G("error")});else G("ok");return F}async function PQ9(A){let B=Bf();if(B)return B.flush(A);return kW1.DEBUG_BUILD&&eR.logger.warn("Cannot flush events. No client defined."),Promise.resolve(!1)}async function SQ9(A){let B=Bf();if(B)return B.close(A);return kW1.DEBUG_BUILD&&eR.logger.warn("Cannot flush events and disable SDK. No client defined."),Promise.resolve(!1)}function jQ9(){return kZ.getCurrentHub().lastEventId()}function Bf(){return kZ.getCurrentHub().getClient()}function kQ9(){return!!Bf()}function V21(){return kZ.getCurrentHub().getScope()}function yQ9(A){let B=Bf(),Q=kZ.getIsolationScope(),D=V21(),{release:Z,environment:G=XQ9.DEFAULT_ENVIRONMENT}=B&&B.getOptions()||{},{userAgent:F}=eR.GLOBAL_OBJ.navigator||{},I=zm1.makeSession({release:Z,environment:G,user:D.getUser()||Q.getUser(),...F&&{userAgent:F},...A}),Y=Q.getSession();if(Y&&Y.status==="ok")zm1.updateSession(Y,{status:"exited"});return Um1(),Q.setSession(I),D.setSession(I),I}function Um1(){let A=kZ.getIsolationScope(),B=V21(),Q=B.getSession()||A.getSession();if(Q)zm1.closeSession(Q);ay0(),A.setSession(),B.setSession()}function ay0(){let A=kZ.getIsolationScope(),B=V21(),Q=Bf(),D=B.getSession()||A.getSession();if(D&&Q&&Q.captureSession)Q.captureSession(D)}function _Q9(A=!1){if(A){Um1();return}ay0()}sy0.addBreadcrumb=EQ9;sy0.captureCheckIn=Em1;sy0.captureEvent=HQ9;sy0.captureException=CQ9;sy0.captureMessage=KQ9;sy0.captureSession=_Q9;sy0.close=SQ9;sy0.configureScope=zQ9;sy0.endSession=Um1;sy0.flush=PQ9;sy0.getClient=Bf;sy0.getCurrentScope=V21;sy0.isInitialized=kQ9;sy0.lastEventId=jQ9;sy0.setContext=UQ9;sy0.setExtra=$Q9;sy0.setExtras=wQ9;sy0.setTag=NQ9;sy0.setTags=qQ9;sy0.setUser=LQ9;sy0.startSession=yQ9;sy0.startTransaction=OQ9;sy0.withActiveSpan=RQ9;sy0.withIsolationScope=MQ9;sy0.withMonitor=TQ9;sy0.withScope=ny0});
var hJ1=E((zg0)=>{Object.defineProperty(zg0,"__esModule",{value:!0});var Hg0=Kg0(),cV9=Hg0.LocalVariablesSync,lV9=Hg0.localVariablesSyncIntegration;zg0.LocalVariables=cV9;zg0.localVariablesIntegration=lV9});
var hW1=E((E_0)=>{Object.defineProperty(E_0,"__esModule",{value:!0});var w69=hH();function $69(A){if(typeof __SENTRY_TRACING__==="boolean"&&!__SENTRY_TRACING__)return!1;let B=w69.getClient(),Q=A||B&&B.getOptions();return!!Q&&(Q.enableTracing||("tracesSampleRate"in Q)||("tracesSampler"in Q))}E_0.hasTracingEnabled=$69});
var iW1=E((x_0)=>{Object.defineProperty(x_0,"__esModule",{value:!0});var Yl=$A(),lW1=qG(),G89=lq(),F89=U21(),$21=w21(),pW1=aX(),k_0=Qf(),y_0=cW1(),I89=dW1();class __0 extends y_0.Span{constructor(A,B){super(A);this._contexts={},this._hub=B||G89.getCurrentHub(),this._name=A.name||"",this._metadata={...A.metadata},this._trimEnd=A.trimEnd,this.transaction=this;let Q=this._metadata.dynamicSamplingContext;if(Q)this._frozenDynamicSamplingContext={...Q}}get name(){return this._name}set name(A){this.setName(A)}get metadata(){return{source:"custom",spanMetadata:{},...this._metadata,...this._attributes[$21.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]&&{source:this._attributes[$21.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]},...this._attributes[$21.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE]&&{sampleRate:this._attributes[$21.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE]}}}set metadata(A){this._metadata=A}setName(A,B="custom"){this._name=A,this.setAttribute($21.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,B)}updateName(A){return this._name=A,this}initSpanRecorder(A=1000){if(!this.spanRecorder)this.spanRecorder=new y_0.SpanRecorder(A);this.spanRecorder.add(this)}setContext(A,B){if(B===null)delete this._contexts[A];else this._contexts[A]=B}setMeasurement(A,B,Q=""){this._measurements[A]={value:B,unit:Q}}setMetadata(A){this._metadata={...this._metadata,...A}}end(A){let B=pW1.spanTimeInputToSeconds(A),Q=this._finishTransaction(B);if(!Q)return;return this._hub.captureEvent(Q)}toContext(){let A=super.toContext();return Yl.dropUndefinedKeys({...A,name:this._name,trimEnd:this._trimEnd})}updateWithContext(A){return super.updateWithContext(A),this._name=A.name||"",this._trimEnd=A.trimEnd,this}getDynamicSamplingContext(){return k_0.getDynamicSamplingContextFromSpan(this)}setHub(A){this._hub=A}getProfileId(){if(this._contexts!==void 0&&this._contexts.profile!==void 0)return this._contexts.profile.profile_id;return}_finishTransaction(A){if(this._endTime!==void 0)return;if(!this._name)lW1.DEBUG_BUILD&&Yl.logger.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>";super.end(A);let B=this._hub.getClient();if(B&&B.emit)B.emit("finishTransaction",this);if(this._sampled!==!0){if(lW1.DEBUG_BUILD&&Yl.logger.log("[Tracing] Discarding transaction because its trace was not chosen to be sampled."),B)B.recordDroppedEvent("sample_rate","transaction");return}let Q=this.spanRecorder?this.spanRecorder.spans.filter((W)=>W!==this&&pW1.spanToJSON(W).timestamp):[];if(this._trimEnd&&Q.length>0){let W=Q.map((J)=>pW1.spanToJSON(J).timestamp).filter(Boolean);this._endTime=W.reduce((J,X)=>{return J>X?J:X})}let{scope:D,isolationScope:Z}=I89.getCapturedScopesOnSpan(this),{metadata:G}=this,{source:F}=G,I={contexts:{...this._contexts,trace:pW1.spanToTraceContext(this)},spans:Q,start_timestamp:this._startTime,tags:this.tags,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{...G,capturedSpanScope:D,capturedSpanIsolationScope:Z,...Yl.dropUndefinedKeys({dynamicSamplingContext:k_0.getDynamicSamplingContextFromSpan(this)})},_metrics_summary:F89.getMetricSummaryJsonForSpan(this),...F&&{transaction_info:{source:F}}};if(Object.keys(this._measurements).length>0)lW1.DEBUG_BUILD&&Yl.logger.log("[Measurements] Adding measurements to transaction",JSON.stringify(this._measurements,void 0,2)),I.measurements=this._measurements;return lW1.DEBUG_BUILD&&Yl.logger.log(`[Tracing] Finishing ${this.op} transaction: ${this._name}.`),I}}x_0.Transaction=__0});
var ik0=E((pk0)=>{Object.defineProperty(pk0,"__esModule",{value:!0});var ck0=ru1(),fH=eA1(),lk0=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function ou1(A){if(!A)return;let B=A.match(lk0);if(!B)return;let Q;if(B[3]==="1")Q=!0;else if(B[3]==="0")Q=!1;return{traceId:B[1],parentSampled:Q,parentSpanId:B[2]}}function R19(A,B){let Q=ou1(A),D=ck0.baggageHeaderToDynamicSamplingContext(B),{traceId:Z,parentSpanId:G,parentSampled:F}=Q||{};if(!Q)return{traceparentData:Q,dynamicSamplingContext:void 0,propagationContext:{traceId:Z||fH.uuid4(),spanId:fH.uuid4().substring(16)}};else return{traceparentData:Q,dynamicSamplingContext:D||{},propagationContext:{traceId:Z||fH.uuid4(),parentSpanId:G||fH.uuid4().substring(16),spanId:fH.uuid4().substring(16),sampled:F,dsc:D||{}}}}function O19(A,B){let Q=ou1(A),D=ck0.baggageHeaderToDynamicSamplingContext(B),{traceId:Z,parentSpanId:G,parentSampled:F}=Q||{};if(!Q)return{traceId:Z||fH.uuid4(),spanId:fH.uuid4().substring(16)};else return{traceId:Z||fH.uuid4(),parentSpanId:G||fH.uuid4().substring(16),spanId:fH.uuid4().substring(16),sampled:F,dsc:D||{}}}function T19(A=fH.uuid4(),B=fH.uuid4().substring(16),Q){let D="";if(Q!==void 0)D=Q?"-1":"-0";return`${A}-${B}${D}`}pk0.TRACEPARENT_REGEXP=lk0;pk0.extractTraceparentData=ou1;pk0.generateSentryTraceHeader=T19;pk0.propagationContextFromHeaders=O19;pk0.tracingContextFromHeaders=R19});
var im1=E((Wx0)=>{Object.defineProperty(Wx0,"__esModule",{value:!0});var eW1=N21(),X39=q21();class dm1{constructor(A){this._value=A}get weight(){return 1}add(A){this._value+=A}toString(){return`${this._value}`}}class cm1{constructor(A){this._last=A,this._min=A,this._max=A,this._sum=A,this._count=1}get weight(){return 5}add(A){if(this._last=A,A<this._min)this._min=A;if(A>this._max)this._max=A;this._sum+=A,this._count++}toString(){return`${this._last}:${this._min}:${this._max}:${this._sum}:${this._count}`}}class lm1{constructor(A){this._value=[A]}get weight(){return this._value.length}add(A){this._value.push(A)}toString(){return this._value.join(":")}}class pm1{constructor(A){this.first=A,this._value=new Set([A])}get weight(){return this._value.size}add(A){this._value.add(A)}toString(){return Array.from(this._value).map((A)=>typeof A==="string"?X39.simpleHash(A):A).join(":")}}var V39={[eW1.COUNTER_METRIC_TYPE]:dm1,[eW1.GAUGE_METRIC_TYPE]:cm1,[eW1.DISTRIBUTION_METRIC_TYPE]:lm1,[eW1.SET_METRIC_TYPE]:pm1};Wx0.CounterMetric=dm1;Wx0.DistributionMetric=lm1;Wx0.GaugeMetric=cm1;Wx0.METRIC_MAP=V39;Wx0.SetMetric=pm1});
var iu1=E((Lk0)=>{Object.defineProperty(Lk0,"__esModule",{value:!0});function PeB(A){if(!A)return{};let B=A.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!B)return{};let Q=B[6]||"",D=B[8]||"";return{host:B[4],path:B[5],protocol:B[2],search:Q,hash:D,relative:B[5]+Q+D}}function SeB(A){return A.split(/[\?#]/,1)[0]}function jeB(A){return A.split(/\\?\//).filter((B)=>B.length>0&&B!==",").length}function keB(A){let{protocol:B,host:Q,path:D}=A,Z=Q&&Q.replace(/^.*@/,"[filtered]:[filtered]@").replace(/(:80)$/,"").replace(/(:443)$/,"")||"";return`${B?`${B}://`:""}${Z}${D}`}Lk0.getNumberOfUrlSegments=jeB;Lk0.getSanitizedUrlString=keB;Lk0.parseUrl=PeB;Lk0.stripUrlQueryAndFragment=SeB});
var jW1=E((Z_0)=>{Object.defineProperty(Z_0,"__esModule",{value:!0});var pq=$A(),Q_0=J21(),O49=Bl(),T49=SW1(),P49=100,yW1;class Zl{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=D_0()}static clone(A){return A?A.clone():new Zl}clone(){let A=new Zl;return A._breadcrumbs=[...this._breadcrumbs],A._tags={...this._tags},A._extra={...this._extra},A._contexts={...this._contexts},A._user=this._user,A._level=this._level,A._span=this._span,A._session=this._session,A._transactionName=this._transactionName,A._fingerprint=this._fingerprint,A._eventProcessors=[...this._eventProcessors],A._requestSession=this._requestSession,A._attachments=[...this._attachments],A._sdkProcessingMetadata={...this._sdkProcessingMetadata},A._propagationContext={...this._propagationContext},A._client=this._client,A}setClient(A){this._client=A}getClient(){return this._client}addScopeListener(A){this._scopeListeners.push(A)}addEventProcessor(A){return this._eventProcessors.push(A),this}setUser(A){if(this._user=A||{email:void 0,id:void 0,ip_address:void 0,segment:void 0,username:void 0},this._session)O49.updateSession(this._session,{user:A});return this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(A){return this._requestSession=A,this}setTags(A){return this._tags={...this._tags,...A},this._notifyScopeListeners(),this}setTag(A,B){return this._tags={...this._tags,[A]:B},this._notifyScopeListeners(),this}setExtras(A){return this._extra={...this._extra,...A},this._notifyScopeListeners(),this}setExtra(A,B){return this._extra={...this._extra,[A]:B},this._notifyScopeListeners(),this}setFingerprint(A){return this._fingerprint=A,this._notifyScopeListeners(),this}setLevel(A){return this._level=A,this._notifyScopeListeners(),this}setTransactionName(A){return this._transactionName=A,this._notifyScopeListeners(),this}setContext(A,B){if(B===null)delete this._contexts[A];else this._contexts[A]=B;return this._notifyScopeListeners(),this}setSpan(A){return this._span=A,this._notifyScopeListeners(),this}getSpan(){return this._span}getTransaction(){let A=this._span;return A&&A.transaction}setSession(A){if(!A)delete this._session;else this._session=A;return this._notifyScopeListeners(),this}getSession(){return this._session}update(A){if(!A)return this;let B=typeof A==="function"?A(this):A;if(B instanceof Zl){let Q=B.getScopeData();if(this._tags={...this._tags,...Q.tags},this._extra={...this._extra,...Q.extra},this._contexts={...this._contexts,...Q.contexts},Q.user&&Object.keys(Q.user).length)this._user=Q.user;if(Q.level)this._level=Q.level;if(Q.fingerprint.length)this._fingerprint=Q.fingerprint;if(B.getRequestSession())this._requestSession=B.getRequestSession();if(Q.propagationContext)this._propagationContext=Q.propagationContext}else if(pq.isPlainObject(B)){let Q=A;if(this._tags={...this._tags,...Q.tags},this._extra={...this._extra,...Q.extra},this._contexts={...this._contexts,...Q.contexts},Q.user)this._user=Q.user;if(Q.level)this._level=Q.level;if(Q.fingerprint)this._fingerprint=Q.fingerprint;if(Q.requestSession)this._requestSession=Q.requestSession;if(Q.propagationContext)this._propagationContext=Q.propagationContext}return this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._span=void 0,this._session=void 0,this._notifyScopeListeners(),this._attachments=[],this._propagationContext=D_0(),this}addBreadcrumb(A,B){let Q=typeof B==="number"?B:P49;if(Q<=0)return this;let D={timestamp:pq.dateTimestampInSeconds(),...A},Z=this._breadcrumbs;return Z.push(D),this._breadcrumbs=Z.length>Q?Z.slice(-Q):Z,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(A){return this._attachments.push(A),this}getAttachments(){return this.getScopeData().attachments}clearAttachments(){return this._attachments=[],this}getScopeData(){let{_breadcrumbs:A,_attachments:B,_contexts:Q,_tags:D,_extra:Z,_user:G,_level:F,_fingerprint:I,_eventProcessors:Y,_propagationContext:W,_sdkProcessingMetadata:J,_transactionName:X,_span:V}=this;return{breadcrumbs:A,attachments:B,contexts:Q,tags:D,extra:Z,user:G,level:F,fingerprint:I||[],eventProcessors:Y,propagationContext:W,sdkProcessingMetadata:J,transactionName:X,span:V}}applyToEvent(A,B={},Q=[]){T49.applyScopeDataToEvent(A,this.getScopeData());let D=[...Q,...Q_0.getGlobalEventProcessors(),...this._eventProcessors];return Q_0.notifyEventProcessors(D,A,B)}setSDKProcessingMetadata(A){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...A},this}setPropagationContext(A){return this._propagationContext=A,this}getPropagationContext(){return this._propagationContext}captureException(A,B){let Q=B&&B.event_id?B.event_id:pq.uuid4();if(!this._client)return pq.logger.warn("No client configured on scope - will not capture exception!"),Q;let D=new Error("Sentry syntheticException");return this._client.captureException(A,{originalException:A,syntheticException:D,...B,event_id:Q},this),Q}captureMessage(A,B,Q){let D=Q&&Q.event_id?Q.event_id:pq.uuid4();if(!this._client)return pq.logger.warn("No client configured on scope - will not capture message!"),D;let Z=new Error(A);return this._client.captureMessage(A,B,{originalException:A,syntheticException:Z,...Q,event_id:D},this),D}captureEvent(A,B){let Q=B&&B.event_id?B.event_id:pq.uuid4();if(!this._client)return pq.logger.warn("No client configured on scope - will not capture event!"),Q;return this._client.captureEvent(A,{...B,event_id:Q},this),Q}_notifyScopeListeners(){if(!this._notifyingListeners)this._notifyingListeners=!0,this._scopeListeners.forEach((A)=>{A(this)}),this._notifyingListeners=!1}}function S49(){if(!yW1)yW1=new Zl;return yW1}function j49(A){yW1=A}function D_0(){return{traceId:pq.uuid4(),spanId:pq.uuid4().substring(16)}}Z_0.Scope=Zl;Z_0.getGlobalScope=S49;Z_0.setGlobalScope=j49});
var jd1=E((Lh0)=>{var{_nullishCoalesce:Sd1}=$A();Object.defineProperty(Lh0,"__esModule",{value:!0});var rJ9=J1("http"),oJ9=J1("https"),tJ9=J1("stream"),Nh0=J1("url"),eJ9=J1("zlib"),qh0=OQ(),AX9=$A(),BX9=$h0(),QX9=32768;function DX9(A){return new tJ9.Readable({read(){this.push(A),this.push(null)}})}function ZX9(A){let B;try{B=new Nh0.URL(A.url)}catch(Y){return AX9.consoleSandbox(()=>{console.warn("[@sentry/node]: Invalid dsn or tunnel option, will not send any events. The tunnel option must be a full URL when used.")}),qh0.createTransport(A,()=>Promise.resolve({}))}let Q=B.protocol==="https:",D=GX9(B,A.proxy||(Q?process.env.https_proxy:void 0)||process.env.http_proxy),Z=Q?oJ9:rJ9,G=A.keepAlive===void 0?!1:A.keepAlive,F=D?new BX9.HttpsProxyAgent(D):new Z.Agent({keepAlive:G,maxSockets:30,timeout:2000}),I=FX9(A,Sd1(A.httpModule,()=>Z),F);return qh0.createTransport(A,I)}function GX9(A,B){let{no_proxy:Q}=process.env;if(Q&&Q.split(",").some((Z)=>A.host.endsWith(Z)||A.hostname.endsWith(Z)))return;else return B}function FX9(A,B,Q){let{hostname:D,pathname:Z,port:G,protocol:F,search:I}=new Nh0.URL(A.url);return function Y(W){return new Promise((J,X)=>{let V=DX9(W.body),C={...A.headers};if(W.body.length>QX9)C["content-encoding"]="gzip",V=V.pipe(eJ9.createGzip());let K=B.request({method:"POST",agent:Q,headers:C,hostname:D,path:`${Z}${I}`,port:G,protocol:F,ca:A.caCerts},(H)=>{H.on("data",()=>{}),H.on("end",()=>{}),H.setEncoding("utf8");let z=Sd1(H.headers["retry-after"],()=>null),$=Sd1(H.headers["x-sentry-rate-limits"],()=>null);J({statusCode:H.statusCode,headers:{"retry-after":z,"x-sentry-rate-limits":Array.isArray($)?$[0]:$}})});K.on("error",X),V.pipe(K)})}}Lh0.makeNodeTransport=ZX9});
var jh0=E((Sh0)=>{var{_optionalChain:EX9}=$A();Object.defineProperty(Sh0,"__esModule",{value:!0});var kd1=OQ(),UX9=J1("async_hooks"),SJ1;function wX9(){if(!SJ1)SJ1=new UX9.AsyncLocalStorage;function A(){return SJ1.getStore()}function B(D){let Z={};return kd1.ensureHubOnCarrier(Z,D),kd1.getHubFromCarrier(Z)}function Q(D,Z){let G=A();if(G&&EX9([Z,"optionalAccess",(I)=>I.reuseExisting]))return D();let F=B(G);return SJ1.run(F,()=>{return D()})}kd1.setAsyncContextStrategy({getCurrentHub:A,runWithAsyncContext:Q})}Sh0.setHooksAsyncContextStrategy=wX9});
var kJ1=E((bh0)=>{Object.defineProperty(bh0,"__esModule",{value:!0});var OX9=J1("util"),jJ1=OQ(),_h0=$A(),xh0="Console",TX9=()=>{return{name:xh0,setupOnce(){},setup(A){_h0.addConsoleInstrumentationHandler(({args:B,level:Q})=>{if(jJ1.getClient()!==A)return;jJ1.addBreadcrumb({category:"console",level:_h0.severityLevelFromString(Q),message:OX9.format.apply(void 0,B)},{input:[...B],level:Q})})}}},vh0=jJ1.defineIntegration(TX9),PX9=jJ1.convertIntegrationFnToClass(xh0,vh0);bh0.Console=PX9;bh0.consoleIntegration=vh0});
var km1=E((b_0)=>{Object.defineProperty(b_0,"__esModule",{value:!0});var TW=$A(),sX=qG(),nW1=aX(),W89=cW1(),J89=iW1(),aW1={idleTimeout:1000,finalTimeout:30000,heartbeatInterval:5000},X89="finishReason",Wl=["heartbeatFailed","idleTimeout","documentHidden","finalTimeout","externalFinish","cancelled"];class jm1 extends W89.SpanRecorder{constructor(A,B,Q,D){super(D);this._pushActivity=A,this._popActivity=B,this.transactionSpanId=Q}add(A){if(A.spanContext().spanId!==this.transactionSpanId){let B=A.end;if(A.end=(...Q)=>{return this._popActivity(A.spanContext().spanId),B.apply(A,Q)},nW1.spanToJSON(A).timestamp===void 0)this._pushActivity(A.spanContext().spanId)}super.add(A)}}class v_0 extends J89.Transaction{constructor(A,B,Q=aW1.idleTimeout,D=aW1.finalTimeout,Z=aW1.heartbeatInterval,G=!1,F=!1){super(A,B);if(this._idleHub=B,this._idleTimeout=Q,this._finalTimeout=D,this._heartbeatInterval=Z,this._onScope=G,this.activities={},this._heartbeatCounter=0,this._finished=!1,this._idleTimeoutCanceledPermanently=!1,this._beforeFinishCallbacks=[],this._finishReason=Wl[4],this._autoFinishAllowed=!F,G)sX.DEBUG_BUILD&&TW.logger.log(`Setting idle transaction on scope. Span ID: ${this.spanContext().spanId}`),B.getScope().setSpan(this);if(!F)this._restartIdleTimeout();setTimeout(()=>{if(!this._finished)this.setStatus("deadline_exceeded"),this._finishReason=Wl[3],this.end()},this._finalTimeout)}end(A){let B=nW1.spanTimeInputToSeconds(A);if(this._finished=!0,this.activities={},this.op==="ui.action.click")this.setAttribute(X89,this._finishReason);if(this.spanRecorder){sX.DEBUG_BUILD&&TW.logger.log("[Tracing] finishing IdleTransaction",new Date(B*1000).toISOString(),this.op);for(let Q of this._beforeFinishCallbacks)Q(this,B);this.spanRecorder.spans=this.spanRecorder.spans.filter((Q)=>{if(Q.spanContext().spanId===this.spanContext().spanId)return!0;if(!nW1.spanToJSON(Q).timestamp)Q.setStatus("cancelled"),Q.end(B),sX.DEBUG_BUILD&&TW.logger.log("[Tracing] cancelling span since transaction ended early",JSON.stringify(Q,void 0,2));let{start_timestamp:D,timestamp:Z}=nW1.spanToJSON(Q),G=D&&D<B,F=(this._finalTimeout+this._idleTimeout)/1000,I=Z&&D&&Z-D<F;if(sX.DEBUG_BUILD){let Y=JSON.stringify(Q,void 0,2);if(!G)TW.logger.log("[Tracing] discarding Span since it happened after Transaction was finished",Y);else if(!I)TW.logger.log("[Tracing] discarding Span since it finished after Transaction final timeout",Y)}return G&&I}),sX.DEBUG_BUILD&&TW.logger.log("[Tracing] flushing IdleTransaction")}else sX.DEBUG_BUILD&&TW.logger.log("[Tracing] No active IdleTransaction");if(this._onScope){let Q=this._idleHub.getScope();if(Q.getTransaction()===this)Q.setSpan(void 0)}return super.end(A)}registerBeforeFinishCallback(A){this._beforeFinishCallbacks.push(A)}initSpanRecorder(A){if(!this.spanRecorder){let B=(D)=>{if(this._finished)return;this._pushActivity(D)},Q=(D)=>{if(this._finished)return;this._popActivity(D)};this.spanRecorder=new jm1(B,Q,this.spanContext().spanId,A),sX.DEBUG_BUILD&&TW.logger.log("Starting heartbeat"),this._pingHeartbeat()}this.spanRecorder.add(this)}cancelIdleTimeout(A,{restartOnChildSpanChange:B}={restartOnChildSpanChange:!0}){if(this._idleTimeoutCanceledPermanently=B===!1,this._idleTimeoutID){if(clearTimeout(this._idleTimeoutID),this._idleTimeoutID=void 0,Object.keys(this.activities).length===0&&this._idleTimeoutCanceledPermanently)this._finishReason=Wl[5],this.end(A)}}setFinishReason(A){this._finishReason=A}sendAutoFinishSignal(){if(!this._autoFinishAllowed)sX.DEBUG_BUILD&&TW.logger.log("[Tracing] Received finish signal for idle transaction."),this._restartIdleTimeout(),this._autoFinishAllowed=!0}_restartIdleTimeout(A){this.cancelIdleTimeout(),this._idleTimeoutID=setTimeout(()=>{if(!this._finished&&Object.keys(this.activities).length===0)this._finishReason=Wl[1],this.end(A)},this._idleTimeout)}_pushActivity(A){this.cancelIdleTimeout(void 0,{restartOnChildSpanChange:!this._idleTimeoutCanceledPermanently}),sX.DEBUG_BUILD&&TW.logger.log(`[Tracing] pushActivity: ${A}`),this.activities[A]=!0,sX.DEBUG_BUILD&&TW.logger.log("[Tracing] new activities count",Object.keys(this.activities).length)}_popActivity(A){if(this.activities[A])sX.DEBUG_BUILD&&TW.logger.log(`[Tracing] popActivity ${A}`),delete this.activities[A],sX.DEBUG_BUILD&&TW.logger.log("[Tracing] new activities count",Object.keys(this.activities).length);if(Object.keys(this.activities).length===0){let B=TW.timestampInSeconds();if(this._idleTimeoutCanceledPermanently){if(this._autoFinishAllowed)this._finishReason=Wl[5],this.end(B)}else this._restartIdleTimeout(B+this._idleTimeout/1000)}}_beat(){if(this._finished)return;let A=Object.keys(this.activities).join("");if(A===this._prevHeartbeatString)this._heartbeatCounter++;else this._heartbeatCounter=1;if(this._prevHeartbeatString=A,this._heartbeatCounter>=3){if(this._autoFinishAllowed)sX.DEBUG_BUILD&&TW.logger.log("[Tracing] Transaction finished because of no change for 3 heart beats"),this.setStatus("deadline_exceeded"),this._finishReason=Wl[0],this.end()}else this._pingHeartbeat()}_pingHeartbeat(){sX.DEBUG_BUILD&&TW.logger.log(`pinging Heartbeat -> current counter: ${this._heartbeatCounter}`),setTimeout(()=>{this._beat()},this._heartbeatInterval)}}b_0.IdleTransaction=v_0;b_0.IdleTransactionSpanRecorder=jm1;b_0.TRACING_DEFAULTS=aW1});
var ku1=E((mj0)=>{Object.defineProperty(mj0,"__esModule",{value:!0});var Su1=RW(),ju1=rR(),JW1=null;function toB(A){ju1.addHandler("error",A),ju1.maybeInstrument("error",eoB)}function eoB(){JW1=Su1.GLOBAL_OBJ.onerror,Su1.GLOBAL_OBJ.onerror=function(A,B,Q,D,Z){let G={column:D,error:Z,line:Q,msg:A,url:B};if(ju1.triggerHandlers("error",G),JW1&&!JW1.__SENTRY_LOADER__)return JW1.apply(this,arguments);return!1},Su1.GLOBAL_OBJ.onerror.__SENTRY_INSTRUMENTED__=!0}mj0.addGlobalErrorInstrumentationHandler=toB});
var kv0=E((jv0)=>{Object.defineProperty(jv0,"__esModule",{value:!0});var Tv0=BO(),XD9=Ov0(),Pv0="MetricsAggregator",VD9=()=>{return{name:Pv0,setupOnce(){},setup(A){A.metricsAggregator=new XD9.BrowserMetricsAggregator(A)}}},Sv0=Tv0.defineIntegration(VD9),CD9=Tv0.convertIntegrationFnToClass(Pv0,Sv0);jv0.MetricsAggregator=CD9;jv0.metricsAggregatorIntegration=Sv0});
var kx0=E((jx0)=>{Object.defineProperty(jx0,"__esModule",{value:!0});var rm1=$A(),i39=rW1();function om1(A,B){let Q;return rm1.forEachEnvelopeItem(A,(D,Z)=>{if(B.includes(Z))Q=Array.isArray(D)?D[1]:void 0;return!!Q}),Q}function n39(A,B){return(Q)=>{let D=A(Q);return{...D,send:async(Z)=>{let G=om1(Z,["event","transaction","profile","replay_event"]);if(G)G.release=B;return D.send(Z)}}}}function a39(A,B){return rm1.createEnvelope(B?{...A[0],dsn:B}:A[0],A[1])}function s39(A,B){return(Q)=>{let D=A(Q),Z=new Map;function G(Y,W){let J=W?`${Y}:${W}`:Y,X=Z.get(J);if(!X){let V=rm1.dsnFromString(Y);if(!V)return;let C=i39.getEnvelopeEndpointWithUrlEncodedAuth(V,Q.tunnel);X=W?n39(A,W)({...Q,url:C}):A({...Q,url:C}),Z.set(J,X)}return[Y,X]}async function F(Y){function W(V){let C=V&&V.length?V:["event"];return om1(Y,C)}let J=B({envelope:Y,getEvent:W}).map((V)=>{if(typeof V==="string")return G(V,void 0);else return G(V.dsn,V.release)}).filter((V)=>!!V);if(J.length===0)J.push(["",D]);return(await Promise.all(J.map(([V,C])=>C.send(a39(Y,V)))))[0]}async function I(Y){let W=[await D.flush(Y)];for(let[,J]of Z)W.push(await J.flush(Y));return W.every((J)=>J)}return{send:F,flush:I}}}jx0.eventFromEnvelope=om1;jx0.makeMultiplexedTransport=s39});
var lJ1=E((_g0)=>{Object.defineProperty(_g0,"__esModule",{value:!0});var cJ1=OQ(),Sg0=$A(),XC9=md1(),jg0="OnUnhandledRejection",VC9=(A={})=>{let B=A.mode||"warn";return{name:jg0,setupOnce(){},setup(Q){global.process.on("unhandledRejection",yg0(Q,{mode:B}))}}},kg0=cJ1.defineIntegration(VC9),CC9=cJ1.convertIntegrationFnToClass(jg0,kg0);function yg0(A,B){return function Q(D,Z){if(cJ1.getClient()!==A)return;cJ1.captureException(D,{originalException:Z,captureContext:{extra:{unhandledPromiseRejection:!0}},mechanism:{handled:!1,type:"onunhandledrejection"}}),KC9(D,B)}}function KC9(A,B){let Q="This error originated either by throwing inside of an async function without a catch block, or by rejecting a promise which was not handled with .catch(). The promise rejected with the reason:";if(B.mode==="warn")Sg0.consoleSandbox(()=>{console.warn(Q),console.error(A&&A.stack?A.stack:A)});else if(B.mode==="strict")Sg0.consoleSandbox(()=>{console.warn(Q)}),XC9.logAndExitProcess(A)}_g0.OnUnhandledRejection=CC9;_g0.makeUnhandledPromiseHandler=yg0;_g0.onUnhandledRejectionIntegration=kg0});
var lU=E((Gj0)=>{Object.defineProperty(Gj0,"__esModule",{value:!0});var psB=mq(),Wu1=RW(),isB="Sentry Logger ",Ju1=["debug","info","warn","error","log","assert","trace"],Xu1={};function Zj0(A){if(!("console"in Wu1.GLOBAL_OBJ))return A();let B=Wu1.GLOBAL_OBJ.console,Q={},D=Object.keys(Xu1);D.forEach((Z)=>{let G=Xu1[Z];Q[Z]=B[Z],B[Z]=G});try{return A()}finally{D.forEach((Z)=>{B[Z]=Q[Z]})}}function nsB(){let A=!1,B={enable:()=>{A=!0},disable:()=>{A=!1},isEnabled:()=>A};if(psB.DEBUG_BUILD)Ju1.forEach((Q)=>{B[Q]=(...D)=>{if(A)Zj0(()=>{Wu1.GLOBAL_OBJ.console[Q](`${isB}[${Q}]:`,...D)})}});else Ju1.forEach((Q)=>{B[Q]=()=>{return}});return B}var asB=nsB();Gj0.CONSOLE_LEVELS=Ju1;Gj0.consoleSandbox=Zj0;Gj0.logger=asB;Gj0.originalConsoleMethods=Xu1});
var lq=E((J_0)=>{Object.defineProperty(J_0,"__esModule",{value:!0});var jJ=$A(),b49=Al(),$m1=qG(),F_0=jW1(),qm1=Bl(),f49=_W1(),xW1=parseFloat(f49.SDK_VERSION),h49=100;class H21{constructor(A,B,Q,D=xW1){this._version=D;let Z;if(!B)Z=new F_0.Scope,Z.setClient(A);else Z=B;let G;if(!Q)G=new F_0.Scope,G.setClient(A);else G=Q;if(this._stack=[{scope:Z}],A)this.bindClient(A);this._isolationScope=G}isOlderThan(A){return this._version<A}bindClient(A){let B=this.getStackTop();if(B.client=A,B.scope.setClient(A),A&&A.setupIntegrations)A.setupIntegrations()}pushScope(){let A=this.getScope().clone();return this.getStack().push({client:this.getClient(),scope:A}),A}popScope(){if(this.getStack().length<=1)return!1;return!!this.getStack().pop()}withScope(A){let B=this.pushScope(),Q;try{Q=A(B)}catch(D){throw this.popScope(),D}if(jJ.isThenable(Q))return Q.then((D)=>{return this.popScope(),D},(D)=>{throw this.popScope(),D});return this.popScope(),Q}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStack(){return this._stack}getStackTop(){return this._stack[this._stack.length-1]}captureException(A,B){let Q=this._lastEventId=B&&B.event_id?B.event_id:jJ.uuid4(),D=new Error("Sentry syntheticException");return this.getScope().captureException(A,{originalException:A,syntheticException:D,...B,event_id:Q}),Q}captureMessage(A,B,Q){let D=this._lastEventId=Q&&Q.event_id?Q.event_id:jJ.uuid4(),Z=new Error(A);return this.getScope().captureMessage(A,B,{originalException:A,syntheticException:Z,...Q,event_id:D}),D}captureEvent(A,B){let Q=B&&B.event_id?B.event_id:jJ.uuid4();if(!A.type)this._lastEventId=Q;return this.getScope().captureEvent(A,{...B,event_id:Q}),Q}lastEventId(){return this._lastEventId}addBreadcrumb(A,B){let{scope:Q,client:D}=this.getStackTop();if(!D)return;let{beforeBreadcrumb:Z=null,maxBreadcrumbs:G=h49}=D.getOptions&&D.getOptions()||{};if(G<=0)return;let I={timestamp:jJ.dateTimestampInSeconds(),...A},Y=Z?jJ.consoleSandbox(()=>Z(I,B)):I;if(Y===null)return;if(D.emit)D.emit("beforeAddBreadcrumb",Y,B);Q.addBreadcrumb(Y,G)}setUser(A){this.getScope().setUser(A),this.getIsolationScope().setUser(A)}setTags(A){this.getScope().setTags(A),this.getIsolationScope().setTags(A)}setExtras(A){this.getScope().setExtras(A),this.getIsolationScope().setExtras(A)}setTag(A,B){this.getScope().setTag(A,B),this.getIsolationScope().setTag(A,B)}setExtra(A,B){this.getScope().setExtra(A,B),this.getIsolationScope().setExtra(A,B)}setContext(A,B){this.getScope().setContext(A,B),this.getIsolationScope().setContext(A,B)}configureScope(A){let{scope:B,client:Q}=this.getStackTop();if(Q)A(B)}run(A){let B=Nm1(this);try{A(this)}finally{Nm1(B)}}getIntegration(A){let B=this.getClient();if(!B)return null;try{return B.getIntegration(A)}catch(Q){return $m1.DEBUG_BUILD&&jJ.logger.warn(`Cannot retrieve integration ${A.id} from the current Hub`),null}}startTransaction(A,B){let Q=this._callExtensionMethod("startTransaction",A,B);if($m1.DEBUG_BUILD&&!Q)if(!this.getClient())jJ.logger.warn("Tracing extension 'startTransaction' is missing. You should 'init' the SDK before calling 'startTransaction'");else jJ.logger.warn(`Tracing extension 'startTransaction' has not been added. Call 'addTracingExtensions' before calling 'init':
Sentry.addTracingExtensions();
Sentry.init({...});
`);return Q}traceHeaders(){return this._callExtensionMethod("traceHeaders")}captureSession(A=!1){if(A)return this.endSession();this._sendSessionUpdate()}endSession(){let B=this.getStackTop().scope,Q=B.getSession();if(Q)qm1.closeSession(Q);this._sendSessionUpdate(),B.setSession()}startSession(A){let{scope:B,client:Q}=this.getStackTop(),{release:D,environment:Z=b49.DEFAULT_ENVIRONMENT}=Q&&Q.getOptions()||{},{userAgent:G}=jJ.GLOBAL_OBJ.navigator||{},F=qm1.makeSession({release:D,environment:Z,user:B.getUser(),...G&&{userAgent:G},...A}),I=B.getSession&&B.getSession();if(I&&I.status==="ok")qm1.updateSession(I,{status:"exited"});return this.endSession(),B.setSession(F),F}shouldSendDefaultPii(){let A=this.getClient(),B=A&&A.getOptions();return Boolean(B&&B.sendDefaultPii)}_sendSessionUpdate(){let{scope:A,client:B}=this.getStackTop(),Q=A.getSession();if(Q&&B&&B.captureSession)B.captureSession(Q)}_callExtensionMethod(A,...B){let D=Df().__SENTRY__;if(D&&D.extensions&&typeof D.extensions[A]==="function")return D.extensions[A].apply(this,B);$m1.DEBUG_BUILD&&jJ.logger.warn(`Extension method ${A} couldn't be found, doing nothing.`)}}function Df(){return jJ.GLOBAL_OBJ.__SENTRY__=jJ.GLOBAL_OBJ.__SENTRY__||{extensions:{},hub:void 0},jJ.GLOBAL_OBJ}function Nm1(A){let B=Df(),Q=K21(B);return vW1(B,A),Q}function I_0(){let A=Df();if(A.__SENTRY__&&A.__SENTRY__.acs){let B=A.__SENTRY__.acs.getCurrentHub();if(B)return B}return Y_0(A)}function g49(){return I_0().getIsolationScope()}function Y_0(A=Df()){if(!W_0(A)||K21(A).isOlderThan(xW1))vW1(A,new H21);return K21(A)}function u49(A,B=Y_0()){if(!W_0(A)||K21(A).isOlderThan(xW1)){let Q=B.getClient(),D=B.getScope(),Z=B.getIsolationScope();vW1(A,new H21(Q,D.clone(),Z.clone()))}}function m49(A){let B=Df();B.__SENTRY__=B.__SENTRY__||{},B.__SENTRY__.acs=A}function d49(A,B={}){let Q=Df();if(Q.__SENTRY__&&Q.__SENTRY__.acs)return Q.__SENTRY__.acs.runWithAsyncContext(A,B);return A()}function W_0(A){return!!(A&&A.__SENTRY__&&A.__SENTRY__.hub)}function K21(A){return jJ.getGlobalSingleton("hub",()=>new H21,A)}function vW1(A,B){if(!A)return!1;let Q=A.__SENTRY__=A.__SENTRY__||{};return Q.hub=B,!0}J_0.API_VERSION=xW1;J_0.Hub=H21;J_0.ensureHubOnCarrier=u49;J_0.getCurrentHub=I_0;J_0.getHubFromCarrier=K21;J_0.getIsolationScope=g49;J_0.getMainCarrier=Df;J_0.makeMain=Nm1;J_0.runWithAsyncContext=d49;J_0.setAsyncContextStrategy=m49;J_0.setHubOnCarrier=vW1});
var lu1=E((Uk0)=>{Object.defineProperty(Uk0,"__esModule",{value:!0});var EeB=vH(),oR;(function(A){A[A.PENDING=0]="PENDING";let Q=1;A[A.RESOLVED=Q]="RESOLVED";let D=2;A[A.REJECTED=D]="REJECTED"})(oR||(oR={}));function UeB(A){return new dq((B)=>{B(A)})}function weB(A){return new dq((B,Q)=>{Q(A)})}class dq{constructor(A){dq.prototype.__init.call(this),dq.prototype.__init2.call(this),dq.prototype.__init3.call(this),dq.prototype.__init4.call(this),this._state=oR.PENDING,this._handlers=[];try{A(this._resolve,this._reject)}catch(B){this._reject(B)}}then(A,B){return new dq((Q,D)=>{this._handlers.push([!1,(Z)=>{if(!A)Q(Z);else try{Q(A(Z))}catch(G){D(G)}},(Z)=>{if(!B)D(Z);else try{Q(B(Z))}catch(G){D(G)}}]),this._executeHandlers()})}catch(A){return this.then((B)=>B,A)}finally(A){return new dq((B,Q)=>{let D,Z;return this.then((G)=>{if(Z=!1,D=G,A)A()},(G)=>{if(Z=!0,D=G,A)A()}).then(()=>{if(Z){Q(D);return}B(D)})})}__init(){this._resolve=(A)=>{this._setResult(oR.RESOLVED,A)}}__init2(){this._reject=(A)=>{this._setResult(oR.REJECTED,A)}}__init3(){this._setResult=(A,B)=>{if(this._state!==oR.PENDING)return;if(EeB.isThenable(B)){B.then(this._resolve,this._reject);return}this._state=A,this._value=B,this._executeHandlers()}}__init4(){this._executeHandlers=()=>{if(this._state===oR.PENDING)return;let A=this._handlers.slice();this._handlers=[],A.forEach((B)=>{if(B[0])return;if(this._state===oR.RESOLVED)B[1](this._value);if(this._state===oR.REJECTED)B[2](this._value);B[0]=!0})}}}Uk0.SyncPromise=dq;Uk0.rejectedSyncPromise=weB;Uk0.resolvedSyncPromise=UeB});
var md1=E((Lg0)=>{Object.defineProperty(Lg0,"__esModule",{value:!0});var AC9=OQ(),uJ1=$A(),ud1=u21(),BC9=2000;function QC9(A){uJ1.consoleSandbox(()=>{console.error(A)});let B=AC9.getClient();if(B===void 0)ud1.DEBUG_BUILD&&uJ1.logger.warn("No NodeClient was defined, we are exiting the process now."),global.process.exit(1);let Q=B.getOptions(),D=Q&&Q.shutdownTimeout&&Q.shutdownTimeout>0&&Q.shutdownTimeout||BC9;B.close(D).then((Z)=>{if(!Z)ud1.DEBUG_BUILD&&uJ1.logger.warn("We reached the timeout for emptying the request buffer, still exiting now!");global.process.exit(1)},(Z)=>{ud1.DEBUG_BUILD&&uJ1.logger.error(Z)})}Lg0.logAndExitProcess=QC9});
var mm1=E((Ix0)=>{Object.defineProperty(Ix0,"__esModule",{value:!0});var um1=$A();function a59(A,B,Q,D,Z){let G={sent_at:new Date().toISOString()};if(Q&&Q.sdk)G.sdk={name:Q.sdk.name,version:Q.sdk.version};if(!!D&&!!Z)G.dsn=um1.dsnToString(Z);if(B)G.trace=um1.dropUndefinedKeys(B);let F=s59(A);return um1.createEnvelope(G,[F])}function s59(A){return[{type:"check_in"},A]}Ix0.createCheckInEnvelope=a59});
var mq=E((Dj0)=>{Object.defineProperty(Dj0,"__esModule",{value:!0});var csB=typeof __SENTRY_DEBUG__==="undefined"||__SENTRY_DEBUG__;Dj0.DEBUG_BUILD=csB});
var mu1=E((Ik0)=>{Object.defineProperty(Ik0,"__esModule",{value:!0});function ptB(){let A=typeof WeakSet==="function",B=A?new WeakSet:[];function Q(Z){if(A){if(B.has(Z))return!0;return B.add(Z),!1}for(let G=0;G<B.length;G++)if(B[G]===Z)return!0;return B.push(Z),!1}function D(Z){if(A)B.delete(Z);else for(let G=0;G<B.length;G++)if(B[G]===Z){B.splice(G,1);break}}return[Q,D]}Ik0.memoBuilder=ptB});
var mv0=E((uv0)=>{Object.defineProperty(uv0,"__esModule",{value:!0});var yv0=$A(),_v0=qG(),xv0=hH(),zD9=aX(),ZJ1=N21(),vv0=kv0();function GJ1(A,B,Q,D={}){let Z=xv0.getClient(),G=xv0.getCurrentScope();if(Z){if(!Z.metricsAggregator){_v0.DEBUG_BUILD&&yv0.logger.warn("No metrics aggregator enabled. Please add the MetricsAggregator integration to use metrics APIs");return}let{unit:F,tags:I,timestamp:Y}=D,{release:W,environment:J}=Z.getOptions(),X=G.getTransaction(),V={};if(W)V.release=W;if(J)V.environment=J;if(X)V.transaction=zD9.spanToJSON(X).description||"";_v0.DEBUG_BUILD&&yv0.logger.log(`Adding value of ${Q} to ${A} metric ${B}`),Z.metricsAggregator.add(A,B,Q,F,{...V,...I},Y)}}function bv0(A,B=1,Q){GJ1(ZJ1.COUNTER_METRIC_TYPE,A,B,Q)}function fv0(A,B,Q){GJ1(ZJ1.DISTRIBUTION_METRIC_TYPE,A,B,Q)}function hv0(A,B,Q){GJ1(ZJ1.SET_METRIC_TYPE,A,B,Q)}function gv0(A,B,Q){GJ1(ZJ1.GAUGE_METRIC_TYPE,A,B,Q)}var ED9={increment:bv0,distribution:fv0,set:hv0,gauge:gv0,MetricsAggregator:vv0.MetricsAggregator,metricsAggregatorIntegration:vv0.metricsAggregatorIntegration};uv0.distribution=fv0;uv0.gauge=gv0;uv0.increment=bv0;uv0.metrics=ED9;uv0.set=hv0});
var nJ1=E((gg0)=>{var{_optionalChain:iJ1}=$A();Object.defineProperty(gg0,"__esModule",{value:!0});var NG=OQ(),Ef=$A(),PC9=Cf();gg0.ChannelName=void 0;(function(A){A.RequestCreate="undici:request:create";let Q="undici:request:headers";A.RequestEnd=Q;let D="undici:request:error";A.RequestError=D})(gg0.ChannelName||(gg0.ChannelName={}));var SC9=(A)=>{return new eX(A)},jC9=NG.defineIntegration(SC9);class eX{static __initStatic(){this.id="Undici"}__init(){this.name=eX.id}__init2(){this._createSpanUrlMap=new Ef.LRUMap(100)}__init3(){this._headersUrlMap=new Ef.LRUMap(100)}constructor(A={}){eX.prototype.__init.call(this),eX.prototype.__init2.call(this),eX.prototype.__init3.call(this),eX.prototype.__init4.call(this),eX.prototype.__init5.call(this),eX.prototype.__init6.call(this),this._options={breadcrumbs:A.breadcrumbs===void 0?!0:A.breadcrumbs,tracing:A.tracing,shouldCreateSpanForRequest:A.shouldCreateSpanForRequest}}setupOnce(A){if(PC9.NODE_VERSION.major<16)return;let B;try{B=J1("diagnostics_channel")}catch(Q){}if(!B||!B.subscribe)return;B.subscribe(gg0.ChannelName.RequestCreate,this._onRequestCreate),B.subscribe(gg0.ChannelName.RequestEnd,this._onRequestEnd),B.subscribe(gg0.ChannelName.RequestError,this._onRequestError)}_shouldCreateSpan(A){if(this._options.tracing===!1||this._options.tracing===void 0&&!NG.hasTracingEnabled())return!1;if(this._options.shouldCreateSpanForRequest===void 0)return!0;let B=this._createSpanUrlMap.get(A);if(B!==void 0)return B;let Q=this._options.shouldCreateSpanForRequest(A);return this._createSpanUrlMap.set(A,Q),Q}__init4(){this._onRequestCreate=(A)=>{if(!iJ1([NG.getClient,"call",(J)=>J(),"optionalAccess",(J)=>J.getIntegration,"call",(J)=>J(eX)]))return;let{request:B}=A,Q=B.origin?B.origin.toString()+B.path:B.path,D=NG.getClient();if(!D)return;if(NG.isSentryRequestUrl(Q,D)||B.__sentry_span__!==void 0)return;let Z=D.getOptions(),G=NG.getCurrentScope(),F=NG.getIsolationScope(),I=NG.getActiveSpan(),Y=this._shouldCreateSpan(Q)?yC9(I,B,Q):void 0;if(Y)B.__sentry_span__=Y;if(((J)=>{if(Z.tracePropagationTargets===void 0)return!0;let X=this._headersUrlMap.get(J);if(X!==void 0)return X;let V=Ef.stringMatchesSomePattern(J,Z.tracePropagationTargets);return this._headersUrlMap.set(J,V),V})(Q)){let{traceId:J,spanId:X,sampled:V,dsc:C}={...F.getPropagationContext(),...G.getPropagationContext()},K=Y?NG.spanToTraceHeader(Y):Ef.generateSentryTraceHeader(J,X,V),H=Ef.dynamicSamplingContextToSentryBaggageHeader(C||(Y?NG.getDynamicSamplingContextFromSpan(Y):NG.getDynamicSamplingContextFromClient(J,D,G)));kC9(B,K,H)}}}__init5(){this._onRequestEnd=(A)=>{if(!iJ1([NG.getClient,"call",(G)=>G(),"optionalAccess",(G)=>G.getIntegration,"call",(G)=>G(eX)]))return;let{request:B,response:Q}=A,D=B.origin?B.origin.toString()+B.path:B.path;if(NG.isSentryRequestUrl(D,NG.getClient()))return;let Z=B.__sentry_span__;if(Z)NG.setHttpStatus(Z,Q.statusCode),Z.end();if(this._options.breadcrumbs)NG.addBreadcrumb({category:"http",data:{method:B.method,status_code:Q.statusCode,url:D},type:"http"},{event:"response",request:B,response:Q})}}__init6(){this._onRequestError=(A)=>{if(!iJ1([NG.getClient,"call",(Z)=>Z(),"optionalAccess",(Z)=>Z.getIntegration,"call",(Z)=>Z(eX)]))return;let{request:B}=A,Q=B.origin?B.origin.toString()+B.path:B.path;if(NG.isSentryRequestUrl(Q,NG.getClient()))return;let D=B.__sentry_span__;if(D)D.setStatus("internal_error"),D.end();if(this._options.breadcrumbs)NG.addBreadcrumb({category:"http",data:{method:B.method,url:Q},level:"error",type:"http"},{event:"error",request:B})}}}eX.__initStatic();function kC9(A,B,Q){let D;if(Array.isArray(A.headers))D=A.headers.some((Z)=>Z==="sentry-trace");else D=A.headers.split(`\r
`).some((G)=>G.startsWith("sentry-trace:"));if(D)return;if(A.addHeader("sentry-trace",B),Q)A.addHeader("baggage",Q)}function yC9(A,B,Q){let D=Ef.parseUrl(Q),Z=B.method||"GET",G={"http.method":Z};if(D.search)G["http.query"]=D.search;if(D.hash)G["http.fragment"]=D.hash;return iJ1([A,"optionalAccess",(F)=>F.startChild,"call",(F)=>F({op:"http.client",origin:"auto.http.node.undici",description:`${Z} ${Ef.getSanitizedUrlString(D)}`,data:G})])}gg0.Undici=eX;gg0.nativeNodeFetchintegration=jC9});
var nd1=E((Fu0)=>{var{_optionalChain:Zu0}=$A();Object.defineProperty(Fu0,"__esModule",{value:!0});var Ml=OQ(),Gu0=$A();function kK9(A={}){return function({path:B,type:Q,next:D,rawInput:Z}){let G=Zu0([Ml.getClient,"call",(W)=>W(),"optionalAccess",(W)=>W.getOptions,"call",(W)=>W()]),F=Ml.getCurrentScope().getTransaction();if(F){F.updateName(`trpc/${B}`),F.setAttribute(Ml.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,"route"),F.op="rpc.server";let W={procedure_type:Q};if(A.attachRpcInput!==void 0?A.attachRpcInput:Zu0([G,"optionalAccess",(J)=>J.sendDefaultPii]))W.input=Gu0.normalize(Z);F.setContext("trpc",W)}function I(W){if(!W.ok)Ml.captureException(W.error,{mechanism:{handled:!1,data:{function:"trpcMiddleware"}}})}let Y;try{Y=D()}catch(W){throw Ml.captureException(W,{mechanism:{handled:!1,data:{function:"trpcMiddleware"}}}),W}if(Gu0.isThenable(Y))Promise.resolve(Y).then((W)=>{I(W)},(W)=>{Ml.captureException(W,{mechanism:{handled:!1,data:{function:"trpcMiddleware"}}})});else I(Y);return Y}}Fu0.trpcMiddleware=kK9});
var nx0=E((ix0)=>{Object.defineProperty(ix0,"__esModule",{value:!0});var tm1=$A(),lx0=new Map,cx0=new Set;function J79(A){if(!tm1.GLOBAL_OBJ._sentryModuleMetadata)return;for(let B of Object.keys(tm1.GLOBAL_OBJ._sentryModuleMetadata)){let Q=tm1.GLOBAL_OBJ._sentryModuleMetadata[B];if(cx0.has(B))continue;cx0.add(B);let D=A(B);for(let Z of D.reverse())if(Z.filename){lx0.set(Z.filename,Q);break}}}function px0(A,B){return J79(A),lx0.get(B)}function X79(A,B){try{B.exception.values.forEach((Q)=>{if(!Q.stacktrace)return;for(let D of Q.stacktrace.frames||[]){if(!D.filename)continue;let Z=px0(A,D.filename);if(Z)D.module_metadata=Z}})}catch(Q){}}function V79(A){try{A.exception.values.forEach((B)=>{if(!B.stacktrace)return;for(let Q of B.stacktrace.frames||[])delete Q.module_metadata})}catch(B){}}ix0.addMetadataToStackFrames=X79;ix0.getMetadataForUrl=px0;ix0.stripMetadataFromStackFrames=V79});
var oA1=E((rS0)=>{Object.defineProperty(rS0,"__esModule",{value:!0});var DW1=vH();function CsB(A,B=0){if(typeof A!=="string"||B===0)return A;return A.length<=B?A:`${A.slice(0,B)}...`}function KsB(A,B){let Q=A,D=Q.length;if(D<=150)return Q;if(B>D)B=D;let Z=Math.max(B-60,0);if(Z<5)Z=0;let G=Math.min(Z+140,D);if(G>D-5)G=D;if(G===D)Z=Math.max(G-140,0);if(Q=Q.slice(Z,G),Z>0)Q=`'{snip} ${Q}`;if(G<D)Q+=" {snip}";return Q}function HsB(A,B){if(!Array.isArray(A))return"";let Q=[];for(let D=0;D<A.length;D++){let Z=A[D];try{if(DW1.isVueViewModel(Z))Q.push("[VueViewModel]");else Q.push(String(Z))}catch(G){Q.push("[value cannot be serialized]")}}return Q.join(B)}function sS0(A,B,Q=!1){if(!DW1.isString(A))return!1;if(DW1.isRegExp(B))return B.test(A);if(DW1.isString(B))return Q?A===B:A.includes(B);return!1}function zsB(A,B=[],Q=!1){return B.some((D)=>sS0(A,D,Q))}rS0.isMatchingPattern=sS0;rS0.safeJoin=HsB;rS0.snipLine=KsB;rS0.stringMatchesSomePattern=zsB;rS0.truncate=CsB});
var oX=E((Db0)=>{Object.defineProperty(Db0,"__esModule",{value:!0});var BF9=typeof __SENTRY_DEBUG__==="undefined"||__SENTRY_DEBUG__;Db0.DEBUG_BUILD=BF9});
var ob0=E((rb0)=>{Object.defineProperty(rb0,"__esModule",{value:!0});var RI9=Wf(),sb0=0,Cd1=1/0,NJ1=0,OI9=(A)=>{A.forEach((B)=>{if(B.interactionId)Cd1=Math.min(Cd1,B.interactionId),NJ1=Math.max(NJ1,B.interactionId),sb0=NJ1?(NJ1-Cd1)/7+1:0})},Kd1,TI9=()=>{return Kd1?sb0:performance.interactionCount||0},PI9=()=>{if("interactionCount"in performance||Kd1)return;Kd1=RI9.observe("event",OI9,{type:"event",buffered:!0,durationThreshold:0})};rb0.getInteractionCount=TI9;rb0.initInteractionCountPolyfill=PI9});
var ok0=E((rk0)=>{Object.defineProperty(rk0,"__esModule",{value:!0});var Q09=eu1(),D09=au1();function Z09(A,B,Q){let D=[{type:"client_report"},{timestamp:Q||D09.dateTimestampInSeconds(),discarded_events:A}];return Q09.createEnvelope(B?{dsn:B}:{},[D])}rk0.createClientReportEnvelope=Z09});
var pJ1=E((hg0)=>{Object.defineProperty(hg0,"__esModule",{value:!0});var UC9=J1("http"),wC9=J1("url"),xg0=OQ(),Nl=$A(),vg0="Spotlight",$C9=(A={})=>{let B={sidecarUrl:A.sidecarUrl||"http://localhost:8969/stream"};return{name:vg0,setupOnce(){},setup(Q){if(typeof process==="object"&&process.env)Nl.logger.warn("[Spotlight] It seems you're not in dev mode. Do you really want to have Spotlight enabled?");NC9(Q,B)}}},bg0=xg0.defineIntegration($C9),qC9=xg0.convertIntegrationFnToClass(vg0,bg0);function NC9(A,B){let Q=LC9(B.sidecarUrl);if(!Q)return;let D=0;if(typeof A.on!=="function"){Nl.logger.warn("[Spotlight] Cannot connect to spotlight due to missing method on SDK client (`client.on`)");return}A.on("beforeEnvelope",(Z)=>{if(D>3){Nl.logger.warn("[Spotlight] Disabled Sentry -> Spotlight integration due to too many failed requests");return}let G=Nl.serializeEnvelope(Z),I=fg0()({method:"POST",path:Q.pathname,hostname:Q.hostname,port:Q.port,headers:{"Content-Type":"application/x-sentry-envelope"}},(Y)=>{Y.on("data",()=>{}),Y.on("end",()=>{}),Y.setEncoding("utf8")});I.on("error",()=>{D++,Nl.logger.warn("[Spotlight] Failed to send envelope to Spotlight Sidecar")}),I.write(G),I.end()})}function LC9(A){try{return new wC9.URL(`${A}`)}catch(B){Nl.logger.warn(`[Spotlight] Invalid sidecar URL: ${A}`);return}}function fg0(){let{request:A}=UC9;if(MC9(A))return A.__sentry_original__;return A}function MC9(A){return"__sentry_original__"in A}hg0.Spotlight=qC9;hg0.getNativeHttpRequest=fg0;hg0.spotlightIntegration=bg0});
var pb0=E((lb0)=>{Object.defineProperty(lb0,"__esModule",{value:!0});var II9=Hl(),YI9=zl(),WI9=Wf(),JI9=El(),XI9=(A,B={})=>{let Q=YI9.initMetric("CLS",0),D,Z=0,G=[],F=(Y)=>{Y.forEach((W)=>{if(!W.hadRecentInput){let J=G[0],X=G[G.length-1];if(Z&&G.length!==0&&W.startTime-X.startTime<1000&&W.startTime-J.startTime<5000)Z+=W.value,G.push(W);else Z=W.value,G=[W];if(Z>Q.value){if(Q.value=Z,Q.entries=G,D)D()}}})},I=WI9.observe("layout-shift",F);if(I){D=II9.bindReporter(A,Q,B.reportAllChanges);let Y=()=>{F(I.takeRecords()),D(!0)};return JI9.onHidden(Y),Y}return};lb0.onCLS=XI9});
var q21=E((r_0)=>{Object.defineProperty(r_0,"__esModule",{value:!0});var z59=$A();function E59(A,B,Q,D){let Z=Object.entries(z59.dropUndefinedKeys(D)).sort((G,F)=>G[0].localeCompare(F[0]));return`${A}${B}${Q}${Z}`}function U59(A){let B=0;for(let Q=0;Q<A.length;Q++){let D=A.charCodeAt(Q);B=(B<<5)-B+D,B&=B}return B>>>0}function w59(A){let B="";for(let Q of A){let D=Object.entries(Q.tags),Z=D.length>0?`|#${D.map(([G,F])=>`${G}:${F}`).join(",")}`:"";B+=`${Q.name}@${Q.unit}:${Q.metric}|${Q.metricType}${Z}|T${Q.timestamp}
`}return B}function $59(A){return A.replace(/[^\w]+/gi,"_")}function q59(A){return A.replace(/[^\w\-.]+/gi,"_")}function N59(A){return A.replace(/[^\w\-./]+/gi,"")}var L59=[[`
`,"\\n"],["\r","\\r"],["\t","\\t"],["\\","\\\\"],["|","\\u{7c}"],[",","\\u{2c}"]];function M59(A){for(let[B,Q]of L59)if(A===B)return Q;return A}function R59(A){return[...A].reduce((B,Q)=>B+M59(Q),"")}function O59(A){let B={};for(let Q in A)if(Object.prototype.hasOwnProperty.call(A,Q)){let D=N59(Q);B[D]=R59(String(A[Q]))}return B}r_0.getBucketKey=E59;r_0.sanitizeMetricKey=q59;r_0.sanitizeTags=O59;r_0.sanitizeUnit=$59;r_0.serializeMetricBuckets=w59;r_0.simpleHash=U59});
var qG=E((yy0)=>{Object.defineProperty(yy0,"__esModule",{value:!0});var L99=typeof __SENTRY_DEBUG__==="undefined"||__SENTRY_DEBUG__;yy0.DEBUG_BUILD=L99});
var qJ1=E((ib0)=>{Object.defineProperty(ib0,"__esModule",{value:!0});var wJ1=yC(),CI9=El(),$J1=-1,KI9=()=>{if(wJ1.WINDOW.document&&wJ1.WINDOW.document.visibilityState)$J1=wJ1.WINDOW.document.visibilityState==="hidden"&&!wJ1.WINDOW.document.prerendering?0:1/0},HI9=()=>{CI9.onHidden(({timeStamp:A})=>{$J1=A},!0)},zI9=()=>{if($J1<0)KI9(),HI9();return{get firstHiddenTime(){return $J1}}};ib0.getVisibilityWatcher=zI9});
var qd1=E((Of0)=>{Object.defineProperty(Of0,"__esModule",{value:!0});var sq=OQ(),Xf=$A();function AW9(A,B,Q,D,Z="auto.http.browser"){if(!sq.hasTracingEnabled()||!A.fetchData)return;let G=B(A.fetchData.url);if(A.endTimestamp&&G){let C=A.fetchData.__span;if(!C)return;let K=D[C];if(K)QW9(K,A),delete D[C];return}let F=sq.getCurrentScope(),I=sq.getClient(),{method:Y,url:W}=A.fetchData,J=BW9(W),X=J?Xf.parseUrl(J).host:void 0,V=G?sq.startInactiveSpan({name:`${Y} ${W}`,onlyIfParent:!0,attributes:{url:W,type:"fetch","http.method":Y,"http.url":J,"server.address":X,[sq.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]:Z},op:"http.client"}):void 0;if(V)A.fetchData.__span=V.spanContext().spanId,D[V.spanContext().spanId]=V;if(Q(A.fetchData.url)&&I){let C=A.args[0];A.args[1]=A.args[1]||{};let K=A.args[1];K.headers=Rf0(C,I,F,K,V)}return V}function Rf0(A,B,Q,D,Z){let G=Z||Q.getSpan(),F=sq.getIsolationScope(),{traceId:I,spanId:Y,sampled:W,dsc:J}={...F.getPropagationContext(),...Q.getPropagationContext()},X=G?sq.spanToTraceHeader(G):Xf.generateSentryTraceHeader(I,Y,W),V=Xf.dynamicSamplingContextToSentryBaggageHeader(J||(G?sq.getDynamicSamplingContextFromSpan(G):sq.getDynamicSamplingContextFromClient(I,B,Q))),C=D.headers||(typeof Request!=="undefined"&&Xf.isInstanceOf(A,Request)?A.headers:void 0);if(!C)return{"sentry-trace":X,baggage:V};else if(typeof Headers!=="undefined"&&Xf.isInstanceOf(C,Headers)){let K=new Headers(C);if(K.append("sentry-trace",X),V)K.append(Xf.BAGGAGE_HEADER_NAME,V);return K}else if(Array.isArray(C)){let K=[...C,["sentry-trace",X]];if(V)K.push([Xf.BAGGAGE_HEADER_NAME,V]);return K}else{let K="baggage"in C?C.baggage:void 0,H=[];if(Array.isArray(K))H.push(...K);else if(K)H.push(K);if(V)H.push(V);return{...C,"sentry-trace":X,baggage:H.length>0?H.join(","):void 0}}}function BW9(A){try{return new URL(A).href}catch(B){return}}function QW9(A,B){if(B.response){sq.setHttpStatus(A,B.response.status);let Q=B.response&&B.response.headers&&B.response.headers.get("content-length");if(Q){let D=parseInt(Q);if(D>0)A.setAttribute("http.response_content_length",D)}}else if(B.error)A.setStatus("internal_error");A.end()}Of0.addTracingHeadersToFetchRequest=Rf0;Of0.instrumentFetchRequest=AW9});
var qu0=E(($u0)=>{Object.defineProperty($u0,"__esModule",{value:!0});var FH9=kJ1(),IH9=vJ1(),YH9=dJ1(),WH9=lJ1(),JH9=gJ1(),XH9=xJ1(),VH9=yJ1(),CH9=OQ(),KH9=hJ1(),HH9=nJ1(),zH9=pJ1(),EH9=rJ1(),UH9=ad1();$u0.Console=FH9.Console;$u0.Http=IH9.Http;$u0.OnUncaughtException=YH9.OnUncaughtException;$u0.OnUnhandledRejection=WH9.OnUnhandledRejection;$u0.Modules=JH9.Modules;$u0.ContextLines=XH9.ContextLines;$u0.Context=VH9.Context;$u0.RequestData=CH9.RequestData;$u0.LocalVariables=KH9.LocalVariables;$u0.Undici=HH9.Undici;$u0.Spotlight=zH9.Spotlight;$u0.Anr=EH9.Anr;$u0.Hapi=UH9.Hapi});
var qx0=E(($x0)=>{Object.defineProperty($x0,"__esModule",{value:!0});var Ux0=$A(),j39=qG(),k39=hH(),y39=lq();function _39(A,B){if(B.debug===!0)if(j39.DEBUG_BUILD)Ux0.logger.enable();else Ux0.consoleSandbox(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")});k39.getCurrentScope().update(B.initialScope);let D=new A(B);wx0(D),x39(D)}function wx0(A){let Q=y39.getCurrentHub().getStackTop();Q.client=A,Q.scope.setClient(A)}function x39(A){if(A.init)A.init();else if(A.setupIntegrations)A.setupIntegrations()}$x0.initAndBind=_39;$x0.setCurrentClient=wx0});
var rJ1=E((Au0,Bu0)=>{var{_optionalChain:CK9,_optionalChainDelete:og0}=$A();Object.defineProperty(Au0,"__esModule",{value:!0});var KK9=J1("url"),WO=OQ(),sJ1=$A(),pd1=Cf(),HK9=rg0(),zK9=50,EK9=5000;function id1(A,...B){sJ1.logger.log(`[ANR] ${A}`,...B)}function UK9(){return sJ1.GLOBAL_OBJ}function wK9(){let A=WO.getGlobalScope().getScopeData();return WO.mergeScopeData(A,WO.getIsolationScope().getScopeData()),WO.mergeScopeData(A,WO.getCurrentScope().getScopeData()),A.attachments=[],A.eventProcessors=[],A}function $K9(){return sJ1.dynamicRequire(Bu0,"worker_threads")}async function qK9(A){let B={message:"ANR"},Q={};for(let D of A.getEventProcessors()){if(B===null)break;B=await D(B,Q)}return CK9([B,"optionalAccess",(D)=>D.contexts])||{}}var tg0="Anr",NK9=(A={})=>{if(pd1.NODE_VERSION.major<16||pd1.NODE_VERSION.major===16&&pd1.NODE_VERSION.minor<17)throw new Error("ANR detection requires Node 16.17.0 or later");let B,Q,D=UK9();return D.__SENTRY_GET_SCOPES__=wK9,{name:tg0,setupOnce(){},startWorker:()=>{if(B)return;if(Q)B=MK9(Q,A)},stopWorker:()=>{if(B)B.then((Z)=>{Z(),B=void 0})},setup(Z){Q=Z,setImmediate(()=>this.startWorker())}}},eg0=WO.defineIntegration(NK9),LK9=WO.convertIntegrationFnToClass(tg0,eg0);async function MK9(A,B){let Q=A.getDsn();if(!Q)return()=>{};let D=await qK9(A);og0([D,"access",(J)=>J.app,"optionalAccess",(J)=>delete J.app_memory]),og0([D,"access",(J)=>J.device,"optionalAccess",(J)=>delete J.free_memory]);let Z=A.getOptions(),G=A.getSdkMetadata()||{};if(G.sdk)G.sdk.integrations=Z.integrations.map((J)=>J.name);let F={debug:sJ1.logger.isEnabled(),dsn:Q,environment:Z.environment||"production",release:Z.release,dist:Z.dist,sdkMetadata:G,appRootPath:B.appRootPath,pollInterval:B.pollInterval||zK9,anrThreshold:B.anrThreshold||EK9,captureStackTrace:!!B.captureStackTrace,staticTags:B.staticTags||{},contexts:D};if(F.captureStackTrace){let J=J1("inspector");if(!J.url())J.open(0)}let{Worker:I}=$K9(),Y=new I(new KK9.URL(`data:application/javascript;base64,${HK9.base64WorkerScript}`),{workerData:F});process.on("exit",()=>{Y.terminate()});let W=setInterval(()=>{try{let J=WO.getCurrentScope().getSession(),X=J?{...J,toJSON:void 0}:void 0;Y.postMessage({session:X})}catch(J){}},F.pollInterval);return W.unref(),Y.on("message",(J)=>{if(J==="session-ended")id1("ANR event sent from ANR worker. Clearing session in this thread."),WO.getCurrentScope().setSession(void 0)}),Y.once("error",(J)=>{clearInterval(W),id1("ANR worker error",J)}),Y.once("exit",(J)=>{clearInterval(W),id1("ANR worker exit",J)}),Y.unref(),()=>{Y.terminate(),clearInterval(W)}}Au0.Anr=LK9;Au0.anrIntegration=eg0});
var rR=E((Pj0)=>{Object.defineProperty(Pj0,"__esModule",{value:!0});var urB=mq(),mrB=lU(),drB=FW1(),oc={},Tj0={};function crB(A,B){oc[A]=oc[A]||[],oc[A].push(B)}function lrB(){Object.keys(oc).forEach((A)=>{oc[A]=void 0})}function prB(A,B){if(!Tj0[A])B(),Tj0[A]=!0}function irB(A,B){let Q=A&&oc[A];if(!Q)return;for(let D of Q)try{D(B)}catch(Z){urB.DEBUG_BUILD&&mrB.logger.error(`Error while triggering instrumentation handler.
Type: ${A}
Name: ${drB.getFunctionName(D)}
Error:`,Z)}}Pj0.addHandler=crB;Pj0.maybeInstrument=prB;Pj0.resetInstrumentationHandlers=lrB;Pj0.triggerHandlers=irB});
var rW1=E((n_0)=>{Object.defineProperty(n_0,"__esModule",{value:!0});var bm1=$A(),l89="7";function i_0(A){let B=A.protocol?`${A.protocol}:`:"",Q=A.port?`:${A.port}`:"";return`${B}//${A.host}${Q}${A.path?`/${A.path}`:""}/api/`}function p89(A){return`${i_0(A)}${A.projectId}/envelope/`}function i89(A,B){return bm1.urlEncode({sentry_key:A.publicKey,sentry_version:l89,...B&&{sentry_client:`${B.name}/${B.version}`}})}function n89(A,B={}){let Q=typeof B==="string"?B:B.tunnel,D=typeof B==="string"||!B._metadata?void 0:B._metadata.sdk;return Q?Q:`${p89(A)}?${i89(A,D)}`}function a89(A,B){let Q=bm1.makeDsn(A);if(!Q)return"";let D=`${i_0(Q)}embed/error-page/`,Z=`dsn=${bm1.dsnToString(Q)}`;for(let G in B){if(G==="dsn")continue;if(G==="onClose")continue;if(G==="user"){let F=B.user;if(!F)continue;if(F.name)Z+=`&name=${encodeURIComponent(F.name)}`;if(F.email)Z+=`&email=${encodeURIComponent(F.email)}`}else Z+=`&${encodeURIComponent(G)}=${encodeURIComponent(B[G])}`}return`${D}?${Z}`}n_0.getEnvelopeEndpointWithUrlEncodedAuth=n89;n_0.getReportDialogEndpoint=a89});
var rf0=E((sf0)=>{Object.defineProperty(sf0,"__esModule",{value:!0});var yZ=OQ(),gH=$A(),gj=oX(),PW9=Vd1(),cf0=wl(),b21=$d1(),pf0=OJ1(),xC=yC(),if0="BrowserTracing",SW9={...yZ.TRACING_DEFAULTS,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableInp:!1,interactionsSampleRate:1,_experiments:{},...pf0.defaultRequestInstrumentationOptions},jW9=(A={})=>{let B=gj.DEBUG_BUILD?!!(A.tracePropagationTargets||A.tracingOrigins):!1;if(yZ.addTracingExtensions(),!A.tracePropagationTargets&&A.tracingOrigins)A.tracePropagationTargets=A.tracingOrigins;let Q={...SW9,...A},D=b21.startTrackingWebVitals(),Z={};if(Q.enableInp)b21.startTrackingINP(Z,Q.interactionsSampleRate);if(Q.enableLongTask)b21.startTrackingLongTasks();if(Q._experiments.enableInteractions)b21.startTrackingInteractions();let G={name:void 0,context:void 0};function F(I){let Y=yZ.getCurrentHub(),{beforeStartSpan:W,idleTimeout:J,finalTimeout:X,heartbeatInterval:V}=Q,C=I.op==="pageload",K;if(C){let L=C?Md1("sentry-trace"):"",N=C?Md1("baggage"):void 0,{traceId:O,dsc:R,parentSpanId:T,sampled:j}=gH.propagationContextFromHeaders(L,N);K={traceId:O,parentSpanId:T,parentSampled:j,...I,metadata:{...I.metadata,dynamicSamplingContext:R},trimEnd:!0}}else K={trimEnd:!0,...I};let H=W?W(K):K;if(H.metadata=H.name!==K.name?{...H.metadata,source:"custom"}:H.metadata,G.name=H.name,G.context=H,H.sampled===!1)gj.DEBUG_BUILD&&gH.logger.log(`[Tracing] Will not send ${H.op} transaction because of beforeNavigate.`);gj.DEBUG_BUILD&&gH.logger.log(`[Tracing] Starting ${H.op} transaction on scope`);let{location:z}=xC.WINDOW,$=yZ.startIdleTransaction(Y,H,J,X,!0,{location:z},V,C);if(C&&xC.WINDOW.document){if(xC.WINDOW.document.addEventListener("readystatechange",()=>{if(["interactive","complete"].includes(xC.WINDOW.document.readyState))$.sendAutoFinishSignal()}),["interactive","complete"].includes(xC.WINDOW.document.readyState))$.sendAutoFinishSignal()}return $.registerBeforeFinishCallback((L)=>{D(),b21.addPerformanceEntries(L)}),$}return{name:if0,setupOnce:()=>{},afterAllSetup(I){let Y=I.getOptions(),{markBackgroundSpan:W,traceFetch:J,traceXHR:X,shouldCreateSpanForRequest:V,enableHTTPTimings:C,_experiments:K}=Q,H=Y&&Y.tracePropagationTargets,z=H||Q.tracePropagationTargets;if(gj.DEBUG_BUILD&&B&&H)gH.logger.warn("[Tracing] The `tracePropagationTargets` option was set in the BrowserTracing integration and top level `Sentry.init`. The top level `Sentry.init` value is being used.");let $,L=xC.WINDOW.location&&xC.WINDOW.location.href;if(I.on)I.on("startNavigationSpan",(N)=>{if($)gj.DEBUG_BUILD&&gH.logger.log(`[Tracing] Finishing current transaction with op: ${yZ.spanToJSON($).op}`),$.end();$=F({op:"navigation",...N})}),I.on("startPageLoadSpan",(N)=>{if($)gj.DEBUG_BUILD&&gH.logger.log(`[Tracing] Finishing current transaction with op: ${yZ.spanToJSON($).op}`),$.end();$=F({op:"pageload",...N})});if(Q.instrumentPageLoad&&I.emit&&xC.WINDOW.location){let N={name:xC.WINDOW.location.pathname,startTimestamp:gH.browserPerformanceTimeOrigin?gH.browserPerformanceTimeOrigin/1000:void 0,origin:"auto.pageload.browser",attributes:{[yZ.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]:"url"}};nf0(I,N)}if(Q.instrumentNavigation&&I.emit&&xC.WINDOW.location)gH.addHistoryInstrumentationHandler(({to:N,from:O})=>{if(O===void 0&&L&&L.indexOf(N)!==-1){L=void 0;return}if(O!==N){L=void 0;let R={name:xC.WINDOW.location.pathname,origin:"auto.navigation.browser",attributes:{[yZ.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]:"url"}};af0(I,R)}});if(W)PW9.registerBackgroundTabDetection();if(K.enableInteractions)kW9(Q,G);if(Q.enableInp)_W9(Z,G);pf0.instrumentOutgoingRequests({traceFetch:J,traceXHR:X,tracePropagationTargets:z,shouldCreateSpanForRequest:V,enableHTTPTimings:C})},options:Q}};function nf0(A,B){if(!A.emit)return;A.emit("startPageLoadSpan",B);let Q=yZ.getActiveSpan();return(Q&&yZ.spanToJSON(Q).op)==="pageload"?Q:void 0}function af0(A,B){if(!A.emit)return;A.emit("startNavigationSpan",B);let Q=yZ.getActiveSpan();return(Q&&yZ.spanToJSON(Q).op)==="navigation"?Q:void 0}function Md1(A){let B=gH.getDomElement(`meta[name=${A}]`);return B?B.getAttribute("content"):void 0}function kW9(A,B){let Q,D=()=>{let{idleTimeout:Z,finalTimeout:G,heartbeatInterval:F}=A,I="ui.action.click",Y=yZ.getActiveTransaction();if(Y&&Y.op&&["navigation","pageload"].includes(Y.op)){gj.DEBUG_BUILD&&gH.logger.warn("[Tracing] Did not create ui.action.click transaction because a pageload or navigation transaction is in progress.");return}if(Q)Q.setFinishReason("interactionInterrupted"),Q.end(),Q=void 0;if(!B.name){gj.DEBUG_BUILD&&gH.logger.warn("[Tracing] Did not create ui.action.click transaction because _latestRouteName is missing.");return}let{location:W}=xC.WINDOW,J={name:B.name,op:"ui.action.click",trimEnd:!0,data:{[yZ.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]:B.context?xW9(B.context):"url"}};Q=yZ.startIdleTransaction(yZ.getCurrentHub(),J,Z,G,!0,{location:W},F)};["click"].forEach((Z)=>{if(xC.WINDOW.document)addEventListener(Z,D,{once:!1,capture:!0})})}function yW9(A){return"duration"in A}var lf0=10;function _W9(A,B){let Q=({entries:D})=>{let Z=yZ.getClient(),G=Z!==void 0&&Z.getIntegrationByName!==void 0?Z.getIntegrationByName("Replay"):void 0,F=G!==void 0?G.getReplayId():void 0,I=yZ.getActiveTransaction(),Y=yZ.getCurrentScope(),W=Y!==void 0?Y.getUser():void 0;D.forEach((J)=>{if(yW9(J)){let X=J.interactionId;if(X===void 0)return;let V=A[X],C=J.duration,K=J.startTime,H=Object.keys(A),z=H.length>0?H.reduce(($,L)=>{return A[$].duration<A[L].duration?$:L}):void 0;if(J.entryType==="first-input"){if(H.map((L)=>A[L]).some((L)=>{return L.duration===C&&L.startTime===K}))return}if(!X)return;if(V)V.duration=Math.max(V.duration,C);else if(H.length<lf0||z===void 0||C>A[z].duration){let{name:$,context:L}=B;if($&&L){if(z&&Object.keys(A).length>=lf0)delete A[z];A[X]={routeName:$,duration:C,parentContext:L,user:W,activeTransaction:I,replayId:F,startTime:K}}}}})};cf0.addPerformanceInstrumentationHandler("event",Q),cf0.addPerformanceInstrumentationHandler("first-input",Q)}function xW9(A){let B=A.attributes&&A.attributes[yZ.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE],Q=A.data&&A.data[yZ.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE],D=A.metadata&&A.metadata.source;return B||Q||D}sf0.BROWSER_TRACING_INTEGRATION_ID=if0;sf0.browserTracingIntegration=jW9;sf0.getMetaContent=Md1;sf0.startBrowserTracingNavigationSpan=af0;sf0.startBrowserTracingPageLoadSpan=nf0});
var rg0=E((XK9)=>{/*! @sentry/node 7.120.3 (5a833b4) | https://github.com/getsentry/sentry-javascript */XK9.base64WorkerScript="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"});
var ru0=E((su0,sd1)=>{/*!
    localForage -- Offline Storage, Improved
    Version 1.10.0
    https://localforage.github.io/localForage
    (c) 2013-2017 Mozilla, Apache License 2.0
*/(function(A){if(typeof su0==="object"&&typeof sd1!=="undefined")sd1.exports=A();else if(typeof define==="function"&&define.amd)define([],A);else{var B;if(typeof window!=="undefined")B=window;else if(typeof global!=="undefined")B=global;else if(typeof self!=="undefined")B=self;else B=this;B.localforage=A()}})(function(){var A,B,Q;return function D(Z,G,F){function I(J,X){if(!G[J]){if(!Z[J]){var V=J1;if(!X&&V)return V(J,!0);if(Y)return Y(J,!0);var C=new Error("Cannot find module '"+J+"'");throw C.code="MODULE_NOT_FOUND",C}var K=G[J]={exports:{}};Z[J][0].call(K.exports,function(H){var z=Z[J][1][H];return I(z?z:H)},K,K.exports,D,Z,G,F)}return G[J].exports}var Y=J1;for(var W=0;W<F.length;W++)I(F[W]);return I}({1:[function(D,Z,G){(function(F){var I=F.MutationObserver||F.WebKitMutationObserver,Y;if(I){var W=0,J=new I(H),X=F.document.createTextNode("");J.observe(X,{characterData:!0}),Y=function(){X.data=W=++W%2}}else if(!F.setImmediate&&typeof F.MessageChannel!=="undefined"){var V=new F.MessageChannel;V.port1.onmessage=H,Y=function(){V.port2.postMessage(0)}}else if("document"in F&&"onreadystatechange"in F.document.createElement("script"))Y=function(){var $=F.document.createElement("script");$.onreadystatechange=function(){H(),$.onreadystatechange=null,$.parentNode.removeChild($),$=null},F.document.documentElement.appendChild($)};else Y=function(){setTimeout(H,0)};var C,K=[];function H(){C=!0;var $,L,N=K.length;while(N){L=K,K=[],$=-1;while(++$<N)L[$]();N=K.length}C=!1}Z.exports=z;function z($){if(K.push($)===1&&!C)Y()}}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{}],2:[function(D,Z,G){var F=D(1);function I(){}var Y={},W=["REJECTED"],J=["FULFILLED"],X=["PENDING"];Z.exports=V;function V(T){if(typeof T!=="function")throw new TypeError("resolver must be a function");if(this.state=X,this.queue=[],this.outcome=void 0,T!==I)z(this,T)}V.prototype.catch=function(T){return this.then(null,T)},V.prototype.then=function(T,j){if(typeof T!=="function"&&this.state===J||typeof j!=="function"&&this.state===W)return this;var f=new this.constructor(I);if(this.state!==X){var k=this.state===J?T:j;K(f,k,this.outcome)}else this.queue.push(new C(f,T,j));return f};function C(T,j,f){if(this.promise=T,typeof j==="function")this.onFulfilled=j,this.callFulfilled=this.otherCallFulfilled;if(typeof f==="function")this.onRejected=f,this.callRejected=this.otherCallRejected}C.prototype.callFulfilled=function(T){Y.resolve(this.promise,T)},C.prototype.otherCallFulfilled=function(T){K(this.promise,this.onFulfilled,T)},C.prototype.callRejected=function(T){Y.reject(this.promise,T)},C.prototype.otherCallRejected=function(T){K(this.promise,this.onRejected,T)};function K(T,j,f){F(function(){var k;try{k=j(f)}catch(c){return Y.reject(T,c)}if(k===T)Y.reject(T,new TypeError("Cannot resolve promise with itself"));else Y.resolve(T,k)})}Y.resolve=function(T,j){var f=$(H,j);if(f.status==="error")return Y.reject(T,f.value);var k=f.value;if(k)z(T,k);else{T.state=J,T.outcome=j;var c=-1,h=T.queue.length;while(++c<h)T.queue[c].callFulfilled(j)}return T},Y.reject=function(T,j){T.state=W,T.outcome=j;var f=-1,k=T.queue.length;while(++f<k)T.queue[f].callRejected(j);return T};function H(T){var j=T&&T.then;if(T&&(typeof T==="object"||typeof T==="function")&&typeof j==="function")return function f(){j.apply(T,arguments)}}function z(T,j){var f=!1;function k(a){if(f)return;f=!0,Y.reject(T,a)}function c(a){if(f)return;f=!0,Y.resolve(T,a)}function h(){j(c,k)}var n=$(h);if(n.status==="error")k(n.value)}function $(T,j){var f={};try{f.value=T(j),f.status="success"}catch(k){f.status="error",f.value=k}return f}V.resolve=L;function L(T){if(T instanceof this)return T;return Y.resolve(new this(I),T)}V.reject=N;function N(T){var j=new this(I);return Y.reject(j,T)}V.all=O;function O(T){var j=this;if(Object.prototype.toString.call(T)!=="[object Array]")return this.reject(new TypeError("must be an array"));var f=T.length,k=!1;if(!f)return this.resolve([]);var c=new Array(f),h=0,n=-1,a=new this(I);while(++n<f)x(T[n],n);return a;function x(e,W1){j.resolve(e).then(U1,function(y1){if(!k)k=!0,Y.reject(a,y1)});function U1(y1){if(c[W1]=y1,++h===f&&!k)k=!0,Y.resolve(a,c)}}}V.race=R;function R(T){var j=this;if(Object.prototype.toString.call(T)!=="[object Array]")return this.reject(new TypeError("must be an array"));var f=T.length,k=!1;if(!f)return this.resolve([]);var c=-1,h=new this(I);while(++c<f)n(T[c]);return h;function n(a){j.resolve(a).then(function(x){if(!k)k=!0,Y.resolve(h,x)},function(x){if(!k)k=!0,Y.reject(h,x)})}}},{"1":1}],3:[function(D,Z,G){(function(F){if(typeof F.Promise!=="function")F.Promise=D(2)}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{"2":2}],4:[function(D,Z,G){var F=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(E1){return typeof E1}:function(E1){return E1&&typeof Symbol==="function"&&E1.constructor===Symbol&&E1!==Symbol.prototype?"symbol":typeof E1};function I(E1,t1){if(!(E1 instanceof t1))throw new TypeError("Cannot call a class as a function")}function Y(){try{if(typeof indexedDB!=="undefined")return indexedDB;if(typeof webkitIndexedDB!=="undefined")return webkitIndexedDB;if(typeof mozIndexedDB!=="undefined")return mozIndexedDB;if(typeof OIndexedDB!=="undefined")return OIndexedDB;if(typeof msIndexedDB!=="undefined")return msIndexedDB}catch(E1){return}}var W=Y();function J(){try{if(!W||!W.open)return!1;var E1=typeof openDatabase!=="undefined"&&/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),t1=typeof fetch==="function"&&fetch.toString().indexOf("[native code")!==-1;return(!E1||t1)&&typeof indexedDB!=="undefined"&&typeof IDBKeyRange!=="undefined"}catch(d1){return!1}}function X(E1,t1){E1=E1||[],t1=t1||{};try{return new Blob(E1,t1)}catch($0){if($0.name!=="TypeError")throw $0;var d1=typeof BlobBuilder!=="undefined"?BlobBuilder:typeof MSBlobBuilder!=="undefined"?MSBlobBuilder:typeof MozBlobBuilder!=="undefined"?MozBlobBuilder:WebKitBlobBuilder,C0=new d1;for(var L0=0;L0<E1.length;L0+=1)C0.append(E1[L0]);return C0.getBlob(t1.type)}}if(typeof Promise==="undefined")D(3);var V=Promise;function C(E1,t1){if(t1)E1.then(function(d1){t1(null,d1)},function(d1){t1(d1)})}function K(E1,t1,d1){if(typeof t1==="function")E1.then(t1);if(typeof d1==="function")E1.catch(d1)}function H(E1){if(typeof E1!=="string")console.warn(E1+" used as a key, but it is not a string."),E1=String(E1);return E1}function z(){if(arguments.length&&typeof arguments[arguments.length-1]==="function")return arguments[arguments.length-1]}var $="local-forage-detect-blob-support",L=void 0,N={},O=Object.prototype.toString,R="readonly",T="readwrite";function j(E1){var t1=E1.length,d1=new ArrayBuffer(t1),C0=new Uint8Array(d1);for(var L0=0;L0<t1;L0++)C0[L0]=E1.charCodeAt(L0);return d1}function f(E1){return new V(function(t1){var d1=E1.transaction($,T),C0=X([""]);d1.objectStore($).put(C0,"key"),d1.onabort=function(L0){L0.preventDefault(),L0.stopPropagation(),t1(!1)},d1.oncomplete=function(){var L0=navigator.userAgent.match(/Chrome\/(\d+)/),$0=navigator.userAgent.match(/Edge\//);t1($0||!L0||parseInt(L0[1],10)>=43)}}).catch(function(){return!1})}function k(E1){if(typeof L==="boolean")return V.resolve(L);return f(E1).then(function(t1){return L=t1,L})}function c(E1){var t1=N[E1.name],d1={};if(d1.promise=new V(function(C0,L0){d1.resolve=C0,d1.reject=L0}),t1.deferredOperations.push(d1),!t1.dbReady)t1.dbReady=d1.promise;else t1.dbReady=t1.dbReady.then(function(){return d1.promise})}function h(E1){var t1=N[E1.name],d1=t1.deferredOperations.pop();if(d1)return d1.resolve(),d1.promise}function n(E1,t1){var d1=N[E1.name],C0=d1.deferredOperations.pop();if(C0)return C0.reject(t1),C0.promise}function a(E1,t1){return new V(function(d1,C0){if(N[E1.name]=N[E1.name]||G1(),E1.db)if(t1)c(E1),E1.db.close();else return d1(E1.db);var L0=[E1.name];if(t1)L0.push(E1.version);var $0=W.open.apply(W,L0);if(t1)$0.onupgradeneeded=function(QA){var h0=$0.result;try{if(h0.createObjectStore(E1.storeName),QA.oldVersion<=1)h0.createObjectStore($)}catch(e0){if(e0.name==="ConstraintError")console.warn('The database "'+E1.name+'" has been upgraded from version '+QA.oldVersion+" to version "+QA.newVersion+', but the storage "'+E1.storeName+'" already exists.');else throw e0}};$0.onerror=function(QA){QA.preventDefault(),C0($0.error)},$0.onsuccess=function(){var QA=$0.result;QA.onversionchange=function(h0){h0.target.close()},d1(QA),h(E1)}})}function x(E1){return a(E1,!1)}function e(E1){return a(E1,!0)}function W1(E1,t1){if(!E1.db)return!0;var d1=!E1.db.objectStoreNames.contains(E1.storeName),C0=E1.version<E1.db.version,L0=E1.version>E1.db.version;if(C0){if(E1.version!==t1)console.warn('The database "'+E1.name+`" can't be downgraded from version `+E1.db.version+" to version "+E1.version+".");E1.version=E1.db.version}if(L0||d1){if(d1){var $0=E1.db.version+1;if($0>E1.version)E1.version=$0}return!0}return!1}function U1(E1){return new V(function(t1,d1){var C0=new FileReader;C0.onerror=d1,C0.onloadend=function(L0){var $0=btoa(L0.target.result||"");t1({__local_forage_encoded_blob:!0,data:$0,type:E1.type})},C0.readAsBinaryString(E1)})}function y1(E1){var t1=j(atob(E1.data));return X([t1],{type:E1.type})}function W0(E1){return E1&&E1.__local_forage_encoded_blob}function F0(E1){var t1=this,d1=t1._initReady().then(function(){var C0=N[t1._dbInfo.name];if(C0&&C0.dbReady)return C0.dbReady});return K(d1,E1,E1),d1}function g1(E1){c(E1);var t1=N[E1.name],d1=t1.forages;for(var C0=0;C0<d1.length;C0++){var L0=d1[C0];if(L0._dbInfo.db)L0._dbInfo.db.close(),L0._dbInfo.db=null}return E1.db=null,x(E1).then(function($0){if(E1.db=$0,W1(E1))return e(E1);return $0}).then(function($0){E1.db=t1.db=$0;for(var QA=0;QA<d1.length;QA++)d1[QA]._dbInfo.db=$0}).catch(function($0){throw n(E1,$0),$0})}function K1(E1,t1,d1,C0){if(C0===void 0)C0=1;try{var L0=E1.db.transaction(E1.storeName,t1);d1(null,L0)}catch($0){if(C0>0&&(!E1.db||$0.name==="InvalidStateError"||$0.name==="NotFoundError"))return V.resolve().then(function(){if(!E1.db||$0.name==="NotFoundError"&&!E1.db.objectStoreNames.contains(E1.storeName)&&E1.version<=E1.db.version){if(E1.db)E1.version=E1.db.version+1;return e(E1)}}).then(function(){return g1(E1).then(function(){K1(E1,t1,d1,C0-1)})}).catch(d1);d1($0)}}function G1(){return{forages:[],db:null,dbReady:null,deferredOperations:[]}}function L1(E1){var t1=this,d1={db:null};if(E1)for(var C0 in E1)d1[C0]=E1[C0];var L0=N[d1.name];if(!L0)L0=G1(),N[d1.name]=L0;if(L0.forages.push(t1),!t1._initReady)t1._initReady=t1.ready,t1.ready=F0;var $0=[];function QA(){return V.resolve()}for(var h0=0;h0<L0.forages.length;h0++){var e0=L0.forages[h0];if(e0!==t1)$0.push(e0._initReady().catch(QA))}var XA=L0.forages.slice(0);return V.all($0).then(function(){return d1.db=L0.db,x(d1)}).then(function(HA){if(d1.db=HA,W1(d1,t1._defaultConfig.version))return e(d1);return HA}).then(function(HA){d1.db=L0.db=HA,t1._dbInfo=d1;for(var iA=0;iA<XA.length;iA++){var h2=XA[iA];if(h2!==t1)h2._dbInfo.db=d1.db,h2._dbInfo.version=d1.version}})}function M1(E1,t1){var d1=this;E1=H(E1);var C0=new V(function(L0,$0){d1.ready().then(function(){K1(d1._dbInfo,R,function(QA,h0){if(QA)return $0(QA);try{var e0=h0.objectStore(d1._dbInfo.storeName),XA=e0.get(E1);XA.onsuccess=function(){var HA=XA.result;if(HA===void 0)HA=null;if(W0(HA))HA=y1(HA);L0(HA)},XA.onerror=function(){$0(XA.error)}}catch(HA){$0(HA)}})}).catch($0)});return C(C0,t1),C0}function a1(E1,t1){var d1=this,C0=new V(function(L0,$0){d1.ready().then(function(){K1(d1._dbInfo,R,function(QA,h0){if(QA)return $0(QA);try{var e0=h0.objectStore(d1._dbInfo.storeName),XA=e0.openCursor(),HA=1;XA.onsuccess=function(){var iA=XA.result;if(iA){var h2=iA.value;if(W0(h2))h2=y1(h2);var vB=E1(h2,iA.key,HA++);if(vB!==void 0)L0(vB);else iA.continue()}else L0()},XA.onerror=function(){$0(XA.error)}}catch(iA){$0(iA)}})}).catch($0)});return C(C0,t1),C0}function i1(E1,t1,d1){var C0=this;E1=H(E1);var L0=new V(function($0,QA){var h0;C0.ready().then(function(){if(h0=C0._dbInfo,O.call(t1)==="[object Blob]")return k(h0.db).then(function(e0){if(e0)return t1;return U1(t1)});return t1}).then(function(e0){K1(C0._dbInfo,T,function(XA,HA){if(XA)return QA(XA);try{var iA=HA.objectStore(C0._dbInfo.storeName);if(e0===null)e0=void 0;var h2=iA.put(e0,E1);HA.oncomplete=function(){if(e0===void 0)e0=null;$0(e0)},HA.onabort=HA.onerror=function(){var vB=h2.error?h2.error:h2.transaction.error;QA(vB)}}catch(vB){QA(vB)}})}).catch(QA)});return C(L0,d1),L0}function E0(E1,t1){var d1=this;E1=H(E1);var C0=new V(function(L0,$0){d1.ready().then(function(){K1(d1._dbInfo,T,function(QA,h0){if(QA)return $0(QA);try{var e0=h0.objectStore(d1._dbInfo.storeName),XA=e0.delete(E1);h0.oncomplete=function(){L0()},h0.onerror=function(){$0(XA.error)},h0.onabort=function(){var HA=XA.error?XA.error:XA.transaction.error;$0(HA)}}catch(HA){$0(HA)}})}).catch($0)});return C(C0,t1),C0}function B1(E1){var t1=this,d1=new V(function(C0,L0){t1.ready().then(function(){K1(t1._dbInfo,T,function($0,QA){if($0)return L0($0);try{var h0=QA.objectStore(t1._dbInfo.storeName),e0=h0.clear();QA.oncomplete=function(){C0()},QA.onabort=QA.onerror=function(){var XA=e0.error?e0.error:e0.transaction.error;L0(XA)}}catch(XA){L0(XA)}})}).catch(L0)});return C(d1,E1),d1}function A1(E1){var t1=this,d1=new V(function(C0,L0){t1.ready().then(function(){K1(t1._dbInfo,R,function($0,QA){if($0)return L0($0);try{var h0=QA.objectStore(t1._dbInfo.storeName),e0=h0.count();e0.onsuccess=function(){C0(e0.result)},e0.onerror=function(){L0(e0.error)}}catch(XA){L0(XA)}})}).catch(L0)});return C(d1,E1),d1}function I1(E1,t1){var d1=this,C0=new V(function(L0,$0){if(E1<0){L0(null);return}d1.ready().then(function(){K1(d1._dbInfo,R,function(QA,h0){if(QA)return $0(QA);try{var e0=h0.objectStore(d1._dbInfo.storeName),XA=!1,HA=e0.openKeyCursor();HA.onsuccess=function(){var iA=HA.result;if(!iA){L0(null);return}if(E1===0)L0(iA.key);else if(!XA)XA=!0,iA.advance(E1);else L0(iA.key)},HA.onerror=function(){$0(HA.error)}}catch(iA){$0(iA)}})}).catch($0)});return C(C0,t1),C0}function q1(E1){var t1=this,d1=new V(function(C0,L0){t1.ready().then(function(){K1(t1._dbInfo,R,function($0,QA){if($0)return L0($0);try{var h0=QA.objectStore(t1._dbInfo.storeName),e0=h0.openKeyCursor(),XA=[];e0.onsuccess=function(){var HA=e0.result;if(!HA){C0(XA);return}XA.push(HA.key),HA.continue()},e0.onerror=function(){L0(e0.error)}}catch(HA){L0(HA)}})}).catch(L0)});return C(d1,E1),d1}function P1(E1,t1){t1=z.apply(this,arguments);var d1=this.config();if(E1=typeof E1!=="function"&&E1||{},!E1.name)E1.name=E1.name||d1.name,E1.storeName=E1.storeName||d1.storeName;var C0=this,L0;if(!E1.name)L0=V.reject("Invalid arguments");else{var $0=E1.name===d1.name&&C0._dbInfo.db,QA=$0?V.resolve(C0._dbInfo.db):x(E1).then(function(h0){var e0=N[E1.name],XA=e0.forages;e0.db=h0;for(var HA=0;HA<XA.length;HA++)XA[HA]._dbInfo.db=h0;return h0});if(!E1.storeName)L0=QA.then(function(h0){c(E1);var e0=N[E1.name],XA=e0.forages;h0.close();for(var HA=0;HA<XA.length;HA++){var iA=XA[HA];iA._dbInfo.db=null}var h2=new V(function(vB,v9){var FQ=W.deleteDatabase(E1.name);FQ.onerror=function(){var qQ=FQ.result;if(qQ)qQ.close();v9(FQ.error)},FQ.onblocked=function(){console.warn('dropInstance blocked for database "'+E1.name+'" until all open connections are closed')},FQ.onsuccess=function(){var qQ=FQ.result;if(qQ)qQ.close();vB(qQ)}});return h2.then(function(vB){e0.db=vB;for(var v9=0;v9<XA.length;v9++){var FQ=XA[v9];h(FQ._dbInfo)}}).catch(function(vB){throw(n(E1,vB)||V.resolve()).catch(function(){}),vB})});else L0=QA.then(function(h0){if(!h0.objectStoreNames.contains(E1.storeName))return;var e0=h0.version+1;c(E1);var XA=N[E1.name],HA=XA.forages;h0.close();for(var iA=0;iA<HA.length;iA++){var h2=HA[iA];h2._dbInfo.db=null,h2._dbInfo.version=e0}var vB=new V(function(v9,FQ){var qQ=W.open(E1.name,e0);qQ.onerror=function(o8){var u6=qQ.result;u6.close(),FQ(o8)},qQ.onupgradeneeded=function(){var o8=qQ.result;o8.deleteObjectStore(E1.storeName)},qQ.onsuccess=function(){var o8=qQ.result;o8.close(),v9(o8)}});return vB.then(function(v9){XA.db=v9;for(var FQ=0;FQ<HA.length;FQ++){var qQ=HA[FQ];qQ._dbInfo.db=v9,h(qQ._dbInfo)}}).catch(function(v9){throw(n(E1,v9)||V.resolve()).catch(function(){}),v9})})}return C(L0,t1),L0}var Q1={_driver:"asyncStorage",_initStorage:L1,_support:J(),iterate:a1,getItem:M1,setItem:i1,removeItem:E0,clear:B1,length:A1,key:I1,keys:q1,dropInstance:P1};function f1(){return typeof openDatabase==="function"}var l1="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n1="~~local_forage_type~",V0=/^~~local_forage_type~([^~]+)~/,I0="__lfsc__:",M0=I0.length,YA="arbf",m0="blob",SA="si08",v2="ui08",Y2="uic8",N2="si16",b2="si32",_B="ur16",W4="ui32",gA="fl32",X2="fl64",L2=M0+YA.length,lA=Object.prototype.toString;function uA(E1){var t1=E1.length*0.75,d1=E1.length,C0,L0=0,$0,QA,h0,e0;if(E1[E1.length-1]==="="){if(t1--,E1[E1.length-2]==="=")t1--}var XA=new ArrayBuffer(t1),HA=new Uint8Array(XA);for(C0=0;C0<d1;C0+=4)$0=l1.indexOf(E1[C0]),QA=l1.indexOf(E1[C0+1]),h0=l1.indexOf(E1[C0+2]),e0=l1.indexOf(E1[C0+3]),HA[L0++]=$0<<2|QA>>4,HA[L0++]=(QA&15)<<4|h0>>2,HA[L0++]=(h0&3)<<6|e0&63;return XA}function r2(E1){var t1=new Uint8Array(E1),d1="",C0;for(C0=0;C0<t1.length;C0+=3)d1+=l1[t1[C0]>>2],d1+=l1[(t1[C0]&3)<<4|t1[C0+1]>>4],d1+=l1[(t1[C0+1]&15)<<2|t1[C0+2]>>6],d1+=l1[t1[C0+2]&63];if(t1.length%3===2)d1=d1.substring(0,d1.length-1)+"=";else if(t1.length%3===1)d1=d1.substring(0,d1.length-2)+"==";return d1}function gB(E1,t1){var d1="";if(E1)d1=lA.call(E1);if(E1&&(d1==="[object ArrayBuffer]"||E1.buffer&&lA.call(E1.buffer)==="[object ArrayBuffer]")){var C0,L0=I0;if(E1 instanceof ArrayBuffer)C0=E1,L0+=YA;else if(C0=E1.buffer,d1==="[object Int8Array]")L0+=SA;else if(d1==="[object Uint8Array]")L0+=v2;else if(d1==="[object Uint8ClampedArray]")L0+=Y2;else if(d1==="[object Int16Array]")L0+=N2;else if(d1==="[object Uint16Array]")L0+=_B;else if(d1==="[object Int32Array]")L0+=b2;else if(d1==="[object Uint32Array]")L0+=W4;else if(d1==="[object Float32Array]")L0+=gA;else if(d1==="[object Float64Array]")L0+=X2;else t1(new Error("Failed to get type for BinaryArray"));t1(L0+r2(C0))}else if(d1==="[object Blob]"){var $0=new FileReader;$0.onload=function(){var QA=n1+E1.type+"~"+r2(this.result);t1(I0+m0+QA)},$0.readAsArrayBuffer(E1)}else try{t1(JSON.stringify(E1))}catch(QA){console.error("Couldn't convert value into a JSON string: ",E1),t1(null,QA)}}function g6(E1){if(E1.substring(0,M0)!==I0)return JSON.parse(E1);var t1=E1.substring(L2),d1=E1.substring(M0,L2),C0;if(d1===m0&&V0.test(t1)){var L0=t1.match(V0);C0=L0[1],t1=t1.substring(L0[0].length)}var $0=uA(t1);switch(d1){case YA:return $0;case m0:return X([$0],{type:C0});case SA:return new Int8Array($0);case v2:return new Uint8Array($0);case Y2:return new Uint8ClampedArray($0);case N2:return new Int16Array($0);case _B:return new Uint16Array($0);case b2:return new Int32Array($0);case W4:return new Uint32Array($0);case gA:return new Float32Array($0);case X2:return new Float64Array($0);default:throw new Error("Unkown type: "+d1)}}var k7={serialize:gB,deserialize:g6,stringToBuffer:uA,bufferToString:r2};function O4(E1,t1,d1,C0){E1.executeSql("CREATE TABLE IF NOT EXISTS "+t1.storeName+" (id INTEGER PRIMARY KEY, key unique, value)",[],d1,C0)}function GB(E1){var t1=this,d1={db:null};if(E1)for(var C0 in E1)d1[C0]=typeof E1[C0]!=="string"?E1[C0].toString():E1[C0];var L0=new V(function($0,QA){try{d1.db=openDatabase(d1.name,String(d1.version),d1.description,d1.size)}catch(h0){return QA(h0)}d1.db.transaction(function(h0){O4(h0,d1,function(){t1._dbInfo=d1,$0()},function(e0,XA){QA(XA)})},QA)});return d1.serializer=k7,L0}function T4(E1,t1,d1,C0,L0,$0){E1.executeSql(d1,C0,L0,function(QA,h0){if(h0.code===h0.SYNTAX_ERR)QA.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name = ?",[t1.storeName],function(e0,XA){if(!XA.rows.length)O4(e0,t1,function(){e0.executeSql(d1,C0,L0,$0)},$0);else $0(e0,h0)},$0);else $0(QA,h0)},$0)}function d3(E1,t1){var d1=this;E1=H(E1);var C0=new V(function(L0,$0){d1.ready().then(function(){var QA=d1._dbInfo;QA.db.transaction(function(h0){T4(h0,QA,"SELECT * FROM "+QA.storeName+" WHERE key = ? LIMIT 1",[E1],function(e0,XA){var HA=XA.rows.length?XA.rows.item(0).value:null;if(HA)HA=QA.serializer.deserialize(HA);L0(HA)},function(e0,XA){$0(XA)})})}).catch($0)});return C(C0,t1),C0}function a5(E1,t1){var d1=this,C0=new V(function(L0,$0){d1.ready().then(function(){var QA=d1._dbInfo;QA.db.transaction(function(h0){T4(h0,QA,"SELECT * FROM "+QA.storeName,[],function(e0,XA){var HA=XA.rows,iA=HA.length;for(var h2=0;h2<iA;h2++){var vB=HA.item(h2),v9=vB.value;if(v9)v9=QA.serializer.deserialize(v9);if(v9=E1(v9,vB.key,h2+1),v9!==void 0){L0(v9);return}}L0()},function(e0,XA){$0(XA)})})}).catch($0)});return C(C0,t1),C0}function O8(E1,t1,d1,C0){var L0=this;E1=H(E1);var $0=new V(function(QA,h0){L0.ready().then(function(){if(t1===void 0)t1=null;var e0=t1,XA=L0._dbInfo;XA.serializer.serialize(t1,function(HA,iA){if(iA)h0(iA);else XA.db.transaction(function(h2){T4(h2,XA,"INSERT OR REPLACE INTO "+XA.storeName+" (key, value) VALUES (?, ?)",[E1,HA],function(){QA(e0)},function(vB,v9){h0(v9)})},function(h2){if(h2.code===h2.QUOTA_ERR){if(C0>0){QA(O8.apply(L0,[E1,e0,d1,C0-1]));return}h0(h2)}})})}).catch(h0)});return C($0,d1),$0}function U5(E1,t1,d1){return O8.apply(this,[E1,t1,d1,1])}function s5(E1,t1){var d1=this;E1=H(E1);var C0=new V(function(L0,$0){d1.ready().then(function(){var QA=d1._dbInfo;QA.db.transaction(function(h0){T4(h0,QA,"DELETE FROM "+QA.storeName+" WHERE key = ?",[E1],function(){L0()},function(e0,XA){$0(XA)})})}).catch($0)});return C(C0,t1),C0}function y7(E1){var t1=this,d1=new V(function(C0,L0){t1.ready().then(function(){var $0=t1._dbInfo;$0.db.transaction(function(QA){T4(QA,$0,"DELETE FROM "+$0.storeName,[],function(){C0()},function(h0,e0){L0(e0)})})}).catch(L0)});return C(d1,E1),d1}function _7(E1){var t1=this,d1=new V(function(C0,L0){t1.ready().then(function(){var $0=t1._dbInfo;$0.db.transaction(function(QA){T4(QA,$0,"SELECT COUNT(key) as c FROM "+$0.storeName,[],function(h0,e0){var XA=e0.rows.item(0).c;C0(XA)},function(h0,e0){L0(e0)})})}).catch(L0)});return C(d1,E1),d1}function pA(E1,t1){var d1=this,C0=new V(function(L0,$0){d1.ready().then(function(){var QA=d1._dbInfo;QA.db.transaction(function(h0){T4(h0,QA,"SELECT key FROM "+QA.storeName+" WHERE id = ? LIMIT 1",[E1+1],function(e0,XA){var HA=XA.rows.length?XA.rows.item(0).key:null;L0(HA)},function(e0,XA){$0(XA)})})}).catch($0)});return C(C0,t1),C0}function V2(E1){var t1=this,d1=new V(function(C0,L0){t1.ready().then(function(){var $0=t1._dbInfo;$0.db.transaction(function(QA){T4(QA,$0,"SELECT key FROM "+$0.storeName,[],function(h0,e0){var XA=[];for(var HA=0;HA<e0.rows.length;HA++)XA.push(e0.rows.item(HA).key);C0(XA)},function(h0,e0){L0(e0)})})}).catch(L0)});return C(d1,E1),d1}function _9(E1){return new V(function(t1,d1){E1.transaction(function(C0){C0.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'",[],function(L0,$0){var QA=[];for(var h0=0;h0<$0.rows.length;h0++)QA.push($0.rows.item(h0).name);t1({db:E1,storeNames:QA})},function(L0,$0){d1($0)})},function(C0){d1(C0)})})}function w5(E1,t1){t1=z.apply(this,arguments);var d1=this.config();if(E1=typeof E1!=="function"&&E1||{},!E1.name)E1.name=E1.name||d1.name,E1.storeName=E1.storeName||d1.storeName;var C0=this,L0;if(!E1.name)L0=V.reject("Invalid arguments");else L0=new V(function($0){var QA;if(E1.name===d1.name)QA=C0._dbInfo.db;else QA=openDatabase(E1.name,"","",0);if(!E1.storeName)$0(_9(QA));else $0({db:QA,storeNames:[E1.storeName]})}).then(function($0){return new V(function(QA,h0){$0.db.transaction(function(e0){function XA(vB){return new V(function(v9,FQ){e0.executeSql("DROP TABLE IF EXISTS "+vB,[],function(){v9()},function(qQ,o8){FQ(o8)})})}var HA=[];for(var iA=0,h2=$0.storeNames.length;iA<h2;iA++)HA.push(XA($0.storeNames[iA]));V.all(HA).then(function(){QA()}).catch(function(vB){h0(vB)})},function(e0){h0(e0)})})});return C(L0,t1),L0}var Y0={_driver:"webSQLStorage",_initStorage:GB,_support:f1(),iterate:a5,getItem:d3,setItem:U5,removeItem:s5,clear:y7,length:_7,key:pA,keys:V2,dropInstance:w5};function k1(){try{return typeof localStorage!=="undefined"&&"setItem"in localStorage&&!!localStorage.setItem}catch(E1){return!1}}function Q0(E1,t1){var d1=E1.name+"/";if(E1.storeName!==t1.storeName)d1+=E1.storeName+"/";return d1}function u0(){var E1="_localforage_support_test";try{return localStorage.setItem(E1,!0),localStorage.removeItem(E1),!1}catch(t1){return!0}}function i0(){return!u0()||localStorage.length>0}function mA(E1){var t1=this,d1={};if(E1)for(var C0 in E1)d1[C0]=E1[C0];if(d1.keyPrefix=Q0(E1,t1._defaultConfig),!i0())return V.reject();return t1._dbInfo=d1,d1.serializer=k7,V.resolve()}function lB(E1){var t1=this,d1=t1.ready().then(function(){var C0=t1._dbInfo.keyPrefix;for(var L0=localStorage.length-1;L0>=0;L0--){var $0=localStorage.key(L0);if($0.indexOf(C0)===0)localStorage.removeItem($0)}});return C(d1,E1),d1}function x9(E1,t1){var d1=this;E1=H(E1);var C0=d1.ready().then(function(){var L0=d1._dbInfo,$0=localStorage.getItem(L0.keyPrefix+E1);if($0)$0=L0.serializer.deserialize($0);return $0});return C(C0,t1),C0}function zQ(E1,t1){var d1=this,C0=d1.ready().then(function(){var L0=d1._dbInfo,$0=L0.keyPrefix,QA=$0.length,h0=localStorage.length,e0=1;for(var XA=0;XA<h0;XA++){var HA=localStorage.key(XA);if(HA.indexOf($0)!==0)continue;var iA=localStorage.getItem(HA);if(iA)iA=L0.serializer.deserialize(iA);if(iA=E1(iA,HA.substring(QA),e0++),iA!==void 0)return iA}});return C(C0,t1),C0}function q4(E1,t1){var d1=this,C0=d1.ready().then(function(){var L0=d1._dbInfo,$0;try{$0=localStorage.key(E1)}catch(QA){$0=null}if($0)$0=$0.substring(L0.keyPrefix.length);return $0});return C(C0,t1),C0}function xB(E1){var t1=this,d1=t1.ready().then(function(){var C0=t1._dbInfo,L0=localStorage.length,$0=[];for(var QA=0;QA<L0;QA++){var h0=localStorage.key(QA);if(h0.indexOf(C0.keyPrefix)===0)$0.push(h0.substring(C0.keyPrefix.length))}return $0});return C(d1,E1),d1}function $Q(E1){var t1=this,d1=t1.keys().then(function(C0){return C0.length});return C(d1,E1),d1}function z6(E1,t1){var d1=this;E1=H(E1);var C0=d1.ready().then(function(){var L0=d1._dbInfo;localStorage.removeItem(L0.keyPrefix+E1)});return C(C0,t1),C0}function oQ(E1,t1,d1){var C0=this;E1=H(E1);var L0=C0.ready().then(function(){if(t1===void 0)t1=null;var $0=t1;return new V(function(QA,h0){var e0=C0._dbInfo;e0.serializer.serialize(t1,function(XA,HA){if(HA)h0(HA);else try{localStorage.setItem(e0.keyPrefix+E1,XA),QA($0)}catch(iA){if(iA.name==="QuotaExceededError"||iA.name==="NS_ERROR_DOM_QUOTA_REACHED")h0(iA);h0(iA)}})})});return C(L0,d1),L0}function U9(E1,t1){if(t1=z.apply(this,arguments),E1=typeof E1!=="function"&&E1||{},!E1.name){var d1=this.config();E1.name=E1.name||d1.name,E1.storeName=E1.storeName||d1.storeName}var C0=this,L0;if(!E1.name)L0=V.reject("Invalid arguments");else L0=new V(function($0){if(!E1.storeName)$0(E1.name+"/");else $0(Q0(E1,C0._defaultConfig))}).then(function($0){for(var QA=localStorage.length-1;QA>=0;QA--){var h0=localStorage.key(QA);if(h0.indexOf($0)===0)localStorage.removeItem(h0)}});return C(L0,t1),L0}var J4={_driver:"localStorageWrapper",_initStorage:mA,_support:k1(),iterate:zQ,getItem:x9,setItem:oQ,removeItem:z6,clear:lB,length:$Q,key:q4,keys:xB,dropInstance:U9},_1=function E1(t1,d1){return t1===d1||typeof t1==="number"&&typeof d1==="number"&&isNaN(t1)&&isNaN(d1)},u1=function E1(t1,d1){var C0=t1.length,L0=0;while(L0<C0){if(_1(t1[L0],d1))return!0;L0++}return!1},q0=Array.isArray||function(E1){return Object.prototype.toString.call(E1)==="[object Array]"},y0={},U0={},v0={INDEXEDDB:Q1,WEBSQL:Y0,LOCALSTORAGE:J4},EA=[v0.INDEXEDDB._driver,v0.WEBSQL._driver,v0.LOCALSTORAGE._driver],ZA=["dropInstance"],VA=["clear","getItem","iterate","key","keys","length","removeItem","setItem"].concat(ZA),AA={description:"",driver:EA.slice(),name:"localforage",size:4980736,storeName:"keyvaluepairs",version:1};function UA(E1,t1){E1[t1]=function(){var d1=arguments;return E1.ready().then(function(){return E1[t1].apply(E1,d1)})}}function uB(){for(var E1=1;E1<arguments.length;E1++){var t1=arguments[E1];if(t1){for(var d1 in t1)if(t1.hasOwnProperty(d1))if(q0(t1[d1]))arguments[0][d1]=t1[d1].slice();else arguments[0][d1]=t1[d1]}}return arguments[0]}var f2=function(){function E1(t1){I(this,E1);for(var d1 in v0)if(v0.hasOwnProperty(d1)){var C0=v0[d1],L0=C0._driver;if(this[d1]=L0,!y0[L0])this.defineDriver(C0)}this._defaultConfig=uB({},AA),this._config=uB({},this._defaultConfig,t1),this._driverSet=null,this._initDriver=null,this._ready=!1,this._dbInfo=null,this._wrapLibraryMethodsWithReady(),this.setDriver(this._config.driver).catch(function(){})}return E1.prototype.config=function t1(d1){if((typeof d1==="undefined"?"undefined":F(d1))==="object"){if(this._ready)return new Error("Can't call config() after localforage has been used.");for(var C0 in d1){if(C0==="storeName")d1[C0]=d1[C0].replace(/\W/g,"_");if(C0==="version"&&typeof d1[C0]!=="number")return new Error("Database version must be a number.");this._config[C0]=d1[C0]}if("driver"in d1&&d1.driver)return this.setDriver(this._config.driver);return!0}else if(typeof d1==="string")return this._config[d1];else return this._config},E1.prototype.defineDriver=function t1(d1,C0,L0){var $0=new V(function(QA,h0){try{var e0=d1._driver,XA=new Error("Custom driver not compliant; see https://mozilla.github.io/localForage/#definedriver");if(!d1._driver){h0(XA);return}var HA=VA.concat("_initStorage");for(var iA=0,h2=HA.length;iA<h2;iA++){var vB=HA[iA],v9=!u1(ZA,vB);if((v9||d1[vB])&&typeof d1[vB]!=="function"){h0(XA);return}}var FQ=function o8(){var u6=function BF(uF){return function(){var SQ=new Error("Method "+uF+" is not implemented by the current driver"),JG=V.reject(SQ);return C(JG,arguments[arguments.length-1]),JG}};for(var A6=0,lD=ZA.length;A6<lD;A6++){var y5=ZA[A6];if(!d1[y5])d1[y5]=u6(y5)}};FQ();var qQ=function o8(u6){if(y0[e0])console.info("Redefining LocalForage driver: "+e0);y0[e0]=d1,U0[e0]=u6,QA()};if("_support"in d1)if(d1._support&&typeof d1._support==="function")d1._support().then(qQ,h0);else qQ(!!d1._support);else qQ(!0)}catch(o8){h0(o8)}});return K($0,C0,L0),$0},E1.prototype.driver=function t1(){return this._driver||null},E1.prototype.getDriver=function t1(d1,C0,L0){var $0=y0[d1]?V.resolve(y0[d1]):V.reject(new Error("Driver not found."));return K($0,C0,L0),$0},E1.prototype.getSerializer=function t1(d1){var C0=V.resolve(k7);return K(C0,d1),C0},E1.prototype.ready=function t1(d1){var C0=this,L0=C0._driverSet.then(function(){if(C0._ready===null)C0._ready=C0._initDriver();return C0._ready});return K(L0,d1,d1),L0},E1.prototype.setDriver=function t1(d1,C0,L0){var $0=this;if(!q0(d1))d1=[d1];var QA=this._getSupportedDrivers(d1);function h0(){$0._config.driver=$0.driver()}function e0(iA){return $0._extend(iA),h0(),$0._ready=$0._initStorage($0._config),$0._ready}function XA(iA){return function(){var h2=0;function vB(){while(h2<iA.length){var v9=iA[h2];return h2++,$0._dbInfo=null,$0._ready=null,$0.getDriver(v9).then(e0).catch(vB)}h0();var FQ=new Error("No available storage method found.");return $0._driverSet=V.reject(FQ),$0._driverSet}return vB()}}var HA=this._driverSet!==null?this._driverSet.catch(function(){return V.resolve()}):V.resolve();return this._driverSet=HA.then(function(){var iA=QA[0];return $0._dbInfo=null,$0._ready=null,$0.getDriver(iA).then(function(h2){$0._driver=h2._driver,h0(),$0._wrapLibraryMethodsWithReady(),$0._initDriver=XA(QA)})}).catch(function(){h0();var iA=new Error("No available storage method found.");return $0._driverSet=V.reject(iA),$0._driverSet}),K(this._driverSet,C0,L0),this._driverSet},E1.prototype.supports=function t1(d1){return!!U0[d1]},E1.prototype._extend=function t1(d1){uB(this,d1)},E1.prototype._getSupportedDrivers=function t1(d1){var C0=[];for(var L0=0,$0=d1.length;L0<$0;L0++){var QA=d1[L0];if(this.supports(QA))C0.push(QA)}return C0},E1.prototype._wrapLibraryMethodsWithReady=function t1(){for(var d1=0,C0=VA.length;d1<C0;d1++)UA(this,VA[d1])},E1.prototype.createInstance=function t1(d1){return new E1(d1)},E1}(),HB=new f2;Z.exports=HB},{"3":3}]},{},[4])(4)})});
var ru1=E((dk0)=>{Object.defineProperty(dk0,"__esModule",{value:!0});var V19=mq(),C19=vH(),K19=lU(),H19="baggage",su1="sentry-",uk0=/^sentry-/,mk0=8192;function z19(A){if(!C19.isString(A)&&!Array.isArray(A))return;let B={};if(Array.isArray(A))B=A.reduce((D,Z)=>{let G=gk0(Z);for(let F of Object.keys(G))D[F]=G[F];return D},{});else{if(!A)return;B=gk0(A)}let Q=Object.entries(B).reduce((D,[Z,G])=>{if(Z.match(uk0)){let F=Z.slice(su1.length);D[F]=G}return D},{});if(Object.keys(Q).length>0)return Q;else return}function E19(A){if(!A)return;let B=Object.entries(A).reduce((Q,[D,Z])=>{if(Z)Q[`${su1}${D}`]=Z;return Q},{});return U19(B)}function gk0(A){return A.split(",").map((B)=>B.split("=").map((Q)=>decodeURIComponent(Q.trim()))).reduce((B,[Q,D])=>{return B[Q]=D,B},{})}function U19(A){if(Object.keys(A).length===0)return;return Object.entries(A).reduce((B,[Q,D],Z)=>{let G=`${encodeURIComponent(Q)}=${encodeURIComponent(D)}`,F=Z===0?G:`${B},${G}`;if(F.length>mk0)return V19.DEBUG_BUILD&&K19.logger.warn(`Not adding key: ${Q} with val: ${D} to baggage header due to exceeding baggage size limits.`),B;else return F},"")}dk0.BAGGAGE_HEADER_NAME=H19;dk0.MAX_BAGGAGE_STRING_LENGTH=mk0;dk0.SENTRY_BAGGAGE_KEY_PREFIX=su1;dk0.SENTRY_BAGGAGE_KEY_PREFIX_REGEX=uk0;dk0.baggageHeaderToDynamicSamplingContext=z19;dk0.dynamicSamplingContextToSentryBaggageHeader=E19});
var sg0=E((ag0)=>{Object.defineProperty(ag0,"__esModule",{value:!0});var aJ1=J1("fs"),ld1=J1("path");function WK9(A){let B=ld1.resolve(A);if(!aJ1.existsSync(B))throw new Error(`Cannot read contents of ${B}. Directory does not exist.`);if(!aJ1.statSync(B).isDirectory())throw new Error(`Cannot read contents of ${B}, because it is not a directory.`);let Q=(D)=>{return aJ1.readdirSync(D).reduce((Z,G)=>{let F=ld1.join(D,G);if(aJ1.statSync(F).isDirectory())return Z.concat(Q(F));return Z.push(F),Z},[])};return Q(B).map((D)=>ld1.relative(B,D))}ag0.deepReadDirSync=WK9});
var sm0=E((am0)=>{Object.defineProperty(am0,"__esModule",{value:!0});var pm0=OQ(),im0=AX1(),nm0="Automatic instrumentation of CronJob only supports crontab string";function OE9(A,B){let Q=!1;return new Proxy(A,{construct(D,Z){let[G,F,I,Y,W,...J]=Z;if(typeof G!=="string")throw new Error(nm0);if(Q)throw new Error(`A job named '${B}' has already been scheduled`);Q=!0;let X=im0.replaceCronNames(G);function V(C,K){return pm0.withMonitor(B,()=>{return F(C,K)},{schedule:{type:"crontab",value:X},timezone:W||void 0})}return new D(G,V,I,Y,W,...J)},get(D,Z){if(Z==="from")return(G)=>{let{cronTime:F,onTick:I,timeZone:Y}=G;if(typeof F!=="string")throw new Error(nm0);if(Q)throw new Error(`A job named '${B}' has already been scheduled`);Q=!0;let W=im0.replaceCronNames(F);return G.onTick=(J,X)=>{return pm0.withMonitor(B,()=>{return I(J,X)},{schedule:{type:"crontab",value:W},timezone:Y||void 0})},D.from(G)};else return D[Z]}})}am0.instrumentCron=OE9});
var tm0=E((om0)=>{var{_optionalChain:rm0}=$A();Object.defineProperty(om0,"__esModule",{value:!0});var PE9=OQ(),SE9=AX1();function jE9(A){return new Proxy(A,{get(B,Q){if(Q==="schedule"&&B.schedule)return new Proxy(B.schedule,{apply(D,Z,G){let[F,,I]=G;if(!rm0([I,"optionalAccess",(Y)=>Y.name]))throw new Error('Missing "name" for scheduled job. A name is required for Sentry check-in monitoring.');return PE9.withMonitor(I.name,()=>{return D.apply(Z,G)},{schedule:{type:"crontab",value:SE9.replaceCronNames(F)},timezone:rm0([I,"optionalAccess",(Y)=>Y.timezone])})}});else return B[Q]}})}om0.instrumentNodeCron=jE9});
var tu0=E((ou0)=>{Object.defineProperty(ou0,"__esModule",{value:!0});var AN=$A(),Cz9=ru0(),Nf=d21(),mj=AN.GLOBAL_OBJ;class c21{static __initStatic(){this.id="Offline"}constructor(A={}){this.name=c21.id,this.maxStoredEvents=A.maxStoredEvents||30,this.offlineEventStore=Cz9.createInstance({name:"sentry/offlineEventStore"})}setupOnce(A,B){if(this.hub=B(),"addEventListener"in mj)mj.addEventListener("online",()=>{this._sendEvents().catch(()=>{Nf.DEBUG_BUILD&&AN.logger.warn("could not send cached events")})});let Q=(D)=>{if(this.hub&&this.hub.getIntegration(c21)){if("navigator"in mj&&"onLine"in mj.navigator&&!mj.navigator.onLine)return Nf.DEBUG_BUILD&&AN.logger.log("Event dropped due to being a offline - caching instead"),this._cacheEvent(D).then((Z)=>this._enforceMaxEvents()).catch((Z)=>{Nf.DEBUG_BUILD&&AN.logger.warn("could not cache event while offline")}),null}return D};if(Q.id=this.name,A(Q),"navigator"in mj&&"onLine"in mj.navigator&&mj.navigator.onLine)this._sendEvents().catch(()=>{Nf.DEBUG_BUILD&&AN.logger.warn("could not send cached events")})}async _cacheEvent(A){return this.offlineEventStore.setItem(AN.uuid4(),AN.normalize(A))}async _enforceMaxEvents(){let A=[];return this.offlineEventStore.iterate((B,Q,D)=>{A.push({cacheKey:Q,event:B})}).then(()=>this._purgeEvents(A.sort((B,Q)=>(Q.event.timestamp||0)-(B.event.timestamp||0)).slice(this.maxStoredEvents<A.length?this.maxStoredEvents:A.length).map((B)=>B.cacheKey))).catch((B)=>{Nf.DEBUG_BUILD&&AN.logger.warn("could not enforce max events")})}async _purgeEvent(A){return this.offlineEventStore.removeItem(A)}async _purgeEvents(A){return Promise.all(A.map((B)=>this._purgeEvent(B))).then()}async _sendEvents(){return this.offlineEventStore.iterate((A,B,Q)=>{if(this.hub)this.hub.captureEvent(A),this._purgeEvent(B).catch((D)=>{Nf.DEBUG_BUILD&&AN.logger.warn("could not purge event from cache")});else Nf.DEBUG_BUILD&&AN.logger.warn("no hub found - could not send cached event")})}}c21.__initStatic();ou0.Offline=c21});
var u21=E((th0)=>{Object.defineProperty(th0,"__esModule",{value:!0});var FV9=typeof __SENTRY_DEBUG__==="undefined"||__SENTRY_DEBUG__;th0.DEBUG_BUILD=FV9});
var uu1=E((Dk0,wW1)=>{Object.defineProperty(Dk0,"__esModule",{value:!0});var vtB=gu1();function btB(){return!vtB.isBrowserBundle()&&Object.prototype.toString.call(typeof process!=="undefined"?process:0)==="[object process]"}function UW1(A,B){return A.require(B)}function ftB(A){let B;try{B=UW1(wW1,A)}catch(Q){}try{let{cwd:Q}=UW1(wW1,"process");B=UW1(wW1,`${Q()}/node_modules/${A}`)}catch(Q){}return B}Dk0.dynamicRequire=UW1;Dk0.isNodeEnv=btB;Dk0.loadModule=ftB});
var vH=E((aS0)=>{Object.defineProperty(aS0,"__esModule",{value:!0});var pS0=Object.prototype.toString;function haB(A){switch(pS0.call(A)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return QW1(A,Error)}}function ac(A,B){return pS0.call(A)===`[object ${B}]`}function gaB(A){return ac(A,"ErrorEvent")}function uaB(A){return ac(A,"DOMError")}function maB(A){return ac(A,"DOMException")}function daB(A){return ac(A,"String")}function iS0(A){return typeof A==="object"&&A!==null&&"__sentry_template_string__"in A&&"__sentry_template_values__"in A}function caB(A){return A===null||iS0(A)||typeof A!=="object"&&typeof A!=="function"}function nS0(A){return ac(A,"Object")}function laB(A){return typeof Event!=="undefined"&&QW1(A,Event)}function paB(A){return typeof Element!=="undefined"&&QW1(A,Element)}function iaB(A){return ac(A,"RegExp")}function naB(A){return Boolean(A&&A.then&&typeof A.then==="function")}function aaB(A){return nS0(A)&&"nativeEvent"in A&&"preventDefault"in A&&"stopPropagation"in A}function saB(A){return typeof A==="number"&&A!==A}function QW1(A,B){try{return A instanceof B}catch(Q){return!1}}function raB(A){return!!(typeof A==="object"&&A!==null&&(A.__isVue||A._isVue))}aS0.isDOMError=uaB;aS0.isDOMException=maB;aS0.isElement=paB;aS0.isError=haB;aS0.isErrorEvent=gaB;aS0.isEvent=laB;aS0.isInstanceOf=QW1;aS0.isNaN=saB;aS0.isParameterizedString=iS0;aS0.isPlainObject=nS0;aS0.isPrimitive=caB;aS0.isRegExp=iaB;aS0.isString=daB;aS0.isSyntheticEvent=aaB;aS0.isThenable=naB;aS0.isVueViewModel=raB});
var vJ1=E((Fg0)=>{var{_optionalChain:ql}=$A();Object.defineProperty(Fg0,"__esModule",{value:!0});var SW=OQ(),vC=$A(),vd1=u21(),UV9=Cf(),m21=Qg0(),wV9=(A={})=>{let{breadcrumbs:B,tracing:Q,shouldCreateSpanForRequest:D}=A,Z={breadcrumbs:B,tracing:Q===!1?!1:vC.dropUndefinedKeys({enableIfHasTracingEnabled:Q===!0?void 0:!0,shouldCreateSpanForRequest:D})};return new zf(Z)},$V9=SW.defineIntegration(wV9);class zf{static __initStatic(){this.id="Http"}__init(){this.name=zf.id}constructor(A={}){zf.prototype.__init.call(this),this._breadcrumbs=typeof A.breadcrumbs==="undefined"?!0:A.breadcrumbs,this._tracing=!A.tracing?void 0:A.tracing===!0?{}:A.tracing}setupOnce(A,B){let Q=ql([B,"call",(Y)=>Y(),"access",(Y)=>Y.getClient,"call",(Y)=>Y(),"optionalAccess",(Y)=>Y.getOptions,"call",(Y)=>Y()]),D=Zg0(this._tracing,Q);if(!this._breadcrumbs&&!D)return;if(Q&&Q.instrumenter!=="sentry"){vd1.DEBUG_BUILD&&vC.logger.log("HTTP Integration is skipped because of instrumenter configuration.");return}let Z=Gg0(D,this._tracing,Q),G=ql([Q,"optionalAccess",(Y)=>Y.tracePropagationTargets])||ql([this,"access",(Y)=>Y._tracing,"optionalAccess",(Y)=>Y.tracePropagationTargets]),F=J1("http"),I=Dg0(F,this._breadcrumbs,Z,G);if(vC.fill(F,"get",I),vC.fill(F,"request",I),UV9.NODE_VERSION.major>8){let Y=J1("https"),W=Dg0(Y,this._breadcrumbs,Z,G);vC.fill(Y,"get",W),vC.fill(Y,"request",W)}}}zf.__initStatic();function Dg0(A,B,Q,D){let Z=new vC.LRUMap(100),G=new vC.LRUMap(100),F=(W)=>{if(Q===void 0)return!0;let J=Z.get(W);if(J!==void 0)return J;let X=Q(W);return Z.set(W,X),X},I=(W)=>{if(D===void 0)return!0;let J=G.get(W);if(J!==void 0)return J;let X=vC.stringMatchesSomePattern(W,D);return G.set(W,X),X};function Y(W,J,X,V){if(!SW.getCurrentHub().getIntegration(zf))return;SW.addBreadcrumb({category:"http",data:{status_code:V&&V.statusCode,...J},type:"http"},{event:W,request:X,response:V})}return function W(J){return function X(...V){let C=m21.normalizeRequestArgs(A,V),K=C[0],H=m21.extractRawUrl(K),z=m21.extractUrl(K),$=SW.getClient();if(SW.isSentryRequestUrl(z,$))return J.apply(A,C);let L=SW.getCurrentScope(),N=SW.getIsolationScope(),O=SW.getActiveSpan(),R=NV9(z,K),T=F(H)?ql([O,"optionalAccess",(j)=>j.startChild,"call",(j)=>j({op:"http.client",origin:"auto.http.node.http",description:`${R["http.method"]} ${R.url}`,data:R})]):void 0;if($&&I(H)){let{traceId:j,spanId:f,sampled:k,dsc:c}={...N.getPropagationContext(),...L.getPropagationContext()},h=T?SW.spanToTraceHeader(T):vC.generateSentryTraceHeader(j,f,k),n=vC.dynamicSamplingContextToSentryBaggageHeader(c||(T?SW.getDynamicSamplingContextFromSpan(T):SW.getDynamicSamplingContextFromClient(j,$,L)));qV9(K,z,h,n)}else vd1.DEBUG_BUILD&&vC.logger.log(`[Tracing] Not adding sentry-trace header to outgoing request (${z}) due to mismatching tracePropagationTargets option.`);return J.apply(A,C).once("response",function(j){let f=this;if(B)Y("response",R,f,j);if(T){if(j.statusCode)SW.setHttpStatus(T,j.statusCode);T.updateName(m21.cleanSpanDescription(SW.spanToJSON(T).description||"",K,f)||""),T.end()}}).once("error",function(){let j=this;if(B)Y("error",R,j);if(T)SW.setHttpStatus(T,500),T.updateName(m21.cleanSpanDescription(SW.spanToJSON(T).description||"",K,j)||""),T.end()})}}}function qV9(A,B,Q,D){if((A.headers||{})["sentry-trace"])return;vd1.DEBUG_BUILD&&vC.logger.log(`[Tracing] Adding sentry-trace header ${Q} to outgoing request to "${B}": `),A.headers={...A.headers,"sentry-trace":Q,...D&&D.length>0&&{baggage:LV9(A,D)}}}function NV9(A,B){let Q=B.method||"GET",D={url:A,"http.method":Q};if(B.hash)D["http.fragment"]=B.hash.substring(1);if(B.search)D["http.query"]=B.search.substring(1);return D}function LV9(A,B){if(!A.headers||!A.headers.baggage)return B;else if(!B)return A.headers.baggage;else if(Array.isArray(A.headers.baggage))return[...A.headers.baggage,B];return[A.headers.baggage,B]}function Zg0(A,B){return A===void 0?!1:A.enableIfHasTracingEnabled?SW.hasTracingEnabled(B):!0}function Gg0(A,B,Q){return A?ql([B,"optionalAccess",(Z)=>Z.shouldCreateSpanForRequest])||ql([Q,"optionalAccess",(Z)=>Z.shouldCreateSpanForRequest]):()=>!1}Fg0.Http=zf;Fg0._getShouldCreateSpanForRequest=Gg0;Fg0._shouldCreateSpans=Zg0;Fg0.httpIntegration=$V9});
var vf0=E((xf0)=>{Object.defineProperty(xf0,"__esModule",{value:!0});var _21=$A(),_f0=oX(),x21=yC();function UW9(A,B=!0,Q=!0){if(!x21.WINDOW||!x21.WINDOW.location){_f0.DEBUG_BUILD&&_21.logger.warn("Could not initialize routing instrumentation due to invalid location");return}let D=x21.WINDOW.location.href,Z;if(B)Z=A({name:x21.WINDOW.location.pathname,startTimestamp:_21.browserPerformanceTimeOrigin?_21.browserPerformanceTimeOrigin/1000:void 0,op:"pageload",origin:"auto.pageload.browser",metadata:{source:"url"}});if(Q)_21.addHistoryInstrumentationHandler(({to:G,from:F})=>{if(F===void 0&&D&&D.indexOf(G)!==-1){D=void 0;return}if(F!==G){if(D=void 0,Z)_f0.DEBUG_BUILD&&_21.logger.log(`[Tracing] Finishing current transaction with op: ${Z.op}`),Z.end();Z=A({name:x21.WINDOW.location.pathname,op:"navigation",origin:"auto.navigation.browser",metadata:{source:"url"}})}})}xf0.instrumentRoutingWithDefaults=UW9});
var vj=E((Gb0)=>{var{_optionalChain:Zb0}=$A();Object.defineProperty(Gb0,"__esModule",{value:!0});function DF9(A){let B=Zb0([A,"call",(D)=>D(),"access",(D)=>D.getClient,"call",(D)=>D(),"optionalAccess",(D)=>D.getOptions,"call",(D)=>D()]);return(Zb0([B,"optionalAccess",(D)=>D.instrumenter])||"sentry")!=="sentry"}Gb0.shouldDisableAutoInstrumentation=DF9});
var vm1=E((p_0)=>{Object.defineProperty(p_0,"__esModule",{value:!0});var m89=$A(),d89=hH();class l_0{constructor(A,B){if(this._client=A,this.flushTimeout=60,this._pendingAggregates={},this._isEnabled=!0,this._intervalId=setInterval(()=>this.flush(),this.flushTimeout*1000),this._intervalId.unref)this._intervalId.unref();this._sessionAttrs=B}flush(){let A=this.getSessionAggregates();if(A.aggregates.length===0)return;this._pendingAggregates={},this._client.sendSession(A)}getSessionAggregates(){let A=Object.keys(this._pendingAggregates).map((Q)=>{return this._pendingAggregates[parseInt(Q)]}),B={attrs:this._sessionAttrs,aggregates:A};return m89.dropUndefinedKeys(B)}close(){clearInterval(this._intervalId),this._isEnabled=!1,this.flush()}incrementSessionStatusCount(){if(!this._isEnabled)return;let A=d89.getCurrentScope(),B=A.getRequestSession();if(B&&B.status)this._incrementSessionStatusCount(B.status,new Date),A.setRequestSession(void 0)}_incrementSessionStatusCount(A,B){let Q=new Date(B).setSeconds(0,0);this._pendingAggregates[Q]=this._pendingAggregates[Q]||{};let D=this._pendingAggregates[Q];if(!D.started)D.started=new Date(Q).toISOString();switch(A){case"errored":return D.errored=(D.errored||0)+1,D.errored;case"ok":return D.exited=(D.exited||0)+1,D.exited;default:return D.crashed=(D.crashed||0)+1,D.crashed}}}p_0.SessionFlusher=l_0});
var vu1=E((cj0)=>{Object.defineProperty(cj0,"__esModule",{value:!0});var ZtB=RW(),VW1=ZtB.getGlobalObject();function GtB(){let A=VW1.chrome,B=A&&A.app&&A.app.runtime,Q="history"in VW1&&!!VW1.history.pushState&&!!VW1.history.replaceState;return!B&&Q}cj0.supportsHistory=GtB});
var w21=E((O_0)=>{Object.defineProperty(O_0,"__esModule",{value:!0});var p69="sentry.source",i69="sentry.sample_rate",n69="sentry.op",a69="sentry.origin",s69="profile_id";O_0.SEMANTIC_ATTRIBUTE_PROFILE_ID=s69;O_0.SEMANTIC_ATTRIBUTE_SENTRY_OP=n69;O_0.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN=a69;O_0.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE=i69;O_0.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE=p69});
var wf0=E((Uf0)=>{Object.defineProperty(Uf0,"__esModule",{value:!0});function MY9(A){return typeof A==="number"&&isFinite(A)}function RY9(A,{startTimestamp:B,...Q}){if(B&&A.startTimestamp>B)A.startTimestamp=B;return A.startChild({startTimestamp:B,...Q})}Uf0._startChild=RY9;Uf0.isMeasurementValue=MY9});
var wl=E((Ef0)=>{Object.defineProperty(Ef0,"__esModule",{value:!0});var Wf0=$A(),eI9=oX(),AY9=pb0(),BY9=ab0(),QY9=Df0(),DY9=Ff0(),ZY9=Wf(),GY9=Yf0(),j21={},LJ1={},Jf0,Xf0,Vf0,Cf0,Kf0;function FY9(A,B=!1){return k21("cls",A,VY9,Jf0,B)}function IY9(A,B=!1){return k21("lcp",A,KY9,Vf0,B)}function YY9(A){return k21("ttfb",A,HY9,Cf0)}function WY9(A){return k21("fid",A,CY9,Xf0)}function JY9(A){return k21("inp",A,zY9,Kf0)}function XY9(A,B){if(Hf0(A,B),!LJ1[A])EY9(A),LJ1[A]=!0;return zf0(A,B)}function Ul(A,B){let Q=j21[A];if(!Q||!Q.length)return;for(let D of Q)try{D(B)}catch(Z){eI9.DEBUG_BUILD&&Wf0.logger.error(`Error while triggering instrumentation handler.
Type: ${A}
Name: ${Wf0.getFunctionName(D)}
Error:`,Z)}}function VY9(){return AY9.onCLS((A)=>{Ul("cls",{metric:A}),Jf0=A},{reportAllChanges:!0})}function CY9(){return BY9.onFID((A)=>{Ul("fid",{metric:A}),Xf0=A})}function KY9(){return DY9.onLCP((A)=>{Ul("lcp",{metric:A}),Vf0=A})}function HY9(){return GY9.onTTFB((A)=>{Ul("ttfb",{metric:A}),Cf0=A})}function zY9(){return QY9.onINP((A)=>{Ul("inp",{metric:A}),Kf0=A})}function k21(A,B,Q,D,Z=!1){Hf0(A,B);let G;if(!LJ1[A])G=Q(),LJ1[A]=!0;if(D)B({metric:D});return zf0(A,B,Z?G:void 0)}function EY9(A){let B={};if(A==="event")B.durationThreshold=0;ZY9.observe(A,(Q)=>{Ul(A,{entries:Q})},B)}function Hf0(A,B){j21[A]=j21[A]||[],j21[A].push(B)}function zf0(A,B,Q){return()=>{if(Q)Q();let D=j21[A];if(!D)return;let Z=D.indexOf(B);if(Z!==-1)D.splice(Z,1)}}Ef0.addClsInstrumentationHandler=FY9;Ef0.addFidInstrumentationHandler=WY9;Ef0.addInpInstrumentationHandler=JY9;Ef0.addLcpInstrumentationHandler=IY9;Ef0.addPerformanceInstrumentationHandler=XY9;Ef0.addTtfbInstrumentationHandler=YY9});
var xJ1=E((oh0)=>{var{_optionalChain:yd1}=$A();Object.defineProperty(oh0,"__esModule",{value:!0});var rX9=J1("fs"),nh0=OQ(),ah0=$A(),_J1=new ah0.LRUMap(100),oX9=7,sh0="ContextLines";function tX9(A){return new Promise((B,Q)=>{rX9.readFile(A,"utf8",(D,Z)=>{if(D)Q(D);else B(Z)})})}var eX9=(A={})=>{let B=A.frameContextLines!==void 0?A.frameContextLines:oX9;return{name:sh0,setupOnce(){},processEvent(Q){return BV9(Q,B)}}},rh0=nh0.defineIntegration(eX9),AV9=nh0.convertIntegrationFnToClass(sh0,rh0);async function BV9(A,B){let Q={},D=[];if(B>0&&yd1([A,"access",(Z)=>Z.exception,"optionalAccess",(Z)=>Z.values]))for(let Z of A.exception.values){if(!yd1([Z,"access",(G)=>G.stacktrace,"optionalAccess",(G)=>G.frames]))continue;for(let G=Z.stacktrace.frames.length-1;G>=0;G--){let F=Z.stacktrace.frames[G];if(F.filename&&!Q[F.filename]&&!_J1.get(F.filename))D.push(DV9(F.filename)),Q[F.filename]=1}}if(D.length>0)await Promise.all(D);if(B>0&&yd1([A,"access",(Z)=>Z.exception,"optionalAccess",(Z)=>Z.values])){for(let Z of A.exception.values)if(Z.stacktrace&&Z.stacktrace.frames)await QV9(Z.stacktrace.frames,B)}return A}function QV9(A,B){for(let Q of A)if(Q.filename&&Q.context_line===void 0){let D=_J1.get(Q.filename);if(D)try{ah0.addContextToFrame(D,Q,B)}catch(Z){}}}async function DV9(A){let B=_J1.get(A);if(B===null)return null;if(B!==void 0)return B;let Q=null;try{Q=(await tX9(A)).split(`
`)}catch(D){}return _J1.set(A,Q),Q}oh0.ContextLines=AV9;oh0.contextLinesIntegration=rh0});
var xm1=E((c_0)=>{Object.defineProperty(c_0,"__esModule",{value:!0});var Xl=$A();function b89(A,B){if(!B)return A;return A.sdk=A.sdk||{},A.sdk.name=A.sdk.name||B.name,A.sdk.version=A.sdk.version||B.version,A.sdk.integrations=[...A.sdk.integrations||[],...B.integrations||[]],A.sdk.packages=[...A.sdk.packages||[],...B.packages||[]],A}function f89(A,B,Q,D){let Z=Xl.getSdkMetadataForEnvelopeHeader(Q),G={sent_at:new Date().toISOString(),...Z&&{sdk:Z},...!!D&&B&&{dsn:Xl.dsnToString(B)}},F="aggregates"in A?[{type:"sessions"},A]:[{type:"session"},A.toJSON()];return Xl.createEnvelope(G,[F])}function h89(A,B,Q,D){let Z=Xl.getSdkMetadataForEnvelopeHeader(Q),G=A.type&&A.type!=="replay_event"?A.type:"event";b89(A,Q&&Q.sdk);let F=Xl.createEventEnvelopeHeaders(A,Z,D,B);delete A.sdkProcessingMetadata;let I=[{type:G},A];return Xl.createEnvelope(F,[I])}c_0.createEventEnvelope=h89;c_0.createSessionEnvelope=f89});
var xu1=E((dj0)=>{Object.defineProperty(dj0,"__esModule",{value:!0});var yu1=RW(),_u1=rR(),XW1=null;function BtB(A){_u1.addHandler("unhandledrejection",A),_u1.maybeInstrument("unhandledrejection",QtB)}function QtB(){XW1=yu1.GLOBAL_OBJ.onunhandledrejection,yu1.GLOBAL_OBJ.onunhandledrejection=function(A){let B=A;if(_u1.triggerHandlers("unhandledrejection",B),XW1&&!XW1.__SENTRY_LOADER__)return XW1.apply(this,arguments);return!0},yu1.GLOBAL_OBJ.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}dj0.addGlobalUnhandledRejectionInstrumentationHandler=BtB});
var xx0=E((_x0)=>{Object.defineProperty(_x0,"__esModule",{value:!0});var yx0=$A();function t39(A,B){let Q={sent_at:new Date().toISOString()};if(B)Q.dsn=yx0.dsnToString(B);let D=A.map(e39);return yx0.createEnvelope(Q,D)}function e39(A){return[{type:"span"},A]}_x0.createSpanEnvelope=t39});
var yC=E((Sb0)=>{Object.defineProperty(Sb0,"__esModule",{value:!0});var fF9=$A(),hF9=fF9.GLOBAL_OBJ;Sb0.WINDOW=hF9});
var yJ1=E((ih0)=>{var{_optionalChain:Hf}=$A();Object.defineProperty(ih0,"__esModule",{value:!0});var kX9=J1("child_process"),hh0=J1("fs"),uH=J1("os"),yX9=J1("path"),gh0=J1("util"),uh0=OQ(),mh0=gh0.promisify(hh0.readFile),dh0=gh0.promisify(hh0.readdir),ch0="Context",_X9=(A={})=>{let B,Q={app:!0,os:!0,device:!0,culture:!0,cloudResource:!0,...A};async function D(G){if(B===void 0)B=Z();let F=vX9(await B);return G.contexts={...G.contexts,app:{...F.app,...Hf([G,"access",(I)=>I.contexts,"optionalAccess",(I)=>I.app])},os:{...F.os,...Hf([G,"access",(I)=>I.contexts,"optionalAccess",(I)=>I.os])},device:{...F.device,...Hf([G,"access",(I)=>I.contexts,"optionalAccess",(I)=>I.device])},culture:{...F.culture,...Hf([G,"access",(I)=>I.contexts,"optionalAccess",(I)=>I.culture])},cloud_resource:{...F.cloud_resource,...Hf([G,"access",(I)=>I.contexts,"optionalAccess",(I)=>I.cloud_resource])}},G}async function Z(){let G={};if(Q.os)G.os=await bX9();if(Q.app)G.app=hX9();if(Q.device)G.device=ph0(Q.device);if(Q.culture){let F=fX9();if(F)G.culture=F}if(Q.cloudResource)G.cloud_resource=lX9();return G}return{name:ch0,setupOnce(){},processEvent(G){return D(G)}}},lh0=uh0.defineIntegration(_X9),xX9=uh0.convertIntegrationFnToClass(ch0,lh0);function vX9(A){if(Hf([A,"optionalAccess",(B)=>B.app,"optionalAccess",(B)=>B.app_memory]))A.app.app_memory=process.memoryUsage().rss;if(Hf([A,"optionalAccess",(B)=>B.device,"optionalAccess",(B)=>B.free_memory]))A.device.free_memory=uH.freemem();return A}async function bX9(){let A=uH.platform();switch(A){case"darwin":return dX9();case"linux":return cX9();default:return{name:gX9[A]||A,version:uH.release()}}}function fX9(){try{if(typeof process.versions.icu!=="string")return;let A=new Date(900000000);if(new Intl.DateTimeFormat("es",{month:"long"}).format(A)==="enero"){let Q=Intl.DateTimeFormat().resolvedOptions();return{locale:Q.locale,timezone:Q.timeZone}}}catch(A){}return}function hX9(){let A=process.memoryUsage().rss;return{app_start_time:new Date(Date.now()-process.uptime()*1000).toISOString(),app_memory:A}}function ph0(A){let B={},Q;try{Q=uH.uptime&&uH.uptime()}catch(D){}if(typeof Q==="number")B.boot_time=new Date(Date.now()-Q*1000).toISOString();if(B.arch=uH.arch(),A===!0||A.memory)B.memory_size=uH.totalmem(),B.free_memory=uH.freemem();if(A===!0||A.cpu){let D=uH.cpus();if(D&&D.length){let Z=D[0];B.processor_count=D.length,B.cpu_description=Z.model,B.processor_frequency=Z.speed}}return B}var gX9={aix:"IBM AIX",freebsd:"FreeBSD",openbsd:"OpenBSD",sunos:"SunOS",win32:"Windows"},uX9=[{name:"fedora-release",distros:["Fedora"]},{name:"redhat-release",distros:["Red Hat Linux","Centos"]},{name:"redhat_version",distros:["Red Hat Linux"]},{name:"SuSE-release",distros:["SUSE Linux"]},{name:"lsb-release",distros:["Ubuntu Linux","Arch Linux"]},{name:"debian_version",distros:["Debian"]},{name:"debian_release",distros:["Debian"]},{name:"arch-release",distros:["Arch Linux"]},{name:"gentoo-release",distros:["Gentoo Linux"]},{name:"novell-release",distros:["SUSE Linux"]},{name:"alpine-release",distros:["Alpine Linux"]}],mX9={alpine:(A)=>A,arch:(A)=>tq(/distrib_release=(.*)/,A),centos:(A)=>tq(/release ([^ ]+)/,A),debian:(A)=>A,fedora:(A)=>tq(/release (..)/,A),mint:(A)=>tq(/distrib_release=(.*)/,A),red:(A)=>tq(/release ([^ ]+)/,A),suse:(A)=>tq(/VERSION = (.*)\n/,A),ubuntu:(A)=>tq(/distrib_release=(.*)/,A)};function tq(A,B){let Q=A.exec(B);return Q?Q[1]:void 0}async function dX9(){let A={kernel_version:uH.release(),name:"Mac OS X",version:`10.${Number(uH.release().split(".")[0])-4}`};try{let B=await new Promise((Q,D)=>{kX9.execFile("/usr/bin/sw_vers",(Z,G)=>{if(Z){D(Z);return}Q(G)})});A.name=tq(/^ProductName:\s+(.*)$/m,B),A.version=tq(/^ProductVersion:\s+(.*)$/m,B),A.build=tq(/^BuildVersion:\s+(.*)$/m,B)}catch(B){}return A}function fh0(A){return A.split(" ")[0].toLowerCase()}async function cX9(){let A={kernel_version:uH.release(),name:"Linux"};try{let B=await dh0("/etc"),Q=uX9.find((I)=>B.includes(I.name));if(!Q)return A;let D=yX9.join("/etc",Q.name),Z=(await mh0(D,{encoding:"utf-8"})).toLowerCase(),{distros:G}=Q;A.name=G.find((I)=>Z.indexOf(fh0(I))>=0)||G[0];let F=fh0(A.name);A.version=mX9[F](Z)}catch(B){}return A}function lX9(){if(process.env.VERCEL)return{"cloud.provider":"vercel","cloud.region":process.env.VERCEL_REGION};else if(process.env.AWS_REGION)return{"cloud.provider":"aws","cloud.region":process.env.AWS_REGION,"cloud.platform":process.env.AWS_EXECUTION_ENV};else if(process.env.GCP_PROJECT)return{"cloud.provider":"gcp"};else if(process.env.ALIYUN_REGION_ID)return{"cloud.provider":"alibaba_cloud","cloud.region":process.env.ALIYUN_REGION_ID};else if(process.env.WEBSITE_SITE_NAME&&process.env.REGION_NAME)return{"cloud.provider":"azure","cloud.region":process.env.REGION_NAME};else if(process.env.IBM_CLOUD_REGION)return{"cloud.provider":"ibm_cloud","cloud.region":process.env.IBM_CLOUD_REGION};else if(process.env.TENCENTCLOUD_REGION)return{"cloud.provider":"tencent_cloud","cloud.region":process.env.TENCENTCLOUD_REGION,"cloud.account.id":process.env.TENCENTCLOUD_APPID,"cloud.availability_zone":process.env.TENCENTCLOUD_ZONE};else if(process.env.NETLIFY)return{"cloud.provider":"netlify"};else if(process.env.FLY_REGION)return{"cloud.provider":"fly.io","cloud.region":process.env.FLY_REGION};else if(process.env.DYNO)return{"cloud.provider":"heroku"};else return}ih0.Context=xX9;ih0.getDeviceContext=ph0;ih0.nodeContextIntegration=lh0;ih0.readDirAsync=dh0;ih0.readFileAsync=mh0});
var yh0=E((kh0)=>{Object.defineProperty(kh0,"__esModule",{value:!0});var qX9=Cf(),NX9=Ph0(),LX9=jh0();function MX9(){if(qX9.NODE_VERSION.major>=14)LX9.setHooksAsyncContextStrategy();else NX9.setDomainAsyncContextStrategy()}kh0.setNodeAsyncContextStrategy=MX9});
var ym0=E((km0)=>{Object.defineProperty(km0,"__esModule",{value:!0});var Tm0=OQ(),od1=$A(),rd1=od1.GLOBAL_OBJ,az9=7,Pm0="ContextLines",sz9=(A={})=>{let B=A.frameContextLines!=null?A.frameContextLines:az9;return{name:Pm0,setupOnce(){},processEvent(Q){return oz9(Q,B)}}},Sm0=Tm0.defineIntegration(sz9),rz9=Tm0.convertIntegrationFnToClass(Pm0,Sm0);function oz9(A,B){let Q=rd1.document,D=rd1.location&&od1.stripUrlQueryAndFragment(rd1.location.href);if(!Q||!D)return A;let Z=A.exception&&A.exception.values;if(!Z||!Z.length)return A;let G=Q.documentElement.innerHTML;if(!G)return A;let F=["<!DOCTYPE html>","<html>",...G.split(`
`),"</html>"];return Z.forEach((I)=>{let Y=I.stacktrace;if(Y&&Y.frames)Y.frames=Y.frames.map((W)=>jm0(W,F,D,B))}),A}function jm0(A,B,Q,D){if(A.filename!==Q||!A.lineno||!B.length)return A;return od1.addContextToFrame(B,A,D),A}km0.ContextLines=rz9;km0.applySourceContextToFrame=jm0;km0.contextLinesIntegration=Sm0});
var ym1=E((h_0)=>{Object.defineProperty(h_0,"__esModule",{value:!0});var Gf=$A(),Jl=qG(),sW1=w21(),H89=hW1(),z89=aX();function E89(A,B,Q){if(!H89.hasTracingEnabled(B))return A.sampled=!1,A;if(A.sampled!==void 0)return A.setAttribute(sW1.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,Number(A.sampled)),A;let D;if(typeof B.tracesSampler==="function")D=B.tracesSampler(Q),A.setAttribute(sW1.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,Number(D));else if(Q.parentSampled!==void 0)D=Q.parentSampled;else if(typeof B.tracesSampleRate!=="undefined")D=B.tracesSampleRate,A.setAttribute(sW1.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,Number(D));else D=1,A.setAttribute(sW1.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,D);if(!f_0(D))return Jl.DEBUG_BUILD&&Gf.logger.warn("[Tracing] Discarding transaction because of invalid sample rate."),A.sampled=!1,A;if(!D)return Jl.DEBUG_BUILD&&Gf.logger.log(`[Tracing] Discarding transaction because ${typeof B.tracesSampler==="function"?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0"}`),A.sampled=!1,A;if(A.sampled=Math.random()<D,!A.sampled)return Jl.DEBUG_BUILD&&Gf.logger.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(D)})`),A;return Jl.DEBUG_BUILD&&Gf.logger.log(`[Tracing] starting ${A.op} transaction - ${z89.spanToJSON(A).description}`),A}function f_0(A){if(Gf.isNaN(A)||!(typeof A==="number"||typeof A==="boolean"))return Jl.DEBUG_BUILD&&Gf.logger.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(A)} of type ${JSON.stringify(typeof A)}.`),!1;if(A<0||A>1)return Jl.DEBUG_BUILD&&Gf.logger.warn(`[Tracing] Given sample rate is invalid. Sample rate must be between 0 and 1. Got ${A}.`),!1;return!0}h_0.isValidSampleRate=f_0;h_0.sampleTransaction=E89});
var yu0=E((ku0)=>{Object.defineProperty(ku0,"__esModule",{value:!0});var Pu0=OQ(),lH9=$A(),Su0="Debug",pH9=(A={})=>{let B={debugger:!1,stringify:!1,...A};return{name:Su0,setupOnce(){},setup(Q){if(!Q.on)return;Q.on("beforeSendEvent",(D,Z)=>{if(B.debugger)debugger;lH9.consoleSandbox(()=>{if(B.stringify){if(console.log(JSON.stringify(D,null,2)),Z&&Object.keys(Z).length)console.log(JSON.stringify(Z,null,2))}else if(console.log(D),Z&&Object.keys(Z).length)console.log(Z)})})}}},ju0=Pu0.defineIntegration(pH9),iH9=Pu0.convertIntegrationFnToClass(Su0,ju0);ku0.Debug=iH9;ku0.debugIntegration=ju0});
var zh0=E((Hh0)=>{Object.defineProperty(Hh0,"__esModule",{value:!0});var gJ9=$A();function PJ1(...A){gJ9.logger.log("[https-proxy-agent:parse-proxy-response]",...A)}function uJ9(A){return new Promise((B,Q)=>{let D=0,Z=[];function G(){let J=A.read();if(J)W(J);else A.once("readable",G)}function F(){A.removeListener("end",I),A.removeListener("error",Y),A.removeListener("readable",G)}function I(){F(),PJ1("onend"),Q(new Error("Proxy connection ended before receiving CONNECT response"))}function Y(J){F(),PJ1("onerror %o",J),Q(J)}function W(J){Z.push(J),D+=J.length;let X=Buffer.concat(Z,D),V=X.indexOf(`\r
\r
`);if(V===-1){PJ1("have not received end of HTTP headers yet..."),G();return}let C=X.slice(0,V).toString("ascii").split(`\r
`),K=C.shift();if(!K)return A.destroy(),Q(new Error("No header received from proxy CONNECT response"));let H=K.split(" "),z=+H[1],$=H.slice(2).join(" "),L={};for(let N of C){if(!N)continue;let O=N.indexOf(":");if(O===-1)return A.destroy(),Q(new Error(`Invalid header from proxy CONNECT response: "${N}"`));let R=N.slice(0,O).toLowerCase(),T=N.slice(O+1).trimStart(),j=L[R];if(typeof j==="string")L[R]=[j,T];else if(Array.isArray(j))j.push(T);else L[R]=T}PJ1("got proxy server response: %o %o",K,L),F(),B({connect:{statusCode:z,statusText:$,headers:L},buffered:X})}A.on("error",Y),A.on("end",I),G()})}Hh0.parseProxyResponse=uJ9});
var zl=E((ub0)=>{Object.defineProperty(ub0,"__esModule",{value:!0});var gb0=yC(),tF9=bb0(),eF9=UJ1(),AI9=S21(),BI9=(A,B)=>{let Q=AI9.getNavigationEntry(),D="navigate";if(Q)if(gb0.WINDOW.document&&gb0.WINDOW.document.prerendering||eF9.getActivationStart()>0)D="prerender";else D=Q.type.replace(/_/g,"-");return{name:A,value:typeof B==="undefined"?-1:B,rating:"good",delta:0,entries:[],id:tF9.generateUniqueID(),navigationType:D}};ub0.initMetric=BI9});

module.exports = ed1;
