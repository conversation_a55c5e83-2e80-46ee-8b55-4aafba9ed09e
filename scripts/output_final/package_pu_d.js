// dependency_chain package extracted with entry point: pu

var $F0=E((HT2)=>{Object.defineProperty(HT2,"__esModule",{value:!0});HT2.parseKeyPairsIntoRecord=HT2.parsePairKeyValue=HT2.getKeyPairs=HT2.serializeKeyPairs=void 0;var ts4=ZQ(),Xu=wF0();function es4(A){return A.reduce((B,Q)=>{let D=`${B}${B!==""?Xu.BAGGAGE_ITEMS_SEPARATOR:""}${Q}`;return D.length>Xu.BAGGAGE_MAX_TOTAL_LENGTH?B:D},"")}HT2.serializeKeyPairs=es4;function Ar4(A){return A.getAllEntries().map(([B,Q])=>{let D=`${encodeURIComponent(B)}=${encodeURIComponent(Q.value)}`;if(Q.metadata!==void 0)D+=Xu.BAGGAGE_PROPERTIES_SEPARATOR+Q.metadata.toString();return D})}HT2.getKeyPairs=Ar4;function KT2(A){let B=A.split(Xu.BAGGAGE_PROPERTIES_SEPARATOR);if(B.length<=0)return;let Q=B.shift();if(!Q)return;let D=Q.indexOf(Xu.BAGGAGE_KEY_PAIR_SEPARATOR);if(D<=0)return;let Z=decodeURIComponent(Q.substring(0,D).trim()),G=decodeURIComponent(Q.substring(D+1).trim()),F;if(B.length>0)F=ts4.baggageEntryMetadataFromString(B.join(Xu.BAGGAGE_PROPERTIES_SEPARATOR));return{key:Z,value:G,metadata:F}}HT2.parsePairKeyValue=KT2;function Br4(A){if(typeof A!=="string"||A.length===0)return{};return A.split(Xu.BAGGAGE_ITEMS_SEPARATOR).map((B)=>{return KT2(B)}).filter((B)=>B!==void 0&&B.value.length>0).reduce((B,Q)=>{return B[Q.key]=Q.value,B},{})}HT2.parseKeyPairsIntoRecord=Br4});
var $R2=E((UR2)=>{Object.defineProperty(UR2,"__esModule",{value:!0});UR2.deleteBaggage=UR2.setBaggage=UR2.getActiveBaggage=UR2.getBaggage=void 0;var nn4=g51(),an4=f51(),iG0=an4.createContextKey("OpenTelemetry Baggage Key");function ER2(A){return A.getValue(iG0)||void 0}UR2.getBaggage=ER2;function sn4(){return ER2(nn4.ContextAPI.getInstance().active())}UR2.getActiveBaggage=sn4;function rn4(A,B){return A.setValue(iG0,B)}UR2.setBaggage=rn4;function on4(A){return A.deleteValue(iG0)}UR2.deleteBaggage=on4});
var $T2=E((UT2)=>{Object.defineProperty(UT2,"__esModule",{value:!0});UT2.W3CBaggagePropagator=void 0;var qF0=ZQ(),Gr4=c51(),Vu=wF0(),NF0=$F0();class ET2{inject(A,B,Q){let D=qF0.propagation.getBaggage(A);if(!D||Gr4.isTracingSuppressed(A))return;let Z=NF0.getKeyPairs(D).filter((F)=>{return F.length<=Vu.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS}).slice(0,Vu.BAGGAGE_MAX_NAME_VALUE_PAIRS),G=NF0.serializeKeyPairs(Z);if(G.length>0)Q.set(B,Vu.BAGGAGE_HEADER,G)}extract(A,B,Q){let D=Q.get(B,Vu.BAGGAGE_HEADER),Z=Array.isArray(D)?D.join(Vu.BAGGAGE_ITEMS_SEPARATOR):D;if(!Z)return A;let G={};if(Z.length===0)return A;if(Z.split(Vu.BAGGAGE_ITEMS_SEPARATOR).forEach((I)=>{let Y=NF0.parsePairKeyValue(I);if(Y){let W={value:Y.value};if(Y.metadata)W.metadata=Y.metadata;G[Y.key]=W}}),Object.entries(G).length===0)return A;return qF0.propagation.setBaggage(A,qF0.propagation.createBaggage(G))}fields(){return[Vu.BAGGAGE_HEADER]}}UT2.W3CBaggagePropagator=ET2});
var $b2=E((Ub2)=>{Object.defineProperty(Ub2,"__esModule",{value:!0});Ub2.getRPCMetadata=Ub2.deleteRPCMetadata=Ub2.setRPCMetadata=Ub2.RPCType=void 0;var o46=ZQ(),_F0=o46.createContextKey("OpenTelemetry SDK Context Key RPC_METADATA"),t46;(function(A){A.HTTP="http"})(t46=Ub2.RPCType||(Ub2.RPCType={}));function e46(A,B){return A.setValue(_F0,B)}Ub2.setRPCMetadata=e46;function A66(A){return A.deleteValue(_F0)}Ub2.deleteRPCMetadata=A66;function B66(A){return A.getValue(_F0)}Ub2.getRPCMetadata=B66});
var AL2=E((Yu)=>{var Fi4=Yu&&Yu.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;Object.defineProperty(A,D,{enumerable:!0,get:function(){return B[Q]}})}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),Ii4=Yu&&Yu.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))Fi4(B,A,Q)};Object.defineProperty(Yu,"__esModule",{value:!0});Ii4(eN2(),Yu)});
var CL2=E((XL2)=>{Object.defineProperty(XL2,"__esModule",{value:!0});XL2.DiagComponentLogger=void 0;var Ui4=Wu();class JL2{constructor(A){this._namespace=A.namespace||"DiagComponentLogger"}debug(...A){return b51("debug",this._namespace,A)}error(...A){return b51("error",this._namespace,A)}info(...A){return b51("info",this._namespace,A)}warn(...A){return b51("warn",this._namespace,A)}verbose(...A){return b51("verbose",this._namespace,A)}}XL2.DiagComponentLogger=JL2;function b51(A,B,Q){let D=Ui4.getGlobal("diag");if(!D)return;return Q.unshift(B),D[A](...Q)}});
var CO1=E((zM2)=>{Object.defineProperty(zM2,"__esModule",{value:!0});zM2.wrapSpanContext=zM2.isSpanContextValid=zM2.isValidSpanId=zM2.isValidTraceId=void 0;var CM2=XO1(),Jn4=VO1(),Xn4=/^([0-9a-f]{32})$/i,Vn4=/^[0-9a-f]{16}$/i;function KM2(A){return Xn4.test(A)&&A!==CM2.INVALID_TRACEID}zM2.isValidTraceId=KM2;function HM2(A){return Vn4.test(A)&&A!==CM2.INVALID_SPANID}zM2.isValidSpanId=HM2;function Cn4(A){return KM2(A.traceId)&&HM2(A.spanId)}zM2.isSpanContextValid=Cn4;function Kn4(A){return new Jn4.NonRecordingSpan(A)}zM2.wrapSpanContext=Kn4});
var Df2=E((Bf2)=>{Object.defineProperty(Bf2,"__esModule",{value:!0});Bf2._export=void 0;var Af2=ZQ(),M66=c51();function R66(A,B){return new Promise((Q)=>{Af2.context.with(M66.suppressTracing(Af2.context.active()),()=>{A.export(B,(D)=>{Q(D)})})})}Bf2._export=R66});
var EL2=E((HL2)=>{Object.defineProperty(HL2,"__esModule",{value:!0});HL2.createLogLevelDiagLogger=void 0;var DP=YO1();function $i4(A,B){if(A<DP.DiagLogLevel.NONE)A=DP.DiagLogLevel.NONE;else if(A>DP.DiagLogLevel.ALL)A=DP.DiagLogLevel.ALL;B=B||{};function Q(D,Z){let G=B[D];if(typeof G==="function"&&A>=Z)return G.bind(B);return function(){}}return{error:Q("error",DP.DiagLogLevel.ERROR),warn:Q("warn",DP.DiagLogLevel.WARN),info:Q("info",DP.DiagLogLevel.INFO),debug:Q("debug",DP.DiagLogLevel.DEBUG),verbose:Q("verbose",DP.DiagLogLevel.VERBOSE)}}HL2.createLogLevelDiagLogger=$i4});
var GP=E((mL)=>{var D46=mL&&mL.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),NO1=mL&&mL.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))D46(B,A,Q)};Object.defineProperty(mL,"__esModule",{value:!0});NO1(ty2(),mL);NO1(Pv2(),mL);NO1(_v2(),mL);NO1(bv2(),mL)});
var GR2=E((DR2)=>{Object.defineProperty(DR2,"__esModule",{value:!0});DR2.NOOP_METER_PROVIDER=DR2.NoopMeterProvider=void 0;var cn4=qG0();class dG0{getMeter(A,B,Q){return cn4.NOOP_METER}}DR2.NoopMeterProvider=dG0;DR2.NOOP_METER_PROVIDER=new dG0});
var Gb2=E((Db2)=>{Object.defineProperty(Db2,"__esModule",{value:!0});Db2.validateValue=Db2.validateKey=void 0;var jF0="[_0-9a-z-*/]",_46=`[a-z]${jF0}{0,255}`,x46=`[a-z0-9]${jF0}{0,240}@[a-z]${jF0}{0,13}`,v46=new RegExp(`^(?:${_46}|${x46})$`),b46=/^[ -~]{0,255}[!-~]$/,f46=/,|=/;function h46(A){return v46.test(A)}Db2.validateKey=h46;function g46(A){return b46.test(A)&&!f46.test(A)}Db2.validateValue=g46});
var IL2=E((GL2)=>{Object.defineProperty(GL2,"__esModule",{value:!0});GL2.isCompatible=GL2._makeCompatibilityCheck=void 0;var Yi4=YG0(),DL2=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function ZL2(A){let B=new Set([A]),Q=new Set,D=A.match(DL2);if(!D)return()=>!1;let Z={major:+D[1],minor:+D[2],patch:+D[3],prerelease:D[4]};if(Z.prerelease!=null)return function I(Y){return Y===A};function G(I){return Q.add(I),!1}function F(I){return B.add(I),!0}return function I(Y){if(B.has(Y))return!0;if(Q.has(Y))return!1;let W=Y.match(DL2);if(!W)return G(Y);let J={major:+W[1],minor:+W[2],patch:+W[3],prerelease:W[4]};if(J.prerelease!=null)return G(Y);if(Z.major!==J.major)return G(Y);if(Z.major===0){if(Z.minor===J.minor&&Z.patch<=J.patch)return F(Y);return G(Y)}if(Z.minor<=J.minor)return F(Y);return G(Y)}}GL2._makeCompatibilityCheck=ZL2;GL2.isCompatible=ZL2(Yi4.VERSION)});
var Ju=E((wL2)=>{Object.defineProperty(wL2,"__esModule",{value:!0});wL2.DiagAPI=void 0;var qi4=CL2(),Ni4=EL2(),UL2=YO1(),WO1=Wu(),Li4="diag";class JG0{constructor(){function A(D){return function(...Z){let G=WO1.getGlobal("diag");if(!G)return;return G[D](...Z)}}let B=this,Q=(D,Z={logLevel:UL2.DiagLogLevel.INFO})=>{var G,F,I;if(D===B){let J=new Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return B.error((G=J.stack)!==null&&G!==void 0?G:J.message),!1}if(typeof Z==="number")Z={logLevel:Z};let Y=WO1.getGlobal("diag"),W=Ni4.createLogLevelDiagLogger((F=Z.logLevel)!==null&&F!==void 0?F:UL2.DiagLogLevel.INFO,D);if(Y&&!Z.suppressOverrideMessage){let J=(I=new Error().stack)!==null&&I!==void 0?I:"<failed to generate stacktrace>";Y.warn(`Current logger will be overwritten from ${J}`),W.warn(`Current logger will overwrite one already registered from ${J}`)}return WO1.registerGlobal("diag",W,B,!0)};B.setLogger=Q,B.disable=()=>{WO1.unregisterGlobal(Li4,B)},B.createComponentLogger=(D)=>{return new qi4.DiagComponentLogger(D)},B.verbose=A("verbose"),B.debug=A("debug"),B.info=A("info"),B.warn=A("warn"),B.error=A("error")}static instance(){if(!this._instance)this._instance=new JG0;return this._instance}}wL2.DiagAPI=JG0});
var LF0=E((yT2)=>{Object.defineProperty(yT2,"__esModule",{value:!0});yT2.loggingErrorHandler=void 0;var Jr4=ZQ();function Xr4(){return(A)=>{Jr4.diag.error(Vr4(A))}}yT2.loggingErrorHandler=Xr4;function Vr4(A){if(typeof A==="string")return A;else return JSON.stringify(Cr4(A))}function Cr4(A){let B={},Q=A;while(Q!==null)Object.getOwnPropertyNames(Q).forEach((D)=>{if(B[D])return;let Z=Q[D];if(Z)B[D]=String(Z)}),Q=Object.getPrototypeOf(Q);return B}});
var LG0=E((nL2)=>{Object.defineProperty(nL2,"__esModule",{value:!0});nL2.defaultTextMapSetter=nL2.defaultTextMapGetter=void 0;nL2.defaultTextMapGetter={get(A,B){if(A==null)return;return A[B]},keys(A){if(A==null)return[];return Object.keys(A)}};nL2.defaultTextMapSetter={set(A,B,Q){if(A==null)return;A[B]=Q}}});
var LL2=E((qL2)=>{Object.defineProperty(qL2,"__esModule",{value:!0});qL2.BaggageImpl=void 0;class Mo{constructor(A){this._entries=A?new Map(A):new Map}getEntry(A){let B=this._entries.get(A);if(!B)return;return Object.assign({},B)}getAllEntries(){return Array.from(this._entries.entries()).map(([A,B])=>[A,B])}setEntry(A,B){let Q=new Mo(this._entries);return Q._entries.set(A,B),Q}removeEntry(A){let B=new Mo(this._entries);return B._entries.delete(A),B}removeEntries(...A){let B=new Mo(this._entries);for(let Q of A)B._entries.delete(Q);return B}clear(){return new Mo}}qL2.BaggageImpl=Mo});
var MF0=E((eT2)=>{Object.defineProperty(eT2,"__esModule",{value:!0});eT2.createConstMap=void 0;function Rr4(A){let B={},Q=A.length;for(let D=0;D<Q;D++){let Z=A[D];if(Z)B[String(Z).toUpperCase().replace(/[-.]/g,"_")]=Z}return B}eT2.createConstMap=Rr4});
var MT2=E((NT2)=>{Object.defineProperty(NT2,"__esModule",{value:!0});NT2.AnchoredClock=void 0;class qT2{_monotonicClock;_epochMillis;_performanceMillis;constructor(A,B){this._monotonicClock=B,this._epochMillis=A.now(),this._performanceMillis=B.now()}now(){let A=this._monotonicClock.now()-this._performanceMillis;return this._epochMillis+A}}NT2.AnchoredClock=qT2});
var OL2=E((ML2)=>{Object.defineProperty(ML2,"__esModule",{value:!0});ML2.baggageEntryMetadataSymbol=void 0;ML2.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")});
var PG0=E((QM2)=>{Object.defineProperty(QM2,"__esModule",{value:!0});QM2.TraceFlags=void 0;var si4;(function(A){A[A.NONE=0]="NONE",A[A.SAMPLED=1]="SAMPLED"})(si4=QM2.TraceFlags||(QM2.TraceFlags={}))});
var PM2=E((OM2)=>{Object.defineProperty(OM2,"__esModule",{value:!0});OM2.NoopTracerProvider=void 0;var Ln4=xG0();class RM2{getTracer(A,B,Q){return new Ln4.NoopTracer}}OM2.NoopTracerProvider=RM2});
var PR2=E((OR2)=>{Object.defineProperty(OR2,"__esModule",{value:!0});OR2.propagation=void 0;var Za4=RR2();OR2.propagation=Za4.PropagationAPI.getInstance()});
var Pv2=E((Hu)=>{var e26=Hu&&Hu.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),AB6=Hu&&Hu.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))e26(B,A,Q)};Object.defineProperty(Hu,"__esModule",{value:!0});AB6(Tv2(),Hu)});
var QR2=E((AR2)=>{Object.defineProperty(AR2,"__esModule",{value:!0});AR2.diag=void 0;var dn4=Ju();AR2.diag=dn4.DiagAPI.instance()});
var Qb2=E((Ab2)=>{Object.defineProperty(Ab2,"__esModule",{value:!0});Ab2.CompositePropagator=void 0;var tv2=ZQ();class ev2{_propagators;_fields;constructor(A={}){this._propagators=A.propagators??[],this._fields=Array.from(new Set(this._propagators.map((B)=>typeof B.fields==="function"?B.fields():[]).reduce((B,Q)=>B.concat(Q),[])))}inject(A,B,Q){for(let D of this._propagators)try{D.inject(A,B,Q)}catch(Z){tv2.diag.warn(`Failed to inject with ${D.constructor.name}. Err: ${Z.message}`)}}extract(A,B,Q){return this._propagators.reduce((D,Z)=>{try{return Z.extract(D,B,Q)}catch(G){tv2.diag.warn(`Failed to extract with ${Z.constructor.name}. Err: ${G.message}`)}return D},A)}fields(){return this._fields.slice()}}Ab2.CompositePropagator=ev2});
var RF0=E((cL)=>{Object.defineProperty(cL,"__esModule",{value:!0});cL.getStringListFromEnv=cL.getNumberFromEnv=cL.getStringFromEnv=cL.getBooleanFromEnv=cL.unrefTimer=cL.otperformance=cL._globalThis=cL.SDK_INFO=void 0;var x_=cv2();Object.defineProperty(cL,"SDK_INFO",{enumerable:!0,get:function(){return x_.SDK_INFO}});Object.defineProperty(cL,"_globalThis",{enumerable:!0,get:function(){return x_._globalThis}});Object.defineProperty(cL,"otperformance",{enumerable:!0,get:function(){return x_.otperformance}});Object.defineProperty(cL,"unrefTimer",{enumerable:!0,get:function(){return x_.unrefTimer}});Object.defineProperty(cL,"getBooleanFromEnv",{enumerable:!0,get:function(){return x_.getBooleanFromEnv}});Object.defineProperty(cL,"getStringFromEnv",{enumerable:!0,get:function(){return x_.getStringFromEnv}});Object.defineProperty(cL,"getNumberFromEnv",{enumerable:!0,get:function(){return x_.getNumberFromEnv}});Object.defineProperty(cL,"getStringListFromEnv",{enumerable:!0,get:function(){return x_.getStringListFromEnv}})});
var RR2=E((LR2)=>{Object.defineProperty(LR2,"__esModule",{value:!0});LR2.PropagationAPI=void 0;var nG0=Wu(),Ba4=zR2(),qR2=LG0(),KO1=$R2(),Qa4=XG0(),NR2=Ju(),aG0="propagation",Da4=new Ba4.NoopTextMapPropagator;class sG0{constructor(){this.createBaggage=Qa4.createBaggage,this.getBaggage=KO1.getBaggage,this.getActiveBaggage=KO1.getActiveBaggage,this.setBaggage=KO1.setBaggage,this.deleteBaggage=KO1.deleteBaggage}static getInstance(){if(!this._instance)this._instance=new sG0;return this._instance}setGlobalPropagator(A){return nG0.registerGlobal(aG0,A,NR2.DiagAPI.instance())}inject(A,B,Q=qR2.defaultTextMapSetter){return this._getGlobalPropagator().inject(A,B,Q)}extract(A,B,Q=qR2.defaultTextMapGetter){return this._getGlobalPropagator().extract(A,B,Q)}fields(){return this._getGlobalPropagator().fields()}disable(){nG0.unregisterGlobal(aG0,NR2.DiagAPI.instance())}_getGlobalPropagator(){return nG0.getGlobal(aG0)||Da4}}LR2.PropagationAPI=sG0});
var Tb2=E((Rb2)=>{Object.defineProperty(Rb2,"__esModule",{value:!0});Rb2.isPlainObject=void 0;var Z66="[object Object]",G66="[object Null]",F66="[object Undefined]",I66=Function.prototype,qb2=I66.toString,Y66=qb2.call(Object),W66=Object.getPrototypeOf,Nb2=Object.prototype,Lb2=Nb2.hasOwnProperty,zu=Symbol?Symbol.toStringTag:void 0,Mb2=Nb2.toString;function J66(A){if(!X66(A)||V66(A)!==Z66)return!1;let B=W66(A);if(B===null)return!0;let Q=Lb2.call(B,"constructor")&&B.constructor;return typeof Q=="function"&&Q instanceof Q&&qb2.call(Q)===Y66}Rb2.isPlainObject=J66;function X66(A){return A!=null&&typeof A=="object"}function V66(A){if(A==null)return A===void 0?F66:G66;return zu&&zu in Object(A)?C66(A):K66(A)}function C66(A){let B=Lb2.call(A,zu),Q=A[zu],D=!1;try{A[zu]=void 0,D=!0}catch(G){}let Z=Mb2.call(A);if(D)if(B)A[zu]=Q;else delete A[zu];return Z}function K66(A){return Mb2.call(A)}});
var Tv2=E((Lv2)=>{Object.defineProperty(Lv2,"__esModule",{value:!0});Lv2.SEMRESATTRS_K8S_STATEFULSET_NAME=Lv2.SEMRESATTRS_K8S_STATEFULSET_UID=Lv2.SEMRESATTRS_K8S_DEPLOYMENT_NAME=Lv2.SEMRESATTRS_K8S_DEPLOYMENT_UID=Lv2.SEMRESATTRS_K8S_REPLICASET_NAME=Lv2.SEMRESATTRS_K8S_REPLICASET_UID=Lv2.SEMRESATTRS_K8S_CONTAINER_NAME=Lv2.SEMRESATTRS_K8S_POD_NAME=Lv2.SEMRESATTRS_K8S_POD_UID=Lv2.SEMRESATTRS_K8S_NAMESPACE_NAME=Lv2.SEMRESATTRS_K8S_NODE_UID=Lv2.SEMRESATTRS_K8S_NODE_NAME=Lv2.SEMRESATTRS_K8S_CLUSTER_NAME=Lv2.SEMRESATTRS_HOST_IMAGE_VERSION=Lv2.SEMRESATTRS_HOST_IMAGE_ID=Lv2.SEMRESATTRS_HOST_IMAGE_NAME=Lv2.SEMRESATTRS_HOST_ARCH=Lv2.SEMRESATTRS_HOST_TYPE=Lv2.SEMRESATTRS_HOST_NAME=Lv2.SEMRESATTRS_HOST_ID=Lv2.SEMRESATTRS_FAAS_MAX_MEMORY=Lv2.SEMRESATTRS_FAAS_INSTANCE=Lv2.SEMRESATTRS_FAAS_VERSION=Lv2.SEMRESATTRS_FAAS_ID=Lv2.SEMRESATTRS_FAAS_NAME=Lv2.SEMRESATTRS_DEVICE_MODEL_NAME=Lv2.SEMRESATTRS_DEVICE_MODEL_IDENTIFIER=Lv2.SEMRESATTRS_DEVICE_ID=Lv2.SEMRESATTRS_DEPLOYMENT_ENVIRONMENT=Lv2.SEMRESATTRS_CONTAINER_IMAGE_TAG=Lv2.SEMRESATTRS_CONTAINER_IMAGE_NAME=Lv2.SEMRESATTRS_CONTAINER_RUNTIME=Lv2.SEMRESATTRS_CONTAINER_ID=Lv2.SEMRESATTRS_CONTAINER_NAME=Lv2.SEMRESATTRS_AWS_LOG_STREAM_ARNS=Lv2.SEMRESATTRS_AWS_LOG_STREAM_NAMES=Lv2.SEMRESATTRS_AWS_LOG_GROUP_ARNS=Lv2.SEMRESATTRS_AWS_LOG_GROUP_NAMES=Lv2.SEMRESATTRS_AWS_EKS_CLUSTER_ARN=Lv2.SEMRESATTRS_AWS_ECS_TASK_REVISION=Lv2.SEMRESATTRS_AWS_ECS_TASK_FAMILY=Lv2.SEMRESATTRS_AWS_ECS_TASK_ARN=Lv2.SEMRESATTRS_AWS_ECS_LAUNCHTYPE=Lv2.SEMRESATTRS_AWS_ECS_CLUSTER_ARN=Lv2.SEMRESATTRS_AWS_ECS_CONTAINER_ARN=Lv2.SEMRESATTRS_CLOUD_PLATFORM=Lv2.SEMRESATTRS_CLOUD_AVAILABILITY_ZONE=Lv2.SEMRESATTRS_CLOUD_REGION=Lv2.SEMRESATTRS_CLOUD_ACCOUNT_ID=Lv2.SEMRESATTRS_CLOUD_PROVIDER=void 0;Lv2.CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE=Lv2.CLOUDPLATFORMVALUES_AZURE_APP_SERVICE=Lv2.CLOUDPLATFORMVALUES_AZURE_FUNCTIONS=Lv2.CLOUDPLATFORMVALUES_AZURE_AKS=Lv2.CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES=Lv2.CLOUDPLATFORMVALUES_AZURE_VM=Lv2.CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK=Lv2.CLOUDPLATFORMVALUES_AWS_LAMBDA=Lv2.CLOUDPLATFORMVALUES_AWS_EKS=Lv2.CLOUDPLATFORMVALUES_AWS_ECS=Lv2.CLOUDPLATFORMVALUES_AWS_EC2=Lv2.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC=Lv2.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS=Lv2.CloudProviderValues=Lv2.CLOUDPROVIDERVALUES_GCP=Lv2.CLOUDPROVIDERVALUES_AZURE=Lv2.CLOUDPROVIDERVALUES_AWS=Lv2.CLOUDPROVIDERVALUES_ALIBABA_CLOUD=Lv2.SemanticResourceAttributes=Lv2.SEMRESATTRS_WEBENGINE_DESCRIPTION=Lv2.SEMRESATTRS_WEBENGINE_VERSION=Lv2.SEMRESATTRS_WEBENGINE_NAME=Lv2.SEMRESATTRS_TELEMETRY_AUTO_VERSION=Lv2.SEMRESATTRS_TELEMETRY_SDK_VERSION=Lv2.SEMRESATTRS_TELEMETRY_SDK_LANGUAGE=Lv2.SEMRESATTRS_TELEMETRY_SDK_NAME=Lv2.SEMRESATTRS_SERVICE_VERSION=Lv2.SEMRESATTRS_SERVICE_INSTANCE_ID=Lv2.SEMRESATTRS_SERVICE_NAMESPACE=Lv2.SEMRESATTRS_SERVICE_NAME=Lv2.SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION=Lv2.SEMRESATTRS_PROCESS_RUNTIME_VERSION=Lv2.SEMRESATTRS_PROCESS_RUNTIME_NAME=Lv2.SEMRESATTRS_PROCESS_OWNER=Lv2.SEMRESATTRS_PROCESS_COMMAND_ARGS=Lv2.SEMRESATTRS_PROCESS_COMMAND_LINE=Lv2.SEMRESATTRS_PROCESS_COMMAND=Lv2.SEMRESATTRS_PROCESS_EXECUTABLE_PATH=Lv2.SEMRESATTRS_PROCESS_EXECUTABLE_NAME=Lv2.SEMRESATTRS_PROCESS_PID=Lv2.SEMRESATTRS_OS_VERSION=Lv2.SEMRESATTRS_OS_NAME=Lv2.SEMRESATTRS_OS_DESCRIPTION=Lv2.SEMRESATTRS_OS_TYPE=Lv2.SEMRESATTRS_K8S_CRONJOB_NAME=Lv2.SEMRESATTRS_K8S_CRONJOB_UID=Lv2.SEMRESATTRS_K8S_JOB_NAME=Lv2.SEMRESATTRS_K8S_JOB_UID=Lv2.SEMRESATTRS_K8S_DAEMONSET_NAME=Lv2.SEMRESATTRS_K8S_DAEMONSET_UID=void 0;Lv2.TelemetrySdkLanguageValues=Lv2.TELEMETRYSDKLANGUAGEVALUES_WEBJS=Lv2.TELEMETRYSDKLANGUAGEVALUES_RUBY=Lv2.TELEMETRYSDKLANGUAGEVALUES_PYTHON=Lv2.TELEMETRYSDKLANGUAGEVALUES_PHP=Lv2.TELEMETRYSDKLANGUAGEVALUES_NODEJS=Lv2.TELEMETRYSDKLANGUAGEVALUES_JAVA=Lv2.TELEMETRYSDKLANGUAGEVALUES_GO=Lv2.TELEMETRYSDKLANGUAGEVALUES_ERLANG=Lv2.TELEMETRYSDKLANGUAGEVALUES_DOTNET=Lv2.TELEMETRYSDKLANGUAGEVALUES_CPP=Lv2.OsTypeValues=Lv2.OSTYPEVALUES_Z_OS=Lv2.OSTYPEVALUES_SOLARIS=Lv2.OSTYPEVALUES_AIX=Lv2.OSTYPEVALUES_HPUX=Lv2.OSTYPEVALUES_DRAGONFLYBSD=Lv2.OSTYPEVALUES_OPENBSD=Lv2.OSTYPEVALUES_NETBSD=Lv2.OSTYPEVALUES_FREEBSD=Lv2.OSTYPEVALUES_DARWIN=Lv2.OSTYPEVALUES_LINUX=Lv2.OSTYPEVALUES_WINDOWS=Lv2.HostArchValues=Lv2.HOSTARCHVALUES_X86=Lv2.HOSTARCHVALUES_PPC64=Lv2.HOSTARCHVALUES_PPC32=Lv2.HOSTARCHVALUES_IA64=Lv2.HOSTARCHVALUES_ARM64=Lv2.HOSTARCHVALUES_ARM32=Lv2.HOSTARCHVALUES_AMD64=Lv2.AwsEcsLaunchtypeValues=Lv2.AWSECSLAUNCHTYPEVALUES_FARGATE=Lv2.AWSECSLAUNCHTYPEVALUES_EC2=Lv2.CloudPlatformValues=Lv2.CLOUDPLATFORMVALUES_GCP_APP_ENGINE=Lv2.CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS=Lv2.CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE=Lv2.CLOUDPLATFORMVALUES_GCP_CLOUD_RUN=void 0;var Ku=MF0(),ey2="cloud.provider",A_2="cloud.account.id",B_2="cloud.region",Q_2="cloud.availability_zone",D_2="cloud.platform",Z_2="aws.ecs.container.arn",G_2="aws.ecs.cluster.arn",F_2="aws.ecs.launchtype",I_2="aws.ecs.task.arn",Y_2="aws.ecs.task.family",W_2="aws.ecs.task.revision",J_2="aws.eks.cluster.arn",X_2="aws.log.group.names",V_2="aws.log.group.arns",C_2="aws.log.stream.names",K_2="aws.log.stream.arns",H_2="container.name",z_2="container.id",E_2="container.runtime",U_2="container.image.name",w_2="container.image.tag",$_2="deployment.environment",q_2="device.id",N_2="device.model.identifier",L_2="device.model.name",M_2="faas.name",R_2="faas.id",O_2="faas.version",T_2="faas.instance",P_2="faas.max_memory",S_2="host.id",j_2="host.name",k_2="host.type",y_2="host.arch",__2="host.image.name",x_2="host.image.id",v_2="host.image.version",b_2="k8s.cluster.name",f_2="k8s.node.name",h_2="k8s.node.uid",g_2="k8s.namespace.name",u_2="k8s.pod.uid",m_2="k8s.pod.name",d_2="k8s.container.name",c_2="k8s.replicaset.uid",l_2="k8s.replicaset.name",p_2="k8s.deployment.uid",i_2="k8s.deployment.name",n_2="k8s.statefulset.uid",a_2="k8s.statefulset.name",s_2="k8s.daemonset.uid",r_2="k8s.daemonset.name",o_2="k8s.job.uid",t_2="k8s.job.name",e_2="k8s.cronjob.uid",Ax2="k8s.cronjob.name",Bx2="os.type",Qx2="os.description",Dx2="os.name",Zx2="os.version",Gx2="process.pid",Fx2="process.executable.name",Ix2="process.executable.path",Yx2="process.command",Wx2="process.command_line",Jx2="process.command_args",Xx2="process.owner",Vx2="process.runtime.name",Cx2="process.runtime.version",Kx2="process.runtime.description",Hx2="service.name",zx2="service.namespace",Ex2="service.instance.id",Ux2="service.version",wx2="telemetry.sdk.name",$x2="telemetry.sdk.language",qx2="telemetry.sdk.version",Nx2="telemetry.auto.version",Lx2="webengine.name",Mx2="webengine.version",Rx2="webengine.description";Lv2.SEMRESATTRS_CLOUD_PROVIDER=ey2;Lv2.SEMRESATTRS_CLOUD_ACCOUNT_ID=A_2;Lv2.SEMRESATTRS_CLOUD_REGION=B_2;Lv2.SEMRESATTRS_CLOUD_AVAILABILITY_ZONE=Q_2;Lv2.SEMRESATTRS_CLOUD_PLATFORM=D_2;Lv2.SEMRESATTRS_AWS_ECS_CONTAINER_ARN=Z_2;Lv2.SEMRESATTRS_AWS_ECS_CLUSTER_ARN=G_2;Lv2.SEMRESATTRS_AWS_ECS_LAUNCHTYPE=F_2;Lv2.SEMRESATTRS_AWS_ECS_TASK_ARN=I_2;Lv2.SEMRESATTRS_AWS_ECS_TASK_FAMILY=Y_2;Lv2.SEMRESATTRS_AWS_ECS_TASK_REVISION=W_2;Lv2.SEMRESATTRS_AWS_EKS_CLUSTER_ARN=J_2;Lv2.SEMRESATTRS_AWS_LOG_GROUP_NAMES=X_2;Lv2.SEMRESATTRS_AWS_LOG_GROUP_ARNS=V_2;Lv2.SEMRESATTRS_AWS_LOG_STREAM_NAMES=C_2;Lv2.SEMRESATTRS_AWS_LOG_STREAM_ARNS=K_2;Lv2.SEMRESATTRS_CONTAINER_NAME=H_2;Lv2.SEMRESATTRS_CONTAINER_ID=z_2;Lv2.SEMRESATTRS_CONTAINER_RUNTIME=E_2;Lv2.SEMRESATTRS_CONTAINER_IMAGE_NAME=U_2;Lv2.SEMRESATTRS_CONTAINER_IMAGE_TAG=w_2;Lv2.SEMRESATTRS_DEPLOYMENT_ENVIRONMENT=$_2;Lv2.SEMRESATTRS_DEVICE_ID=q_2;Lv2.SEMRESATTRS_DEVICE_MODEL_IDENTIFIER=N_2;Lv2.SEMRESATTRS_DEVICE_MODEL_NAME=L_2;Lv2.SEMRESATTRS_FAAS_NAME=M_2;Lv2.SEMRESATTRS_FAAS_ID=R_2;Lv2.SEMRESATTRS_FAAS_VERSION=O_2;Lv2.SEMRESATTRS_FAAS_INSTANCE=T_2;Lv2.SEMRESATTRS_FAAS_MAX_MEMORY=P_2;Lv2.SEMRESATTRS_HOST_ID=S_2;Lv2.SEMRESATTRS_HOST_NAME=j_2;Lv2.SEMRESATTRS_HOST_TYPE=k_2;Lv2.SEMRESATTRS_HOST_ARCH=y_2;Lv2.SEMRESATTRS_HOST_IMAGE_NAME=__2;Lv2.SEMRESATTRS_HOST_IMAGE_ID=x_2;Lv2.SEMRESATTRS_HOST_IMAGE_VERSION=v_2;Lv2.SEMRESATTRS_K8S_CLUSTER_NAME=b_2;Lv2.SEMRESATTRS_K8S_NODE_NAME=f_2;Lv2.SEMRESATTRS_K8S_NODE_UID=h_2;Lv2.SEMRESATTRS_K8S_NAMESPACE_NAME=g_2;Lv2.SEMRESATTRS_K8S_POD_UID=u_2;Lv2.SEMRESATTRS_K8S_POD_NAME=m_2;Lv2.SEMRESATTRS_K8S_CONTAINER_NAME=d_2;Lv2.SEMRESATTRS_K8S_REPLICASET_UID=c_2;Lv2.SEMRESATTRS_K8S_REPLICASET_NAME=l_2;Lv2.SEMRESATTRS_K8S_DEPLOYMENT_UID=p_2;Lv2.SEMRESATTRS_K8S_DEPLOYMENT_NAME=i_2;Lv2.SEMRESATTRS_K8S_STATEFULSET_UID=n_2;Lv2.SEMRESATTRS_K8S_STATEFULSET_NAME=a_2;Lv2.SEMRESATTRS_K8S_DAEMONSET_UID=s_2;Lv2.SEMRESATTRS_K8S_DAEMONSET_NAME=r_2;Lv2.SEMRESATTRS_K8S_JOB_UID=o_2;Lv2.SEMRESATTRS_K8S_JOB_NAME=t_2;Lv2.SEMRESATTRS_K8S_CRONJOB_UID=e_2;Lv2.SEMRESATTRS_K8S_CRONJOB_NAME=Ax2;Lv2.SEMRESATTRS_OS_TYPE=Bx2;Lv2.SEMRESATTRS_OS_DESCRIPTION=Qx2;Lv2.SEMRESATTRS_OS_NAME=Dx2;Lv2.SEMRESATTRS_OS_VERSION=Zx2;Lv2.SEMRESATTRS_PROCESS_PID=Gx2;Lv2.SEMRESATTRS_PROCESS_EXECUTABLE_NAME=Fx2;Lv2.SEMRESATTRS_PROCESS_EXECUTABLE_PATH=Ix2;Lv2.SEMRESATTRS_PROCESS_COMMAND=Yx2;Lv2.SEMRESATTRS_PROCESS_COMMAND_LINE=Wx2;Lv2.SEMRESATTRS_PROCESS_COMMAND_ARGS=Jx2;Lv2.SEMRESATTRS_PROCESS_OWNER=Xx2;Lv2.SEMRESATTRS_PROCESS_RUNTIME_NAME=Vx2;Lv2.SEMRESATTRS_PROCESS_RUNTIME_VERSION=Cx2;Lv2.SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION=Kx2;Lv2.SEMRESATTRS_SERVICE_NAME=Hx2;Lv2.SEMRESATTRS_SERVICE_NAMESPACE=zx2;Lv2.SEMRESATTRS_SERVICE_INSTANCE_ID=Ex2;Lv2.SEMRESATTRS_SERVICE_VERSION=Ux2;Lv2.SEMRESATTRS_TELEMETRY_SDK_NAME=wx2;Lv2.SEMRESATTRS_TELEMETRY_SDK_LANGUAGE=$x2;Lv2.SEMRESATTRS_TELEMETRY_SDK_VERSION=qx2;Lv2.SEMRESATTRS_TELEMETRY_AUTO_VERSION=Nx2;Lv2.SEMRESATTRS_WEBENGINE_NAME=Lx2;Lv2.SEMRESATTRS_WEBENGINE_VERSION=Mx2;Lv2.SEMRESATTRS_WEBENGINE_DESCRIPTION=Rx2;Lv2.SemanticResourceAttributes=Ku.createConstMap([ey2,A_2,B_2,Q_2,D_2,Z_2,G_2,F_2,I_2,Y_2,W_2,J_2,X_2,V_2,C_2,K_2,H_2,z_2,E_2,U_2,w_2,$_2,q_2,N_2,L_2,M_2,R_2,O_2,T_2,P_2,S_2,j_2,k_2,y_2,__2,x_2,v_2,b_2,f_2,h_2,g_2,u_2,m_2,d_2,c_2,l_2,p_2,i_2,n_2,a_2,s_2,r_2,o_2,t_2,e_2,Ax2,Bx2,Qx2,Dx2,Zx2,Gx2,Fx2,Ix2,Yx2,Wx2,Jx2,Xx2,Vx2,Cx2,Kx2,Hx2,zx2,Ex2,Ux2,wx2,$x2,qx2,Nx2,Lx2,Mx2,Rx2]);var Ox2="alibaba_cloud",Tx2="aws",Px2="azure",Sx2="gcp";Lv2.CLOUDPROVIDERVALUES_ALIBABA_CLOUD=Ox2;Lv2.CLOUDPROVIDERVALUES_AWS=Tx2;Lv2.CLOUDPROVIDERVALUES_AZURE=Px2;Lv2.CLOUDPROVIDERVALUES_GCP=Sx2;Lv2.CloudProviderValues=Ku.createConstMap([Ox2,Tx2,Px2,Sx2]);var jx2="alibaba_cloud_ecs",kx2="alibaba_cloud_fc",yx2="aws_ec2",_x2="aws_ecs",xx2="aws_eks",vx2="aws_lambda",bx2="aws_elastic_beanstalk",fx2="azure_vm",hx2="azure_container_instances",gx2="azure_aks",ux2="azure_functions",mx2="azure_app_service",dx2="gcp_compute_engine",cx2="gcp_cloud_run",lx2="gcp_kubernetes_engine",px2="gcp_cloud_functions",ix2="gcp_app_engine";Lv2.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS=jx2;Lv2.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC=kx2;Lv2.CLOUDPLATFORMVALUES_AWS_EC2=yx2;Lv2.CLOUDPLATFORMVALUES_AWS_ECS=_x2;Lv2.CLOUDPLATFORMVALUES_AWS_EKS=xx2;Lv2.CLOUDPLATFORMVALUES_AWS_LAMBDA=vx2;Lv2.CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK=bx2;Lv2.CLOUDPLATFORMVALUES_AZURE_VM=fx2;Lv2.CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES=hx2;Lv2.CLOUDPLATFORMVALUES_AZURE_AKS=gx2;Lv2.CLOUDPLATFORMVALUES_AZURE_FUNCTIONS=ux2;Lv2.CLOUDPLATFORMVALUES_AZURE_APP_SERVICE=mx2;Lv2.CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE=dx2;Lv2.CLOUDPLATFORMVALUES_GCP_CLOUD_RUN=cx2;Lv2.CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE=lx2;Lv2.CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS=px2;Lv2.CLOUDPLATFORMVALUES_GCP_APP_ENGINE=ix2;Lv2.CloudPlatformValues=Ku.createConstMap([jx2,kx2,yx2,_x2,xx2,vx2,bx2,fx2,hx2,gx2,ux2,mx2,dx2,cx2,lx2,px2,ix2]);var nx2="ec2",ax2="fargate";Lv2.AWSECSLAUNCHTYPEVALUES_EC2=nx2;Lv2.AWSECSLAUNCHTYPEVALUES_FARGATE=ax2;Lv2.AwsEcsLaunchtypeValues=Ku.createConstMap([nx2,ax2]);var sx2="amd64",rx2="arm32",ox2="arm64",tx2="ia64",ex2="ppc32",Av2="ppc64",Bv2="x86";Lv2.HOSTARCHVALUES_AMD64=sx2;Lv2.HOSTARCHVALUES_ARM32=rx2;Lv2.HOSTARCHVALUES_ARM64=ox2;Lv2.HOSTARCHVALUES_IA64=tx2;Lv2.HOSTARCHVALUES_PPC32=ex2;Lv2.HOSTARCHVALUES_PPC64=Av2;Lv2.HOSTARCHVALUES_X86=Bv2;Lv2.HostArchValues=Ku.createConstMap([sx2,rx2,ox2,tx2,ex2,Av2,Bv2]);var Qv2="windows",Dv2="linux",Zv2="darwin",Gv2="freebsd",Fv2="netbsd",Iv2="openbsd",Yv2="dragonflybsd",Wv2="hpux",Jv2="aix",Xv2="solaris",Vv2="z_os";Lv2.OSTYPEVALUES_WINDOWS=Qv2;Lv2.OSTYPEVALUES_LINUX=Dv2;Lv2.OSTYPEVALUES_DARWIN=Zv2;Lv2.OSTYPEVALUES_FREEBSD=Gv2;Lv2.OSTYPEVALUES_NETBSD=Fv2;Lv2.OSTYPEVALUES_OPENBSD=Iv2;Lv2.OSTYPEVALUES_DRAGONFLYBSD=Yv2;Lv2.OSTYPEVALUES_HPUX=Wv2;Lv2.OSTYPEVALUES_AIX=Jv2;Lv2.OSTYPEVALUES_SOLARIS=Xv2;Lv2.OSTYPEVALUES_Z_OS=Vv2;Lv2.OsTypeValues=Ku.createConstMap([Qv2,Dv2,Zv2,Gv2,Fv2,Iv2,Yv2,Wv2,Jv2,Xv2,Vv2]);var Cv2="cpp",Kv2="dotnet",Hv2="erlang",zv2="go",Ev2="java",Uv2="nodejs",wv2="php",$v2="python",qv2="ruby",Nv2="webjs";Lv2.TELEMETRYSDKLANGUAGEVALUES_CPP=Cv2;Lv2.TELEMETRYSDKLANGUAGEVALUES_DOTNET=Kv2;Lv2.TELEMETRYSDKLANGUAGEVALUES_ERLANG=Hv2;Lv2.TELEMETRYSDKLANGUAGEVALUES_GO=zv2;Lv2.TELEMETRYSDKLANGUAGEVALUES_JAVA=Ev2;Lv2.TELEMETRYSDKLANGUAGEVALUES_NODEJS=Uv2;Lv2.TELEMETRYSDKLANGUAGEVALUES_PHP=wv2;Lv2.TELEMETRYSDKLANGUAGEVALUES_PYTHON=$v2;Lv2.TELEMETRYSDKLANGUAGEVALUES_RUBY=qv2;Lv2.TELEMETRYSDKLANGUAGEVALUES_WEBJS=Nv2;Lv2.TelemetrySdkLanguageValues=Ku.createConstMap([Cv2,Kv2,Hv2,zv2,Ev2,Uv2,wv2,$v2,qv2,Nv2])});
var VO1=E((YM2)=>{Object.defineProperty(YM2,"__esModule",{value:!0});YM2.NonRecordingSpan=void 0;var oi4=XO1();class IM2{constructor(A=oi4.INVALID_SPAN_CONTEXT){this._spanContext=A}spanContext(){return this._spanContext}setAttribute(A,B){return this}setAttributes(A){return this}addEvent(A,B){return this}addLink(A){return this}addLinks(A){return this}setStatus(A){return this}updateName(A){return this}end(A){}isRecording(){return!1}recordException(A,B){}}YM2.NonRecordingSpan=IM2});
var VR2=E((JR2)=>{Object.defineProperty(JR2,"__esModule",{value:!0});JR2.metrics=void 0;var in4=WR2();JR2.metrics=in4.MetricsAPI.getInstance()});
var WR2=E((IR2)=>{Object.defineProperty(IR2,"__esModule",{value:!0});IR2.MetricsAPI=void 0;var pn4=GR2(),cG0=Wu(),FR2=Ju(),lG0="metrics";class pG0{constructor(){}static getInstance(){if(!this._instance)this._instance=new pG0;return this._instance}setGlobalMeterProvider(A){return cG0.registerGlobal(lG0,A,FR2.DiagAPI.instance())}getMeterProvider(){return cG0.getGlobal(lG0)||pn4.NOOP_METER_PROVIDER}getMeter(A,B,Q){return this.getMeterProvider().getMeter(A,B,Q)}disable(){cG0.unregisterGlobal(lG0,FR2.DiagAPI.instance())}}IR2.MetricsAPI=pG0});
var Wu=E((YL2)=>{Object.defineProperty(YL2,"__esModule",{value:!0});YL2.unregisterGlobal=YL2.getGlobal=YL2.registerGlobal=void 0;var Ji4=AL2(),Lo=YG0(),Xi4=IL2(),Vi4=Lo.VERSION.split(".")[0],x51=Symbol.for(`opentelemetry.js.api.${Vi4}`),v51=Ji4._globalThis;function Ci4(A,B,Q,D=!1){var Z;let G=v51[x51]=(Z=v51[x51])!==null&&Z!==void 0?Z:{version:Lo.VERSION};if(!D&&G[A]){let F=new Error(`@opentelemetry/api: Attempted duplicate registration of API: ${A}`);return Q.error(F.stack||F.message),!1}if(G.version!==Lo.VERSION){let F=new Error(`@opentelemetry/api: Registration of version v${G.version} for ${A} does not match previously registered API v${Lo.VERSION}`);return Q.error(F.stack||F.message),!1}return G[A]=B,Q.debug(`@opentelemetry/api: Registered a global for ${A} v${Lo.VERSION}.`),!0}YL2.registerGlobal=Ci4;function Ki4(A){var B,Q;let D=(B=v51[x51])===null||B===void 0?void 0:B.version;if(!D||!Xi4.isCompatible(D))return;return(Q=v51[x51])===null||Q===void 0?void 0:Q[A]}YL2.getGlobal=Ki4;function Hi4(A,B){B.debug(`@opentelemetry/api: Unregistering a global for ${A} v${Lo.VERSION}.`);let Q=v51[x51];if(Q)delete Q[A]}YL2.unregisterGlobal=Hi4});
var XG0=E((TL2)=>{Object.defineProperty(TL2,"__esModule",{value:!0});TL2.baggageEntryMetadataFromString=TL2.createBaggage=void 0;var Mi4=Ju(),Ri4=LL2(),Oi4=OL2(),Ti4=Mi4.DiagAPI.instance();function Pi4(A={}){return new Ri4.BaggageImpl(new Map(Object.entries(A)))}TL2.createBaggage=Pi4;function Si4(A){if(typeof A!=="string")Ti4.error(`Cannot create baggage metadata from unknown type: ${typeof A}`),A="";return{__TYPE__:Oi4.baggageEntryMetadataSymbol,toString(){return A}}}TL2.baggageEntryMetadataFromString=Si4});
var XO1=E((DM2)=>{Object.defineProperty(DM2,"__esModule",{value:!0});DM2.INVALID_SPAN_CONTEXT=DM2.INVALID_TRACEID=DM2.INVALID_SPANID=void 0;var ri4=PG0();DM2.INVALID_SPANID="0000000000000000";DM2.INVALID_TRACEID="00000000000000000000000000000000";DM2.INVALID_SPAN_CONTEXT={traceId:DM2.INVALID_TRACEID,spanId:DM2.INVALID_SPANID,traceFlags:ri4.TraceFlags.NONE}});
var YG0=E((BL2)=>{Object.defineProperty(BL2,"__esModule",{value:!0});BL2.VERSION=void 0;BL2.VERSION="1.9.0"});
var YO1=E((KL2)=>{Object.defineProperty(KL2,"__esModule",{value:!0});KL2.DiagLogLevel=void 0;var wi4;(function(A){A[A.NONE=0]="NONE",A[A.ERROR=30]="ERROR",A[A.WARN=50]="WARN",A[A.INFO=60]="INFO",A[A.DEBUG=70]="DEBUG",A[A.VERBOSE=80]="VERBOSE",A[A.ALL=9999]="ALL"})(wi4=KL2.DiagLogLevel||(KL2.DiagLogLevel={}))});
var ZQ=E((d5)=>{Object.defineProperty(d5,"__esModule",{value:!0});d5.trace=d5.propagation=d5.metrics=d5.diag=d5.context=d5.INVALID_SPAN_CONTEXT=d5.INVALID_TRACEID=d5.INVALID_SPANID=d5.isValidSpanId=d5.isValidTraceId=d5.isSpanContextValid=d5.createTraceState=d5.TraceFlags=d5.SpanStatusCode=d5.SpanKind=d5.SamplingDecision=d5.ProxyTracerProvider=d5.ProxyTracer=d5.defaultTextMapSetter=d5.defaultTextMapGetter=d5.ValueType=d5.createNoopMeter=d5.DiagLogLevel=d5.DiagConsoleLogger=d5.ROOT_CONTEXT=d5.createContextKey=d5.baggageEntryMetadataFromString=void 0;var Fa4=XG0();Object.defineProperty(d5,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return Fa4.baggageEntryMetadataFromString}});var hR2=f51();Object.defineProperty(d5,"createContextKey",{enumerable:!0,get:function(){return hR2.createContextKey}});Object.defineProperty(d5,"ROOT_CONTEXT",{enumerable:!0,get:function(){return hR2.ROOT_CONTEXT}});var Ia4=xL2();Object.defineProperty(d5,"DiagConsoleLogger",{enumerable:!0,get:function(){return Ia4.DiagConsoleLogger}});var Ya4=YO1();Object.defineProperty(d5,"DiagLogLevel",{enumerable:!0,get:function(){return Ya4.DiagLogLevel}});var Wa4=qG0();Object.defineProperty(d5,"createNoopMeter",{enumerable:!0,get:function(){return Wa4.createNoopMeter}});var Ja4=iL2();Object.defineProperty(d5,"ValueType",{enumerable:!0,get:function(){return Ja4.ValueType}});var gR2=LG0();Object.defineProperty(d5,"defaultTextMapGetter",{enumerable:!0,get:function(){return gR2.defaultTextMapGetter}});Object.defineProperty(d5,"defaultTextMapSetter",{enumerable:!0,get:function(){return gR2.defaultTextMapSetter}});var Xa4=vG0();Object.defineProperty(d5,"ProxyTracer",{enumerable:!0,get:function(){return Xa4.ProxyTracer}});var Va4=bG0();Object.defineProperty(d5,"ProxyTracerProvider",{enumerable:!0,get:function(){return Va4.ProxyTracerProvider}});var Ca4=_M2();Object.defineProperty(d5,"SamplingDecision",{enumerable:!0,get:function(){return Ca4.SamplingDecision}});var Ka4=vM2();Object.defineProperty(d5,"SpanKind",{enumerable:!0,get:function(){return Ka4.SpanKind}});var Ha4=fM2();Object.defineProperty(d5,"SpanStatusCode",{enumerable:!0,get:function(){return Ha4.SpanStatusCode}});var za4=PG0();Object.defineProperty(d5,"TraceFlags",{enumerable:!0,get:function(){return za4.TraceFlags}});var Ea4=rM2();Object.defineProperty(d5,"createTraceState",{enumerable:!0,get:function(){return Ea4.createTraceState}});var eG0=CO1();Object.defineProperty(d5,"isSpanContextValid",{enumerable:!0,get:function(){return eG0.isSpanContextValid}});Object.defineProperty(d5,"isValidTraceId",{enumerable:!0,get:function(){return eG0.isValidTraceId}});Object.defineProperty(d5,"isValidSpanId",{enumerable:!0,get:function(){return eG0.isValidSpanId}});var AF0=XO1();Object.defineProperty(d5,"INVALID_SPANID",{enumerable:!0,get:function(){return AF0.INVALID_SPANID}});Object.defineProperty(d5,"INVALID_TRACEID",{enumerable:!0,get:function(){return AF0.INVALID_TRACEID}});Object.defineProperty(d5,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return AF0.INVALID_SPAN_CONTEXT}});var uR2=eM2();Object.defineProperty(d5,"context",{enumerable:!0,get:function(){return uR2.context}});var mR2=QR2();Object.defineProperty(d5,"diag",{enumerable:!0,get:function(){return mR2.diag}});var dR2=VR2();Object.defineProperty(d5,"metrics",{enumerable:!0,get:function(){return dR2.metrics}});var cR2=PR2();Object.defineProperty(d5,"propagation",{enumerable:!0,get:function(){return cR2.propagation}});var lR2=fR2();Object.defineProperty(d5,"trace",{enumerable:!0,get:function(){return lR2.trace}});d5.default={context:uR2.context,diag:mR2.diag,metrics:dR2.metrics,propagation:cR2.propagation,trace:lR2.trace}});
var _M2=E((yM2)=>{Object.defineProperty(yM2,"__esModule",{value:!0});yM2.SamplingDecision=void 0;var Tn4;(function(A){A[A.NOT_RECORD=0]="NOT_RECORD",A[A.RECORD=1]="RECORD",A[A.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"})(Tn4=yM2.SamplingDecision||(yM2.SamplingDecision={}))});
var _v2=E((Sv2)=>{Object.defineProperty(Sv2,"__esModule",{value:!0});Sv2.ATTR_JVM_GC_NAME=Sv2.ATTR_JVM_GC_ACTION=Sv2.ATTR_HTTP_ROUTE=Sv2.ATTR_HTTP_RESPONSE_STATUS_CODE=Sv2.ATTR_HTTP_RESPONSE_HEADER=Sv2.ATTR_HTTP_REQUEST_RESEND_COUNT=Sv2.ATTR_HTTP_REQUEST_METHOD_ORIGINAL=Sv2.HTTP_REQUEST_METHOD_VALUE_TRACE=Sv2.HTTP_REQUEST_METHOD_VALUE_PUT=Sv2.HTTP_REQUEST_METHOD_VALUE_POST=Sv2.HTTP_REQUEST_METHOD_VALUE_PATCH=Sv2.HTTP_REQUEST_METHOD_VALUE_OPTIONS=Sv2.HTTP_REQUEST_METHOD_VALUE_HEAD=Sv2.HTTP_REQUEST_METHOD_VALUE_GET=Sv2.HTTP_REQUEST_METHOD_VALUE_DELETE=Sv2.HTTP_REQUEST_METHOD_VALUE_CONNECT=Sv2.HTTP_REQUEST_METHOD_VALUE_OTHER=Sv2.ATTR_HTTP_REQUEST_METHOD=Sv2.ATTR_HTTP_REQUEST_HEADER=Sv2.ATTR_EXCEPTION_TYPE=Sv2.ATTR_EXCEPTION_STACKTRACE=Sv2.ATTR_EXCEPTION_MESSAGE=Sv2.ATTR_EXCEPTION_ESCAPED=Sv2.ERROR_TYPE_VALUE_OTHER=Sv2.ATTR_ERROR_TYPE=Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_POH=Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_LOH=Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN2=Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN1=Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN0=Sv2.ATTR_DOTNET_GC_HEAP_GENERATION=Sv2.ATTR_CLIENT_PORT=Sv2.ATTR_CLIENT_ADDRESS=Sv2.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS=Sv2.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE=Sv2.ATTR_ASPNETCORE_ROUTING_MATCH_STATUS=Sv2.ATTR_ASPNETCORE_ROUTING_IS_FALLBACK=Sv2.ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED=Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED=Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER=Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER=Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED=Sv2.ATTR_ASPNETCORE_RATE_LIMITING_RESULT=Sv2.ATTR_ASPNETCORE_RATE_LIMITING_POLICY=Sv2.ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE=Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED=Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED=Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED=Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED=Sv2.ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT=void 0;Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_GO=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_CPP=Sv2.ATTR_TELEMETRY_SDK_LANGUAGE=Sv2.SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS=Sv2.SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS=Sv2.SIGNALR_TRANSPORT_VALUE_LONG_POLLING=Sv2.ATTR_SIGNALR_TRANSPORT=Sv2.SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT=Sv2.SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE=Sv2.SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN=Sv2.ATTR_SIGNALR_CONNECTION_STATUS=Sv2.ATTR_SERVICE_VERSION=Sv2.ATTR_SERVICE_NAME=Sv2.ATTR_SERVER_PORT=Sv2.ATTR_SERVER_ADDRESS=Sv2.ATTR_OTEL_STATUS_DESCRIPTION=Sv2.OTEL_STATUS_CODE_VALUE_OK=Sv2.OTEL_STATUS_CODE_VALUE_ERROR=Sv2.ATTR_OTEL_STATUS_CODE=Sv2.ATTR_OTEL_SCOPE_VERSION=Sv2.ATTR_OTEL_SCOPE_NAME=Sv2.NETWORK_TYPE_VALUE_IPV6=Sv2.NETWORK_TYPE_VALUE_IPV4=Sv2.ATTR_NETWORK_TYPE=Sv2.NETWORK_TRANSPORT_VALUE_UNIX=Sv2.NETWORK_TRANSPORT_VALUE_UDP=Sv2.NETWORK_TRANSPORT_VALUE_TCP=Sv2.NETWORK_TRANSPORT_VALUE_QUIC=Sv2.NETWORK_TRANSPORT_VALUE_PIPE=Sv2.ATTR_NETWORK_TRANSPORT=Sv2.ATTR_NETWORK_PROTOCOL_VERSION=Sv2.ATTR_NETWORK_PROTOCOL_NAME=Sv2.ATTR_NETWORK_PEER_PORT=Sv2.ATTR_NETWORK_PEER_ADDRESS=Sv2.ATTR_NETWORK_LOCAL_PORT=Sv2.ATTR_NETWORK_LOCAL_ADDRESS=Sv2.JVM_THREAD_STATE_VALUE_WAITING=Sv2.JVM_THREAD_STATE_VALUE_TIMED_WAITING=Sv2.JVM_THREAD_STATE_VALUE_TERMINATED=Sv2.JVM_THREAD_STATE_VALUE_RUNNABLE=Sv2.JVM_THREAD_STATE_VALUE_NEW=Sv2.JVM_THREAD_STATE_VALUE_BLOCKED=Sv2.ATTR_JVM_THREAD_STATE=Sv2.ATTR_JVM_THREAD_DAEMON=Sv2.JVM_MEMORY_TYPE_VALUE_NON_HEAP=Sv2.JVM_MEMORY_TYPE_VALUE_HEAP=Sv2.ATTR_JVM_MEMORY_TYPE=Sv2.ATTR_JVM_MEMORY_POOL_NAME=void 0;Sv2.ATTR_USER_AGENT_ORIGINAL=Sv2.ATTR_URL_SCHEME=Sv2.ATTR_URL_QUERY=Sv2.ATTR_URL_PATH=Sv2.ATTR_URL_FULL=Sv2.ATTR_URL_FRAGMENT=Sv2.ATTR_TELEMETRY_SDK_VERSION=Sv2.ATTR_TELEMETRY_SDK_NAME=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_RUST=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_RUBY=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_PHP=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_JAVA=void 0;Sv2.ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT="aspnetcore.diagnostics.exception.result";Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED="aborted";Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED="handled";Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED="skipped";Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED="unhandled";Sv2.ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE="aspnetcore.diagnostics.handler.type";Sv2.ATTR_ASPNETCORE_RATE_LIMITING_POLICY="aspnetcore.rate_limiting.policy";Sv2.ATTR_ASPNETCORE_RATE_LIMITING_RESULT="aspnetcore.rate_limiting.result";Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED="acquired";Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER="endpoint_limiter";Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER="global_limiter";Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED="request_canceled";Sv2.ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED="aspnetcore.request.is_unhandled";Sv2.ATTR_ASPNETCORE_ROUTING_IS_FALLBACK="aspnetcore.routing.is_fallback";Sv2.ATTR_ASPNETCORE_ROUTING_MATCH_STATUS="aspnetcore.routing.match_status";Sv2.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE="failure";Sv2.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS="success";Sv2.ATTR_CLIENT_ADDRESS="client.address";Sv2.ATTR_CLIENT_PORT="client.port";Sv2.ATTR_DOTNET_GC_HEAP_GENERATION="dotnet.gc.heap.generation";Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN0="gen0";Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN1="gen1";Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN2="gen2";Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_LOH="loh";Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_POH="poh";Sv2.ATTR_ERROR_TYPE="error.type";Sv2.ERROR_TYPE_VALUE_OTHER="_OTHER";Sv2.ATTR_EXCEPTION_ESCAPED="exception.escaped";Sv2.ATTR_EXCEPTION_MESSAGE="exception.message";Sv2.ATTR_EXCEPTION_STACKTRACE="exception.stacktrace";Sv2.ATTR_EXCEPTION_TYPE="exception.type";var BB6=(A)=>`http.request.header.${A}`;Sv2.ATTR_HTTP_REQUEST_HEADER=BB6;Sv2.ATTR_HTTP_REQUEST_METHOD="http.request.method";Sv2.HTTP_REQUEST_METHOD_VALUE_OTHER="_OTHER";Sv2.HTTP_REQUEST_METHOD_VALUE_CONNECT="CONNECT";Sv2.HTTP_REQUEST_METHOD_VALUE_DELETE="DELETE";Sv2.HTTP_REQUEST_METHOD_VALUE_GET="GET";Sv2.HTTP_REQUEST_METHOD_VALUE_HEAD="HEAD";Sv2.HTTP_REQUEST_METHOD_VALUE_OPTIONS="OPTIONS";Sv2.HTTP_REQUEST_METHOD_VALUE_PATCH="PATCH";Sv2.HTTP_REQUEST_METHOD_VALUE_POST="POST";Sv2.HTTP_REQUEST_METHOD_VALUE_PUT="PUT";Sv2.HTTP_REQUEST_METHOD_VALUE_TRACE="TRACE";Sv2.ATTR_HTTP_REQUEST_METHOD_ORIGINAL="http.request.method_original";Sv2.ATTR_HTTP_REQUEST_RESEND_COUNT="http.request.resend_count";var QB6=(A)=>`http.response.header.${A}`;Sv2.ATTR_HTTP_RESPONSE_HEADER=QB6;Sv2.ATTR_HTTP_RESPONSE_STATUS_CODE="http.response.status_code";Sv2.ATTR_HTTP_ROUTE="http.route";Sv2.ATTR_JVM_GC_ACTION="jvm.gc.action";Sv2.ATTR_JVM_GC_NAME="jvm.gc.name";Sv2.ATTR_JVM_MEMORY_POOL_NAME="jvm.memory.pool.name";Sv2.ATTR_JVM_MEMORY_TYPE="jvm.memory.type";Sv2.JVM_MEMORY_TYPE_VALUE_HEAP="heap";Sv2.JVM_MEMORY_TYPE_VALUE_NON_HEAP="non_heap";Sv2.ATTR_JVM_THREAD_DAEMON="jvm.thread.daemon";Sv2.ATTR_JVM_THREAD_STATE="jvm.thread.state";Sv2.JVM_THREAD_STATE_VALUE_BLOCKED="blocked";Sv2.JVM_THREAD_STATE_VALUE_NEW="new";Sv2.JVM_THREAD_STATE_VALUE_RUNNABLE="runnable";Sv2.JVM_THREAD_STATE_VALUE_TERMINATED="terminated";Sv2.JVM_THREAD_STATE_VALUE_TIMED_WAITING="timed_waiting";Sv2.JVM_THREAD_STATE_VALUE_WAITING="waiting";Sv2.ATTR_NETWORK_LOCAL_ADDRESS="network.local.address";Sv2.ATTR_NETWORK_LOCAL_PORT="network.local.port";Sv2.ATTR_NETWORK_PEER_ADDRESS="network.peer.address";Sv2.ATTR_NETWORK_PEER_PORT="network.peer.port";Sv2.ATTR_NETWORK_PROTOCOL_NAME="network.protocol.name";Sv2.ATTR_NETWORK_PROTOCOL_VERSION="network.protocol.version";Sv2.ATTR_NETWORK_TRANSPORT="network.transport";Sv2.NETWORK_TRANSPORT_VALUE_PIPE="pipe";Sv2.NETWORK_TRANSPORT_VALUE_QUIC="quic";Sv2.NETWORK_TRANSPORT_VALUE_TCP="tcp";Sv2.NETWORK_TRANSPORT_VALUE_UDP="udp";Sv2.NETWORK_TRANSPORT_VALUE_UNIX="unix";Sv2.ATTR_NETWORK_TYPE="network.type";Sv2.NETWORK_TYPE_VALUE_IPV4="ipv4";Sv2.NETWORK_TYPE_VALUE_IPV6="ipv6";Sv2.ATTR_OTEL_SCOPE_NAME="otel.scope.name";Sv2.ATTR_OTEL_SCOPE_VERSION="otel.scope.version";Sv2.ATTR_OTEL_STATUS_CODE="otel.status_code";Sv2.OTEL_STATUS_CODE_VALUE_ERROR="ERROR";Sv2.OTEL_STATUS_CODE_VALUE_OK="OK";Sv2.ATTR_OTEL_STATUS_DESCRIPTION="otel.status_description";Sv2.ATTR_SERVER_ADDRESS="server.address";Sv2.ATTR_SERVER_PORT="server.port";Sv2.ATTR_SERVICE_NAME="service.name";Sv2.ATTR_SERVICE_VERSION="service.version";Sv2.ATTR_SIGNALR_CONNECTION_STATUS="signalr.connection.status";Sv2.SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN="app_shutdown";Sv2.SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE="normal_closure";Sv2.SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT="timeout";Sv2.ATTR_SIGNALR_TRANSPORT="signalr.transport";Sv2.SIGNALR_TRANSPORT_VALUE_LONG_POLLING="long_polling";Sv2.SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS="server_sent_events";Sv2.SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS="web_sockets";Sv2.ATTR_TELEMETRY_SDK_LANGUAGE="telemetry.sdk.language";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_CPP="cpp";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET="dotnet";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG="erlang";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_GO="go";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_JAVA="java";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS="nodejs";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_PHP="php";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON="python";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_RUBY="ruby";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_RUST="rust";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT="swift";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS="webjs";Sv2.ATTR_TELEMETRY_SDK_NAME="telemetry.sdk.name";Sv2.ATTR_TELEMETRY_SDK_VERSION="telemetry.sdk.version";Sv2.ATTR_URL_FRAGMENT="url.fragment";Sv2.ATTR_URL_FULL="url.full";Sv2.ATTR_URL_PATH="url.path";Sv2.ATTR_URL_QUERY="url.query";Sv2.ATTR_URL_SCHEME="url.scheme";Sv2.ATTR_USER_AGENT_ORIGINAL="user_agent.original"});
var bG0=E((jM2)=>{Object.defineProperty(jM2,"__esModule",{value:!0});jM2.ProxyTracerProvider=void 0;var Mn4=vG0(),Rn4=PM2(),On4=new Rn4.NoopTracerProvider;class SM2{getTracer(A,B,Q){var D;return(D=this.getDelegateTracer(A,B,Q))!==null&&D!==void 0?D:new Mn4.ProxyTracer(this,A,B,Q)}getDelegate(){var A;return(A=this._delegate)!==null&&A!==void 0?A:On4}setDelegate(A){this._delegate=A}getDelegateTracer(A,B,Q){var D;return(D=this._delegate)===null||D===void 0?void 0:D.getTracer(A,B,Q)}}jM2.ProxyTracerProvider=SM2});
var bv2=E((xv2)=>{Object.defineProperty(xv2,"__esModule",{value:!0});xv2.METRIC_SIGNALR_SERVER_CONNECTION_DURATION=xv2.METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS=xv2.METRIC_KESTREL_UPGRADED_CONNECTIONS=xv2.METRIC_KESTREL_TLS_HANDSHAKE_DURATION=xv2.METRIC_KESTREL_REJECTED_CONNECTIONS=xv2.METRIC_KESTREL_QUEUED_REQUESTS=xv2.METRIC_KESTREL_QUEUED_CONNECTIONS=xv2.METRIC_KESTREL_CONNECTION_DURATION=xv2.METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES=xv2.METRIC_KESTREL_ACTIVE_CONNECTIONS=xv2.METRIC_JVM_THREAD_COUNT=xv2.METRIC_JVM_MEMORY_USED_AFTER_LAST_GC=xv2.METRIC_JVM_MEMORY_USED=xv2.METRIC_JVM_MEMORY_LIMIT=xv2.METRIC_JVM_MEMORY_COMMITTED=xv2.METRIC_JVM_GC_DURATION=xv2.METRIC_JVM_CPU_TIME=xv2.METRIC_JVM_CPU_RECENT_UTILIZATION=xv2.METRIC_JVM_CPU_COUNT=xv2.METRIC_JVM_CLASS_UNLOADED=xv2.METRIC_JVM_CLASS_LOADED=xv2.METRIC_JVM_CLASS_COUNT=xv2.METRIC_HTTP_SERVER_REQUEST_DURATION=xv2.METRIC_HTTP_CLIENT_REQUEST_DURATION=xv2.METRIC_DOTNET_TIMER_COUNT=xv2.METRIC_DOTNET_THREAD_POOL_WORK_ITEM_COUNT=xv2.METRIC_DOTNET_THREAD_POOL_THREAD_COUNT=xv2.METRIC_DOTNET_THREAD_POOL_QUEUE_LENGTH=xv2.METRIC_DOTNET_PROCESS_MEMORY_WORKING_SET=xv2.METRIC_DOTNET_PROCESS_CPU_TIME=xv2.METRIC_DOTNET_PROCESS_CPU_COUNT=xv2.METRIC_DOTNET_MONITOR_LOCK_CONTENTIONS=xv2.METRIC_DOTNET_JIT_COMPILED_METHODS=xv2.METRIC_DOTNET_JIT_COMPILED_IL_SIZE=xv2.METRIC_DOTNET_JIT_COMPILATION_TIME=xv2.METRIC_DOTNET_GC_PAUSE_TIME=xv2.METRIC_DOTNET_GC_LAST_COLLECTION_MEMORY_COMMITTED_SIZE=xv2.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_SIZE=xv2.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_FRAGMENTATION_SIZE=xv2.METRIC_DOTNET_GC_HEAP_TOTAL_ALLOCATED=xv2.METRIC_DOTNET_GC_COLLECTIONS=xv2.METRIC_DOTNET_EXCEPTIONS=xv2.METRIC_DOTNET_ASSEMBLY_COUNT=xv2.METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS=xv2.METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS=xv2.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION=xv2.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE=xv2.METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS=xv2.METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES=xv2.METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS=void 0;xv2.METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS="aspnetcore.diagnostics.exceptions";xv2.METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES="aspnetcore.rate_limiting.active_request_leases";xv2.METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS="aspnetcore.rate_limiting.queued_requests";xv2.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE="aspnetcore.rate_limiting.request.time_in_queue";xv2.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION="aspnetcore.rate_limiting.request_lease.duration";xv2.METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS="aspnetcore.rate_limiting.requests";xv2.METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS="aspnetcore.routing.match_attempts";xv2.METRIC_DOTNET_ASSEMBLY_COUNT="dotnet.assembly.count";xv2.METRIC_DOTNET_EXCEPTIONS="dotnet.exceptions";xv2.METRIC_DOTNET_GC_COLLECTIONS="dotnet.gc.collections";xv2.METRIC_DOTNET_GC_HEAP_TOTAL_ALLOCATED="dotnet.gc.heap.total_allocated";xv2.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_FRAGMENTATION_SIZE="dotnet.gc.last_collection.heap.fragmentation.size";xv2.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_SIZE="dotnet.gc.last_collection.heap.size";xv2.METRIC_DOTNET_GC_LAST_COLLECTION_MEMORY_COMMITTED_SIZE="dotnet.gc.last_collection.memory.committed_size";xv2.METRIC_DOTNET_GC_PAUSE_TIME="dotnet.gc.pause.time";xv2.METRIC_DOTNET_JIT_COMPILATION_TIME="dotnet.jit.compilation.time";xv2.METRIC_DOTNET_JIT_COMPILED_IL_SIZE="dotnet.jit.compiled_il.size";xv2.METRIC_DOTNET_JIT_COMPILED_METHODS="dotnet.jit.compiled_methods";xv2.METRIC_DOTNET_MONITOR_LOCK_CONTENTIONS="dotnet.monitor.lock_contentions";xv2.METRIC_DOTNET_PROCESS_CPU_COUNT="dotnet.process.cpu.count";xv2.METRIC_DOTNET_PROCESS_CPU_TIME="dotnet.process.cpu.time";xv2.METRIC_DOTNET_PROCESS_MEMORY_WORKING_SET="dotnet.process.memory.working_set";xv2.METRIC_DOTNET_THREAD_POOL_QUEUE_LENGTH="dotnet.thread_pool.queue.length";xv2.METRIC_DOTNET_THREAD_POOL_THREAD_COUNT="dotnet.thread_pool.thread.count";xv2.METRIC_DOTNET_THREAD_POOL_WORK_ITEM_COUNT="dotnet.thread_pool.work_item.count";xv2.METRIC_DOTNET_TIMER_COUNT="dotnet.timer.count";xv2.METRIC_HTTP_CLIENT_REQUEST_DURATION="http.client.request.duration";xv2.METRIC_HTTP_SERVER_REQUEST_DURATION="http.server.request.duration";xv2.METRIC_JVM_CLASS_COUNT="jvm.class.count";xv2.METRIC_JVM_CLASS_LOADED="jvm.class.loaded";xv2.METRIC_JVM_CLASS_UNLOADED="jvm.class.unloaded";xv2.METRIC_JVM_CPU_COUNT="jvm.cpu.count";xv2.METRIC_JVM_CPU_RECENT_UTILIZATION="jvm.cpu.recent_utilization";xv2.METRIC_JVM_CPU_TIME="jvm.cpu.time";xv2.METRIC_JVM_GC_DURATION="jvm.gc.duration";xv2.METRIC_JVM_MEMORY_COMMITTED="jvm.memory.committed";xv2.METRIC_JVM_MEMORY_LIMIT="jvm.memory.limit";xv2.METRIC_JVM_MEMORY_USED="jvm.memory.used";xv2.METRIC_JVM_MEMORY_USED_AFTER_LAST_GC="jvm.memory.used_after_last_gc";xv2.METRIC_JVM_THREAD_COUNT="jvm.thread.count";xv2.METRIC_KESTREL_ACTIVE_CONNECTIONS="kestrel.active_connections";xv2.METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES="kestrel.active_tls_handshakes";xv2.METRIC_KESTREL_CONNECTION_DURATION="kestrel.connection.duration";xv2.METRIC_KESTREL_QUEUED_CONNECTIONS="kestrel.queued_connections";xv2.METRIC_KESTREL_QUEUED_REQUESTS="kestrel.queued_requests";xv2.METRIC_KESTREL_REJECTED_CONNECTIONS="kestrel.rejected_connections";xv2.METRIC_KESTREL_TLS_HANDSHAKE_DURATION="kestrel.tls_handshake.duration";xv2.METRIC_KESTREL_UPGRADED_CONNECTIONS="kestrel.upgraded_connections";xv2.METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS="signalr.server.active_connections";xv2.METRIC_SIGNALR_SERVER_CONNECTION_DURATION="signalr.server.connection.duration"});
var c51=E((JT2)=>{Object.defineProperty(JT2,"__esModule",{value:!0});JT2.isTracingSuppressed=JT2.unsuppressTracing=JT2.suppressTracing=void 0;var us4=ZQ(),UF0=us4.createContextKey("OpenTelemetry SDK Context Key SUPPRESS_TRACING");function ms4(A){return A.setValue(UF0,!0)}JT2.suppressTracing=ms4;function ds4(A){return A.deleteValue(UF0)}JT2.unsuppressTracing=ds4;function cs4(A){return A.getValue(UF0)===!0}JT2.isTracingSuppressed=cs4});
var cT2=E((mT2)=>{Object.defineProperty(mT2,"__esModule",{value:!0});mT2.getStringListFromEnv=mT2.getBooleanFromEnv=mT2.getStringFromEnv=mT2.getNumberFromEnv=void 0;var hT2=ZQ(),gT2=J1("util");function Ur4(A){let B=process.env[A];if(B==null||B.trim()==="")return;let Q=Number(B);if(isNaN(Q)){hT2.diag.warn(`Unknown value ${gT2.inspect(B)} for ${A}, expected a number, using defaults`);return}return Q}mT2.getNumberFromEnv=Ur4;function uT2(A){let B=process.env[A];if(B==null||B.trim()==="")return;return B}mT2.getStringFromEnv=uT2;function wr4(A){let B=process.env[A]?.trim().toLowerCase();if(B==null||B==="")return!1;if(B==="true")return!0;else if(B==="false")return!1;else return hT2.diag.warn(`Unknown value ${gT2.inspect(B)} for ${A}, expected 'true' or 'false', falling back to 'false' (default)`),!1}mT2.getBooleanFromEnv=wr4;function $r4(A){return uT2(A)?.split(",").map((B)=>B.trim()).filter((B)=>B!=="")}mT2.getStringListFromEnv=$r4});
var cv2=E((dL)=>{Object.defineProperty(dL,"__esModule",{value:!0});dL.unrefTimer=dL.SDK_INFO=dL.otperformance=dL._globalThis=dL.getStringListFromEnv=dL.getNumberFromEnv=dL.getBooleanFromEnv=dL.getStringFromEnv=void 0;var LO1=cT2();Object.defineProperty(dL,"getStringFromEnv",{enumerable:!0,get:function(){return LO1.getStringFromEnv}});Object.defineProperty(dL,"getBooleanFromEnv",{enumerable:!0,get:function(){return LO1.getBooleanFromEnv}});Object.defineProperty(dL,"getNumberFromEnv",{enumerable:!0,get:function(){return LO1.getNumberFromEnv}});Object.defineProperty(dL,"getStringListFromEnv",{enumerable:!0,get:function(){return LO1.getStringListFromEnv}});var F46=iT2();Object.defineProperty(dL,"_globalThis",{enumerable:!0,get:function(){return F46._globalThis}});var I46=sT2();Object.defineProperty(dL,"otperformance",{enumerable:!0,get:function(){return I46.otperformance}});var Y46=gv2();Object.defineProperty(dL,"SDK_INFO",{enumerable:!0,get:function(){return Y46.SDK_INFO}});var W46=dv2();Object.defineProperty(dL,"unrefTimer",{enumerable:!0,get:function(){return W46.unrefTimer}})});
var dv2=E((uv2)=>{Object.defineProperty(uv2,"__esModule",{value:!0});uv2.unrefTimer=void 0;function G46(A){A.unref()}uv2.unrefTimer=G46});
var eM2=E((oM2)=>{Object.defineProperty(oM2,"__esModule",{value:!0});oM2.context=void 0;var mn4=g51();oM2.context=mn4.ContextAPI.getInstance()});
var eN2=E((Iu)=>{var Zi4=Iu&&Iu.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;Object.defineProperty(A,D,{enumerable:!0,get:function(){return B[Q]}})}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),Gi4=Iu&&Iu.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))Zi4(B,A,Q)};Object.defineProperty(Iu,"__esModule",{value:!0});Gi4(tN2(),Iu)});
var eb2=E((ob2)=>{Object.defineProperty(ob2,"__esModule",{value:!0});ob2.diagLogLevelFromString=void 0;var FP=ZQ(),rb2={ALL:FP.DiagLogLevel.ALL,VERBOSE:FP.DiagLogLevel.VERBOSE,DEBUG:FP.DiagLogLevel.DEBUG,INFO:FP.DiagLogLevel.INFO,WARN:FP.DiagLogLevel.WARN,ERROR:FP.DiagLogLevel.ERROR,NONE:FP.DiagLogLevel.NONE};function L66(A){if(A==null)return;let B=rb2[A.toUpperCase()];if(B==null)return FP.diag.warn(`Unknown log level "${A}", expected one of ${Object.keys(rb2)}, using default`),FP.DiagLogLevel.INFO;return B}ob2.diagLogLevelFromString=L66});
var f51=E((SL2)=>{Object.defineProperty(SL2,"__esModule",{value:!0});SL2.ROOT_CONTEXT=SL2.createContextKey=void 0;function ki4(A){return Symbol.for(A)}SL2.createContextKey=ki4;class JO1{constructor(A){let B=this;B._currentContext=A?new Map(A):new Map,B.getValue=(Q)=>B._currentContext.get(Q),B.setValue=(Q,D)=>{let Z=new JO1(B._currentContext);return Z._currentContext.set(Q,D),Z},B.deleteValue=(Q)=>{let D=new JO1(B._currentContext);return D._currentContext.delete(Q),D}}}SL2.ROOT_CONTEXT=new JO1});
var fM2=E((bM2)=>{Object.defineProperty(bM2,"__esModule",{value:!0});bM2.SpanStatusCode=void 0;var Sn4;(function(A){A[A.UNSET=0]="UNSET",A[A.OK=1]="OK",A[A.ERROR=2]="ERROR"})(Sn4=bM2.SpanStatusCode||(bM2.SpanStatusCode={}))});
var fR2=E((vR2)=>{Object.defineProperty(vR2,"__esModule",{value:!0});vR2.trace=void 0;var Ga4=xR2();vR2.trace=Ga4.TraceAPI.getInstance()});
var fT2=E((vT2)=>{Object.defineProperty(vT2,"__esModule",{value:!0});vT2.globalErrorHandler=vT2.setGlobalErrorHandler=void 0;var Kr4=LF0(),xT2=Kr4.loggingErrorHandler();function Hr4(A){xT2=A}vT2.setGlobalErrorHandler=Hr4;function zr4(A){try{xT2(A)}catch{}}vT2.globalErrorHandler=zr4});
var fb2=E((vb2)=>{Object.defineProperty(vb2,"__esModule",{value:!0});vb2.callWithTimeout=vb2.TimeoutError=void 0;class jO1 extends Error{constructor(A){super(A);Object.setPrototypeOf(this,jO1.prototype)}}vb2.TimeoutError=jO1;function U66(A,B){let Q,D=new Promise(function Z(G,F){Q=setTimeout(function I(){F(new jO1("Operation timed out."))},B)});return Promise.race([A,D]).then((Z)=>{return clearTimeout(Q),Z},(Z)=>{throw clearTimeout(Q),Z})}vb2.callWithTimeout=U66});
var g51=E((AM2)=>{Object.defineProperty(AM2,"__esModule",{value:!0});AM2.ContextAPI=void 0;var ni4=tL2(),MG0=Wu(),eL2=Ju(),RG0="context",ai4=new ni4.NoopContextManager;class OG0{constructor(){}static getInstance(){if(!this._instance)this._instance=new OG0;return this._instance}setGlobalContextManager(A){return MG0.registerGlobal(RG0,A,eL2.DiagAPI.instance())}active(){return this._getContextManager().active()}with(A,B,Q,...D){return this._getContextManager().with(A,B,Q,...D)}bind(A,B){return this._getContextManager().bind(A,B)}_getContextManager(){return MG0.getGlobal(RG0)||ai4}disable(){this._getContextManager().disable(),MG0.unregisterGlobal(RG0,eL2.DiagAPI.instance())}}AM2.ContextAPI=OG0});
var gv2=E((fv2)=>{Object.defineProperty(fv2,"__esModule",{value:!0});fv2.SDK_INFO=void 0;var Z46=tT2(),l51=GP();fv2.SDK_INFO={[l51.SEMRESATTRS_TELEMETRY_SDK_NAME]:"opentelemetry",[l51.SEMRESATTRS_PROCESS_RUNTIME_NAME]:"node",[l51.SEMRESATTRS_TELEMETRY_SDK_LANGUAGE]:l51.TELEMETRYSDKLANGUAGEVALUES_NODEJS,[l51.SEMRESATTRS_TELEMETRY_SDK_VERSION]:Z46.VERSION}});
var iL2=E((pL2)=>{Object.defineProperty(pL2,"__esModule",{value:!0});pL2.ValueType=void 0;var li4;(function(A){A[A.INT=0]="INT",A[A.DOUBLE=1]="DOUBLE"})(li4=pL2.ValueType||(pL2.ValueType={}))});
var iT2=E((lT2)=>{Object.defineProperty(lT2,"__esModule",{value:!0});lT2._globalThis=void 0;lT2._globalThis=typeof globalThis==="object"?globalThis:global});
var kG0=E((XM2)=>{Object.defineProperty(XM2,"__esModule",{value:!0});XM2.getSpanContext=XM2.setSpanContext=XM2.deleteSpan=XM2.setSpan=XM2.getActiveSpan=XM2.getSpan=void 0;var ti4=f51(),ei4=VO1(),An4=g51(),SG0=ti4.createContextKey("OpenTelemetry Context Key SPAN");function jG0(A){return A.getValue(SG0)||void 0}XM2.getSpan=jG0;function Bn4(){return jG0(An4.ContextAPI.getInstance().active())}XM2.getActiveSpan=Bn4;function JM2(A,B){return A.setValue(SG0,B)}XM2.setSpan=JM2;function Qn4(A){return A.deleteValue(SG0)}XM2.deleteSpan=Qn4;function Dn4(A,B){return JM2(A,new ei4.NonRecordingSpan(B))}XM2.setSpanContext=Dn4;function Zn4(A){var B;return(B=jG0(A))===null||B===void 0?void 0:B.spanContext()}XM2.getSpanContext=Zn4});
var kT2=E((ST2)=>{Object.defineProperty(ST2,"__esModule",{value:!0});ST2.isAttributeValue=ST2.isAttributeKey=ST2.sanitizeAttributes=void 0;var RT2=ZQ();function Fr4(A){let B={};if(typeof A!=="object"||A==null)return B;for(let[Q,D]of Object.entries(A)){if(!OT2(Q)){RT2.diag.warn(`Invalid attribute key: ${Q}`);continue}if(!TT2(D)){RT2.diag.warn(`Invalid attribute value set for key: ${Q}`);continue}if(Array.isArray(D))B[Q]=D.slice();else B[Q]=D}return B}ST2.sanitizeAttributes=Fr4;function OT2(A){return typeof A==="string"&&A.length>0}ST2.isAttributeKey=OT2;function TT2(A){if(A==null)return!0;if(Array.isArray(A))return Ir4(A);return PT2(A)}ST2.isAttributeValue=TT2;function Ir4(A){let B;for(let Q of A){if(Q==null)continue;if(!B){if(PT2(Q)){B=typeof Q;continue}return!1}if(typeof Q===B)continue;return!1}return!0}function PT2(A){switch(typeof A){case"number":case"boolean":case"string":return!0}return!1}});
var mb2=E((gb2)=>{Object.defineProperty(gb2,"__esModule",{value:!0});gb2.isUrlIgnored=gb2.urlMatches=void 0;function hb2(A,B){if(typeof B==="string")return A===B;else return!!A.match(B)}gb2.urlMatches=hb2;function $66(A,B){if(!B)return!1;for(let Q of B)if(hb2(A,Q))return!0;return!1}gb2.isUrlIgnored=$66});
var nM2=E((pM2)=>{Object.defineProperty(pM2,"__esModule",{value:!0});pM2.TraceStateImpl=void 0;var mM2=uM2(),dM2=32,hn4=512,cM2=",",lM2="=";class mG0{constructor(A){if(this._internalState=new Map,A)this._parse(A)}set(A,B){let Q=this._clone();if(Q._internalState.has(A))Q._internalState.delete(A);return Q._internalState.set(A,B),Q}unset(A){let B=this._clone();return B._internalState.delete(A),B}get(A){return this._internalState.get(A)}serialize(){return this._keys().reduce((A,B)=>{return A.push(B+lM2+this.get(B)),A},[]).join(cM2)}_parse(A){if(A.length>hn4)return;if(this._internalState=A.split(cM2).reverse().reduce((B,Q)=>{let D=Q.trim(),Z=D.indexOf(lM2);if(Z!==-1){let G=D.slice(0,Z),F=D.slice(Z+1,Q.length);if(mM2.validateKey(G)&&mM2.validateValue(F))B.set(G,F)}return B},new Map),this._internalState.size>dM2)this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,dM2))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let A=new mG0;return A._internalState=new Map(this._internalState),A}}pM2.TraceStateImpl=mG0});
var ov2=E((rv2)=>{Object.defineProperty(rv2,"__esModule",{value:!0});rv2.ExportResultCode=void 0;var y46;(function(A){A[A.SUCCESS=0]="SUCCESS",A[A.FAILED=1]="FAILED"})(y46=rv2.ExportResultCode||(rv2.ExportResultCode={}))});
var oy2=E((ly2)=>{Object.defineProperty(ly2,"__esModule",{value:!0});ly2.SEMATTRS_NET_HOST_CARRIER_ICC=ly2.SEMATTRS_NET_HOST_CARRIER_MNC=ly2.SEMATTRS_NET_HOST_CARRIER_MCC=ly2.SEMATTRS_NET_HOST_CARRIER_NAME=ly2.SEMATTRS_NET_HOST_CONNECTION_SUBTYPE=ly2.SEMATTRS_NET_HOST_CONNECTION_TYPE=ly2.SEMATTRS_NET_HOST_NAME=ly2.SEMATTRS_NET_HOST_PORT=ly2.SEMATTRS_NET_HOST_IP=ly2.SEMATTRS_NET_PEER_NAME=ly2.SEMATTRS_NET_PEER_PORT=ly2.SEMATTRS_NET_PEER_IP=ly2.SEMATTRS_NET_TRANSPORT=ly2.SEMATTRS_FAAS_INVOKED_REGION=ly2.SEMATTRS_FAAS_INVOKED_PROVIDER=ly2.SEMATTRS_FAAS_INVOKED_NAME=ly2.SEMATTRS_FAAS_COLDSTART=ly2.SEMATTRS_FAAS_CRON=ly2.SEMATTRS_FAAS_TIME=ly2.SEMATTRS_FAAS_DOCUMENT_NAME=ly2.SEMATTRS_FAAS_DOCUMENT_TIME=ly2.SEMATTRS_FAAS_DOCUMENT_OPERATION=ly2.SEMATTRS_FAAS_DOCUMENT_COLLECTION=ly2.SEMATTRS_FAAS_EXECUTION=ly2.SEMATTRS_FAAS_TRIGGER=ly2.SEMATTRS_EXCEPTION_ESCAPED=ly2.SEMATTRS_EXCEPTION_STACKTRACE=ly2.SEMATTRS_EXCEPTION_MESSAGE=ly2.SEMATTRS_EXCEPTION_TYPE=ly2.SEMATTRS_DB_SQL_TABLE=ly2.SEMATTRS_DB_MONGODB_COLLECTION=ly2.SEMATTRS_DB_REDIS_DATABASE_INDEX=ly2.SEMATTRS_DB_HBASE_NAMESPACE=ly2.SEMATTRS_DB_CASSANDRA_COORDINATOR_DC=ly2.SEMATTRS_DB_CASSANDRA_COORDINATOR_ID=ly2.SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT=ly2.SEMATTRS_DB_CASSANDRA_IDEMPOTENCE=ly2.SEMATTRS_DB_CASSANDRA_TABLE=ly2.SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL=ly2.SEMATTRS_DB_CASSANDRA_PAGE_SIZE=ly2.SEMATTRS_DB_CASSANDRA_KEYSPACE=ly2.SEMATTRS_DB_MSSQL_INSTANCE_NAME=ly2.SEMATTRS_DB_OPERATION=ly2.SEMATTRS_DB_STATEMENT=ly2.SEMATTRS_DB_NAME=ly2.SEMATTRS_DB_JDBC_DRIVER_CLASSNAME=ly2.SEMATTRS_DB_USER=ly2.SEMATTRS_DB_CONNECTION_STRING=ly2.SEMATTRS_DB_SYSTEM=ly2.SEMATTRS_AWS_LAMBDA_INVOKED_ARN=void 0;ly2.SEMATTRS_MESSAGING_DESTINATION_KIND=ly2.SEMATTRS_MESSAGING_DESTINATION=ly2.SEMATTRS_MESSAGING_SYSTEM=ly2.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES=ly2.SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS=ly2.SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT=ly2.SEMATTRS_AWS_DYNAMODB_COUNT=ly2.SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS=ly2.SEMATTRS_AWS_DYNAMODB_SEGMENT=ly2.SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD=ly2.SEMATTRS_AWS_DYNAMODB_TABLE_COUNT=ly2.SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE=ly2.SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES=ly2.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES=ly2.SEMATTRS_AWS_DYNAMODB_SELECT=ly2.SEMATTRS_AWS_DYNAMODB_INDEX_NAME=ly2.SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET=ly2.SEMATTRS_AWS_DYNAMODB_LIMIT=ly2.SEMATTRS_AWS_DYNAMODB_PROJECTION=ly2.SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ=ly2.SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY=ly2.SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY=ly2.SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS=ly2.SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY=ly2.SEMATTRS_AWS_DYNAMODB_TABLE_NAMES=ly2.SEMATTRS_HTTP_CLIENT_IP=ly2.SEMATTRS_HTTP_ROUTE=ly2.SEMATTRS_HTTP_SERVER_NAME=ly2.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED=ly2.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH=ly2.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED=ly2.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH=ly2.SEMATTRS_HTTP_USER_AGENT=ly2.SEMATTRS_HTTP_FLAVOR=ly2.SEMATTRS_HTTP_STATUS_CODE=ly2.SEMATTRS_HTTP_SCHEME=ly2.SEMATTRS_HTTP_HOST=ly2.SEMATTRS_HTTP_TARGET=ly2.SEMATTRS_HTTP_URL=ly2.SEMATTRS_HTTP_METHOD=ly2.SEMATTRS_CODE_LINENO=ly2.SEMATTRS_CODE_FILEPATH=ly2.SEMATTRS_CODE_NAMESPACE=ly2.SEMATTRS_CODE_FUNCTION=ly2.SEMATTRS_THREAD_NAME=ly2.SEMATTRS_THREAD_ID=ly2.SEMATTRS_ENDUSER_SCOPE=ly2.SEMATTRS_ENDUSER_ROLE=ly2.SEMATTRS_ENDUSER_ID=ly2.SEMATTRS_PEER_SERVICE=void 0;ly2.DBSYSTEMVALUES_FILEMAKER=ly2.DBSYSTEMVALUES_DERBY=ly2.DBSYSTEMVALUES_FIREBIRD=ly2.DBSYSTEMVALUES_ADABAS=ly2.DBSYSTEMVALUES_CACHE=ly2.DBSYSTEMVALUES_EDB=ly2.DBSYSTEMVALUES_FIRSTSQL=ly2.DBSYSTEMVALUES_INGRES=ly2.DBSYSTEMVALUES_HANADB=ly2.DBSYSTEMVALUES_MAXDB=ly2.DBSYSTEMVALUES_PROGRESS=ly2.DBSYSTEMVALUES_HSQLDB=ly2.DBSYSTEMVALUES_CLOUDSCAPE=ly2.DBSYSTEMVALUES_HIVE=ly2.DBSYSTEMVALUES_REDSHIFT=ly2.DBSYSTEMVALUES_POSTGRESQL=ly2.DBSYSTEMVALUES_DB2=ly2.DBSYSTEMVALUES_ORACLE=ly2.DBSYSTEMVALUES_MYSQL=ly2.DBSYSTEMVALUES_MSSQL=ly2.DBSYSTEMVALUES_OTHER_SQL=ly2.SemanticAttributes=ly2.SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE=ly2.SEMATTRS_MESSAGE_COMPRESSED_SIZE=ly2.SEMATTRS_MESSAGE_ID=ly2.SEMATTRS_MESSAGE_TYPE=ly2.SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE=ly2.SEMATTRS_RPC_JSONRPC_ERROR_CODE=ly2.SEMATTRS_RPC_JSONRPC_REQUEST_ID=ly2.SEMATTRS_RPC_JSONRPC_VERSION=ly2.SEMATTRS_RPC_GRPC_STATUS_CODE=ly2.SEMATTRS_RPC_METHOD=ly2.SEMATTRS_RPC_SERVICE=ly2.SEMATTRS_RPC_SYSTEM=ly2.SEMATTRS_MESSAGING_KAFKA_TOMBSTONE=ly2.SEMATTRS_MESSAGING_KAFKA_PARTITION=ly2.SEMATTRS_MESSAGING_KAFKA_CLIENT_ID=ly2.SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP=ly2.SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY=ly2.SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY=ly2.SEMATTRS_MESSAGING_CONSUMER_ID=ly2.SEMATTRS_MESSAGING_OPERATION=ly2.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES=ly2.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES=ly2.SEMATTRS_MESSAGING_CONVERSATION_ID=ly2.SEMATTRS_MESSAGING_MESSAGE_ID=ly2.SEMATTRS_MESSAGING_URL=ly2.SEMATTRS_MESSAGING_PROTOCOL_VERSION=ly2.SEMATTRS_MESSAGING_PROTOCOL=ly2.SEMATTRS_MESSAGING_TEMP_DESTINATION=void 0;ly2.FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD=ly2.FaasDocumentOperationValues=ly2.FAASDOCUMENTOPERATIONVALUES_DELETE=ly2.FAASDOCUMENTOPERATIONVALUES_EDIT=ly2.FAASDOCUMENTOPERATIONVALUES_INSERT=ly2.FaasTriggerValues=ly2.FAASTRIGGERVALUES_OTHER=ly2.FAASTRIGGERVALUES_TIMER=ly2.FAASTRIGGERVALUES_PUBSUB=ly2.FAASTRIGGERVALUES_HTTP=ly2.FAASTRIGGERVALUES_DATASOURCE=ly2.DbCassandraConsistencyLevelValues=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_ANY=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_THREE=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_TWO=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_ONE=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_ALL=ly2.DbSystemValues=ly2.DBSYSTEMVALUES_COCKROACHDB=ly2.DBSYSTEMVALUES_MEMCACHED=ly2.DBSYSTEMVALUES_ELASTICSEARCH=ly2.DBSYSTEMVALUES_GEODE=ly2.DBSYSTEMVALUES_NEO4J=ly2.DBSYSTEMVALUES_DYNAMODB=ly2.DBSYSTEMVALUES_COSMOSDB=ly2.DBSYSTEMVALUES_COUCHDB=ly2.DBSYSTEMVALUES_COUCHBASE=ly2.DBSYSTEMVALUES_REDIS=ly2.DBSYSTEMVALUES_MONGODB=ly2.DBSYSTEMVALUES_HBASE=ly2.DBSYSTEMVALUES_CASSANDRA=ly2.DBSYSTEMVALUES_COLDFUSION=ly2.DBSYSTEMVALUES_H2=ly2.DBSYSTEMVALUES_VERTICA=ly2.DBSYSTEMVALUES_TERADATA=ly2.DBSYSTEMVALUES_SYBASE=ly2.DBSYSTEMVALUES_SQLITE=ly2.DBSYSTEMVALUES_POINTBASE=ly2.DBSYSTEMVALUES_PERVASIVE=ly2.DBSYSTEMVALUES_NETEZZA=ly2.DBSYSTEMVALUES_MARIADB=ly2.DBSYSTEMVALUES_INTERBASE=ly2.DBSYSTEMVALUES_INSTANTDB=ly2.DBSYSTEMVALUES_INFORMIX=void 0;ly2.MESSAGINGOPERATIONVALUES_RECEIVE=ly2.MessagingDestinationKindValues=ly2.MESSAGINGDESTINATIONKINDVALUES_TOPIC=ly2.MESSAGINGDESTINATIONKINDVALUES_QUEUE=ly2.HttpFlavorValues=ly2.HTTPFLAVORVALUES_QUIC=ly2.HTTPFLAVORVALUES_SPDY=ly2.HTTPFLAVORVALUES_HTTP_2_0=ly2.HTTPFLAVORVALUES_HTTP_1_1=ly2.HTTPFLAVORVALUES_HTTP_1_0=ly2.NetHostConnectionSubtypeValues=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_NR=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_GSM=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_LTE=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_IDEN=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSPA=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_UMTS=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EDGE=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_GPRS=ly2.NetHostConnectionTypeValues=ly2.NETHOSTCONNECTIONTYPEVALUES_UNKNOWN=ly2.NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE=ly2.NETHOSTCONNECTIONTYPEVALUES_CELL=ly2.NETHOSTCONNECTIONTYPEVALUES_WIRED=ly2.NETHOSTCONNECTIONTYPEVALUES_WIFI=ly2.NetTransportValues=ly2.NETTRANSPORTVALUES_OTHER=ly2.NETTRANSPORTVALUES_INPROC=ly2.NETTRANSPORTVALUES_PIPE=ly2.NETTRANSPORTVALUES_UNIX=ly2.NETTRANSPORTVALUES_IP=ly2.NETTRANSPORTVALUES_IP_UDP=ly2.NETTRANSPORTVALUES_IP_TCP=ly2.FaasInvokedProviderValues=ly2.FAASINVOKEDPROVIDERVALUES_GCP=ly2.FAASINVOKEDPROVIDERVALUES_AZURE=ly2.FAASINVOKEDPROVIDERVALUES_AWS=void 0;ly2.MessageTypeValues=ly2.MESSAGETYPEVALUES_RECEIVED=ly2.MESSAGETYPEVALUES_SENT=ly2.RpcGrpcStatusCodeValues=ly2.RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED=ly2.RPCGRPCSTATUSCODEVALUES_DATA_LOSS=ly2.RPCGRPCSTATUSCODEVALUES_UNAVAILABLE=ly2.RPCGRPCSTATUSCODEVALUES_INTERNAL=ly2.RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED=ly2.RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE=ly2.RPCGRPCSTATUSCODEVALUES_ABORTED=ly2.RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION=ly2.RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED=ly2.RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED=ly2.RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS=ly2.RPCGRPCSTATUSCODEVALUES_NOT_FOUND=ly2.RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED=ly2.RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT=ly2.RPCGRPCSTATUSCODEVALUES_UNKNOWN=ly2.RPCGRPCSTATUSCODEVALUES_CANCELLED=ly2.RPCGRPCSTATUSCODEVALUES_OK=ly2.MessagingOperationValues=ly2.MESSAGINGOPERATIONVALUES_PROCESS=void 0;var IE=MF0(),BP2="aws.lambda.invoked_arn",QP2="db.system",DP2="db.connection_string",ZP2="db.user",GP2="db.jdbc.driver_classname",FP2="db.name",IP2="db.statement",YP2="db.operation",WP2="db.mssql.instance_name",JP2="db.cassandra.keyspace",XP2="db.cassandra.page_size",VP2="db.cassandra.consistency_level",CP2="db.cassandra.table",KP2="db.cassandra.idempotence",HP2="db.cassandra.speculative_execution_count",zP2="db.cassandra.coordinator.id",EP2="db.cassandra.coordinator.dc",UP2="db.hbase.namespace",wP2="db.redis.database_index",$P2="db.mongodb.collection",qP2="db.sql.table",NP2="exception.type",LP2="exception.message",MP2="exception.stacktrace",RP2="exception.escaped",OP2="faas.trigger",TP2="faas.execution",PP2="faas.document.collection",SP2="faas.document.operation",jP2="faas.document.time",kP2="faas.document.name",yP2="faas.time",_P2="faas.cron",xP2="faas.coldstart",vP2="faas.invoked_name",bP2="faas.invoked_provider",fP2="faas.invoked_region",hP2="net.transport",gP2="net.peer.ip",uP2="net.peer.port",mP2="net.peer.name",dP2="net.host.ip",cP2="net.host.port",lP2="net.host.name",pP2="net.host.connection.type",iP2="net.host.connection.subtype",nP2="net.host.carrier.name",aP2="net.host.carrier.mcc",sP2="net.host.carrier.mnc",rP2="net.host.carrier.icc",oP2="peer.service",tP2="enduser.id",eP2="enduser.role",AS2="enduser.scope",BS2="thread.id",QS2="thread.name",DS2="code.function",ZS2="code.namespace",GS2="code.filepath",FS2="code.lineno",IS2="http.method",YS2="http.url",WS2="http.target",JS2="http.host",XS2="http.scheme",VS2="http.status_code",CS2="http.flavor",KS2="http.user_agent",HS2="http.request_content_length",zS2="http.request_content_length_uncompressed",ES2="http.response_content_length",US2="http.response_content_length_uncompressed",wS2="http.server_name",$S2="http.route",qS2="http.client_ip",NS2="aws.dynamodb.table_names",LS2="aws.dynamodb.consumed_capacity",MS2="aws.dynamodb.item_collection_metrics",RS2="aws.dynamodb.provisioned_read_capacity",OS2="aws.dynamodb.provisioned_write_capacity",TS2="aws.dynamodb.consistent_read",PS2="aws.dynamodb.projection",SS2="aws.dynamodb.limit",jS2="aws.dynamodb.attributes_to_get",kS2="aws.dynamodb.index_name",yS2="aws.dynamodb.select",_S2="aws.dynamodb.global_secondary_indexes",xS2="aws.dynamodb.local_secondary_indexes",vS2="aws.dynamodb.exclusive_start_table",bS2="aws.dynamodb.table_count",fS2="aws.dynamodb.scan_forward",hS2="aws.dynamodb.segment",gS2="aws.dynamodb.total_segments",uS2="aws.dynamodb.count",mS2="aws.dynamodb.scanned_count",dS2="aws.dynamodb.attribute_definitions",cS2="aws.dynamodb.global_secondary_index_updates",lS2="messaging.system",pS2="messaging.destination",iS2="messaging.destination_kind",nS2="messaging.temp_destination",aS2="messaging.protocol",sS2="messaging.protocol_version",rS2="messaging.url",oS2="messaging.message_id",tS2="messaging.conversation_id",eS2="messaging.message_payload_size_bytes",Aj2="messaging.message_payload_compressed_size_bytes",Bj2="messaging.operation",Qj2="messaging.consumer_id",Dj2="messaging.rabbitmq.routing_key",Zj2="messaging.kafka.message_key",Gj2="messaging.kafka.consumer_group",Fj2="messaging.kafka.client_id",Ij2="messaging.kafka.partition",Yj2="messaging.kafka.tombstone",Wj2="rpc.system",Jj2="rpc.service",Xj2="rpc.method",Vj2="rpc.grpc.status_code",Cj2="rpc.jsonrpc.version",Kj2="rpc.jsonrpc.request_id",Hj2="rpc.jsonrpc.error_code",zj2="rpc.jsonrpc.error_message",Ej2="message.type",Uj2="message.id",wj2="message.compressed_size",$j2="message.uncompressed_size";ly2.SEMATTRS_AWS_LAMBDA_INVOKED_ARN=BP2;ly2.SEMATTRS_DB_SYSTEM=QP2;ly2.SEMATTRS_DB_CONNECTION_STRING=DP2;ly2.SEMATTRS_DB_USER=ZP2;ly2.SEMATTRS_DB_JDBC_DRIVER_CLASSNAME=GP2;ly2.SEMATTRS_DB_NAME=FP2;ly2.SEMATTRS_DB_STATEMENT=IP2;ly2.SEMATTRS_DB_OPERATION=YP2;ly2.SEMATTRS_DB_MSSQL_INSTANCE_NAME=WP2;ly2.SEMATTRS_DB_CASSANDRA_KEYSPACE=JP2;ly2.SEMATTRS_DB_CASSANDRA_PAGE_SIZE=XP2;ly2.SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL=VP2;ly2.SEMATTRS_DB_CASSANDRA_TABLE=CP2;ly2.SEMATTRS_DB_CASSANDRA_IDEMPOTENCE=KP2;ly2.SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT=HP2;ly2.SEMATTRS_DB_CASSANDRA_COORDINATOR_ID=zP2;ly2.SEMATTRS_DB_CASSANDRA_COORDINATOR_DC=EP2;ly2.SEMATTRS_DB_HBASE_NAMESPACE=UP2;ly2.SEMATTRS_DB_REDIS_DATABASE_INDEX=wP2;ly2.SEMATTRS_DB_MONGODB_COLLECTION=$P2;ly2.SEMATTRS_DB_SQL_TABLE=qP2;ly2.SEMATTRS_EXCEPTION_TYPE=NP2;ly2.SEMATTRS_EXCEPTION_MESSAGE=LP2;ly2.SEMATTRS_EXCEPTION_STACKTRACE=MP2;ly2.SEMATTRS_EXCEPTION_ESCAPED=RP2;ly2.SEMATTRS_FAAS_TRIGGER=OP2;ly2.SEMATTRS_FAAS_EXECUTION=TP2;ly2.SEMATTRS_FAAS_DOCUMENT_COLLECTION=PP2;ly2.SEMATTRS_FAAS_DOCUMENT_OPERATION=SP2;ly2.SEMATTRS_FAAS_DOCUMENT_TIME=jP2;ly2.SEMATTRS_FAAS_DOCUMENT_NAME=kP2;ly2.SEMATTRS_FAAS_TIME=yP2;ly2.SEMATTRS_FAAS_CRON=_P2;ly2.SEMATTRS_FAAS_COLDSTART=xP2;ly2.SEMATTRS_FAAS_INVOKED_NAME=vP2;ly2.SEMATTRS_FAAS_INVOKED_PROVIDER=bP2;ly2.SEMATTRS_FAAS_INVOKED_REGION=fP2;ly2.SEMATTRS_NET_TRANSPORT=hP2;ly2.SEMATTRS_NET_PEER_IP=gP2;ly2.SEMATTRS_NET_PEER_PORT=uP2;ly2.SEMATTRS_NET_PEER_NAME=mP2;ly2.SEMATTRS_NET_HOST_IP=dP2;ly2.SEMATTRS_NET_HOST_PORT=cP2;ly2.SEMATTRS_NET_HOST_NAME=lP2;ly2.SEMATTRS_NET_HOST_CONNECTION_TYPE=pP2;ly2.SEMATTRS_NET_HOST_CONNECTION_SUBTYPE=iP2;ly2.SEMATTRS_NET_HOST_CARRIER_NAME=nP2;ly2.SEMATTRS_NET_HOST_CARRIER_MCC=aP2;ly2.SEMATTRS_NET_HOST_CARRIER_MNC=sP2;ly2.SEMATTRS_NET_HOST_CARRIER_ICC=rP2;ly2.SEMATTRS_PEER_SERVICE=oP2;ly2.SEMATTRS_ENDUSER_ID=tP2;ly2.SEMATTRS_ENDUSER_ROLE=eP2;ly2.SEMATTRS_ENDUSER_SCOPE=AS2;ly2.SEMATTRS_THREAD_ID=BS2;ly2.SEMATTRS_THREAD_NAME=QS2;ly2.SEMATTRS_CODE_FUNCTION=DS2;ly2.SEMATTRS_CODE_NAMESPACE=ZS2;ly2.SEMATTRS_CODE_FILEPATH=GS2;ly2.SEMATTRS_CODE_LINENO=FS2;ly2.SEMATTRS_HTTP_METHOD=IS2;ly2.SEMATTRS_HTTP_URL=YS2;ly2.SEMATTRS_HTTP_TARGET=WS2;ly2.SEMATTRS_HTTP_HOST=JS2;ly2.SEMATTRS_HTTP_SCHEME=XS2;ly2.SEMATTRS_HTTP_STATUS_CODE=VS2;ly2.SEMATTRS_HTTP_FLAVOR=CS2;ly2.SEMATTRS_HTTP_USER_AGENT=KS2;ly2.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH=HS2;ly2.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED=zS2;ly2.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH=ES2;ly2.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED=US2;ly2.SEMATTRS_HTTP_SERVER_NAME=wS2;ly2.SEMATTRS_HTTP_ROUTE=$S2;ly2.SEMATTRS_HTTP_CLIENT_IP=qS2;ly2.SEMATTRS_AWS_DYNAMODB_TABLE_NAMES=NS2;ly2.SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY=LS2;ly2.SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS=MS2;ly2.SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY=RS2;ly2.SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY=OS2;ly2.SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ=TS2;ly2.SEMATTRS_AWS_DYNAMODB_PROJECTION=PS2;ly2.SEMATTRS_AWS_DYNAMODB_LIMIT=SS2;ly2.SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET=jS2;ly2.SEMATTRS_AWS_DYNAMODB_INDEX_NAME=kS2;ly2.SEMATTRS_AWS_DYNAMODB_SELECT=yS2;ly2.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES=_S2;ly2.SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES=xS2;ly2.SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE=vS2;ly2.SEMATTRS_AWS_DYNAMODB_TABLE_COUNT=bS2;ly2.SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD=fS2;ly2.SEMATTRS_AWS_DYNAMODB_SEGMENT=hS2;ly2.SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS=gS2;ly2.SEMATTRS_AWS_DYNAMODB_COUNT=uS2;ly2.SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT=mS2;ly2.SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS=dS2;ly2.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES=cS2;ly2.SEMATTRS_MESSAGING_SYSTEM=lS2;ly2.SEMATTRS_MESSAGING_DESTINATION=pS2;ly2.SEMATTRS_MESSAGING_DESTINATION_KIND=iS2;ly2.SEMATTRS_MESSAGING_TEMP_DESTINATION=nS2;ly2.SEMATTRS_MESSAGING_PROTOCOL=aS2;ly2.SEMATTRS_MESSAGING_PROTOCOL_VERSION=sS2;ly2.SEMATTRS_MESSAGING_URL=rS2;ly2.SEMATTRS_MESSAGING_MESSAGE_ID=oS2;ly2.SEMATTRS_MESSAGING_CONVERSATION_ID=tS2;ly2.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES=eS2;ly2.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES=Aj2;ly2.SEMATTRS_MESSAGING_OPERATION=Bj2;ly2.SEMATTRS_MESSAGING_CONSUMER_ID=Qj2;ly2.SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY=Dj2;ly2.SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY=Zj2;ly2.SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP=Gj2;ly2.SEMATTRS_MESSAGING_KAFKA_CLIENT_ID=Fj2;ly2.SEMATTRS_MESSAGING_KAFKA_PARTITION=Ij2;ly2.SEMATTRS_MESSAGING_KAFKA_TOMBSTONE=Yj2;ly2.SEMATTRS_RPC_SYSTEM=Wj2;ly2.SEMATTRS_RPC_SERVICE=Jj2;ly2.SEMATTRS_RPC_METHOD=Xj2;ly2.SEMATTRS_RPC_GRPC_STATUS_CODE=Vj2;ly2.SEMATTRS_RPC_JSONRPC_VERSION=Cj2;ly2.SEMATTRS_RPC_JSONRPC_REQUEST_ID=Kj2;ly2.SEMATTRS_RPC_JSONRPC_ERROR_CODE=Hj2;ly2.SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE=zj2;ly2.SEMATTRS_MESSAGE_TYPE=Ej2;ly2.SEMATTRS_MESSAGE_ID=Uj2;ly2.SEMATTRS_MESSAGE_COMPRESSED_SIZE=wj2;ly2.SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE=$j2;ly2.SemanticAttributes=IE.createConstMap([BP2,QP2,DP2,ZP2,GP2,FP2,IP2,YP2,WP2,JP2,XP2,VP2,CP2,KP2,HP2,zP2,EP2,UP2,wP2,$P2,qP2,NP2,LP2,MP2,RP2,OP2,TP2,PP2,SP2,jP2,kP2,yP2,_P2,xP2,vP2,bP2,fP2,hP2,gP2,uP2,mP2,dP2,cP2,lP2,pP2,iP2,nP2,aP2,sP2,rP2,oP2,tP2,eP2,AS2,BS2,QS2,DS2,ZS2,GS2,FS2,IS2,YS2,WS2,JS2,XS2,VS2,CS2,KS2,HS2,zS2,ES2,US2,wS2,$S2,qS2,NS2,LS2,MS2,RS2,OS2,TS2,PS2,SS2,jS2,kS2,yS2,_S2,xS2,vS2,bS2,fS2,hS2,gS2,uS2,mS2,dS2,cS2,lS2,pS2,iS2,nS2,aS2,sS2,rS2,oS2,tS2,eS2,Aj2,Bj2,Qj2,Dj2,Zj2,Gj2,Fj2,Ij2,Yj2,Wj2,Jj2,Xj2,Vj2,Cj2,Kj2,Hj2,zj2,Ej2,Uj2,wj2,$j2]);var qj2="other_sql",Nj2="mssql",Lj2="mysql",Mj2="oracle",Rj2="db2",Oj2="postgresql",Tj2="redshift",Pj2="hive",Sj2="cloudscape",jj2="hsqldb",kj2="progress",yj2="maxdb",_j2="hanadb",xj2="ingres",vj2="firstsql",bj2="edb",fj2="cache",hj2="adabas",gj2="firebird",uj2="derby",mj2="filemaker",dj2="informix",cj2="instantdb",lj2="interbase",pj2="mariadb",ij2="netezza",nj2="pervasive",aj2="pointbase",sj2="sqlite",rj2="sybase",oj2="teradata",tj2="vertica",ej2="h2",Ak2="coldfusion",Bk2="cassandra",Qk2="hbase",Dk2="mongodb",Zk2="redis",Gk2="couchbase",Fk2="couchdb",Ik2="cosmosdb",Yk2="dynamodb",Wk2="neo4j",Jk2="geode",Xk2="elasticsearch",Vk2="memcached",Ck2="cockroachdb";ly2.DBSYSTEMVALUES_OTHER_SQL=qj2;ly2.DBSYSTEMVALUES_MSSQL=Nj2;ly2.DBSYSTEMVALUES_MYSQL=Lj2;ly2.DBSYSTEMVALUES_ORACLE=Mj2;ly2.DBSYSTEMVALUES_DB2=Rj2;ly2.DBSYSTEMVALUES_POSTGRESQL=Oj2;ly2.DBSYSTEMVALUES_REDSHIFT=Tj2;ly2.DBSYSTEMVALUES_HIVE=Pj2;ly2.DBSYSTEMVALUES_CLOUDSCAPE=Sj2;ly2.DBSYSTEMVALUES_HSQLDB=jj2;ly2.DBSYSTEMVALUES_PROGRESS=kj2;ly2.DBSYSTEMVALUES_MAXDB=yj2;ly2.DBSYSTEMVALUES_HANADB=_j2;ly2.DBSYSTEMVALUES_INGRES=xj2;ly2.DBSYSTEMVALUES_FIRSTSQL=vj2;ly2.DBSYSTEMVALUES_EDB=bj2;ly2.DBSYSTEMVALUES_CACHE=fj2;ly2.DBSYSTEMVALUES_ADABAS=hj2;ly2.DBSYSTEMVALUES_FIREBIRD=gj2;ly2.DBSYSTEMVALUES_DERBY=uj2;ly2.DBSYSTEMVALUES_FILEMAKER=mj2;ly2.DBSYSTEMVALUES_INFORMIX=dj2;ly2.DBSYSTEMVALUES_INSTANTDB=cj2;ly2.DBSYSTEMVALUES_INTERBASE=lj2;ly2.DBSYSTEMVALUES_MARIADB=pj2;ly2.DBSYSTEMVALUES_NETEZZA=ij2;ly2.DBSYSTEMVALUES_PERVASIVE=nj2;ly2.DBSYSTEMVALUES_POINTBASE=aj2;ly2.DBSYSTEMVALUES_SQLITE=sj2;ly2.DBSYSTEMVALUES_SYBASE=rj2;ly2.DBSYSTEMVALUES_TERADATA=oj2;ly2.DBSYSTEMVALUES_VERTICA=tj2;ly2.DBSYSTEMVALUES_H2=ej2;ly2.DBSYSTEMVALUES_COLDFUSION=Ak2;ly2.DBSYSTEMVALUES_CASSANDRA=Bk2;ly2.DBSYSTEMVALUES_HBASE=Qk2;ly2.DBSYSTEMVALUES_MONGODB=Dk2;ly2.DBSYSTEMVALUES_REDIS=Zk2;ly2.DBSYSTEMVALUES_COUCHBASE=Gk2;ly2.DBSYSTEMVALUES_COUCHDB=Fk2;ly2.DBSYSTEMVALUES_COSMOSDB=Ik2;ly2.DBSYSTEMVALUES_DYNAMODB=Yk2;ly2.DBSYSTEMVALUES_NEO4J=Wk2;ly2.DBSYSTEMVALUES_GEODE=Jk2;ly2.DBSYSTEMVALUES_ELASTICSEARCH=Xk2;ly2.DBSYSTEMVALUES_MEMCACHED=Vk2;ly2.DBSYSTEMVALUES_COCKROACHDB=Ck2;ly2.DbSystemValues=IE.createConstMap([qj2,Nj2,Lj2,Mj2,Rj2,Oj2,Tj2,Pj2,Sj2,jj2,kj2,yj2,_j2,xj2,vj2,bj2,fj2,hj2,gj2,uj2,mj2,dj2,cj2,lj2,pj2,ij2,nj2,aj2,sj2,rj2,oj2,tj2,ej2,Ak2,Bk2,Qk2,Dk2,Zk2,Gk2,Fk2,Ik2,Yk2,Wk2,Jk2,Xk2,Vk2,Ck2]);var Kk2="all",Hk2="each_quorum",zk2="quorum",Ek2="local_quorum",Uk2="one",wk2="two",$k2="three",qk2="local_one",Nk2="any",Lk2="serial",Mk2="local_serial";ly2.DBCASSANDRACONSISTENCYLEVELVALUES_ALL=Kk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM=Hk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM=zk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM=Ek2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_ONE=Uk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_TWO=wk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_THREE=$k2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE=qk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_ANY=Nk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL=Lk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL=Mk2;ly2.DbCassandraConsistencyLevelValues=IE.createConstMap([Kk2,Hk2,zk2,Ek2,Uk2,wk2,$k2,qk2,Nk2,Lk2,Mk2]);var Rk2="datasource",Ok2="http",Tk2="pubsub",Pk2="timer",Sk2="other";ly2.FAASTRIGGERVALUES_DATASOURCE=Rk2;ly2.FAASTRIGGERVALUES_HTTP=Ok2;ly2.FAASTRIGGERVALUES_PUBSUB=Tk2;ly2.FAASTRIGGERVALUES_TIMER=Pk2;ly2.FAASTRIGGERVALUES_OTHER=Sk2;ly2.FaasTriggerValues=IE.createConstMap([Rk2,Ok2,Tk2,Pk2,Sk2]);var jk2="insert",kk2="edit",yk2="delete";ly2.FAASDOCUMENTOPERATIONVALUES_INSERT=jk2;ly2.FAASDOCUMENTOPERATIONVALUES_EDIT=kk2;ly2.FAASDOCUMENTOPERATIONVALUES_DELETE=yk2;ly2.FaasDocumentOperationValues=IE.createConstMap([jk2,kk2,yk2]);var _k2="alibaba_cloud",xk2="aws",vk2="azure",bk2="gcp";ly2.FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD=_k2;ly2.FAASINVOKEDPROVIDERVALUES_AWS=xk2;ly2.FAASINVOKEDPROVIDERVALUES_AZURE=vk2;ly2.FAASINVOKEDPROVIDERVALUES_GCP=bk2;ly2.FaasInvokedProviderValues=IE.createConstMap([_k2,xk2,vk2,bk2]);var fk2="ip_tcp",hk2="ip_udp",gk2="ip",uk2="unix",mk2="pipe",dk2="inproc",ck2="other";ly2.NETTRANSPORTVALUES_IP_TCP=fk2;ly2.NETTRANSPORTVALUES_IP_UDP=hk2;ly2.NETTRANSPORTVALUES_IP=gk2;ly2.NETTRANSPORTVALUES_UNIX=uk2;ly2.NETTRANSPORTVALUES_PIPE=mk2;ly2.NETTRANSPORTVALUES_INPROC=dk2;ly2.NETTRANSPORTVALUES_OTHER=ck2;ly2.NetTransportValues=IE.createConstMap([fk2,hk2,gk2,uk2,mk2,dk2,ck2]);var lk2="wifi",pk2="wired",ik2="cell",nk2="unavailable",ak2="unknown";ly2.NETHOSTCONNECTIONTYPEVALUES_WIFI=lk2;ly2.NETHOSTCONNECTIONTYPEVALUES_WIRED=pk2;ly2.NETHOSTCONNECTIONTYPEVALUES_CELL=ik2;ly2.NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE=nk2;ly2.NETHOSTCONNECTIONTYPEVALUES_UNKNOWN=ak2;ly2.NetHostConnectionTypeValues=IE.createConstMap([lk2,pk2,ik2,nk2,ak2]);var sk2="gprs",rk2="edge",ok2="umts",tk2="cdma",ek2="evdo_0",Ay2="evdo_a",By2="cdma2000_1xrtt",Qy2="hsdpa",Dy2="hsupa",Zy2="hspa",Gy2="iden",Fy2="evdo_b",Iy2="lte",Yy2="ehrpd",Wy2="hspap",Jy2="gsm",Xy2="td_scdma",Vy2="iwlan",Cy2="nr",Ky2="nrnsa",Hy2="lte_ca";ly2.NETHOSTCONNECTIONSUBTYPEVALUES_GPRS=sk2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EDGE=rk2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_UMTS=ok2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA=tk2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0=ek2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A=Ay2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT=By2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA=Qy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA=Dy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSPA=Zy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_IDEN=Gy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B=Fy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_LTE=Iy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD=Yy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP=Wy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_GSM=Jy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA=Xy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN=Vy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_NR=Cy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA=Ky2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA=Hy2;ly2.NetHostConnectionSubtypeValues=IE.createConstMap([sk2,rk2,ok2,tk2,ek2,Ay2,By2,Qy2,Dy2,Zy2,Gy2,Fy2,Iy2,Yy2,Wy2,Jy2,Xy2,Vy2,Cy2,Ky2,Hy2]);var zy2="1.0",Ey2="1.1",Uy2="2.0",wy2="SPDY",$y2="QUIC";ly2.HTTPFLAVORVALUES_HTTP_1_0=zy2;ly2.HTTPFLAVORVALUES_HTTP_1_1=Ey2;ly2.HTTPFLAVORVALUES_HTTP_2_0=Uy2;ly2.HTTPFLAVORVALUES_SPDY=wy2;ly2.HTTPFLAVORVALUES_QUIC=$y2;ly2.HttpFlavorValues={HTTP_1_0:zy2,HTTP_1_1:Ey2,HTTP_2_0:Uy2,SPDY:wy2,QUIC:$y2};var qy2="queue",Ny2="topic";ly2.MESSAGINGDESTINATIONKINDVALUES_QUEUE=qy2;ly2.MESSAGINGDESTINATIONKINDVALUES_TOPIC=Ny2;ly2.MessagingDestinationKindValues=IE.createConstMap([qy2,Ny2]);var Ly2="receive",My2="process";ly2.MESSAGINGOPERATIONVALUES_RECEIVE=Ly2;ly2.MESSAGINGOPERATIONVALUES_PROCESS=My2;ly2.MessagingOperationValues=IE.createConstMap([Ly2,My2]);var Ry2=0,Oy2=1,Ty2=2,Py2=3,Sy2=4,jy2=5,ky2=6,yy2=7,_y2=8,xy2=9,vy2=10,by2=11,fy2=12,hy2=13,gy2=14,uy2=15,my2=16;ly2.RPCGRPCSTATUSCODEVALUES_OK=Ry2;ly2.RPCGRPCSTATUSCODEVALUES_CANCELLED=Oy2;ly2.RPCGRPCSTATUSCODEVALUES_UNKNOWN=Ty2;ly2.RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT=Py2;ly2.RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED=Sy2;ly2.RPCGRPCSTATUSCODEVALUES_NOT_FOUND=jy2;ly2.RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS=ky2;ly2.RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED=yy2;ly2.RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED=_y2;ly2.RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION=xy2;ly2.RPCGRPCSTATUSCODEVALUES_ABORTED=vy2;ly2.RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE=by2;ly2.RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED=fy2;ly2.RPCGRPCSTATUSCODEVALUES_INTERNAL=hy2;ly2.RPCGRPCSTATUSCODEVALUES_UNAVAILABLE=gy2;ly2.RPCGRPCSTATUSCODEVALUES_DATA_LOSS=uy2;ly2.RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED=my2;ly2.RpcGrpcStatusCodeValues={OK:Ry2,CANCELLED:Oy2,UNKNOWN:Ty2,INVALID_ARGUMENT:Py2,DEADLINE_EXCEEDED:Sy2,NOT_FOUND:jy2,ALREADY_EXISTS:ky2,PERMISSION_DENIED:yy2,RESOURCE_EXHAUSTED:_y2,FAILED_PRECONDITION:xy2,ABORTED:vy2,OUT_OF_RANGE:by2,UNIMPLEMENTED:fy2,INTERNAL:hy2,UNAVAILABLE:gy2,DATA_LOSS:uy2,UNAUTHENTICATED:my2};var dy2="SENT",cy2="RECEIVED";ly2.MESSAGETYPEVALUES_SENT=dy2;ly2.MESSAGETYPEVALUES_RECEIVED=cy2;ly2.MessageTypeValues=IE.createConstMap([dy2,cy2])});
var pb2=E((cb2)=>{Object.defineProperty(cb2,"__esModule",{value:!0});cb2.Deferred=void 0;class db2{_promise;_resolve;_reject;constructor(){this._promise=new Promise((A,B)=>{this._resolve=A,this._reject=B})}get promise(){return this._promise}resolve(A){this._resolve(A)}reject(A){this._reject(A)}}cb2.Deferred=db2});
var qG0=E((vL2)=>{Object.defineProperty(vL2,"__esModule",{value:!0});vL2.createNoopMeter=vL2.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=vL2.NOOP_OBSERVABLE_GAUGE_METRIC=vL2.NOOP_OBSERVABLE_COUNTER_METRIC=vL2.NOOP_UP_DOWN_COUNTER_METRIC=vL2.NOOP_HISTOGRAM_METRIC=vL2.NOOP_GAUGE_METRIC=vL2.NOOP_COUNTER_METRIC=vL2.NOOP_METER=vL2.NoopObservableUpDownCounterMetric=vL2.NoopObservableGaugeMetric=vL2.NoopObservableCounterMetric=vL2.NoopObservableMetric=vL2.NoopHistogramMetric=vL2.NoopGaugeMetric=vL2.NoopUpDownCounterMetric=vL2.NoopCounterMetric=vL2.NoopMetric=vL2.NoopMeter=void 0;class CG0{constructor(){}createGauge(A,B){return vL2.NOOP_GAUGE_METRIC}createHistogram(A,B){return vL2.NOOP_HISTOGRAM_METRIC}createCounter(A,B){return vL2.NOOP_COUNTER_METRIC}createUpDownCounter(A,B){return vL2.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(A,B){return vL2.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(A,B){return vL2.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(A,B){return vL2.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(A,B){}removeBatchObservableCallback(A){}}vL2.NoopMeter=CG0;class Ro{}vL2.NoopMetric=Ro;class KG0 extends Ro{add(A,B){}}vL2.NoopCounterMetric=KG0;class HG0 extends Ro{add(A,B){}}vL2.NoopUpDownCounterMetric=HG0;class zG0 extends Ro{record(A,B){}}vL2.NoopGaugeMetric=zG0;class EG0 extends Ro{record(A,B){}}vL2.NoopHistogramMetric=EG0;class h51{addCallback(A){}removeCallback(A){}}vL2.NoopObservableMetric=h51;class UG0 extends h51{}vL2.NoopObservableCounterMetric=UG0;class wG0 extends h51{}vL2.NoopObservableGaugeMetric=wG0;class $G0 extends h51{}vL2.NoopObservableUpDownCounterMetric=$G0;vL2.NOOP_METER=new CG0;vL2.NOOP_COUNTER_METRIC=new KG0;vL2.NOOP_GAUGE_METRIC=new zG0;vL2.NOOP_HISTOGRAM_METRIC=new EG0;vL2.NOOP_UP_DOWN_COUNTER_METRIC=new HG0;vL2.NOOP_OBSERVABLE_COUNTER_METRIC=new UG0;vL2.NOOP_OBSERVABLE_GAUGE_METRIC=new wG0;vL2.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new $G0;function _i4(){return vL2.NOOP_METER}vL2.createNoopMeter=_i4});
var rM2=E((aM2)=>{Object.defineProperty(aM2,"__esModule",{value:!0});aM2.createTraceState=void 0;var gn4=nM2();function un4(A){return new gn4.TraceStateImpl(A)}aM2.createTraceState=un4});
var sT2=E((nT2)=>{Object.defineProperty(nT2,"__esModule",{value:!0});nT2.otperformance=void 0;var Mr4=J1("perf_hooks");nT2.otperformance=Mr4.performance});
var sb2=E((nb2)=>{Object.defineProperty(nb2,"__esModule",{value:!0});nb2.BindOnceFuture=void 0;var N66=pb2();class ib2{_callback;_that;_isCalled=!1;_deferred=new N66.Deferred;constructor(A,B){this._callback=A,this._that=B}get isCalled(){return this._isCalled}get promise(){return this._deferred.promise}call(...A){if(!this._isCalled){this._isCalled=!0;try{Promise.resolve(this._callback.call(this._that,...A)).then((B)=>this._deferred.resolve(B),(B)=>this._deferred.reject(B))}catch(B){this._deferred.reject(B)}}return this._deferred.promise}}nb2.BindOnceFuture=ib2});
var sv2=E((nv2)=>{Object.defineProperty(nv2,"__esModule",{value:!0});nv2.addHrTimes=nv2.isTimeInput=nv2.isTimeInputHrTime=nv2.hrTimeToMicroseconds=nv2.hrTimeToMilliseconds=nv2.hrTimeToNanoseconds=nv2.hrTimeToTimeStamp=nv2.hrTimeDuration=nv2.timeInputToHrTime=nv2.hrTime=nv2.getTimeOrigin=nv2.millisToHrTime=void 0;var OF0=RF0(),lv2=9,V46=6,C46=Math.pow(10,V46),MO1=Math.pow(10,lv2);function p51(A){let B=A/1000,Q=Math.trunc(B),D=Math.round(A%1000*C46);return[Q,D]}nv2.millisToHrTime=p51;function TF0(){let A=OF0.otperformance.timeOrigin;if(typeof A!=="number"){let B=OF0.otperformance;A=B.timing&&B.timing.fetchStart}return A}nv2.getTimeOrigin=TF0;function pv2(A){let B=p51(TF0()),Q=p51(typeof A==="number"?A:OF0.otperformance.now());return iv2(B,Q)}nv2.hrTime=pv2;function K46(A){if(PF0(A))return A;else if(typeof A==="number")if(A<TF0())return pv2(A);else return p51(A);else if(A instanceof Date)return p51(A.getTime());else throw TypeError("Invalid input type")}nv2.timeInputToHrTime=K46;function H46(A,B){let Q=B[0]-A[0],D=B[1]-A[1];if(D<0)Q-=1,D+=MO1;return[Q,D]}nv2.hrTimeDuration=H46;function z46(A){let B=lv2,Q=`${"0".repeat(B)}${A[1]}Z`,D=Q.substring(Q.length-B-1);return new Date(A[0]*1000).toISOString().replace("000Z",D)}nv2.hrTimeToTimeStamp=z46;function E46(A){return A[0]*MO1+A[1]}nv2.hrTimeToNanoseconds=E46;function U46(A){return A[0]*1000+A[1]/1e6}nv2.hrTimeToMilliseconds=U46;function w46(A){return A[0]*1e6+A[1]/1000}nv2.hrTimeToMicroseconds=w46;function PF0(A){return Array.isArray(A)&&A.length===2&&typeof A[0]==="number"&&typeof A[1]==="number"}nv2.isTimeInputHrTime=PF0;function $46(A){return PF0(A)||typeof A==="number"||A instanceof Date}nv2.isTimeInput=$46;function iv2(A,B){let Q=[A[0]+B[0],A[1]+B[1]];if(Q[1]>=MO1)Q[1]-=MO1,Q[0]+=1;return Q}nv2.addHrTimes=iv2});
var tL2=E((rL2)=>{Object.defineProperty(rL2,"__esModule",{value:!0});rL2.NoopContextManager=void 0;var ii4=f51();class sL2{active(){return ii4.ROOT_CONTEXT}with(A,B,Q,...D){return B.call(Q,...D)}bind(A,B){return B}enable(){return this}disable(){return this}}rL2.NoopContextManager=sL2});
var tN2=E((rN2)=>{Object.defineProperty(rN2,"__esModule",{value:!0});rN2._globalThis=void 0;rN2._globalThis=typeof globalThis==="object"?globalThis:global});
var tT2=E((rT2)=>{Object.defineProperty(rT2,"__esModule",{value:!0});rT2.VERSION=void 0;rT2.VERSION="2.0.0"});
var ty2=E((Cu)=>{var L06=Cu&&Cu.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),M06=Cu&&Cu.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))L06(B,A,Q)};Object.defineProperty(Cu,"__esModule",{value:!0});M06(oy2(),Cu)});
var uM2=E((hM2)=>{Object.defineProperty(hM2,"__esModule",{value:!0});hM2.validateValue=hM2.validateKey=void 0;var uG0="[_0-9a-z-*/]",jn4=`[a-z]${uG0}{0,255}`,kn4=`[a-z0-9]${uG0}{0,240}@[a-z]${uG0}{0,13}`,yn4=new RegExp(`^(?:${jn4}|${kn4})$`),_n4=/^[ -~]{0,255}[!-~]$/,xn4=/,|=/;function vn4(A){return yn4.test(A)}hM2.validateKey=vn4;function bn4(A){return _n4.test(A)&&!xn4.test(A)}hM2.validateValue=bn4});
var vG0=E((LM2)=>{Object.defineProperty(LM2,"__esModule",{value:!0});LM2.ProxyTracer=void 0;var qn4=xG0(),Nn4=new qn4.NoopTracer;class NM2{constructor(A,B,Q,D){this._provider=A,this.name=B,this.version=Q,this.options=D}startSpan(A,B,Q){return this._getTracer().startSpan(A,B,Q)}startActiveSpan(A,B,Q,D){let Z=this._getTracer();return Reflect.apply(Z.startActiveSpan,Z,arguments)}_getTracer(){if(this._delegate)return this._delegate;let A=this._provider.getDelegateTracer(this.name,this.version,this.options);if(!A)return Nn4;return this._delegate=A,this._delegate}}LM2.ProxyTracer=NM2});
var vM2=E((xM2)=>{Object.defineProperty(xM2,"__esModule",{value:!0});xM2.SpanKind=void 0;var Pn4;(function(A){A[A.INTERNAL=0]="INTERNAL",A[A.SERVER=1]="SERVER",A[A.CLIENT=2]="CLIENT",A[A.PRODUCER=3]="PRODUCER",A[A.CONSUMER=4]="CONSUMER"})(Pn4=xM2.SpanKind||(xM2.SpanKind={}))});
var wF0=E((VT2)=>{Object.defineProperty(VT2,"__esModule",{value:!0});VT2.BAGGAGE_MAX_TOTAL_LENGTH=VT2.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS=VT2.BAGGAGE_MAX_NAME_VALUE_PAIRS=VT2.BAGGAGE_HEADER=VT2.BAGGAGE_ITEMS_SEPARATOR=VT2.BAGGAGE_PROPERTIES_SEPARATOR=VT2.BAGGAGE_KEY_PAIR_SEPARATOR=void 0;VT2.BAGGAGE_KEY_PAIR_SEPARATOR="=";VT2.BAGGAGE_PROPERTIES_SEPARATOR=";";VT2.BAGGAGE_ITEMS_SEPARATOR=",";VT2.BAGGAGE_HEADER="baggage";VT2.BAGGAGE_MAX_NAME_VALUE_PAIRS=180;VT2.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS=4096;VT2.BAGGAGE_MAX_TOTAL_LENGTH=8192});
var xG0=E(($M2)=>{Object.defineProperty($M2,"__esModule",{value:!0});$M2.NoopTracer=void 0;var Un4=g51(),UM2=kG0(),yG0=VO1(),wn4=CO1(),_G0=Un4.ContextAPI.getInstance();class wM2{startSpan(A,B,Q=_G0.active()){if(Boolean(B===null||B===void 0?void 0:B.root))return new yG0.NonRecordingSpan;let Z=Q&&UM2.getSpanContext(Q);if($n4(Z)&&wn4.isSpanContextValid(Z))return new yG0.NonRecordingSpan(Z);else return new yG0.NonRecordingSpan}startActiveSpan(A,B,Q,D){let Z,G,F;if(arguments.length<2)return;else if(arguments.length===2)F=B;else if(arguments.length===3)Z=B,F=Q;else Z=B,G=Q,F=D;let I=G!==null&&G!==void 0?G:_G0.active(),Y=this.startSpan(A,Z,I),W=UM2.setSpan(I,Y);return _G0.with(W,F,void 0,Y)}}$M2.NoopTracer=wM2;function $n4(A){return typeof A==="object"&&typeof A.spanId==="string"&&typeof A.traceId==="string"&&typeof A.traceFlags==="number"}});
var xL2=E((yL2)=>{Object.defineProperty(yL2,"__esModule",{value:!0});yL2.DiagConsoleLogger=void 0;var VG0=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class kL2{constructor(){function A(B){return function(...Q){if(console){let D=console[B];if(typeof D!=="function")D=console.log;if(typeof D==="function")return D.apply(console,Q)}}}for(let B=0;B<VG0.length;B++)this[VG0[B].n]=A(VG0[B].c)}}yL2.DiagConsoleLogger=kL2});
var xR2=E((yR2)=>{Object.defineProperty(yR2,"__esModule",{value:!0});yR2.TraceAPI=void 0;var rG0=Wu(),SR2=bG0(),jR2=CO1(),Oo=kG0(),kR2=Ju(),oG0="trace";class tG0{constructor(){this._proxyTracerProvider=new SR2.ProxyTracerProvider,this.wrapSpanContext=jR2.wrapSpanContext,this.isSpanContextValid=jR2.isSpanContextValid,this.deleteSpan=Oo.deleteSpan,this.getSpan=Oo.getSpan,this.getActiveSpan=Oo.getActiveSpan,this.getSpanContext=Oo.getSpanContext,this.setSpan=Oo.setSpan,this.setSpanContext=Oo.setSpanContext}static getInstance(){if(!this._instance)this._instance=new tG0;return this._instance}setGlobalTracerProvider(A){let B=rG0.registerGlobal(oG0,this._proxyTracerProvider,kR2.DiagAPI.instance());if(B)this._proxyTracerProvider.setDelegate(A);return B}getTracerProvider(){return rG0.getGlobal(oG0)||this._proxyTracerProvider}getTracer(A,B){return this.getTracerProvider().getTracer(A,B)}disable(){rG0.unregisterGlobal(oG0,kR2.DiagAPI.instance()),this._proxyTracerProvider=new SR2.ProxyTracerProvider}}yR2.TraceAPI=tG0});
var xb2=E((yb2)=>{Object.defineProperty(yb2,"__esModule",{value:!0});yb2.merge=void 0;var Pb2=Tb2(),H66=20;function z66(...A){let B=A.shift(),Q=new WeakMap;while(A.length>0)B=jb2(B,A.shift(),0,Q);return B}yb2.merge=z66;function xF0(A){if(SO1(A))return A.slice();return A}function jb2(A,B,Q=0,D){let Z;if(Q>H66)return;if(Q++,PO1(A)||PO1(B)||kb2(B))Z=xF0(B);else if(SO1(A)){if(Z=A.slice(),SO1(B))for(let G=0,F=B.length;G<F;G++)Z.push(xF0(B[G]));else if(i51(B)){let G=Object.keys(B);for(let F=0,I=G.length;F<I;F++){let Y=G[F];Z[Y]=xF0(B[Y])}}}else if(i51(A))if(i51(B)){if(!E66(A,B))return B;Z=Object.assign({},A);let G=Object.keys(B);for(let F=0,I=G.length;F<I;F++){let Y=G[F],W=B[Y];if(PO1(W))if(typeof W==="undefined")delete Z[Y];else Z[Y]=W;else{let J=Z[Y],X=W;if(Sb2(A,Y,D)||Sb2(B,Y,D))delete Z[Y];else{if(i51(J)&&i51(X)){let V=D.get(J)||[],C=D.get(X)||[];V.push({obj:A,key:Y}),C.push({obj:B,key:Y}),D.set(J,V),D.set(X,C)}Z[Y]=jb2(Z[Y],W,Q,D)}}}}else Z=B;return Z}function Sb2(A,B,Q){let D=Q.get(A[B])||[];for(let Z=0,G=D.length;Z<G;Z++){let F=D[Z];if(F.key===B&&F.obj===A)return!0}return!1}function SO1(A){return Array.isArray(A)}function kb2(A){return typeof A==="function"}function i51(A){return!PO1(A)&&!SO1(A)&&!kb2(A)&&typeof A==="object"}function PO1(A){return typeof A==="string"||typeof A==="number"||typeof A==="boolean"||typeof A==="undefined"||A instanceof Date||A instanceof RegExp||A===null}function E66(A,B){if(!Pb2.isPlainObject(A)||!Pb2.isPlainObject(B))return!1;return!0}});
var y3=E((r9)=>{Object.defineProperty(r9,"__esModule",{value:!0});r9.internal=r9.diagLogLevelFromString=r9.BindOnceFuture=r9.urlMatches=r9.isUrlIgnored=r9.callWithTimeout=r9.TimeoutError=r9.merge=r9.TraceState=r9.unsuppressTracing=r9.suppressTracing=r9.isTracingSuppressed=r9.setRPCMetadata=r9.getRPCMetadata=r9.deleteRPCMetadata=r9.RPCType=r9.parseTraceParent=r9.W3CTraceContextPropagator=r9.TRACE_STATE_HEADER=r9.TRACE_PARENT_HEADER=r9.CompositePropagator=r9.unrefTimer=r9.otperformance=r9.getStringListFromEnv=r9.getNumberFromEnv=r9.getBooleanFromEnv=r9.getStringFromEnv=r9._globalThis=r9.SDK_INFO=r9.parseKeyPairsIntoRecord=r9.ExportResultCode=r9.timeInputToHrTime=r9.millisToHrTime=r9.isTimeInputHrTime=r9.isTimeInput=r9.hrTimeToTimeStamp=r9.hrTimeToNanoseconds=r9.hrTimeToMilliseconds=r9.hrTimeToMicroseconds=r9.hrTimeDuration=r9.hrTime=r9.getTimeOrigin=r9.addHrTimes=r9.loggingErrorHandler=r9.setGlobalErrorHandler=r9.globalErrorHandler=r9.sanitizeAttributes=r9.isAttributeValue=r9.AnchoredClock=r9.W3CBaggagePropagator=void 0;var O66=$T2();Object.defineProperty(r9,"W3CBaggagePropagator",{enumerable:!0,get:function(){return O66.W3CBaggagePropagator}});var T66=MT2();Object.defineProperty(r9,"AnchoredClock",{enumerable:!0,get:function(){return T66.AnchoredClock}});var Zf2=kT2();Object.defineProperty(r9,"isAttributeValue",{enumerable:!0,get:function(){return Zf2.isAttributeValue}});Object.defineProperty(r9,"sanitizeAttributes",{enumerable:!0,get:function(){return Zf2.sanitizeAttributes}});var Gf2=fT2();Object.defineProperty(r9,"globalErrorHandler",{enumerable:!0,get:function(){return Gf2.globalErrorHandler}});Object.defineProperty(r9,"setGlobalErrorHandler",{enumerable:!0,get:function(){return Gf2.setGlobalErrorHandler}});var P66=LF0();Object.defineProperty(r9,"loggingErrorHandler",{enumerable:!0,get:function(){return P66.loggingErrorHandler}});var YE=sv2();Object.defineProperty(r9,"addHrTimes",{enumerable:!0,get:function(){return YE.addHrTimes}});Object.defineProperty(r9,"getTimeOrigin",{enumerable:!0,get:function(){return YE.getTimeOrigin}});Object.defineProperty(r9,"hrTime",{enumerable:!0,get:function(){return YE.hrTime}});Object.defineProperty(r9,"hrTimeDuration",{enumerable:!0,get:function(){return YE.hrTimeDuration}});Object.defineProperty(r9,"hrTimeToMicroseconds",{enumerable:!0,get:function(){return YE.hrTimeToMicroseconds}});Object.defineProperty(r9,"hrTimeToMilliseconds",{enumerable:!0,get:function(){return YE.hrTimeToMilliseconds}});Object.defineProperty(r9,"hrTimeToNanoseconds",{enumerable:!0,get:function(){return YE.hrTimeToNanoseconds}});Object.defineProperty(r9,"hrTimeToTimeStamp",{enumerable:!0,get:function(){return YE.hrTimeToTimeStamp}});Object.defineProperty(r9,"isTimeInput",{enumerable:!0,get:function(){return YE.isTimeInput}});Object.defineProperty(r9,"isTimeInputHrTime",{enumerable:!0,get:function(){return YE.isTimeInputHrTime}});Object.defineProperty(r9,"millisToHrTime",{enumerable:!0,get:function(){return YE.millisToHrTime}});Object.defineProperty(r9,"timeInputToHrTime",{enumerable:!0,get:function(){return YE.timeInputToHrTime}});var S66=ov2();Object.defineProperty(r9,"ExportResultCode",{enumerable:!0,get:function(){return S66.ExportResultCode}});var j66=$F0();Object.defineProperty(r9,"parseKeyPairsIntoRecord",{enumerable:!0,get:function(){return j66.parseKeyPairsIntoRecord}});var v_=RF0();Object.defineProperty(r9,"SDK_INFO",{enumerable:!0,get:function(){return v_.SDK_INFO}});Object.defineProperty(r9,"_globalThis",{enumerable:!0,get:function(){return v_._globalThis}});Object.defineProperty(r9,"getStringFromEnv",{enumerable:!0,get:function(){return v_.getStringFromEnv}});Object.defineProperty(r9,"getBooleanFromEnv",{enumerable:!0,get:function(){return v_.getBooleanFromEnv}});Object.defineProperty(r9,"getNumberFromEnv",{enumerable:!0,get:function(){return v_.getNumberFromEnv}});Object.defineProperty(r9,"getStringListFromEnv",{enumerable:!0,get:function(){return v_.getStringListFromEnv}});Object.defineProperty(r9,"otperformance",{enumerable:!0,get:function(){return v_.otperformance}});Object.defineProperty(r9,"unrefTimer",{enumerable:!0,get:function(){return v_.unrefTimer}});var k66=Qb2();Object.defineProperty(r9,"CompositePropagator",{enumerable:!0,get:function(){return k66.CompositePropagator}});var kO1=zb2();Object.defineProperty(r9,"TRACE_PARENT_HEADER",{enumerable:!0,get:function(){return kO1.TRACE_PARENT_HEADER}});Object.defineProperty(r9,"TRACE_STATE_HEADER",{enumerable:!0,get:function(){return kO1.TRACE_STATE_HEADER}});Object.defineProperty(r9,"W3CTraceContextPropagator",{enumerable:!0,get:function(){return kO1.W3CTraceContextPropagator}});Object.defineProperty(r9,"parseTraceParent",{enumerable:!0,get:function(){return kO1.parseTraceParent}});var yO1=$b2();Object.defineProperty(r9,"RPCType",{enumerable:!0,get:function(){return yO1.RPCType}});Object.defineProperty(r9,"deleteRPCMetadata",{enumerable:!0,get:function(){return yO1.deleteRPCMetadata}});Object.defineProperty(r9,"getRPCMetadata",{enumerable:!0,get:function(){return yO1.getRPCMetadata}});Object.defineProperty(r9,"setRPCMetadata",{enumerable:!0,get:function(){return yO1.setRPCMetadata}});var vF0=c51();Object.defineProperty(r9,"isTracingSuppressed",{enumerable:!0,get:function(){return vF0.isTracingSuppressed}});Object.defineProperty(r9,"suppressTracing",{enumerable:!0,get:function(){return vF0.suppressTracing}});Object.defineProperty(r9,"unsuppressTracing",{enumerable:!0,get:function(){return vF0.unsuppressTracing}});var y66=yF0();Object.defineProperty(r9,"TraceState",{enumerable:!0,get:function(){return y66.TraceState}});var _66=xb2();Object.defineProperty(r9,"merge",{enumerable:!0,get:function(){return _66.merge}});var Ff2=fb2();Object.defineProperty(r9,"TimeoutError",{enumerable:!0,get:function(){return Ff2.TimeoutError}});Object.defineProperty(r9,"callWithTimeout",{enumerable:!0,get:function(){return Ff2.callWithTimeout}});var If2=mb2();Object.defineProperty(r9,"isUrlIgnored",{enumerable:!0,get:function(){return If2.isUrlIgnored}});Object.defineProperty(r9,"urlMatches",{enumerable:!0,get:function(){return If2.urlMatches}});var x66=sb2();Object.defineProperty(r9,"BindOnceFuture",{enumerable:!0,get:function(){return x66.BindOnceFuture}});var v66=eb2();Object.defineProperty(r9,"diagLogLevelFromString",{enumerable:!0,get:function(){return v66.diagLogLevelFromString}});var b66=Df2();r9.internal={_export:b66._export}});
var yF0=E((Jb2)=>{Object.defineProperty(Jb2,"__esModule",{value:!0});Jb2.TraceState=void 0;var Fb2=Gb2(),Ib2=32,m46=512,Yb2=",",Wb2="=";class kF0{_internalState=new Map;constructor(A){if(A)this._parse(A)}set(A,B){let Q=this._clone();if(Q._internalState.has(A))Q._internalState.delete(A);return Q._internalState.set(A,B),Q}unset(A){let B=this._clone();return B._internalState.delete(A),B}get(A){return this._internalState.get(A)}serialize(){return this._keys().reduce((A,B)=>{return A.push(B+Wb2+this.get(B)),A},[]).join(Yb2)}_parse(A){if(A.length>m46)return;if(this._internalState=A.split(Yb2).reverse().reduce((B,Q)=>{let D=Q.trim(),Z=D.indexOf(Wb2);if(Z!==-1){let G=D.slice(0,Z),F=D.slice(Z+1,Q.length);if(Fb2.validateKey(G)&&Fb2.validateValue(F))B.set(G,F)}return B},new Map),this._internalState.size>Ib2)this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,Ib2))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let A=new kF0;return A._internalState=new Map(this._internalState),A}}Jb2.TraceState=kF0});
var zR2=E((KR2)=>{Object.defineProperty(KR2,"__esModule",{value:!0});KR2.NoopTextMapPropagator=void 0;class CR2{inject(A,B){}extract(A,B){return A}fields(){return[]}}KR2.NoopTextMapPropagator=CR2});
var zb2=E((Kb2)=>{Object.defineProperty(Kb2,"__esModule",{value:!0});Kb2.W3CTraceContextPropagator=Kb2.parseTraceParent=Kb2.TRACE_STATE_HEADER=Kb2.TRACE_PARENT_HEADER=void 0;var RO1=ZQ(),d46=c51(),c46=yF0();Kb2.TRACE_PARENT_HEADER="traceparent";Kb2.TRACE_STATE_HEADER="tracestate";var l46="00",p46="(?!ff)[\\da-f]{2}",i46="(?![0]{32})[\\da-f]{32}",n46="(?![0]{16})[\\da-f]{16}",a46="[\\da-f]{2}",s46=new RegExp(`^\\s?(${p46})-(${i46})-(${n46})-(${a46})(-.*)?\\s?$`);function Vb2(A){let B=s46.exec(A);if(!B)return null;if(B[1]==="00"&&B[5])return null;return{traceId:B[2],spanId:B[3],traceFlags:parseInt(B[4],16)}}Kb2.parseTraceParent=Vb2;class Cb2{inject(A,B,Q){let D=RO1.trace.getSpanContext(A);if(!D||d46.isTracingSuppressed(A)||!RO1.isSpanContextValid(D))return;let Z=`${l46}-${D.traceId}-${D.spanId}-0${Number(D.traceFlags||RO1.TraceFlags.NONE).toString(16)}`;if(Q.set(B,Kb2.TRACE_PARENT_HEADER,Z),D.traceState)Q.set(B,Kb2.TRACE_STATE_HEADER,D.traceState.serialize())}extract(A,B,Q){let D=Q.get(B,Kb2.TRACE_PARENT_HEADER);if(!D)return A;let Z=Array.isArray(D)?D[0]:D;if(typeof Z!=="string")return A;let G=Vb2(Z);if(!G)return A;G.isRemote=!0;let F=Q.get(B,Kb2.TRACE_STATE_HEADER);if(F){let I=Array.isArray(F)?F.join(","):F;G.traceState=new c46.TraceState(typeof I==="string"?I:void 0)}return RO1.trace.setSpanContext(A,G)}fields(){return[Kb2.TRACE_PARENT_HEADER,Kb2.TRACE_STATE_HEADER]}}Kb2.W3CTraceContextPropagator=Cb2});

module.exports = pu;
