// dependency_chain package extracted with entry point: msA

var $JA=E((Q55,wJA)=>{var o3Q=lr1();function t3Q(A){let B=["mkdir","realpath","stat","rmdir","utimes"],Q={...A};return B.forEach((D)=>{Q[D]=(...Z)=>{let G=Z.pop(),F;try{F=A[`${D}Sync`](...Z)}catch(I){return G(I)}G(null,F)}}),Q}function e3Q(A){return(...B)=>new Promise((Q,D)=>{B.push((Z,G)=>{if(Z)D(Z);else Q(G)}),A(...B)})}function A7Q(A){return(...B)=>{let Q,D;if(B.push((Z,G)=>{Q=Z,D=G}),A(...B),Q)throw Q;return D}}function B7Q(A){if(A={...A},A.fs=t3Q(A.fs||o3Q),typeof A.retries==="number"&&A.retries>0||A.retries&&typeof A.retries.retries==="number"&&A.retries.retries>0)throw Object.assign(new Error("Cannot use retries with the sync api"),{code:"ESYNC"});return A}wJA.exports={toPromise:e3Q,toSync:A7Q,toSyncOptions:B7Q}});
var BJA=E((s85,AJA)=>{var eWA=J1("stream").Stream;AJA.exports=L3Q;function L3Q(A){return{ReadStream:B,WriteStream:Q};function B(D,Z){if(!(this instanceof B))return new B(D,Z);eWA.call(this);var G=this;this.path=D,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=65536,Z=Z||{};var F=Object.keys(Z);for(var I=0,Y=F.length;I<Y;I++){var W=F[I];this[W]=Z[W]}if(this.encoding)this.setEncoding(this.encoding);if(this.start!==void 0){if(typeof this.start!=="number")throw TypeError("start must be a Number");if(this.end===void 0)this.end=1/0;else if(typeof this.end!=="number")throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}if(this.fd!==null){process.nextTick(function(){G._read()});return}A.open(this.path,this.flags,this.mode,function(J,X){if(J){G.emit("error",J),G.readable=!1;return}G.fd=X,G.emit("open",X),G._read()})}function Q(D,Z){if(!(this instanceof Q))return new Q(D,Z);eWA.call(this),this.path=D,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,Z=Z||{};var G=Object.keys(Z);for(var F=0,I=G.length;F<I;F++){var Y=G[F];this[Y]=Z[Y]}if(this.start!==void 0){if(typeof this.start!=="number")throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}if(this.busy=!1,this._queue=[],this.fd===null)this._open=A.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush()}}});
var CJA=E((f3Q,sr1)=>{var VJA=Symbol();function v3Q(A,B,Q){let D=B[VJA];if(D)return B.stat(A,(G,F)=>{if(G)return Q(G);Q(null,F.mtime,D)});let Z=new Date(Math.ceil(Date.now()/1000)*1000+5);B.utimes(A,Z,Z,(G)=>{if(G)return Q(G);B.stat(A,(F,I)=>{if(F)return Q(F);let Y=I.mtime.getTime()%1000===0?"s":"ms";Object.defineProperty(B,VJA,{value:Y}),Q(null,I.mtime,Y)})})}function b3Q(A){let B=Date.now();if(A==="s")B=Math.ceil(B/1000)*1000;return new Date(B)}f3Q.probe=v3Q;f3Q.getMtime=b3Q});
var DJA=E((r85,QJA)=>{QJA.exports=R3Q;var M3Q=Object.getPrototypeOf||function(A){return A.__proto__};function R3Q(A){if(A===null||typeof A!=="object")return A;if(A instanceof Object)var B={__proto__:M3Q(A)};else var B=Object.create(null);return Object.getOwnPropertyNames(A).forEach(function(Q){Object.defineProperty(B,Q,Object.getOwnPropertyDescriptor(A,Q))}),B}});
var IJA=E((t85,FJA)=>{function Bz(A,B){if(typeof B==="boolean")B={forever:B};if(this._originalTimeouts=JSON.parse(JSON.stringify(A)),this._timeouts=A,this._options=B||{},this._maxRetryTime=B&&B.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._options.forever)this._cachedTimeouts=this._timeouts.slice(0)}FJA.exports=Bz;Bz.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts};Bz.prototype.stop=function(){if(this._timeout)clearTimeout(this._timeout);this._timeouts=[],this._cachedTimeouts=null};Bz.prototype.retry=function(A){if(this._timeout)clearTimeout(this._timeout);if(!A)return!1;var B=new Date().getTime();if(A&&B-this._operationStart>=this._maxRetryTime)return this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(A);var Q=this._timeouts.shift();if(Q===void 0)if(this._cachedTimeouts)this._errors.splice(this._errors.length-1,this._errors.length),this._timeouts=this._cachedTimeouts.slice(0),Q=this._timeouts.shift();else return!1;var D=this,Z=setTimeout(function(){if(D._attempts++,D._operationTimeoutCb){if(D._timeout=setTimeout(function(){D._operationTimeoutCb(D._attempts)},D._operationTimeout),D._options.unref)D._timeout.unref()}D._fn(D._attempts)},Q);if(this._options.unref)Z.unref();return!0};Bz.prototype.attempt=function(A,B){if(this._fn=A,B){if(B.timeout)this._operationTimeout=B.timeout;if(B.cb)this._operationTimeoutCb=B.cb}var Q=this;if(this._operationTimeoutCb)this._timeout=setTimeout(function(){Q._operationTimeoutCb()},Q._operationTimeout);this._operationStart=new Date().getTime(),this._fn(this._attempts)};Bz.prototype.try=function(A){console.log("Using RetryOperation.try() is deprecated"),this.attempt(A)};Bz.prototype.start=function(A){console.log("Using RetryOperation.start() is deprecated"),this.attempt(A)};Bz.prototype.start=Bz.prototype.try;Bz.prototype.errors=function(){return this._errors};Bz.prototype.attempts=function(){return this._attempts};Bz.prototype.mainError=function(){if(this._errors.length===0)return null;var A={},B=null,Q=0;for(var D=0;D<this._errors.length;D++){var Z=this._errors[D],G=Z.message,F=(A[G]||0)+1;if(A[G]=F,F>=Q)B=Z,Q=F}return B}});
var JJA=E((A55,rH1)=>{rH1.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];if(process.platform!=="win32")rH1.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");if(process.platform==="linux")rH1.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")});
var Qz1=E((D55,ok)=>{var An=UJA(),{toPromise:Az1,toSync:Bz1,toSyncOptions:Ao1}=$JA();async function qJA(A,B){let Q=await Az1(An.lock)(A,B);return Az1(Q)}function Q7Q(A,B){let Q=Bz1(An.lock)(A,Ao1(B));return Bz1(Q)}function D7Q(A,B){return Az1(An.unlock)(A,B)}function Z7Q(A,B){return Bz1(An.unlock)(A,Ao1(B))}function G7Q(A,B){return Az1(An.check)(A,B)}function F7Q(A,B){return Bz1(An.check)(A,Ao1(B))}ok.exports=qJA;ok.exports.lock=qJA;ok.exports.unlock=D7Q;ok.exports.lockSync=Q7Q;ok.exports.unlockSync=Z7Q;ok.exports.check=G7Q;ok.exports.checkSync=F7Q});
var UJA=E((i3Q,qQ1)=>{var u3Q=J1("path"),tr1=lr1(),m3Q=WJA(),d3Q=XJA(),KJA=CJA(),xO={};function $Q1(A,B){return B.lockfilePath||`${A}.lock`}function er1(A,B,Q){if(!B.realpath)return Q(null,u3Q.resolve(A));B.fs.realpath(A,Q)}function or1(A,B,Q){let D=$Q1(A,B);B.fs.mkdir(D,(Z)=>{if(!Z)return KJA.probe(D,B.fs,(G,F,I)=>{if(G)return B.fs.rmdir(D,()=>{}),Q(G);Q(null,F,I)});if(Z.code!=="EEXIST")return Q(Z);if(B.stale<=0)return Q(Object.assign(new Error("Lock file is already being held"),{code:"ELOCKED",file:A}));B.fs.stat(D,(G,F)=>{if(G){if(G.code==="ENOENT")return or1(A,{...B,stale:0},Q);return Q(G)}if(!HJA(F,B))return Q(Object.assign(new Error("Lock file is already being held"),{code:"ELOCKED",file:A}));zJA(A,B,(I)=>{if(I)return Q(I);or1(A,{...B,stale:0},Q)})})})}function HJA(A,B){return A.mtime.getTime()<Date.now()-B.stale}function zJA(A,B,Q){B.fs.rmdir($Q1(A,B),(D)=>{if(D&&D.code!=="ENOENT")return Q(D);Q()})}function eH1(A,B){let Q=xO[A];if(Q.updateTimeout)return;if(Q.updateDelay=Q.updateDelay||B.update,Q.updateTimeout=setTimeout(()=>{Q.updateTimeout=null,B.fs.stat(Q.lockfilePath,(D,Z)=>{let G=Q.lastUpdate+B.stale<Date.now();if(D){if(D.code==="ENOENT"||G)return rr1(A,Q,Object.assign(D,{code:"ECOMPROMISED"}));return Q.updateDelay=1000,eH1(A,B)}if(Q.mtime.getTime()!==Z.mtime.getTime())return rr1(A,Q,Object.assign(new Error("Unable to update lock within the stale threshold"),{code:"ECOMPROMISED"}));let I=KJA.getMtime(Q.mtimePrecision);B.fs.utimes(Q.lockfilePath,I,I,(Y)=>{let W=Q.lastUpdate+B.stale<Date.now();if(Q.released)return;if(Y){if(Y.code==="ENOENT"||W)return rr1(A,Q,Object.assign(Y,{code:"ECOMPROMISED"}));return Q.updateDelay=1000,eH1(A,B)}Q.mtime=I,Q.lastUpdate=Date.now(),Q.updateDelay=null,eH1(A,B)})})},Q.updateDelay),Q.updateTimeout.unref)Q.updateTimeout.unref()}function rr1(A,B,Q){if(B.released=!0,B.updateTimeout)clearTimeout(B.updateTimeout);if(xO[A]===B)delete xO[A];B.options.onCompromised(Q)}function c3Q(A,B,Q){B={stale:1e4,update:null,realpath:!0,retries:0,fs:tr1,onCompromised:(D)=>{throw D},...B},B.retries=B.retries||0,B.retries=typeof B.retries==="number"?{retries:B.retries}:B.retries,B.stale=Math.max(B.stale||0,2000),B.update=B.update==null?B.stale/2:B.update||0,B.update=Math.max(Math.min(B.update,B.stale/2),1000),er1(A,B,(D,Z)=>{if(D)return Q(D);let G=m3Q.operation(B.retries);G.attempt(()=>{or1(Z,B,(F,I,Y)=>{if(G.retry(F))return;if(F)return Q(G.mainError());let W=xO[Z]={lockfilePath:$Q1(Z,B),mtime:I,mtimePrecision:Y,options:B,lastUpdate:Date.now()};eH1(Z,B),Q(null,(J)=>{if(W.released)return J&&J(Object.assign(new Error("Lock is already released"),{code:"ERELEASED"}));EJA(Z,{...B,realpath:!1},J)})})})})}function EJA(A,B,Q){B={fs:tr1,realpath:!0,...B},er1(A,B,(D,Z)=>{if(D)return Q(D);let G=xO[Z];if(!G)return Q(Object.assign(new Error("Lock is not acquired/owned by you"),{code:"ENOTACQUIRED"}));G.updateTimeout&&clearTimeout(G.updateTimeout),G.released=!0,delete xO[Z],zJA(Z,B,Q)})}function l3Q(A,B,Q){B={stale:1e4,realpath:!0,fs:tr1,...B},B.stale=Math.max(B.stale||0,2000),er1(A,B,(D,Z)=>{if(D)return Q(D);B.fs.stat($Q1(Z,B),(G,F)=>{if(G)return G.code==="ENOENT"?Q(null,!1):Q(G);return Q(null,!HJA(F,B))})})}function p3Q(){return xO}d3Q(()=>{for(let A in xO){let B=xO[A].options;try{B.fs.rmdirSync($Q1(A,B))}catch(Q){}}});i3Q.lock=c3Q;i3Q.unlock=EJA;i3Q.check=l3Q;i3Q.getLocks=p3Q});
var WJA=E((k3Q)=>{var j3Q=IJA();k3Q.operation=function(A){var B=k3Q.timeouts(A);return new j3Q(B,{forever:A&&A.forever,unref:A&&A.unref,maxRetryTime:A&&A.maxRetryTime})};k3Q.timeouts=function(A){if(A instanceof Array)return[].concat(A);var B={retries:10,factor:2,minTimeout:1000,maxTimeout:1/0,randomize:!1};for(var Q in A)B[Q]=A[Q];if(B.minTimeout>B.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");var D=[];for(var Z=0;Z<B.retries;Z++)D.push(this.createTimeout(Z,B));if(A&&A.forever&&!D.length)D.push(this.createTimeout(Z,B));return D.sort(function(G,F){return G-F}),D};k3Q.createTimeout=function(A,B){var Q=B.randomize?Math.random()+1:1,D=Math.round(Q*B.minTimeout*Math.pow(B.factor,A));return D=Math.min(D,B.maxTimeout),D};k3Q.wrap=function(A,B,Q){if(B instanceof Array)Q=B,B=null;if(!Q){Q=[];for(var D in A)if(typeof A[D]==="function")Q.push(D)}for(var Z=0;Z<Q.length;Z++){var G=Q[Z],F=A[G];A[G]=function I(Y){var W=k3Q.operation(B),J=Array.prototype.slice.call(arguments,1),X=J.pop();J.push(function(V){if(W.retry(V))return;if(V)arguments[0]=W.mainError();X.apply(this,arguments)}),W.attempt(function(){Y.apply(A,J)})}.bind(A,F),A[G].options=B}}});
var XJA=E((B55,ei)=>{var e7=global.process,Lh=function(A){return A&&typeof A==="object"&&typeof A.removeListener==="function"&&typeof A.emit==="function"&&typeof A.reallyExit==="function"&&typeof A.listeners==="function"&&typeof A.kill==="function"&&typeof A.pid==="number"&&typeof A.on==="function"};if(!Lh(e7))ei.exports=function(){return function(){}};else{if(pr1=J1("assert"),Mh=JJA(),ir1=/^win/i.test(e7.platform),ti=J1("events"),typeof ti!=="function")ti=ti.EventEmitter;if(e7.__signal_exit_emitter__)wF=e7.__signal_exit_emitter__;else wF=e7.__signal_exit_emitter__=new ti,wF.count=0,wF.emitted={};if(!wF.infinite)wF.setMaxListeners(1/0),wF.infinite=!0;ei.exports=function(A,B){if(!Lh(global.process))return function(){};if(pr1.equal(typeof A,"function","a callback must be provided for exit handler"),Rh===!1)oH1();var Q="exit";if(B&&B.alwaysLast)Q="afterexit";var D=function(){if(wF.removeListener(Q,A),wF.listeners("exit").length===0&&wF.listeners("afterexit").length===0)EQ1()};return wF.on(Q,A),D},EQ1=function A(){if(!Rh||!Lh(global.process))return;Rh=!1,Mh.forEach(function(B){try{e7.removeListener(B,UQ1[B])}catch(Q){}}),e7.emit=wQ1,e7.reallyExit=tH1,wF.count-=1},ei.exports.unload=EQ1,rk=function A(B,Q,D){if(wF.emitted[B])return;wF.emitted[B]=!0,wF.emit(B,Q,D)},UQ1={},Mh.forEach(function(A){UQ1[A]=function B(){if(!Lh(global.process))return;var Q=e7.listeners(A);if(Q.length===wF.count){if(EQ1(),rk("exit",null,A),rk("afterexit",null,A),ir1&&A==="SIGHUP")A="SIGINT";e7.kill(e7.pid,A)}}}),ei.exports.signals=function(){return Mh},Rh=!1,oH1=function A(){if(Rh||!Lh(global.process))return;Rh=!0,wF.count+=1,Mh=Mh.filter(function(B){try{return e7.on(B,UQ1[B]),!0}catch(Q){return!1}}),e7.emit=ar1,e7.reallyExit=nr1},ei.exports.load=oH1,tH1=e7.reallyExit,nr1=function A(B){if(!Lh(global.process))return;e7.exitCode=B||0,rk("exit",e7.exitCode,null),rk("afterexit",e7.exitCode,null),tH1.call(e7,e7.exitCode)},wQ1=e7.emit,ar1=function A(B,Q){if(B==="exit"&&Lh(global.process)){if(Q!==void 0)e7.exitCode=Q;var D=wQ1.apply(this,arguments);return rk("exit",e7.exitCode,null),rk("afterexit",e7.exitCode,null),D}else return wQ1.apply(this,arguments)}}var pr1,Mh,ir1,ti,wF,EQ1,rk,UQ1,Rh,oH1,tH1,nr1,wQ1,ar1});
var lr1=E((o85,cr1)=>{var yD=J1("fs"),O3Q=tWA(),T3Q=BJA(),P3Q=DJA(),nH1=J1("util"),EY,sH1;if(typeof Symbol==="function"&&typeof Symbol.for==="function")EY=Symbol.for("graceful-fs.queue"),sH1=Symbol.for("graceful-fs.previous");else EY="___graceful-fs.queue",sH1="___graceful-fs.previous";function S3Q(){}function GJA(A,B){Object.defineProperty(A,EY,{get:function(){return B}})}var Nh=S3Q;if(nH1.debuglog)Nh=nH1.debuglog("gfs4");else if(/\bgfs4\b/i.test(process.env.NODE_DEBUG||""))Nh=function(){var A=nH1.format.apply(nH1,arguments);A="GFS4: "+A.split(/\n/).join(`
GFS4: `),console.error(A)};if(!yD[EY]){if(ur1=global[EY]||[],GJA(yD,ur1),yD.close=function(A){function B(Q,D){return A.call(yD,Q,function(Z){if(!Z)ZJA();if(typeof D==="function")D.apply(this,arguments)})}return Object.defineProperty(B,sH1,{value:A}),B}(yD.close),yD.closeSync=function(A){function B(Q){A.apply(yD,arguments),ZJA()}return Object.defineProperty(B,sH1,{value:A}),B}(yD.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||""))process.on("exit",function(){Nh(yD[EY]),J1("assert").equal(yD[EY].length,0)})}var ur1;if(!global[EY])GJA(global,yD[EY]);cr1.exports=mr1(P3Q(yD));if(process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!yD.__patched)cr1.exports=mr1(yD),yD.__patched=!0;function mr1(A){O3Q(A),A.gracefulify=mr1,A.createReadStream=R,A.createWriteStream=T;var B=A.readFile;A.readFile=Q;function Q(k,c,h){if(typeof c==="function")h=c,c=null;return n(k,c,h);function n(a,x,e,W1){return B(a,x,function(U1){if(U1&&(U1.code==="EMFILE"||U1.code==="ENFILE"))oi([n,[a,x,e],U1,W1||Date.now(),Date.now()]);else if(typeof e==="function")e.apply(this,arguments)})}}var D=A.writeFile;A.writeFile=Z;function Z(k,c,h,n){if(typeof h==="function")n=h,h=null;return a(k,c,h,n);function a(x,e,W1,U1,y1){return D(x,e,W1,function(W0){if(W0&&(W0.code==="EMFILE"||W0.code==="ENFILE"))oi([a,[x,e,W1,U1],W0,y1||Date.now(),Date.now()]);else if(typeof U1==="function")U1.apply(this,arguments)})}}var G=A.appendFile;if(G)A.appendFile=F;function F(k,c,h,n){if(typeof h==="function")n=h,h=null;return a(k,c,h,n);function a(x,e,W1,U1,y1){return G(x,e,W1,function(W0){if(W0&&(W0.code==="EMFILE"||W0.code==="ENFILE"))oi([a,[x,e,W1,U1],W0,y1||Date.now(),Date.now()]);else if(typeof U1==="function")U1.apply(this,arguments)})}}var I=A.copyFile;if(I)A.copyFile=Y;function Y(k,c,h,n){if(typeof h==="function")n=h,h=0;return a(k,c,h,n);function a(x,e,W1,U1,y1){return I(x,e,W1,function(W0){if(W0&&(W0.code==="EMFILE"||W0.code==="ENFILE"))oi([a,[x,e,W1,U1],W0,y1||Date.now(),Date.now()]);else if(typeof U1==="function")U1.apply(this,arguments)})}}var W=A.readdir;A.readdir=X;var J=/^v[0-5]\./;function X(k,c,h){if(typeof c==="function")h=c,c=null;var n=J.test(process.version)?function x(e,W1,U1,y1){return W(e,a(e,W1,U1,y1))}:function x(e,W1,U1,y1){return W(e,W1,a(e,W1,U1,y1))};return n(k,c,h);function a(x,e,W1,U1){return function(y1,W0){if(y1&&(y1.code==="EMFILE"||y1.code==="ENFILE"))oi([n,[x,e,W1],y1,U1||Date.now(),Date.now()]);else{if(W0&&W0.sort)W0.sort();if(typeof W1==="function")W1.call(this,y1,W0)}}}}if(process.version.substr(0,4)==="v0.8"){var V=T3Q(A);$=V.ReadStream,N=V.WriteStream}var C=A.ReadStream;if(C)$.prototype=Object.create(C.prototype),$.prototype.open=L;var K=A.WriteStream;if(K)N.prototype=Object.create(K.prototype),N.prototype.open=O;Object.defineProperty(A,"ReadStream",{get:function(){return $},set:function(k){$=k},enumerable:!0,configurable:!0}),Object.defineProperty(A,"WriteStream",{get:function(){return N},set:function(k){N=k},enumerable:!0,configurable:!0});var H=$;Object.defineProperty(A,"FileReadStream",{get:function(){return H},set:function(k){H=k},enumerable:!0,configurable:!0});var z=N;Object.defineProperty(A,"FileWriteStream",{get:function(){return z},set:function(k){z=k},enumerable:!0,configurable:!0});function $(k,c){if(this instanceof $)return C.apply(this,arguments),this;else return $.apply(Object.create($.prototype),arguments)}function L(){var k=this;f(k.path,k.flags,k.mode,function(c,h){if(c){if(k.autoClose)k.destroy();k.emit("error",c)}else k.fd=h,k.emit("open",h),k.read()})}function N(k,c){if(this instanceof N)return K.apply(this,arguments),this;else return N.apply(Object.create(N.prototype),arguments)}function O(){var k=this;f(k.path,k.flags,k.mode,function(c,h){if(c)k.destroy(),k.emit("error",c);else k.fd=h,k.emit("open",h)})}function R(k,c){return new A.ReadStream(k,c)}function T(k,c){return new A.WriteStream(k,c)}var j=A.open;A.open=f;function f(k,c,h,n){if(typeof h==="function")n=h,h=null;return a(k,c,h,n);function a(x,e,W1,U1,y1){return j(x,e,W1,function(W0,F0){if(W0&&(W0.code==="EMFILE"||W0.code==="ENFILE"))oi([a,[x,e,W1,U1],W0,y1||Date.now(),Date.now()]);else if(typeof U1==="function")U1.apply(this,arguments)})}}return A}function oi(A){Nh("ENQUEUE",A[0].name,A[1]),yD[EY].push(A),dr1()}var aH1;function ZJA(){var A=Date.now();for(var B=0;B<yD[EY].length;++B)if(yD[EY][B].length>2)yD[EY][B][3]=A,yD[EY][B][4]=A;dr1()}function dr1(){if(clearTimeout(aH1),aH1=void 0,yD[EY].length===0)return;var A=yD[EY].shift(),B=A[0],Q=A[1],D=A[2],Z=A[3],G=A[4];if(Z===void 0)Nh("RETRY",B.name,Q),B.apply(null,Q);else if(Date.now()-Z>=60000){Nh("TIMEOUT",B.name,Q);var F=Q.pop();if(typeof F==="function")F.call(null,D)}else{var I=Date.now()-G,Y=Math.max(G-Z,1),W=Math.min(Y*1.2,100);if(I>=W)Nh("RETRY",B.name,Q),B.apply(null,Q.concat([Z]));else yD[EY].push(A)}if(aH1===void 0)aH1=setTimeout(dr1,0)}});
var tWA=E((a85,oWA)=>{var sk=J1("constants"),$3Q=process.cwd,pH1=null,q3Q=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){if(!pH1)pH1=$3Q.call(process);return pH1};try{process.cwd()}catch(A){}if(typeof process.chdir==="function"){if(iH1=process.chdir,process.chdir=function(A){pH1=null,iH1.call(process,A)},Object.setPrototypeOf)Object.setPrototypeOf(process.chdir,iH1)}var iH1;oWA.exports=N3Q;function N3Q(A){if(sk.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./))B(A);if(!A.lutimes)Q(A);if(A.chown=G(A.chown),A.fchown=G(A.fchown),A.lchown=G(A.lchown),A.chmod=D(A.chmod),A.fchmod=D(A.fchmod),A.lchmod=D(A.lchmod),A.chownSync=F(A.chownSync),A.fchownSync=F(A.fchownSync),A.lchownSync=F(A.lchownSync),A.chmodSync=Z(A.chmodSync),A.fchmodSync=Z(A.fchmodSync),A.lchmodSync=Z(A.lchmodSync),A.stat=I(A.stat),A.fstat=I(A.fstat),A.lstat=I(A.lstat),A.statSync=Y(A.statSync),A.fstatSync=Y(A.fstatSync),A.lstatSync=Y(A.lstatSync),A.chmod&&!A.lchmod)A.lchmod=function(J,X,V){if(V)process.nextTick(V)},A.lchmodSync=function(){};if(A.chown&&!A.lchown)A.lchown=function(J,X,V,C){if(C)process.nextTick(C)},A.lchownSync=function(){};if(q3Q==="win32")A.rename=typeof A.rename!=="function"?A.rename:function(J){function X(V,C,K){var H=Date.now(),z=0;J(V,C,function $(L){if(L&&(L.code==="EACCES"||L.code==="EPERM"||L.code==="EBUSY")&&Date.now()-H<60000){if(setTimeout(function(){A.stat(C,function(N,O){if(N&&N.code==="ENOENT")J(V,C,$);else K(L)})},z),z<100)z+=10;return}if(K)K(L)})}if(Object.setPrototypeOf)Object.setPrototypeOf(X,J);return X}(A.rename);A.read=typeof A.read!=="function"?A.read:function(J){function X(V,C,K,H,z,$){var L;if($&&typeof $==="function"){var N=0;L=function(O,R,T){if(O&&O.code==="EAGAIN"&&N<10)return N++,J.call(A,V,C,K,H,z,L);$.apply(this,arguments)}}return J.call(A,V,C,K,H,z,L)}if(Object.setPrototypeOf)Object.setPrototypeOf(X,J);return X}(A.read),A.readSync=typeof A.readSync!=="function"?A.readSync:function(J){return function(X,V,C,K,H){var z=0;while(!0)try{return J.call(A,X,V,C,K,H)}catch($){if($.code==="EAGAIN"&&z<10){z++;continue}throw $}}}(A.readSync);function B(J){J.lchmod=function(X,V,C){J.open(X,sk.O_WRONLY|sk.O_SYMLINK,V,function(K,H){if(K){if(C)C(K);return}J.fchmod(H,V,function(z){J.close(H,function($){if(C)C(z||$)})})})},J.lchmodSync=function(X,V){var C=J.openSync(X,sk.O_WRONLY|sk.O_SYMLINK,V),K=!0,H;try{H=J.fchmodSync(C,V),K=!1}finally{if(K)try{J.closeSync(C)}catch(z){}else J.closeSync(C)}return H}}function Q(J){if(sk.hasOwnProperty("O_SYMLINK")&&J.futimes)J.lutimes=function(X,V,C,K){J.open(X,sk.O_SYMLINK,function(H,z){if(H){if(K)K(H);return}J.futimes(z,V,C,function($){J.close(z,function(L){if(K)K($||L)})})})},J.lutimesSync=function(X,V,C){var K=J.openSync(X,sk.O_SYMLINK),H,z=!0;try{H=J.futimesSync(K,V,C),z=!1}finally{if(z)try{J.closeSync(K)}catch($){}else J.closeSync(K)}return H};else if(J.futimes)J.lutimes=function(X,V,C,K){if(K)process.nextTick(K)},J.lutimesSync=function(){}}function D(J){if(!J)return J;return function(X,V,C){return J.call(A,X,V,function(K){if(W(K))K=null;if(C)C.apply(this,arguments)})}}function Z(J){if(!J)return J;return function(X,V){try{return J.call(A,X,V)}catch(C){if(!W(C))throw C}}}function G(J){if(!J)return J;return function(X,V,C,K){return J.call(A,X,V,C,function(H){if(W(H))H=null;if(K)K.apply(this,arguments)})}}function F(J){if(!J)return J;return function(X,V,C){try{return J.call(A,X,V,C)}catch(K){if(!W(K))throw K}}}function I(J){if(!J)return J;return function(X,V,C){if(typeof V==="function")C=V,V=null;function K(H,z){if(z){if(z.uid<0)z.uid+=4294967296;if(z.gid<0)z.gid+=4294967296}if(C)C.apply(this,arguments)}return V?J.call(A,X,V,K):J.call(A,X,K)}}function Y(J){if(!J)return J;return function(X,V){var C=V?J.call(A,X,V):J.call(A,X);if(C){if(C.uid<0)C.uid+=4294967296;if(C.gid<0)C.gid+=4294967296}return C}}function W(J){if(!J)return!0;if(J.code==="ENOSYS")return!0;var X=!process.getuid||process.getuid()!==0;if(X){if(J.code==="EINVAL"||J.code==="EPERM")return!0}return!1}}});

module.exports = msA;
