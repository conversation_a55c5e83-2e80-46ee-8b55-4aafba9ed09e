// dependency_chain package extracted with entry point: z4B

var $9B=E((Yp5,w9B)=>{w9B.exports=function A(B){var Q=0,D=B.length,Z=0,G;while(Z<D)if(Q++,G=B.charCodeAt(Z++),G>=55296&&G<=56319&&Z<D){if(G=B.charCodeAt(Z),(G&64512)==56320)Z++}return Q}});
var $j1=E((Vp5,j9B)=>{var FD1=E9B(),T9B=Cj1(),Uj1=Zm(),Hj1=pV0(),OR6=O9B();j9B.exports=Mx;Mx.normalizeId=Lx;Mx.fullPath=zj1;Mx.url=Ej1;Mx.ids=kR6;Mx.inlineRef=iV0;Mx.schema=wj1;function Mx(A,B,Q){var D=this._refs[Q];if(typeof D=="string")if(this._refs[D])D=this._refs[D];else return Mx.call(this,A,B,D);if(D=D||this._schemas[Q],D instanceof Hj1)return iV0(D.schema,this._opts.inlineRefs)?D.schema:D.validate||this._compile(D);var Z=wj1.call(this,B,Q),G,F,I;if(Z)G=Z.schema,B=Z.root,I=Z.baseId;if(G instanceof Hj1)F=G.validate||A.call(this,G.schema,B,void 0,I);else if(G!==void 0)F=iV0(G,this._opts.inlineRefs)?G:A.call(this,G,B,void 0,I);return F}function wj1(A,B){var Q=FD1.parse(B),D=S9B(Q),Z=zj1(this._getId(A.schema));if(Object.keys(A.schema).length===0||D!==Z){var G=Lx(D),F=this._refs[G];if(typeof F=="string")return TR6.call(this,A,F,Q);else if(F instanceof Hj1){if(!F.validate)this._compile(F);A=F}else if(F=this._schemas[G],F instanceof Hj1){if(!F.validate)this._compile(F);if(G==Lx(B))return{schema:F,root:A,baseId:Z};A=F}else return;if(!A.schema)return;Z=zj1(this._getId(A.schema))}return P9B.call(this,Q,Z,A.schema,A)}function TR6(A,B,Q){var D=wj1.call(this,A,B);if(D){var{schema:Z,baseId:G}=D;A=D.root;var F=this._getId(Z);if(F)G=Ej1(G,F);return P9B.call(this,Q,G,Z,A)}}var PR6=Uj1.toHash(["properties","patternProperties","enum","dependencies","definitions"]);function P9B(A,B,Q,D){if(A.fragment=A.fragment||"",A.fragment.slice(0,1)!="/")return;var Z=A.fragment.split("/");for(var G=1;G<Z.length;G++){var F=Z[G];if(F){if(F=Uj1.unescapeFragment(F),Q=Q[F],Q===void 0)break;var I;if(!PR6[F]){if(I=this._getId(Q),I)B=Ej1(B,I);if(Q.$ref){var Y=Ej1(B,Q.$ref),W=wj1.call(this,D,Y);if(W)Q=W.schema,D=W.root,B=W.baseId}}}}if(Q!==void 0&&Q!==D.schema)return{schema:Q,root:D,baseId:B}}var SR6=Uj1.toHash(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum"]);function iV0(A,B){if(B===!1)return!1;if(B===void 0||B===!0)return nV0(A);else if(B)return aV0(A)<=B}function nV0(A){var B;if(Array.isArray(A)){for(var Q=0;Q<A.length;Q++)if(B=A[Q],typeof B=="object"&&!nV0(B))return!1}else for(var D in A){if(D=="$ref")return!1;if(B=A[D],typeof B=="object"&&!nV0(B))return!1}return!0}function aV0(A){var B=0,Q;if(Array.isArray(A))for(var D=0;D<A.length;D++){if(Q=A[D],typeof Q=="object")B+=aV0(Q);if(B==1/0)return 1/0}else for(var Z in A){if(Z=="$ref")return 1/0;if(SR6[Z])B++;else{if(Q=A[Z],typeof Q=="object")B+=aV0(Q)+1;if(B==1/0)return 1/0}}return B}function zj1(A,B){if(B!==!1)A=Lx(A);var Q=FD1.parse(A);return S9B(Q)}function S9B(A){return FD1.serialize(A).split("#")[0]+"#"}var jR6=/#\/?$/;function Lx(A){return A?A.replace(jR6,""):""}function Ej1(A,B){return B=Lx(B),FD1.resolve(A,B)}function kR6(A){var B=Lx(this._getId(A)),Q={"":B},D={"":zj1(B,!1)},Z={},G=this;return OR6(A,{allKeys:!0},function(F,I,Y,W,J,X,V){if(I==="")return;var C=G._getId(F),K=Q[W],H=D[W]+"/"+J;if(V!==void 0)H+="/"+(typeof V=="number"?V:Uj1.escapeFragment(V));if(typeof C=="string"){C=K=Lx(K?FD1.resolve(K,C):C);var z=G._refs[C];if(typeof z=="string")z=G._refs[z];if(z&&z.schema){if(!T9B(F,z.schema))throw new Error('id "'+C+'" resolves to more than one schema')}else if(C!=Lx(H))if(C[0]=="#"){if(Z[C]&&!T9B(F,Z[C]))throw new Error('id "'+C+'" resolves to more than one schema');Z[C]=F}else G._refs[C]=H}Q[I]=K,D[I]=H}),Z}});
var A4B=E((ip5,eQB)=>{eQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X,V="data"+(F||""),C="valid"+G,K="errs__"+G,H=B.opts.$data&&I&&I.$data,z;if(H)Z+=" var schema"+G+" = "+B.util.getData(I.$data,F,B.dataPathArr)+"; ",z="schema"+G;else z=I;var $=this,L="definition"+G,N=$.definition,O="",R,T,j,f,k;if(H&&N.$data){k="keywordValidate"+G;var c=N.validateSchema;Z+=" var "+L+" = RULES.custom['"+Q+"'].definition; var "+k+" = "+L+".validate;"}else{if(f=B.useCustomRule($,I,B.schema,B),!f)return;z="validate.schema"+Y,k=f.code,R=N.compile,T=N.inline,j=N.macro}var h=k+".errors",n="i"+G,a="ruleErr"+G,x=N.async;if(x&&!B.async)throw new Error("async keyword in sync schema");if(!(T||j))Z+=""+h+" = null;";if(Z+="var "+K+" = errors;var "+C+";",H&&N.$data){if(O+="}",Z+=" if ("+z+" === undefined) { "+C+" = true; } else { ",c)O+="}",Z+=" "+C+" = "+L+".validateSchema("+z+"); if ("+C+") { "}if(T)if(N.statements)Z+=" "+f.validate+" ";else Z+=" "+C+" = "+f.validate+"; ";else if(j){var e=B.util.copy(B),O="";e.level++;var W1="valid"+e.level;e.schema=f.validate,e.schemaPath="";var U1=B.compositeRule;B.compositeRule=e.compositeRule=!0;var y1=B.validate(e).replace(/validate\.schema/g,k);B.compositeRule=e.compositeRule=U1,Z+=" "+y1}else{var W0=W0||[];if(W0.push(Z),Z="",Z+="  "+k+".call( ",B.opts.passContext)Z+="this";else Z+="self";if(R||N.schema===!1)Z+=" , "+V+" ";else Z+=" , "+z+" , "+V+" , validate.schema"+B.schemaPath+" ";if(Z+=" , (dataPath || '')",B.errorPath!='""')Z+=" + "+B.errorPath;var F0=F?"data"+(F-1||""):"parentData",g1=F?B.dataPathArr[F]:"parentDataProperty";Z+=" , "+F0+" , "+g1+" , rootData )  ";var K1=Z;if(Z=W0.pop(),N.errors===!1){if(Z+=" "+C+" = ",x)Z+="await ";Z+=""+K1+"; "}else if(x)h="customErrors"+G,Z+=" var "+h+" = null; try { "+C+" = await "+K1+"; } catch (e) { "+C+" = false; if (e instanceof ValidationError) "+h+" = e.errors; else throw e; } ";else Z+=" "+h+" = null; "+C+" = "+K1+"; "}if(N.modifying)Z+=" if ("+F0+") "+V+" = "+F0+"["+g1+"];";if(Z+=""+O,N.valid){if(J)Z+=" if (true) { "}else{if(Z+=" if ( ",N.valid===void 0)if(Z+=" !",j)Z+=""+W1;else Z+=""+C;else Z+=" "+!N.valid+" ";Z+=") { ",X=$.keyword;var W0=W0||[];W0.push(Z),Z="";var W0=W0||[];if(W0.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: '"+(X||"custom")+"' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { keyword: '"+$.keyword+"' } ",B.opts.messages!==!1)Z+=` , message: 'should pass "`+$.keyword+`" keyword validation' `;if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+V+" ";Z+=" } "}else Z+=" {} ";var G1=Z;if(Z=W0.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+G1+"]); ";else Z+=" validate.errors = ["+G1+"]; return false; ";else Z+=" var err = "+G1+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";var L1=Z;if(Z=W0.pop(),T)if(N.errors){if(N.errors!="full"){if(Z+="  for (var "+n+"="+K+"; "+n+"<errors; "+n+"++) { var "+a+" = vErrors["+n+"]; if ("+a+".dataPath === undefined) "+a+".dataPath = (dataPath || '') + "+B.errorPath+"; if ("+a+".schemaPath === undefined) { "+a+'.schemaPath = "'+W+'"; } ',B.opts.verbose)Z+=" "+a+".schema = "+z+"; "+a+".data = "+V+"; ";Z+=" } "}}else if(N.errors===!1)Z+=" "+L1+" ";else{if(Z+=" if ("+K+" == errors) { "+L1+" } else {  for (var "+n+"="+K+"; "+n+"<errors; "+n+"++) { var "+a+" = vErrors["+n+"]; if ("+a+".dataPath === undefined) "+a+".dataPath = (dataPath || '') + "+B.errorPath+"; if ("+a+".schemaPath === undefined) { "+a+'.schemaPath = "'+W+'"; } ',B.opts.verbose)Z+=" "+a+".schema = "+z+"; "+a+".data = "+V+"; ";Z+=" } } "}else if(j){if(Z+="   var err =   ",B.createErrors!==!1){if(Z+=" { keyword: '"+(X||"custom")+"' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { keyword: '"+$.keyword+"' } ",B.opts.messages!==!1)Z+=` , message: 'should pass "`+$.keyword+`" keyword validation' `;if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+V+" ";Z+=" } "}else Z+=" {} ";if(Z+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(vErrors); ";else Z+=" validate.errors = vErrors; return false; "}else if(N.errors===!1)Z+=" "+L1+" ";else{if(Z+=" if (Array.isArray("+h+")) { if (vErrors === null) vErrors = "+h+"; else vErrors = vErrors.concat("+h+"); errors = vErrors.length;  for (var "+n+"="+K+"; "+n+"<errors; "+n+"++) { var "+a+" = vErrors["+n+"]; if ("+a+".dataPath === undefined) "+a+".dataPath = (dataPath || '') + "+B.errorPath+";  "+a+'.schemaPath = "'+W+'";  ',B.opts.verbose)Z+=" "+a+".schema = "+z+"; "+a+".data = "+V+"; ";Z+=" } } else { "+L1+" } "}if(Z+=" } ",J)Z+=" else { "}return Z}});
var AC0=E((jp5,NQB)=>{NQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,O,X="data"+(F||""),V=B.opts.$data&&I&&I.$data,C;if(V)Z+=" var schema"+G+" = "+B.util.getData(I.$data,F,B.dataPathArr)+"; ",C="schema"+G;else C=I;var K=Q=="maximum",H=K?"exclusiveMaximum":"exclusiveMinimum",z=B.schema[H],$=B.opts.$data&&z&&z.$data,L=K?"<":">",N=K?">":"<",O=void 0;if(!(V||typeof I=="number"||I===void 0))throw new Error(Q+" must be number");if(!($||z===void 0||typeof z=="number"||typeof z=="boolean"))throw new Error(H+" must be number or boolean");if($){var R=B.util.getData(z.$data,F,B.dataPathArr),T="exclusive"+G,j="exclType"+G,f="exclIsNumber"+G,k="op"+G,c="' + "+k+" + '";Z+=" var schemaExcl"+G+" = "+R+"; ",R="schemaExcl"+G,Z+=" var "+T+"; var "+j+" = typeof "+R+"; if ("+j+" != 'boolean' && "+j+" != 'undefined' && "+j+" != 'number') { ";var O=H,h=h||[];if(h.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: '"+(O||"_exclusiveLimit")+"' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: {} ",B.opts.messages!==!1)Z+=" , message: '"+H+" should be boolean' ";if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";var n=Z;if(Z=h.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+n+"]); ";else Z+=" validate.errors = ["+n+"]; return false; ";else Z+=" var err = "+n+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(Z+=" } else if ( ",V)Z+=" ("+C+" !== undefined && typeof "+C+" != 'number') || ";if(Z+=" "+j+" == 'number' ? ( ("+T+" = "+C+" === undefined || "+R+" "+L+"= "+C+") ? "+X+" "+N+"= "+R+" : "+X+" "+N+" "+C+" ) : ( ("+T+" = "+R+" === true) ? "+X+" "+N+"= "+C+" : "+X+" "+N+" "+C+" ) || "+X+" !== "+X+") { var op"+G+" = "+T+" ? '"+L+"' : '"+L+"='; ",I===void 0)O=H,W=B.errSchemaPath+"/"+H,C=R,V=$}else{var f=typeof z=="number",c=L;if(f&&V){var k="'"+c+"'";if(Z+=" if ( ",V)Z+=" ("+C+" !== undefined && typeof "+C+" != 'number') || ";Z+=" ( "+C+" === undefined || "+z+" "+L+"= "+C+" ? "+X+" "+N+"= "+z+" : "+X+" "+N+" "+C+" ) || "+X+" !== "+X+") { "}else{if(f&&I===void 0)T=!0,O=H,W=B.errSchemaPath+"/"+H,C=z,N+="=";else{if(f)C=Math[K?"min":"max"](z,I);if(z===(f?C:!0))T=!0,O=H,W=B.errSchemaPath+"/"+H,N+="=";else T=!1,c+="="}var k="'"+c+"'";if(Z+=" if ( ",V)Z+=" ("+C+" !== undefined && typeof "+C+" != 'number') || ";Z+=" "+X+" "+N+" "+C+" || "+X+" !== "+X+") { "}}O=O||Q;var h=h||[];if(h.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: '"+(O||"_limit")+"' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { comparison: "+k+", limit: "+C+", exclusive: "+T+" } ",B.opts.messages!==!1)if(Z+=" , message: 'should be "+c+" ",V)Z+="' + "+C;else Z+=""+C+"'";if(B.opts.verbose){if(Z+=" , schema:  ",V)Z+="validate.schema"+Y;else Z+=""+I;Z+="         , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" "}Z+=" } "}else Z+=" {} ";var n=Z;if(Z=h.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+n+"]); ";else Z+=" validate.errors = ["+n+"]; return false; ";else Z+=" var err = "+n+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(Z+=" } ",J)Z+=" else { ";return Z}});
var BC0=E((kp5,LQB)=>{LQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,H,X="data"+(F||""),V=B.opts.$data&&I&&I.$data,C;if(V)Z+=" var schema"+G+" = "+B.util.getData(I.$data,F,B.dataPathArr)+"; ",C="schema"+G;else C=I;if(!(V||typeof I=="number"))throw new Error(Q+" must be number");var K=Q=="maxItems"?">":"<";if(Z+="if ( ",V)Z+=" ("+C+" !== undefined && typeof "+C+" != 'number') || ";Z+=" "+X+".length "+K+" "+C+") { ";var H=Q,z=z||[];if(z.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: '"+(H||"_limitItems")+"' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { limit: "+C+" } ",B.opts.messages!==!1){if(Z+=" , message: 'should NOT have ",Q=="maxItems")Z+="more";else Z+="fewer";if(Z+=" than ",V)Z+="' + "+C+" + '";else Z+=""+I;Z+=" items' "}if(B.opts.verbose){if(Z+=" , schema:  ",V)Z+="validate.schema"+Y;else Z+=""+I;Z+="         , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" "}Z+=" } "}else Z+=" {} ";var $=Z;if(Z=z.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+$+"]); ";else Z+=" validate.errors = ["+$+"]; return false; ";else Z+=" var err = "+$+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(Z+="} ",J)Z+=" else { ";return Z}});
var BQB=E((wp5,AQB)=>{AQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.errSchemaPath+"/"+Q,W=!B.opts.allErrors,J="data"+(F||""),X="valid"+G,V,C;if(I=="#"||I=="#/")if(B.isRoot)V=B.async,C="validate";else V=B.root.schema.$async===!0,C="root.refVal[0]";else{var K=B.resolveRef(B.baseId,I,B.isRoot);if(K===void 0){var H=B.MissingRefError.message(B.baseId,I);if(B.opts.missingRefs=="fail"){B.logger.error(H);var z=z||[];if(z.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: '$ref' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(Y)+" , params: { ref: '"+B.util.escapeQuotes(I)+"' } ",B.opts.messages!==!1)Z+=" , message: 'can\\'t resolve reference "+B.util.escapeQuotes(I)+"' ";if(B.opts.verbose)Z+=" , schema: "+B.util.toQuotedString(I)+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+J+" ";Z+=" } "}else Z+=" {} ";var $=Z;if(Z=z.pop(),!B.compositeRule&&W)if(B.async)Z+=" throw new ValidationError(["+$+"]); ";else Z+=" validate.errors = ["+$+"]; return false; ";else Z+=" var err = "+$+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(W)Z+=" if (false) { "}else if(B.opts.missingRefs=="ignore"){if(B.logger.warn(H),W)Z+=" if (true) { "}else throw new B.MissingRefError(B.baseId,I,H)}else if(K.inline){var L=B.util.copy(B);L.level++;var N="valid"+L.level;L.schema=K.schema,L.schemaPath="",L.errSchemaPath=I;var O=B.validate(L).replace(/validate\.schema/g,K.code);if(Z+=" "+O+" ",W)Z+=" if ("+N+") { "}else V=K.$async===!0||B.async&&K.$async!==!1,C=K.code}if(C){var z=z||[];if(z.push(Z),Z="",B.opts.passContext)Z+=" "+C+".call(this, ";else Z+=" "+C+"( ";if(Z+=" "+J+", (dataPath || '')",B.errorPath!='""')Z+=" + "+B.errorPath;var R=F?"data"+(F-1||""):"parentData",T=F?B.dataPathArr[F]:"parentDataProperty";Z+=" , "+R+" , "+T+", rootData)  ";var j=Z;if(Z=z.pop(),V){if(!B.async)throw new Error("async schema referenced by sync schema");if(W)Z+=" var "+X+"; ";if(Z+=" try { await "+j+"; ",W)Z+=" "+X+" = true; ";if(Z+=" } catch (e) { if (!(e instanceof ValidationError)) throw e; if (vErrors === null) vErrors = e.errors; else vErrors = vErrors.concat(e.errors); errors = vErrors.length; ",W)Z+=" "+X+" = false; ";if(Z+=" } ",W)Z+=" if ("+X+") { "}else if(Z+=" if (!"+j+") { if (vErrors === null) vErrors = "+C+".errors; else vErrors = vErrors.concat("+C+".errors); errors = vErrors.length; } ",W)Z+=" else { "}return Z}});
var CQB=E((Rp5,VQB)=>{VQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||""),V="errs__"+G,C=B.util.copy(B),K="";C.level++;var H="valid"+C.level,z={},$={},L=B.opts.ownProperties;for(T in I){if(T=="__proto__")continue;var N=I[T],O=Array.isArray(N)?$:z;O[T]=N}Z+="var "+V+" = errors;";var R=B.errorPath;Z+="var missing"+G+";";for(var T in $)if(O=$[T],O.length){if(Z+=" if ( "+X+B.util.getProperty(T)+" !== undefined ",L)Z+=" && Object.prototype.hasOwnProperty.call("+X+", '"+B.util.escapeQuotes(T)+"') ";if(J){Z+=" && ( ";var j=O;if(j){var f,k=-1,c=j.length-1;while(k<c){if(f=j[k+=1],k)Z+=" || ";var h=B.util.getProperty(f),n=X+h;if(Z+=" ( ( "+n+" === undefined ",L)Z+=" || ! Object.prototype.hasOwnProperty.call("+X+", '"+B.util.escapeQuotes(f)+"') ";Z+=") && (missing"+G+" = "+B.util.toQuotedString(B.opts.jsonPointers?f:h)+") ) "}}Z+=")) {  ";var a="missing"+G,x="' + "+a+" + '";if(B.opts._errorDataPathProperty)B.errorPath=B.opts.jsonPointers?B.util.getPathExpr(R,a,!0):R+" + "+a;var e=e||[];if(e.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: 'dependencies' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { property: '"+B.util.escapeQuotes(T)+"', missingProperty: '"+x+"', depsCount: "+O.length+", deps: '"+B.util.escapeQuotes(O.length==1?O[0]:O.join(", "))+"' } ",B.opts.messages!==!1){if(Z+=" , message: 'should have ",O.length==1)Z+="property "+B.util.escapeQuotes(O[0]);else Z+="properties "+B.util.escapeQuotes(O.join(", "));Z+=" when property "+B.util.escapeQuotes(T)+" is present' "}if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";var W1=Z;if(Z=e.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+W1+"]); ";else Z+=" validate.errors = ["+W1+"]; return false; ";else Z+=" var err = "+W1+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}else{Z+=" ) { ";var U1=O;if(U1){var f,y1=-1,W0=U1.length-1;while(y1<W0){f=U1[y1+=1];var h=B.util.getProperty(f),x=B.util.escapeQuotes(f),n=X+h;if(B.opts._errorDataPathProperty)B.errorPath=B.util.getPath(R,f,B.opts.jsonPointers);if(Z+=" if ( "+n+" === undefined ",L)Z+=" || ! Object.prototype.hasOwnProperty.call("+X+", '"+B.util.escapeQuotes(f)+"') ";if(Z+=") {  var err =   ",B.createErrors!==!1){if(Z+=" { keyword: 'dependencies' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { property: '"+B.util.escapeQuotes(T)+"', missingProperty: '"+x+"', depsCount: "+O.length+", deps: '"+B.util.escapeQuotes(O.length==1?O[0]:O.join(", "))+"' } ",B.opts.messages!==!1){if(Z+=" , message: 'should have ",O.length==1)Z+="property "+B.util.escapeQuotes(O[0]);else Z+="properties "+B.util.escapeQuotes(O.join(", "));Z+=" when property "+B.util.escapeQuotes(T)+" is present' "}if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";Z+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } "}}}if(Z+=" }   ",J)K+="}",Z+=" else { "}B.errorPath=R;var F0=C.baseId;for(var T in z){var N=z[T];if(B.opts.strictKeywords?typeof N=="object"&&Object.keys(N).length>0||N===!1:B.util.schemaHasRules(N,B.RULES.all)){if(Z+=" "+H+" = true; if ( "+X+B.util.getProperty(T)+" !== undefined ",L)Z+=" && Object.prototype.hasOwnProperty.call("+X+", '"+B.util.escapeQuotes(T)+"') ";if(Z+=") { ",C.schema=N,C.schemaPath=Y+B.util.getProperty(T),C.errSchemaPath=W+"/"+B.util.escapeFragment(T),Z+="  "+B.validate(C)+" ",C.baseId=F0,Z+=" }  ",J)Z+=" if ("+H+") { ",K+="}"}}if(J)Z+="   "+K+" if ("+V+" == errors) {";return Z}});
var Cj1=E((Ip5,U9B)=>{U9B.exports=function A(B,Q){if(B===Q)return!0;if(B&&Q&&typeof B=="object"&&typeof Q=="object"){if(B.constructor!==Q.constructor)return!1;var D,Z,G;if(Array.isArray(B)){if(D=B.length,D!=Q.length)return!1;for(Z=D;Z--!==0;)if(!A(B[Z],Q[Z]))return!1;return!0}if(B.constructor===RegExp)return B.source===Q.source&&B.flags===Q.flags;if(B.valueOf!==Object.prototype.valueOf)return B.valueOf()===Q.valueOf();if(B.toString!==Object.prototype.toString)return B.toString()===Q.toString();if(G=Object.keys(B),D=G.length,D!==Object.keys(Q).length)return!1;for(Z=D;Z--!==0;)if(!Object.prototype.hasOwnProperty.call(Q,G[Z]))return!1;for(Z=D;Z--!==0;){var F=G[Z];if(!A(B[F],Q[F]))return!1}return!0}return B!==B&&Q!==Q}});
var D4B=E((ap5,Q4B)=>{var B4B=GC0();Q4B.exports={$id:"https://github.com/ajv-validator/ajv/blob/master/lib/definition_schema.js",definitions:{simpleTypes:B4B.definitions.simpleTypes},type:"object",dependencies:{schema:["validate"],$data:["validate"],statements:["inline"],valid:{not:{required:["macro"]}}},properties:{type:B4B.properties.type,schema:{type:"boolean"},statements:{type:"boolean"},dependencies:{type:"array",items:{type:"string"}},metaSchema:{type:"object"},modifying:{type:"boolean"},valid:{type:"boolean"},$data:{type:"boolean"},async:{type:"boolean"},errors:{anyOf:[{type:"boolean"},{const:"full"}]}}}});
var DC0=E((_p5,RQB)=>{RQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,H,X="data"+(F||""),V=B.opts.$data&&I&&I.$data,C;if(V)Z+=" var schema"+G+" = "+B.util.getData(I.$data,F,B.dataPathArr)+"; ",C="schema"+G;else C=I;if(!(V||typeof I=="number"))throw new Error(Q+" must be number");var K=Q=="maxProperties"?">":"<";if(Z+="if ( ",V)Z+=" ("+C+" !== undefined && typeof "+C+" != 'number') || ";Z+=" Object.keys("+X+").length "+K+" "+C+") { ";var H=Q,z=z||[];if(z.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: '"+(H||"_limitProperties")+"' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { limit: "+C+" } ",B.opts.messages!==!1){if(Z+=" , message: 'should NOT have ",Q=="maxProperties")Z+="more";else Z+="fewer";if(Z+=" than ",V)Z+="' + "+C+" + '";else Z+=""+I;Z+=" properties' "}if(B.opts.verbose){if(Z+=" , schema:  ",V)Z+="validate.schema"+Y;else Z+=""+I;Z+="         , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" "}Z+=" } "}else Z+=" {} ";var $=Z;if(Z=z.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+$+"]); ";else Z+=" validate.errors = ["+$+"]; return false; ";else Z+=" var err = "+$+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(Z+="} ",J)Z+=" else { ";return Z}});
var DQB=E(($p5,QQB)=>{QQB.exports=function A(B,Q,D){var Z=" ",G=B.schema[Q],F=B.schemaPath+B.util.getProperty(Q),I=B.errSchemaPath+"/"+Q,Y=!B.opts.allErrors,W=B.util.copy(B),J="";W.level++;var X="valid"+W.level,V=W.baseId,C=!0,K=G;if(K){var H,z=-1,$=K.length-1;while(z<$)if(H=K[z+=1],B.opts.strictKeywords?typeof H=="object"&&Object.keys(H).length>0||H===!1:B.util.schemaHasRules(H,B.RULES.all)){if(C=!1,W.schema=H,W.schemaPath=F+"["+z+"]",W.errSchemaPath=I+"/"+z,Z+="  "+B.validate(W)+" ",W.baseId=V,Y)Z+=" if ("+X+") { ",J+="}"}}if(Y)if(C)Z+=" if (true) { ";else Z+=" "+J.slice(0,-1)+" ";return Z}});
var E9B=E((Vj1,z9B)=>{(function(A,B){typeof Vj1==="object"&&typeof z9B!=="undefined"?B(Vj1):typeof define==="function"&&define.amd?define(["exports"],B):B(A.URI=A.URI||{})})(Vj1,function(A){function B(){for(var Y0=arguments.length,k1=Array(Y0),Q0=0;Q0<Y0;Q0++)k1[Q0]=arguments[Q0];if(k1.length>1){k1[0]=k1[0].slice(0,-1);var u0=k1.length-1;for(var i0=1;i0<u0;++i0)k1[i0]=k1[i0].slice(1,-1);return k1[u0]=k1[u0].slice(1),k1.join("")}else return k1[0]}function Q(Y0){return"(?:"+Y0+")"}function D(Y0){return Y0===void 0?"undefined":Y0===null?"null":Object.prototype.toString.call(Y0).split(" ").pop().split("]").shift().toLowerCase()}function Z(Y0){return Y0.toUpperCase()}function G(Y0){return Y0!==void 0&&Y0!==null?Y0 instanceof Array?Y0:typeof Y0.length!=="number"||Y0.split||Y0.setInterval||Y0.call?[Y0]:Array.prototype.slice.call(Y0):[]}function F(Y0,k1){var Q0=Y0;if(k1)for(var u0 in k1)Q0[u0]=k1[u0];return Q0}function I(Y0){var k1="[A-Za-z]",Q0="[\\x0D]",u0="[0-9]",i0="[\\x22]",mA=B(u0,"[A-Fa-f]"),lB="[\\x0A]",x9="[\\x20]",zQ=Q(Q("%[EFef]"+mA+"%"+mA+mA+"%"+mA+mA)+"|"+Q("%[89A-Fa-f]"+mA+"%"+mA+mA)+"|"+Q("%"+mA+mA)),q4="[\\:\\/\\?\\#\\[\\]\\@]",xB="[\\!\\$\\&\\'\\(\\)\\*\\+\\,\\;\\=]",$Q=B(q4,xB),z6=Y0?"[\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]":"[]",oQ=Y0?"[\\uE000-\\uF8FF]":"[]",U9=B(k1,u0,"[\\-\\.\\_\\~]",z6),J4=Q(k1+B(k1,u0,"[\\+\\-\\.]")+"*"),_1=Q(Q(zQ+"|"+B(U9,xB,"[\\:]"))+"*"),u1=Q(Q("25[0-5]")+"|"+Q("2[0-4]"+u0)+"|"+Q("1"+u0+u0)+"|"+Q("[1-9]"+u0)+"|"+u0),q0=Q(Q("25[0-5]")+"|"+Q("2[0-4]"+u0)+"|"+Q("1"+u0+u0)+"|"+Q("0?[1-9]"+u0)+"|0?0?"+u0),y0=Q(q0+"\\."+q0+"\\."+q0+"\\."+q0),U0=Q(mA+"{1,4}"),v0=Q(Q(U0+"\\:"+U0)+"|"+y0),EA=Q(Q(U0+"\\:")+"{6}"+v0),ZA=Q("\\:\\:"+Q(U0+"\\:")+"{5}"+v0),VA=Q(Q(U0)+"?\\:\\:"+Q(U0+"\\:")+"{4}"+v0),AA=Q(Q(Q(U0+"\\:")+"{0,1}"+U0)+"?\\:\\:"+Q(U0+"\\:")+"{3}"+v0),UA=Q(Q(Q(U0+"\\:")+"{0,2}"+U0)+"?\\:\\:"+Q(U0+"\\:")+"{2}"+v0),uB=Q(Q(Q(U0+"\\:")+"{0,3}"+U0)+"?\\:\\:"+U0+"\\:"+v0),f2=Q(Q(Q(U0+"\\:")+"{0,4}"+U0)+"?\\:\\:"+v0),HB=Q(Q(Q(U0+"\\:")+"{0,5}"+U0)+"?\\:\\:"+U0),E1=Q(Q(Q(U0+"\\:")+"{0,6}"+U0)+"?\\:\\:"),t1=Q([EA,ZA,VA,AA,UA,uB,f2,HB,E1].join("|")),d1=Q(Q(U9+"|"+zQ)+"+"),C0=Q(t1+"\\%25"+d1),L0=Q(t1+Q("\\%25|\\%(?!"+mA+"{2})")+d1),$0=Q("[vV]"+mA+"+\\."+B(U9,xB,"[\\:]")+"+"),QA=Q("\\["+Q(L0+"|"+t1+"|"+$0)+"\\]"),h0=Q(Q(zQ+"|"+B(U9,xB))+"*"),e0=Q(QA+"|"+y0+"(?!"+h0+")|"+h0),XA=Q(u0+"*"),HA=Q(Q(_1+"@")+"?"+e0+Q("\\:"+XA)+"?"),iA=Q(zQ+"|"+B(U9,xB,"[\\:\\@]")),h2=Q(iA+"*"),vB=Q(iA+"+"),v9=Q(Q(zQ+"|"+B(U9,xB,"[\\@]"))+"+"),FQ=Q(Q("\\/"+h2)+"*"),qQ=Q("\\/"+Q(vB+FQ)+"?"),o8=Q(v9+FQ),u6=Q(vB+FQ),A6="(?!"+iA+")",lD=Q(FQ+"|"+qQ+"|"+o8+"|"+u6+"|"+A6),y5=Q(Q(iA+"|"+B("[\\/\\?]",oQ))+"*"),BF=Q(Q(iA+"|[\\/\\?]")+"*"),uF=Q(Q("\\/\\/"+HA+FQ)+"|"+qQ+"|"+u6+"|"+A6),SQ=Q(J4+"\\:"+uF+Q("\\?"+y5)+"?"+Q("\\#"+BF)+"?"),JG=Q(Q("\\/\\/"+HA+FQ)+"|"+qQ+"|"+o8+"|"+A6),dI=Q(JG+Q("\\?"+y5)+"?"+Q("\\#"+BF)+"?"),GH=Q(SQ+"|"+dI),YR=Q(J4+"\\:"+uF+Q("\\?"+y5)+"?"),HU="^("+J4+")\\:"+Q(Q("\\/\\/("+Q("("+_1+")@")+"?("+e0+")"+Q("\\:("+XA+")")+"?)")+"?("+FQ+"|"+qQ+"|"+u6+"|"+A6+")")+Q("\\?("+y5+")")+"?"+Q("\\#("+BF+")")+"?$",c3="^(){0}"+Q(Q("\\/\\/("+Q("("+_1+")@")+"?("+e0+")"+Q("\\:("+XA+")")+"?)")+"?("+FQ+"|"+qQ+"|"+o8+"|"+A6+")")+Q("\\?("+y5+")")+"?"+Q("\\#("+BF+")")+"?$",zU="^("+J4+")\\:"+Q(Q("\\/\\/("+Q("("+_1+")@")+"?("+e0+")"+Q("\\:("+XA+")")+"?)")+"?("+FQ+"|"+qQ+"|"+u6+"|"+A6+")")+Q("\\?("+y5+")")+"?$",WR="^"+Q("\\#("+BF+")")+"?$",t$="^"+Q("("+_1+")@")+"?("+e0+")"+Q("\\:("+XA+")")+"?$";return{NOT_SCHEME:new RegExp(B("[^]",k1,u0,"[\\+\\-\\.]"),"g"),NOT_USERINFO:new RegExp(B("[^\\%\\:]",U9,xB),"g"),NOT_HOST:new RegExp(B("[^\\%\\[\\]\\:]",U9,xB),"g"),NOT_PATH:new RegExp(B("[^\\%\\/\\:\\@]",U9,xB),"g"),NOT_PATH_NOSCHEME:new RegExp(B("[^\\%\\/\\@]",U9,xB),"g"),NOT_QUERY:new RegExp(B("[^\\%]",U9,xB,"[\\:\\@\\/\\?]",oQ),"g"),NOT_FRAGMENT:new RegExp(B("[^\\%]",U9,xB,"[\\:\\@\\/\\?]"),"g"),ESCAPE:new RegExp(B("[^]",U9,xB),"g"),UNRESERVED:new RegExp(U9,"g"),OTHER_CHARS:new RegExp(B("[^\\%]",U9,$Q),"g"),PCT_ENCODED:new RegExp(zQ,"g"),IPV4ADDRESS:new RegExp("^("+y0+")$"),IPV6ADDRESS:new RegExp("^\\[?("+t1+")"+Q(Q("\\%25|\\%(?!"+mA+"{2})")+"("+d1+")")+"?\\]?$")}}var Y=I(!1),W=I(!0),J=function(){function Y0(k1,Q0){var u0=[],i0=!0,mA=!1,lB=void 0;try{for(var x9=k1[Symbol.iterator](),zQ;!(i0=(zQ=x9.next()).done);i0=!0)if(u0.push(zQ.value),Q0&&u0.length===Q0)break}catch(q4){mA=!0,lB=q4}finally{try{if(!i0&&x9.return)x9.return()}finally{if(mA)throw lB}}return u0}return function(k1,Q0){if(Array.isArray(k1))return k1;else if(Symbol.iterator in Object(k1))return Y0(k1,Q0);else throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),X=function(Y0){if(Array.isArray(Y0)){for(var k1=0,Q0=Array(Y0.length);k1<Y0.length;k1++)Q0[k1]=Y0[k1];return Q0}else return Array.from(Y0)},V=2147483647,C=36,K=1,H=26,z=38,$=700,L=72,N=128,O="-",R=/^xn--/,T=/[^\0-\x7E]/,j=/[\x2E\u3002\uFF0E\uFF61]/g,f={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},k=C-K,c=Math.floor,h=String.fromCharCode;function n(Y0){throw new RangeError(f[Y0])}function a(Y0,k1){var Q0=[],u0=Y0.length;while(u0--)Q0[u0]=k1(Y0[u0]);return Q0}function x(Y0,k1){var Q0=Y0.split("@"),u0="";if(Q0.length>1)u0=Q0[0]+"@",Y0=Q0[1];Y0=Y0.replace(j,".");var i0=Y0.split("."),mA=a(i0,k1).join(".");return u0+mA}function e(Y0){var k1=[],Q0=0,u0=Y0.length;while(Q0<u0){var i0=Y0.charCodeAt(Q0++);if(i0>=55296&&i0<=56319&&Q0<u0){var mA=Y0.charCodeAt(Q0++);if((mA&64512)==56320)k1.push(((i0&1023)<<10)+(mA&1023)+65536);else k1.push(i0),Q0--}else k1.push(i0)}return k1}var W1=function Y0(k1){return String.fromCodePoint.apply(String,X(k1))},U1=function Y0(k1){if(k1-48<10)return k1-22;if(k1-65<26)return k1-65;if(k1-97<26)return k1-97;return C},y1=function Y0(k1,Q0){return k1+22+75*(k1<26)-((Q0!=0)<<5)},W0=function Y0(k1,Q0,u0){var i0=0;k1=u0?c(k1/$):k1>>1,k1+=c(k1/Q0);for(;k1>k*H>>1;i0+=C)k1=c(k1/k);return c(i0+(k+1)*k1/(k1+z))},F0=function Y0(k1){var Q0=[],u0=k1.length,i0=0,mA=N,lB=L,x9=k1.lastIndexOf(O);if(x9<0)x9=0;for(var zQ=0;zQ<x9;++zQ){if(k1.charCodeAt(zQ)>=128)n("not-basic");Q0.push(k1.charCodeAt(zQ))}for(var q4=x9>0?x9+1:0;q4<u0;){var xB=i0;for(var $Q=1,z6=C;;z6+=C){if(q4>=u0)n("invalid-input");var oQ=U1(k1.charCodeAt(q4++));if(oQ>=C||oQ>c((V-i0)/$Q))n("overflow");i0+=oQ*$Q;var U9=z6<=lB?K:z6>=lB+H?H:z6-lB;if(oQ<U9)break;var J4=C-U9;if($Q>c(V/J4))n("overflow");$Q*=J4}var _1=Q0.length+1;if(lB=W0(i0-xB,_1,xB==0),c(i0/_1)>V-mA)n("overflow");mA+=c(i0/_1),i0%=_1,Q0.splice(i0++,0,mA)}return String.fromCodePoint.apply(String,Q0)},g1=function Y0(k1){var Q0=[];k1=e(k1);var u0=k1.length,i0=N,mA=0,lB=L,x9=!0,zQ=!1,q4=void 0;try{for(var xB=k1[Symbol.iterator](),$Q;!(x9=($Q=xB.next()).done);x9=!0){var z6=$Q.value;if(z6<128)Q0.push(h(z6))}}catch(L0){zQ=!0,q4=L0}finally{try{if(!x9&&xB.return)xB.return()}finally{if(zQ)throw q4}}var oQ=Q0.length,U9=oQ;if(oQ)Q0.push(O);while(U9<u0){var J4=V,_1=!0,u1=!1,q0=void 0;try{for(var y0=k1[Symbol.iterator](),U0;!(_1=(U0=y0.next()).done);_1=!0){var v0=U0.value;if(v0>=i0&&v0<J4)J4=v0}}catch(L0){u1=!0,q0=L0}finally{try{if(!_1&&y0.return)y0.return()}finally{if(u1)throw q0}}var EA=U9+1;if(J4-i0>c((V-mA)/EA))n("overflow");mA+=(J4-i0)*EA,i0=J4;var ZA=!0,VA=!1,AA=void 0;try{for(var UA=k1[Symbol.iterator](),uB;!(ZA=(uB=UA.next()).done);ZA=!0){var f2=uB.value;if(f2<i0&&++mA>V)n("overflow");if(f2==i0){var HB=mA;for(var E1=C;;E1+=C){var t1=E1<=lB?K:E1>=lB+H?H:E1-lB;if(HB<t1)break;var d1=HB-t1,C0=C-t1;Q0.push(h(y1(t1+d1%C0,0))),HB=c(d1/C0)}Q0.push(h(y1(HB,0))),lB=W0(mA,EA,U9==oQ),mA=0,++U9}}}catch(L0){VA=!0,AA=L0}finally{try{if(!ZA&&UA.return)UA.return()}finally{if(VA)throw AA}}++mA,++i0}return Q0.join("")},K1=function Y0(k1){return x(k1,function(Q0){return R.test(Q0)?F0(Q0.slice(4).toLowerCase()):Q0})},G1=function Y0(k1){return x(k1,function(Q0){return T.test(Q0)?"xn--"+g1(Q0):Q0})},L1={version:"2.1.0",ucs2:{decode:e,encode:W1},decode:F0,encode:g1,toASCII:G1,toUnicode:K1},M1={};function a1(Y0){var k1=Y0.charCodeAt(0),Q0=void 0;if(k1<16)Q0="%0"+k1.toString(16).toUpperCase();else if(k1<128)Q0="%"+k1.toString(16).toUpperCase();else if(k1<2048)Q0="%"+(k1>>6|192).toString(16).toUpperCase()+"%"+(k1&63|128).toString(16).toUpperCase();else Q0="%"+(k1>>12|224).toString(16).toUpperCase()+"%"+(k1>>6&63|128).toString(16).toUpperCase()+"%"+(k1&63|128).toString(16).toUpperCase();return Q0}function i1(Y0){var k1="",Q0=0,u0=Y0.length;while(Q0<u0){var i0=parseInt(Y0.substr(Q0+1,2),16);if(i0<128)k1+=String.fromCharCode(i0),Q0+=3;else if(i0>=194&&i0<224){if(u0-Q0>=6){var mA=parseInt(Y0.substr(Q0+4,2),16);k1+=String.fromCharCode((i0&31)<<6|mA&63)}else k1+=Y0.substr(Q0,6);Q0+=6}else if(i0>=224){if(u0-Q0>=9){var lB=parseInt(Y0.substr(Q0+4,2),16),x9=parseInt(Y0.substr(Q0+7,2),16);k1+=String.fromCharCode((i0&15)<<12|(lB&63)<<6|x9&63)}else k1+=Y0.substr(Q0,9);Q0+=9}else k1+=Y0.substr(Q0,3),Q0+=3}return k1}function E0(Y0,k1){function Q0(u0){var i0=i1(u0);return!i0.match(k1.UNRESERVED)?u0:i0}if(Y0.scheme)Y0.scheme=String(Y0.scheme).replace(k1.PCT_ENCODED,Q0).toLowerCase().replace(k1.NOT_SCHEME,"");if(Y0.userinfo!==void 0)Y0.userinfo=String(Y0.userinfo).replace(k1.PCT_ENCODED,Q0).replace(k1.NOT_USERINFO,a1).replace(k1.PCT_ENCODED,Z);if(Y0.host!==void 0)Y0.host=String(Y0.host).replace(k1.PCT_ENCODED,Q0).toLowerCase().replace(k1.NOT_HOST,a1).replace(k1.PCT_ENCODED,Z);if(Y0.path!==void 0)Y0.path=String(Y0.path).replace(k1.PCT_ENCODED,Q0).replace(Y0.scheme?k1.NOT_PATH:k1.NOT_PATH_NOSCHEME,a1).replace(k1.PCT_ENCODED,Z);if(Y0.query!==void 0)Y0.query=String(Y0.query).replace(k1.PCT_ENCODED,Q0).replace(k1.NOT_QUERY,a1).replace(k1.PCT_ENCODED,Z);if(Y0.fragment!==void 0)Y0.fragment=String(Y0.fragment).replace(k1.PCT_ENCODED,Q0).replace(k1.NOT_FRAGMENT,a1).replace(k1.PCT_ENCODED,Z);return Y0}function B1(Y0){return Y0.replace(/^0*(.*)/,"$1")||"0"}function A1(Y0,k1){var Q0=Y0.match(k1.IPV4ADDRESS)||[],u0=J(Q0,2),i0=u0[1];if(i0)return i0.split(".").map(B1).join(".");else return Y0}function I1(Y0,k1){var Q0=Y0.match(k1.IPV6ADDRESS)||[],u0=J(Q0,3),i0=u0[1],mA=u0[2];if(i0){var lB=i0.toLowerCase().split("::").reverse(),x9=J(lB,2),zQ=x9[0],q4=x9[1],xB=q4?q4.split(":").map(B1):[],$Q=zQ.split(":").map(B1),z6=k1.IPV4ADDRESS.test($Q[$Q.length-1]),oQ=z6?7:8,U9=$Q.length-oQ,J4=Array(oQ);for(var _1=0;_1<oQ;++_1)J4[_1]=xB[_1]||$Q[U9+_1]||"";if(z6)J4[oQ-1]=A1(J4[oQ-1],k1);var u1=J4.reduce(function(EA,ZA,VA){if(!ZA||ZA==="0"){var AA=EA[EA.length-1];if(AA&&AA.index+AA.length===VA)AA.length++;else EA.push({index:VA,length:1})}return EA},[]),q0=u1.sort(function(EA,ZA){return ZA.length-EA.length})[0],y0=void 0;if(q0&&q0.length>1){var U0=J4.slice(0,q0.index),v0=J4.slice(q0.index+q0.length);y0=U0.join(":")+"::"+v0.join(":")}else y0=J4.join(":");if(mA)y0+="%"+mA;return y0}else return Y0}var q1=/^(?:([^:\/?#]+):)?(?:\/\/((?:([^\/?#@]*)@)?(\[[^\/?#\]]+\]|[^\/?#:]*)(?:\:(\d*))?))?([^?#]*)(?:\?([^#]*))?(?:#((?:.|\n|\r)*))?/i,P1="".match(/(){0}/)[1]===void 0;function Q1(Y0){var k1=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Q0={},u0=k1.iri!==!1?W:Y;if(k1.reference==="suffix")Y0=(k1.scheme?k1.scheme+":":"")+"//"+Y0;var i0=Y0.match(q1);if(i0){if(P1){if(Q0.scheme=i0[1],Q0.userinfo=i0[3],Q0.host=i0[4],Q0.port=parseInt(i0[5],10),Q0.path=i0[6]||"",Q0.query=i0[7],Q0.fragment=i0[8],isNaN(Q0.port))Q0.port=i0[5]}else if(Q0.scheme=i0[1]||void 0,Q0.userinfo=Y0.indexOf("@")!==-1?i0[3]:void 0,Q0.host=Y0.indexOf("//")!==-1?i0[4]:void 0,Q0.port=parseInt(i0[5],10),Q0.path=i0[6]||"",Q0.query=Y0.indexOf("?")!==-1?i0[7]:void 0,Q0.fragment=Y0.indexOf("#")!==-1?i0[8]:void 0,isNaN(Q0.port))Q0.port=Y0.match(/\/\/(?:.|\n)*\:(?:\/|\?|\#|$)/)?i0[4]:void 0;if(Q0.host)Q0.host=I1(A1(Q0.host,u0),u0);if(Q0.scheme===void 0&&Q0.userinfo===void 0&&Q0.host===void 0&&Q0.port===void 0&&!Q0.path&&Q0.query===void 0)Q0.reference="same-document";else if(Q0.scheme===void 0)Q0.reference="relative";else if(Q0.fragment===void 0)Q0.reference="absolute";else Q0.reference="uri";if(k1.reference&&k1.reference!=="suffix"&&k1.reference!==Q0.reference)Q0.error=Q0.error||"URI is not a "+k1.reference+" reference.";var mA=M1[(k1.scheme||Q0.scheme||"").toLowerCase()];if(!k1.unicodeSupport&&(!mA||!mA.unicodeSupport)){if(Q0.host&&(k1.domainHost||mA&&mA.domainHost))try{Q0.host=L1.toASCII(Q0.host.replace(u0.PCT_ENCODED,i1).toLowerCase())}catch(lB){Q0.error=Q0.error||"Host's domain name can not be converted to ASCII via punycode: "+lB}E0(Q0,Y)}else E0(Q0,u0);if(mA&&mA.parse)mA.parse(Q0,k1)}else Q0.error=Q0.error||"URI can not be parsed.";return Q0}function f1(Y0,k1){var Q0=k1.iri!==!1?W:Y,u0=[];if(Y0.userinfo!==void 0)u0.push(Y0.userinfo),u0.push("@");if(Y0.host!==void 0)u0.push(I1(A1(String(Y0.host),Q0),Q0).replace(Q0.IPV6ADDRESS,function(i0,mA,lB){return"["+mA+(lB?"%25"+lB:"")+"]"}));if(typeof Y0.port==="number"||typeof Y0.port==="string")u0.push(":"),u0.push(String(Y0.port));return u0.length?u0.join(""):void 0}var l1=/^\.\.?\//,n1=/^\/\.(\/|$)/,V0=/^\/\.\.(\/|$)/,I0=/^\/?(?:.|\n)*?(?=\/|$)/;function M0(Y0){var k1=[];while(Y0.length)if(Y0.match(l1))Y0=Y0.replace(l1,"");else if(Y0.match(n1))Y0=Y0.replace(n1,"/");else if(Y0.match(V0))Y0=Y0.replace(V0,"/"),k1.pop();else if(Y0==="."||Y0==="..")Y0="";else{var Q0=Y0.match(I0);if(Q0){var u0=Q0[0];Y0=Y0.slice(u0.length),k1.push(u0)}else throw new Error("Unexpected dot segment condition")}return k1.join("")}function YA(Y0){var k1=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Q0=k1.iri?W:Y,u0=[],i0=M1[(k1.scheme||Y0.scheme||"").toLowerCase()];if(i0&&i0.serialize)i0.serialize(Y0,k1);if(Y0.host){if(Q0.IPV6ADDRESS.test(Y0.host));else if(k1.domainHost||i0&&i0.domainHost)try{Y0.host=!k1.iri?L1.toASCII(Y0.host.replace(Q0.PCT_ENCODED,i1).toLowerCase()):L1.toUnicode(Y0.host)}catch(x9){Y0.error=Y0.error||"Host's domain name can not be converted to "+(!k1.iri?"ASCII":"Unicode")+" via punycode: "+x9}}if(E0(Y0,Q0),k1.reference!=="suffix"&&Y0.scheme)u0.push(Y0.scheme),u0.push(":");var mA=f1(Y0,k1);if(mA!==void 0){if(k1.reference!=="suffix")u0.push("//");if(u0.push(mA),Y0.path&&Y0.path.charAt(0)!=="/")u0.push("/")}if(Y0.path!==void 0){var lB=Y0.path;if(!k1.absolutePath&&(!i0||!i0.absolutePath))lB=M0(lB);if(mA===void 0)lB=lB.replace(/^\/\//,"/%2F");u0.push(lB)}if(Y0.query!==void 0)u0.push("?"),u0.push(Y0.query);if(Y0.fragment!==void 0)u0.push("#"),u0.push(Y0.fragment);return u0.join("")}function m0(Y0,k1){var Q0=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},u0=arguments[3],i0={};if(!u0)Y0=Q1(YA(Y0,Q0),Q0),k1=Q1(YA(k1,Q0),Q0);if(Q0=Q0||{},!Q0.tolerant&&k1.scheme)i0.scheme=k1.scheme,i0.userinfo=k1.userinfo,i0.host=k1.host,i0.port=k1.port,i0.path=M0(k1.path||""),i0.query=k1.query;else{if(k1.userinfo!==void 0||k1.host!==void 0||k1.port!==void 0)i0.userinfo=k1.userinfo,i0.host=k1.host,i0.port=k1.port,i0.path=M0(k1.path||""),i0.query=k1.query;else{if(!k1.path)if(i0.path=Y0.path,k1.query!==void 0)i0.query=k1.query;else i0.query=Y0.query;else{if(k1.path.charAt(0)==="/")i0.path=M0(k1.path);else{if((Y0.userinfo!==void 0||Y0.host!==void 0||Y0.port!==void 0)&&!Y0.path)i0.path="/"+k1.path;else if(!Y0.path)i0.path=k1.path;else i0.path=Y0.path.slice(0,Y0.path.lastIndexOf("/")+1)+k1.path;i0.path=M0(i0.path)}i0.query=k1.query}i0.userinfo=Y0.userinfo,i0.host=Y0.host,i0.port=Y0.port}i0.scheme=Y0.scheme}return i0.fragment=k1.fragment,i0}function SA(Y0,k1,Q0){var u0=F({scheme:"null"},Q0);return YA(m0(Q1(Y0,u0),Q1(k1,u0),u0,!0),u0)}function v2(Y0,k1){if(typeof Y0==="string")Y0=YA(Q1(Y0,k1),k1);else if(D(Y0)==="object")Y0=Q1(YA(Y0,k1),k1);return Y0}function Y2(Y0,k1,Q0){if(typeof Y0==="string")Y0=YA(Q1(Y0,Q0),Q0);else if(D(Y0)==="object")Y0=YA(Y0,Q0);if(typeof k1==="string")k1=YA(Q1(k1,Q0),Q0);else if(D(k1)==="object")k1=YA(k1,Q0);return Y0===k1}function N2(Y0,k1){return Y0&&Y0.toString().replace(!k1||!k1.iri?Y.ESCAPE:W.ESCAPE,a1)}function b2(Y0,k1){return Y0&&Y0.toString().replace(!k1||!k1.iri?Y.PCT_ENCODED:W.PCT_ENCODED,i1)}var _B={scheme:"http",domainHost:!0,parse:function Y0(k1,Q0){if(!k1.host)k1.error=k1.error||"HTTP URIs must have a host.";return k1},serialize:function Y0(k1,Q0){var u0=String(k1.scheme).toLowerCase()==="https";if(k1.port===(u0?443:80)||k1.port==="")k1.port=void 0;if(!k1.path)k1.path="/";return k1}},W4={scheme:"https",domainHost:_B.domainHost,parse:_B.parse,serialize:_B.serialize};function gA(Y0){return typeof Y0.secure==="boolean"?Y0.secure:String(Y0.scheme).toLowerCase()==="wss"}var X2={scheme:"ws",domainHost:!0,parse:function Y0(k1,Q0){var u0=k1;return u0.secure=gA(u0),u0.resourceName=(u0.path||"/")+(u0.query?"?"+u0.query:""),u0.path=void 0,u0.query=void 0,u0},serialize:function Y0(k1,Q0){if(k1.port===(gA(k1)?443:80)||k1.port==="")k1.port=void 0;if(typeof k1.secure==="boolean")k1.scheme=k1.secure?"wss":"ws",k1.secure=void 0;if(k1.resourceName){var u0=k1.resourceName.split("?"),i0=J(u0,2),mA=i0[0],lB=i0[1];k1.path=mA&&mA!=="/"?mA:void 0,k1.query=lB,k1.resourceName=void 0}return k1.fragment=void 0,k1}},L2={scheme:"wss",domainHost:X2.domainHost,parse:X2.parse,serialize:X2.serialize},lA={},uA=!0,r2="[A-Za-z0-9\\-\\.\\_\\~"+(uA?"\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF":"")+"]",gB="[0-9A-Fa-f]",g6=Q(Q("%[EFef]"+gB+"%"+gB+gB+"%"+gB+gB)+"|"+Q("%[89A-Fa-f]"+gB+"%"+gB+gB)+"|"+Q("%"+gB+gB)),k7="[A-Za-z0-9\\!\\$\\%\\'\\*\\+\\-\\^\\_\\`\\{\\|\\}\\~]",O4="[\\!\\$\\%\\'\\(\\)\\*\\+\\,\\-\\.0-9\\<\\>A-Z\\x5E-\\x7E]",GB=B(O4,"[\\\"\\\\]"),T4="[\\!\\$\\'\\(\\)\\*\\+\\,\\;\\:\\@]",d3=new RegExp(r2,"g"),a5=new RegExp(g6,"g"),O8=new RegExp(B("[^]",k7,"[\\.]","[\\\"]",GB),"g"),U5=new RegExp(B("[^]",r2,T4),"g"),s5=U5;function y7(Y0){var k1=i1(Y0);return!k1.match(d3)?Y0:k1}var _7={scheme:"mailto",parse:function Y0(k1,Q0){var u0=k1,i0=u0.to=u0.path?u0.path.split(","):[];if(u0.path=void 0,u0.query){var mA=!1,lB={},x9=u0.query.split("&");for(var zQ=0,q4=x9.length;zQ<q4;++zQ){var xB=x9[zQ].split("=");switch(xB[0]){case"to":var $Q=xB[1].split(",");for(var z6=0,oQ=$Q.length;z6<oQ;++z6)i0.push($Q[z6]);break;case"subject":u0.subject=b2(xB[1],Q0);break;case"body":u0.body=b2(xB[1],Q0);break;default:mA=!0,lB[b2(xB[0],Q0)]=b2(xB[1],Q0);break}}if(mA)u0.headers=lB}u0.query=void 0;for(var U9=0,J4=i0.length;U9<J4;++U9){var _1=i0[U9].split("@");if(_1[0]=b2(_1[0]),!Q0.unicodeSupport)try{_1[1]=L1.toASCII(b2(_1[1],Q0).toLowerCase())}catch(u1){u0.error=u0.error||"Email address's domain name can not be converted to ASCII via punycode: "+u1}else _1[1]=b2(_1[1],Q0).toLowerCase();i0[U9]=_1.join("@")}return u0},serialize:function Y0(k1,Q0){var u0=k1,i0=G(k1.to);if(i0){for(var mA=0,lB=i0.length;mA<lB;++mA){var x9=String(i0[mA]),zQ=x9.lastIndexOf("@"),q4=x9.slice(0,zQ).replace(a5,y7).replace(a5,Z).replace(O8,a1),xB=x9.slice(zQ+1);try{xB=!Q0.iri?L1.toASCII(b2(xB,Q0).toLowerCase()):L1.toUnicode(xB)}catch(U9){u0.error=u0.error||"Email address's domain name can not be converted to "+(!Q0.iri?"ASCII":"Unicode")+" via punycode: "+U9}i0[mA]=q4+"@"+xB}u0.path=i0.join(",")}var $Q=k1.headers=k1.headers||{};if(k1.subject)$Q.subject=k1.subject;if(k1.body)$Q.body=k1.body;var z6=[];for(var oQ in $Q)if($Q[oQ]!==lA[oQ])z6.push(oQ.replace(a5,y7).replace(a5,Z).replace(U5,a1)+"="+$Q[oQ].replace(a5,y7).replace(a5,Z).replace(s5,a1));if(z6.length)u0.query=z6.join("&");return u0}},pA=/^([^\:]+)\:(.*)/,V2={scheme:"urn",parse:function Y0(k1,Q0){var u0=k1.path&&k1.path.match(pA),i0=k1;if(u0){var mA=Q0.scheme||i0.scheme||"urn",lB=u0[1].toLowerCase(),x9=u0[2],zQ=mA+":"+(Q0.nid||lB),q4=M1[zQ];if(i0.nid=lB,i0.nss=x9,i0.path=void 0,q4)i0=q4.parse(i0,Q0)}else i0.error=i0.error||"URN can not be parsed.";return i0},serialize:function Y0(k1,Q0){var u0=Q0.scheme||k1.scheme||"urn",i0=k1.nid,mA=u0+":"+(Q0.nid||i0),lB=M1[mA];if(lB)k1=lB.serialize(k1,Q0);var x9=k1,zQ=k1.nss;return x9.path=(i0||Q0.nid)+":"+zQ,x9}},_9=/^[0-9A-Fa-f]{8}(?:\-[0-9A-Fa-f]{4}){3}\-[0-9A-Fa-f]{12}$/,w5={scheme:"urn:uuid",parse:function Y0(k1,Q0){var u0=k1;if(u0.uuid=u0.nss,u0.nss=void 0,!Q0.tolerant&&(!u0.uuid||!u0.uuid.match(_9)))u0.error=u0.error||"UUID is not valid.";return u0},serialize:function Y0(k1,Q0){var u0=k1;return u0.nss=(k1.uuid||"").toLowerCase(),u0}};M1[_B.scheme]=_B,M1[W4.scheme]=W4,M1[X2.scheme]=X2,M1[L2.scheme]=L2,M1[_7.scheme]=_7,M1[V2.scheme]=V2,M1[w5.scheme]=w5,A.SCHEMES=M1,A.pctEncChar=a1,A.pctDecChars=i1,A.parse=Q1,A.removeDotSegments=M0,A.serialize=YA,A.resolveComponents=m0,A.resolve=SA,A.normalize=v2,A.equal=Y2,A.escapeComponent=N2,A.unescapeComponent=b2,Object.defineProperty(A,"__esModule",{value:!0})})});
var EQB=E((Tp5,zQB)=>{zQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||"");if(B.opts.format===!1){if(J)Z+=" if (true) { ";return Z}var V=B.opts.$data&&I&&I.$data,C;if(V)Z+=" var schema"+G+" = "+B.util.getData(I.$data,F,B.dataPathArr)+"; ",C="schema"+G;else C=I;var K=B.opts.unknownFormats,H=Array.isArray(K);if(V){var z="format"+G,$="isObject"+G,L="formatType"+G;if(Z+=" var "+z+" = formats["+C+"]; var "+$+" = typeof "+z+" == 'object' && !("+z+" instanceof RegExp) && "+z+".validate; var "+L+" = "+$+" && "+z+".type || 'string'; if ("+$+") { ",B.async)Z+=" var async"+G+" = "+z+".async; ";if(Z+=" "+z+" = "+z+".validate; } if (  ",V)Z+=" ("+C+" !== undefined && typeof "+C+" != 'string') || ";if(Z+=" (",K!="ignore"){if(Z+=" ("+C+" && !"+z+" ",H)Z+=" && self._opts.unknownFormats.indexOf("+C+") == -1 ";Z+=") || "}if(Z+=" ("+z+" && "+L+" == '"+D+"' && !(typeof "+z+" == 'function' ? ",B.async)Z+=" (async"+G+" ? await "+z+"("+X+") : "+z+"("+X+")) ";else Z+=" "+z+"("+X+") ";Z+=" : "+z+".test("+X+"))))) {"}else{var z=B.formats[I];if(!z)if(K=="ignore"){if(B.logger.warn('unknown format "'+I+'" ignored in schema at path "'+B.errSchemaPath+'"'),J)Z+=" if (true) { ";return Z}else if(H&&K.indexOf(I)>=0){if(J)Z+=" if (true) { ";return Z}else throw new Error('unknown format "'+I+'" is used in schema at path "'+B.errSchemaPath+'"');var $=typeof z=="object"&&!(z instanceof RegExp)&&z.validate,L=$&&z.type||"string";if($){var N=z.async===!0;z=z.validate}if(L!=D){if(J)Z+=" if (true) { ";return Z}if(N){if(!B.async)throw new Error("async format in sync schema");var O="formats"+B.util.getProperty(I)+".validate";Z+=" if (!(await "+O+"("+X+"))) { "}else{Z+=" if (! ";var O="formats"+B.util.getProperty(I);if($)O+=".validate";if(typeof z=="function")Z+=" "+O+"("+X+") ";else Z+=" "+O+".test("+X+") ";Z+=") { "}}var R=R||[];if(R.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: 'format' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { format:  ",V)Z+=""+C;else Z+=""+B.util.toQuotedString(I);if(Z+="  } ",B.opts.messages!==!1){if(Z+=` , message: 'should match format "`,V)Z+="' + "+C+" + '";else Z+=""+B.util.escapeQuotes(I);Z+=`"' `}if(B.opts.verbose){if(Z+=" , schema:  ",V)Z+="validate.schema"+Y;else Z+=""+B.util.toQuotedString(I);Z+="         , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" "}Z+=" } "}else Z+=" {} ";var T=Z;if(Z=R.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+T+"]); ";else Z+=" validate.errors = ["+T+"]; return false; ";else Z+=" var err = "+T+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(Z+=" } ",J)Z+=" else { ";return Z}});
var F4B=E((rp5,WO6)=>{WO6.exports={$schema:"http://json-schema.org/draft-07/schema#",$id:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#",description:"Meta-schema for $data reference (JSON Schema extension proposal)",type:"object",required:["$data"],properties:{$data:{type:"string",anyOf:[{format:"relative-json-pointer"},{format:"json-pointer"}]}},additionalProperties:!1}});
var G4B=E((sp5,Z4B)=>{var DO6=/^[a-z_$][a-z0-9_$-]*$/i,ZO6=A4B(),GO6=D4B();Z4B.exports={add:FO6,get:IO6,remove:YO6,validate:FC0};function FO6(A,B){var Q=this.RULES;if(Q.keywords[A])throw new Error("Keyword "+A+" is already defined");if(!DO6.test(A))throw new Error("Keyword "+A+" is not a valid identifier");if(B){this.validateKeyword(B,!0);var D=B.type;if(Array.isArray(D))for(var Z=0;Z<D.length;Z++)F(A,D[Z],B);else F(A,D,B);var G=B.metaSchema;if(G){if(B.$data&&this._opts.$data)G={anyOf:[G,{$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"}]};B.validateSchema=this.compile(G,!0)}}Q.keywords[A]=Q.all[A]=!0;function F(I,Y,W){var J;for(var X=0;X<Q.length;X++){var V=Q[X];if(V.type==Y){J=V;break}}if(!J)J={type:Y,rules:[]},Q.push(J);var C={keyword:I,definition:W,custom:!0,code:ZO6,implements:W.implements};J.rules.push(C),Q.custom[I]=C}return this}function IO6(A){var B=this.RULES.custom[A];return B?B.definition:this.RULES.keywords[A]||!1}function YO6(A){var B=this.RULES;delete B.keywords[A],delete B.all[A],delete B.custom[A];for(var Q=0;Q<B.length;Q++){var D=B[Q].rules;for(var Z=0;Z<D.length;Z++)if(D[Z].keyword==A){D.splice(Z,1);break}}return this}function FC0(A,B){FC0.errors=null;var Q=this._validateKeyword=this._validateKeyword||this.compile(GO6,!0);if(Q(A))return!0;if(FC0.errors=Q.errors,B)throw new Error("custom keyword definition is invalid: "+this.errorsText(Q.errors));else return!1}});
var GC0=E((np5,QO6)=>{QO6.exports={$schema:"http://json-schema.org/draft-07/schema#",$id:"http://json-schema.org/draft-07/schema#",title:"Core schema meta-schema",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:!0}});
var GQB=E((qp5,ZQB)=>{ZQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||""),V="valid"+G,C="errs__"+G,K=B.util.copy(B),H="";K.level++;var z="valid"+K.level,$=I.every(function(f){return B.opts.strictKeywords?typeof f=="object"&&Object.keys(f).length>0||f===!1:B.util.schemaHasRules(f,B.RULES.all)});if($){var L=K.baseId;Z+=" var "+C+" = errors; var "+V+" = false;  ";var N=B.compositeRule;B.compositeRule=K.compositeRule=!0;var O=I;if(O){var R,T=-1,j=O.length-1;while(T<j)R=O[T+=1],K.schema=R,K.schemaPath=Y+"["+T+"]",K.errSchemaPath=W+"/"+T,Z+="  "+B.validate(K)+" ",K.baseId=L,Z+=" "+V+" = "+V+" || "+z+"; if (!"+V+") { ",H+="}"}if(B.compositeRule=K.compositeRule=N,Z+=" "+H+" if (!"+V+") {   var err =   ",B.createErrors!==!1){if(Z+=" { keyword: 'anyOf' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: {} ",B.opts.messages!==!1)Z+=" , message: 'should match some schema in anyOf' ";if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";if(Z+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(vErrors); ";else Z+=" validate.errors = vErrors; return false; ";if(Z+=" } else {  errors = "+C+"; if (vErrors !== null) { if ("+C+") vErrors.length = "+C+"; else vErrors = null; } ",B.opts.allErrors)Z+=" } "}else if(J)Z+=" if (true) { ";return Z}});
var HQB=E((Op5,KQB)=>{KQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||""),V="valid"+G,C=B.opts.$data&&I&&I.$data,K;if(C)Z+=" var schema"+G+" = "+B.util.getData(I.$data,F,B.dataPathArr)+"; ",K="schema"+G;else K=I;var H="i"+G,z="schema"+G;if(!C)Z+=" var "+z+" = validate.schema"+Y+";";if(Z+="var "+V+";",C)Z+=" if (schema"+G+" === undefined) "+V+" = true; else if (!Array.isArray(schema"+G+")) "+V+" = false; else {";if(Z+=""+V+" = false;for (var "+H+"=0; "+H+"<"+z+".length; "+H+"++) if (equal("+X+", "+z+"["+H+"])) { "+V+" = true; break; }",C)Z+="  }  ";Z+=" if (!"+V+") {   ";var $=$||[];if($.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: 'enum' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { allowedValues: schema"+G+" } ",B.opts.messages!==!1)Z+=" , message: 'should be equal to one of the allowed values' ";if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";var L=Z;if(Z=$.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+L+"]); ";else Z+=" validate.errors = ["+L+"]; return false; ";else Z+=" var err = "+L+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(Z+=" }",J)Z+=" else { ";return Z}});
var IQB=E((Np5,FQB)=>{FQB.exports=function A(B,Q,D){var Z=" ",G=B.schema[Q],F=B.errSchemaPath+"/"+Q,I=!B.opts.allErrors,Y=B.util.toQuotedString(G);if(B.opts.$comment===!0)Z+=" console.log("+Y+");";else if(typeof B.opts.$comment=="function")Z+=" self._opts.$comment("+Y+", "+B.util.toQuotedString(F)+", validate.root.schema);";return Z}});
var O9B=E((Xp5,R9B)=>{var Nx=R9B.exports=function(A,B,Q){if(typeof B=="function")Q=B,B={};Q=B.cb||Q;var D=typeof Q=="function"?Q:Q.pre||function(){},Z=Q.post||function(){};Kj1(B,D,Z,A,"",A)};Nx.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0};Nx.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0};Nx.propsKeywords={definitions:!0,properties:!0,patternProperties:!0,dependencies:!0};Nx.skipKeywords={default:!0,enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0};function Kj1(A,B,Q,D,Z,G,F,I,Y,W){if(D&&typeof D=="object"&&!Array.isArray(D)){B(D,Z,G,F,I,Y,W);for(var J in D){var X=D[J];if(Array.isArray(X)){if(J in Nx.arrayKeywords)for(var V=0;V<X.length;V++)Kj1(A,B,Q,X[V],Z+"/"+J+"/"+V,G,Z,J,D,V)}else if(J in Nx.propsKeywords){if(X&&typeof X=="object")for(var C in X)Kj1(A,B,Q,X[C],Z+"/"+J+"/"+RR6(C),G,Z,J,D,C)}else if(J in Nx.keywords||A.allKeys&&!(J in Nx.skipKeywords))Kj1(A,B,Q,X,Z+"/"+J,G,Z,J,D)}Q(D,Z,G,F,I,Y,W)}}function RR6(A){return A.replace(/~/g,"~0").replace(/\//g,"~1")}});
var QC0=E((yp5,MQB)=>{MQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,H,X="data"+(F||""),V=B.opts.$data&&I&&I.$data,C;if(V)Z+=" var schema"+G+" = "+B.util.getData(I.$data,F,B.dataPathArr)+"; ",C="schema"+G;else C=I;if(!(V||typeof I=="number"))throw new Error(Q+" must be number");var K=Q=="maxLength"?">":"<";if(Z+="if ( ",V)Z+=" ("+C+" !== undefined && typeof "+C+" != 'number') || ";if(B.opts.unicode===!1)Z+=" "+X+".length ";else Z+=" ucs2length("+X+") ";Z+=" "+K+" "+C+") { ";var H=Q,z=z||[];if(z.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: '"+(H||"_limitLength")+"' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { limit: "+C+" } ",B.opts.messages!==!1){if(Z+=" , message: 'should NOT be ",Q=="maxLength")Z+="longer";else Z+="shorter";if(Z+=" than ",V)Z+="' + "+C+" + '";else Z+=""+I;Z+=" characters' "}if(B.opts.verbose){if(Z+=" , schema:  ",V)Z+="validate.schema"+Y;else Z+=""+I;Z+="         , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" "}Z+=" } "}else Z+=" {} ";var $=Z;if(Z=z.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+$+"]); ";else Z+=" validate.errors = ["+$+"]; return false; ";else Z+=" var err = "+$+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(Z+="} ",J)Z+=" else { ";return Z}});
var SQB=E((vp5,PQB)=>{PQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||""),V="errs__"+G,C=B.util.copy(B);C.level++;var K="valid"+C.level;if(B.opts.strictKeywords?typeof I=="object"&&Object.keys(I).length>0||I===!1:B.util.schemaHasRules(I,B.RULES.all)){C.schema=I,C.schemaPath=Y,C.errSchemaPath=W,Z+=" var "+V+" = errors;  ";var H=B.compositeRule;B.compositeRule=C.compositeRule=!0,C.createErrors=!1;var z;if(C.opts.allErrors)z=C.opts.allErrors,C.opts.allErrors=!1;if(Z+=" "+B.validate(C)+" ",C.createErrors=!0,z)C.opts.allErrors=z;B.compositeRule=C.compositeRule=H,Z+=" if ("+K+") {   ";var $=$||[];if($.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: 'not' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: {} ",B.opts.messages!==!1)Z+=" , message: 'should NOT be valid' ";if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";var L=Z;if(Z=$.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+L+"]); ";else Z+=" validate.errors = ["+L+"]; return false; ";else Z+=" var err = "+L+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(Z+=" } else {  errors = "+V+"; if (vErrors !== null) { if ("+V+") vErrors.length = "+V+"; else vErrors = null; } ",B.opts.allErrors)Z+=" } "}else{if(Z+="  var err =   ",B.createErrors!==!1){if(Z+=" { keyword: 'not' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: {} ",B.opts.messages!==!1)Z+=" , message: 'should NOT be valid' ";if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";if(Z+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",J)Z+=" if (false) { "}return Z}});
var TQB=E((xp5,OQB)=>{OQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||""),V=B.opts.$data&&I&&I.$data,C;if(V)Z+=" var schema"+G+" = "+B.util.getData(I.$data,F,B.dataPathArr)+"; ",C="schema"+G;else C=I;if(!(V||typeof I=="number"))throw new Error(Q+" must be number");if(Z+="var division"+G+";if (",V)Z+=" "+C+" !== undefined && ( typeof "+C+" != 'number' || ";if(Z+=" (division"+G+" = "+X+" / "+C+", ",B.opts.multipleOfPrecision)Z+=" Math.abs(Math.round(division"+G+") - division"+G+") > 1e-"+B.opts.multipleOfPrecision+" ";else Z+=" division"+G+" !== parseInt(division"+G+") ";if(Z+=" ) ",V)Z+="  )  ";Z+=" ) {   ";var K=K||[];if(K.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: 'multipleOf' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { multipleOf: "+C+" } ",B.opts.messages!==!1)if(Z+=" , message: 'should be multiple of ",V)Z+="' + "+C;else Z+=""+C+"'";if(B.opts.verbose){if(Z+=" , schema:  ",V)Z+="validate.schema"+Y;else Z+=""+I;Z+="         , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" "}Z+=" } "}else Z+=" {} ";var H=Z;if(Z=K.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+H+"]); ";else Z+=" validate.errors = ["+H+"]; return false; ";else Z+=" var err = "+H+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(Z+="} ",J)Z+=" else { ";return Z}});
var WQB=E((Lp5,YQB)=>{YQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||""),V="valid"+G,C=B.opts.$data&&I&&I.$data,K;if(C)Z+=" var schema"+G+" = "+B.util.getData(I.$data,F,B.dataPathArr)+"; ",K="schema"+G;else K=I;if(!C)Z+=" var schema"+G+" = validate.schema"+Y+";";Z+="var "+V+" = equal("+X+", schema"+G+"); if (!"+V+") {   ";var H=H||[];if(H.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: 'const' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { allowedValue: schema"+G+" } ",B.opts.messages!==!1)Z+=" , message: 'should be equal to constant' ";if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";var z=Z;if(Z=H.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+z+"]); ";else Z+=" validate.errors = ["+z+"]; return false; ";else Z+=" var err = "+z+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(Z+=" }",J)Z+=" else { ";return Z}});
var XQB=E((Mp5,JQB)=>{JQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||""),V="valid"+G,C="errs__"+G,K=B.util.copy(B),H="";K.level++;var z="valid"+K.level,$="i"+G,L=K.dataLevel=B.dataLevel+1,N="data"+L,O=B.baseId,R=B.opts.strictKeywords?typeof I=="object"&&Object.keys(I).length>0||I===!1:B.util.schemaHasRules(I,B.RULES.all);if(Z+="var "+C+" = errors;var "+V+";",R){var T=B.compositeRule;B.compositeRule=K.compositeRule=!0,K.schema=I,K.schemaPath=Y,K.errSchemaPath=W,Z+=" var "+z+" = false; for (var "+$+" = 0; "+$+" < "+X+".length; "+$+"++) { ",K.errorPath=B.util.getPathExpr(B.errorPath,$,B.opts.jsonPointers,!0);var j=X+"["+$+"]";K.dataPathArr[L]=$;var f=B.validate(K);if(K.baseId=O,B.util.varOccurences(f,N)<2)Z+=" "+B.util.varReplace(f,N,j)+" ";else Z+=" var "+N+" = "+j+"; "+f+" ";Z+=" if ("+z+") break; }  ",B.compositeRule=K.compositeRule=T,Z+=" "+H+" if (!"+z+") {"}else Z+=" if ("+X+".length == 0) {";var k=k||[];if(k.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: 'contains' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: {} ",B.opts.messages!==!1)Z+=" , message: 'should contain a valid item' ";if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";var c=Z;if(Z=k.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+c+"]); ";else Z+=" validate.errors = ["+c+"]; return false; ";else Z+=" var err = "+c+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(Z+=" } else { ",R)Z+="  errors = "+C+"; if (vErrors !== null) { if ("+C+") vErrors.length = "+C+"; else vErrors = null; } ";if(B.opts.allErrors)Z+=" } ";return Z}});
var YC0=E((op5,H4B)=>{var Y4B=g9B(),Gm=$j1(),JO6=m9B(),W4B=pV0(),XO6=oV0(),VO6=e9B(),CO6=iQB(),J4B=sQB(),X4B=Zm();H4B.exports=YZ;YZ.prototype.validate=HO6;YZ.prototype.compile=zO6;YZ.prototype.addSchema=EO6;YZ.prototype.addMetaSchema=UO6;YZ.prototype.validateSchema=wO6;YZ.prototype.getSchema=qO6;YZ.prototype.removeSchema=LO6;YZ.prototype.addFormat=kO6;YZ.prototype.errorsText=jO6;YZ.prototype._addSchema=MO6;YZ.prototype._compile=RO6;YZ.prototype.compileAsync=tQB();var Sj1=G4B();YZ.prototype.addKeyword=Sj1.add;YZ.prototype.getKeyword=Sj1.get;YZ.prototype.removeKeyword=Sj1.remove;YZ.prototype.validateKeyword=Sj1.validate;var V4B=qj1();YZ.ValidationError=V4B.Validation;YZ.MissingRefError=V4B.MissingRef;YZ.$dataMetaSchema=J4B;var Pj1="http://json-schema.org/draft-07/schema",I4B=["removeAdditional","useDefaults","coerceTypes","strictDefaults"],KO6=["/properties"];function YZ(A){if(!(this instanceof YZ))return new YZ(A);if(A=this._opts=X4B.copy(A)||{},fO6(this),this._schemas={},this._refs={},this._fragments={},this._formats=VO6(A.format),this._cache=A.cache||new JO6,this._loadingSchemas={},this._compilations=[],this.RULES=CO6(),this._getId=OO6(A),A.loopRequired=A.loopRequired||1/0,A.errorDataPath=="property")A._errorDataPathProperty=!0;if(A.serialize===void 0)A.serialize=XO6;if(this._metaOpts=bO6(this),A.formats)xO6(this);if(A.keywords)vO6(this);if(yO6(this),typeof A.meta=="object")this.addMetaSchema(A.meta);if(A.nullable)this.addKeyword("nullable",{metaSchema:{type:"boolean"}});_O6(this)}function HO6(A,B){var Q;if(typeof A=="string"){if(Q=this.getSchema(A),!Q)throw new Error('no schema with key or ref "'+A+'"')}else{var D=this._addSchema(A);Q=D.validate||this._compile(D)}var Z=Q(B);if(Q.$async!==!0)this.errors=Q.errors;return Z}function zO6(A,B){var Q=this._addSchema(A,void 0,B);return Q.validate||this._compile(Q)}function EO6(A,B,Q,D){if(Array.isArray(A)){for(var Z=0;Z<A.length;Z++)this.addSchema(A[Z],void 0,Q,D);return this}var G=this._getId(A);if(G!==void 0&&typeof G!="string")throw new Error("schema id must be string");return B=Gm.normalizeId(B||G),K4B(this,B),this._schemas[B]=this._addSchema(A,Q,D,!0),this}function UO6(A,B,Q){return this.addSchema(A,B,Q,!0),this}function wO6(A,B){var Q=A.$schema;if(Q!==void 0&&typeof Q!="string")throw new Error("$schema must be a string");if(Q=Q||this._opts.defaultMeta||$O6(this),!Q)return this.logger.warn("meta-schema not available"),this.errors=null,!0;var D=this.validate(Q,A);if(!D&&B){var Z="schema is invalid: "+this.errorsText();if(this._opts.validateSchema=="log")this.logger.error(Z);else throw new Error(Z)}return D}function $O6(A){var B=A._opts.meta;return A._opts.defaultMeta=typeof B=="object"?A._getId(B)||B:A.getSchema(Pj1)?Pj1:void 0,A._opts.defaultMeta}function qO6(A){var B=C4B(this,A);switch(typeof B){case"object":return B.validate||this._compile(B);case"string":return this.getSchema(B);case"undefined":return NO6(this,A)}}function NO6(A,B){var Q=Gm.schema.call(A,{schema:{}},B);if(Q){var{schema:D,root:Z,baseId:G}=Q,F=Y4B.call(A,D,Z,void 0,G);return A._fragments[B]=new W4B({ref:B,fragment:!0,schema:D,root:Z,baseId:G,validate:F}),F}}function C4B(A,B){return B=Gm.normalizeId(B),A._schemas[B]||A._refs[B]||A._fragments[B]}function LO6(A){if(A instanceof RegExp)return Tj1(this,this._schemas,A),Tj1(this,this._refs,A),this;switch(typeof A){case"undefined":return Tj1(this,this._schemas),Tj1(this,this._refs),this._cache.clear(),this;case"string":var B=C4B(this,A);if(B)this._cache.del(B.cacheKey);return delete this._schemas[A],delete this._refs[A],this;case"object":var Q=this._opts.serialize,D=Q?Q(A):A;this._cache.del(D);var Z=this._getId(A);if(Z)Z=Gm.normalizeId(Z),delete this._schemas[Z],delete this._refs[Z]}return this}function Tj1(A,B,Q){for(var D in B){var Z=B[D];if(!Z.meta&&(!Q||Q.test(D)))A._cache.del(Z.cacheKey),delete B[D]}}function MO6(A,B,Q,D){if(typeof A!="object"&&typeof A!="boolean")throw new Error("schema should be object or boolean");var Z=this._opts.serialize,G=Z?Z(A):A,F=this._cache.get(G);if(F)return F;D=D||this._opts.addUsedSchema!==!1;var I=Gm.normalizeId(this._getId(A));if(I&&D)K4B(this,I);var Y=this._opts.validateSchema!==!1&&!B,W;if(Y&&!(W=I&&I==Gm.normalizeId(A.$schema)))this.validateSchema(A,!0);var J=Gm.ids.call(this,A),X=new W4B({id:I,schema:A,localRefs:J,cacheKey:G,meta:Q});if(I[0]!="#"&&D)this._refs[I]=X;if(this._cache.put(G,X),Y&&W)this.validateSchema(A,!0);return X}function RO6(A,B){if(A.compiling){if(A.validate=Z,Z.schema=A.schema,Z.errors=null,Z.root=B?B:Z,A.schema.$async===!0)Z.$async=!0;return Z}A.compiling=!0;var Q;if(A.meta)Q=this._opts,this._opts=this._metaOpts;var D;try{D=Y4B.call(this,A.schema,B,A.localRefs)}catch(G){throw delete A.validate,G}finally{if(A.compiling=!1,A.meta)this._opts=Q}return A.validate=D,A.refs=D.refs,A.refVal=D.refVal,A.root=D.root,D;function Z(){var G=A.validate,F=G.apply(this,arguments);return Z.errors=G.errors,F}}function OO6(A){switch(A.schemaId){case"auto":return SO6;case"id":return TO6;default:return PO6}}function TO6(A){if(A.$id)this.logger.warn("schema $id ignored",A.$id);return A.id}function PO6(A){if(A.id)this.logger.warn("schema id ignored",A.id);return A.$id}function SO6(A){if(A.$id&&A.id&&A.$id!=A.id)throw new Error("schema $id is different from id");return A.$id||A.id}function jO6(A,B){if(A=A||this.errors,!A)return"No errors";B=B||{};var Q=B.separator===void 0?", ":B.separator,D=B.dataVar===void 0?"data":B.dataVar,Z="";for(var G=0;G<A.length;G++){var F=A[G];if(F)Z+=D+F.dataPath+" "+F.message+Q}return Z.slice(0,-Q.length)}function kO6(A,B){if(typeof B=="string")B=new RegExp(B);return this._formats[A]=B,this}function yO6(A){var B;if(A._opts.$data)B=F4B(),A.addMetaSchema(B,B.$id,!0);if(A._opts.meta===!1)return;var Q=GC0();if(A._opts.$data)Q=J4B(Q,KO6);A.addMetaSchema(Q,Pj1,!0),A._refs["http://json-schema.org/schema"]=Pj1}function _O6(A){var B=A._opts.schemas;if(!B)return;if(Array.isArray(B))A.addSchema(B);else for(var Q in B)A.addSchema(B[Q],Q)}function xO6(A){for(var B in A._opts.formats){var Q=A._opts.formats[B];A.addFormat(B,Q)}}function vO6(A){for(var B in A._opts.keywords){var Q=A._opts.keywords[B];A.addKeyword(B,Q)}}function K4B(A,B){if(A._schemas[B]||A._refs[B])throw new Error('schema with key or id "'+B+'" already exists')}function bO6(A){var B=X4B.copy(A._opts);for(var Q=0;Q<I4B.length;Q++)delete B[I4B[Q]];return B}function fO6(A){var B=A._opts.logger;if(B===!1)A.logger={log:IC0,warn:IC0,error:IC0};else{if(B===void 0)B=console;if(!(typeof B=="object"&&B.log&&B.warn&&B.error))throw new Error("logger must implement log, warn and error methods");A.logger=B}}function IC0(){}});
var Zm=E((Wp5,L9B)=>{L9B.exports={copy:FR6,checkDataType:hV0,checkDataTypes:IR6,coerceToTypes:YR6,toHash:uV0,getProperty:mV0,escapeQuotes:dV0,equal:Cj1(),ucs2length:$9B(),varOccurences:XR6,varReplace:VR6,schemaHasRules:CR6,schemaHasRulesExcept:KR6,schemaUnknownRules:HR6,toQuotedString:gV0,getPathExpr:zR6,getPath:ER6,getData:$R6,unescapeFragment:qR6,unescapeJsonPointer:lV0,escapeFragment:NR6,escapeJsonPointer:cV0};function FR6(A,B){B=B||{};for(var Q in A)B[Q]=A[Q];return B}function hV0(A,B,Q,D){var Z=D?" !== ":" === ",G=D?" || ":" && ",F=D?"!":"",I=D?"":"!";switch(A){case"null":return B+Z+"null";case"array":return F+"Array.isArray("+B+")";case"object":return"("+F+B+G+"typeof "+B+Z+'"object"'+G+I+"Array.isArray("+B+"))";case"integer":return"(typeof "+B+Z+'"number"'+G+I+"("+B+" % 1)"+G+B+Z+B+(Q?G+F+"isFinite("+B+")":"")+")";case"number":return"(typeof "+B+Z+'"'+A+'"'+(Q?G+F+"isFinite("+B+")":"")+")";default:return"typeof "+B+Z+'"'+A+'"'}}function IR6(A,B,Q){switch(A.length){case 1:return hV0(A[0],B,Q,!0);default:var D="",Z=uV0(A);if(Z.array&&Z.object)D=Z.null?"(":"(!"+B+" || ",D+="typeof "+B+' !== "object")',delete Z.null,delete Z.array,delete Z.object;if(Z.number)delete Z.integer;for(var G in Z)D+=(D?" && ":"")+hV0(G,B,Q,!0);return D}}var q9B=uV0(["string","number","integer","boolean","null"]);function YR6(A,B){if(Array.isArray(B)){var Q=[];for(var D=0;D<B.length;D++){var Z=B[D];if(q9B[Z])Q[Q.length]=Z;else if(A==="array"&&Z==="array")Q[Q.length]=Z}if(Q.length)return Q}else if(q9B[B])return[B];else if(A==="array"&&B==="array")return["array"]}function uV0(A){var B={};for(var Q=0;Q<A.length;Q++)B[A[Q]]=!0;return B}var WR6=/^[a-z$_][a-z$_0-9]*$/i,JR6=/'|\\/g;function mV0(A){return typeof A=="number"?"["+A+"]":WR6.test(A)?"."+A:"['"+dV0(A)+"']"}function dV0(A){return A.replace(JR6,"\\$&").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\f/g,"\\f").replace(/\t/g,"\\t")}function XR6(A,B){B+="[^0-9]";var Q=A.match(new RegExp(B,"g"));return Q?Q.length:0}function VR6(A,B,Q){return B+="([^0-9])",Q=Q.replace(/\$/g,"$$$$"),A.replace(new RegExp(B,"g"),Q+"$1")}function CR6(A,B){if(typeof A=="boolean")return!A;for(var Q in A)if(B[Q])return!0}function KR6(A,B,Q){if(typeof A=="boolean")return!A&&Q!="not";for(var D in A)if(D!=Q&&B[D])return!0}function HR6(A,B){if(typeof A=="boolean")return;for(var Q in A)if(!B[Q])return Q}function gV0(A){return"'"+dV0(A)+"'"}function zR6(A,B,Q,D){var Z=Q?"'/' + "+B+(D?"":".replace(/~/g, '~0').replace(/\\//g, '~1')"):D?"'[' + "+B+" + ']'":"'[\\'' + "+B+" + '\\']'";return N9B(A,Z)}function ER6(A,B,Q){var D=Q?gV0("/"+cV0(B)):gV0(mV0(B));return N9B(A,D)}var UR6=/^\/(?:[^~]|~0|~1)*$/,wR6=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function $R6(A,B,Q){var D,Z,G,F;if(A==="")return"rootData";if(A[0]=="/"){if(!UR6.test(A))throw new Error("Invalid JSON-pointer: "+A);Z=A,G="rootData"}else{if(F=A.match(wR6),!F)throw new Error("Invalid JSON-pointer: "+A);if(D=+F[1],Z=F[2],Z=="#"){if(D>=B)throw new Error("Cannot access property/index "+D+" levels up, current level is "+B);return Q[B-D]}if(D>B)throw new Error("Cannot access data "+D+" levels up, current level is "+B);if(G="data"+(B-D||""),!Z)return G}var I=G,Y=Z.split("/");for(var W=0;W<Y.length;W++){var J=Y[W];if(J)G+=mV0(lV0(J)),I+=" && "+G}return I}function N9B(A,B){if(A=='""')return B;return(A+" + "+B).replace(/([^\\])' \+ '/g,"$1")}function qR6(A){return lV0(decodeURIComponent(A))}function NR6(A){return encodeURIComponent(cV0(A))}function cV0(A){return A.replace(/~/g,"~0").replace(/\//g,"~1")}function lV0(A){return A.replace(/~1/g,"/").replace(/~0/g,"~")}});
var _QB=E((fp5,yQB)=>{yQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||""),V=B.opts.$data&&I&&I.$data,C;if(V)Z+=" var schema"+G+" = "+B.util.getData(I.$data,F,B.dataPathArr)+"; ",C="schema"+G;else C=I;var K=V?"(new RegExp("+C+"))":B.usePattern(I);if(Z+="if ( ",V)Z+=" ("+C+" !== undefined && typeof "+C+" != 'string') || ";Z+=" !"+K+".test("+X+") ) {   ";var H=H||[];if(H.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: 'pattern' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { pattern:  ",V)Z+=""+C;else Z+=""+B.util.toQuotedString(I);if(Z+="  } ",B.opts.messages!==!1){if(Z+=` , message: 'should match pattern "`,V)Z+="' + "+C+" + '";else Z+=""+B.util.escapeQuotes(I);Z+=`"' `}if(B.opts.verbose){if(Z+=" , schema:  ",V)Z+="validate.schema"+Y;else Z+=""+B.util.toQuotedString(I);Z+="         , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" "}Z+=" } "}else Z+=" {} ";var z=Z;if(Z=H.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+z+"]); ";else Z+=" validate.errors = ["+z+"]; return false; ";else Z+=" var err = "+z+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(Z+="} ",J)Z+=" else { ";return Z}});
var cQB=E((dp5,dQB)=>{dQB.exports={$ref:BQB(),allOf:DQB(),anyOf:GQB(),$comment:IQB(),const:WQB(),contains:XQB(),dependencies:CQB(),enum:HQB(),format:EQB(),if:wQB(),items:qQB(),maximum:AC0(),minimum:AC0(),maxItems:BC0(),minItems:BC0(),maxLength:QC0(),minLength:QC0(),maxProperties:DC0(),minProperties:DC0(),multipleOf:TQB(),not:SQB(),oneOf:kQB(),pattern:_QB(),properties:vQB(),propertyNames:fQB(),required:gQB(),uniqueItems:mQB(),validate:tV0()}});
var e9B=E((Up5,t9B)=>{var cR6=Zm(),lR6=/^(\d\d\d\d)-(\d\d)-(\d\d)$/,pR6=[0,31,28,31,30,31,30,31,31,30,31,30,31],iR6=/^(\d\d):(\d\d):(\d\d)(\.\d+)?(z|[+-]\d\d(?::?\d\d)?)?$/i,d9B=/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i,nR6=/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\?(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,aR6=/^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,c9B=/^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?)*\})*$/i,l9B=/^(?:(?:http[s\u017F]?|ftp):\/\/)(?:(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+(?::(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*)?@)?(?:(?!10(?:\.[0-9]{1,3}){3})(?!127(?:\.[0-9]{1,3}){3})(?!169\.254(?:\.[0-9]{1,3}){2})(?!192\.168(?:\.[0-9]{1,3}){2})(?!172\.(?:1[6-9]|2[0-9]|3[01])(?:\.[0-9]{1,3}){2})(?:[1-9][0-9]?|1[0-9][0-9]|2[01][0-9]|22[0-3])(?:\.(?:1?[0-9]{1,2}|2[0-4][0-9]|25[0-5])){2}(?:\.(?:[1-9][0-9]?|1[0-9][0-9]|2[0-4][0-9]|25[0-4]))|(?:(?:(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+-)*(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+)(?:\.(?:(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+-)*(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+)*(?:\.(?:(?:[a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]){2,})))(?::[0-9]{2,5})?(?:\/(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*)?$/i,p9B=/^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i,i9B=/^(?:\/(?:[^~/]|~0|~1)*)*$/,n9B=/^#(?:\/(?:[a-z0-9_\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i,a9B=/^(?:0|[1-9][0-9]*)(?:#|(?:\/(?:[^~/]|~0|~1)*)*)$/;t9B.exports=Oj1;function Oj1(A){return A=A=="full"?"full":"fast",cR6.copy(Oj1[A])}Oj1.fast={date:/^\d\d\d\d-[0-1]\d-[0-3]\d$/,time:/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)?$/i,"date-time":/^\d\d\d\d-[0-1]\d-[0-3]\d[t\s](?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i,uri:/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/)?[^\s]*$/i,"uri-reference":/^(?:(?:[a-z][a-z0-9+\-.]*:)?\/?\/)?(?:[^\\\s#][^\s#]*)?(?:#[^\\\s]*)?$/i,"uri-template":c9B,url:l9B,email:/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i,hostname:d9B,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^\s*(?:(?:(?:[0-9a-f]{1,4}:){7}(?:[0-9a-f]{1,4}|:))|(?:(?:[0-9a-f]{1,4}:){6}(?::[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){5}(?:(?:(?::[0-9a-f]{1,4}){1,2})|:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){4}(?:(?:(?::[0-9a-f]{1,4}){1,3})|(?:(?::[0-9a-f]{1,4})?:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){3}(?:(?:(?::[0-9a-f]{1,4}){1,4})|(?:(?::[0-9a-f]{1,4}){0,2}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){2}(?:(?:(?::[0-9a-f]{1,4}){1,5})|(?:(?::[0-9a-f]{1,4}){0,3}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){1}(?:(?:(?::[0-9a-f]{1,4}){1,6})|(?:(?::[0-9a-f]{1,4}){0,4}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?::(?:(?:(?::[0-9a-f]{1,4}){1,7})|(?:(?::[0-9a-f]{1,4}){0,5}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(?:%.+)?\s*$/i,regex:o9B,uuid:p9B,"json-pointer":i9B,"json-pointer-uri-fragment":n9B,"relative-json-pointer":a9B};Oj1.full={date:s9B,time:r9B,"date-time":oR6,uri:eR6,"uri-reference":aR6,"uri-template":c9B,url:l9B,email:/^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i,hostname:d9B,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^\s*(?:(?:(?:[0-9a-f]{1,4}:){7}(?:[0-9a-f]{1,4}|:))|(?:(?:[0-9a-f]{1,4}:){6}(?::[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){5}(?:(?:(?::[0-9a-f]{1,4}){1,2})|:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){4}(?:(?:(?::[0-9a-f]{1,4}){1,3})|(?:(?::[0-9a-f]{1,4})?:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){3}(?:(?:(?::[0-9a-f]{1,4}){1,4})|(?:(?::[0-9a-f]{1,4}){0,2}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){2}(?:(?:(?::[0-9a-f]{1,4}){1,5})|(?:(?::[0-9a-f]{1,4}){0,3}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){1}(?:(?:(?::[0-9a-f]{1,4}){1,6})|(?:(?::[0-9a-f]{1,4}){0,4}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?::(?:(?:(?::[0-9a-f]{1,4}){1,7})|(?:(?::[0-9a-f]{1,4}){0,5}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(?:%.+)?\s*$/i,regex:o9B,uuid:p9B,"json-pointer":i9B,"json-pointer-uri-fragment":n9B,"relative-json-pointer":a9B};function sR6(A){return A%4===0&&(A%100!==0||A%400===0)}function s9B(A){var B=A.match(lR6);if(!B)return!1;var Q=+B[1],D=+B[2],Z=+B[3];return D>=1&&D<=12&&Z>=1&&Z<=(D==2&&sR6(Q)?29:pR6[D])}function r9B(A,B){var Q=A.match(iR6);if(!Q)return!1;var D=Q[1],Z=Q[2],G=Q[3],F=Q[5];return(D<=23&&Z<=59&&G<=59||D==23&&Z==59&&G==60)&&(!B||F)}var rR6=/t|\s/i;function oR6(A){var B=A.split(rR6);return B.length==2&&s9B(B[0])&&r9B(B[1],!0)}var tR6=/\/|:/;function eR6(A){return tR6.test(A)&&nR6.test(A)}var AO6=/[^\\]\\Z/;function o9B(A){if(AO6.test(A))return!1;try{return new RegExp(A),!0}catch(B){return!1}}});
var fQB=E((gp5,bQB)=>{bQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||""),V="errs__"+G,C=B.util.copy(B),K="";C.level++;var H="valid"+C.level;if(Z+="var "+V+" = errors;",B.opts.strictKeywords?typeof I=="object"&&Object.keys(I).length>0||I===!1:B.util.schemaHasRules(I,B.RULES.all)){C.schema=I,C.schemaPath=Y,C.errSchemaPath=W;var z="key"+G,$="idx"+G,L="i"+G,N="' + "+z+" + '",O=C.dataLevel=B.dataLevel+1,R="data"+O,T="dataProperties"+G,j=B.opts.ownProperties,f=B.baseId;if(j)Z+=" var "+T+" = undefined; ";if(j)Z+=" "+T+" = "+T+" || Object.keys("+X+"); for (var "+$+"=0; "+$+"<"+T+".length; "+$+"++) { var "+z+" = "+T+"["+$+"]; ";else Z+=" for (var "+z+" in "+X+") { ";Z+=" var startErrs"+G+" = errors; ";var k=z,c=B.compositeRule;B.compositeRule=C.compositeRule=!0;var h=B.validate(C);if(C.baseId=f,B.util.varOccurences(h,R)<2)Z+=" "+B.util.varReplace(h,R,k)+" ";else Z+=" var "+R+" = "+k+"; "+h+" ";if(B.compositeRule=C.compositeRule=c,Z+=" if (!"+H+") { for (var "+L+"=startErrs"+G+"; "+L+"<errors; "+L+"++) { vErrors["+L+"].propertyName = "+z+"; }   var err =   ",B.createErrors!==!1){if(Z+=" { keyword: 'propertyNames' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { propertyName: '"+N+"' } ",B.opts.messages!==!1)Z+=" , message: 'property name \\'"+N+"\\' is invalid' ";if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";if(Z+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(vErrors); ";else Z+=" validate.errors = vErrors; return false; ";if(J)Z+=" break; ";Z+=" } }"}if(J)Z+=" "+K+" if ("+V+" == errors) {";return Z}});
var g9B=E((zp5,h9B)=>{var Nj1=$j1(),Mj1=Zm(),b9B=qj1(),_R6=oV0(),v9B=tV0(),xR6=Mj1.ucs2length,vR6=Cj1(),bR6=b9B.Validation;h9B.exports=eV0;function eV0(A,B,Q,D){var Z=this,G=this._opts,F=[void 0],I={},Y=[],W={},J=[],X={},V=[];B=B||{schema:A,refVal:F,refs:I};var C=fR6.call(this,A,B,D),K=this._compilations[C.index];if(C.compiling)return K.callValidate=N;var H=this._formats,z=this.RULES;try{var $=O(A,B,Q,D);K.validate=$;var L=K.callValidate;if(L){if(L.schema=$.schema,L.errors=null,L.refs=$.refs,L.refVal=$.refVal,L.root=$.root,L.$async=$.$async,G.sourceCode)L.source=$.source}return $}finally{hR6.call(this,A,B,D)}function N(){var a=K.validate,x=a.apply(this,arguments);return N.errors=a.errors,x}function O(a,x,e,W1){var U1=!x||x&&x.schema==a;if(x.schema!=B.schema)return eV0.call(Z,a,x,e,W1);var y1=a.$async===!0,W0=v9B({isTop:!0,schema:a,isRoot:U1,baseId:W1,root:x,schemaPath:"",errSchemaPath:"#",errorPath:'""',MissingRefError:b9B.MissingRef,RULES:z,validate:v9B,util:Mj1,resolve:Nj1,resolveRef:R,usePattern:c,useDefault:h,useCustomRule:n,opts:G,formats:H,logger:Z.logger,self:Z});if(W0=Lj1(F,mR6)+Lj1(Y,gR6)+Lj1(J,uR6)+Lj1(V,dR6)+W0,G.processCode)W0=G.processCode(W0,a);var F0;try{var g1=new Function("self","RULES","formats","root","refVal","defaults","customRules","equal","ucs2length","ValidationError",W0);F0=g1(Z,z,H,B,F,J,V,vR6,xR6,bR6),F[0]=F0}catch(K1){throw Z.logger.error("Error compiling schema, function code:",W0),K1}if(F0.schema=a,F0.errors=null,F0.refs=I,F0.refVal=F,F0.root=U1?F0:x,y1)F0.$async=!0;if(G.sourceCode===!0)F0.source={code:W0,patterns:Y,defaults:J};return F0}function R(a,x,e){x=Nj1.url(a,x);var W1=I[x],U1,y1;if(W1!==void 0)return U1=F[W1],y1="refVal["+W1+"]",k(U1,y1);if(!e&&B.refs){var W0=B.refs[x];if(W0!==void 0)return U1=B.refVal[W0],y1=T(x,U1),k(U1,y1)}y1=T(x);var F0=Nj1.call(Z,O,B,x);if(F0===void 0){var g1=Q&&Q[x];if(g1)F0=Nj1.inlineRef(g1,G.inlineRefs)?g1:eV0.call(Z,g1,B,Q,a)}if(F0===void 0)j(x);else return f(x,F0),k(F0,y1)}function T(a,x){var e=F.length;return F[e]=x,I[a]=e,"refVal"+e}function j(a){delete I[a]}function f(a,x){var e=I[a];F[e]=x}function k(a,x){return typeof a=="object"||typeof a=="boolean"?{code:x,schema:a,inline:!0}:{code:x,$async:a&&!!a.$async}}function c(a){var x=W[a];if(x===void 0)x=W[a]=Y.length,Y[x]=a;return"pattern"+x}function h(a){switch(typeof a){case"boolean":case"number":return""+a;case"string":return Mj1.toQuotedString(a);case"object":if(a===null)return"null";var x=_R6(a),e=X[x];if(e===void 0)e=X[x]=J.length,J[e]=a;return"default"+e}}function n(a,x,e,W1){if(Z._opts.validateSchema!==!1){var U1=a.definition.dependencies;if(U1&&!U1.every(function(a1){return Object.prototype.hasOwnProperty.call(e,a1)}))throw new Error("parent schema must have all required keywords: "+U1.join(","));var y1=a.definition.validateSchema;if(y1){var W0=y1(x);if(!W0){var F0="keyword schema is invalid: "+Z.errorsText(y1.errors);if(Z._opts.validateSchema=="log")Z.logger.error(F0);else throw new Error(F0)}}}var g1=a.definition.compile,K1=a.definition.inline,G1=a.definition.macro,L1;if(g1)L1=g1.call(Z,x,e,W1);else if(G1){if(L1=G1.call(Z,x,e,W1),G.validateSchema!==!1)Z.validateSchema(L1,!0)}else if(K1)L1=K1.call(Z,W1,a.keyword,x,e);else if(L1=a.definition.validate,!L1)return;if(L1===void 0)throw new Error('custom keyword "'+a.keyword+'"failed to compile');var M1=V.length;return V[M1]=L1,{code:"customRule"+M1,validate:L1}}}function fR6(A,B,Q){var D=f9B.call(this,A,B,Q);if(D>=0)return{index:D,compiling:!0};return D=this._compilations.length,this._compilations[D]={schema:A,root:B,baseId:Q},{index:D,compiling:!1}}function hR6(A,B,Q){var D=f9B.call(this,A,B,Q);if(D>=0)this._compilations.splice(D,1)}function f9B(A,B,Q){for(var D=0;D<this._compilations.length;D++){var Z=this._compilations[D];if(Z.schema==A&&Z.root==B&&Z.baseId==Q)return D}return-1}function gR6(A,B){return"var pattern"+A+" = new RegExp("+Mj1.toQuotedString(B[A])+");"}function uR6(A){return"var default"+A+" = defaults["+A+"];"}function mR6(A,B){return B[A]===void 0?"":"var refVal"+A+" = refVal["+A+"];"}function dR6(A){return"var customRule"+A+" = customRules["+A+"];"}function Lj1(A,B){if(!A.length)return"";var Q="";for(var D=0;D<A.length;D++)Q+=B(D,A);return Q}});
var gQB=E((up5,hQB)=>{hQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||""),V="valid"+G,C=B.opts.$data&&I&&I.$data,K;if(C)Z+=" var schema"+G+" = "+B.util.getData(I.$data,F,B.dataPathArr)+"; ",K="schema"+G;else K=I;var H="schema"+G;if(!C)if(I.length<B.opts.loopRequired&&B.schema.properties&&Object.keys(B.schema.properties).length){var z=[],$=I;if($){var L,N=-1,O=$.length-1;while(N<O){L=$[N+=1];var R=B.schema.properties[L];if(!(R&&(B.opts.strictKeywords?typeof R=="object"&&Object.keys(R).length>0||R===!1:B.util.schemaHasRules(R,B.RULES.all))))z[z.length]=L}}}else var z=I;if(C||z.length){var T=B.errorPath,j=C||z.length>=B.opts.loopRequired,f=B.opts.ownProperties;if(J)if(Z+=" var missing"+G+"; ",j){if(!C)Z+=" var "+H+" = validate.schema"+Y+"; ";var k="i"+G,c="schema"+G+"["+k+"]",h="' + "+c+" + '";if(B.opts._errorDataPathProperty)B.errorPath=B.util.getPathExpr(T,c,B.opts.jsonPointers);if(Z+=" var "+V+" = true; ",C)Z+=" if (schema"+G+" === undefined) "+V+" = true; else if (!Array.isArray(schema"+G+")) "+V+" = false; else {";if(Z+=" for (var "+k+" = 0; "+k+" < "+H+".length; "+k+"++) { "+V+" = "+X+"["+H+"["+k+"]] !== undefined ",f)Z+=" &&   Object.prototype.hasOwnProperty.call("+X+", "+H+"["+k+"]) ";if(Z+="; if (!"+V+") break; } ",C)Z+="  }  ";Z+="  if (!"+V+") {   ";var n=n||[];if(n.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: 'required' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { missingProperty: '"+h+"' } ",B.opts.messages!==!1){if(Z+=" , message: '",B.opts._errorDataPathProperty)Z+="is a required property";else Z+="should have required property \\'"+h+"\\'";Z+="' "}if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";var a=Z;if(Z=n.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+a+"]); ";else Z+=" validate.errors = ["+a+"]; return false; ";else Z+=" var err = "+a+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";Z+=" } else { "}else{Z+=" if ( ";var x=z;if(x){var e,k=-1,W1=x.length-1;while(k<W1){if(e=x[k+=1],k)Z+=" || ";var U1=B.util.getProperty(e),y1=X+U1;if(Z+=" ( ( "+y1+" === undefined ",f)Z+=" || ! Object.prototype.hasOwnProperty.call("+X+", '"+B.util.escapeQuotes(e)+"') ";Z+=") && (missing"+G+" = "+B.util.toQuotedString(B.opts.jsonPointers?e:U1)+") ) "}}Z+=") {  ";var c="missing"+G,h="' + "+c+" + '";if(B.opts._errorDataPathProperty)B.errorPath=B.opts.jsonPointers?B.util.getPathExpr(T,c,!0):T+" + "+c;var n=n||[];if(n.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: 'required' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { missingProperty: '"+h+"' } ",B.opts.messages!==!1){if(Z+=" , message: '",B.opts._errorDataPathProperty)Z+="is a required property";else Z+="should have required property \\'"+h+"\\'";Z+="' "}if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";var a=Z;if(Z=n.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+a+"]); ";else Z+=" validate.errors = ["+a+"]; return false; ";else Z+=" var err = "+a+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";Z+=" } else { "}else if(j){if(!C)Z+=" var "+H+" = validate.schema"+Y+"; ";var k="i"+G,c="schema"+G+"["+k+"]",h="' + "+c+" + '";if(B.opts._errorDataPathProperty)B.errorPath=B.util.getPathExpr(T,c,B.opts.jsonPointers);if(C){if(Z+=" if ("+H+" && !Array.isArray("+H+")) {  var err =   ",B.createErrors!==!1){if(Z+=" { keyword: 'required' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { missingProperty: '"+h+"' } ",B.opts.messages!==!1){if(Z+=" , message: '",B.opts._errorDataPathProperty)Z+="is a required property";else Z+="should have required property \\'"+h+"\\'";Z+="' "}if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";Z+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } else if ("+H+" !== undefined) { "}if(Z+=" for (var "+k+" = 0; "+k+" < "+H+".length; "+k+"++) { if ("+X+"["+H+"["+k+"]] === undefined ",f)Z+=" || ! Object.prototype.hasOwnProperty.call("+X+", "+H+"["+k+"]) ";if(Z+=") {  var err =   ",B.createErrors!==!1){if(Z+=" { keyword: 'required' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { missingProperty: '"+h+"' } ",B.opts.messages!==!1){if(Z+=" , message: '",B.opts._errorDataPathProperty)Z+="is a required property";else Z+="should have required property \\'"+h+"\\'";Z+="' "}if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";if(Z+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } } ",C)Z+="  }  "}else{var W0=z;if(W0){var e,F0=-1,g1=W0.length-1;while(F0<g1){e=W0[F0+=1];var U1=B.util.getProperty(e),h=B.util.escapeQuotes(e),y1=X+U1;if(B.opts._errorDataPathProperty)B.errorPath=B.util.getPath(T,e,B.opts.jsonPointers);if(Z+=" if ( "+y1+" === undefined ",f)Z+=" || ! Object.prototype.hasOwnProperty.call("+X+", '"+B.util.escapeQuotes(e)+"') ";if(Z+=") {  var err =   ",B.createErrors!==!1){if(Z+=" { keyword: 'required' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { missingProperty: '"+h+"' } ",B.opts.messages!==!1){if(Z+=" , message: '",B.opts._errorDataPathProperty)Z+="is a required property";else Z+="should have required property \\'"+h+"\\'";Z+="' "}if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";Z+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } "}}}B.errorPath=T}else if(J)Z+=" if (true) {";return Z}});
var iQB=E((cp5,pQB)=>{var lQB=cQB(),ZC0=Zm().toHash;pQB.exports=function A(){var B=[{type:"number",rules:[{maximum:["exclusiveMaximum"]},{minimum:["exclusiveMinimum"]},"multipleOf","format"]},{type:"string",rules:["maxLength","minLength","pattern","format"]},{type:"array",rules:["maxItems","minItems","items","contains","uniqueItems"]},{type:"object",rules:["maxProperties","minProperties","required","dependencies","propertyNames",{properties:["additionalProperties","patternProperties"]}]},{rules:["$ref","const","enum","not","anyOf","oneOf","allOf","if"]}],Q=["type","$comment"],D=["$schema","$id","id","$data","$async","title","description","default","definitions","examples","readOnly","writeOnly","contentMediaType","contentEncoding","additionalItems","then","else"],Z=["number","integer","string","array","object","boolean","null"];return B.all=ZC0(Q),B.types=ZC0(Z),B.forEach(function(G){if(G.rules=G.rules.map(function(F){var I;if(typeof F=="object"){var Y=Object.keys(F)[0];I=F[Y],F=Y,I.forEach(function(J){Q.push(J),B.all[J]=!0})}Q.push(F);var W=B.all[F]={keyword:F,code:lQB[F],implements:I};return W}),B.all.$comment={keyword:"$comment",code:lQB.$comment},G.type)B.types[G.type]=G}),B.keywords=ZC0(Q.concat(D)),B.custom={},B}});
var kQB=E((bp5,jQB)=>{jQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||""),V="valid"+G,C="errs__"+G,K=B.util.copy(B),H="";K.level++;var z="valid"+K.level,$=K.baseId,L="prevValid"+G,N="passingSchemas"+G;Z+="var "+C+" = errors , "+L+" = false , "+V+" = false , "+N+" = null; ";var O=B.compositeRule;B.compositeRule=K.compositeRule=!0;var R=I;if(R){var T,j=-1,f=R.length-1;while(j<f){if(T=R[j+=1],B.opts.strictKeywords?typeof T=="object"&&Object.keys(T).length>0||T===!1:B.util.schemaHasRules(T,B.RULES.all))K.schema=T,K.schemaPath=Y+"["+j+"]",K.errSchemaPath=W+"/"+j,Z+="  "+B.validate(K)+" ",K.baseId=$;else Z+=" var "+z+" = true; ";if(j)Z+=" if ("+z+" && "+L+") { "+V+" = false; "+N+" = ["+N+", "+j+"]; } else { ",H+="}";Z+=" if ("+z+") { "+V+" = "+L+" = true; "+N+" = "+j+"; }"}}if(B.compositeRule=K.compositeRule=O,Z+=""+H+"if (!"+V+") {   var err =   ",B.createErrors!==!1){if(Z+=" { keyword: 'oneOf' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { passingSchemas: "+N+" } ",B.opts.messages!==!1)Z+=" , message: 'should match exactly one schema in oneOf' ";if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";if(Z+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(vErrors); ";else Z+=" validate.errors = vErrors; return false; ";if(Z+="} else {  errors = "+C+"; if (vErrors !== null) { if ("+C+") vErrors.length = "+C+"; else vErrors = null; }",B.opts.allErrors)Z+=" } ";return Z}});
var m9B=E((Ep5,u9B)=>{var Rj1=u9B.exports=function A(){this._cache={}};Rj1.prototype.put=function A(B,Q){this._cache[B]=Q};Rj1.prototype.get=function A(B){return this._cache[B]};Rj1.prototype.del=function A(B){delete this._cache[B]};Rj1.prototype.clear=function A(){this._cache={}}});
var mQB=E((mp5,uQB)=>{uQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||""),V="valid"+G,C=B.opts.$data&&I&&I.$data,K;if(C)Z+=" var schema"+G+" = "+B.util.getData(I.$data,F,B.dataPathArr)+"; ",K="schema"+G;else K=I;if((I||C)&&B.opts.uniqueItems!==!1){if(C)Z+=" var "+V+"; if ("+K+" === false || "+K+" === undefined) "+V+" = true; else if (typeof "+K+" != 'boolean') "+V+" = false; else { ";Z+=" var i = "+X+".length , "+V+" = true , j; if (i > 1) { ";var H=B.schema.items&&B.schema.items.type,z=Array.isArray(H);if(!H||H=="object"||H=="array"||z&&(H.indexOf("object")>=0||H.indexOf("array")>=0))Z+=" outer: for (;i--;) { for (j = i; j--;) { if (equal("+X+"[i], "+X+"[j])) { "+V+" = false; break outer; } } } ";else{Z+=" var itemIndices = {}, item; for (;i--;) { var item = "+X+"[i]; ";var $="checkDataType"+(z?"s":"");if(Z+=" if ("+B.util[$](H,"item",B.opts.strictNumbers,!0)+") continue; ",z)Z+=` if (typeof item == 'string') item = '"' + item; `;Z+=" if (typeof itemIndices[item] == 'number') { "+V+" = false; j = itemIndices[item]; break; } itemIndices[item] = i; } "}if(Z+=" } ",C)Z+="  }  ";Z+=" if (!"+V+") {   ";var L=L||[];if(L.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: 'uniqueItems' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { i: i, j: j } ",B.opts.messages!==!1)Z+=" , message: 'should NOT have duplicate items (items ## ' + j + ' and ' + i + ' are identical)' ";if(B.opts.verbose){if(Z+=" , schema:  ",C)Z+="validate.schema"+Y;else Z+=""+I;Z+="         , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" "}Z+=" } "}else Z+=" {} ";var N=Z;if(Z=L.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+N+"]); ";else Z+=" validate.errors = ["+N+"]; return false; ";else Z+=" var err = "+N+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(Z+=" } ",J)Z+=" else { "}else if(J)Z+=" if (true) { ";return Z}});
var oV0=E((Kp5,_9B)=>{_9B.exports=function(A,B){if(!B)B={};if(typeof B==="function")B={cmp:B};var Q=typeof B.cycles==="boolean"?B.cycles:!1,D=B.cmp&&function(G){return function(F){return function(I,Y){var W={key:I,value:F[I]},J={key:Y,value:F[Y]};return G(W,J)}}}(B.cmp),Z=[];return function G(F){if(F&&F.toJSON&&typeof F.toJSON==="function")F=F.toJSON();if(F===void 0)return;if(typeof F=="number")return isFinite(F)?""+F:"null";if(typeof F!=="object")return JSON.stringify(F);var I,Y;if(Array.isArray(F)){Y="[";for(I=0;I<F.length;I++){if(I)Y+=",";Y+=G(F[I])||"null"}return Y+"]"}if(F===null)return"null";if(Z.indexOf(F)!==-1){if(Q)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}var W=Z.push(F)-1,J=Object.keys(F).sort(D&&D(F));Y="";for(I=0;I<J.length;I++){var X=J[I],V=G(F[X]);if(!V)continue;if(Y)Y+=",";Y+=JSON.stringify(X)+":"+V}return Z.splice(W,1),"{"+Y+"}"}(A)}});
var pV0=E((Jp5,M9B)=>{var LR6=Zm();M9B.exports=MR6;function MR6(A){LR6.copy(A,this)}});
var qQB=E((Sp5,$QB)=>{$QB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||""),V="valid"+G,C="errs__"+G,K=B.util.copy(B),H="";K.level++;var z="valid"+K.level,$="i"+G,L=K.dataLevel=B.dataLevel+1,N="data"+L,O=B.baseId;if(Z+="var "+C+" = errors;var "+V+";",Array.isArray(I)){var R=B.schema.additionalItems;if(R===!1){Z+=" "+V+" = "+X+".length <= "+I.length+"; ";var T=W;W=B.errSchemaPath+"/additionalItems",Z+="  if (!"+V+") {   ";var j=j||[];if(j.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: 'additionalItems' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { limit: "+I.length+" } ",B.opts.messages!==!1)Z+=" , message: 'should NOT have more than "+I.length+" items' ";if(B.opts.verbose)Z+=" , schema: false , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";var f=Z;if(Z=j.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+f+"]); ";else Z+=" validate.errors = ["+f+"]; return false; ";else Z+=" var err = "+f+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(Z+=" } ",W=T,J)H+="}",Z+=" else { "}var k=I;if(k){var c,h=-1,n=k.length-1;while(h<n)if(c=k[h+=1],B.opts.strictKeywords?typeof c=="object"&&Object.keys(c).length>0||c===!1:B.util.schemaHasRules(c,B.RULES.all)){Z+=" "+z+" = true; if ("+X+".length > "+h+") { ";var a=X+"["+h+"]";K.schema=c,K.schemaPath=Y+"["+h+"]",K.errSchemaPath=W+"/"+h,K.errorPath=B.util.getPathExpr(B.errorPath,h,B.opts.jsonPointers,!0),K.dataPathArr[L]=h;var x=B.validate(K);if(K.baseId=O,B.util.varOccurences(x,N)<2)Z+=" "+B.util.varReplace(x,N,a)+" ";else Z+=" var "+N+" = "+a+"; "+x+" ";if(Z+=" }  ",J)Z+=" if ("+z+") { ",H+="}"}}if(typeof R=="object"&&(B.opts.strictKeywords?typeof R=="object"&&Object.keys(R).length>0||R===!1:B.util.schemaHasRules(R,B.RULES.all))){K.schema=R,K.schemaPath=B.schemaPath+".additionalItems",K.errSchemaPath=B.errSchemaPath+"/additionalItems",Z+=" "+z+" = true; if ("+X+".length > "+I.length+") {  for (var "+$+" = "+I.length+"; "+$+" < "+X+".length; "+$+"++) { ",K.errorPath=B.util.getPathExpr(B.errorPath,$,B.opts.jsonPointers,!0);var a=X+"["+$+"]";K.dataPathArr[L]=$;var x=B.validate(K);if(K.baseId=O,B.util.varOccurences(x,N)<2)Z+=" "+B.util.varReplace(x,N,a)+" ";else Z+=" var "+N+" = "+a+"; "+x+" ";if(J)Z+=" if (!"+z+") break; ";if(Z+=" } }  ",J)Z+=" if ("+z+") { ",H+="}"}}else if(B.opts.strictKeywords?typeof I=="object"&&Object.keys(I).length>0||I===!1:B.util.schemaHasRules(I,B.RULES.all)){K.schema=I,K.schemaPath=Y,K.errSchemaPath=W,Z+="  for (var "+$+" = 0; "+$+" < "+X+".length; "+$+"++) { ",K.errorPath=B.util.getPathExpr(B.errorPath,$,B.opts.jsonPointers,!0);var a=X+"["+$+"]";K.dataPathArr[L]=$;var x=B.validate(K);if(K.baseId=O,B.util.varOccurences(x,N)<2)Z+=" "+B.util.varReplace(x,N,a)+" ";else Z+=" var "+N+" = "+a+"; "+x+" ";if(J)Z+=" if (!"+z+") break; ";Z+=" }"}if(J)Z+=" "+H+" if ("+C+" == errors) {";return Z}});
var qj1=E((Cp5,y9B)=>{var sV0=$j1();y9B.exports={Validation:k9B(yR6),MissingRef:k9B(rV0)};function yR6(A){this.message="validation failed",this.errors=A,this.ajv=this.validation=!0}rV0.message=function(A,B){return"can't resolve reference "+B+" from id "+A};function rV0(A,B,Q){this.message=Q||rV0.message(A,B),this.missingRef=sV0.url(A,B),this.missingSchema=sV0.normalizeId(sV0.fullPath(this.missingRef))}function k9B(A){return A.prototype=Object.create(Error.prototype),A.prototype.constructor=A,A}});
var sQB=E((lp5,aQB)=>{var nQB=["multipleOf","maximum","exclusiveMaximum","minimum","exclusiveMinimum","maxLength","minLength","pattern","additionalItems","maxItems","minItems","uniqueItems","maxProperties","minProperties","required","additionalProperties","enum","format","const"];aQB.exports=function(A,B){for(var Q=0;Q<B.length;Q++){A=JSON.parse(JSON.stringify(A));var D=B[Q].split("/"),Z=A,G;for(G=1;G<D.length;G++)Z=Z[D[G]];for(G=0;G<nQB.length;G++){var F=nQB[G],I=Z[F];if(I)Z[F]={anyOf:[I,{$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"}]}}}return A}});
var tQB=E((pp5,oQB)=>{var BO6=qj1().MissingRef;oQB.exports=rQB;function rQB(A,B,Q){var D=this;if(typeof this._opts.loadSchema!="function")throw new Error("options.loadSchema should be a function");if(typeof B=="function")Q=B,B=void 0;var Z=G(A).then(function(){var I=D._addSchema(A,void 0,B);return I.validate||F(I)});if(Q)Z.then(function(I){Q(null,I)},Q);return Z;function G(I){var Y=I.$schema;return Y&&!D.getSchema(Y)?rQB.call(D,{$ref:Y},!0):Promise.resolve()}function F(I){try{return D._compile(I)}catch(W){if(W instanceof BO6)return Y(W);throw W}function Y(W){var J=W.missingSchema;if(C(J))throw new Error("Schema "+J+" is loaded but "+W.missingRef+" cannot be resolved");var X=D._loadingSchemas[J];if(!X)X=D._loadingSchemas[J]=D._opts.loadSchema(J),X.then(V,V);return X.then(function(K){if(!C(J))return G(K).then(function(){if(!C(J))D.addSchema(K,J,void 0,B)})}).then(function(){return F(I)});function V(){delete D._loadingSchemas[J]}function C(K){return D._refs[K]||D._schemas[K]}}}}});
var tV0=E((Hp5,x9B)=>{x9B.exports=function A(B,Q,D){var Z="",G=B.schema.$async===!0,F=B.util.schemaHasRulesExcept(B.schema,B.RULES.all,"$ref"),I=B.self._getId(B.schema);if(B.opts.strictKeywords){var Y=B.util.schemaUnknownRules(B.schema,B.RULES.keywords);if(Y){var W="unknown keyword: "+Y;if(B.opts.strictKeywords==="log")B.logger.warn(W);else throw new Error(W)}}if(B.isTop){if(Z+=" var validate = ",G)B.async=!0,Z+="async ";if(Z+="function(data, dataPath, parentData, parentDataProperty, rootData) { 'use strict'; ",I&&(B.opts.sourceCode||B.opts.processCode))Z+=" "+("/*# sourceURL="+I+" */")+" "}if(typeof B.schema=="boolean"||!(F||B.schema.$ref)){var Q="false schema",J=B.level,X=B.dataLevel,V=B.schema[Q],C=B.schemaPath+B.util.getProperty(Q),K=B.errSchemaPath+"/"+Q,R=!B.opts.allErrors,f,H="data"+(X||""),O="valid"+J;if(B.schema===!1){if(B.isTop)R=!0;else Z+=" var "+O+" = false; ";var z=z||[];if(z.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: '"+(f||"false schema")+"' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(K)+" , params: {} ",B.opts.messages!==!1)Z+=" , message: 'boolean schema is false' ";if(B.opts.verbose)Z+=" , schema: false , parentSchema: validate.schema"+B.schemaPath+" , data: "+H+" ";Z+=" } "}else Z+=" {} ";var $=Z;if(Z=z.pop(),!B.compositeRule&&R)if(B.async)Z+=" throw new ValidationError(["+$+"]); ";else Z+=" validate.errors = ["+$+"]; return false; ";else Z+=" var err = "+$+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}else if(B.isTop)if(G)Z+=" return data; ";else Z+=" validate.errors = null; return true; ";else Z+=" var "+O+" = true; ";if(B.isTop)Z+=" }; return validate; ";return Z}if(B.isTop){var L=B.isTop,J=B.level=0,X=B.dataLevel=0,H="data";if(B.rootId=B.resolve.fullPath(B.self._getId(B.root.schema)),B.baseId=B.baseId||B.rootId,delete B.isTop,B.dataPathArr=[""],B.schema.default!==void 0&&B.opts.useDefaults&&B.opts.strictDefaults){var N="default is ignored in the schema root";if(B.opts.strictDefaults==="log")B.logger.warn(N);else throw new Error(N)}Z+=" var vErrors = null; ",Z+=" var errors = 0;     ",Z+=" if (rootData === undefined) rootData = data; "}else{var{level:J,dataLevel:X}=B,H="data"+(X||"");if(I)B.baseId=B.resolve.url(B.baseId,I);if(G&&!B.async)throw new Error("async schema in sync schema");Z+=" var errs_"+J+" = errors;"}var O="valid"+J,R=!B.opts.allErrors,T="",j="",f,k=B.schema.type,c=Array.isArray(k);if(k&&B.opts.nullable&&B.schema.nullable===!0){if(c){if(k.indexOf("null")==-1)k=k.concat("null")}else if(k!="null")k=[k,"null"],c=!0}if(c&&k.length==1)k=k[0],c=!1;if(B.schema.$ref&&F){if(B.opts.extendRefs=="fail")throw new Error('$ref: validation keywords used in schema at path "'+B.errSchemaPath+'" (see option extendRefs)');else if(B.opts.extendRefs!==!0)F=!1,B.logger.warn('$ref: keywords ignored in schema at path "'+B.errSchemaPath+'"')}if(B.schema.$comment&&B.opts.$comment)Z+=" "+B.RULES.all.$comment.code(B,"$comment");if(k){if(B.opts.coerceTypes)var h=B.util.coerceToTypes(B.opts.coerceTypes,k);var n=B.RULES.types[k];if(h||c||n===!0||n&&!I0(n)){var C=B.schemaPath+".type",K=B.errSchemaPath+"/type",C=B.schemaPath+".type",K=B.errSchemaPath+"/type",a=c?"checkDataTypes":"checkDataType";if(Z+=" if ("+B.util[a](k,H,B.opts.strictNumbers,!0)+") { ",h){var x="dataType"+J,e="coerced"+J;if(Z+=" var "+x+" = typeof "+H+"; var "+e+" = undefined; ",B.opts.coerceTypes=="array")Z+=" if ("+x+" == 'object' && Array.isArray("+H+") && "+H+".length == 1) { "+H+" = "+H+"[0]; "+x+" = typeof "+H+"; if ("+B.util.checkDataType(B.schema.type,H,B.opts.strictNumbers)+") "+e+" = "+H+"; } ";Z+=" if ("+e+" !== undefined) ; ";var W1=h;if(W1){var U1,y1=-1,W0=W1.length-1;while(y1<W0)if(U1=W1[y1+=1],U1=="string")Z+=" else if ("+x+" == 'number' || "+x+" == 'boolean') "+e+" = '' + "+H+"; else if ("+H+" === null) "+e+" = ''; ";else if(U1=="number"||U1=="integer"){if(Z+=" else if ("+x+" == 'boolean' || "+H+" === null || ("+x+" == 'string' && "+H+" && "+H+" == +"+H+" ",U1=="integer")Z+=" && !("+H+" % 1)";Z+=")) "+e+" = +"+H+"; "}else if(U1=="boolean")Z+=" else if ("+H+" === 'false' || "+H+" === 0 || "+H+" === null) "+e+" = false; else if ("+H+" === 'true' || "+H+" === 1) "+e+" = true; ";else if(U1=="null")Z+=" else if ("+H+" === '' || "+H+" === 0 || "+H+" === false) "+e+" = null; ";else if(B.opts.coerceTypes=="array"&&U1=="array")Z+=" else if ("+x+" == 'string' || "+x+" == 'number' || "+x+" == 'boolean' || "+H+" == null) "+e+" = ["+H+"]; "}Z+=" else {   ";var z=z||[];if(z.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: '"+(f||"type")+"' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(K)+" , params: { type: '",c)Z+=""+k.join(",");else Z+=""+k;if(Z+="' } ",B.opts.messages!==!1){if(Z+=" , message: 'should be ",c)Z+=""+k.join(",");else Z+=""+k;Z+="' "}if(B.opts.verbose)Z+=" , schema: validate.schema"+C+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+H+" ";Z+=" } "}else Z+=" {} ";var $=Z;if(Z=z.pop(),!B.compositeRule&&R)if(B.async)Z+=" throw new ValidationError(["+$+"]); ";else Z+=" validate.errors = ["+$+"]; return false; ";else Z+=" var err = "+$+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";Z+=" } if ("+e+" !== undefined) {  ";var F0=X?"data"+(X-1||""):"parentData",g1=X?B.dataPathArr[X]:"parentDataProperty";if(Z+=" "+H+" = "+e+"; ",!X)Z+="if ("+F0+" !== undefined)";Z+=" "+F0+"["+g1+"] = "+e+"; } "}else{var z=z||[];if(z.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: '"+(f||"type")+"' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(K)+" , params: { type: '",c)Z+=""+k.join(",");else Z+=""+k;if(Z+="' } ",B.opts.messages!==!1){if(Z+=" , message: 'should be ",c)Z+=""+k.join(",");else Z+=""+k;Z+="' "}if(B.opts.verbose)Z+=" , schema: validate.schema"+C+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+H+" ";Z+=" } "}else Z+=" {} ";var $=Z;if(Z=z.pop(),!B.compositeRule&&R)if(B.async)Z+=" throw new ValidationError(["+$+"]); ";else Z+=" validate.errors = ["+$+"]; return false; ";else Z+=" var err = "+$+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}Z+=" } "}}if(B.schema.$ref&&!F){if(Z+=" "+B.RULES.all.$ref.code(B,"$ref")+" ",R){if(Z+=" } if (errors === ",L)Z+="0";else Z+="errs_"+J;Z+=") { ",j+="}"}}else{var K1=B.RULES;if(K1){var n,G1=-1,L1=K1.length-1;while(G1<L1)if(n=K1[G1+=1],I0(n)){if(n.type)Z+=" if ("+B.util.checkDataType(n.type,H,B.opts.strictNumbers)+") { ";if(B.opts.useDefaults){if(n.type=="object"&&B.schema.properties){var V=B.schema.properties,M1=Object.keys(V),a1=M1;if(a1){var i1,E0=-1,B1=a1.length-1;while(E0<B1){i1=a1[E0+=1];var A1=V[i1];if(A1.default!==void 0){var I1=H+B.util.getProperty(i1);if(B.compositeRule){if(B.opts.strictDefaults){var N="default is ignored for: "+I1;if(B.opts.strictDefaults==="log")B.logger.warn(N);else throw new Error(N)}}else{if(Z+=" if ("+I1+" === undefined ",B.opts.useDefaults=="empty")Z+=" || "+I1+" === null || "+I1+" === '' ";if(Z+=" ) "+I1+" = ",B.opts.useDefaults=="shared")Z+=" "+B.useDefault(A1.default)+" ";else Z+=" "+JSON.stringify(A1.default)+" ";Z+="; "}}}}}else if(n.type=="array"&&Array.isArray(B.schema.items)){var q1=B.schema.items;if(q1){var A1,y1=-1,P1=q1.length-1;while(y1<P1)if(A1=q1[y1+=1],A1.default!==void 0){var I1=H+"["+y1+"]";if(B.compositeRule){if(B.opts.strictDefaults){var N="default is ignored for: "+I1;if(B.opts.strictDefaults==="log")B.logger.warn(N);else throw new Error(N)}}else{if(Z+=" if ("+I1+" === undefined ",B.opts.useDefaults=="empty")Z+=" || "+I1+" === null || "+I1+" === '' ";if(Z+=" ) "+I1+" = ",B.opts.useDefaults=="shared")Z+=" "+B.useDefault(A1.default)+" ";else Z+=" "+JSON.stringify(A1.default)+" ";Z+="; "}}}}}var Q1=n.rules;if(Q1){var f1,l1=-1,n1=Q1.length-1;while(l1<n1)if(f1=Q1[l1+=1],M0(f1)){var V0=f1.code(B,f1.keyword,n.type);if(V0){if(Z+=" "+V0+" ",R)T+="}"}}}if(R)Z+=" "+T+" ",T="";if(n.type){if(Z+=" } ",k&&k===n.type&&!h){Z+=" else { ";var C=B.schemaPath+".type",K=B.errSchemaPath+"/type",z=z||[];if(z.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: '"+(f||"type")+"' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(K)+" , params: { type: '",c)Z+=""+k.join(",");else Z+=""+k;if(Z+="' } ",B.opts.messages!==!1){if(Z+=" , message: 'should be ",c)Z+=""+k.join(",");else Z+=""+k;Z+="' "}if(B.opts.verbose)Z+=" , schema: validate.schema"+C+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+H+" ";Z+=" } "}else Z+=" {} ";var $=Z;if(Z=z.pop(),!B.compositeRule&&R)if(B.async)Z+=" throw new ValidationError(["+$+"]); ";else Z+=" validate.errors = ["+$+"]; return false; ";else Z+=" var err = "+$+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";Z+=" } "}}if(R){if(Z+=" if (errors === ",L)Z+="0";else Z+="errs_"+J;Z+=") { ",j+="}"}}}}if(R)Z+=" "+j+" ";if(L){if(G)Z+=" if (errors === 0) return data;           ",Z+=" else throw new ValidationError(vErrors); ";else Z+=" validate.errors = vErrors; ",Z+=" return errors === 0;       ";Z+=" }; return validate;"}else Z+=" var "+O+" = errors === errs_"+J+";";function I0(m0){var SA=m0.rules;for(var v2=0;v2<SA.length;v2++)if(M0(SA[v2]))return!0}function M0(m0){return B.schema[m0.keyword]!==void 0||m0.implements&&YA(m0)}function YA(m0){var SA=m0.implements;for(var v2=0;v2<SA.length;v2++)if(B.schema[SA[v2]]!==void 0)return!0}return Z}});
var vQB=E((hp5,xQB)=>{xQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||""),V="errs__"+G,C=B.util.copy(B),K="";C.level++;var H="valid"+C.level,z="key"+G,$="idx"+G,L=C.dataLevel=B.dataLevel+1,N="data"+L,O="dataProperties"+G,R=Object.keys(I||{}).filter(y1),T=B.schema.patternProperties||{},j=Object.keys(T).filter(y1),f=B.schema.additionalProperties,k=R.length||j.length,c=f===!1,h=typeof f=="object"&&Object.keys(f).length,n=B.opts.removeAdditional,a=c||h||n,x=B.opts.ownProperties,e=B.baseId,W1=B.schema.required;if(W1&&!(B.opts.$data&&W1.$data)&&W1.length<B.opts.loopRequired)var U1=B.util.toHash(W1);function y1(b2){return b2!=="__proto__"}if(Z+="var "+V+" = errors;var "+H+" = true;",x)Z+=" var "+O+" = undefined;";if(a){if(x)Z+=" "+O+" = "+O+" || Object.keys("+X+"); for (var "+$+"=0; "+$+"<"+O+".length; "+$+"++) { var "+z+" = "+O+"["+$+"]; ";else Z+=" for (var "+z+" in "+X+") { ";if(k){if(Z+=" var isAdditional"+G+" = !(false ",R.length)if(R.length>8)Z+=" || validate.schema"+Y+".hasOwnProperty("+z+") ";else{var W0=R;if(W0){var F0,g1=-1,K1=W0.length-1;while(g1<K1)F0=W0[g1+=1],Z+=" || "+z+" == "+B.util.toQuotedString(F0)+" "}}if(j.length){var G1=j;if(G1){var L1,M1=-1,a1=G1.length-1;while(M1<a1)L1=G1[M1+=1],Z+=" || "+B.usePattern(L1)+".test("+z+") "}}Z+=" ); if (isAdditional"+G+") { "}if(n=="all")Z+=" delete "+X+"["+z+"]; ";else{var i1=B.errorPath,E0="' + "+z+" + '";if(B.opts._errorDataPathProperty)B.errorPath=B.util.getPathExpr(B.errorPath,z,B.opts.jsonPointers);if(c)if(n)Z+=" delete "+X+"["+z+"]; ";else{Z+=" "+H+" = false; ";var B1=W;W=B.errSchemaPath+"/additionalProperties";var A1=A1||[];if(A1.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: 'additionalProperties' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { additionalProperty: '"+E0+"' } ",B.opts.messages!==!1){if(Z+=" , message: '",B.opts._errorDataPathProperty)Z+="is an invalid additional property";else Z+="should NOT have additional properties";Z+="' "}if(B.opts.verbose)Z+=" , schema: false , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";var I1=Z;if(Z=A1.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+I1+"]); ";else Z+=" validate.errors = ["+I1+"]; return false; ";else Z+=" var err = "+I1+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";if(W=B1,J)Z+=" break; "}else if(h)if(n=="failing"){Z+=" var "+V+" = errors;  ";var q1=B.compositeRule;B.compositeRule=C.compositeRule=!0,C.schema=f,C.schemaPath=B.schemaPath+".additionalProperties",C.errSchemaPath=B.errSchemaPath+"/additionalProperties",C.errorPath=B.opts._errorDataPathProperty?B.errorPath:B.util.getPathExpr(B.errorPath,z,B.opts.jsonPointers);var P1=X+"["+z+"]";C.dataPathArr[L]=z;var Q1=B.validate(C);if(C.baseId=e,B.util.varOccurences(Q1,N)<2)Z+=" "+B.util.varReplace(Q1,N,P1)+" ";else Z+=" var "+N+" = "+P1+"; "+Q1+" ";Z+=" if (!"+H+") { errors = "+V+"; if (validate.errors !== null) { if (errors) validate.errors.length = errors; else validate.errors = null; } delete "+X+"["+z+"]; }  ",B.compositeRule=C.compositeRule=q1}else{C.schema=f,C.schemaPath=B.schemaPath+".additionalProperties",C.errSchemaPath=B.errSchemaPath+"/additionalProperties",C.errorPath=B.opts._errorDataPathProperty?B.errorPath:B.util.getPathExpr(B.errorPath,z,B.opts.jsonPointers);var P1=X+"["+z+"]";C.dataPathArr[L]=z;var Q1=B.validate(C);if(C.baseId=e,B.util.varOccurences(Q1,N)<2)Z+=" "+B.util.varReplace(Q1,N,P1)+" ";else Z+=" var "+N+" = "+P1+"; "+Q1+" ";if(J)Z+=" if (!"+H+") break; "}B.errorPath=i1}if(k)Z+=" } ";if(Z+=" }  ",J)Z+=" if ("+H+") { ",K+="}"}var f1=B.opts.useDefaults&&!B.compositeRule;if(R.length){var l1=R;if(l1){var F0,n1=-1,V0=l1.length-1;while(n1<V0){F0=l1[n1+=1];var I0=I[F0];if(B.opts.strictKeywords?typeof I0=="object"&&Object.keys(I0).length>0||I0===!1:B.util.schemaHasRules(I0,B.RULES.all)){var M0=B.util.getProperty(F0),P1=X+M0,YA=f1&&I0.default!==void 0;C.schema=I0,C.schemaPath=Y+M0,C.errSchemaPath=W+"/"+B.util.escapeFragment(F0),C.errorPath=B.util.getPath(B.errorPath,F0,B.opts.jsonPointers),C.dataPathArr[L]=B.util.toQuotedString(F0);var Q1=B.validate(C);if(C.baseId=e,B.util.varOccurences(Q1,N)<2){Q1=B.util.varReplace(Q1,N,P1);var m0=P1}else{var m0=N;Z+=" var "+N+" = "+P1+"; "}if(YA)Z+=" "+Q1+" ";else{if(U1&&U1[F0]){if(Z+=" if ( "+m0+" === undefined ",x)Z+=" || ! Object.prototype.hasOwnProperty.call("+X+", '"+B.util.escapeQuotes(F0)+"') ";Z+=") { "+H+" = false; ";var i1=B.errorPath,B1=W,SA=B.util.escapeQuotes(F0);if(B.opts._errorDataPathProperty)B.errorPath=B.util.getPath(i1,F0,B.opts.jsonPointers);W=B.errSchemaPath+"/required";var A1=A1||[];if(A1.push(Z),Z="",B.createErrors!==!1){if(Z+=" { keyword: 'required' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { missingProperty: '"+SA+"' } ",B.opts.messages!==!1){if(Z+=" , message: '",B.opts._errorDataPathProperty)Z+="is a required property";else Z+="should have required property \\'"+SA+"\\'";Z+="' "}if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";var I1=Z;if(Z=A1.pop(),!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(["+I1+"]); ";else Z+=" validate.errors = ["+I1+"]; return false; ";else Z+=" var err = "+I1+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";W=B1,B.errorPath=i1,Z+=" } else { "}else if(J){if(Z+=" if ( "+m0+" === undefined ",x)Z+=" || ! Object.prototype.hasOwnProperty.call("+X+", '"+B.util.escapeQuotes(F0)+"') ";Z+=") { "+H+" = true; } else { "}else{if(Z+=" if ("+m0+" !== undefined ",x)Z+=" &&   Object.prototype.hasOwnProperty.call("+X+", '"+B.util.escapeQuotes(F0)+"') ";Z+=" ) { "}Z+=" "+Q1+" } "}}if(J)Z+=" if ("+H+") { ",K+="}"}}}if(j.length){var v2=j;if(v2){var L1,Y2=-1,N2=v2.length-1;while(Y2<N2){L1=v2[Y2+=1];var I0=T[L1];if(B.opts.strictKeywords?typeof I0=="object"&&Object.keys(I0).length>0||I0===!1:B.util.schemaHasRules(I0,B.RULES.all)){if(C.schema=I0,C.schemaPath=B.schemaPath+".patternProperties"+B.util.getProperty(L1),C.errSchemaPath=B.errSchemaPath+"/patternProperties/"+B.util.escapeFragment(L1),x)Z+=" "+O+" = "+O+" || Object.keys("+X+"); for (var "+$+"=0; "+$+"<"+O+".length; "+$+"++) { var "+z+" = "+O+"["+$+"]; ";else Z+=" for (var "+z+" in "+X+") { ";Z+=" if ("+B.usePattern(L1)+".test("+z+")) { ",C.errorPath=B.util.getPathExpr(B.errorPath,z,B.opts.jsonPointers);var P1=X+"["+z+"]";C.dataPathArr[L]=z;var Q1=B.validate(C);if(C.baseId=e,B.util.varOccurences(Q1,N)<2)Z+=" "+B.util.varReplace(Q1,N,P1)+" ";else Z+=" var "+N+" = "+P1+"; "+Q1+" ";if(J)Z+=" if (!"+H+") break; ";if(Z+=" } ",J)Z+=" else "+H+" = true; ";if(Z+=" }  ",J)Z+=" if ("+H+") { ",K+="}"}}}}if(J)Z+=" "+K+" if ("+V+" == errors) {";return Z}});
var wQB=E((Pp5,UQB)=>{UQB.exports=function A(B,Q,D){var Z=" ",G=B.level,F=B.dataLevel,I=B.schema[Q],Y=B.schemaPath+B.util.getProperty(Q),W=B.errSchemaPath+"/"+Q,J=!B.opts.allErrors,X="data"+(F||""),V="valid"+G,C="errs__"+G,K=B.util.copy(B);K.level++;var H="valid"+K.level,z=B.schema.then,$=B.schema.else,L=z!==void 0&&(B.opts.strictKeywords?typeof z=="object"&&Object.keys(z).length>0||z===!1:B.util.schemaHasRules(z,B.RULES.all)),N=$!==void 0&&(B.opts.strictKeywords?typeof $=="object"&&Object.keys($).length>0||$===!1:B.util.schemaHasRules($,B.RULES.all)),O=K.baseId;if(L||N){var R;K.createErrors=!1,K.schema=I,K.schemaPath=Y,K.errSchemaPath=W,Z+=" var "+C+" = errors; var "+V+" = true;  ";var T=B.compositeRule;if(B.compositeRule=K.compositeRule=!0,Z+="  "+B.validate(K)+" ",K.baseId=O,K.createErrors=!0,Z+="  errors = "+C+"; if (vErrors !== null) { if ("+C+") vErrors.length = "+C+"; else vErrors = null; }  ",B.compositeRule=K.compositeRule=T,L){if(Z+=" if ("+H+") {  ",K.schema=B.schema.then,K.schemaPath=B.schemaPath+".then",K.errSchemaPath=B.errSchemaPath+"/then",Z+="  "+B.validate(K)+" ",K.baseId=O,Z+=" "+V+" = "+H+"; ",L&&N)R="ifClause"+G,Z+=" var "+R+" = 'then'; ";else R="'then'";if(Z+=" } ",N)Z+=" else { "}else Z+=" if (!"+H+") { ";if(N){if(K.schema=B.schema.else,K.schemaPath=B.schemaPath+".else",K.errSchemaPath=B.errSchemaPath+"/else",Z+="  "+B.validate(K)+" ",K.baseId=O,Z+=" "+V+" = "+H+"; ",L&&N)R="ifClause"+G,Z+=" var "+R+" = 'else'; ";else R="'else'";Z+=" } "}if(Z+=" if (!"+V+") {   var err =   ",B.createErrors!==!1){if(Z+=" { keyword: 'if' , dataPath: (dataPath || '') + "+B.errorPath+" , schemaPath: "+B.util.toQuotedString(W)+" , params: { failingKeyword: "+R+" } ",B.opts.messages!==!1)Z+=` , message: 'should match "' + `+R+` + '" schema' `;if(B.opts.verbose)Z+=" , schema: validate.schema"+Y+" , parentSchema: validate.schema"+B.schemaPath+" , data: "+X+" ";Z+=" } "}else Z+=" {} ";if(Z+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!B.compositeRule&&J)if(B.async)Z+=" throw new ValidationError(vErrors); ";else Z+=" validate.errors = vErrors; return false; ";if(Z+=" }   ",J)Z+=" else { "}else if(J)Z+=" if (true) { ";return Z}});

module.exports = z4B;
