// dependency_chain package extracted with entry point: Pp0

var nl0=E((tL9)=>{var nL9=J1("url").parse,aL9={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},sL9=String.prototype.endsWith||function(A){return A.length<=this.length&&this.indexOf(A,this.length-A.length)!==-1};function rL9(A){var B=typeof A==="string"?nL9(A):A||{},Q=B.protocol,D=B.host,Z=B.port;if(typeof D!=="string"||!D||typeof Q!=="string")return"";if(Q=Q.split(":",1)[0],D=D.replace(/:\d*$/,""),Z=parseInt(Z)||aL9[Q]||0,!oL9(D,Z))return"";var G=fl("npm_config_"+Q+"_proxy")||fl(Q+"_proxy")||fl("npm_config_proxy")||fl("all_proxy");if(G&&G.indexOf("://")===-1)G=Q+"://"+G;return G}function oL9(A,B){var Q=(fl("npm_config_no_proxy")||fl("no_proxy")).toLowerCase();if(!Q)return!0;if(Q==="*")return!1;return Q.split(/[,\s]/).every(function(D){if(!D)return!0;var Z=D.match(/^(.+):(\d+)$/),G=Z?Z[1]:D,F=Z?parseInt(Z[2]):0;if(F&&F!==B)return!0;if(!/^[.*]/.test(G))return A!==G;if(G.charAt(0)==="*")G=G.slice(1);return!sL9.call(A,G)})}function fl(A){return process.env[A.toLowerCase()]||process.env[A.toUpperCase()]||""}tL9.getProxyForUrl=rL9});

module.exports = Pp0;
