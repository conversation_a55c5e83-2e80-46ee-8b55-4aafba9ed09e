// dependency_chain package extracted with entry point: NF8

var $q0=E((_Z3,eRB)=>{var J7=M4();j$();l11();Rv();zq0();Hq0();eE();MG1();a11();N8();gv1();var{asn1:K0,pki:C6}=J7,OG1=eRB.exports=J7.pkcs12=J7.pkcs12||{},tRB={name:"ContentInfo",tagClass:K0.Class.UNIVERSAL,type:K0.Type.SEQUENCE,constructed:!0,value:[{name:"ContentInfo.contentType",tagClass:K0.Class.UNIVERSAL,type:K0.Type.OID,constructed:!1,capture:"contentType"},{name:"ContentInfo.content",tagClass:K0.Class.CONTEXT_SPECIFIC,constructed:!0,captureAsn1:"content"}]},EG8={name:"PFX",tagClass:K0.Class.UNIVERSAL,type:K0.Type.SEQUENCE,constructed:!0,value:[{name:"PFX.version",tagClass:K0.Class.UNIVERSAL,type:K0.Type.INTEGER,constructed:!1,capture:"version"},tRB,{name:"PFX.macData",tagClass:K0.Class.UNIVERSAL,type:K0.Type.SEQUENCE,constructed:!0,optional:!0,captureAsn1:"mac",value:[{name:"PFX.macData.mac",tagClass:K0.Class.UNIVERSAL,type:K0.Type.SEQUENCE,constructed:!0,value:[{name:"PFX.macData.mac.digestAlgorithm",tagClass:K0.Class.UNIVERSAL,type:K0.Type.SEQUENCE,constructed:!0,value:[{name:"PFX.macData.mac.digestAlgorithm.algorithm",tagClass:K0.Class.UNIVERSAL,type:K0.Type.OID,constructed:!1,capture:"macAlgorithm"},{name:"PFX.macData.mac.digestAlgorithm.parameters",tagClass:K0.Class.UNIVERSAL,captureAsn1:"macAlgorithmParameters"}]},{name:"PFX.macData.mac.digest",tagClass:K0.Class.UNIVERSAL,type:K0.Type.OCTETSTRING,constructed:!1,capture:"macDigest"}]},{name:"PFX.macData.macSalt",tagClass:K0.Class.UNIVERSAL,type:K0.Type.OCTETSTRING,constructed:!1,capture:"macSalt"},{name:"PFX.macData.iterations",tagClass:K0.Class.UNIVERSAL,type:K0.Type.INTEGER,constructed:!1,optional:!0,capture:"macIterations"}]}]},UG8={name:"SafeBag",tagClass:K0.Class.UNIVERSAL,type:K0.Type.SEQUENCE,constructed:!0,value:[{name:"SafeBag.bagId",tagClass:K0.Class.UNIVERSAL,type:K0.Type.OID,constructed:!1,capture:"bagId"},{name:"SafeBag.bagValue",tagClass:K0.Class.CONTEXT_SPECIFIC,constructed:!0,captureAsn1:"bagValue"},{name:"SafeBag.bagAttributes",tagClass:K0.Class.UNIVERSAL,type:K0.Type.SET,constructed:!0,optional:!0,capture:"bagAttributes"}]},wG8={name:"Attribute",tagClass:K0.Class.UNIVERSAL,type:K0.Type.SEQUENCE,constructed:!0,value:[{name:"Attribute.attrId",tagClass:K0.Class.UNIVERSAL,type:K0.Type.OID,constructed:!1,capture:"oid"},{name:"Attribute.attrValues",tagClass:K0.Class.UNIVERSAL,type:K0.Type.SET,constructed:!0,capture:"values"}]},$G8={name:"CertBag",tagClass:K0.Class.UNIVERSAL,type:K0.Type.SEQUENCE,constructed:!0,value:[{name:"CertBag.certId",tagClass:K0.Class.UNIVERSAL,type:K0.Type.OID,constructed:!1,capture:"certId"},{name:"CertBag.certValue",tagClass:K0.Class.CONTEXT_SPECIFIC,constructed:!0,value:[{name:"CertBag.certValue[0]",tagClass:K0.Class.UNIVERSAL,type:K0.Class.OCTETSTRING,constructed:!1,capture:"cert"}]}]};function RG1(A,B,Q,D){var Z=[];for(var G=0;G<A.length;G++)for(var F=0;F<A[G].safeBags.length;F++){var I=A[G].safeBags[F];if(D!==void 0&&I.type!==D)continue;if(B===null){Z.push(I);continue}if(I.attributes[B]!==void 0&&I.attributes[B].indexOf(Q)>=0)Z.push(I)}return Z}OG1.pkcs12FromAsn1=function(A,B,Q){if(typeof B==="string")Q=B,B=!0;else if(B===void 0)B=!0;var D={},Z=[];if(!K0.validate(A,EG8,D,Z)){var G=new Error("Cannot read PKCS#12 PFX. ASN.1 object is not an PKCS#12 PFX.");throw G.errors=G,G}var F={version:D.version.charCodeAt(0),safeContents:[],getBags:function(z){var $={},L;if("localKeyId"in z)L=z.localKeyId;else if("localKeyIdHex"in z)L=J7.util.hexToBytes(z.localKeyIdHex);if(L===void 0&&!("friendlyName"in z)&&"bagType"in z)$[z.bagType]=RG1(F.safeContents,null,null,z.bagType);if(L!==void 0)$.localKeyId=RG1(F.safeContents,"localKeyId",L,z.bagType);if("friendlyName"in z)$.friendlyName=RG1(F.safeContents,"friendlyName",z.friendlyName,z.bagType);return $},getBagsByFriendlyName:function(z,$){return RG1(F.safeContents,"friendlyName",z,$)},getBagsByLocalKeyId:function(z,$){return RG1(F.safeContents,"localKeyId",z,$)}};if(D.version.charCodeAt(0)!==3){var G=new Error("PKCS#12 PFX of version other than 3 not supported.");throw G.version=D.version.charCodeAt(0),G}if(K0.derToOid(D.contentType)!==C6.oids.data){var G=new Error("Only PKCS#12 PFX in password integrity mode supported.");throw G.oid=K0.derToOid(D.contentType),G}var I=D.content.value[0];if(I.tagClass!==K0.Class.UNIVERSAL||I.type!==K0.Type.OCTETSTRING)throw new Error("PKCS#12 authSafe content data is not an OCTET STRING.");if(I=wq0(I),D.mac){var Y=null,W=0,J=K0.derToOid(D.macAlgorithm);switch(J){case C6.oids.sha1:Y=J7.md.sha1.create(),W=20;break;case C6.oids.sha256:Y=J7.md.sha256.create(),W=32;break;case C6.oids.sha384:Y=J7.md.sha384.create(),W=48;break;case C6.oids.sha512:Y=J7.md.sha512.create(),W=64;break;case C6.oids.md5:Y=J7.md.md5.create(),W=16;break}if(Y===null)throw new Error("PKCS#12 uses unsupported MAC algorithm: "+J);var X=new J7.util.ByteBuffer(D.macSalt),V="macIterations"in D?parseInt(J7.util.bytesToHex(D.macIterations),16):1,C=OG1.generateKey(Q,X,3,V,W,Y),K=J7.hmac.create();K.start(Y,C),K.update(I.value);var H=K.getMac();if(H.getBytes()!==D.macDigest)throw new Error("PKCS#12 MAC could not be verified. Invalid password?")}return qG8(F,I.value,B,Q),F};function wq0(A){if(A.composed||A.constructed){var B=J7.util.createBuffer();for(var Q=0;Q<A.value.length;++Q)B.putBytes(A.value[Q].value);A.composed=A.constructed=!1,A.value=B.getBytes()}return A}function qG8(A,B,Q,D){if(B=K0.fromDer(B,Q),B.tagClass!==K0.Class.UNIVERSAL||B.type!==K0.Type.SEQUENCE||B.constructed!==!0)throw new Error("PKCS#12 AuthenticatedSafe expected to be a SEQUENCE OF ContentInfo");for(var Z=0;Z<B.value.length;Z++){var G=B.value[Z],F={},I=[];if(!K0.validate(G,tRB,F,I)){var Y=new Error("Cannot read ContentInfo.");throw Y.errors=I,Y}var W={encrypted:!1},J=null,X=F.content.value[0];switch(K0.derToOid(F.contentType)){case C6.oids.data:if(X.tagClass!==K0.Class.UNIVERSAL||X.type!==K0.Type.OCTETSTRING)throw new Error("PKCS#12 SafeContents Data is not an OCTET STRING.");J=wq0(X).value;break;case C6.oids.encryptedData:J=NG8(X,D),W.encrypted=!0;break;default:var Y=new Error("Unsupported PKCS#12 contentType.");throw Y.contentType=K0.derToOid(F.contentType),Y}W.safeBags=LG8(J,Q,D),A.safeContents.push(W)}}function NG8(A,B){var Q={},D=[];if(!K0.validate(A,J7.pkcs7.asn1.encryptedDataValidator,Q,D)){var Z=new Error("Cannot read EncryptedContentInfo.");throw Z.errors=D,Z}var G=K0.derToOid(Q.contentType);if(G!==C6.oids.data){var Z=new Error("PKCS#12 EncryptedContentInfo ContentType is not Data.");throw Z.oid=G,Z}G=K0.derToOid(Q.encAlgorithm);var F=C6.pbe.getCipher(G,Q.encParameter,B),I=wq0(Q.encryptedContentAsn1),Y=J7.util.createBuffer(I.value);if(F.update(Y),!F.finish())throw new Error("Failed to decrypt PKCS#12 SafeContents.");return F.output.getBytes()}function LG8(A,B,Q){if(!B&&A.length===0)return[];if(A=K0.fromDer(A,B),A.tagClass!==K0.Class.UNIVERSAL||A.type!==K0.Type.SEQUENCE||A.constructed!==!0)throw new Error("PKCS#12 SafeContents expected to be a SEQUENCE OF SafeBag.");var D=[];for(var Z=0;Z<A.value.length;Z++){var G=A.value[Z],F={},I=[];if(!K0.validate(G,UG8,F,I)){var Y=new Error("Cannot read SafeBag.");throw Y.errors=I,Y}var W={type:K0.derToOid(F.bagId),attributes:MG8(F.bagAttributes)};D.push(W);var J,X,V=F.bagValue.value[0];switch(W.type){case C6.oids.pkcs8ShroudedKeyBag:if(V=C6.decryptPrivateKeyInfo(V,Q),V===null)throw new Error("Unable to decrypt PKCS#8 ShroudedKeyBag, wrong password?");case C6.oids.keyBag:try{W.key=C6.privateKeyFromAsn1(V)}catch(K){W.key=null,W.asn1=V}continue;case C6.oids.certBag:J=$G8,X=function(){if(K0.derToOid(F.certId)!==C6.oids.x509Certificate){var K=new Error("Unsupported certificate type, only X.509 supported.");throw K.oid=K0.derToOid(F.certId),K}var H=K0.fromDer(F.cert,B);try{W.cert=C6.certificateFromAsn1(H,!0)}catch(z){W.cert=null,W.asn1=H}};break;default:var Y=new Error("Unsupported PKCS#12 SafeBag type.");throw Y.oid=W.type,Y}if(J!==void 0&&!K0.validate(V,J,F,I)){var Y=new Error("Cannot read PKCS#12 "+J.name);throw Y.errors=I,Y}X()}return D}function MG8(A){var B={};if(A!==void 0)for(var Q=0;Q<A.length;++Q){var D={},Z=[];if(!K0.validate(A[Q],wG8,D,Z)){var G=new Error("Cannot read PKCS#12 BagAttribute.");throw G.errors=Z,G}var F=K0.derToOid(D.oid);if(C6.oids[F]===void 0)continue;B[C6.oids[F]]=[];for(var I=0;I<D.values.length;++I)B[C6.oids[F]].push(D.values[I].value)}return B}OG1.toPkcs12Asn1=function(A,B,Q,D){if(D=D||{},D.saltSize=D.saltSize||8,D.count=D.count||2048,D.algorithm=D.algorithm||D.encAlgorithm||"aes128",!("useMac"in D))D.useMac=!0;if(!("localKeyId"in D))D.localKeyId=null;if(!("generateLocalKeyId"in D))D.generateLocalKeyId=!0;var Z=D.localKeyId,G;if(Z!==null)Z=J7.util.hexToBytes(Z);else if(D.generateLocalKeyId)if(B){var F=J7.util.isArray(B)?B[0]:B;if(typeof F==="string")F=C6.certificateFromPem(F);var I=J7.md.sha1.create();I.update(K0.toDer(C6.certificateToAsn1(F)).getBytes()),Z=I.digest().getBytes()}else Z=J7.random.getBytes(20);var Y=[];if(Z!==null)Y.push(K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.OID,!1,K0.oidToDer(C6.oids.localKeyId).getBytes()),K0.create(K0.Class.UNIVERSAL,K0.Type.SET,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.OCTETSTRING,!1,Z)])]));if("friendlyName"in D)Y.push(K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.OID,!1,K0.oidToDer(C6.oids.friendlyName).getBytes()),K0.create(K0.Class.UNIVERSAL,K0.Type.SET,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.BMPSTRING,!1,D.friendlyName)])]));if(Y.length>0)G=K0.create(K0.Class.UNIVERSAL,K0.Type.SET,!0,Y);var W=[],J=[];if(B!==null)if(J7.util.isArray(B))J=B;else J=[B];var X=[];for(var V=0;V<J.length;++V){if(B=J[V],typeof B==="string")B=C6.certificateFromPem(B);var C=V===0?G:void 0,K=C6.certificateToAsn1(B),H=K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.OID,!1,K0.oidToDer(C6.oids.certBag).getBytes()),K0.create(K0.Class.CONTEXT_SPECIFIC,0,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.OID,!1,K0.oidToDer(C6.oids.x509Certificate).getBytes()),K0.create(K0.Class.CONTEXT_SPECIFIC,0,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.OCTETSTRING,!1,K0.toDer(K).getBytes())])])]),C]);X.push(H)}if(X.length>0){var z=K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,X),$=K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.OID,!1,K0.oidToDer(C6.oids.data).getBytes()),K0.create(K0.Class.CONTEXT_SPECIFIC,0,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.OCTETSTRING,!1,K0.toDer(z).getBytes())])]);W.push($)}var L=null;if(A!==null){var N=C6.wrapRsaPrivateKey(C6.privateKeyToAsn1(A));if(Q===null)L=K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.OID,!1,K0.oidToDer(C6.oids.keyBag).getBytes()),K0.create(K0.Class.CONTEXT_SPECIFIC,0,!0,[N]),G]);else L=K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.OID,!1,K0.oidToDer(C6.oids.pkcs8ShroudedKeyBag).getBytes()),K0.create(K0.Class.CONTEXT_SPECIFIC,0,!0,[C6.encryptPrivateKeyInfo(N,Q,D)]),G]);var O=K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,[L]),R=K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.OID,!1,K0.oidToDer(C6.oids.data).getBytes()),K0.create(K0.Class.CONTEXT_SPECIFIC,0,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.OCTETSTRING,!1,K0.toDer(O).getBytes())])]);W.push(R)}var T=K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,W),j;if(D.useMac){var I=J7.md.sha1.create(),f=new J7.util.ByteBuffer(J7.random.getBytes(D.saltSize)),k=D.count,A=OG1.generateKey(Q,f,3,k,20),c=J7.hmac.create();c.start(I,A),c.update(K0.toDer(T).getBytes());var h=c.getMac();j=K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.OID,!1,K0.oidToDer(C6.oids.sha1).getBytes()),K0.create(K0.Class.UNIVERSAL,K0.Type.NULL,!1,"")]),K0.create(K0.Class.UNIVERSAL,K0.Type.OCTETSTRING,!1,h.getBytes())]),K0.create(K0.Class.UNIVERSAL,K0.Type.OCTETSTRING,!1,f.getBytes()),K0.create(K0.Class.UNIVERSAL,K0.Type.INTEGER,!1,K0.integerToDer(k).getBytes())])}return K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.INTEGER,!1,K0.integerToDer(3).getBytes()),K0.create(K0.Class.UNIVERSAL,K0.Type.SEQUENCE,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.OID,!1,K0.oidToDer(C6.oids.data).getBytes()),K0.create(K0.Class.CONTEXT_SPECIFIC,0,!0,[K0.create(K0.Class.UNIVERSAL,K0.Type.OCTETSTRING,!1,K0.toDer(T).getBytes())])]),j])};OG1.generateKey=J7.pbe.generatePkcs12Key});
var Bq0=E((UZ3,JRB)=>{var gM=M4();bM();N8();var IRB=JRB.exports=gM.sha256=gM.sha256||{};gM.md.sha256=gM.md.algorithms.sha256=IRB;IRB.create=function(){if(!YRB)DD8();var A=null,B=gM.util.createBuffer(),Q=new Array(64),D={algorithm:"sha256",blockLength:64,digestLength:32,messageLength:0,fullMessageLength:null,messageLengthSize:8};return D.start=function(){D.messageLength=0,D.fullMessageLength=D.messageLength64=[];var Z=D.messageLengthSize/4;for(var G=0;G<Z;++G)D.fullMessageLength.push(0);return B=gM.util.createBuffer(),A={h0:1779033703,h1:3144134277,h2:1013904242,h3:2773480762,h4:1359893119,h5:2600822924,h6:528734635,h7:1541459225},D},D.start(),D.update=function(Z,G){if(G==="utf8")Z=gM.util.encodeUtf8(Z);var F=Z.length;D.messageLength+=F,F=[F/4294967296>>>0,F>>>0];for(var I=D.fullMessageLength.length-1;I>=0;--I)D.fullMessageLength[I]+=F[1],F[1]=F[0]+(D.fullMessageLength[I]/4294967296>>>0),D.fullMessageLength[I]=D.fullMessageLength[I]>>>0,F[0]=F[1]/4294967296>>>0;if(B.putBytes(Z),FRB(A,Q,B),B.read>2048||B.length()===0)B.compact();return D},D.digest=function(){var Z=gM.util.createBuffer();Z.putBytes(B.bytes());var G=D.fullMessageLength[D.fullMessageLength.length-1]+D.messageLengthSize,F=G&D.blockLength-1;Z.putBytes(Aq0.substr(0,D.blockLength-F));var I,Y,W=D.fullMessageLength[0]*8;for(var J=0;J<D.fullMessageLength.length-1;++J)I=D.fullMessageLength[J+1]*8,Y=I/4294967296>>>0,W+=Y,Z.putInt32(W>>>0),W=I>>>0;Z.putInt32(W);var X={h0:A.h0,h1:A.h1,h2:A.h2,h3:A.h3,h4:A.h4,h5:A.h5,h6:A.h6,h7:A.h7};FRB(X,Q,Z);var V=gM.util.createBuffer();return V.putInt32(X.h0),V.putInt32(X.h1),V.putInt32(X.h2),V.putInt32(X.h3),V.putInt32(X.h4),V.putInt32(X.h5),V.putInt32(X.h6),V.putInt32(X.h7),V},D};var Aq0=null,YRB=!1,WRB=null;function DD8(){Aq0=String.fromCharCode(128),Aq0+=gM.util.fillString(String.fromCharCode(0),64),WRB=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],YRB=!0}function FRB(A,B,Q){var D,Z,G,F,I,Y,W,J,X,V,C,K,H,z,$,L=Q.length();while(L>=64){for(W=0;W<16;++W)B[W]=Q.getInt32();for(;W<64;++W)D=B[W-2],D=(D>>>17|D<<15)^(D>>>19|D<<13)^D>>>10,Z=B[W-15],Z=(Z>>>7|Z<<25)^(Z>>>18|Z<<14)^Z>>>3,B[W]=D+B[W-7]+Z+B[W-16]|0;J=A.h0,X=A.h1,V=A.h2,C=A.h3,K=A.h4,H=A.h5,z=A.h6,$=A.h7;for(W=0;W<64;++W)F=(K>>>6|K<<26)^(K>>>11|K<<21)^(K>>>25|K<<7),I=z^K&(H^z),G=(J>>>2|J<<30)^(J>>>13|J<<19)^(J>>>22|J<<10),Y=J&X|V&(J^X),D=$+F+I+WRB[W]+B[W],Z=G+Y,$=z,z=H,H=K,K=C+D>>>0,C=V,V=X,X=J,J=D+Z>>>0;A.h0=A.h0+J|0,A.h1=A.h1+X|0,A.h2=A.h2+V|0,A.h3=A.h3+C|0,A.h4=A.h4+K|0,A.h5=A.h5+H|0,A.h6=A.h6+z|0,A.h7=A.h7+$|0,L-=64}}});
var Eq0=E((SZ3,cRB)=>{var tm=M4();N8();tm.mgf=tm.mgf||{};var YG8=cRB.exports=tm.mgf.mgf1=tm.mgf1=tm.mgf1||{};YG8.create=function(A){var B={generate:function(Q,D){var Z=new tm.util.ByteBuffer,G=Math.ceil(D/A.digestLength);for(var F=0;F<G;F++){var I=new tm.util.ByteBuffer;I.putInt32(F),A.start(),A.update(Q+I.getBytes()),Z.putBuffer(A.digest())}return Z.truncate(Z.length()-D),Z.getBytes()}};return B}});
var Gq0=E((qZ3,KRB)=>{var tV=M4();N8();var Zq0=[217,120,249,196,25,221,181,237,40,233,253,121,74,160,216,157,198,126,55,131,43,118,83,142,98,76,100,136,68,139,251,162,23,154,89,245,135,179,79,19,97,69,109,141,9,129,125,50,189,143,64,235,134,183,123,11,240,149,33,34,92,107,78,130,84,214,101,147,206,96,178,28,115,86,192,20,167,140,241,220,18,117,202,31,59,190,228,209,66,61,212,48,163,60,182,38,111,191,14,218,70,105,7,87,39,242,29,155,188,148,67,3,248,17,199,246,144,239,62,231,6,195,213,47,200,102,30,215,8,232,234,222,128,82,238,247,132,170,114,172,53,77,106,42,150,26,210,113,90,21,73,116,75,159,208,94,4,24,164,236,194,224,65,110,15,81,203,204,36,145,175,80,161,244,112,57,153,124,58,133,35,184,180,122,252,2,54,91,37,85,151,49,45,93,250,152,227,138,146,174,5,223,41,16,103,108,186,201,211,0,230,207,225,158,168,44,99,22,1,63,88,226,137,169,13,56,52,27,171,51,255,176,187,72,12,95,185,177,205,46,197,243,219,71,229,165,156,119,10,166,32,104,254,127,193,173],VRB=[1,2,3,5],GD8=function(A,B){return A<<B&65535|(A&65535)>>16-B},FD8=function(A,B){return(A&65535)>>B|A<<16-B&65535};KRB.exports=tV.rc2=tV.rc2||{};tV.rc2.expandKey=function(A,B){if(typeof A==="string")A=tV.util.createBuffer(A);B=B||128;var Q=A,D=A.length(),Z=B,G=Math.ceil(Z/8),F=255>>(Z&7),I;for(I=D;I<128;I++)Q.putByte(Zq0[Q.at(I-1)+Q.at(I-D)&255]);Q.setAt(128-G,Zq0[Q.at(128-G)&F]);for(I=127-G;I>=0;I--)Q.setAt(I,Zq0[Q.at(I+1)^Q.at(I+G)]);return Q};var CRB=function(A,B,Q){var D=!1,Z=null,G=null,F=null,I,Y,W,J,X=[];A=tV.rc2.expandKey(A,B);for(W=0;W<64;W++)X.push(A.getInt16Le());if(Q)I=function(K){for(W=0;W<4;W++)K[W]+=X[J]+(K[(W+3)%4]&K[(W+2)%4])+(~K[(W+3)%4]&K[(W+1)%4]),K[W]=GD8(K[W],VRB[W]),J++},Y=function(K){for(W=0;W<4;W++)K[W]+=X[K[(W+3)%4]&63]};else I=function(K){for(W=3;W>=0;W--)K[W]=FD8(K[W],VRB[W]),K[W]-=X[J]+(K[(W+3)%4]&K[(W+2)%4])+(~K[(W+3)%4]&K[(W+1)%4]),J--},Y=function(K){for(W=3;W>=0;W--)K[W]-=X[K[(W+3)%4]&63]};var V=function(K){var H=[];for(W=0;W<4;W++){var z=Z.getInt16Le();if(F!==null)if(Q)z^=F.getInt16Le();else F.putInt16Le(z);H.push(z&65535)}J=Q?0:63;for(var $=0;$<K.length;$++)for(var L=0;L<K[$][0];L++)K[$][1](H);for(W=0;W<4;W++){if(F!==null)if(Q)F.putInt16Le(H[W]);else H[W]^=F.getInt16Le();G.putInt16Le(H[W])}},C=null;return C={start:function(K,H){if(K){if(typeof K==="string")K=tV.util.createBuffer(K)}D=!1,Z=tV.util.createBuffer(),G=H||new tV.util.createBuffer,F=K,C.output=G},update:function(K){if(!D)Z.putBuffer(K);while(Z.length()>=8)V([[5,I],[1,Y],[6,I],[1,Y],[5,I]])},finish:function(K){var H=!0;if(Q)if(K)H=K(8,Z,!Q);else{var z=Z.length()===8?8:8-Z.length();Z.fillWithByte(z,z)}if(H)D=!0,C.update();if(!Q){if(H=Z.length()===0,H)if(K)H=K(8,G,!Q);else{var $=G.length(),L=G.at($-1);if(L>$)H=!1;else G.truncate(L)}}return H}},C};tV.rc2.startEncrypting=function(A,B,Q){var D=tV.rc2.createEncryptionCipher(A,128);return D.start(B,Q),D};tV.rc2.createEncryptionCipher=function(A,B){return CRB(A,B,!0)};tV.rc2.startDecrypting=function(A,B,Q){var D=tV.rc2.createDecryptionCipher(A,128);return D.start(B,Q),D};tV.rc2.createDecryptionCipher=function(A,B){return CRB(A,B,!1)}});
var Hq0=E((TZ3,gRB)=>{var IB=M4();Mv();j$();qG1();bM();Rv();Sv1();am();eE();Gq0();MG1();N8();if(typeof Kq0==="undefined")Kq0=IB.jsbn.BigInteger;var Kq0,CA=IB.asn1,$4=IB.pki=IB.pki||{};gRB.exports=$4.pbe=IB.pbe=IB.pbe||{};var om=$4.oids,DG8={name:"EncryptedPrivateKeyInfo",tagClass:CA.Class.UNIVERSAL,type:CA.Type.SEQUENCE,constructed:!0,value:[{name:"EncryptedPrivateKeyInfo.encryptionAlgorithm",tagClass:CA.Class.UNIVERSAL,type:CA.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:CA.Class.UNIVERSAL,type:CA.Type.OID,constructed:!1,capture:"encryptionOid"},{name:"AlgorithmIdentifier.parameters",tagClass:CA.Class.UNIVERSAL,type:CA.Type.SEQUENCE,constructed:!0,captureAsn1:"encryptionParams"}]},{name:"EncryptedPrivateKeyInfo.encryptedData",tagClass:CA.Class.UNIVERSAL,type:CA.Type.OCTETSTRING,constructed:!1,capture:"encryptedData"}]},ZG8={name:"PBES2Algorithms",tagClass:CA.Class.UNIVERSAL,type:CA.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.keyDerivationFunc",tagClass:CA.Class.UNIVERSAL,type:CA.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.keyDerivationFunc.oid",tagClass:CA.Class.UNIVERSAL,type:CA.Type.OID,constructed:!1,capture:"kdfOid"},{name:"PBES2Algorithms.params",tagClass:CA.Class.UNIVERSAL,type:CA.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.params.salt",tagClass:CA.Class.UNIVERSAL,type:CA.Type.OCTETSTRING,constructed:!1,capture:"kdfSalt"},{name:"PBES2Algorithms.params.iterationCount",tagClass:CA.Class.UNIVERSAL,type:CA.Type.INTEGER,constructed:!1,capture:"kdfIterationCount"},{name:"PBES2Algorithms.params.keyLength",tagClass:CA.Class.UNIVERSAL,type:CA.Type.INTEGER,constructed:!1,optional:!0,capture:"keyLength"},{name:"PBES2Algorithms.params.prf",tagClass:CA.Class.UNIVERSAL,type:CA.Type.SEQUENCE,constructed:!0,optional:!0,value:[{name:"PBES2Algorithms.params.prf.algorithm",tagClass:CA.Class.UNIVERSAL,type:CA.Type.OID,constructed:!1,capture:"prfOid"}]}]}]},{name:"PBES2Algorithms.encryptionScheme",tagClass:CA.Class.UNIVERSAL,type:CA.Type.SEQUENCE,constructed:!0,value:[{name:"PBES2Algorithms.encryptionScheme.oid",tagClass:CA.Class.UNIVERSAL,type:CA.Type.OID,constructed:!1,capture:"encOid"},{name:"PBES2Algorithms.encryptionScheme.iv",tagClass:CA.Class.UNIVERSAL,type:CA.Type.OCTETSTRING,constructed:!1,capture:"encIv"}]}]},GG8={name:"pkcs-12PbeParams",tagClass:CA.Class.UNIVERSAL,type:CA.Type.SEQUENCE,constructed:!0,value:[{name:"pkcs-12PbeParams.salt",tagClass:CA.Class.UNIVERSAL,type:CA.Type.OCTETSTRING,constructed:!1,capture:"salt"},{name:"pkcs-12PbeParams.iterations",tagClass:CA.Class.UNIVERSAL,type:CA.Type.INTEGER,constructed:!1,capture:"iterations"}]};$4.encryptPrivateKeyInfo=function(A,B,Q){Q=Q||{},Q.saltSize=Q.saltSize||8,Q.count=Q.count||2048,Q.algorithm=Q.algorithm||"aes128",Q.prfAlgorithm=Q.prfAlgorithm||"sha1";var D=IB.random.getBytesSync(Q.saltSize),Z=Q.count,G=CA.integerToDer(Z),F,I,Y;if(Q.algorithm.indexOf("aes")===0||Q.algorithm==="des"){var W,J,X;switch(Q.algorithm){case"aes128":F=16,W=16,J=om["aes128-CBC"],X=IB.aes.createEncryptionCipher;break;case"aes192":F=24,W=16,J=om["aes192-CBC"],X=IB.aes.createEncryptionCipher;break;case"aes256":F=32,W=16,J=om["aes256-CBC"],X=IB.aes.createEncryptionCipher;break;case"des":F=8,W=8,J=om.desCBC,X=IB.des.createEncryptionCipher;break;default:var V=new Error("Cannot encrypt private key. Unknown encryption algorithm.");throw V.algorithm=Q.algorithm,V}var C="hmacWith"+Q.prfAlgorithm.toUpperCase(),K=hRB(C),H=IB.pkcs5.pbkdf2(B,D,Z,F,K),z=IB.random.getBytesSync(W),$=X(H);$.start(z),$.update(CA.toDer(A)),$.finish(),Y=$.output.getBytes();var L=FG8(D,G,F,C);I=CA.create(CA.Class.UNIVERSAL,CA.Type.SEQUENCE,!0,[CA.create(CA.Class.UNIVERSAL,CA.Type.OID,!1,CA.oidToDer(om.pkcs5PBES2).getBytes()),CA.create(CA.Class.UNIVERSAL,CA.Type.SEQUENCE,!0,[CA.create(CA.Class.UNIVERSAL,CA.Type.SEQUENCE,!0,[CA.create(CA.Class.UNIVERSAL,CA.Type.OID,!1,CA.oidToDer(om.pkcs5PBKDF2).getBytes()),L]),CA.create(CA.Class.UNIVERSAL,CA.Type.SEQUENCE,!0,[CA.create(CA.Class.UNIVERSAL,CA.Type.OID,!1,CA.oidToDer(J).getBytes()),CA.create(CA.Class.UNIVERSAL,CA.Type.OCTETSTRING,!1,z)])])])}else if(Q.algorithm==="3des"){F=24;var N=new IB.util.ByteBuffer(D),H=$4.pbe.generatePkcs12Key(B,N,1,Z,F),z=$4.pbe.generatePkcs12Key(B,N,2,Z,F),$=IB.des.createEncryptionCipher(H);$.start(z),$.update(CA.toDer(A)),$.finish(),Y=$.output.getBytes(),I=CA.create(CA.Class.UNIVERSAL,CA.Type.SEQUENCE,!0,[CA.create(CA.Class.UNIVERSAL,CA.Type.OID,!1,CA.oidToDer(om["pbeWithSHAAnd3-KeyTripleDES-CBC"]).getBytes()),CA.create(CA.Class.UNIVERSAL,CA.Type.SEQUENCE,!0,[CA.create(CA.Class.UNIVERSAL,CA.Type.OCTETSTRING,!1,D),CA.create(CA.Class.UNIVERSAL,CA.Type.INTEGER,!1,G.getBytes())])])}else{var V=new Error("Cannot encrypt private key. Unknown encryption algorithm.");throw V.algorithm=Q.algorithm,V}var O=CA.create(CA.Class.UNIVERSAL,CA.Type.SEQUENCE,!0,[I,CA.create(CA.Class.UNIVERSAL,CA.Type.OCTETSTRING,!1,Y)]);return O};$4.decryptPrivateKeyInfo=function(A,B){var Q=null,D={},Z=[];if(!CA.validate(A,DG8,D,Z)){var G=new Error("Cannot read encrypted private key. ASN.1 object is not a supported EncryptedPrivateKeyInfo.");throw G.errors=Z,G}var F=CA.derToOid(D.encryptionOid),I=$4.pbe.getCipher(F,D.encryptionParams,B),Y=IB.util.createBuffer(D.encryptedData);if(I.update(Y),I.finish())Q=CA.fromDer(I.output);return Q};$4.encryptedPrivateKeyToPem=function(A,B){var Q={type:"ENCRYPTED PRIVATE KEY",body:CA.toDer(A).getBytes()};return IB.pem.encode(Q,{maxline:B})};$4.encryptedPrivateKeyFromPem=function(A){var B=IB.pem.decode(A)[0];if(B.type!=="ENCRYPTED PRIVATE KEY"){var Q=new Error('Could not convert encrypted private key from PEM; PEM header type is "ENCRYPTED PRIVATE KEY".');throw Q.headerType=B.type,Q}if(B.procType&&B.procType.type==="ENCRYPTED")throw new Error("Could not convert encrypted private key from PEM; PEM is encrypted.");return CA.fromDer(B.body)};$4.encryptRsaPrivateKey=function(A,B,Q){if(Q=Q||{},!Q.legacy){var D=$4.wrapRsaPrivateKey($4.privateKeyToAsn1(A));return D=$4.encryptPrivateKeyInfo(D,B,Q),$4.encryptedPrivateKeyToPem(D)}var Z,G,F,I;switch(Q.algorithm){case"aes128":Z="AES-128-CBC",F=16,G=IB.random.getBytesSync(16),I=IB.aes.createEncryptionCipher;break;case"aes192":Z="AES-192-CBC",F=24,G=IB.random.getBytesSync(16),I=IB.aes.createEncryptionCipher;break;case"aes256":Z="AES-256-CBC",F=32,G=IB.random.getBytesSync(16),I=IB.aes.createEncryptionCipher;break;case"3des":Z="DES-EDE3-CBC",F=24,G=IB.random.getBytesSync(8),I=IB.des.createEncryptionCipher;break;case"des":Z="DES-CBC",F=8,G=IB.random.getBytesSync(8),I=IB.des.createEncryptionCipher;break;default:var Y=new Error('Could not encrypt RSA private key; unsupported encryption algorithm "'+Q.algorithm+'".');throw Y.algorithm=Q.algorithm,Y}var W=IB.pbe.opensslDeriveBytes(B,G.substr(0,8),F),J=I(W);J.start(G),J.update(CA.toDer($4.privateKeyToAsn1(A))),J.finish();var X={type:"RSA PRIVATE KEY",procType:{version:"4",type:"ENCRYPTED"},dekInfo:{algorithm:Z,parameters:IB.util.bytesToHex(G).toUpperCase()},body:J.output.getBytes()};return IB.pem.encode(X)};$4.decryptRsaPrivateKey=function(A,B){var Q=null,D=IB.pem.decode(A)[0];if(D.type!=="ENCRYPTED PRIVATE KEY"&&D.type!=="PRIVATE KEY"&&D.type!=="RSA PRIVATE KEY"){var Z=new Error('Could not convert private key from PEM; PEM header type is not "ENCRYPTED PRIVATE KEY", "PRIVATE KEY", or "RSA PRIVATE KEY".');throw Z.headerType=Z,Z}if(D.procType&&D.procType.type==="ENCRYPTED"){var G,F;switch(D.dekInfo.algorithm){case"DES-CBC":G=8,F=IB.des.createDecryptionCipher;break;case"DES-EDE3-CBC":G=24,F=IB.des.createDecryptionCipher;break;case"AES-128-CBC":G=16,F=IB.aes.createDecryptionCipher;break;case"AES-192-CBC":G=24,F=IB.aes.createDecryptionCipher;break;case"AES-256-CBC":G=32,F=IB.aes.createDecryptionCipher;break;case"RC2-40-CBC":G=5,F=function(X){return IB.rc2.createDecryptionCipher(X,40)};break;case"RC2-64-CBC":G=8,F=function(X){return IB.rc2.createDecryptionCipher(X,64)};break;case"RC2-128-CBC":G=16,F=function(X){return IB.rc2.createDecryptionCipher(X,128)};break;default:var Z=new Error('Could not decrypt private key; unsupported encryption algorithm "'+D.dekInfo.algorithm+'".');throw Z.algorithm=D.dekInfo.algorithm,Z}var I=IB.util.hexToBytes(D.dekInfo.parameters),Y=IB.pbe.opensslDeriveBytes(B,I.substr(0,8),G),W=F(Y);if(W.start(I),W.update(IB.util.createBuffer(D.body)),W.finish())Q=W.output.getBytes();else return Q}else Q=D.body;if(D.type==="ENCRYPTED PRIVATE KEY")Q=$4.decryptPrivateKeyInfo(CA.fromDer(Q),B);else Q=CA.fromDer(Q);if(Q!==null)Q=$4.privateKeyFromAsn1(Q);return Q};$4.pbe.generatePkcs12Key=function(A,B,Q,D,Z,G){var F,I;if(typeof G==="undefined"||G===null){if(!("sha1"in IB.md))throw new Error('"sha1" hash algorithm unavailable.');G=IB.md.sha1.create()}var{digestLength:Y,blockLength:W}=G,J=new IB.util.ByteBuffer,X=new IB.util.ByteBuffer;if(A!==null&&A!==void 0){for(I=0;I<A.length;I++)X.putInt16(A.charCodeAt(I));X.putInt16(0)}var V=X.length(),C=B.length(),K=new IB.util.ByteBuffer;K.fillWithByte(Q,W);var H=W*Math.ceil(C/W),z=new IB.util.ByteBuffer;for(I=0;I<H;I++)z.putByte(B.at(I%C));var $=W*Math.ceil(V/W),L=new IB.util.ByteBuffer;for(I=0;I<$;I++)L.putByte(X.at(I%V));var N=z;N.putBuffer(L);var O=Math.ceil(Z/Y);for(var R=1;R<=O;R++){var T=new IB.util.ByteBuffer;T.putBytes(K.bytes()),T.putBytes(N.bytes());for(var j=0;j<D;j++)G.start(),G.update(T.getBytes()),T=G.digest();var f=new IB.util.ByteBuffer;for(I=0;I<W;I++)f.putByte(T.at(I%Y));var k=Math.ceil(C/W)+Math.ceil(V/W),c=new IB.util.ByteBuffer;for(F=0;F<k;F++){var h=new IB.util.ByteBuffer(N.getBytes(W)),n=511;for(I=f.length()-1;I>=0;I--)n=n>>8,n+=f.at(I)+h.at(I),h.setAt(I,n&255);c.putBuffer(h)}N=c,J.putBuffer(T)}return J.truncate(J.length()-Z),J};$4.pbe.getCipher=function(A,B,Q){switch(A){case $4.oids.pkcs5PBES2:return $4.pbe.getCipherForPBES2(A,B,Q);case $4.oids["pbeWithSHAAnd3-KeyTripleDES-CBC"]:case $4.oids["pbewithSHAAnd40BitRC2-CBC"]:return $4.pbe.getCipherForPKCS12PBE(A,B,Q);default:var D=new Error("Cannot read encrypted PBE data block. Unsupported OID.");throw D.oid=A,D.supportedOids=["pkcs5PBES2","pbeWithSHAAnd3-KeyTripleDES-CBC","pbewithSHAAnd40BitRC2-CBC"],D}};$4.pbe.getCipherForPBES2=function(A,B,Q){var D={},Z=[];if(!CA.validate(B,ZG8,D,Z)){var G=new Error("Cannot read password-based-encryption algorithm parameters. ASN.1 object is not a supported EncryptedPrivateKeyInfo.");throw G.errors=Z,G}if(A=CA.derToOid(D.kdfOid),A!==$4.oids.pkcs5PBKDF2){var G=new Error("Cannot read encrypted private key. Unsupported key derivation function OID.");throw G.oid=A,G.supportedOids=["pkcs5PBKDF2"],G}if(A=CA.derToOid(D.encOid),A!==$4.oids["aes128-CBC"]&&A!==$4.oids["aes192-CBC"]&&A!==$4.oids["aes256-CBC"]&&A!==$4.oids["des-EDE3-CBC"]&&A!==$4.oids.desCBC){var G=new Error("Cannot read encrypted private key. Unsupported encryption scheme OID.");throw G.oid=A,G.supportedOids=["aes128-CBC","aes192-CBC","aes256-CBC","des-EDE3-CBC","desCBC"],G}var F=D.kdfSalt,I=IB.util.createBuffer(D.kdfIterationCount);I=I.getInt(I.length()<<3);var Y,W;switch($4.oids[A]){case"aes128-CBC":Y=16,W=IB.aes.createDecryptionCipher;break;case"aes192-CBC":Y=24,W=IB.aes.createDecryptionCipher;break;case"aes256-CBC":Y=32,W=IB.aes.createDecryptionCipher;break;case"des-EDE3-CBC":Y=24,W=IB.des.createDecryptionCipher;break;case"desCBC":Y=8,W=IB.des.createDecryptionCipher;break}var J=fRB(D.prfOid),X=IB.pkcs5.pbkdf2(Q,F,I,Y,J),V=D.encIv,C=W(X);return C.start(V),C};$4.pbe.getCipherForPKCS12PBE=function(A,B,Q){var D={},Z=[];if(!CA.validate(B,GG8,D,Z)){var G=new Error("Cannot read password-based-encryption algorithm parameters. ASN.1 object is not a supported EncryptedPrivateKeyInfo.");throw G.errors=Z,G}var F=IB.util.createBuffer(D.salt),I=IB.util.createBuffer(D.iterations);I=I.getInt(I.length()<<3);var Y,W,J;switch(A){case $4.oids["pbeWithSHAAnd3-KeyTripleDES-CBC"]:Y=24,W=8,J=IB.des.startDecrypting;break;case $4.oids["pbewithSHAAnd40BitRC2-CBC"]:Y=5,W=8,J=function(H,z){var $=IB.rc2.createDecryptionCipher(H,40);return $.start(z,null),$};break;default:var G=new Error("Cannot read PKCS #12 PBE data block. Unsupported OID.");throw G.oid=A,G}var X=fRB(D.prfOid),V=$4.pbe.generatePkcs12Key(Q,F,1,I,Y,X);X.start();var C=$4.pbe.generatePkcs12Key(Q,F,2,I,W,X);return J(V,C)};$4.pbe.opensslDeriveBytes=function(A,B,Q,D){if(typeof D==="undefined"||D===null){if(!("md5"in IB.md))throw new Error('"md5" hash algorithm unavailable.');D=IB.md.md5.create()}if(B===null)B="";var Z=[bRB(D,A+B)];for(var G=16,F=1;G<Q;++F,G+=16)Z.push(bRB(D,Z[F-1]+A+B));return Z.join("").substr(0,Q)};function bRB(A,B){return A.start().update(B).digest().getBytes()}function fRB(A){var B;if(!A)B="hmacWithSHA1";else if(B=$4.oids[CA.derToOid(A)],!B){var Q=new Error("Unsupported PRF OID.");throw Q.oid=A,Q.supported=["hmacWithSHA1","hmacWithSHA224","hmacWithSHA256","hmacWithSHA384","hmacWithSHA512"],Q}return hRB(B)}function hRB(A){var B=IB.md;switch(A){case"hmacWithSHA224":B=IB.md.sha512;case"hmacWithSHA1":case"hmacWithSHA256":case"hmacWithSHA384":case"hmacWithSHA512":A=A.substr(8).toLowerCase();break;default:var Q=new Error("Unsupported PRF algorithm.");throw Q.algorithm=A,Q.supported=["hmacWithSHA1","hmacWithSHA224","hmacWithSHA256","hmacWithSHA384","hmacWithSHA512"],Q}if(!B||!(A in B))throw new Error("Unknown hash algorithm: "+A);return B[A].create()}function FG8(A,B,Q,D){var Z=CA.create(CA.Class.UNIVERSAL,CA.Type.SEQUENCE,!0,[CA.create(CA.Class.UNIVERSAL,CA.Type.OCTETSTRING,!1,A),CA.create(CA.Class.UNIVERSAL,CA.Type.INTEGER,!1,B.getBytes())]);if(D!=="hmacWithSHA1")Z.value.push(CA.create(CA.Class.UNIVERSAL,CA.Type.INTEGER,!1,IB.util.hexToBytes(Q.toString(16))),CA.create(CA.Class.UNIVERSAL,CA.Type.SEQUENCE,!0,[CA.create(CA.Class.UNIVERSAL,CA.Type.OID,!1,CA.oidToDer($4.oids[D]).getBytes()),CA.create(CA.Class.UNIVERSAL,CA.Type.NULL,!1,"")]));return Z}});
var Jq0=E((MZ3,PRB)=>{var dM=M4();N8();eE();a11();var TRB=PRB.exports=dM.pkcs1=dM.pkcs1||{};TRB.encode_rsa_oaep=function(A,B,Q){var D,Z,G,F;if(typeof Q==="string")D=Q,Z=arguments[3]||void 0,G=arguments[4]||void 0;else if(Q){if(D=Q.label||void 0,Z=Q.seed||void 0,G=Q.md||void 0,Q.mgf1&&Q.mgf1.md)F=Q.mgf1.md}if(!G)G=dM.md.sha1.create();else G.start();if(!F)F=G;var I=Math.ceil(A.n.bitLength()/8),Y=I-2*G.digestLength-2;if(B.length>Y){var W=new Error("RSAES-OAEP input message length is too long.");throw W.length=B.length,W.maxLength=Y,W}if(!D)D="";G.update(D,"raw");var J=G.digest(),X="",V=Y-B.length;for(var C=0;C<V;C++)X+="\x00";var K=J.getBytes()+X+"\x01"+B;if(!Z)Z=dM.random.getBytes(G.digestLength);else if(Z.length!==G.digestLength){var W=new Error("Invalid RSAES-OAEP seed. The seed length must match the digest length.");throw W.seedLength=Z.length,W.digestLength=G.digestLength,W}var H=_v1(Z,I-G.digestLength-1,F),z=dM.util.xorBytes(K,H,K.length),$=_v1(z,G.digestLength,F),L=dM.util.xorBytes(Z,$,Z.length);return"\x00"+L+z};TRB.decode_rsa_oaep=function(A,B,Q){var D,Z,G;if(typeof Q==="string")D=Q,Z=arguments[3]||void 0;else if(Q){if(D=Q.label||void 0,Z=Q.md||void 0,Q.mgf1&&Q.mgf1.md)G=Q.mgf1.md}var F=Math.ceil(A.n.bitLength()/8);if(B.length!==F){var z=new Error("RSAES-OAEP encoded message length is invalid.");throw z.length=B.length,z.expectedLength=F,z}if(Z===void 0)Z=dM.md.sha1.create();else Z.start();if(!G)G=Z;if(F<2*Z.digestLength+2)throw new Error("RSAES-OAEP key is too short for the hash function.");if(!D)D="";Z.update(D,"raw");var I=Z.digest().getBytes(),Y=B.charAt(0),W=B.substring(1,Z.digestLength+1),J=B.substring(1+Z.digestLength),X=_v1(J,Z.digestLength,G),V=dM.util.xorBytes(W,X,W.length),C=_v1(V,F-Z.digestLength-1,G),K=dM.util.xorBytes(J,C,J.length),H=K.substring(0,Z.digestLength),z=Y!=="\x00";for(var $=0;$<Z.digestLength;++$)z|=I.charAt($)!==H.charAt($);var L=1,N=Z.digestLength;for(var O=Z.digestLength;O<K.length;O++){var R=K.charCodeAt(O),T=R&1^1,j=L?65534:0;z|=R&j,L=L&T,N+=L}if(z||K.charCodeAt(N)!==1)throw new Error("Invalid RSAES-OAEP padding.");return K.substring(N+1)};function _v1(A,B,Q){if(!Q)Q=dM.md.sha1.create();var D="",Z=Math.ceil(B/Q.digestLength);for(var G=0;G<Z;++G){var F=String.fromCharCode(G>>24&255,G>>16&255,G>>8&255,G&255);Q.start(),Q.update(A+F),D+=Q.digest().getBytes()}return D.substring(0,B)}});
var LG1=E((NZ3,NRB)=>{var Fq0=M4();NRB.exports=Fq0.jsbn=Fq0.jsbn||{};var GS,ID8=244837814094590,HRB=(ID8&16777215)==15715070;function _A(A,B,Q){if(this.data=[],A!=null)if(typeof A=="number")this.fromNumber(A,B,Q);else if(B==null&&typeof A!="string")this.fromString(A,256);else this.fromString(A,B)}Fq0.jsbn.BigInteger=_A;function L8(){return new _A(null)}function YD8(A,B,Q,D,Z,G){while(--G>=0){var F=B*this.data[A++]+Q.data[D]+Z;Z=Math.floor(F/67108864),Q.data[D++]=F&67108863}return Z}function WD8(A,B,Q,D,Z,G){var F=B&32767,I=B>>15;while(--G>=0){var Y=this.data[A]&32767,W=this.data[A++]>>15,J=I*Y+W*F;Y=F*Y+((J&32767)<<15)+Q.data[D]+(Z&1073741823),Z=(Y>>>30)+(J>>>15)+I*W+(Z>>>30),Q.data[D++]=Y&1073741823}return Z}function zRB(A,B,Q,D,Z,G){var F=B&16383,I=B>>14;while(--G>=0){var Y=this.data[A]&16383,W=this.data[A++]>>14,J=I*Y+W*F;Y=F*Y+((J&16383)<<14)+Q.data[D]+Z,Z=(Y>>28)+(J>>14)+I*W,Q.data[D++]=Y&268435455}return Z}if(typeof navigator==="undefined")_A.prototype.am=zRB,GS=28;else if(HRB&&navigator.appName=="Microsoft Internet Explorer")_A.prototype.am=WD8,GS=30;else if(HRB&&navigator.appName!="Netscape")_A.prototype.am=YD8,GS=26;else _A.prototype.am=zRB,GS=28;_A.prototype.DB=GS;_A.prototype.DM=(1<<GS)-1;_A.prototype.DV=1<<GS;var Iq0=52;_A.prototype.FV=Math.pow(2,Iq0);_A.prototype.F1=Iq0-GS;_A.prototype.F2=2*GS-Iq0;var JD8="0123456789abcdefghijklmnopqrstuvwxyz",kv1=new Array,i11,AU;i11=48;for(AU=0;AU<=9;++AU)kv1[i11++]=AU;i11=97;for(AU=10;AU<36;++AU)kv1[i11++]=AU;i11=65;for(AU=10;AU<36;++AU)kv1[i11++]=AU;function ERB(A){return JD8.charAt(A)}function URB(A,B){var Q=kv1[A.charCodeAt(B)];return Q==null?-1:Q}function XD8(A){for(var B=this.t-1;B>=0;--B)A.data[B]=this.data[B];A.t=this.t,A.s=this.s}function VD8(A){if(this.t=1,this.s=A<0?-1:0,A>0)this.data[0]=A;else if(A<-1)this.data[0]=A+this.DV;else this.t=0}function Ov(A){var B=L8();return B.fromInt(A),B}function CD8(A,B){var Q;if(B==16)Q=4;else if(B==8)Q=3;else if(B==256)Q=8;else if(B==2)Q=1;else if(B==32)Q=5;else if(B==4)Q=2;else{this.fromRadix(A,B);return}this.t=0,this.s=0;var D=A.length,Z=!1,G=0;while(--D>=0){var F=Q==8?A[D]&255:URB(A,D);if(F<0){if(A.charAt(D)=="-")Z=!0;continue}if(Z=!1,G==0)this.data[this.t++]=F;else if(G+Q>this.DB)this.data[this.t-1]|=(F&(1<<this.DB-G)-1)<<G,this.data[this.t++]=F>>this.DB-G;else this.data[this.t-1]|=F<<G;if(G+=Q,G>=this.DB)G-=this.DB}if(Q==8&&(A[0]&128)!=0){if(this.s=-1,G>0)this.data[this.t-1]|=(1<<this.DB-G)-1<<G}if(this.clamp(),Z)_A.ZERO.subTo(this,this)}function KD8(){var A=this.s&this.DM;while(this.t>0&&this.data[this.t-1]==A)--this.t}function HD8(A){if(this.s<0)return"-"+this.negate().toString(A);var B;if(A==16)B=4;else if(A==8)B=3;else if(A==2)B=1;else if(A==32)B=5;else if(A==4)B=2;else return this.toRadix(A);var Q=(1<<B)-1,D,Z=!1,G="",F=this.t,I=this.DB-F*this.DB%B;if(F-- >0){if(I<this.DB&&(D=this.data[F]>>I)>0)Z=!0,G=ERB(D);while(F>=0){if(I<B)D=(this.data[F]&(1<<I)-1)<<B-I,D|=this.data[--F]>>(I+=this.DB-B);else if(D=this.data[F]>>(I-=B)&Q,I<=0)I+=this.DB,--F;if(D>0)Z=!0;if(Z)G+=ERB(D)}}return Z?G:"0"}function zD8(){var A=L8();return _A.ZERO.subTo(this,A),A}function ED8(){return this.s<0?this.negate():this}function UD8(A){var B=this.s-A.s;if(B!=0)return B;var Q=this.t;if(B=Q-A.t,B!=0)return this.s<0?-B:B;while(--Q>=0)if((B=this.data[Q]-A.data[Q])!=0)return B;return 0}function yv1(A){var B=1,Q;if((Q=A>>>16)!=0)A=Q,B+=16;if((Q=A>>8)!=0)A=Q,B+=8;if((Q=A>>4)!=0)A=Q,B+=4;if((Q=A>>2)!=0)A=Q,B+=2;if((Q=A>>1)!=0)A=Q,B+=1;return B}function wD8(){if(this.t<=0)return 0;return this.DB*(this.t-1)+yv1(this.data[this.t-1]^this.s&this.DM)}function $D8(A,B){var Q;for(Q=this.t-1;Q>=0;--Q)B.data[Q+A]=this.data[Q];for(Q=A-1;Q>=0;--Q)B.data[Q]=0;B.t=this.t+A,B.s=this.s}function qD8(A,B){for(var Q=A;Q<this.t;++Q)B.data[Q-A]=this.data[Q];B.t=Math.max(this.t-A,0),B.s=this.s}function ND8(A,B){var Q=A%this.DB,D=this.DB-Q,Z=(1<<D)-1,G=Math.floor(A/this.DB),F=this.s<<Q&this.DM,I;for(I=this.t-1;I>=0;--I)B.data[I+G+1]=this.data[I]>>D|F,F=(this.data[I]&Z)<<Q;for(I=G-1;I>=0;--I)B.data[I]=0;B.data[G]=F,B.t=this.t+G+1,B.s=this.s,B.clamp()}function LD8(A,B){B.s=this.s;var Q=Math.floor(A/this.DB);if(Q>=this.t){B.t=0;return}var D=A%this.DB,Z=this.DB-D,G=(1<<D)-1;B.data[0]=this.data[Q]>>D;for(var F=Q+1;F<this.t;++F)B.data[F-Q-1]|=(this.data[F]&G)<<Z,B.data[F-Q]=this.data[F]>>D;if(D>0)B.data[this.t-Q-1]|=(this.s&G)<<Z;B.t=this.t-Q,B.clamp()}function MD8(A,B){var Q=0,D=0,Z=Math.min(A.t,this.t);while(Q<Z)D+=this.data[Q]-A.data[Q],B.data[Q++]=D&this.DM,D>>=this.DB;if(A.t<this.t){D-=A.s;while(Q<this.t)D+=this.data[Q],B.data[Q++]=D&this.DM,D>>=this.DB;D+=this.s}else{D+=this.s;while(Q<A.t)D-=A.data[Q],B.data[Q++]=D&this.DM,D>>=this.DB;D-=A.s}if(B.s=D<0?-1:0,D<-1)B.data[Q++]=this.DV+D;else if(D>0)B.data[Q++]=D;B.t=Q,B.clamp()}function RD8(A,B){var Q=this.abs(),D=A.abs(),Z=Q.t;B.t=Z+D.t;while(--Z>=0)B.data[Z]=0;for(Z=0;Z<D.t;++Z)B.data[Z+Q.t]=Q.am(0,D.data[Z],B,Z,0,Q.t);if(B.s=0,B.clamp(),this.s!=A.s)_A.ZERO.subTo(B,B)}function OD8(A){var B=this.abs(),Q=A.t=2*B.t;while(--Q>=0)A.data[Q]=0;for(Q=0;Q<B.t-1;++Q){var D=B.am(Q,B.data[Q],A,2*Q,0,1);if((A.data[Q+B.t]+=B.am(Q+1,2*B.data[Q],A,2*Q+1,D,B.t-Q-1))>=B.DV)A.data[Q+B.t]-=B.DV,A.data[Q+B.t+1]=1}if(A.t>0)A.data[A.t-1]+=B.am(Q,B.data[Q],A,2*Q,0,1);A.s=0,A.clamp()}function TD8(A,B,Q){var D=A.abs();if(D.t<=0)return;var Z=this.abs();if(Z.t<D.t){if(B!=null)B.fromInt(0);if(Q!=null)this.copyTo(Q);return}if(Q==null)Q=L8();var G=L8(),F=this.s,I=A.s,Y=this.DB-yv1(D.data[D.t-1]);if(Y>0)D.lShiftTo(Y,G),Z.lShiftTo(Y,Q);else D.copyTo(G),Z.copyTo(Q);var W=G.t,J=G.data[W-1];if(J==0)return;var X=J*(1<<this.F1)+(W>1?G.data[W-2]>>this.F2:0),V=this.FV/X,C=(1<<this.F1)/X,K=1<<this.F2,H=Q.t,z=H-W,$=B==null?L8():B;if(G.dlShiftTo(z,$),Q.compareTo($)>=0)Q.data[Q.t++]=1,Q.subTo($,Q);_A.ONE.dlShiftTo(W,$),$.subTo(G,G);while(G.t<W)G.data[G.t++]=0;while(--z>=0){var L=Q.data[--H]==J?this.DM:Math.floor(Q.data[H]*V+(Q.data[H-1]+K)*C);if((Q.data[H]+=G.am(0,L,Q,z,0,W))<L){G.dlShiftTo(z,$),Q.subTo($,Q);while(Q.data[H]<--L)Q.subTo($,Q)}}if(B!=null){if(Q.drShiftTo(W,B),F!=I)_A.ZERO.subTo(B,B)}if(Q.t=W,Q.clamp(),Y>0)Q.rShiftTo(Y,Q);if(F<0)_A.ZERO.subTo(Q,Q)}function PD8(A){var B=L8();if(this.abs().divRemTo(A,null,B),this.s<0&&B.compareTo(_A.ZERO)>0)A.subTo(B,B);return B}function sm(A){this.m=A}function SD8(A){if(A.s<0||A.compareTo(this.m)>=0)return A.mod(this.m);else return A}function jD8(A){return A}function kD8(A){A.divRemTo(this.m,null,A)}function yD8(A,B,Q){A.multiplyTo(B,Q),this.reduce(Q)}function _D8(A,B){A.squareTo(B),this.reduce(B)}sm.prototype.convert=SD8;sm.prototype.revert=jD8;sm.prototype.reduce=kD8;sm.prototype.mulTo=yD8;sm.prototype.sqrTo=_D8;function xD8(){if(this.t<1)return 0;var A=this.data[0];if((A&1)==0)return 0;var B=A&3;return B=B*(2-(A&15)*B)&15,B=B*(2-(A&255)*B)&255,B=B*(2-((A&65535)*B&65535))&65535,B=B*(2-A*B%this.DV)%this.DV,B>0?this.DV-B:-B}function rm(A){this.m=A,this.mp=A.invDigit(),this.mpl=this.mp&32767,this.mph=this.mp>>15,this.um=(1<<A.DB-15)-1,this.mt2=2*A.t}function vD8(A){var B=L8();if(A.abs().dlShiftTo(this.m.t,B),B.divRemTo(this.m,null,B),A.s<0&&B.compareTo(_A.ZERO)>0)this.m.subTo(B,B);return B}function bD8(A){var B=L8();return A.copyTo(B),this.reduce(B),B}function fD8(A){while(A.t<=this.mt2)A.data[A.t++]=0;for(var B=0;B<this.m.t;++B){var Q=A.data[B]&32767,D=Q*this.mpl+((Q*this.mph+(A.data[B]>>15)*this.mpl&this.um)<<15)&A.DM;Q=B+this.m.t,A.data[Q]+=this.m.am(0,D,A,B,0,this.m.t);while(A.data[Q]>=A.DV)A.data[Q]-=A.DV,A.data[++Q]++}if(A.clamp(),A.drShiftTo(this.m.t,A),A.compareTo(this.m)>=0)A.subTo(this.m,A)}function hD8(A,B){A.squareTo(B),this.reduce(B)}function gD8(A,B,Q){A.multiplyTo(B,Q),this.reduce(Q)}rm.prototype.convert=vD8;rm.prototype.revert=bD8;rm.prototype.reduce=fD8;rm.prototype.mulTo=gD8;rm.prototype.sqrTo=hD8;function uD8(){return(this.t>0?this.data[0]&1:this.s)==0}function mD8(A,B){if(A>4294967295||A<1)return _A.ONE;var Q=L8(),D=L8(),Z=B.convert(this),G=yv1(A)-1;Z.copyTo(Q);while(--G>=0)if(B.sqrTo(Q,D),(A&1<<G)>0)B.mulTo(D,Z,Q);else{var F=Q;Q=D,D=F}return B.revert(Q)}function dD8(A,B){var Q;if(A<256||B.isEven())Q=new sm(B);else Q=new rm(B);return this.exp(A,Q)}_A.prototype.copyTo=XD8;_A.prototype.fromInt=VD8;_A.prototype.fromString=CD8;_A.prototype.clamp=KD8;_A.prototype.dlShiftTo=$D8;_A.prototype.drShiftTo=qD8;_A.prototype.lShiftTo=ND8;_A.prototype.rShiftTo=LD8;_A.prototype.subTo=MD8;_A.prototype.multiplyTo=RD8;_A.prototype.squareTo=OD8;_A.prototype.divRemTo=TD8;_A.prototype.invDigit=xD8;_A.prototype.isEven=uD8;_A.prototype.exp=mD8;_A.prototype.toString=HD8;_A.prototype.negate=zD8;_A.prototype.abs=ED8;_A.prototype.compareTo=UD8;_A.prototype.bitLength=wD8;_A.prototype.mod=PD8;_A.prototype.modPowInt=dD8;_A.ZERO=Ov(0);_A.ONE=Ov(1);function cD8(){var A=L8();return this.copyTo(A),A}function lD8(){if(this.s<0){if(this.t==1)return this.data[0]-this.DV;else if(this.t==0)return-1}else if(this.t==1)return this.data[0];else if(this.t==0)return 0;return(this.data[1]&(1<<32-this.DB)-1)<<this.DB|this.data[0]}function pD8(){return this.t==0?this.s:this.data[0]<<24>>24}function iD8(){return this.t==0?this.s:this.data[0]<<16>>16}function nD8(A){return Math.floor(Math.LN2*this.DB/Math.log(A))}function aD8(){if(this.s<0)return-1;else if(this.t<=0||this.t==1&&this.data[0]<=0)return 0;else return 1}function sD8(A){if(A==null)A=10;if(this.signum()==0||A<2||A>36)return"0";var B=this.chunkSize(A),Q=Math.pow(A,B),D=Ov(Q),Z=L8(),G=L8(),F="";this.divRemTo(D,Z,G);while(Z.signum()>0)F=(Q+G.intValue()).toString(A).substr(1)+F,Z.divRemTo(D,Z,G);return G.intValue().toString(A)+F}function rD8(A,B){if(this.fromInt(0),B==null)B=10;var Q=this.chunkSize(B),D=Math.pow(B,Q),Z=!1,G=0,F=0;for(var I=0;I<A.length;++I){var Y=URB(A,I);if(Y<0){if(A.charAt(I)=="-"&&this.signum()==0)Z=!0;continue}if(F=B*F+Y,++G>=Q)this.dMultiply(D),this.dAddOffset(F,0),G=0,F=0}if(G>0)this.dMultiply(Math.pow(B,G)),this.dAddOffset(F,0);if(Z)_A.ZERO.subTo(this,this)}function oD8(A,B,Q){if(typeof B=="number")if(A<2)this.fromInt(1);else{if(this.fromNumber(A,Q),!this.testBit(A-1))this.bitwiseTo(_A.ONE.shiftLeft(A-1),Yq0,this);if(this.isEven())this.dAddOffset(1,0);while(!this.isProbablePrime(B))if(this.dAddOffset(2,0),this.bitLength()>A)this.subTo(_A.ONE.shiftLeft(A-1),this)}else{var D=new Array,Z=A&7;if(D.length=(A>>3)+1,B.nextBytes(D),Z>0)D[0]&=(1<<Z)-1;else D[0]=0;this.fromString(D,256)}}function tD8(){var A=this.t,B=new Array;B[0]=this.s;var Q=this.DB-A*this.DB%8,D,Z=0;if(A-- >0){if(Q<this.DB&&(D=this.data[A]>>Q)!=(this.s&this.DM)>>Q)B[Z++]=D|this.s<<this.DB-Q;while(A>=0){if(Q<8)D=(this.data[A]&(1<<Q)-1)<<8-Q,D|=this.data[--A]>>(Q+=this.DB-8);else if(D=this.data[A]>>(Q-=8)&255,Q<=0)Q+=this.DB,--A;if((D&128)!=0)D|=-256;if(Z==0&&(this.s&128)!=(D&128))++Z;if(Z>0||D!=this.s)B[Z++]=D}}return B}function eD8(A){return this.compareTo(A)==0}function AZ8(A){return this.compareTo(A)<0?this:A}function BZ8(A){return this.compareTo(A)>0?this:A}function QZ8(A,B,Q){var D,Z,G=Math.min(A.t,this.t);for(D=0;D<G;++D)Q.data[D]=B(this.data[D],A.data[D]);if(A.t<this.t){Z=A.s&this.DM;for(D=G;D<this.t;++D)Q.data[D]=B(this.data[D],Z);Q.t=this.t}else{Z=this.s&this.DM;for(D=G;D<A.t;++D)Q.data[D]=B(Z,A.data[D]);Q.t=A.t}Q.s=B(this.s,A.s),Q.clamp()}function DZ8(A,B){return A&B}function ZZ8(A){var B=L8();return this.bitwiseTo(A,DZ8,B),B}function Yq0(A,B){return A|B}function GZ8(A){var B=L8();return this.bitwiseTo(A,Yq0,B),B}function wRB(A,B){return A^B}function FZ8(A){var B=L8();return this.bitwiseTo(A,wRB,B),B}function $RB(A,B){return A&~B}function IZ8(A){var B=L8();return this.bitwiseTo(A,$RB,B),B}function YZ8(){var A=L8();for(var B=0;B<this.t;++B)A.data[B]=this.DM&~this.data[B];return A.t=this.t,A.s=~this.s,A}function WZ8(A){var B=L8();if(A<0)this.rShiftTo(-A,B);else this.lShiftTo(A,B);return B}function JZ8(A){var B=L8();if(A<0)this.lShiftTo(-A,B);else this.rShiftTo(A,B);return B}function XZ8(A){if(A==0)return-1;var B=0;if((A&65535)==0)A>>=16,B+=16;if((A&255)==0)A>>=8,B+=8;if((A&15)==0)A>>=4,B+=4;if((A&3)==0)A>>=2,B+=2;if((A&1)==0)++B;return B}function VZ8(){for(var A=0;A<this.t;++A)if(this.data[A]!=0)return A*this.DB+XZ8(this.data[A]);if(this.s<0)return this.t*this.DB;return-1}function CZ8(A){var B=0;while(A!=0)A&=A-1,++B;return B}function KZ8(){var A=0,B=this.s&this.DM;for(var Q=0;Q<this.t;++Q)A+=CZ8(this.data[Q]^B);return A}function HZ8(A){var B=Math.floor(A/this.DB);if(B>=this.t)return this.s!=0;return(this.data[B]&1<<A%this.DB)!=0}function zZ8(A,B){var Q=_A.ONE.shiftLeft(A);return this.bitwiseTo(Q,B,Q),Q}function EZ8(A){return this.changeBit(A,Yq0)}function UZ8(A){return this.changeBit(A,$RB)}function wZ8(A){return this.changeBit(A,wRB)}function $Z8(A,B){var Q=0,D=0,Z=Math.min(A.t,this.t);while(Q<Z)D+=this.data[Q]+A.data[Q],B.data[Q++]=D&this.DM,D>>=this.DB;if(A.t<this.t){D+=A.s;while(Q<this.t)D+=this.data[Q],B.data[Q++]=D&this.DM,D>>=this.DB;D+=this.s}else{D+=this.s;while(Q<A.t)D+=A.data[Q],B.data[Q++]=D&this.DM,D>>=this.DB;D+=A.s}if(B.s=D<0?-1:0,D>0)B.data[Q++]=D;else if(D<-1)B.data[Q++]=this.DV+D;B.t=Q,B.clamp()}function qZ8(A){var B=L8();return this.addTo(A,B),B}function NZ8(A){var B=L8();return this.subTo(A,B),B}function LZ8(A){var B=L8();return this.multiplyTo(A,B),B}function MZ8(A){var B=L8();return this.divRemTo(A,B,null),B}function RZ8(A){var B=L8();return this.divRemTo(A,null,B),B}function OZ8(A){var B=L8(),Q=L8();return this.divRemTo(A,B,Q),new Array(B,Q)}function TZ8(A){this.data[this.t]=this.am(0,A-1,this,0,0,this.t),++this.t,this.clamp()}function PZ8(A,B){if(A==0)return;while(this.t<=B)this.data[this.t++]=0;this.data[B]+=A;while(this.data[B]>=this.DV){if(this.data[B]-=this.DV,++B>=this.t)this.data[this.t++]=0;++this.data[B]}}function NG1(){}function qRB(A){return A}function SZ8(A,B,Q){A.multiplyTo(B,Q)}function jZ8(A,B){A.squareTo(B)}NG1.prototype.convert=qRB;NG1.prototype.revert=qRB;NG1.prototype.mulTo=SZ8;NG1.prototype.sqrTo=jZ8;function kZ8(A){return this.exp(A,new NG1)}function yZ8(A,B,Q){var D=Math.min(this.t+A.t,B);Q.s=0,Q.t=D;while(D>0)Q.data[--D]=0;var Z;for(Z=Q.t-this.t;D<Z;++D)Q.data[D+this.t]=this.am(0,A.data[D],Q,D,0,this.t);for(Z=Math.min(A.t,B);D<Z;++D)this.am(0,A.data[D],Q,D,0,B-D);Q.clamp()}function _Z8(A,B,Q){--B;var D=Q.t=this.t+A.t-B;Q.s=0;while(--D>=0)Q.data[D]=0;for(D=Math.max(B-this.t,0);D<A.t;++D)Q.data[this.t+D-B]=this.am(B-D,A.data[D],Q,0,0,this.t+D-B);Q.clamp(),Q.drShiftTo(1,Q)}function n11(A){this.r2=L8(),this.q3=L8(),_A.ONE.dlShiftTo(2*A.t,this.r2),this.mu=this.r2.divide(A),this.m=A}function xZ8(A){if(A.s<0||A.t>2*this.m.t)return A.mod(this.m);else if(A.compareTo(this.m)<0)return A;else{var B=L8();return A.copyTo(B),this.reduce(B),B}}function vZ8(A){return A}function bZ8(A){if(A.drShiftTo(this.m.t-1,this.r2),A.t>this.m.t+1)A.t=this.m.t+1,A.clamp();this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);while(A.compareTo(this.r2)<0)A.dAddOffset(1,this.m.t+1);A.subTo(this.r2,A);while(A.compareTo(this.m)>=0)A.subTo(this.m,A)}function fZ8(A,B){A.squareTo(B),this.reduce(B)}function hZ8(A,B,Q){A.multiplyTo(B,Q),this.reduce(Q)}n11.prototype.convert=xZ8;n11.prototype.revert=vZ8;n11.prototype.reduce=bZ8;n11.prototype.mulTo=hZ8;n11.prototype.sqrTo=fZ8;function gZ8(A,B){var Q=A.bitLength(),D,Z=Ov(1),G;if(Q<=0)return Z;else if(Q<18)D=1;else if(Q<48)D=3;else if(Q<144)D=4;else if(Q<768)D=5;else D=6;if(Q<8)G=new sm(B);else if(B.isEven())G=new n11(B);else G=new rm(B);var F=new Array,I=3,Y=D-1,W=(1<<D)-1;if(F[1]=G.convert(this),D>1){var J=L8();G.sqrTo(F[1],J);while(I<=W)F[I]=L8(),G.mulTo(J,F[I-2],F[I]),I+=2}var X=A.t-1,V,C=!0,K=L8(),H;Q=yv1(A.data[X])-1;while(X>=0){if(Q>=Y)V=A.data[X]>>Q-Y&W;else if(V=(A.data[X]&(1<<Q+1)-1)<<Y-Q,X>0)V|=A.data[X-1]>>this.DB+Q-Y;I=D;while((V&1)==0)V>>=1,--I;if((Q-=I)<0)Q+=this.DB,--X;if(C)F[V].copyTo(Z),C=!1;else{while(I>1)G.sqrTo(Z,K),G.sqrTo(K,Z),I-=2;if(I>0)G.sqrTo(Z,K);else H=Z,Z=K,K=H;G.mulTo(K,F[V],Z)}while(X>=0&&(A.data[X]&1<<Q)==0)if(G.sqrTo(Z,K),H=Z,Z=K,K=H,--Q<0)Q=this.DB-1,--X}return G.revert(Z)}function uZ8(A){var B=this.s<0?this.negate():this.clone(),Q=A.s<0?A.negate():A.clone();if(B.compareTo(Q)<0){var D=B;B=Q,Q=D}var Z=B.getLowestSetBit(),G=Q.getLowestSetBit();if(G<0)return B;if(Z<G)G=Z;if(G>0)B.rShiftTo(G,B),Q.rShiftTo(G,Q);while(B.signum()>0){if((Z=B.getLowestSetBit())>0)B.rShiftTo(Z,B);if((Z=Q.getLowestSetBit())>0)Q.rShiftTo(Z,Q);if(B.compareTo(Q)>=0)B.subTo(Q,B),B.rShiftTo(1,B);else Q.subTo(B,Q),Q.rShiftTo(1,Q)}if(G>0)Q.lShiftTo(G,Q);return Q}function mZ8(A){if(A<=0)return 0;var B=this.DV%A,Q=this.s<0?A-1:0;if(this.t>0)if(B==0)Q=this.data[0]%A;else for(var D=this.t-1;D>=0;--D)Q=(B*Q+this.data[D])%A;return Q}function dZ8(A){var B=A.isEven();if(this.isEven()&&B||A.signum()==0)return _A.ZERO;var Q=A.clone(),D=this.clone(),Z=Ov(1),G=Ov(0),F=Ov(0),I=Ov(1);while(Q.signum()!=0){while(Q.isEven()){if(Q.rShiftTo(1,Q),B){if(!Z.isEven()||!G.isEven())Z.addTo(this,Z),G.subTo(A,G);Z.rShiftTo(1,Z)}else if(!G.isEven())G.subTo(A,G);G.rShiftTo(1,G)}while(D.isEven()){if(D.rShiftTo(1,D),B){if(!F.isEven()||!I.isEven())F.addTo(this,F),I.subTo(A,I);F.rShiftTo(1,F)}else if(!I.isEven())I.subTo(A,I);I.rShiftTo(1,I)}if(Q.compareTo(D)>=0){if(Q.subTo(D,Q),B)Z.subTo(F,Z);G.subTo(I,G)}else{if(D.subTo(Q,D),B)F.subTo(Z,F);I.subTo(G,I)}}if(D.compareTo(_A.ONE)!=0)return _A.ZERO;if(I.compareTo(A)>=0)return I.subtract(A);if(I.signum()<0)I.addTo(A,I);else return I;if(I.signum()<0)return I.add(A);else return I}var k$=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509],cZ8=67108864/k$[k$.length-1];function lZ8(A){var B,Q=this.abs();if(Q.t==1&&Q.data[0]<=k$[k$.length-1]){for(B=0;B<k$.length;++B)if(Q.data[0]==k$[B])return!0;return!1}if(Q.isEven())return!1;B=1;while(B<k$.length){var D=k$[B],Z=B+1;while(Z<k$.length&&D<cZ8)D*=k$[Z++];D=Q.modInt(D);while(B<Z)if(D%k$[B++]==0)return!1}return Q.millerRabin(A)}function pZ8(A){var B=this.subtract(_A.ONE),Q=B.getLowestSetBit();if(Q<=0)return!1;var D=B.shiftRight(Q),Z=iZ8(),G;for(var F=0;F<A;++F){do G=new _A(this.bitLength(),Z);while(G.compareTo(_A.ONE)<=0||G.compareTo(B)>=0);var I=G.modPow(D,this);if(I.compareTo(_A.ONE)!=0&&I.compareTo(B)!=0){var Y=1;while(Y++<Q&&I.compareTo(B)!=0)if(I=I.modPowInt(2,this),I.compareTo(_A.ONE)==0)return!1;if(I.compareTo(B)!=0)return!1}}return!0}function iZ8(){return{nextBytes:function(A){for(var B=0;B<A.length;++B)A[B]=Math.floor(Math.random()*256)}}}_A.prototype.chunkSize=nD8;_A.prototype.toRadix=sD8;_A.prototype.fromRadix=rD8;_A.prototype.fromNumber=oD8;_A.prototype.bitwiseTo=QZ8;_A.prototype.changeBit=zZ8;_A.prototype.addTo=$Z8;_A.prototype.dMultiply=TZ8;_A.prototype.dAddOffset=PZ8;_A.prototype.multiplyLowerTo=yZ8;_A.prototype.multiplyUpperTo=_Z8;_A.prototype.modInt=mZ8;_A.prototype.millerRabin=pZ8;_A.prototype.clone=cD8;_A.prototype.intValue=lD8;_A.prototype.byteValue=pD8;_A.prototype.shortValue=iD8;_A.prototype.signum=aD8;_A.prototype.toByteArray=tD8;_A.prototype.equals=eD8;_A.prototype.min=AZ8;_A.prototype.max=BZ8;_A.prototype.and=ZZ8;_A.prototype.or=GZ8;_A.prototype.xor=FZ8;_A.prototype.andNot=IZ8;_A.prototype.not=YZ8;_A.prototype.shiftLeft=WZ8;_A.prototype.shiftRight=JZ8;_A.prototype.getLowestSetBit=VZ8;_A.prototype.bitCount=KZ8;_A.prototype.testBit=HZ8;_A.prototype.setBit=EZ8;_A.prototype.clearBit=UZ8;_A.prototype.flipBit=wZ8;_A.prototype.add=qZ8;_A.prototype.subtract=NZ8;_A.prototype.multiply=LZ8;_A.prototype.divide=MZ8;_A.prototype.remainder=RZ8;_A.prototype.divideAndRemainder=OZ8;_A.prototype.modPow=gZ8;_A.prototype.modInverse=dZ8;_A.prototype.pow=kZ8;_A.prototype.gcd=uZ8;_A.prototype.isProbablePrime=lZ8});
var M4=E((ZZ3,yMB)=>{yMB.exports={options:{usePureJavaScript:!1}}});
var MG1=E((OZ3,vRB)=>{var g9=M4();j$();LG1();Rv();Jq0();Vq0();eE();N8();if(typeof b6==="undefined")b6=g9.jsbn.BigInteger;var b6,Cq0=g9.util.isNodejs?J1("crypto"):null,p0=g9.asn1,BU=g9.util;g9.pki=g9.pki||{};vRB.exports=g9.pki.rsa=g9.rsa=g9.rsa||{};var sQ=g9.pki,aZ8=[6,4,2,4,2,4,6,2],sZ8={name:"PrivateKeyInfo",tagClass:p0.Class.UNIVERSAL,type:p0.Type.SEQUENCE,constructed:!0,value:[{name:"PrivateKeyInfo.version",tagClass:p0.Class.UNIVERSAL,type:p0.Type.INTEGER,constructed:!1,capture:"privateKeyVersion"},{name:"PrivateKeyInfo.privateKeyAlgorithm",tagClass:p0.Class.UNIVERSAL,type:p0.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:p0.Class.UNIVERSAL,type:p0.Type.OID,constructed:!1,capture:"privateKeyOid"}]},{name:"PrivateKeyInfo",tagClass:p0.Class.UNIVERSAL,type:p0.Type.OCTETSTRING,constructed:!1,capture:"privateKey"}]},rZ8={name:"RSAPrivateKey",tagClass:p0.Class.UNIVERSAL,type:p0.Type.SEQUENCE,constructed:!0,value:[{name:"RSAPrivateKey.version",tagClass:p0.Class.UNIVERSAL,type:p0.Type.INTEGER,constructed:!1,capture:"privateKeyVersion"},{name:"RSAPrivateKey.modulus",tagClass:p0.Class.UNIVERSAL,type:p0.Type.INTEGER,constructed:!1,capture:"privateKeyModulus"},{name:"RSAPrivateKey.publicExponent",tagClass:p0.Class.UNIVERSAL,type:p0.Type.INTEGER,constructed:!1,capture:"privateKeyPublicExponent"},{name:"RSAPrivateKey.privateExponent",tagClass:p0.Class.UNIVERSAL,type:p0.Type.INTEGER,constructed:!1,capture:"privateKeyPrivateExponent"},{name:"RSAPrivateKey.prime1",tagClass:p0.Class.UNIVERSAL,type:p0.Type.INTEGER,constructed:!1,capture:"privateKeyPrime1"},{name:"RSAPrivateKey.prime2",tagClass:p0.Class.UNIVERSAL,type:p0.Type.INTEGER,constructed:!1,capture:"privateKeyPrime2"},{name:"RSAPrivateKey.exponent1",tagClass:p0.Class.UNIVERSAL,type:p0.Type.INTEGER,constructed:!1,capture:"privateKeyExponent1"},{name:"RSAPrivateKey.exponent2",tagClass:p0.Class.UNIVERSAL,type:p0.Type.INTEGER,constructed:!1,capture:"privateKeyExponent2"},{name:"RSAPrivateKey.coefficient",tagClass:p0.Class.UNIVERSAL,type:p0.Type.INTEGER,constructed:!1,capture:"privateKeyCoefficient"}]},oZ8={name:"RSAPublicKey",tagClass:p0.Class.UNIVERSAL,type:p0.Type.SEQUENCE,constructed:!0,value:[{name:"RSAPublicKey.modulus",tagClass:p0.Class.UNIVERSAL,type:p0.Type.INTEGER,constructed:!1,capture:"publicKeyModulus"},{name:"RSAPublicKey.exponent",tagClass:p0.Class.UNIVERSAL,type:p0.Type.INTEGER,constructed:!1,capture:"publicKeyExponent"}]},tZ8=g9.pki.rsa.publicKeyValidator={name:"SubjectPublicKeyInfo",tagClass:p0.Class.UNIVERSAL,type:p0.Type.SEQUENCE,constructed:!0,captureAsn1:"subjectPublicKeyInfo",value:[{name:"SubjectPublicKeyInfo.AlgorithmIdentifier",tagClass:p0.Class.UNIVERSAL,type:p0.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:p0.Class.UNIVERSAL,type:p0.Type.OID,constructed:!1,capture:"publicKeyOid"}]},{name:"SubjectPublicKeyInfo.subjectPublicKey",tagClass:p0.Class.UNIVERSAL,type:p0.Type.BITSTRING,constructed:!1,value:[{name:"SubjectPublicKeyInfo.subjectPublicKey.RSAPublicKey",tagClass:p0.Class.UNIVERSAL,type:p0.Type.SEQUENCE,constructed:!0,optional:!0,captureAsn1:"rsaPublicKey"}]}]},eZ8={name:"DigestInfo",tagClass:p0.Class.UNIVERSAL,type:p0.Type.SEQUENCE,constructed:!0,value:[{name:"DigestInfo.DigestAlgorithm",tagClass:p0.Class.UNIVERSAL,type:p0.Type.SEQUENCE,constructed:!0,value:[{name:"DigestInfo.DigestAlgorithm.algorithmIdentifier",tagClass:p0.Class.UNIVERSAL,type:p0.Type.OID,constructed:!1,capture:"algorithmIdentifier"},{name:"DigestInfo.DigestAlgorithm.parameters",tagClass:p0.Class.UNIVERSAL,type:p0.Type.NULL,capture:"parameters",optional:!0,constructed:!1}]},{name:"DigestInfo.digest",tagClass:p0.Class.UNIVERSAL,type:p0.Type.OCTETSTRING,constructed:!1,capture:"digest"}]},AG8=function(A){var B;if(A.algorithm in sQ.oids)B=sQ.oids[A.algorithm];else{var Q=new Error("Unknown message digest algorithm.");throw Q.algorithm=A.algorithm,Q}var D=p0.oidToDer(B).getBytes(),Z=p0.create(p0.Class.UNIVERSAL,p0.Type.SEQUENCE,!0,[]),G=p0.create(p0.Class.UNIVERSAL,p0.Type.SEQUENCE,!0,[]);G.value.push(p0.create(p0.Class.UNIVERSAL,p0.Type.OID,!1,D)),G.value.push(p0.create(p0.Class.UNIVERSAL,p0.Type.NULL,!1,""));var F=p0.create(p0.Class.UNIVERSAL,p0.Type.OCTETSTRING,!1,A.digest().getBytes());return Z.value.push(G),Z.value.push(F),p0.toDer(Z).getBytes()},_RB=function(A,B,Q){if(Q)return A.modPow(B.e,B.n);if(!B.p||!B.q)return A.modPow(B.d,B.n);if(!B.dP)B.dP=B.d.mod(B.p.subtract(b6.ONE));if(!B.dQ)B.dQ=B.d.mod(B.q.subtract(b6.ONE));if(!B.qInv)B.qInv=B.q.modInverse(B.p);var D;do D=new b6(g9.util.bytesToHex(g9.random.getBytes(B.n.bitLength()/8)),16);while(D.compareTo(B.n)>=0||!D.gcd(B.n).equals(b6.ONE));A=A.multiply(D.modPow(B.e,B.n)).mod(B.n);var Z=A.mod(B.p).modPow(B.dP,B.p),G=A.mod(B.q).modPow(B.dQ,B.q);while(Z.compareTo(G)<0)Z=Z.add(B.p);var F=Z.subtract(G).multiply(B.qInv).mod(B.p).multiply(B.q).add(G);return F=F.multiply(D.modInverse(B.n)).mod(B.n),F};sQ.rsa.encrypt=function(A,B,Q){var D=Q,Z,G=Math.ceil(B.n.bitLength()/8);if(Q!==!1&&Q!==!0)D=Q===2,Z=xRB(A,B,Q);else Z=g9.util.createBuffer(),Z.putBytes(A);var F=new b6(Z.toHex(),16),I=_RB(F,B,D),Y=I.toString(16),W=g9.util.createBuffer(),J=G-Math.ceil(Y.length/2);while(J>0)W.putByte(0),--J;return W.putBytes(g9.util.hexToBytes(Y)),W.getBytes()};sQ.rsa.decrypt=function(A,B,Q,D){var Z=Math.ceil(B.n.bitLength()/8);if(A.length!==Z){var G=new Error("Encrypted message length is invalid.");throw G.length=A.length,G.expected=Z,G}var F=new b6(g9.util.createBuffer(A).toHex(),16);if(F.compareTo(B.n)>=0)throw new Error("Encrypted message is invalid.");var I=_RB(F,B,Q),Y=I.toString(16),W=g9.util.createBuffer(),J=Z-Math.ceil(Y.length/2);while(J>0)W.putByte(0),--J;if(W.putBytes(g9.util.hexToBytes(Y)),D!==!1)return xv1(W.getBytes(),B,Q);return W.getBytes()};sQ.rsa.createKeyPairGenerationState=function(A,B,Q){if(typeof A==="string")A=parseInt(A,10);A=A||2048,Q=Q||{};var D=Q.prng||g9.random,Z={nextBytes:function(I){var Y=D.getBytesSync(I.length);for(var W=0;W<I.length;++W)I[W]=Y.charCodeAt(W)}},G=Q.algorithm||"PRIMEINC",F;if(G==="PRIMEINC")F={algorithm:G,state:0,bits:A,rng:Z,eInt:B||65537,e:new b6(null),p:null,q:null,qBits:A>>1,pBits:A-(A>>1),pqState:0,num:null,keys:null},F.e.fromInt(F.eInt);else throw new Error("Invalid key generation algorithm: "+G);return F};sQ.rsa.stepKeyPairGenerationState=function(A,B){if(!("algorithm"in A))A.algorithm="PRIMEINC";var Q=new b6(null);Q.fromInt(30);var D=0,Z=function(X,V){return X|V},G=+new Date,F,I=0;while(A.keys===null&&(B<=0||I<B)){if(A.state===0){var Y=A.p===null?A.pBits:A.qBits,W=Y-1;if(A.pqState===0){if(A.num=new b6(Y,A.rng),!A.num.testBit(W))A.num.bitwiseTo(b6.ONE.shiftLeft(W),Z,A.num);A.num.dAddOffset(31-A.num.mod(Q).byteValue(),0),D=0,++A.pqState}else if(A.pqState===1)if(A.num.bitLength()>Y)A.pqState=0;else if(A.num.isProbablePrime(QG8(A.num.bitLength())))++A.pqState;else A.num.dAddOffset(aZ8[D++%8],0);else if(A.pqState===2)A.pqState=A.num.subtract(b6.ONE).gcd(A.e).compareTo(b6.ONE)===0?3:0;else if(A.pqState===3){if(A.pqState=0,A.p===null)A.p=A.num;else A.q=A.num;if(A.p!==null&&A.q!==null)++A.state;A.num=null}}else if(A.state===1){if(A.p.compareTo(A.q)<0)A.num=A.p,A.p=A.q,A.q=A.num;++A.state}else if(A.state===2)A.p1=A.p.subtract(b6.ONE),A.q1=A.q.subtract(b6.ONE),A.phi=A.p1.multiply(A.q1),++A.state;else if(A.state===3)if(A.phi.gcd(A.e).compareTo(b6.ONE)===0)++A.state;else A.p=null,A.q=null,A.state=0;else if(A.state===4)if(A.n=A.p.multiply(A.q),A.n.bitLength()===A.bits)++A.state;else A.q=null,A.state=0;else if(A.state===5){var J=A.e.modInverse(A.phi);A.keys={privateKey:sQ.rsa.setPrivateKey(A.n,A.e,J,A.p,A.q,J.mod(A.p1),J.mod(A.q1),A.q.modInverse(A.p)),publicKey:sQ.rsa.setPublicKey(A.n,A.e)}}F=+new Date,I+=F-G,G=F}return A.keys!==null};sQ.rsa.generateKeyPair=function(A,B,Q,D){if(arguments.length===1){if(typeof A==="object")Q=A,A=void 0;else if(typeof A==="function")D=A,A=void 0}else if(arguments.length===2)if(typeof A==="number"){if(typeof B==="function")D=B,B=void 0;else if(typeof B!=="number")Q=B,B=void 0}else Q=A,D=B,A=void 0,B=void 0;else if(arguments.length===3)if(typeof B==="number"){if(typeof Q==="function")D=Q,Q=void 0}else D=Q,Q=B,B=void 0;if(Q=Q||{},A===void 0)A=Q.bits||2048;if(B===void 0)B=Q.e||65537;if(!g9.options.usePureJavaScript&&!Q.prng&&A>=256&&A<=16384&&(B===65537||B===3)){if(D){if(SRB("generateKeyPair"))return Cq0.generateKeyPair("rsa",{modulusLength:A,publicExponent:B,publicKeyEncoding:{type:"spki",format:"pem"},privateKeyEncoding:{type:"pkcs8",format:"pem"}},function(I,Y,W){if(I)return D(I);D(null,{privateKey:sQ.privateKeyFromPem(W),publicKey:sQ.publicKeyFromPem(Y)})});if(jRB("generateKey")&&jRB("exportKey"))return BU.globalScope.crypto.subtle.generateKey({name:"RSASSA-PKCS1-v1_5",modulusLength:A,publicExponent:yRB(B),hash:{name:"SHA-256"}},!0,["sign","verify"]).then(function(I){return BU.globalScope.crypto.subtle.exportKey("pkcs8",I.privateKey)}).then(void 0,function(I){D(I)}).then(function(I){if(I){var Y=sQ.privateKeyFromAsn1(p0.fromDer(g9.util.createBuffer(I)));D(null,{privateKey:Y,publicKey:sQ.setRsaPublicKey(Y.n,Y.e)})}});if(kRB("generateKey")&&kRB("exportKey")){var Z=BU.globalScope.msCrypto.subtle.generateKey({name:"RSASSA-PKCS1-v1_5",modulusLength:A,publicExponent:yRB(B),hash:{name:"SHA-256"}},!0,["sign","verify"]);Z.oncomplete=function(I){var Y=I.target.result,W=BU.globalScope.msCrypto.subtle.exportKey("pkcs8",Y.privateKey);W.oncomplete=function(J){var X=J.target.result,V=sQ.privateKeyFromAsn1(p0.fromDer(g9.util.createBuffer(X)));D(null,{privateKey:V,publicKey:sQ.setRsaPublicKey(V.n,V.e)})},W.onerror=function(J){D(J)}},Z.onerror=function(I){D(I)};return}}else if(SRB("generateKeyPairSync")){var G=Cq0.generateKeyPairSync("rsa",{modulusLength:A,publicExponent:B,publicKeyEncoding:{type:"spki",format:"pem"},privateKeyEncoding:{type:"pkcs8",format:"pem"}});return{privateKey:sQ.privateKeyFromPem(G.privateKey),publicKey:sQ.publicKeyFromPem(G.publicKey)}}}var F=sQ.rsa.createKeyPairGenerationState(A,B,Q);if(!D)return sQ.rsa.stepKeyPairGenerationState(F,0),F.keys;BG8(F,Q,D)};sQ.setRsaPublicKey=sQ.rsa.setPublicKey=function(A,B){var Q={n:A,e:B};return Q.encrypt=function(D,Z,G){if(typeof Z==="string")Z=Z.toUpperCase();else if(Z===void 0)Z="RSAES-PKCS1-V1_5";if(Z==="RSAES-PKCS1-V1_5")Z={encode:function(I,Y,W){return xRB(I,Y,2).getBytes()}};else if(Z==="RSA-OAEP"||Z==="RSAES-OAEP")Z={encode:function(I,Y){return g9.pkcs1.encode_rsa_oaep(Y,I,G)}};else if(["RAW","NONE","NULL",null].indexOf(Z)!==-1)Z={encode:function(I){return I}};else if(typeof Z==="string")throw new Error('Unsupported encryption scheme: "'+Z+'".');var F=Z.encode(D,Q,!0);return sQ.rsa.encrypt(F,Q,!0)},Q.verify=function(D,Z,G,F){if(typeof G==="string")G=G.toUpperCase();else if(G===void 0)G="RSASSA-PKCS1-V1_5";if(F===void 0)F={_parseAllDigestBytes:!0};if(!("_parseAllDigestBytes"in F))F._parseAllDigestBytes=!0;if(G==="RSASSA-PKCS1-V1_5")G={verify:function(Y,W){W=xv1(W,Q,!0);var J=p0.fromDer(W,{parseAllBytes:F._parseAllDigestBytes}),X={},V=[];if(!p0.validate(J,eZ8,X,V)){var C=new Error("ASN.1 object does not contain a valid RSASSA-PKCS1-v1_5 DigestInfo value.");throw C.errors=V,C}var K=p0.derToOid(X.algorithmIdentifier);if(!(K===g9.oids.md2||K===g9.oids.md5||K===g9.oids.sha1||K===g9.oids.sha224||K===g9.oids.sha256||K===g9.oids.sha384||K===g9.oids.sha512||K===g9.oids["sha512-224"]||K===g9.oids["sha512-256"])){var C=new Error("Unknown RSASSA-PKCS1-v1_5 DigestAlgorithm identifier.");throw C.oid=K,C}if(K===g9.oids.md2||K===g9.oids.md5){if(!("parameters"in X))throw new Error("ASN.1 object does not contain a valid RSASSA-PKCS1-v1_5 DigestInfo value. Missing algorithm identifer NULL parameters.")}return Y===X.digest}};else if(G==="NONE"||G==="NULL"||G===null)G={verify:function(Y,W){return W=xv1(W,Q,!0),Y===W}};var I=sQ.rsa.decrypt(Z,Q,!0,!1);return G.verify(D,I,Q.n.bitLength())},Q};sQ.setRsaPrivateKey=sQ.rsa.setPrivateKey=function(A,B,Q,D,Z,G,F,I){var Y={n:A,e:B,d:Q,p:D,q:Z,dP:G,dQ:F,qInv:I};return Y.decrypt=function(W,J,X){if(typeof J==="string")J=J.toUpperCase();else if(J===void 0)J="RSAES-PKCS1-V1_5";var V=sQ.rsa.decrypt(W,Y,!1,!1);if(J==="RSAES-PKCS1-V1_5")J={decode:xv1};else if(J==="RSA-OAEP"||J==="RSAES-OAEP")J={decode:function(C,K){return g9.pkcs1.decode_rsa_oaep(K,C,X)}};else if(["RAW","NONE","NULL",null].indexOf(J)!==-1)J={decode:function(C){return C}};else throw new Error('Unsupported encryption scheme: "'+J+'".');return J.decode(V,Y,!1)},Y.sign=function(W,J){var X=!1;if(typeof J==="string")J=J.toUpperCase();if(J===void 0||J==="RSASSA-PKCS1-V1_5")J={encode:AG8},X=1;else if(J==="NONE"||J==="NULL"||J===null)J={encode:function(){return W}},X=1;var V=J.encode(W,Y.n.bitLength());return sQ.rsa.encrypt(V,Y,X)},Y};sQ.wrapRsaPrivateKey=function(A){return p0.create(p0.Class.UNIVERSAL,p0.Type.SEQUENCE,!0,[p0.create(p0.Class.UNIVERSAL,p0.Type.INTEGER,!1,p0.integerToDer(0).getBytes()),p0.create(p0.Class.UNIVERSAL,p0.Type.SEQUENCE,!0,[p0.create(p0.Class.UNIVERSAL,p0.Type.OID,!1,p0.oidToDer(sQ.oids.rsaEncryption).getBytes()),p0.create(p0.Class.UNIVERSAL,p0.Type.NULL,!1,"")]),p0.create(p0.Class.UNIVERSAL,p0.Type.OCTETSTRING,!1,p0.toDer(A).getBytes())])};sQ.privateKeyFromAsn1=function(A){var B={},Q=[];if(p0.validate(A,sZ8,B,Q))A=p0.fromDer(g9.util.createBuffer(B.privateKey));if(B={},Q=[],!p0.validate(A,rZ8,B,Q)){var D=new Error("Cannot read private key. ASN.1 object does not contain an RSAPrivateKey.");throw D.errors=Q,D}var Z,G,F,I,Y,W,J,X;return Z=g9.util.createBuffer(B.privateKeyModulus).toHex(),G=g9.util.createBuffer(B.privateKeyPublicExponent).toHex(),F=g9.util.createBuffer(B.privateKeyPrivateExponent).toHex(),I=g9.util.createBuffer(B.privateKeyPrime1).toHex(),Y=g9.util.createBuffer(B.privateKeyPrime2).toHex(),W=g9.util.createBuffer(B.privateKeyExponent1).toHex(),J=g9.util.createBuffer(B.privateKeyExponent2).toHex(),X=g9.util.createBuffer(B.privateKeyCoefficient).toHex(),sQ.setRsaPrivateKey(new b6(Z,16),new b6(G,16),new b6(F,16),new b6(I,16),new b6(Y,16),new b6(W,16),new b6(J,16),new b6(X,16))};sQ.privateKeyToAsn1=sQ.privateKeyToRSAPrivateKey=function(A){return p0.create(p0.Class.UNIVERSAL,p0.Type.SEQUENCE,!0,[p0.create(p0.Class.UNIVERSAL,p0.Type.INTEGER,!1,p0.integerToDer(0).getBytes()),p0.create(p0.Class.UNIVERSAL,p0.Type.INTEGER,!1,cM(A.n)),p0.create(p0.Class.UNIVERSAL,p0.Type.INTEGER,!1,cM(A.e)),p0.create(p0.Class.UNIVERSAL,p0.Type.INTEGER,!1,cM(A.d)),p0.create(p0.Class.UNIVERSAL,p0.Type.INTEGER,!1,cM(A.p)),p0.create(p0.Class.UNIVERSAL,p0.Type.INTEGER,!1,cM(A.q)),p0.create(p0.Class.UNIVERSAL,p0.Type.INTEGER,!1,cM(A.dP)),p0.create(p0.Class.UNIVERSAL,p0.Type.INTEGER,!1,cM(A.dQ)),p0.create(p0.Class.UNIVERSAL,p0.Type.INTEGER,!1,cM(A.qInv))])};sQ.publicKeyFromAsn1=function(A){var B={},Q=[];if(p0.validate(A,tZ8,B,Q)){var D=p0.derToOid(B.publicKeyOid);if(D!==sQ.oids.rsaEncryption){var Z=new Error("Cannot read public key. Unknown OID.");throw Z.oid=D,Z}A=B.rsaPublicKey}if(Q=[],!p0.validate(A,oZ8,B,Q)){var Z=new Error("Cannot read public key. ASN.1 object does not contain an RSAPublicKey.");throw Z.errors=Q,Z}var G=g9.util.createBuffer(B.publicKeyModulus).toHex(),F=g9.util.createBuffer(B.publicKeyExponent).toHex();return sQ.setRsaPublicKey(new b6(G,16),new b6(F,16))};sQ.publicKeyToAsn1=sQ.publicKeyToSubjectPublicKeyInfo=function(A){return p0.create(p0.Class.UNIVERSAL,p0.Type.SEQUENCE,!0,[p0.create(p0.Class.UNIVERSAL,p0.Type.SEQUENCE,!0,[p0.create(p0.Class.UNIVERSAL,p0.Type.OID,!1,p0.oidToDer(sQ.oids.rsaEncryption).getBytes()),p0.create(p0.Class.UNIVERSAL,p0.Type.NULL,!1,"")]),p0.create(p0.Class.UNIVERSAL,p0.Type.BITSTRING,!1,[sQ.publicKeyToRSAPublicKey(A)])])};sQ.publicKeyToRSAPublicKey=function(A){return p0.create(p0.Class.UNIVERSAL,p0.Type.SEQUENCE,!0,[p0.create(p0.Class.UNIVERSAL,p0.Type.INTEGER,!1,cM(A.n)),p0.create(p0.Class.UNIVERSAL,p0.Type.INTEGER,!1,cM(A.e))])};function xRB(A,B,Q){var D=g9.util.createBuffer(),Z=Math.ceil(B.n.bitLength()/8);if(A.length>Z-11){var G=new Error("Message is too long for PKCS#1 v1.5 padding.");throw G.length=A.length,G.max=Z-11,G}D.putByte(0),D.putByte(Q);var F=Z-3-A.length,I;if(Q===0||Q===1){I=Q===0?0:255;for(var Y=0;Y<F;++Y)D.putByte(I)}else while(F>0){var W=0,J=g9.random.getBytes(F);for(var Y=0;Y<F;++Y)if(I=J.charCodeAt(Y),I===0)++W;else D.putByte(I);F=W}return D.putByte(0),D.putBytes(A),D}function xv1(A,B,Q,D){var Z=Math.ceil(B.n.bitLength()/8),G=g9.util.createBuffer(A),F=G.getByte(),I=G.getByte();if(F!==0||Q&&I!==0&&I!==1||!Q&&I!=2||Q&&I===0&&typeof D==="undefined")throw new Error("Encryption block is invalid.");var Y=0;if(I===0){Y=Z-3-D;for(var W=0;W<Y;++W)if(G.getByte()!==0)throw new Error("Encryption block is invalid.")}else if(I===1){Y=0;while(G.length()>1){if(G.getByte()!==255){--G.read;break}++Y}}else if(I===2){Y=0;while(G.length()>1){if(G.getByte()===0){--G.read;break}++Y}}var J=G.getByte();if(J!==0||Y!==Z-3-G.length())throw new Error("Encryption block is invalid.");return G.getBytes()}function BG8(A,B,Q){if(typeof B==="function")Q=B,B={};B=B||{};var D={algorithm:{name:B.algorithm||"PRIMEINC",options:{workers:B.workers||2,workLoad:B.workLoad||100,workerScript:B.workerScript}}};if("prng"in B)D.prng=B.prng;Z();function Z(){G(A.pBits,function(I,Y){if(I)return Q(I);if(A.p=Y,A.q!==null)return F(I,A.q);G(A.qBits,F)})}function G(I,Y){g9.prime.generateProbablePrime(I,D,Y)}function F(I,Y){if(I)return Q(I);if(A.q=Y,A.p.compareTo(A.q)<0){var W=A.p;A.p=A.q,A.q=W}if(A.p.subtract(b6.ONE).gcd(A.e).compareTo(b6.ONE)!==0){A.p=null,Z();return}if(A.q.subtract(b6.ONE).gcd(A.e).compareTo(b6.ONE)!==0){A.q=null,G(A.qBits,F);return}if(A.p1=A.p.subtract(b6.ONE),A.q1=A.q.subtract(b6.ONE),A.phi=A.p1.multiply(A.q1),A.phi.gcd(A.e).compareTo(b6.ONE)!==0){A.p=A.q=null,Z();return}if(A.n=A.p.multiply(A.q),A.n.bitLength()!==A.bits){A.q=null,G(A.qBits,F);return}var J=A.e.modInverse(A.phi);A.keys={privateKey:sQ.rsa.setPrivateKey(A.n,A.e,J,A.p,A.q,J.mod(A.p1),J.mod(A.q1),A.q.modInverse(A.p)),publicKey:sQ.rsa.setPublicKey(A.n,A.e)},Q(null,A.keys)}}function cM(A){var B=A.toString(16);if(B[0]>="8")B="00"+B;var Q=g9.util.hexToBytes(B);if(Q.length>1&&(Q.charCodeAt(0)===0&&(Q.charCodeAt(1)&128)===0||Q.charCodeAt(0)===255&&(Q.charCodeAt(1)&128)===128))return Q.substr(1);return Q}function QG8(A){if(A<=100)return 27;if(A<=150)return 18;if(A<=200)return 15;if(A<=250)return 12;if(A<=300)return 9;if(A<=350)return 8;if(A<=400)return 7;if(A<=500)return 6;if(A<=600)return 5;if(A<=800)return 4;if(A<=1250)return 3;return 2}function SRB(A){return g9.util.isNodejs&&typeof Cq0[A]==="function"}function jRB(A){return typeof BU.globalScope!=="undefined"&&typeof BU.globalScope.crypto==="object"&&typeof BU.globalScope.crypto.subtle==="object"&&typeof BU.globalScope.crypto.subtle[A]==="function"}function kRB(A){return typeof BU.globalScope!=="undefined"&&typeof BU.globalScope.msCrypto==="object"&&typeof BU.globalScope.msCrypto.subtle==="object"&&typeof BU.globalScope.msCrypto.subtle[A]==="function"}function yRB(A){var B=g9.util.hexToBytes(A.toString(16)),Q=new Uint8Array(B.length);for(var D=0;D<B.length;++D)Q[D]=B.charCodeAt(D);return Q}});
var Mv=E((WZ3,pMB)=>{var W7=M4();Uv1();a$0();N8();pMB.exports=W7.aes=W7.aes||{};W7.aes.startEncrypting=function(A,B,Q,D){var Z=qv1({key:A,output:Q,decrypt:!1,mode:D});return Z.start(B),Z};W7.aes.createEncryptionCipher=function(A,B){return qv1({key:A,output:null,decrypt:!1,mode:B})};W7.aes.startDecrypting=function(A,B,Q,D){var Z=qv1({key:A,output:Q,decrypt:!0,mode:D});return Z.start(B),Z};W7.aes.createDecryptionCipher=function(A,B){return qv1({key:A,output:null,decrypt:!0,mode:B})};W7.aes.Algorithm=function(A,B){if(!o$0)cMB();var Q=this;Q.name=A,Q.mode=new B({blockSize:16,cipher:{encrypt:function(D,Z){return r$0(Q._w,D,Z,!1)},decrypt:function(D,Z){return r$0(Q._w,D,Z,!0)}}}),Q._init=!1};W7.aes.Algorithm.prototype.initialize=function(A){if(this._init)return;var B=A.key,Q;if(typeof B==="string"&&(B.length===16||B.length===24||B.length===32))B=W7.util.createBuffer(B);else if(W7.util.isArray(B)&&(B.length===16||B.length===24||B.length===32)){Q=B,B=W7.util.createBuffer();for(var D=0;D<Q.length;++D)B.putByte(Q[D])}if(!W7.util.isArray(B)){Q=B,B=[];var Z=Q.length();if(Z===16||Z===24||Z===32){Z=Z>>>2;for(var D=0;D<Z;++D)B.push(Q.getInt32())}}if(!W7.util.isArray(B)||!(B.length===4||B.length===6||B.length===8))throw new Error("Invalid key parameter.");var G=this.mode.name,F=["CFB","OFB","CTR","GCM"].indexOf(G)!==-1;this._w=lMB(B,A.decrypt&&!F),this._init=!0};W7.aes._expandKey=function(A,B){if(!o$0)cMB();return lMB(A,B)};W7.aes._updateBlock=r$0;c11("AES-ECB",W7.cipher.modes.ecb);c11("AES-CBC",W7.cipher.modes.cbc);c11("AES-CFB",W7.cipher.modes.cfb);c11("AES-OFB",W7.cipher.modes.ofb);c11("AES-CTR",W7.cipher.modes.ctr);c11("AES-GCM",W7.cipher.modes.gcm);function c11(A,B){var Q=function(){return new W7.aes.Algorithm(A,B)};W7.cipher.registerAlgorithm(A,Q)}var o$0=!1,d11=4,$X,s$0,dMB,nm,S$;function cMB(){o$0=!0,dMB=[0,1,2,4,8,16,32,64,128,27,54];var A=new Array(256);for(var B=0;B<128;++B)A[B]=B<<1,A[B+128]=B+128<<1^283;$X=new Array(256),s$0=new Array(256),nm=new Array(4),S$=new Array(4);for(var B=0;B<4;++B)nm[B]=new Array(256),S$[B]=new Array(256);var Q=0,D=0,Z,G,F,I,Y,W,J;for(var B=0;B<256;++B){I=D^D<<1^D<<2^D<<3^D<<4,I=I>>8^I&255^99,$X[Q]=I,s$0[I]=Q,Y=A[I],Z=A[Q],G=A[Z],F=A[G],W=Y<<24^I<<16^I<<8^(I^Y),J=(Z^G^F)<<24^(Q^F)<<16^(Q^G^F)<<8^(Q^Z^F);for(var X=0;X<4;++X)nm[X][Q]=W,S$[X][I]=J,W=W<<24|W>>>8,J=J<<24|J>>>8;if(Q===0)Q=D=1;else Q=Z^A[A[A[Z^F]]],D^=A[A[D]]}}function lMB(A,B){var Q=A.slice(0),D,Z=1,G=Q.length,F=G+6+1,I=d11*F;for(var Y=G;Y<I;++Y){if(D=Q[Y-1],Y%G===0)D=$X[D>>>16&255]<<24^$X[D>>>8&255]<<16^$X[D&255]<<8^$X[D>>>24]^dMB[Z]<<24,Z++;else if(G>6&&Y%G===4)D=$X[D>>>24]<<24^$X[D>>>16&255]<<16^$X[D>>>8&255]<<8^$X[D&255];Q[Y]=Q[Y-G]^D}if(B){var W,J=S$[0],X=S$[1],V=S$[2],C=S$[3],K=Q.slice(0);I=Q.length;for(var Y=0,H=I-d11;Y<I;Y+=d11,H-=d11)if(Y===0||Y===I-d11)K[Y]=Q[H],K[Y+1]=Q[H+3],K[Y+2]=Q[H+2],K[Y+3]=Q[H+1];else for(var z=0;z<d11;++z)W=Q[H+z],K[Y+(3&-z)]=J[$X[W>>>24]]^X[$X[W>>>16&255]]^V[$X[W>>>8&255]]^C[$X[W&255]];Q=K}return Q}function r$0(A,B,Q,D){var Z=A.length/4-1,G,F,I,Y,W;if(D)G=S$[0],F=S$[1],I=S$[2],Y=S$[3],W=s$0;else G=nm[0],F=nm[1],I=nm[2],Y=nm[3],W=$X;var J,X,V,C,K,H,z;J=B[0]^A[0],X=B[D?3:1]^A[1],V=B[2]^A[2],C=B[D?1:3]^A[3];var $=3;for(var L=1;L<Z;++L)K=G[J>>>24]^F[X>>>16&255]^I[V>>>8&255]^Y[C&255]^A[++$],H=G[X>>>24]^F[V>>>16&255]^I[C>>>8&255]^Y[J&255]^A[++$],z=G[V>>>24]^F[C>>>16&255]^I[J>>>8&255]^Y[X&255]^A[++$],C=G[C>>>24]^F[J>>>16&255]^I[X>>>8&255]^Y[V&255]^A[++$],J=K,X=H,V=z;Q[0]=W[J>>>24]<<24^W[X>>>16&255]<<16^W[V>>>8&255]<<8^W[C&255]^A[++$],Q[D?3:1]=W[X>>>24]<<24^W[V>>>16&255]<<16^W[C>>>8&255]<<8^W[J&255]^A[++$],Q[2]=W[V>>>24]<<24^W[C>>>16&255]<<16^W[J>>>8&255]<<8^W[X&255]^A[++$],Q[D?1:3]=W[C>>>24]<<24^W[J>>>16&255]<<16^W[X>>>8&255]<<8^W[V&255]^A[++$]}function qv1(A){A=A||{};var B=(A.mode||"CBC").toUpperCase(),Q="AES-"+B,D;if(A.decrypt)D=W7.cipher.createDecipher(Q,A.key);else D=W7.cipher.createCipher(Q,A.key);var Z=D.start;return D.start=function(G,F){var I=null;if(F instanceof W7.util.ByteBuffer)I=F,F={};F=F||{},F.output=I,F.iv=G,Z.call(D,F)},D}});
var N8=E((FZ3,gMB)=>{var bMB=M4(),fMB=vMB(),O0=gMB.exports=bMB.util=bMB.util||{};(function(){if(typeof process!=="undefined"&&process.nextTick){if(O0.nextTick=process.nextTick,typeof setImmediate==="function")O0.setImmediate=setImmediate;else O0.setImmediate=O0.nextTick;return}if(typeof setImmediate==="function"){O0.setImmediate=function(){return setImmediate.apply(void 0,arguments)},O0.nextTick=function(I){return setImmediate(I)};return}if(O0.setImmediate=function(I){setTimeout(I,0)},typeof window!=="undefined"&&typeof window.postMessage==="function"){let I=function(Y){if(Y.source===window&&Y.data===A){Y.stopPropagation();var W=B.slice();B.length=0,W.forEach(function(J){J()})}};var F=I,A="forge.setImmediate",B=[];O0.setImmediate=function(Y){if(B.push(Y),B.length===1)window.postMessage(A,"*")},window.addEventListener("message",I,!0)}if(typeof MutationObserver!=="undefined"){var Q=Date.now(),D=!0,Z=document.createElement("div"),B=[];new MutationObserver(function(){var Y=B.slice();B.length=0,Y.forEach(function(W){W()})}).observe(Z,{attributes:!0});var G=O0.setImmediate;O0.setImmediate=function(Y){if(Date.now()-Q>15)Q=Date.now(),G(Y);else if(B.push(Y),B.length===1)Z.setAttribute("a",D=!D)}}O0.nextTick=O0.setImmediate})();O0.isNodejs=typeof process!=="undefined"&&process.versions&&process.versions.node;O0.globalScope=function(){if(O0.isNodejs)return global;return typeof self==="undefined"?window:self}();O0.isArray=Array.isArray||function(A){return Object.prototype.toString.call(A)==="[object Array]"};O0.isArrayBuffer=function(A){return typeof ArrayBuffer!=="undefined"&&A instanceof ArrayBuffer};O0.isArrayBufferView=function(A){return A&&O0.isArrayBuffer(A.buffer)&&A.byteLength!==void 0};function EG1(A){if(!(A===8||A===16||A===24||A===32))throw new Error("Only 8, 16, 24, or 32 bits supported: "+A)}O0.ByteBuffer=c$0;function c$0(A){if(this.data="",this.read=0,typeof A==="string")this.data=A;else if(O0.isArrayBuffer(A)||O0.isArrayBufferView(A))if(typeof Buffer!=="undefined"&&A instanceof Buffer)this.data=A.toString("binary");else{var B=new Uint8Array(A);try{this.data=String.fromCharCode.apply(null,B)}catch(D){for(var Q=0;Q<B.length;++Q)this.putByte(B[Q])}}else if(A instanceof c$0||typeof A==="object"&&typeof A.data==="string"&&typeof A.read==="number")this.data=A.data,this.read=A.read;this._constructedStringLength=0}O0.ByteStringBuffer=c$0;var f78=4096;O0.ByteStringBuffer.prototype._optimizeConstructedString=function(A){if(this._constructedStringLength+=A,this._constructedStringLength>f78)this.data.substr(0,1),this._constructedStringLength=0};O0.ByteStringBuffer.prototype.length=function(){return this.data.length-this.read};O0.ByteStringBuffer.prototype.isEmpty=function(){return this.length()<=0};O0.ByteStringBuffer.prototype.putByte=function(A){return this.putBytes(String.fromCharCode(A))};O0.ByteStringBuffer.prototype.fillWithByte=function(A,B){A=String.fromCharCode(A);var Q=this.data;while(B>0){if(B&1)Q+=A;if(B>>>=1,B>0)A+=A}return this.data=Q,this._optimizeConstructedString(B),this};O0.ByteStringBuffer.prototype.putBytes=function(A){return this.data+=A,this._optimizeConstructedString(A.length),this};O0.ByteStringBuffer.prototype.putString=function(A){return this.putBytes(O0.encodeUtf8(A))};O0.ByteStringBuffer.prototype.putInt16=function(A){return this.putBytes(String.fromCharCode(A>>8&255)+String.fromCharCode(A&255))};O0.ByteStringBuffer.prototype.putInt24=function(A){return this.putBytes(String.fromCharCode(A>>16&255)+String.fromCharCode(A>>8&255)+String.fromCharCode(A&255))};O0.ByteStringBuffer.prototype.putInt32=function(A){return this.putBytes(String.fromCharCode(A>>24&255)+String.fromCharCode(A>>16&255)+String.fromCharCode(A>>8&255)+String.fromCharCode(A&255))};O0.ByteStringBuffer.prototype.putInt16Le=function(A){return this.putBytes(String.fromCharCode(A&255)+String.fromCharCode(A>>8&255))};O0.ByteStringBuffer.prototype.putInt24Le=function(A){return this.putBytes(String.fromCharCode(A&255)+String.fromCharCode(A>>8&255)+String.fromCharCode(A>>16&255))};O0.ByteStringBuffer.prototype.putInt32Le=function(A){return this.putBytes(String.fromCharCode(A&255)+String.fromCharCode(A>>8&255)+String.fromCharCode(A>>16&255)+String.fromCharCode(A>>24&255))};O0.ByteStringBuffer.prototype.putInt=function(A,B){EG1(B);var Q="";do B-=8,Q+=String.fromCharCode(A>>B&255);while(B>0);return this.putBytes(Q)};O0.ByteStringBuffer.prototype.putSignedInt=function(A,B){if(A<0)A+=2<<B-1;return this.putInt(A,B)};O0.ByteStringBuffer.prototype.putBuffer=function(A){return this.putBytes(A.getBytes())};O0.ByteStringBuffer.prototype.getByte=function(){return this.data.charCodeAt(this.read++)};O0.ByteStringBuffer.prototype.getInt16=function(){var A=this.data.charCodeAt(this.read)<<8^this.data.charCodeAt(this.read+1);return this.read+=2,A};O0.ByteStringBuffer.prototype.getInt24=function(){var A=this.data.charCodeAt(this.read)<<16^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2);return this.read+=3,A};O0.ByteStringBuffer.prototype.getInt32=function(){var A=this.data.charCodeAt(this.read)<<24^this.data.charCodeAt(this.read+1)<<16^this.data.charCodeAt(this.read+2)<<8^this.data.charCodeAt(this.read+3);return this.read+=4,A};O0.ByteStringBuffer.prototype.getInt16Le=function(){var A=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8;return this.read+=2,A};O0.ByteStringBuffer.prototype.getInt24Le=function(){var A=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2)<<16;return this.read+=3,A};O0.ByteStringBuffer.prototype.getInt32Le=function(){var A=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2)<<16^this.data.charCodeAt(this.read+3)<<24;return this.read+=4,A};O0.ByteStringBuffer.prototype.getInt=function(A){EG1(A);var B=0;do B=(B<<8)+this.data.charCodeAt(this.read++),A-=8;while(A>0);return B};O0.ByteStringBuffer.prototype.getSignedInt=function(A){var B=this.getInt(A),Q=2<<A-2;if(B>=Q)B-=Q<<1;return B};O0.ByteStringBuffer.prototype.getBytes=function(A){var B;if(A)A=Math.min(this.length(),A),B=this.data.slice(this.read,this.read+A),this.read+=A;else if(A===0)B="";else B=this.read===0?this.data:this.data.slice(this.read),this.clear();return B};O0.ByteStringBuffer.prototype.bytes=function(A){return typeof A==="undefined"?this.data.slice(this.read):this.data.slice(this.read,this.read+A)};O0.ByteStringBuffer.prototype.at=function(A){return this.data.charCodeAt(this.read+A)};O0.ByteStringBuffer.prototype.setAt=function(A,B){return this.data=this.data.substr(0,this.read+A)+String.fromCharCode(B)+this.data.substr(this.read+A+1),this};O0.ByteStringBuffer.prototype.last=function(){return this.data.charCodeAt(this.data.length-1)};O0.ByteStringBuffer.prototype.copy=function(){var A=O0.createBuffer(this.data);return A.read=this.read,A};O0.ByteStringBuffer.prototype.compact=function(){if(this.read>0)this.data=this.data.slice(this.read),this.read=0;return this};O0.ByteStringBuffer.prototype.clear=function(){return this.data="",this.read=0,this};O0.ByteStringBuffer.prototype.truncate=function(A){var B=Math.max(0,this.length()-A);return this.data=this.data.substr(this.read,B),this.read=0,this};O0.ByteStringBuffer.prototype.toHex=function(){var A="";for(var B=this.read;B<this.data.length;++B){var Q=this.data.charCodeAt(B);if(Q<16)A+="0";A+=Q.toString(16)}return A};O0.ByteStringBuffer.prototype.toString=function(){return O0.decodeUtf8(this.bytes())};function h78(A,B){B=B||{},this.read=B.readOffset||0,this.growSize=B.growSize||1024;var Q=O0.isArrayBuffer(A),D=O0.isArrayBufferView(A);if(Q||D){if(Q)this.data=new DataView(A);else this.data=new DataView(A.buffer,A.byteOffset,A.byteLength);this.write="writeOffset"in B?B.writeOffset:this.data.byteLength;return}if(this.data=new DataView(new ArrayBuffer(0)),this.write=0,A!==null&&A!==void 0)this.putBytes(A);if("writeOffset"in B)this.write=B.writeOffset}O0.DataBuffer=h78;O0.DataBuffer.prototype.length=function(){return this.write-this.read};O0.DataBuffer.prototype.isEmpty=function(){return this.length()<=0};O0.DataBuffer.prototype.accommodate=function(A,B){if(this.length()>=A)return this;B=Math.max(B||this.growSize,A);var Q=new Uint8Array(this.data.buffer,this.data.byteOffset,this.data.byteLength),D=new Uint8Array(this.length()+B);return D.set(Q),this.data=new DataView(D.buffer),this};O0.DataBuffer.prototype.putByte=function(A){return this.accommodate(1),this.data.setUint8(this.write++,A),this};O0.DataBuffer.prototype.fillWithByte=function(A,B){this.accommodate(B);for(var Q=0;Q<B;++Q)this.data.setUint8(A);return this};O0.DataBuffer.prototype.putBytes=function(A,B){if(O0.isArrayBufferView(A)){var Q=new Uint8Array(A.buffer,A.byteOffset,A.byteLength),D=Q.byteLength-Q.byteOffset;this.accommodate(D);var Z=new Uint8Array(this.data.buffer,this.write);return Z.set(Q),this.write+=D,this}if(O0.isArrayBuffer(A)){var Q=new Uint8Array(A);this.accommodate(Q.byteLength);var Z=new Uint8Array(this.data.buffer);return Z.set(Q,this.write),this.write+=Q.byteLength,this}if(A instanceof O0.DataBuffer||typeof A==="object"&&typeof A.read==="number"&&typeof A.write==="number"&&O0.isArrayBufferView(A.data)){var Q=new Uint8Array(A.data.byteLength,A.read,A.length());this.accommodate(Q.byteLength);var Z=new Uint8Array(A.data.byteLength,this.write);return Z.set(Q),this.write+=Q.byteLength,this}if(A instanceof O0.ByteStringBuffer)A=A.data,B="binary";if(B=B||"binary",typeof A==="string"){var G;if(B==="hex")return this.accommodate(Math.ceil(A.length/2)),G=new Uint8Array(this.data.buffer,this.write),this.write+=O0.binary.hex.decode(A,G,this.write),this;if(B==="base64")return this.accommodate(Math.ceil(A.length/4)*3),G=new Uint8Array(this.data.buffer,this.write),this.write+=O0.binary.base64.decode(A,G,this.write),this;if(B==="utf8")A=O0.encodeUtf8(A),B="binary";if(B==="binary"||B==="raw")return this.accommodate(A.length),G=new Uint8Array(this.data.buffer,this.write),this.write+=O0.binary.raw.decode(G),this;if(B==="utf16")return this.accommodate(A.length*2),G=new Uint16Array(this.data.buffer,this.write),this.write+=O0.text.utf16.encode(G),this;throw new Error("Invalid encoding: "+B)}throw Error("Invalid parameter: "+A)};O0.DataBuffer.prototype.putBuffer=function(A){return this.putBytes(A),A.clear(),this};O0.DataBuffer.prototype.putString=function(A){return this.putBytes(A,"utf16")};O0.DataBuffer.prototype.putInt16=function(A){return this.accommodate(2),this.data.setInt16(this.write,A),this.write+=2,this};O0.DataBuffer.prototype.putInt24=function(A){return this.accommodate(3),this.data.setInt16(this.write,A>>8&65535),this.data.setInt8(this.write,A>>16&255),this.write+=3,this};O0.DataBuffer.prototype.putInt32=function(A){return this.accommodate(4),this.data.setInt32(this.write,A),this.write+=4,this};O0.DataBuffer.prototype.putInt16Le=function(A){return this.accommodate(2),this.data.setInt16(this.write,A,!0),this.write+=2,this};O0.DataBuffer.prototype.putInt24Le=function(A){return this.accommodate(3),this.data.setInt8(this.write,A>>16&255),this.data.setInt16(this.write,A>>8&65535,!0),this.write+=3,this};O0.DataBuffer.prototype.putInt32Le=function(A){return this.accommodate(4),this.data.setInt32(this.write,A,!0),this.write+=4,this};O0.DataBuffer.prototype.putInt=function(A,B){EG1(B),this.accommodate(B/8);do B-=8,this.data.setInt8(this.write++,A>>B&255);while(B>0);return this};O0.DataBuffer.prototype.putSignedInt=function(A,B){if(EG1(B),this.accommodate(B/8),A<0)A+=2<<B-1;return this.putInt(A,B)};O0.DataBuffer.prototype.getByte=function(){return this.data.getInt8(this.read++)};O0.DataBuffer.prototype.getInt16=function(){var A=this.data.getInt16(this.read);return this.read+=2,A};O0.DataBuffer.prototype.getInt24=function(){var A=this.data.getInt16(this.read)<<8^this.data.getInt8(this.read+2);return this.read+=3,A};O0.DataBuffer.prototype.getInt32=function(){var A=this.data.getInt32(this.read);return this.read+=4,A};O0.DataBuffer.prototype.getInt16Le=function(){var A=this.data.getInt16(this.read,!0);return this.read+=2,A};O0.DataBuffer.prototype.getInt24Le=function(){var A=this.data.getInt8(this.read)^this.data.getInt16(this.read+1,!0)<<8;return this.read+=3,A};O0.DataBuffer.prototype.getInt32Le=function(){var A=this.data.getInt32(this.read,!0);return this.read+=4,A};O0.DataBuffer.prototype.getInt=function(A){EG1(A);var B=0;do B=(B<<8)+this.data.getInt8(this.read++),A-=8;while(A>0);return B};O0.DataBuffer.prototype.getSignedInt=function(A){var B=this.getInt(A),Q=2<<A-2;if(B>=Q)B-=Q<<1;return B};O0.DataBuffer.prototype.getBytes=function(A){var B;if(A)A=Math.min(this.length(),A),B=this.data.slice(this.read,this.read+A),this.read+=A;else if(A===0)B="";else B=this.read===0?this.data:this.data.slice(this.read),this.clear();return B};O0.DataBuffer.prototype.bytes=function(A){return typeof A==="undefined"?this.data.slice(this.read):this.data.slice(this.read,this.read+A)};O0.DataBuffer.prototype.at=function(A){return this.data.getUint8(this.read+A)};O0.DataBuffer.prototype.setAt=function(A,B){return this.data.setUint8(A,B),this};O0.DataBuffer.prototype.last=function(){return this.data.getUint8(this.write-1)};O0.DataBuffer.prototype.copy=function(){return new O0.DataBuffer(this)};O0.DataBuffer.prototype.compact=function(){if(this.read>0){var A=new Uint8Array(this.data.buffer,this.read),B=new Uint8Array(A.byteLength);B.set(A),this.data=new DataView(B),this.write-=this.read,this.read=0}return this};O0.DataBuffer.prototype.clear=function(){return this.data=new DataView(new ArrayBuffer(0)),this.read=this.write=0,this};O0.DataBuffer.prototype.truncate=function(A){return this.write=Math.max(0,this.length()-A),this.read=Math.min(this.read,this.write),this};O0.DataBuffer.prototype.toHex=function(){var A="";for(var B=this.read;B<this.data.byteLength;++B){var Q=this.data.getUint8(B);if(Q<16)A+="0";A+=Q.toString(16)}return A};O0.DataBuffer.prototype.toString=function(A){var B=new Uint8Array(this.data,this.read,this.length());if(A=A||"utf8",A==="binary"||A==="raw")return O0.binary.raw.encode(B);if(A==="hex")return O0.binary.hex.encode(B);if(A==="base64")return O0.binary.base64.encode(B);if(A==="utf8")return O0.text.utf8.decode(B);if(A==="utf16")return O0.text.utf16.decode(B);throw new Error("Invalid encoding: "+A)};O0.createBuffer=function(A,B){if(B=B||"raw",A!==void 0&&B==="utf8")A=O0.encodeUtf8(A);return new O0.ByteBuffer(A)};O0.fillString=function(A,B){var Q="";while(B>0){if(B&1)Q+=A;if(B>>>=1,B>0)A+=A}return Q};O0.xorBytes=function(A,B,Q){var D="",Z="",G="",F=0,I=0;for(;Q>0;--Q,++F){if(Z=A.charCodeAt(F)^B.charCodeAt(F),I>=10)D+=G,G="",I=0;G+=String.fromCharCode(Z),++I}return D+=G,D};O0.hexToBytes=function(A){var B="",Q=0;if(A.length&!0)Q=1,B+=String.fromCharCode(parseInt(A[0],16));for(;Q<A.length;Q+=2)B+=String.fromCharCode(parseInt(A.substr(Q,2),16));return B};O0.bytesToHex=function(A){return O0.createBuffer(A).toHex()};O0.int32ToBytes=function(A){return String.fromCharCode(A>>24&255)+String.fromCharCode(A>>16&255)+String.fromCharCode(A>>8&255)+String.fromCharCode(A&255)};var Nv="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Lv=[62,-1,-1,-1,63,52,53,54,55,56,57,58,59,60,61,-1,-1,-1,64,-1,-1,-1,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,-1,-1,-1,-1,-1,-1,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],hMB="123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";O0.encode64=function(A,B){var Q="",D="",Z,G,F,I=0;while(I<A.length){if(Z=A.charCodeAt(I++),G=A.charCodeAt(I++),F=A.charCodeAt(I++),Q+=Nv.charAt(Z>>2),Q+=Nv.charAt((Z&3)<<4|G>>4),isNaN(G))Q+="==";else Q+=Nv.charAt((G&15)<<2|F>>6),Q+=isNaN(F)?"=":Nv.charAt(F&63);if(B&&Q.length>B)D+=Q.substr(0,B)+`\r
`,Q=Q.substr(B)}return D+=Q,D};O0.decode64=function(A){A=A.replace(/[^A-Za-z0-9\+\/\=]/g,"");var B="",Q,D,Z,G,F=0;while(F<A.length)if(Q=Lv[A.charCodeAt(F++)-43],D=Lv[A.charCodeAt(F++)-43],Z=Lv[A.charCodeAt(F++)-43],G=Lv[A.charCodeAt(F++)-43],B+=String.fromCharCode(Q<<2|D>>4),Z!==64){if(B+=String.fromCharCode((D&15)<<4|Z>>2),G!==64)B+=String.fromCharCode((Z&3)<<6|G)}return B};O0.encodeUtf8=function(A){return unescape(encodeURIComponent(A))};O0.decodeUtf8=function(A){return decodeURIComponent(escape(A))};O0.binary={raw:{},hex:{},base64:{},base58:{},baseN:{encode:fMB.encode,decode:fMB.decode}};O0.binary.raw.encode=function(A){return String.fromCharCode.apply(null,A)};O0.binary.raw.decode=function(A,B,Q){var D=B;if(!D)D=new Uint8Array(A.length);Q=Q||0;var Z=Q;for(var G=0;G<A.length;++G)D[Z++]=A.charCodeAt(G);return B?Z-Q:D};O0.binary.hex.encode=O0.bytesToHex;O0.binary.hex.decode=function(A,B,Q){var D=B;if(!D)D=new Uint8Array(Math.ceil(A.length/2));Q=Q||0;var Z=0,G=Q;if(A.length&1)Z=1,D[G++]=parseInt(A[0],16);for(;Z<A.length;Z+=2)D[G++]=parseInt(A.substr(Z,2),16);return B?G-Q:D};O0.binary.base64.encode=function(A,B){var Q="",D="",Z,G,F,I=0;while(I<A.byteLength){if(Z=A[I++],G=A[I++],F=A[I++],Q+=Nv.charAt(Z>>2),Q+=Nv.charAt((Z&3)<<4|G>>4),isNaN(G))Q+="==";else Q+=Nv.charAt((G&15)<<2|F>>6),Q+=isNaN(F)?"=":Nv.charAt(F&63);if(B&&Q.length>B)D+=Q.substr(0,B)+`\r
`,Q=Q.substr(B)}return D+=Q,D};O0.binary.base64.decode=function(A,B,Q){var D=B;if(!D)D=new Uint8Array(Math.ceil(A.length/4)*3);A=A.replace(/[^A-Za-z0-9\+\/\=]/g,""),Q=Q||0;var Z,G,F,I,Y=0,W=Q;while(Y<A.length)if(Z=Lv[A.charCodeAt(Y++)-43],G=Lv[A.charCodeAt(Y++)-43],F=Lv[A.charCodeAt(Y++)-43],I=Lv[A.charCodeAt(Y++)-43],D[W++]=Z<<2|G>>4,F!==64){if(D[W++]=(G&15)<<4|F>>2,I!==64)D[W++]=(F&3)<<6|I}return B?W-Q:D.subarray(0,W)};O0.binary.base58.encode=function(A,B){return O0.binary.baseN.encode(A,hMB,B)};O0.binary.base58.decode=function(A,B){return O0.binary.baseN.decode(A,hMB,B)};O0.text={utf8:{},utf16:{}};O0.text.utf8.encode=function(A,B,Q){A=O0.encodeUtf8(A);var D=B;if(!D)D=new Uint8Array(A.length);Q=Q||0;var Z=Q;for(var G=0;G<A.length;++G)D[Z++]=A.charCodeAt(G);return B?Z-Q:D};O0.text.utf8.decode=function(A){return O0.decodeUtf8(String.fromCharCode.apply(null,A))};O0.text.utf16.encode=function(A,B,Q){var D=B;if(!D)D=new Uint8Array(A.length*2);var Z=new Uint16Array(D.buffer);Q=Q||0;var G=Q,F=Q;for(var I=0;I<A.length;++I)Z[F++]=A.charCodeAt(I),G+=2;return B?G-Q:D};O0.text.utf16.decode=function(A){return String.fromCharCode.apply(null,new Uint16Array(A.buffer))};O0.deflate=function(A,B,Q){if(B=O0.decode64(A.deflate(O0.encode64(B)).rval),Q){var D=2,Z=B.charCodeAt(1);if(Z&32)D=6;B=B.substring(D,B.length-4)}return B};O0.inflate=function(A,B,Q){var D=A.inflate(O0.encode64(B)).rval;return D===null?null:O0.decode64(D)};var l$0=function(A,B,Q){if(!A)throw new Error("WebStorage not available.");var D;if(Q===null)D=A.removeItem(B);else Q=O0.encode64(JSON.stringify(Q)),D=A.setItem(B,Q);if(typeof D!=="undefined"&&D.rval!==!0){var Z=new Error(D.error.message);throw Z.id=D.error.id,Z.name=D.error.name,Z}},p$0=function(A,B){if(!A)throw new Error("WebStorage not available.");var Q=A.getItem(B);if(A.init)if(Q.rval===null){if(Q.error){var D=new Error(Q.error.message);throw D.id=Q.error.id,D.name=Q.error.name,D}Q=null}else Q=Q.rval;if(Q!==null)Q=JSON.parse(O0.decode64(Q));return Q},g78=function(A,B,Q,D){var Z=p$0(A,B);if(Z===null)Z={};Z[Q]=D,l$0(A,B,Z)},u78=function(A,B,Q){var D=p$0(A,B);if(D!==null)D=Q in D?D[Q]:null;return D},m78=function(A,B,Q){var D=p$0(A,B);if(D!==null&&Q in D){delete D[Q];var Z=!0;for(var G in D){Z=!1;break}if(Z)D=null;l$0(A,B,D)}},d78=function(A,B){l$0(A,B,null)},Ev1=function(A,B,Q){var D=null;if(typeof Q==="undefined")Q=["web","flash"];var Z,G=!1,F=null;for(var I in Q){Z=Q[I];try{if(Z==="flash"||Z==="both"){if(B[0]===null)throw new Error("Flash local storage not available.");D=A.apply(this,B),G=Z==="flash"}if(Z==="web"||Z==="both")B[0]=localStorage,D=A.apply(this,B),G=!0}catch(Y){F=Y}if(G)break}if(!G)throw F;return D};O0.setItem=function(A,B,Q,D,Z){Ev1(g78,arguments,Z)};O0.getItem=function(A,B,Q,D){return Ev1(u78,arguments,D)};O0.removeItem=function(A,B,Q,D){Ev1(m78,arguments,D)};O0.clearItems=function(A,B,Q){Ev1(d78,arguments,Q)};O0.isEmpty=function(A){for(var B in A)if(A.hasOwnProperty(B))return!1;return!0};O0.format=function(A){var B=/%./g,Q,D,Z=0,G=[],F=0;while(Q=B.exec(A)){if(D=A.substring(F,B.lastIndex-2),D.length>0)G.push(D);F=B.lastIndex;var I=Q[0][1];switch(I){case"s":case"o":if(Z<arguments.length)G.push(arguments[Z+++1]);else G.push("<?>");break;case"%":G.push("%");break;default:G.push("<%"+I+"?>")}}return G.push(A.substring(F)),G.join("")};O0.formatNumber=function(A,B,Q,D){var Z=A,G=isNaN(B=Math.abs(B))?2:B,F=Q===void 0?",":Q,I=D===void 0?".":D,Y=Z<0?"-":"",W=parseInt(Z=Math.abs(+Z||0).toFixed(G),10)+"",J=W.length>3?W.length%3:0;return Y+(J?W.substr(0,J)+I:"")+W.substr(J).replace(/(\d{3})(?=\d)/g,"$1"+I)+(G?F+Math.abs(Z-W).toFixed(G).slice(2):"")};O0.formatSize=function(A){if(A>=1073741824)A=O0.formatNumber(A/1073741824,2,".","")+" GiB";else if(A>=1048576)A=O0.formatNumber(A/1048576,2,".","")+" MiB";else if(A>=1024)A=O0.formatNumber(A/1024,0)+" KiB";else A=O0.formatNumber(A,0)+" bytes";return A};O0.bytesFromIP=function(A){if(A.indexOf(".")!==-1)return O0.bytesFromIPv4(A);if(A.indexOf(":")!==-1)return O0.bytesFromIPv6(A);return null};O0.bytesFromIPv4=function(A){if(A=A.split("."),A.length!==4)return null;var B=O0.createBuffer();for(var Q=0;Q<A.length;++Q){var D=parseInt(A[Q],10);if(isNaN(D))return null;B.putByte(D)}return B.getBytes()};O0.bytesFromIPv6=function(A){var B=0;A=A.split(":").filter(function(F){if(F.length===0)++B;return!0});var Q=(8-A.length+B)*2,D=O0.createBuffer();for(var Z=0;Z<8;++Z){if(!A[Z]||A[Z].length===0){D.fillWithByte(0,Q),Q=0;continue}var G=O0.hexToBytes(A[Z]);if(G.length<2)D.putByte(0);D.putBytes(G)}return D.getBytes()};O0.bytesToIP=function(A){if(A.length===4)return O0.bytesToIPv4(A);if(A.length===16)return O0.bytesToIPv6(A);return null};O0.bytesToIPv4=function(A){if(A.length!==4)return null;var B=[];for(var Q=0;Q<A.length;++Q)B.push(A.charCodeAt(Q));return B.join(".")};O0.bytesToIPv6=function(A){if(A.length!==16)return null;var B=[],Q=[],D=0;for(var Z=0;Z<A.length;Z+=2){var G=O0.bytesToHex(A[Z]+A[Z+1]);while(G[0]==="0"&&G!=="0")G=G.substr(1);if(G==="0"){var F=Q[Q.length-1],I=B.length;if(!F||I!==F.end+1)Q.push({start:I,end:I});else if(F.end=I,F.end-F.start>Q[D].end-Q[D].start)D=Q.length-1}B.push(G)}if(Q.length>0){var Y=Q[D];if(Y.end-Y.start>0){if(B.splice(Y.start,Y.end-Y.start+1,""),Y.start===0)B.unshift("");if(Y.end===7)B.push("")}}return B.join(":")};O0.estimateCores=function(A,B){if(typeof A==="function")B=A,A={};if(A=A||{},"cores"in O0&&!A.update)return B(null,O0.cores);if(typeof navigator!=="undefined"&&"hardwareConcurrency"in navigator&&navigator.hardwareConcurrency>0)return O0.cores=navigator.hardwareConcurrency,B(null,O0.cores);if(typeof Worker==="undefined")return O0.cores=1,B(null,O0.cores);if(typeof Blob==="undefined")return O0.cores=2,B(null,O0.cores);var Q=URL.createObjectURL(new Blob(["(",function(){self.addEventListener("message",function(F){var I=Date.now(),Y=I+4;while(Date.now()<Y);self.postMessage({st:I,et:Y})})}.toString(),")()"],{type:"application/javascript"}));D([],5,16);function D(F,I,Y){if(I===0){var W=Math.floor(F.reduce(function(J,X){return J+X},0)/F.length);return O0.cores=Math.max(1,W),URL.revokeObjectURL(Q),B(null,O0.cores)}Z(Y,function(J,X){F.push(G(Y,X)),D(F,I-1,Y)})}function Z(F,I){var Y=[],W=[];for(var J=0;J<F;++J){var X=new Worker(Q);X.addEventListener("message",function(V){if(W.push(V.data),W.length===F){for(var C=0;C<F;++C)Y[C].terminate();I(null,W)}}),Y.push(X)}for(var J=0;J<F;++J)Y[J].postMessage(J)}function G(F,I){var Y=[];for(var W=0;W<F;++W){var J=I[W],X=Y[W]=[];for(var V=0;V<F;++V){if(W===V)continue;var C=I[V];if(J.st>C.st&&J.st<C.et||C.st>J.st&&C.st<J.et)X.push(V)}}return Y.reduce(function(K,H){return Math.max(K,H.length)},0)}}});
var Nq0=E((xZ3,AOB)=>{var Sv=M4();j$();Rv();Hq0();am();Sv1();$q0();bv1();MG1();N8();gv1();var qq0=Sv.asn1,t11=AOB.exports=Sv.pki=Sv.pki||{};t11.pemToDer=function(A){var B=Sv.pem.decode(A)[0];if(B.procType&&B.procType.type==="ENCRYPTED")throw new Error("Could not convert PEM to DER; PEM is encrypted.");return Sv.util.createBuffer(B.body)};t11.privateKeyFromPem=function(A){var B=Sv.pem.decode(A)[0];if(B.type!=="PRIVATE KEY"&&B.type!=="RSA PRIVATE KEY"){var Q=new Error('Could not convert private key from PEM; PEM header type is not "PRIVATE KEY" or "RSA PRIVATE KEY".');throw Q.headerType=B.type,Q}if(B.procType&&B.procType.type==="ENCRYPTED")throw new Error("Could not convert private key from PEM; PEM is encrypted.");var D=qq0.fromDer(B.body);return t11.privateKeyFromAsn1(D)};t11.privateKeyToPem=function(A,B){var Q={type:"RSA PRIVATE KEY",body:qq0.toDer(t11.privateKeyToAsn1(A)).getBytes()};return Sv.pem.encode(Q,{maxline:B})};t11.privateKeyInfoToPem=function(A,B){var Q={type:"PRIVATE KEY",body:qq0.toDer(A).getBytes()};return Sv.pem.encode(Q,{maxline:B})}});
var POB=E((gZ3,TOB)=>{var AW=M4();LG1();eE();kq0();N8();var qOB=zOB(),BF8=qOB.publicKeyValidator,QF8=qOB.privateKeyValidator;if(typeof xq0==="undefined")xq0=AW.jsbn.BigInteger;var xq0,vq0=AW.util.ByteBuffer,eV=typeof Buffer==="undefined"?Uint8Array:Buffer;AW.pki=AW.pki||{};TOB.exports=AW.pki.ed25519=AW.ed25519=AW.ed25519||{};var K6=AW.ed25519;K6.constants={};K6.constants.PUBLIC_KEY_BYTE_LENGTH=32;K6.constants.PRIVATE_KEY_BYTE_LENGTH=64;K6.constants.SEED_BYTE_LENGTH=32;K6.constants.SIGN_BYTE_LENGTH=64;K6.constants.HASH_BYTE_LENGTH=64;K6.generateKeyPair=function(A){A=A||{};var B=A.seed;if(B===void 0)B=AW.random.getBytesSync(K6.constants.SEED_BYTE_LENGTH);else if(typeof B==="string"){if(B.length!==K6.constants.SEED_BYTE_LENGTH)throw new TypeError('"seed" must be '+K6.constants.SEED_BYTE_LENGTH+" bytes in length.")}else if(!(B instanceof Uint8Array))throw new TypeError('"seed" must be a node.js Buffer, Uint8Array, or a binary string.');B=FS({message:B,encoding:"binary"});var Q=new eV(K6.constants.PUBLIC_KEY_BYTE_LENGTH),D=new eV(K6.constants.PRIVATE_KEY_BYTE_LENGTH);for(var Z=0;Z<32;++Z)D[Z]=B[Z];return FF8(Q,D),{publicKey:Q,privateKey:D}};K6.privateKeyFromAsn1=function(A){var B={},Q=[],D=AW.asn1.validate(A,QF8,B,Q);if(!D){var Z=new Error("Invalid Key.");throw Z.errors=Q,Z}var G=AW.asn1.derToOid(B.privateKeyOid),F=AW.oids.EdDSA25519;if(G!==F)throw new Error('Invalid OID "'+G+'"; OID must be "'+F+'".');var I=B.privateKey,Y=FS({message:AW.asn1.fromDer(I).value,encoding:"binary"});return{privateKeyBytes:Y}};K6.publicKeyFromAsn1=function(A){var B={},Q=[],D=AW.asn1.validate(A,BF8,B,Q);if(!D){var Z=new Error("Invalid Key.");throw Z.errors=Q,Z}var G=AW.asn1.derToOid(B.publicKeyOid),F=AW.oids.EdDSA25519;if(G!==F)throw new Error('Invalid OID "'+G+'"; OID must be "'+F+'".');var I=B.ed25519PublicKey;if(I.length!==K6.constants.PUBLIC_KEY_BYTE_LENGTH)throw new Error("Key length is invalid.");return FS({message:I,encoding:"binary"})};K6.publicKeyFromPrivateKey=function(A){A=A||{};var B=FS({message:A.privateKey,encoding:"binary"});if(B.length!==K6.constants.PRIVATE_KEY_BYTE_LENGTH)throw new TypeError('"options.privateKey" must have a byte length of '+K6.constants.PRIVATE_KEY_BYTE_LENGTH);var Q=new eV(K6.constants.PUBLIC_KEY_BYTE_LENGTH);for(var D=0;D<Q.length;++D)Q[D]=B[32+D];return Q};K6.sign=function(A){A=A||{};var B=FS(A),Q=FS({message:A.privateKey,encoding:"binary"});if(Q.length===K6.constants.SEED_BYTE_LENGTH){var D=K6.generateKeyPair({seed:Q});Q=D.privateKey}else if(Q.length!==K6.constants.PRIVATE_KEY_BYTE_LENGTH)throw new TypeError('"options.privateKey" must have a byte length of '+K6.constants.SEED_BYTE_LENGTH+" or "+K6.constants.PRIVATE_KEY_BYTE_LENGTH);var Z=new eV(K6.constants.SIGN_BYTE_LENGTH+B.length);IF8(Z,B,B.length,Q);var G=new eV(K6.constants.SIGN_BYTE_LENGTH);for(var F=0;F<G.length;++F)G[F]=Z[F];return G};K6.verify=function(A){A=A||{};var B=FS(A);if(A.signature===void 0)throw new TypeError('"options.signature" must be a node.js Buffer, a Uint8Array, a forge ByteBuffer, or a binary string.');var Q=FS({message:A.signature,encoding:"binary"});if(Q.length!==K6.constants.SIGN_BYTE_LENGTH)throw new TypeError('"options.signature" must have a byte length of '+K6.constants.SIGN_BYTE_LENGTH);var D=FS({message:A.publicKey,encoding:"binary"});if(D.length!==K6.constants.PUBLIC_KEY_BYTE_LENGTH)throw new TypeError('"options.publicKey" must have a byte length of '+K6.constants.PUBLIC_KEY_BYTE_LENGTH);var Z=new eV(K6.constants.SIGN_BYTE_LENGTH+B.length),G=new eV(K6.constants.SIGN_BYTE_LENGTH+B.length),F;for(F=0;F<K6.constants.SIGN_BYTE_LENGTH;++F)Z[F]=Q[F];for(F=0;F<B.length;++F)Z[F+K6.constants.SIGN_BYTE_LENGTH]=B[F];return YF8(G,Z,Z.length,D)>=0};function FS(A){var B=A.message;if(B instanceof Uint8Array||B instanceof eV)return B;var Q=A.encoding;if(B===void 0)if(A.md)B=A.md.digest().getBytes(),Q="binary";else throw new TypeError('"options.message" or "options.md" not specified.');if(typeof B==="string"&&!Q)throw new TypeError('"options.encoding" must be "binary" or "utf8".');if(typeof B==="string"){if(typeof Buffer!=="undefined")return Buffer.from(B,Q);B=new vq0(B,Q)}else if(!(B instanceof vq0))throw new TypeError('"options.message" must be a node.js Buffer, a Uint8Array, a forge ByteBuffer, or a string with "options.encoding" specifying its encoding.');var D=new eV(B.length());for(var Z=0;Z<D.length;++Z)D[Z]=B.at(Z);return D}var bq0=GQ(),cv1=GQ([1]),DF8=GQ([30883,4953,19914,30187,55467,16705,2637,112,59544,30585,16505,36039,65139,11119,27886,20995]),ZF8=GQ([61785,9906,39828,60374,45398,33411,5274,224,53552,61171,33010,6542,64743,22239,55772,9222]),EOB=GQ([54554,36645,11616,51542,42930,38181,51040,26924,56412,64982,57905,49316,21502,52590,14035,8553]),UOB=GQ([26200,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214]),yq0=new Float64Array([237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16]),GF8=GQ([41136,18958,6951,50414,58488,44335,6150,12099,55207,15867,153,11085,57099,20417,9344,11139]);function SG1(A,B){var Q=AW.md.sha512.create(),D=new vq0(A);Q.update(D.getBytes(B),"binary");var Z=Q.digest().getBytes();if(typeof Buffer!=="undefined")return Buffer.from(Z,"binary");var G=new eV(K6.constants.HASH_BYTE_LENGTH);for(var F=0;F<64;++F)G[F]=Z.charCodeAt(F);return G}function FF8(A,B){var Q=[GQ(),GQ(),GQ(),GQ()],D,Z=SG1(B,32);Z[0]&=248,Z[31]&=127,Z[31]|=64,uq0(Q,Z),gq0(A,Q);for(D=0;D<32;++D)B[D+32]=A[D];return 0}function IF8(A,B,Q,D){var Z,G,F=new Float64Array(64),I=[GQ(),GQ(),GQ(),GQ()],Y=SG1(D,32);Y[0]&=248,Y[31]&=127,Y[31]|=64;var W=Q+64;for(Z=0;Z<Q;++Z)A[64+Z]=B[Z];for(Z=0;Z<32;++Z)A[32+Z]=Y[32+Z];var J=SG1(A.subarray(32),Q+32);fq0(J),uq0(I,J),gq0(A,I);for(Z=32;Z<64;++Z)A[Z]=D[Z];var X=SG1(A,Q+64);fq0(X);for(Z=32;Z<64;++Z)F[Z]=0;for(Z=0;Z<32;++Z)F[Z]=J[Z];for(Z=0;Z<32;++Z)for(G=0;G<32;G++)F[Z+G]+=X[Z]*Y[G];return NOB(A.subarray(32),F),W}function YF8(A,B,Q,D){var Z,G,F=new eV(32),I=[GQ(),GQ(),GQ(),GQ()],Y=[GQ(),GQ(),GQ(),GQ()];if(G=-1,Q<64)return-1;if(WF8(Y,D))return-1;for(Z=0;Z<Q;++Z)A[Z]=B[Z];for(Z=0;Z<32;++Z)A[Z+32]=D[Z];var W=SG1(A,Q);if(fq0(W),ROB(I,Y,W),uq0(Y,B.subarray(32)),hq0(I,Y),gq0(F,I),Q-=64,LOB(B,0,F,0)){for(Z=0;Z<Q;++Z)A[Z]=0;return-1}for(Z=0;Z<Q;++Z)A[Z]=B[Z+64];return G=Q,G}function NOB(A,B){var Q,D,Z,G;for(D=63;D>=32;--D){Q=0;for(Z=D-32,G=D-12;Z<G;++Z)B[Z]+=Q-16*B[D]*yq0[Z-(D-32)],Q=B[Z]+128>>8,B[Z]-=Q*256;B[Z]+=Q,B[D]=0}Q=0;for(Z=0;Z<32;++Z)B[Z]+=Q-(B[31]>>4)*yq0[Z],Q=B[Z]>>8,B[Z]&=255;for(Z=0;Z<32;++Z)B[Z]-=Q*yq0[Z];for(D=0;D<32;++D)B[D+1]+=B[D]>>8,A[D]=B[D]&255}function fq0(A){var B=new Float64Array(64);for(var Q=0;Q<64;++Q)B[Q]=A[Q],A[Q]=0;NOB(A,B)}function hq0(A,B){var Q=GQ(),D=GQ(),Z=GQ(),G=GQ(),F=GQ(),I=GQ(),Y=GQ(),W=GQ(),J=GQ();A01(Q,A[1],A[0]),A01(J,B[1],B[0]),g3(Q,Q,J),e11(D,A[0],A[1]),e11(J,B[0],B[1]),g3(D,D,J),g3(Z,A[3],B[3]),g3(Z,Z,ZF8),g3(G,A[2],B[2]),e11(G,G,G),A01(F,D,Q),A01(I,G,Z),e11(Y,G,Z),e11(W,D,Q),g3(A[0],F,I),g3(A[1],W,Y),g3(A[2],Y,I),g3(A[3],F,W)}function wOB(A,B,Q){for(var D=0;D<4;++D)OOB(A[D],B[D],Q)}function gq0(A,B){var Q=GQ(),D=GQ(),Z=GQ();CF8(Z,B[2]),g3(Q,B[0],Z),g3(D,B[1],Z),lv1(A,D),A[31]^=MOB(Q)<<7}function lv1(A,B){var Q,D,Z,G=GQ(),F=GQ();for(Q=0;Q<16;++Q)F[Q]=B[Q];_q0(F),_q0(F),_q0(F);for(D=0;D<2;++D){G[0]=F[0]-65517;for(Q=1;Q<15;++Q)G[Q]=F[Q]-65535-(G[Q-1]>>16&1),G[Q-1]&=65535;G[15]=F[15]-32767-(G[14]>>16&1),Z=G[15]>>16&1,G[14]&=65535,OOB(F,G,1-Z)}for(Q=0;Q<16;Q++)A[2*Q]=F[Q]&255,A[2*Q+1]=F[Q]>>8}function WF8(A,B){var Q=GQ(),D=GQ(),Z=GQ(),G=GQ(),F=GQ(),I=GQ(),Y=GQ();if(yv(A[2],cv1),JF8(A[1],B),Bd(Z,A[1]),g3(G,Z,DF8),A01(Z,Z,A[2]),e11(G,A[2],G),Bd(F,G),Bd(I,F),g3(Y,I,F),g3(Q,Y,Z),g3(Q,Q,G),XF8(Q,Q),g3(Q,Q,Z),g3(Q,Q,G),g3(Q,Q,G),g3(A[0],Q,G),Bd(D,A[0]),g3(D,D,G),$OB(D,Z))g3(A[0],A[0],GF8);if(Bd(D,A[0]),g3(D,D,G),$OB(D,Z))return-1;if(MOB(A[0])===B[31]>>7)A01(A[0],bq0,A[0]);return g3(A[3],A[0],A[1]),0}function JF8(A,B){var Q;for(Q=0;Q<16;++Q)A[Q]=B[2*Q]+(B[2*Q+1]<<8);A[15]&=32767}function XF8(A,B){var Q=GQ(),D;for(D=0;D<16;++D)Q[D]=B[D];for(D=250;D>=0;--D)if(Bd(Q,Q),D!==1)g3(Q,Q,B);for(D=0;D<16;++D)A[D]=Q[D]}function $OB(A,B){var Q=new eV(32),D=new eV(32);return lv1(Q,A),lv1(D,B),LOB(Q,0,D,0)}function LOB(A,B,Q,D){return VF8(A,B,Q,D,32)}function VF8(A,B,Q,D,Z){var G,F=0;for(G=0;G<Z;++G)F|=A[B+G]^Q[D+G];return(1&F-1>>>8)-1}function MOB(A){var B=new eV(32);return lv1(B,A),B[0]&1}function ROB(A,B,Q){var D,Z;yv(A[0],bq0),yv(A[1],cv1),yv(A[2],cv1),yv(A[3],bq0);for(Z=255;Z>=0;--Z)D=Q[Z/8|0]>>(Z&7)&1,wOB(A,B,D),hq0(B,A),hq0(A,A),wOB(A,B,D)}function uq0(A,B){var Q=[GQ(),GQ(),GQ(),GQ()];yv(Q[0],EOB),yv(Q[1],UOB),yv(Q[2],cv1),g3(Q[3],EOB,UOB),ROB(A,Q,B)}function yv(A,B){var Q;for(Q=0;Q<16;Q++)A[Q]=B[Q]|0}function CF8(A,B){var Q=GQ(),D;for(D=0;D<16;++D)Q[D]=B[D];for(D=253;D>=0;--D)if(Bd(Q,Q),D!==2&&D!==4)g3(Q,Q,B);for(D=0;D<16;++D)A[D]=Q[D]}function _q0(A){var B,Q,D=1;for(B=0;B<16;++B)Q=A[B]+D+65535,D=Math.floor(Q/65536),A[B]=Q-D*65536;A[0]+=D-1+37*(D-1)}function OOB(A,B,Q){var D,Z=~(Q-1);for(var G=0;G<16;++G)D=Z&(A[G]^B[G]),A[G]^=D,B[G]^=D}function GQ(A){var B,Q=new Float64Array(16);if(A)for(B=0;B<A.length;++B)Q[B]=A[B];return Q}function e11(A,B,Q){for(var D=0;D<16;++D)A[D]=B[D]+Q[D]}function A01(A,B,Q){for(var D=0;D<16;++D)A[D]=B[D]-Q[D]}function Bd(A,B){g3(A,B,B)}function g3(A,B,Q){var D,Z,G=0,F=0,I=0,Y=0,W=0,J=0,X=0,V=0,C=0,K=0,H=0,z=0,$=0,L=0,N=0,O=0,R=0,T=0,j=0,f=0,k=0,c=0,h=0,n=0,a=0,x=0,e=0,W1=0,U1=0,y1=0,W0=0,F0=Q[0],g1=Q[1],K1=Q[2],G1=Q[3],L1=Q[4],M1=Q[5],a1=Q[6],i1=Q[7],E0=Q[8],B1=Q[9],A1=Q[10],I1=Q[11],q1=Q[12],P1=Q[13],Q1=Q[14],f1=Q[15];D=B[0],G+=D*F0,F+=D*g1,I+=D*K1,Y+=D*G1,W+=D*L1,J+=D*M1,X+=D*a1,V+=D*i1,C+=D*E0,K+=D*B1,H+=D*A1,z+=D*I1,$+=D*q1,L+=D*P1,N+=D*Q1,O+=D*f1,D=B[1],F+=D*F0,I+=D*g1,Y+=D*K1,W+=D*G1,J+=D*L1,X+=D*M1,V+=D*a1,C+=D*i1,K+=D*E0,H+=D*B1,z+=D*A1,$+=D*I1,L+=D*q1,N+=D*P1,O+=D*Q1,R+=D*f1,D=B[2],I+=D*F0,Y+=D*g1,W+=D*K1,J+=D*G1,X+=D*L1,V+=D*M1,C+=D*a1,K+=D*i1,H+=D*E0,z+=D*B1,$+=D*A1,L+=D*I1,N+=D*q1,O+=D*P1,R+=D*Q1,T+=D*f1,D=B[3],Y+=D*F0,W+=D*g1,J+=D*K1,X+=D*G1,V+=D*L1,C+=D*M1,K+=D*a1,H+=D*i1,z+=D*E0,$+=D*B1,L+=D*A1,N+=D*I1,O+=D*q1,R+=D*P1,T+=D*Q1,j+=D*f1,D=B[4],W+=D*F0,J+=D*g1,X+=D*K1,V+=D*G1,C+=D*L1,K+=D*M1,H+=D*a1,z+=D*i1,$+=D*E0,L+=D*B1,N+=D*A1,O+=D*I1,R+=D*q1,T+=D*P1,j+=D*Q1,f+=D*f1,D=B[5],J+=D*F0,X+=D*g1,V+=D*K1,C+=D*G1,K+=D*L1,H+=D*M1,z+=D*a1,$+=D*i1,L+=D*E0,N+=D*B1,O+=D*A1,R+=D*I1,T+=D*q1,j+=D*P1,f+=D*Q1,k+=D*f1,D=B[6],X+=D*F0,V+=D*g1,C+=D*K1,K+=D*G1,H+=D*L1,z+=D*M1,$+=D*a1,L+=D*i1,N+=D*E0,O+=D*B1,R+=D*A1,T+=D*I1,j+=D*q1,f+=D*P1,k+=D*Q1,c+=D*f1,D=B[7],V+=D*F0,C+=D*g1,K+=D*K1,H+=D*G1,z+=D*L1,$+=D*M1,L+=D*a1,N+=D*i1,O+=D*E0,R+=D*B1,T+=D*A1,j+=D*I1,f+=D*q1,k+=D*P1,c+=D*Q1,h+=D*f1,D=B[8],C+=D*F0,K+=D*g1,H+=D*K1,z+=D*G1,$+=D*L1,L+=D*M1,N+=D*a1,O+=D*i1,R+=D*E0,T+=D*B1,j+=D*A1,f+=D*I1,k+=D*q1,c+=D*P1,h+=D*Q1,n+=D*f1,D=B[9],K+=D*F0,H+=D*g1,z+=D*K1,$+=D*G1,L+=D*L1,N+=D*M1,O+=D*a1,R+=D*i1,T+=D*E0,j+=D*B1,f+=D*A1,k+=D*I1,c+=D*q1,h+=D*P1,n+=D*Q1,a+=D*f1,D=B[10],H+=D*F0,z+=D*g1,$+=D*K1,L+=D*G1,N+=D*L1,O+=D*M1,R+=D*a1,T+=D*i1,j+=D*E0,f+=D*B1,k+=D*A1,c+=D*I1,h+=D*q1,n+=D*P1,a+=D*Q1,x+=D*f1,D=B[11],z+=D*F0,$+=D*g1,L+=D*K1,N+=D*G1,O+=D*L1,R+=D*M1,T+=D*a1,j+=D*i1,f+=D*E0,k+=D*B1,c+=D*A1,h+=D*I1,n+=D*q1,a+=D*P1,x+=D*Q1,e+=D*f1,D=B[12],$+=D*F0,L+=D*g1,N+=D*K1,O+=D*G1,R+=D*L1,T+=D*M1,j+=D*a1,f+=D*i1,k+=D*E0,c+=D*B1,h+=D*A1,n+=D*I1,a+=D*q1,x+=D*P1,e+=D*Q1,W1+=D*f1,D=B[13],L+=D*F0,N+=D*g1,O+=D*K1,R+=D*G1,T+=D*L1,j+=D*M1,f+=D*a1,k+=D*i1,c+=D*E0,h+=D*B1,n+=D*A1,a+=D*I1,x+=D*q1,e+=D*P1,W1+=D*Q1,U1+=D*f1,D=B[14],N+=D*F0,O+=D*g1,R+=D*K1,T+=D*G1,j+=D*L1,f+=D*M1,k+=D*a1,c+=D*i1,h+=D*E0,n+=D*B1,a+=D*A1,x+=D*I1,e+=D*q1,W1+=D*P1,U1+=D*Q1,y1+=D*f1,D=B[15],O+=D*F0,R+=D*g1,T+=D*K1,j+=D*G1,f+=D*L1,k+=D*M1,c+=D*a1,h+=D*i1,n+=D*E0,a+=D*B1,x+=D*A1,e+=D*I1,W1+=D*q1,U1+=D*P1,y1+=D*Q1,W0+=D*f1,G+=38*R,F+=38*T,I+=38*j,Y+=38*f,W+=38*k,J+=38*c,X+=38*h,V+=38*n,C+=38*a,K+=38*x,H+=38*e,z+=38*W1,$+=38*U1,L+=38*y1,N+=38*W0,Z=1,D=G+Z+65535,Z=Math.floor(D/65536),G=D-Z*65536,D=F+Z+65535,Z=Math.floor(D/65536),F=D-Z*65536,D=I+Z+65535,Z=Math.floor(D/65536),I=D-Z*65536,D=Y+Z+65535,Z=Math.floor(D/65536),Y=D-Z*65536,D=W+Z+65535,Z=Math.floor(D/65536),W=D-Z*65536,D=J+Z+65535,Z=Math.floor(D/65536),J=D-Z*65536,D=X+Z+65535,Z=Math.floor(D/65536),X=D-Z*65536,D=V+Z+65535,Z=Math.floor(D/65536),V=D-Z*65536,D=C+Z+65535,Z=Math.floor(D/65536),C=D-Z*65536,D=K+Z+65535,Z=Math.floor(D/65536),K=D-Z*65536,D=H+Z+65535,Z=Math.floor(D/65536),H=D-Z*65536,D=z+Z+65535,Z=Math.floor(D/65536),z=D-Z*65536,D=$+Z+65535,Z=Math.floor(D/65536),$=D-Z*65536,D=L+Z+65535,Z=Math.floor(D/65536),L=D-Z*65536,D=N+Z+65535,Z=Math.floor(D/65536),N=D-Z*65536,D=O+Z+65535,Z=Math.floor(D/65536),O=D-Z*65536,G+=Z-1+37*(Z-1),Z=1,D=G+Z+65535,Z=Math.floor(D/65536),G=D-Z*65536,D=F+Z+65535,Z=Math.floor(D/65536),F=D-Z*65536,D=I+Z+65535,Z=Math.floor(D/65536),I=D-Z*65536,D=Y+Z+65535,Z=Math.floor(D/65536),Y=D-Z*65536,D=W+Z+65535,Z=Math.floor(D/65536),W=D-Z*65536,D=J+Z+65535,Z=Math.floor(D/65536),J=D-Z*65536,D=X+Z+65535,Z=Math.floor(D/65536),X=D-Z*65536,D=V+Z+65535,Z=Math.floor(D/65536),V=D-Z*65536,D=C+Z+65535,Z=Math.floor(D/65536),C=D-Z*65536,D=K+Z+65535,Z=Math.floor(D/65536),K=D-Z*65536,D=H+Z+65535,Z=Math.floor(D/65536),H=D-Z*65536,D=z+Z+65535,Z=Math.floor(D/65536),z=D-Z*65536,D=$+Z+65535,Z=Math.floor(D/65536),$=D-Z*65536,D=L+Z+65535,Z=Math.floor(D/65536),L=D-Z*65536,D=N+Z+65535,Z=Math.floor(D/65536),N=D-Z*65536,D=O+Z+65535,Z=Math.floor(D/65536),O=D-Z*65536,G+=Z-1+37*(Z-1),A[0]=G,A[1]=F,A[2]=I,A[3]=Y,A[4]=W,A[5]=J,A[6]=X,A[7]=V,A[8]=C,A[9]=K,A[10]=H,A[11]=z,A[12]=$,A[13]=L,A[14]=N,A[15]=O}});
var Pq0=E((vZ3,YOB)=>{var xA=M4();j$();l11();Rv1();am();Nq0();eE();a11();N8();var dv1=function(A,B,Q,D){var Z=xA.util.createBuffer(),G=A.length>>1,F=G+(A.length&1),I=A.substr(0,F),Y=A.substr(G,F),W=xA.util.createBuffer(),J=xA.hmac.create();Q=B+Q;var X=Math.ceil(D/16),V=Math.ceil(D/20);J.start("MD5",I);var C=xA.util.createBuffer();W.putBytes(Q);for(var K=0;K<X;++K)J.start(null,null),J.update(W.getBytes()),W.putBuffer(J.digest()),J.start(null,null),J.update(W.bytes()+Q),C.putBuffer(J.digest());J.start("SHA1",Y);var H=xA.util.createBuffer();W.clear(),W.putBytes(Q);for(var K=0;K<V;++K)J.start(null,null),J.update(W.getBytes()),W.putBuffer(J.digest()),J.start(null,null),J.update(W.bytes()+Q),H.putBuffer(J.digest());return Z.putBytes(xA.util.xorBytes(C.getBytes(),H.getBytes(),D)),Z},RG8=function(A,B,Q){var D=xA.hmac.create();D.start("SHA1",A);var Z=xA.util.createBuffer();return Z.putInt32(B[0]),Z.putInt32(B[1]),Z.putByte(Q.type),Z.putByte(Q.version.major),Z.putByte(Q.version.minor),Z.putInt16(Q.length),Z.putBytes(Q.fragment.bytes()),D.update(Z.getBytes()),D.digest().getBytes()},OG8=function(A,B,Q){var D=!1;try{var Z=A.deflate(B.fragment.getBytes());B.fragment=xA.util.createBuffer(Z),B.length=Z.length,D=!0}catch(G){}return D},TG8=function(A,B,Q){var D=!1;try{var Z=A.inflate(B.fragment.getBytes());B.fragment=xA.util.createBuffer(Z),B.length=Z.length,D=!0}catch(G){}return D},mK=function(A,B){var Q=0;switch(B){case 1:Q=A.getByte();break;case 2:Q=A.getInt16();break;case 3:Q=A.getInt24();break;case 4:Q=A.getInt32();break}return xA.util.createBuffer(A.getBytes(Q))},DU=function(A,B,Q){A.putInt(Q.length(),B<<3),A.putBuffer(Q)},R1={};R1.Versions={TLS_1_0:{major:3,minor:1},TLS_1_1:{major:3,minor:2},TLS_1_2:{major:3,minor:3}};R1.SupportedVersions=[R1.Versions.TLS_1_1,R1.Versions.TLS_1_0];R1.Version=R1.SupportedVersions[0];R1.MaxFragment=15360;R1.ConnectionEnd={server:0,client:1};R1.PRFAlgorithm={tls_prf_sha256:0};R1.BulkCipherAlgorithm={none:null,rc4:0,des3:1,aes:2};R1.CipherType={stream:0,block:1,aead:2};R1.MACAlgorithm={none:null,hmac_md5:0,hmac_sha1:1,hmac_sha256:2,hmac_sha384:3,hmac_sha512:4};R1.CompressionMethod={none:0,deflate:1};R1.ContentType={change_cipher_spec:20,alert:21,handshake:22,application_data:23,heartbeat:24};R1.HandshakeType={hello_request:0,client_hello:1,server_hello:2,certificate:11,server_key_exchange:12,certificate_request:13,server_hello_done:14,certificate_verify:15,client_key_exchange:16,finished:20};R1.Alert={};R1.Alert.Level={warning:1,fatal:2};R1.Alert.Description={close_notify:0,unexpected_message:10,bad_record_mac:20,decryption_failed:21,record_overflow:22,decompression_failure:30,handshake_failure:40,bad_certificate:42,unsupported_certificate:43,certificate_revoked:44,certificate_expired:45,certificate_unknown:46,illegal_parameter:47,unknown_ca:48,access_denied:49,decode_error:50,decrypt_error:51,export_restriction:60,protocol_version:70,insufficient_security:71,internal_error:80,user_canceled:90,no_renegotiation:100};R1.HeartbeatMessageType={heartbeat_request:1,heartbeat_response:2};R1.CipherSuites={};R1.getCipherSuite=function(A){var B=null;for(var Q in R1.CipherSuites){var D=R1.CipherSuites[Q];if(D.id[0]===A.charCodeAt(0)&&D.id[1]===A.charCodeAt(1)){B=D;break}}return B};R1.handleUnexpected=function(A,B){var Q=!A.open&&A.entity===R1.ConnectionEnd.client;if(!Q)A.error(A,{message:"Unexpected message. Received TLS record out of order.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.unexpected_message}})};R1.handleHelloRequest=function(A,B,Q){if(!A.handshaking&&A.handshakes>0)R1.queue(A,R1.createAlert(A,{level:R1.Alert.Level.warning,description:R1.Alert.Description.no_renegotiation})),R1.flush(A);A.process()};R1.parseHelloMessage=function(A,B,Q){var D=null,Z=A.entity===R1.ConnectionEnd.client;if(Q<38)A.error(A,{message:Z?"Invalid ServerHello message. Message too short.":"Invalid ClientHello message. Message too short.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.illegal_parameter}});else{var G=B.fragment,F=G.length();if(D={version:{major:G.getByte(),minor:G.getByte()},random:xA.util.createBuffer(G.getBytes(32)),session_id:mK(G,1),extensions:[]},Z)D.cipher_suite=G.getBytes(2),D.compression_method=G.getByte();else D.cipher_suites=mK(G,2),D.compression_methods=mK(G,1);if(F=Q-(F-G.length()),F>0){var I=mK(G,2);while(I.length()>0)D.extensions.push({type:[I.getByte(),I.getByte()],data:mK(I,2)});if(!Z)for(var Y=0;Y<D.extensions.length;++Y){var W=D.extensions[Y];if(W.type[0]===0&&W.type[1]===0){var J=mK(W.data,2);while(J.length()>0){var X=J.getByte();if(X!==0)break;A.session.extensions.server_name.serverNameList.push(mK(J,2).getBytes())}}}}if(A.session.version){if(D.version.major!==A.session.version.major||D.version.minor!==A.session.version.minor)return A.error(A,{message:"TLS version change is disallowed during renegotiation.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.protocol_version}})}if(Z)A.session.cipherSuite=R1.getCipherSuite(D.cipher_suite);else{var V=xA.util.createBuffer(D.cipher_suites.bytes());while(V.length()>0)if(A.session.cipherSuite=R1.getCipherSuite(V.getBytes(2)),A.session.cipherSuite!==null)break}if(A.session.cipherSuite===null)return A.error(A,{message:"No cipher suites in common.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.handshake_failure},cipherSuite:xA.util.bytesToHex(D.cipher_suite)});if(Z)A.session.compressionMethod=D.compression_method;else A.session.compressionMethod=R1.CompressionMethod.none}return D};R1.createSecurityParameters=function(A,B){var Q=A.entity===R1.ConnectionEnd.client,D=B.random.bytes(),Z=Q?A.session.sp.client_random:D,G=Q?D:R1.createRandom().getBytes();A.session.sp={entity:A.entity,prf_algorithm:R1.PRFAlgorithm.tls_prf_sha256,bulk_cipher_algorithm:null,cipher_type:null,enc_key_length:null,block_length:null,fixed_iv_length:null,record_iv_length:null,mac_algorithm:null,mac_length:null,mac_key_length:null,compression_algorithm:A.session.compressionMethod,pre_master_secret:null,master_secret:null,client_random:Z,server_random:G}};R1.handleServerHello=function(A,B,Q){var D=R1.parseHelloMessage(A,B,Q);if(A.fail)return;if(D.version.minor<=A.version.minor)A.version.minor=D.version.minor;else return A.error(A,{message:"Incompatible TLS version.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.protocol_version}});A.session.version=A.version;var Z=D.session_id.bytes();if(Z.length>0&&Z===A.session.id)A.expect=DOB,A.session.resuming=!0,A.session.sp.server_random=D.random.bytes();else A.expect=SG8,A.session.resuming=!1,R1.createSecurityParameters(A,D);A.session.id=Z,A.process()};R1.handleClientHello=function(A,B,Q){var D=R1.parseHelloMessage(A,B,Q);if(A.fail)return;var Z=D.session_id.bytes(),G=null;if(A.sessionCache){if(G=A.sessionCache.getSession(Z),G===null)Z="";else if(G.version.major!==D.version.major||G.version.minor>D.version.minor)G=null,Z=""}if(Z.length===0)Z=xA.random.getBytes(32);if(A.session.id=Z,A.session.clientHelloVersion=D.version,A.session.sp={},G)A.version=A.session.version=G.version,A.session.sp=G.sp;else{var F;for(var I=1;I<R1.SupportedVersions.length;++I)if(F=R1.SupportedVersions[I],F.minor<=D.version.minor)break;A.version={major:F.major,minor:F.minor},A.session.version=A.version}if(G!==null)A.expect=Oq0,A.session.resuming=!0,A.session.sp.client_random=D.random.bytes();else A.expect=A.verifyClient!==!1?bG8:Rq0,A.session.resuming=!1,R1.createSecurityParameters(A,D);if(A.open=!0,R1.queue(A,R1.createRecord(A,{type:R1.ContentType.handshake,data:R1.createServerHello(A)})),A.session.resuming)R1.queue(A,R1.createRecord(A,{type:R1.ContentType.change_cipher_spec,data:R1.createChangeCipherSpec()})),A.state.pending=R1.createConnectionState(A),A.state.current.write=A.state.pending.write,R1.queue(A,R1.createRecord(A,{type:R1.ContentType.handshake,data:R1.createFinished(A)}));else if(R1.queue(A,R1.createRecord(A,{type:R1.ContentType.handshake,data:R1.createCertificate(A)})),!A.fail){if(R1.queue(A,R1.createRecord(A,{type:R1.ContentType.handshake,data:R1.createServerKeyExchange(A)})),A.verifyClient!==!1)R1.queue(A,R1.createRecord(A,{type:R1.ContentType.handshake,data:R1.createCertificateRequest(A)}));R1.queue(A,R1.createRecord(A,{type:R1.ContentType.handshake,data:R1.createServerHelloDone(A)}))}R1.flush(A),A.process()};R1.handleCertificate=function(A,B,Q){if(Q<3)return A.error(A,{message:"Invalid Certificate message. Message too short.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.illegal_parameter}});var D=B.fragment,Z={certificate_list:mK(D,3)},G,F,I=[];try{while(Z.certificate_list.length()>0)G=mK(Z.certificate_list,3),F=xA.asn1.fromDer(G),G=xA.pki.certificateFromAsn1(F,!0),I.push(G)}catch(W){return A.error(A,{message:"Could not parse certificate list.",cause:W,send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.bad_certificate}})}var Y=A.entity===R1.ConnectionEnd.client;if((Y||A.verifyClient===!0)&&I.length===0)A.error(A,{message:Y?"No server certificate provided.":"No client certificate provided.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.illegal_parameter}});else if(I.length===0)A.expect=Y?BOB:Rq0;else{if(Y)A.session.serverCertificate=I[0];else A.session.clientCertificate=I[0];if(R1.verifyCertificateChain(A,I))A.expect=Y?BOB:Rq0}A.process()};R1.handleServerKeyExchange=function(A,B,Q){if(Q>0)return A.error(A,{message:"Invalid key parameters. Only RSA is supported.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.unsupported_certificate}});A.expect=jG8,A.process()};R1.handleClientKeyExchange=function(A,B,Q){if(Q<48)return A.error(A,{message:"Invalid key parameters. Only RSA is supported.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.unsupported_certificate}});var D=B.fragment,Z={enc_pre_master_secret:mK(D,2).getBytes()},G=null;if(A.getPrivateKey)try{G=A.getPrivateKey(A,A.session.serverCertificate),G=xA.pki.privateKeyFromPem(G)}catch(Y){A.error(A,{message:"Could not get private key.",cause:Y,send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.internal_error}})}if(G===null)return A.error(A,{message:"No private key set.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.internal_error}});try{var F=A.session.sp;F.pre_master_secret=G.decrypt(Z.enc_pre_master_secret);var I=A.session.clientHelloVersion;if(I.major!==F.pre_master_secret.charCodeAt(0)||I.minor!==F.pre_master_secret.charCodeAt(1))throw new Error("TLS version rollback attack detected.")}catch(Y){F.pre_master_secret=xA.random.getBytes(48)}if(A.expect=Oq0,A.session.clientCertificate!==null)A.expect=fG8;A.process()};R1.handleCertificateRequest=function(A,B,Q){if(Q<3)return A.error(A,{message:"Invalid CertificateRequest. Message too short.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.illegal_parameter}});var D=B.fragment,Z={certificate_types:mK(D,1),certificate_authorities:mK(D,2)};A.session.certificateRequest=Z,A.expect=kG8,A.process()};R1.handleCertificateVerify=function(A,B,Q){if(Q<2)return A.error(A,{message:"Invalid CertificateVerify. Message too short.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.illegal_parameter}});var D=B.fragment;D.read-=4;var Z=D.bytes();D.read+=4;var G={signature:mK(D,2).getBytes()},F=xA.util.createBuffer();F.putBuffer(A.session.md5.digest()),F.putBuffer(A.session.sha1.digest()),F=F.getBytes();try{var I=A.session.clientCertificate;if(!I.publicKey.verify(F,G.signature,"NONE"))throw new Error("CertificateVerify signature does not match.");A.session.md5.update(Z),A.session.sha1.update(Z)}catch(Y){return A.error(A,{message:"Bad signature in CertificateVerify.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.handshake_failure}})}A.expect=Oq0,A.process()};R1.handleServerHelloDone=function(A,B,Q){if(Q>0)return A.error(A,{message:"Invalid ServerHelloDone message. Invalid length.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.record_overflow}});if(A.serverCertificate===null){var D={message:"No server certificate provided. Not enough security.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.insufficient_security}},Z=0,G=A.verify(A,D.alert.description,Z,[]);if(G!==!0){if(G||G===0){if(typeof G==="object"&&!xA.util.isArray(G)){if(G.message)D.message=G.message;if(G.alert)D.alert.description=G.alert}else if(typeof G==="number")D.alert.description=G}return A.error(A,D)}}if(A.session.certificateRequest!==null)B=R1.createRecord(A,{type:R1.ContentType.handshake,data:R1.createCertificate(A)}),R1.queue(A,B);B=R1.createRecord(A,{type:R1.ContentType.handshake,data:R1.createClientKeyExchange(A)}),R1.queue(A,B),A.expect=xG8;var F=function(I,Y){if(I.session.certificateRequest!==null&&I.session.clientCertificate!==null)R1.queue(I,R1.createRecord(I,{type:R1.ContentType.handshake,data:R1.createCertificateVerify(I,Y)}));R1.queue(I,R1.createRecord(I,{type:R1.ContentType.change_cipher_spec,data:R1.createChangeCipherSpec()})),I.state.pending=R1.createConnectionState(I),I.state.current.write=I.state.pending.write,R1.queue(I,R1.createRecord(I,{type:R1.ContentType.handshake,data:R1.createFinished(I)})),I.expect=DOB,R1.flush(I),I.process()};if(A.session.certificateRequest===null||A.session.clientCertificate===null)return F(A,null);R1.getClientSignature(A,F)};R1.handleChangeCipherSpec=function(A,B){if(B.fragment.getByte()!==1)return A.error(A,{message:"Invalid ChangeCipherSpec message received.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.illegal_parameter}});var Q=A.entity===R1.ConnectionEnd.client;if(A.session.resuming&&Q||!A.session.resuming&&!Q)A.state.pending=R1.createConnectionState(A);if(A.state.current.read=A.state.pending.read,!A.session.resuming&&Q||A.session.resuming&&!Q)A.state.pending=null;A.expect=Q?yG8:hG8,A.process()};R1.handleFinished=function(A,B,Q){var D=B.fragment;D.read-=4;var Z=D.bytes();D.read+=4;var G=B.fragment.getBytes();D=xA.util.createBuffer(),D.putBuffer(A.session.md5.digest()),D.putBuffer(A.session.sha1.digest());var F=A.entity===R1.ConnectionEnd.client,I=F?"server finished":"client finished",Y=A.session.sp,W=12,J=dv1;if(D=J(Y.master_secret,I,D.getBytes(),W),D.getBytes()!==G)return A.error(A,{message:"Invalid verify_data in Finished message.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.decrypt_error}});if(A.session.md5.update(Z),A.session.sha1.update(Z),A.session.resuming&&F||!A.session.resuming&&!F)R1.queue(A,R1.createRecord(A,{type:R1.ContentType.change_cipher_spec,data:R1.createChangeCipherSpec()})),A.state.current.write=A.state.pending.write,A.state.pending=null,R1.queue(A,R1.createRecord(A,{type:R1.ContentType.handshake,data:R1.createFinished(A)}));A.expect=F?_G8:gG8,A.handshaking=!1,++A.handshakes,A.peerCertificate=F?A.session.serverCertificate:A.session.clientCertificate,R1.flush(A),A.isConnected=!0,A.connected(A),A.process()};R1.handleAlert=function(A,B){var Q=B.fragment,D={level:Q.getByte(),description:Q.getByte()},Z;switch(D.description){case R1.Alert.Description.close_notify:Z="Connection closed.";break;case R1.Alert.Description.unexpected_message:Z="Unexpected message.";break;case R1.Alert.Description.bad_record_mac:Z="Bad record MAC.";break;case R1.Alert.Description.decryption_failed:Z="Decryption failed.";break;case R1.Alert.Description.record_overflow:Z="Record overflow.";break;case R1.Alert.Description.decompression_failure:Z="Decompression failed.";break;case R1.Alert.Description.handshake_failure:Z="Handshake failure.";break;case R1.Alert.Description.bad_certificate:Z="Bad certificate.";break;case R1.Alert.Description.unsupported_certificate:Z="Unsupported certificate.";break;case R1.Alert.Description.certificate_revoked:Z="Certificate revoked.";break;case R1.Alert.Description.certificate_expired:Z="Certificate expired.";break;case R1.Alert.Description.certificate_unknown:Z="Certificate unknown.";break;case R1.Alert.Description.illegal_parameter:Z="Illegal parameter.";break;case R1.Alert.Description.unknown_ca:Z="Unknown certificate authority.";break;case R1.Alert.Description.access_denied:Z="Access denied.";break;case R1.Alert.Description.decode_error:Z="Decode error.";break;case R1.Alert.Description.decrypt_error:Z="Decrypt error.";break;case R1.Alert.Description.export_restriction:Z="Export restriction.";break;case R1.Alert.Description.protocol_version:Z="Unsupported protocol version.";break;case R1.Alert.Description.insufficient_security:Z="Insufficient security.";break;case R1.Alert.Description.internal_error:Z="Internal error.";break;case R1.Alert.Description.user_canceled:Z="User canceled.";break;case R1.Alert.Description.no_renegotiation:Z="Renegotiation not supported.";break;default:Z="Unknown error.";break}if(D.description===R1.Alert.Description.close_notify)return A.close();A.error(A,{message:Z,send:!1,origin:A.entity===R1.ConnectionEnd.client?"server":"client",alert:D}),A.process()};R1.handleHandshake=function(A,B){var Q=B.fragment,D=Q.getByte(),Z=Q.getInt24();if(Z>Q.length())return A.fragmented=B,B.fragment=xA.util.createBuffer(),Q.read-=4,A.process();A.fragmented=null,Q.read-=4;var G=Q.bytes(Z+4);if(Q.read+=4,D in mv1[A.entity][A.expect]){if(A.entity===R1.ConnectionEnd.server&&!A.open&&!A.fail)A.handshaking=!0,A.session={version:null,extensions:{server_name:{serverNameList:[]}},cipherSuite:null,compressionMethod:null,serverCertificate:null,clientCertificate:null,md5:xA.md.md5.create(),sha1:xA.md.sha1.create()};if(D!==R1.HandshakeType.hello_request&&D!==R1.HandshakeType.certificate_verify&&D!==R1.HandshakeType.finished)A.session.md5.update(G),A.session.sha1.update(G);mv1[A.entity][A.expect][D](A,B,Z)}else R1.handleUnexpected(A,B)};R1.handleApplicationData=function(A,B){A.data.putBuffer(B.fragment),A.dataReady(A),A.process()};R1.handleHeartbeat=function(A,B){var Q=B.fragment,D=Q.getByte(),Z=Q.getInt16(),G=Q.getBytes(Z);if(D===R1.HeartbeatMessageType.heartbeat_request){if(A.handshaking||Z>G.length)return A.process();R1.queue(A,R1.createRecord(A,{type:R1.ContentType.heartbeat,data:R1.createHeartbeat(R1.HeartbeatMessageType.heartbeat_response,G)})),R1.flush(A)}else if(D===R1.HeartbeatMessageType.heartbeat_response){if(G!==A.expectedHeartbeatPayload)return A.process();if(A.heartbeatReceived)A.heartbeatReceived(A,xA.util.createBuffer(G))}A.process()};var PG8=0,SG8=1,BOB=2,jG8=3,kG8=4,DOB=5,yG8=6,_G8=7,xG8=8,vG8=0,bG8=1,Rq0=2,fG8=3,Oq0=4,hG8=5,gG8=6,N1=R1.handleUnexpected,ZOB=R1.handleChangeCipherSpec,tY=R1.handleAlert,NX=R1.handleHandshake,GOB=R1.handleApplicationData,eY=R1.handleHeartbeat,Tq0=[];Tq0[R1.ConnectionEnd.client]=[[N1,tY,NX,N1,eY],[N1,tY,NX,N1,eY],[N1,tY,NX,N1,eY],[N1,tY,NX,N1,eY],[N1,tY,NX,N1,eY],[ZOB,tY,N1,N1,eY],[N1,tY,NX,N1,eY],[N1,tY,NX,GOB,eY],[N1,tY,NX,N1,eY]];Tq0[R1.ConnectionEnd.server]=[[N1,tY,NX,N1,eY],[N1,tY,NX,N1,eY],[N1,tY,NX,N1,eY],[N1,tY,NX,N1,eY],[ZOB,tY,N1,N1,eY],[N1,tY,NX,N1,eY],[N1,tY,NX,GOB,eY],[N1,tY,NX,N1,eY]];var{handleHelloRequest:jv,handleServerHello:uG8,handleCertificate:FOB,handleServerKeyExchange:QOB,handleCertificateRequest:Lq0,handleServerHelloDone:uv1,handleFinished:IOB}=R1,mv1=[];mv1[R1.ConnectionEnd.client]=[[N1,N1,uG8,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1],[jv,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,FOB,QOB,Lq0,uv1,N1,N1,N1,N1,N1,N1],[jv,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,QOB,Lq0,uv1,N1,N1,N1,N1,N1,N1],[jv,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,Lq0,uv1,N1,N1,N1,N1,N1,N1],[jv,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,uv1,N1,N1,N1,N1,N1,N1],[jv,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1],[jv,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,IOB],[jv,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1],[jv,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1]];var{handleClientHello:mG8,handleClientKeyExchange:dG8,handleCertificateVerify:cG8}=R1;mv1[R1.ConnectionEnd.server]=[[N1,mG8,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1],[N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,FOB,N1,N1,N1,N1,N1,N1,N1,N1,N1],[N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,dG8,N1,N1,N1,N1],[N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,cG8,N1,N1,N1,N1,N1],[N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1],[N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,IOB],[N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1],[N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1,N1]];R1.generateKeys=function(A,B){var Q=dv1,D=B.client_random+B.server_random;if(!A.session.resuming)B.master_secret=Q(B.pre_master_secret,"master secret",D,48).bytes(),B.pre_master_secret=null;D=B.server_random+B.client_random;var Z=2*B.mac_key_length+2*B.enc_key_length,G=A.version.major===R1.Versions.TLS_1_0.major&&A.version.minor===R1.Versions.TLS_1_0.minor;if(G)Z+=2*B.fixed_iv_length;var F=Q(B.master_secret,"key expansion",D,Z),I={client_write_MAC_key:F.getBytes(B.mac_key_length),server_write_MAC_key:F.getBytes(B.mac_key_length),client_write_key:F.getBytes(B.enc_key_length),server_write_key:F.getBytes(B.enc_key_length)};if(G)I.client_write_IV=F.getBytes(B.fixed_iv_length),I.server_write_IV=F.getBytes(B.fixed_iv_length);return I};R1.createConnectionState=function(A){var B=A.entity===R1.ConnectionEnd.client,Q=function(){var G={sequenceNumber:[0,0],macKey:null,macLength:0,macFunction:null,cipherState:null,cipherFunction:function(F){return!0},compressionState:null,compressFunction:function(F){return!0},updateSequenceNumber:function(){if(G.sequenceNumber[1]===4294967295)G.sequenceNumber[1]=0,++G.sequenceNumber[0];else++G.sequenceNumber[1]}};return G},D={read:Q(),write:Q()};if(D.read.update=function(G,F){if(!D.read.cipherFunction(F,D.read))G.error(G,{message:"Could not decrypt record or bad MAC.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.bad_record_mac}});else if(!D.read.compressFunction(G,F,D.read))G.error(G,{message:"Could not decompress record.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.decompression_failure}});return!G.fail},D.write.update=function(G,F){if(!D.write.compressFunction(G,F,D.write))G.error(G,{message:"Could not compress record.",send:!1,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.internal_error}});else if(!D.write.cipherFunction(F,D.write))G.error(G,{message:"Could not encrypt record.",send:!1,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.internal_error}});return!G.fail},A.session){var Z=A.session.sp;switch(A.session.cipherSuite.initSecurityParameters(Z),Z.keys=R1.generateKeys(A,Z),D.read.macKey=B?Z.keys.server_write_MAC_key:Z.keys.client_write_MAC_key,D.write.macKey=B?Z.keys.client_write_MAC_key:Z.keys.server_write_MAC_key,A.session.cipherSuite.initConnectionState(D,A,Z),Z.compression_algorithm){case R1.CompressionMethod.none:break;case R1.CompressionMethod.deflate:D.read.compressFunction=TG8,D.write.compressFunction=OG8;break;default:throw new Error("Unsupported compression algorithm.")}}return D};R1.createRandom=function(){var A=new Date,B=+A+A.getTimezoneOffset()*60000,Q=xA.util.createBuffer();return Q.putInt32(B),Q.putBytes(xA.random.getBytes(28)),Q};R1.createRecord=function(A,B){if(!B.data)return null;var Q={type:B.type,version:{major:A.version.major,minor:A.version.minor},length:B.data.length(),fragment:B.data};return Q};R1.createAlert=function(A,B){var Q=xA.util.createBuffer();return Q.putByte(B.level),Q.putByte(B.description),R1.createRecord(A,{type:R1.ContentType.alert,data:Q})};R1.createClientHello=function(A){A.session.clientHelloVersion={major:A.version.major,minor:A.version.minor};var B=xA.util.createBuffer();for(var Q=0;Q<A.cipherSuites.length;++Q){var D=A.cipherSuites[Q];B.putByte(D.id[0]),B.putByte(D.id[1])}var Z=B.length(),G=xA.util.createBuffer();G.putByte(R1.CompressionMethod.none);var F=G.length(),I=xA.util.createBuffer();if(A.virtualHost){var Y=xA.util.createBuffer();Y.putByte(0),Y.putByte(0);var W=xA.util.createBuffer();W.putByte(0),DU(W,2,xA.util.createBuffer(A.virtualHost));var J=xA.util.createBuffer();DU(J,2,W),DU(Y,2,J),I.putBuffer(Y)}var X=I.length();if(X>0)X+=2;var V=A.session.id,C=V.length+1+2+4+28+2+Z+1+F+X,K=xA.util.createBuffer();if(K.putByte(R1.HandshakeType.client_hello),K.putInt24(C),K.putByte(A.version.major),K.putByte(A.version.minor),K.putBytes(A.session.sp.client_random),DU(K,1,xA.util.createBuffer(V)),DU(K,2,B),DU(K,1,G),X>0)DU(K,2,I);return K};R1.createServerHello=function(A){var B=A.session.id,Q=B.length+1+2+4+28+2+1,D=xA.util.createBuffer();return D.putByte(R1.HandshakeType.server_hello),D.putInt24(Q),D.putByte(A.version.major),D.putByte(A.version.minor),D.putBytes(A.session.sp.server_random),DU(D,1,xA.util.createBuffer(B)),D.putByte(A.session.cipherSuite.id[0]),D.putByte(A.session.cipherSuite.id[1]),D.putByte(A.session.compressionMethod),D};R1.createCertificate=function(A){var B=A.entity===R1.ConnectionEnd.client,Q=null;if(A.getCertificate){var D;if(B)D=A.session.certificateRequest;else D=A.session.extensions.server_name.serverNameList;Q=A.getCertificate(A,D)}var Z=xA.util.createBuffer();if(Q!==null)try{if(!xA.util.isArray(Q))Q=[Q];var G=null;for(var F=0;F<Q.length;++F){var I=xA.pem.decode(Q[F])[0];if(I.type!=="CERTIFICATE"&&I.type!=="X509 CERTIFICATE"&&I.type!=="TRUSTED CERTIFICATE"){var Y=new Error('Could not convert certificate from PEM; PEM header type is not "CERTIFICATE", "X509 CERTIFICATE", or "TRUSTED CERTIFICATE".');throw Y.headerType=I.type,Y}if(I.procType&&I.procType.type==="ENCRYPTED")throw new Error("Could not convert certificate from PEM; PEM is encrypted.");var W=xA.util.createBuffer(I.body);if(G===null)G=xA.asn1.fromDer(W.bytes(),!1);var J=xA.util.createBuffer();DU(J,3,W),Z.putBuffer(J)}if(Q=xA.pki.certificateFromAsn1(G),B)A.session.clientCertificate=Q;else A.session.serverCertificate=Q}catch(C){return A.error(A,{message:"Could not send certificate list.",cause:C,send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.bad_certificate}})}var X=3+Z.length(),V=xA.util.createBuffer();return V.putByte(R1.HandshakeType.certificate),V.putInt24(X),DU(V,3,Z),V};R1.createClientKeyExchange=function(A){var B=xA.util.createBuffer();B.putByte(A.session.clientHelloVersion.major),B.putByte(A.session.clientHelloVersion.minor),B.putBytes(xA.random.getBytes(46));var Q=A.session.sp;Q.pre_master_secret=B.getBytes();var D=A.session.serverCertificate.publicKey;B=D.encrypt(Q.pre_master_secret);var Z=B.length+2,G=xA.util.createBuffer();return G.putByte(R1.HandshakeType.client_key_exchange),G.putInt24(Z),G.putInt16(B.length),G.putBytes(B),G};R1.createServerKeyExchange=function(A){var B=0,Q=xA.util.createBuffer();if(B>0)Q.putByte(R1.HandshakeType.server_key_exchange),Q.putInt24(B);return Q};R1.getClientSignature=function(A,B){var Q=xA.util.createBuffer();Q.putBuffer(A.session.md5.digest()),Q.putBuffer(A.session.sha1.digest()),Q=Q.getBytes(),A.getSignature=A.getSignature||function(D,Z,G){var F=null;if(D.getPrivateKey)try{F=D.getPrivateKey(D,D.session.clientCertificate),F=xA.pki.privateKeyFromPem(F)}catch(I){D.error(D,{message:"Could not get private key.",cause:I,send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.internal_error}})}if(F===null)D.error(D,{message:"No private key set.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.internal_error}});else Z=F.sign(Z,null);G(D,Z)},A.getSignature(A,Q,B)};R1.createCertificateVerify=function(A,B){var Q=B.length+2,D=xA.util.createBuffer();return D.putByte(R1.HandshakeType.certificate_verify),D.putInt24(Q),D.putInt16(B.length),D.putBytes(B),D};R1.createCertificateRequest=function(A){var B=xA.util.createBuffer();B.putByte(1);var Q=xA.util.createBuffer();for(var D in A.caStore.certs){var Z=A.caStore.certs[D],G=xA.pki.distinguishedNameToAsn1(Z.subject),F=xA.asn1.toDer(G);Q.putInt16(F.length()),Q.putBuffer(F)}var I=1+B.length()+2+Q.length(),Y=xA.util.createBuffer();return Y.putByte(R1.HandshakeType.certificate_request),Y.putInt24(I),DU(Y,1,B),DU(Y,2,Q),Y};R1.createServerHelloDone=function(A){var B=xA.util.createBuffer();return B.putByte(R1.HandshakeType.server_hello_done),B.putInt24(0),B};R1.createChangeCipherSpec=function(){var A=xA.util.createBuffer();return A.putByte(1),A};R1.createFinished=function(A){var B=xA.util.createBuffer();B.putBuffer(A.session.md5.digest()),B.putBuffer(A.session.sha1.digest());var Q=A.entity===R1.ConnectionEnd.client,D=A.session.sp,Z=12,G=dv1,F=Q?"client finished":"server finished";B=G(D.master_secret,F,B.getBytes(),Z);var I=xA.util.createBuffer();return I.putByte(R1.HandshakeType.finished),I.putInt24(B.length()),I.putBuffer(B),I};R1.createHeartbeat=function(A,B,Q){if(typeof Q==="undefined")Q=B.length;var D=xA.util.createBuffer();D.putByte(A),D.putInt16(Q),D.putBytes(B);var Z=D.length(),G=Math.max(16,Z-Q-3);return D.putBytes(xA.random.getBytes(G)),D};R1.queue=function(A,B){if(!B)return;if(B.fragment.length()===0){if(B.type===R1.ContentType.handshake||B.type===R1.ContentType.alert||B.type===R1.ContentType.change_cipher_spec)return}if(B.type===R1.ContentType.handshake){var Q=B.fragment.bytes();A.session.md5.update(Q),A.session.sha1.update(Q),Q=null}var D;if(B.fragment.length()<=R1.MaxFragment)D=[B];else{D=[];var Z=B.fragment.bytes();while(Z.length>R1.MaxFragment)D.push(R1.createRecord(A,{type:B.type,data:xA.util.createBuffer(Z.slice(0,R1.MaxFragment))})),Z=Z.slice(R1.MaxFragment);if(Z.length>0)D.push(R1.createRecord(A,{type:B.type,data:xA.util.createBuffer(Z)}))}for(var G=0;G<D.length&&!A.fail;++G){var F=D[G],I=A.state.current.write;if(I.update(A,F))A.records.push(F)}};R1.flush=function(A){for(var B=0;B<A.records.length;++B){var Q=A.records[B];A.tlsData.putByte(Q.type),A.tlsData.putByte(Q.version.major),A.tlsData.putByte(Q.version.minor),A.tlsData.putInt16(Q.fragment.length()),A.tlsData.putBuffer(A.records[B].fragment)}return A.records=[],A.tlsDataReady(A)};var Mq0=function(A){switch(A){case!0:return!0;case xA.pki.certificateError.bad_certificate:return R1.Alert.Description.bad_certificate;case xA.pki.certificateError.unsupported_certificate:return R1.Alert.Description.unsupported_certificate;case xA.pki.certificateError.certificate_revoked:return R1.Alert.Description.certificate_revoked;case xA.pki.certificateError.certificate_expired:return R1.Alert.Description.certificate_expired;case xA.pki.certificateError.certificate_unknown:return R1.Alert.Description.certificate_unknown;case xA.pki.certificateError.unknown_ca:return R1.Alert.Description.unknown_ca;default:return R1.Alert.Description.bad_certificate}},lG8=function(A){switch(A){case!0:return!0;case R1.Alert.Description.bad_certificate:return xA.pki.certificateError.bad_certificate;case R1.Alert.Description.unsupported_certificate:return xA.pki.certificateError.unsupported_certificate;case R1.Alert.Description.certificate_revoked:return xA.pki.certificateError.certificate_revoked;case R1.Alert.Description.certificate_expired:return xA.pki.certificateError.certificate_expired;case R1.Alert.Description.certificate_unknown:return xA.pki.certificateError.certificate_unknown;case R1.Alert.Description.unknown_ca:return xA.pki.certificateError.unknown_ca;default:return xA.pki.certificateError.bad_certificate}};R1.verifyCertificateChain=function(A,B){try{var Q={};for(var D in A.verifyOptions)Q[D]=A.verifyOptions[D];Q.verify=function(G,F,I){var Y=Mq0(G),W=A.verify(A,G,F,I);if(W!==!0){if(typeof W==="object"&&!xA.util.isArray(W)){var J=new Error("The application rejected the certificate.");if(J.send=!0,J.alert={level:R1.Alert.Level.fatal,description:R1.Alert.Description.bad_certificate},W.message)J.message=W.message;if(W.alert)J.alert.description=W.alert;throw J}if(W!==G)W=lG8(W)}return W},xA.pki.verifyCertificateChain(A.caStore,B,Q)}catch(G){var Z=G;if(typeof Z!=="object"||xA.util.isArray(Z))Z={send:!0,alert:{level:R1.Alert.Level.fatal,description:Mq0(G)}};if(!("send"in Z))Z.send=!0;if(!("alert"in Z))Z.alert={level:R1.Alert.Level.fatal,description:Mq0(Z.error)};A.error(A,Z)}return!A.fail};R1.createSessionCache=function(A,B){var Q=null;if(A&&A.getSession&&A.setSession&&A.order)Q=A;else{Q={},Q.cache=A||{},Q.capacity=Math.max(B||100,1),Q.order=[];for(var D in A)if(Q.order.length<=B)Q.order.push(D);else delete A[D];Q.getSession=function(Z){var G=null,F=null;if(Z)F=xA.util.bytesToHex(Z);else if(Q.order.length>0)F=Q.order[0];if(F!==null&&F in Q.cache){G=Q.cache[F],delete Q.cache[F];for(var I in Q.order)if(Q.order[I]===F){Q.order.splice(I,1);break}}return G},Q.setSession=function(Z,G){if(Q.order.length===Q.capacity){var F=Q.order.shift();delete Q.cache[F]}var F=xA.util.bytesToHex(Z);Q.order.push(F),Q.cache[F]=G}}return Q};R1.createConnection=function(A){var B=null;if(A.caStore)if(xA.util.isArray(A.caStore))B=xA.pki.createCaStore(A.caStore);else B=A.caStore;else B=xA.pki.createCaStore();var Q=A.cipherSuites||null;if(Q===null){Q=[];for(var D in R1.CipherSuites)Q.push(R1.CipherSuites[D])}var Z=A.server?R1.ConnectionEnd.server:R1.ConnectionEnd.client,G=A.sessionCache?R1.createSessionCache(A.sessionCache):null,F={version:{major:R1.Version.major,minor:R1.Version.minor},entity:Z,sessionId:A.sessionId,caStore:B,sessionCache:G,cipherSuites:Q,connected:A.connected,virtualHost:A.virtualHost||null,verifyClient:A.verifyClient||!1,verify:A.verify||function(J,X,V,C){return X},verifyOptions:A.verifyOptions||{},getCertificate:A.getCertificate||null,getPrivateKey:A.getPrivateKey||null,getSignature:A.getSignature||null,input:xA.util.createBuffer(),tlsData:xA.util.createBuffer(),data:xA.util.createBuffer(),tlsDataReady:A.tlsDataReady,dataReady:A.dataReady,heartbeatReceived:A.heartbeatReceived,closed:A.closed,error:function(J,X){if(X.origin=X.origin||(J.entity===R1.ConnectionEnd.client?"client":"server"),X.send)R1.queue(J,R1.createAlert(J,X.alert)),R1.flush(J);var V=X.fatal!==!1;if(V)J.fail=!0;if(A.error(J,X),V)J.close(!1)},deflate:A.deflate||null,inflate:A.inflate||null};F.reset=function(J){F.version={major:R1.Version.major,minor:R1.Version.minor},F.record=null,F.session=null,F.peerCertificate=null,F.state={pending:null,current:null},F.expect=F.entity===R1.ConnectionEnd.client?PG8:vG8,F.fragmented=null,F.records=[],F.open=!1,F.handshakes=0,F.handshaking=!1,F.isConnected=!1,F.fail=!(J||typeof J==="undefined"),F.input.clear(),F.tlsData.clear(),F.data.clear(),F.state.current=R1.createConnectionState(F)},F.reset();var I=function(J,X){var V=X.type-R1.ContentType.change_cipher_spec,C=Tq0[J.entity][J.expect];if(V in C)C[V](J,X);else R1.handleUnexpected(J,X)},Y=function(J){var X=0,V=J.input,C=V.length();if(C<5)X=5-C;else{J.record={type:V.getByte(),version:{major:V.getByte(),minor:V.getByte()},length:V.getInt16(),fragment:xA.util.createBuffer(),ready:!1};var K=J.record.version.major===J.version.major;if(K&&J.session&&J.session.version)K=J.record.version.minor===J.version.minor;if(!K)J.error(J,{message:"Incompatible TLS version.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.protocol_version}})}return X},W=function(J){var X=0,V=J.input,C=V.length();if(C<J.record.length)X=J.record.length-C;else{J.record.fragment.putBytes(V.getBytes(J.record.length)),V.compact();var K=J.state.current.read;if(K.update(J,J.record)){if(J.fragmented!==null)if(J.fragmented.type===J.record.type)J.fragmented.fragment.putBuffer(J.record.fragment),J.record=J.fragmented;else J.error(J,{message:"Invalid fragmented record.",send:!0,alert:{level:R1.Alert.Level.fatal,description:R1.Alert.Description.unexpected_message}});J.record.ready=!0}}return X};return F.handshake=function(J){if(F.entity!==R1.ConnectionEnd.client)F.error(F,{message:"Cannot initiate handshake as a server.",fatal:!1});else if(F.handshaking)F.error(F,{message:"Handshake already in progress.",fatal:!1});else{if(F.fail&&!F.open&&F.handshakes===0)F.fail=!1;F.handshaking=!0,J=J||"";var X=null;if(J.length>0){if(F.sessionCache)X=F.sessionCache.getSession(J);if(X===null)J=""}if(J.length===0&&F.sessionCache){if(X=F.sessionCache.getSession(),X!==null)J=X.id}if(F.session={id:J,version:null,cipherSuite:null,compressionMethod:null,serverCertificate:null,certificateRequest:null,clientCertificate:null,sp:{},md5:xA.md.md5.create(),sha1:xA.md.sha1.create()},X)F.version=X.version,F.session.sp=X.sp;F.session.sp.client_random=R1.createRandom().getBytes(),F.open=!0,R1.queue(F,R1.createRecord(F,{type:R1.ContentType.handshake,data:R1.createClientHello(F)})),R1.flush(F)}},F.process=function(J){var X=0;if(J)F.input.putBytes(J);if(!F.fail){if(F.record!==null&&F.record.ready&&F.record.fragment.isEmpty())F.record=null;if(F.record===null)X=Y(F);if(!F.fail&&F.record!==null&&!F.record.ready)X=W(F);if(!F.fail&&F.record!==null&&F.record.ready)I(F,F.record)}return X},F.prepare=function(J){return R1.queue(F,R1.createRecord(F,{type:R1.ContentType.application_data,data:xA.util.createBuffer(J)})),R1.flush(F)},F.prepareHeartbeatRequest=function(J,X){if(J instanceof xA.util.ByteBuffer)J=J.bytes();if(typeof X==="undefined")X=J.length;return F.expectedHeartbeatPayload=J,R1.queue(F,R1.createRecord(F,{type:R1.ContentType.heartbeat,data:R1.createHeartbeat(R1.HeartbeatMessageType.heartbeat_request,J,X)})),R1.flush(F)},F.close=function(J){if(!F.fail&&F.sessionCache&&F.session){var X={id:F.session.id,version:F.session.version,sp:F.session.sp};X.sp.keys=null,F.sessionCache.setSession(X.id,X)}if(F.open){if(F.open=!1,F.input.clear(),F.isConnected||F.handshaking)F.isConnected=F.handshaking=!1,R1.queue(F,R1.createAlert(F,{level:R1.Alert.Level.warning,description:R1.Alert.Description.close_notify})),R1.flush(F);F.closed(F)}F.reset(J)},F};YOB.exports=xA.tls=xA.tls||{};for(TG1 in R1)if(typeof R1[TG1]!=="function")xA.tls[TG1]=R1[TG1];var TG1;xA.tls.prf_tls1=dv1;xA.tls.hmac_sha1=RG8;xA.tls.createSessionCache=R1.createSessionCache;xA.tls.createConnection=R1.createConnection});
var Qq0=E((wZ3,XRB)=>{var uM=M4();N8();var jv1=null;if(uM.util.isNodejs&&!uM.options.usePureJavaScript&&!process.versions["node-webkit"])jv1=J1("crypto");var ZD8=XRB.exports=uM.prng=uM.prng||{};ZD8.create=function(A){var B={plugin:A,key:null,seed:null,time:null,reseeds:0,generated:0,keyBytes:""},Q=A.md,D=new Array(32);for(var Z=0;Z<32;++Z)D[Z]=Q.create();B.pools=D,B.pool=0,B.generate=function(W,J){if(!J)return B.generateSync(W);var X=B.plugin.cipher,V=B.plugin.increment,C=B.plugin.formatKey,K=B.plugin.formatSeed,H=uM.util.createBuffer();B.key=null,z();function z($){if($)return J($);if(H.length()>=W)return J(null,H.getBytes(W));if(B.generated>1048575)B.key=null;if(B.key===null)return uM.util.nextTick(function(){G(z)});var L=X(B.key,B.seed);B.generated+=L.length,H.putBytes(L),B.key=C(X(B.key,V(B.seed))),B.seed=K(X(B.key,B.seed)),uM.util.setImmediate(z)}},B.generateSync=function(W){var J=B.plugin.cipher,X=B.plugin.increment,V=B.plugin.formatKey,C=B.plugin.formatSeed;B.key=null;var K=uM.util.createBuffer();while(K.length()<W){if(B.generated>1048575)B.key=null;if(B.key===null)F();var H=J(B.key,B.seed);B.generated+=H.length,K.putBytes(H),B.key=V(J(B.key,X(B.seed))),B.seed=C(J(B.key,B.seed))}return K.getBytes(W)};function G(W){if(B.pools[0].messageLength>=32)return I(),W();var J=32-B.pools[0].messageLength<<5;B.seedFile(J,function(X,V){if(X)return W(X);B.collect(V),I(),W()})}function F(){if(B.pools[0].messageLength>=32)return I();var W=32-B.pools[0].messageLength<<5;B.collect(B.seedFileSync(W)),I()}function I(){B.reseeds=B.reseeds===4294967295?0:B.reseeds+1;var W=B.plugin.md.create();W.update(B.keyBytes);var J=1;for(var X=0;X<32;++X){if(B.reseeds%J===0)W.update(B.pools[X].digest().getBytes()),B.pools[X].start();J=J<<1}B.keyBytes=W.digest().getBytes(),W.start(),W.update(B.keyBytes);var V=W.digest().getBytes();B.key=B.plugin.formatKey(B.keyBytes),B.seed=B.plugin.formatSeed(V),B.generated=0}function Y(W){var J=null,X=uM.util.globalScope,V=X.crypto||X.msCrypto;if(V&&V.getRandomValues)J=function(R){return V.getRandomValues(R)};var C=uM.util.createBuffer();if(J)while(C.length()<W){var K=Math.max(1,Math.min(W-C.length(),65536)/4),H=new Uint32Array(Math.floor(K));try{J(H);for(var z=0;z<H.length;++z)C.putInt32(H[z])}catch(R){if(!(typeof QuotaExceededError!=="undefined"&&R instanceof QuotaExceededError))throw R}}if(C.length()<W){var $,L,N,O=Math.floor(Math.random()*65536);while(C.length()<W){L=16807*(O&65535),$=16807*(O>>16),L+=($&32767)<<16,L+=$>>15,L=(L&2147483647)+(L>>31),O=L&4294967295;for(var z=0;z<3;++z)N=O>>>(z<<3),N^=Math.floor(Math.random()*256),C.putByte(N&255)}}return C.getBytes(W)}if(jv1)B.seedFile=function(W,J){jv1.randomBytes(W,function(X,V){if(X)return J(X);J(null,V.toString())})},B.seedFileSync=function(W){return jv1.randomBytes(W).toString()};else B.seedFile=function(W,J){try{J(null,Y(W))}catch(X){J(X)}},B.seedFileSync=Y;return B.collect=function(W){var J=W.length;for(var X=0;X<J;++X)B.pools[B.pool].update(W.substr(X,1)),B.pool=B.pool===31?0:B.pool+1},B.collectInt=function(W,J){var X="";for(var V=0;V<J;V+=8)X+=String.fromCharCode(W>>V&255);B.collect(X)},B.registerWorker=function(W){if(W===self)B.seedFile=function(X,V){function C(K){var H=K.data;if(H.forge&&H.forge.prng)self.removeEventListener("message",C),V(H.forge.prng.err,H.forge.prng.bytes)}self.addEventListener("message",C),self.postMessage({forge:{prng:{needed:X}}})};else{var J=function(X){var V=X.data;if(V.forge&&V.forge.prng)B.seedFile(V.forge.prng.needed,function(C,K){W.postMessage({forge:{prng:{err:C,bytes:K}}})})};W.addEventListener("message",J)}},B}});
var Rv=E((JZ3,iMB)=>{var UG1=M4();UG1.pki=UG1.pki||{};var t$0=iMB.exports=UG1.pki.oids=UG1.oids=UG1.oids||{};function sA(A,B){t$0[A]=B,t$0[B]=A}function P5(A,B){t$0[A]=B}sA("1.2.840.113549.1.1.1","rsaEncryption");sA("1.2.840.113549.1.1.4","md5WithRSAEncryption");sA("1.2.840.113549.1.1.5","sha1WithRSAEncryption");sA("1.2.840.113549.1.1.7","RSAES-OAEP");sA("1.2.840.113549.1.1.8","mgf1");sA("1.2.840.113549.1.1.9","pSpecified");sA("1.2.840.113549.1.1.10","RSASSA-PSS");sA("1.2.840.113549.1.1.11","sha256WithRSAEncryption");sA("1.2.840.113549.1.1.12","sha384WithRSAEncryption");sA("1.2.840.113549.1.1.13","sha512WithRSAEncryption");sA("1.3.101.112","EdDSA25519");sA("1.2.840.10040.4.3","dsa-with-sha1");sA("1.3.14.3.2.7","desCBC");sA("1.3.14.3.2.26","sha1");sA("1.3.14.3.2.29","sha1WithRSASignature");sA("2.16.840.*********.2.1","sha256");sA("2.16.840.*********.2.2","sha384");sA("2.16.840.*********.2.3","sha512");sA("2.16.840.*********.2.4","sha224");sA("2.16.840.*********.2.5","sha512-224");sA("2.16.840.*********.2.6","sha512-256");sA("1.2.840.113549.2.2","md2");sA("1.2.840.113549.2.5","md5");sA("1.2.840.113549.1.7.1","data");sA("1.2.840.113549.1.7.2","signedData");sA("1.2.840.113549.1.7.3","envelopedData");sA("1.2.840.113549.1.7.4","signedAndEnvelopedData");sA("1.2.840.113549.1.7.5","digestedData");sA("1.2.840.113549.1.7.6","encryptedData");sA("1.2.840.113549.1.9.1","emailAddress");sA("1.2.840.113549.1.9.2","unstructuredName");sA("1.2.840.113549.1.9.3","contentType");sA("1.2.840.113549.1.9.4","messageDigest");sA("1.2.840.113549.1.9.5","signingTime");sA("1.2.840.113549.1.9.6","counterSignature");sA("1.2.840.113549.1.9.7","challengePassword");sA("1.2.840.113549.1.9.8","unstructuredAddress");sA("1.2.840.113549.1.9.14","extensionRequest");sA("1.2.840.113549.1.9.20","friendlyName");sA("1.2.840.113549.1.9.21","localKeyId");sA("1.2.840.113549.********","x509Certificate");sA("1.2.840.113549.*********.1","keyBag");sA("1.2.840.113549.*********.2","pkcs8ShroudedKeyBag");sA("1.2.840.113549.*********.3","certBag");sA("1.2.840.113549.*********.4","crlBag");sA("1.2.840.113549.*********.5","secretBag");sA("1.2.840.113549.*********.6","safeContentsBag");sA("1.2.840.113549.1.5.13","pkcs5PBES2");sA("1.2.840.113549.1.5.12","pkcs5PBKDF2");sA("1.2.840.113549.********","pbeWithSHAAnd128BitRC4");sA("1.2.840.113549.********","pbeWithSHAAnd40BitRC4");sA("1.2.840.113549.********","pbeWithSHAAnd3-KeyTripleDES-CBC");sA("1.2.840.113549.********","pbeWithSHAAnd2-KeyTripleDES-CBC");sA("1.2.840.113549.********","pbeWithSHAAnd128BitRC2-CBC");sA("1.2.840.113549.********","pbewithSHAAnd40BitRC2-CBC");sA("1.2.840.113549.2.7","hmacWithSHA1");sA("1.2.840.113549.2.8","hmacWithSHA224");sA("1.2.840.113549.2.9","hmacWithSHA256");sA("1.2.840.113549.2.10","hmacWithSHA384");sA("1.2.840.113549.2.11","hmacWithSHA512");sA("1.2.840.113549.3.7","des-EDE3-CBC");sA("2.16.840.*********.1.2","aes128-CBC");sA("2.16.840.*********.1.22","aes192-CBC");sA("2.16.840.*********.1.42","aes256-CBC");sA("*******","commonName");sA("*******","surname");sA("*******","serialNumber");sA("*******","countryName");sA("*******","localityName");sA("*******","stateOrProvinceName");sA("*******","streetAddress");sA("********","organizationName");sA("********","organizationalUnitName");sA("********","title");sA("********","description");sA("********","businessCategory");sA("********","postalCode");sA("*******2","givenName");sA("*******.4.1.311.********","jurisdictionOfIncorporationStateOrProvinceName");sA("*******.4.1.311.********","jurisdictionOfIncorporationCountryName");sA("2.16.840.1.113730.1.1","nsCertType");sA("2.16.840.1.113730.1.13","nsComment");P5("********","authorityKeyIdentifier");P5("********","keyAttributes");P5("********","certificatePolicies");P5("********","keyUsageRestriction");P5("********","policyMapping");P5("********","subtreesConstraint");P5("********","subjectAltName");P5("********","issuerAltName");P5("********","subjectDirectoryAttributes");P5("*********","basicConstraints");P5("*********","nameConstraints");P5("*********","policyConstraints");P5("*********","basicConstraints");sA("*********","subjectKeyIdentifier");sA("********5","keyUsage");P5("*********","privateKeyUsagePeriod");sA("*********","subjectAltName");sA("*********","issuerAltName");sA("*********","basicConstraints");P5("********0","cRLNumber");P5("********1","cRLReason");P5("********2","expirationDate");P5("********3","instructionCode");P5("********4","invalidityDate");P5("********5","cRLDistributionPoints");P5("********6","issuingDistributionPoint");P5("********7","deltaCRLIndicator");P5("********8","issuingDistributionPoint");P5("********9","certificateIssuer");P5("********0","nameConstraints");sA("********1","cRLDistributionPoints");sA("********2","certificatePolicies");P5("********3","policyMappings");P5("********4","policyConstraints");sA("********5","authorityKeyIdentifier");P5("********6","policyConstraints");sA("********7","extKeyUsage");P5("********6","freshestCRL");P5("********4","inhibitAnyPolicy");sA("*******.4.1.11129.2.4.2","timestampList");sA("*******.5.5.7.1.1","authorityInfoAccess");sA("*******.5.5.7.3.1","serverAuth");sA("*******.5.5.7.3.2","clientAuth");sA("*******.5.5.7.3.3","codeSigning");sA("*******.5.5.7.3.4","emailProtection");sA("*******.5.5.7.3.8","timeStamping")});
var Rv1=E((KZ3,ARB)=>{var fM=M4();bM();N8();var tMB=ARB.exports=fM.md5=fM.md5||{};fM.md.md5=fM.md.algorithms.md5=tMB;tMB.create=function(){if(!eMB)p78();var A=null,B=fM.util.createBuffer(),Q=new Array(16),D={algorithm:"md5",blockLength:64,digestLength:16,messageLength:0,fullMessageLength:null,messageLengthSize:8};return D.start=function(){D.messageLength=0,D.fullMessageLength=D.messageLength64=[];var Z=D.messageLengthSize/4;for(var G=0;G<Z;++G)D.fullMessageLength.push(0);return B=fM.util.createBuffer(),A={h0:1732584193,h1:4023233417,h2:2562383102,h3:271733878},D},D.start(),D.update=function(Z,G){if(G==="utf8")Z=fM.util.encodeUtf8(Z);var F=Z.length;D.messageLength+=F,F=[F/4294967296>>>0,F>>>0];for(var I=D.fullMessageLength.length-1;I>=0;--I)D.fullMessageLength[I]+=F[1],F[1]=F[0]+(D.fullMessageLength[I]/4294967296>>>0),D.fullMessageLength[I]=D.fullMessageLength[I]>>>0,F[0]=F[1]/4294967296>>>0;if(B.putBytes(Z),oMB(A,Q,B),B.read>2048||B.length()===0)B.compact();return D},D.digest=function(){var Z=fM.util.createBuffer();Z.putBytes(B.bytes());var G=D.fullMessageLength[D.fullMessageLength.length-1]+D.messageLengthSize,F=G&D.blockLength-1;Z.putBytes(e$0.substr(0,D.blockLength-F));var I,Y=0;for(var W=D.fullMessageLength.length-1;W>=0;--W)I=D.fullMessageLength[W]*8+Y,Y=I/4294967296>>>0,Z.putInt32Le(I>>>0);var J={h0:A.h0,h1:A.h1,h2:A.h2,h3:A.h3};oMB(J,Q,Z);var X=fM.util.createBuffer();return X.putInt32Le(J.h0),X.putInt32Le(J.h1),X.putInt32Le(J.h2),X.putInt32Le(J.h3),X},D};var e$0=null,Mv1=null,$G1=null,p11=null,eMB=!1;function p78(){e$0=String.fromCharCode(128),e$0+=fM.util.fillString(String.fromCharCode(0),64),Mv1=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,1,6,11,0,5,10,15,4,9,14,3,8,13,2,7,12,5,8,11,14,1,4,7,10,13,0,3,6,9,12,15,2,0,7,14,5,12,3,10,1,8,15,6,13,4,11,2,9],$G1=[7,12,17,22,7,12,17,22,7,12,17,22,7,12,17,22,5,9,14,20,5,9,14,20,5,9,14,20,5,9,14,20,4,11,16,23,4,11,16,23,4,11,16,23,4,11,16,23,6,10,15,21,6,10,15,21,6,10,15,21,6,10,15,21],p11=new Array(64);for(var A=0;A<64;++A)p11[A]=Math.floor(Math.abs(Math.sin(A+1))*4294967296);eMB=!0}function oMB(A,B,Q){var D,Z,G,F,I,Y,W,J,X=Q.length();while(X>=64){Z=A.h0,G=A.h1,F=A.h2,I=A.h3;for(J=0;J<16;++J)B[J]=Q.getInt32Le(),Y=I^G&(F^I),D=Z+Y+p11[J]+B[J],W=$G1[J],Z=I,I=F,F=G,G+=D<<W|D>>>32-W;for(;J<32;++J)Y=F^I&(G^F),D=Z+Y+p11[J]+B[Mv1[J]],W=$G1[J],Z=I,I=F,F=G,G+=D<<W|D>>>32-W;for(;J<48;++J)Y=G^F^I,D=Z+Y+p11[J]+B[Mv1[J]],W=$G1[J],Z=I,I=F,F=G,G+=D<<W|D>>>32-W;for(;J<64;++J)Y=F^(G|~I),D=Z+Y+p11[J]+B[Mv1[J]],W=$G1[J],Z=I,I=F,F=G,G+=D<<W|D>>>32-W;A.h0=A.h0+Z|0,A.h1=A.h1+G|0,A.h2=A.h2+F|0,A.h3=A.h3+I|0,X-=64}}});
var Sv1=E((EZ3,GRB)=>{var qX=M4();l11();bM();N8();var QD8=qX.pkcs5=qX.pkcs5||{},ZS;if(qX.util.isNodejs&&!qX.options.usePureJavaScript)ZS=J1("crypto");GRB.exports=qX.pbkdf2=QD8.pbkdf2=function(A,B,Q,D,Z,G){if(typeof Z==="function")G=Z,Z=null;if(qX.util.isNodejs&&!qX.options.usePureJavaScript&&ZS.pbkdf2&&(Z===null||typeof Z!=="object")&&(ZS.pbkdf2Sync.length>4||(!Z||Z==="sha1"))){if(typeof Z!=="string")Z="sha1";if(A=Buffer.from(A,"binary"),B=Buffer.from(B,"binary"),!G){if(ZS.pbkdf2Sync.length===4)return ZS.pbkdf2Sync(A,B,Q,D).toString("binary");return ZS.pbkdf2Sync(A,B,Q,D,Z).toString("binary")}if(ZS.pbkdf2Sync.length===4)return ZS.pbkdf2(A,B,Q,D,function(N,O){if(N)return G(N);G(null,O.toString("binary"))});return ZS.pbkdf2(A,B,Q,D,Z,function(N,O){if(N)return G(N);G(null,O.toString("binary"))})}if(typeof Z==="undefined"||Z===null)Z="sha1";if(typeof Z==="string"){if(!(Z in qX.md.algorithms))throw new Error("Unknown hash algorithm: "+Z);Z=qX.md[Z].create()}var F=Z.digestLength;if(D>4294967295*F){var I=new Error("Derived key is too long.");if(G)return G(I);throw I}var Y=Math.ceil(D/F),W=D-(Y-1)*F,J=qX.hmac.create();J.start(Z,A);var X="",V,C,K;if(!G){for(var H=1;H<=Y;++H){J.start(null,null),J.update(B),J.update(qX.util.int32ToBytes(H)),V=K=J.digest().getBytes();for(var z=2;z<=Q;++z)J.start(null,null),J.update(K),C=J.digest().getBytes(),V=qX.util.xorBytes(V,C,F),K=C;X+=H<Y?V:V.substr(0,W)}return X}var H=1,z;function $(){if(H>Y)return G(null,X);J.start(null,null),J.update(B),J.update(qX.util.int32ToBytes(H)),V=K=J.digest().getBytes(),z=2,L()}function L(){if(z<=Q)return J.start(null,null),J.update(K),C=J.digest().getBytes(),V=qX.util.xorBytes(V,C,F),K=C,++z,qX.util.setImmediate(L);X+=H<Y?V:V.substr(0,W),++H,$()}$()}});
var Uv1=E((IZ3,uMB)=>{var MI=M4();N8();uMB.exports=MI.cipher=MI.cipher||{};MI.cipher.algorithms=MI.cipher.algorithms||{};MI.cipher.createCipher=function(A,B){var Q=A;if(typeof Q==="string"){if(Q=MI.cipher.getAlgorithm(Q),Q)Q=Q()}if(!Q)throw new Error("Unsupported algorithm: "+A);return new MI.cipher.BlockCipher({algorithm:Q,key:B,decrypt:!1})};MI.cipher.createDecipher=function(A,B){var Q=A;if(typeof Q==="string"){if(Q=MI.cipher.getAlgorithm(Q),Q)Q=Q()}if(!Q)throw new Error("Unsupported algorithm: "+A);return new MI.cipher.BlockCipher({algorithm:Q,key:B,decrypt:!0})};MI.cipher.registerAlgorithm=function(A,B){A=A.toUpperCase(),MI.cipher.algorithms[A]=B};MI.cipher.getAlgorithm=function(A){if(A=A.toUpperCase(),A in MI.cipher.algorithms)return MI.cipher.algorithms[A];return null};var i$0=MI.cipher.BlockCipher=function(A){this.algorithm=A.algorithm,this.mode=this.algorithm.mode,this.blockSize=this.mode.blockSize,this._finish=!1,this._input=null,this.output=null,this._op=A.decrypt?this.mode.decrypt:this.mode.encrypt,this._decrypt=A.decrypt,this.algorithm.initialize(A)};i$0.prototype.start=function(A){A=A||{};var B={};for(var Q in A)B[Q]=A[Q];B.decrypt=this._decrypt,this._finish=!1,this._input=MI.util.createBuffer(),this.output=A.output||MI.util.createBuffer(),this.mode.start(B)};i$0.prototype.update=function(A){if(A)this._input.putBuffer(A);while(!this._op.call(this.mode,this._input,this.output,this._finish)&&!this._finish);this._input.compact()};i$0.prototype.finish=function(A){if(A&&(this.mode.name==="ECB"||this.mode.name==="CBC"))this.mode.pad=function(Q){return A(this.blockSize,Q,!1)},this.mode.unpad=function(Q){return A(this.blockSize,Q,!0)};var B={};if(B.decrypt=this._decrypt,B.overflow=this._input.length()%this.blockSize,!this._decrypt&&this.mode.pad){if(!this.mode.pad(this._input,B))return!1}if(this._finish=!0,this.update(),this._decrypt&&this.mode.unpad){if(!this.mode.unpad(this.output,B))return!1}if(this.mode.afterFinish){if(!this.mode.afterFinish(this.output,B))return!1}return!0}});
var Vq0=E((RZ3,Xq0)=>{var Tv=M4();N8();LG1();eE();(function(){if(Tv.prime){Xq0.exports=Tv.prime;return}var A=Xq0.exports=Tv.prime=Tv.prime||{},B=Tv.jsbn.BigInteger,Q=[6,4,2,4,2,4,6,2],D=new B(null);D.fromInt(30);var Z=function(X,V){return X|V};A.generateProbablePrime=function(X,V,C){if(typeof V==="function")C=V,V={};V=V||{};var K=V.algorithm||"PRIMEINC";if(typeof K==="string")K={name:K};K.options=K.options||{};var H=V.prng||Tv.random,z={nextBytes:function($){var L=H.getBytesSync($.length);for(var N=0;N<$.length;++N)$[N]=L.charCodeAt(N)}};if(K.name==="PRIMEINC")return G(X,z,K.options,C);throw new Error("Invalid prime generation algorithm: "+K.name)};function G(X,V,C,K){if("workers"in C)return Y(X,V,C,K);return F(X,V,C,K)}function F(X,V,C,K){var H=W(X,V),z=0,$=J(H.bitLength());if("millerRabinTests"in C)$=C.millerRabinTests;var L=10;if("maxBlockTime"in C)L=C.maxBlockTime;I(H,X,V,z,$,L,K)}function I(X,V,C,K,H,z,$){var L=+new Date;do{if(X.bitLength()>V)X=W(V,C);if(X.isProbablePrime(H))return $(null,X);X.dAddOffset(Q[K++%8],0)}while(z<0||+new Date-L<z);Tv.util.setImmediate(function(){I(X,V,C,K,H,z,$)})}function Y(X,V,C,K){if(typeof Worker==="undefined")return F(X,V,C,K);var H=W(X,V),z=C.workers,$=C.workLoad||100,L=$*30/8,N=C.workerScript||"forge/prime.worker.js";if(z===-1)return Tv.util.estimateCores(function(R,T){if(R)T=2;z=T-1,O()});O();function O(){z=Math.max(1,z);var R=[];for(var T=0;T<z;++T)R[T]=new Worker(N);var j=z;for(var T=0;T<z;++T)R[T].addEventListener("message",k);var f=!1;function k(c){if(f)return;--j;var h=c.data;if(h.found){for(var n=0;n<R.length;++n)R[n].terminate();return f=!0,K(null,new B(h.prime,16))}if(H.bitLength()>X)H=W(X,V);var a=H.toString(16);c.target.postMessage({hex:a,workLoad:$}),H.dAddOffset(L,0)}}}function W(X,V){var C=new B(X,V),K=X-1;if(!C.testBit(K))C.bitwiseTo(B.ONE.shiftLeft(K),Z,C);return C.dAddOffset(31-C.mod(D).byteValue(),0),C}function J(X){if(X<=100)return 27;if(X<=150)return 18;if(X<=200)return 15;if(X<=250)return 12;if(X<=300)return 9;if(X<=350)return 8;if(X<=400)return 7;if(X<=500)return 6;if(X<=600)return 5;if(X<=800)return 4;if(X<=1250)return 3;return 2}})()});
var XOB=E((bZ3,JOB)=>{var kv=M4();Mv();Pq0();var ZU=JOB.exports=kv.tls;ZU.CipherSuites.TLS_RSA_WITH_AES_128_CBC_SHA={id:[0,47],name:"TLS_RSA_WITH_AES_128_CBC_SHA",initSecurityParameters:function(A){A.bulk_cipher_algorithm=ZU.BulkCipherAlgorithm.aes,A.cipher_type=ZU.CipherType.block,A.enc_key_length=16,A.block_length=16,A.fixed_iv_length=16,A.record_iv_length=16,A.mac_algorithm=ZU.MACAlgorithm.hmac_sha1,A.mac_length=20,A.mac_key_length=20},initConnectionState:WOB};ZU.CipherSuites.TLS_RSA_WITH_AES_256_CBC_SHA={id:[0,53],name:"TLS_RSA_WITH_AES_256_CBC_SHA",initSecurityParameters:function(A){A.bulk_cipher_algorithm=ZU.BulkCipherAlgorithm.aes,A.cipher_type=ZU.CipherType.block,A.enc_key_length=32,A.block_length=16,A.fixed_iv_length=16,A.record_iv_length=16,A.mac_algorithm=ZU.MACAlgorithm.hmac_sha1,A.mac_length=20,A.mac_key_length=20},initConnectionState:WOB};function WOB(A,B,Q){var D=B.entity===kv.tls.ConnectionEnd.client;A.read.cipherState={init:!1,cipher:kv.cipher.createDecipher("AES-CBC",D?Q.keys.server_write_key:Q.keys.client_write_key),iv:D?Q.keys.server_write_IV:Q.keys.client_write_IV},A.write.cipherState={init:!1,cipher:kv.cipher.createCipher("AES-CBC",D?Q.keys.client_write_key:Q.keys.server_write_key),iv:D?Q.keys.client_write_IV:Q.keys.server_write_IV},A.read.cipherFunction=aG8,A.write.cipherFunction=pG8,A.read.macLength=A.write.macLength=Q.mac_length,A.read.macFunction=A.write.macFunction=ZU.hmac_sha1}function pG8(A,B){var Q=!1,D=B.macFunction(B.macKey,B.sequenceNumber,A);A.fragment.putBytes(D),B.updateSequenceNumber();var Z;if(A.version.minor===ZU.Versions.TLS_1_0.minor)Z=B.cipherState.init?null:B.cipherState.iv;else Z=kv.random.getBytesSync(16);B.cipherState.init=!0;var G=B.cipherState.cipher;if(G.start({iv:Z}),A.version.minor>=ZU.Versions.TLS_1_1.minor)G.output.putBytes(Z);if(G.update(A.fragment),G.finish(iG8))A.fragment=G.output,A.length=A.fragment.length(),Q=!0;return Q}function iG8(A,B,Q){if(!Q){var D=A-B.length()%A;B.fillWithByte(D-1,D)}return!0}function nG8(A,B,Q){var D=!0;if(Q){var Z=B.length(),G=B.last();for(var F=Z-1-G;F<Z-1;++F)D=D&&B.at(F)==G;if(D)B.truncate(G+1)}return D}function aG8(A,B){var Q=!1,D;if(A.version.minor===ZU.Versions.TLS_1_0.minor)D=B.cipherState.init?null:B.cipherState.iv;else D=A.fragment.getBytes(16);B.cipherState.init=!0;var Z=B.cipherState.cipher;Z.start({iv:D}),Z.update(A.fragment),Q=Z.finish(nG8);var G=B.macLength,F=kv.random.getBytesSync(G),I=Z.output.length();if(I>=G)A.fragment=Z.output.getBytes(I-G),F=Z.output.getBytes(G);else A.fragment=Z.output.getBytes();A.fragment=kv.util.createBuffer(A.fragment),A.length=A.fragment.length();var Y=B.macFunction(B.macKey,B.sequenceNumber,A);return B.updateSequenceNumber(),Q=sG8(B.macKey,F,Y)&&Q,Q}function sG8(A,B,Q){var D=kv.hmac.create();return D.start("SHA1",A),D.update(B),B=D.digest().getBytes(),D.start(null,null),D.update(Q),Q=D.digest().getBytes(),B===Q}});
var a$0=E((YZ3,mMB)=>{var RI=M4();N8();RI.cipher=RI.cipher||{};var V6=mMB.exports=RI.cipher.modes=RI.cipher.modes||{};V6.ecb=function(A){A=A||{},this.name="ECB",this.cipher=A.cipher,this.blockSize=A.blockSize||16,this._ints=this.blockSize/4,this._inBlock=new Array(this._ints),this._outBlock=new Array(this._ints)};V6.ecb.prototype.start=function(A){};V6.ecb.prototype.encrypt=function(A,B,Q){if(A.length()<this.blockSize&&!(Q&&A.length()>0))return!0;for(var D=0;D<this._ints;++D)this._inBlock[D]=A.getInt32();this.cipher.encrypt(this._inBlock,this._outBlock);for(var D=0;D<this._ints;++D)B.putInt32(this._outBlock[D])};V6.ecb.prototype.decrypt=function(A,B,Q){if(A.length()<this.blockSize&&!(Q&&A.length()>0))return!0;for(var D=0;D<this._ints;++D)this._inBlock[D]=A.getInt32();this.cipher.decrypt(this._inBlock,this._outBlock);for(var D=0;D<this._ints;++D)B.putInt32(this._outBlock[D])};V6.ecb.prototype.pad=function(A,B){var Q=A.length()===this.blockSize?this.blockSize:this.blockSize-A.length();return A.fillWithByte(Q,Q),!0};V6.ecb.prototype.unpad=function(A,B){if(B.overflow>0)return!1;var Q=A.length(),D=A.at(Q-1);if(D>this.blockSize<<2)return!1;return A.truncate(D),!0};V6.cbc=function(A){A=A||{},this.name="CBC",this.cipher=A.cipher,this.blockSize=A.blockSize||16,this._ints=this.blockSize/4,this._inBlock=new Array(this._ints),this._outBlock=new Array(this._ints)};V6.cbc.prototype.start=function(A){if(A.iv===null){if(!this._prev)throw new Error("Invalid IV parameter.");this._iv=this._prev.slice(0)}else if(!("iv"in A))throw new Error("Invalid IV parameter.");else this._iv=wv1(A.iv,this.blockSize),this._prev=this._iv.slice(0)};V6.cbc.prototype.encrypt=function(A,B,Q){if(A.length()<this.blockSize&&!(Q&&A.length()>0))return!0;for(var D=0;D<this._ints;++D)this._inBlock[D]=this._prev[D]^A.getInt32();this.cipher.encrypt(this._inBlock,this._outBlock);for(var D=0;D<this._ints;++D)B.putInt32(this._outBlock[D]);this._prev=this._outBlock};V6.cbc.prototype.decrypt=function(A,B,Q){if(A.length()<this.blockSize&&!(Q&&A.length()>0))return!0;for(var D=0;D<this._ints;++D)this._inBlock[D]=A.getInt32();this.cipher.decrypt(this._inBlock,this._outBlock);for(var D=0;D<this._ints;++D)B.putInt32(this._prev[D]^this._outBlock[D]);this._prev=this._inBlock.slice(0)};V6.cbc.prototype.pad=function(A,B){var Q=A.length()===this.blockSize?this.blockSize:this.blockSize-A.length();return A.fillWithByte(Q,Q),!0};V6.cbc.prototype.unpad=function(A,B){if(B.overflow>0)return!1;var Q=A.length(),D=A.at(Q-1);if(D>this.blockSize<<2)return!1;return A.truncate(D),!0};V6.cfb=function(A){A=A||{},this.name="CFB",this.cipher=A.cipher,this.blockSize=A.blockSize||16,this._ints=this.blockSize/4,this._inBlock=null,this._outBlock=new Array(this._ints),this._partialBlock=new Array(this._ints),this._partialOutput=RI.util.createBuffer(),this._partialBytes=0};V6.cfb.prototype.start=function(A){if(!("iv"in A))throw new Error("Invalid IV parameter.");this._iv=wv1(A.iv,this.blockSize),this._inBlock=this._iv.slice(0),this._partialBytes=0};V6.cfb.prototype.encrypt=function(A,B,Q){var D=A.length();if(D===0)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),this._partialBytes===0&&D>=this.blockSize){for(var Z=0;Z<this._ints;++Z)this._inBlock[Z]=A.getInt32()^this._outBlock[Z],B.putInt32(this._inBlock[Z]);return}var G=(this.blockSize-D)%this.blockSize;if(G>0)G=this.blockSize-G;this._partialOutput.clear();for(var Z=0;Z<this._ints;++Z)this._partialBlock[Z]=A.getInt32()^this._outBlock[Z],this._partialOutput.putInt32(this._partialBlock[Z]);if(G>0)A.read-=this.blockSize;else for(var Z=0;Z<this._ints;++Z)this._inBlock[Z]=this._partialBlock[Z];if(this._partialBytes>0)this._partialOutput.getBytes(this._partialBytes);if(G>0&&!Q)return B.putBytes(this._partialOutput.getBytes(G-this._partialBytes)),this._partialBytes=G,!0;B.putBytes(this._partialOutput.getBytes(D-this._partialBytes)),this._partialBytes=0};V6.cfb.prototype.decrypt=function(A,B,Q){var D=A.length();if(D===0)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),this._partialBytes===0&&D>=this.blockSize){for(var Z=0;Z<this._ints;++Z)this._inBlock[Z]=A.getInt32(),B.putInt32(this._inBlock[Z]^this._outBlock[Z]);return}var G=(this.blockSize-D)%this.blockSize;if(G>0)G=this.blockSize-G;this._partialOutput.clear();for(var Z=0;Z<this._ints;++Z)this._partialBlock[Z]=A.getInt32(),this._partialOutput.putInt32(this._partialBlock[Z]^this._outBlock[Z]);if(G>0)A.read-=this.blockSize;else for(var Z=0;Z<this._ints;++Z)this._inBlock[Z]=this._partialBlock[Z];if(this._partialBytes>0)this._partialOutput.getBytes(this._partialBytes);if(G>0&&!Q)return B.putBytes(this._partialOutput.getBytes(G-this._partialBytes)),this._partialBytes=G,!0;B.putBytes(this._partialOutput.getBytes(D-this._partialBytes)),this._partialBytes=0};V6.ofb=function(A){A=A||{},this.name="OFB",this.cipher=A.cipher,this.blockSize=A.blockSize||16,this._ints=this.blockSize/4,this._inBlock=null,this._outBlock=new Array(this._ints),this._partialOutput=RI.util.createBuffer(),this._partialBytes=0};V6.ofb.prototype.start=function(A){if(!("iv"in A))throw new Error("Invalid IV parameter.");this._iv=wv1(A.iv,this.blockSize),this._inBlock=this._iv.slice(0),this._partialBytes=0};V6.ofb.prototype.encrypt=function(A,B,Q){var D=A.length();if(A.length()===0)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),this._partialBytes===0&&D>=this.blockSize){for(var Z=0;Z<this._ints;++Z)B.putInt32(A.getInt32()^this._outBlock[Z]),this._inBlock[Z]=this._outBlock[Z];return}var G=(this.blockSize-D)%this.blockSize;if(G>0)G=this.blockSize-G;this._partialOutput.clear();for(var Z=0;Z<this._ints;++Z)this._partialOutput.putInt32(A.getInt32()^this._outBlock[Z]);if(G>0)A.read-=this.blockSize;else for(var Z=0;Z<this._ints;++Z)this._inBlock[Z]=this._outBlock[Z];if(this._partialBytes>0)this._partialOutput.getBytes(this._partialBytes);if(G>0&&!Q)return B.putBytes(this._partialOutput.getBytes(G-this._partialBytes)),this._partialBytes=G,!0;B.putBytes(this._partialOutput.getBytes(D-this._partialBytes)),this._partialBytes=0};V6.ofb.prototype.decrypt=V6.ofb.prototype.encrypt;V6.ctr=function(A){A=A||{},this.name="CTR",this.cipher=A.cipher,this.blockSize=A.blockSize||16,this._ints=this.blockSize/4,this._inBlock=null,this._outBlock=new Array(this._ints),this._partialOutput=RI.util.createBuffer(),this._partialBytes=0};V6.ctr.prototype.start=function(A){if(!("iv"in A))throw new Error("Invalid IV parameter.");this._iv=wv1(A.iv,this.blockSize),this._inBlock=this._iv.slice(0),this._partialBytes=0};V6.ctr.prototype.encrypt=function(A,B,Q){var D=A.length();if(D===0)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),this._partialBytes===0&&D>=this.blockSize)for(var Z=0;Z<this._ints;++Z)B.putInt32(A.getInt32()^this._outBlock[Z]);else{var G=(this.blockSize-D)%this.blockSize;if(G>0)G=this.blockSize-G;this._partialOutput.clear();for(var Z=0;Z<this._ints;++Z)this._partialOutput.putInt32(A.getInt32()^this._outBlock[Z]);if(G>0)A.read-=this.blockSize;if(this._partialBytes>0)this._partialOutput.getBytes(this._partialBytes);if(G>0&&!Q)return B.putBytes(this._partialOutput.getBytes(G-this._partialBytes)),this._partialBytes=G,!0;B.putBytes(this._partialOutput.getBytes(D-this._partialBytes)),this._partialBytes=0}$v1(this._inBlock)};V6.ctr.prototype.decrypt=V6.ctr.prototype.encrypt;V6.gcm=function(A){A=A||{},this.name="GCM",this.cipher=A.cipher,this.blockSize=A.blockSize||16,this._ints=this.blockSize/4,this._inBlock=new Array(this._ints),this._outBlock=new Array(this._ints),this._partialOutput=RI.util.createBuffer(),this._partialBytes=0,this._R=3774873600};V6.gcm.prototype.start=function(A){if(!("iv"in A))throw new Error("Invalid IV parameter.");var B=RI.util.createBuffer(A.iv);this._cipherLength=0;var Q;if("additionalData"in A)Q=RI.util.createBuffer(A.additionalData);else Q=RI.util.createBuffer();if("tagLength"in A)this._tagLength=A.tagLength;else this._tagLength=128;if(this._tag=null,A.decrypt){if(this._tag=RI.util.createBuffer(A.tag).getBytes(),this._tag.length!==this._tagLength/8)throw new Error("Authentication tag does not match tag length.")}this._hashBlock=new Array(this._ints),this.tag=null,this._hashSubkey=new Array(this._ints),this.cipher.encrypt([0,0,0,0],this._hashSubkey),this.componentBits=4,this._m=this.generateHashTable(this._hashSubkey,this.componentBits);var D=B.length();if(D===12)this._j0=[B.getInt32(),B.getInt32(),B.getInt32(),1];else{this._j0=[0,0,0,0];while(B.length()>0)this._j0=this.ghash(this._hashSubkey,this._j0,[B.getInt32(),B.getInt32(),B.getInt32(),B.getInt32()]);this._j0=this.ghash(this._hashSubkey,this._j0,[0,0].concat(n$0(D*8)))}this._inBlock=this._j0.slice(0),$v1(this._inBlock),this._partialBytes=0,Q=RI.util.createBuffer(Q),this._aDataLength=n$0(Q.length()*8);var Z=Q.length()%this.blockSize;if(Z)Q.fillWithByte(0,this.blockSize-Z);this._s=[0,0,0,0];while(Q.length()>0)this._s=this.ghash(this._hashSubkey,this._s,[Q.getInt32(),Q.getInt32(),Q.getInt32(),Q.getInt32()])};V6.gcm.prototype.encrypt=function(A,B,Q){var D=A.length();if(D===0)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),this._partialBytes===0&&D>=this.blockSize){for(var Z=0;Z<this._ints;++Z)B.putInt32(this._outBlock[Z]^=A.getInt32());this._cipherLength+=this.blockSize}else{var G=(this.blockSize-D)%this.blockSize;if(G>0)G=this.blockSize-G;this._partialOutput.clear();for(var Z=0;Z<this._ints;++Z)this._partialOutput.putInt32(A.getInt32()^this._outBlock[Z]);if(G<=0||Q){if(Q){var F=D%this.blockSize;this._cipherLength+=F,this._partialOutput.truncate(this.blockSize-F)}else this._cipherLength+=this.blockSize;for(var Z=0;Z<this._ints;++Z)this._outBlock[Z]=this._partialOutput.getInt32();this._partialOutput.read-=this.blockSize}if(this._partialBytes>0)this._partialOutput.getBytes(this._partialBytes);if(G>0&&!Q)return A.read-=this.blockSize,B.putBytes(this._partialOutput.getBytes(G-this._partialBytes)),this._partialBytes=G,!0;B.putBytes(this._partialOutput.getBytes(D-this._partialBytes)),this._partialBytes=0}this._s=this.ghash(this._hashSubkey,this._s,this._outBlock),$v1(this._inBlock)};V6.gcm.prototype.decrypt=function(A,B,Q){var D=A.length();if(D<this.blockSize&&!(Q&&D>0))return!0;this.cipher.encrypt(this._inBlock,this._outBlock),$v1(this._inBlock),this._hashBlock[0]=A.getInt32(),this._hashBlock[1]=A.getInt32(),this._hashBlock[2]=A.getInt32(),this._hashBlock[3]=A.getInt32(),this._s=this.ghash(this._hashSubkey,this._s,this._hashBlock);for(var Z=0;Z<this._ints;++Z)B.putInt32(this._outBlock[Z]^this._hashBlock[Z]);if(D<this.blockSize)this._cipherLength+=D%this.blockSize;else this._cipherLength+=this.blockSize};V6.gcm.prototype.afterFinish=function(A,B){var Q=!0;if(B.decrypt&&B.overflow)A.truncate(this.blockSize-B.overflow);this.tag=RI.util.createBuffer();var D=this._aDataLength.concat(n$0(this._cipherLength*8));this._s=this.ghash(this._hashSubkey,this._s,D);var Z=[];this.cipher.encrypt(this._j0,Z);for(var G=0;G<this._ints;++G)this.tag.putInt32(this._s[G]^Z[G]);if(this.tag.truncate(this.tag.length()%(this._tagLength/8)),B.decrypt&&this.tag.bytes()!==this._tag)Q=!1;return Q};V6.gcm.prototype.multiply=function(A,B){var Q=[0,0,0,0],D=B.slice(0);for(var Z=0;Z<128;++Z){var G=A[Z/32|0]&1<<31-Z%32;if(G)Q[0]^=D[0],Q[1]^=D[1],Q[2]^=D[2],Q[3]^=D[3];this.pow(D,D)}return Q};V6.gcm.prototype.pow=function(A,B){var Q=A[3]&1;for(var D=3;D>0;--D)B[D]=A[D]>>>1|(A[D-1]&1)<<31;if(B[0]=A[0]>>>1,Q)B[0]^=this._R};V6.gcm.prototype.tableMultiply=function(A){var B=[0,0,0,0];for(var Q=0;Q<32;++Q){var D=Q/8|0,Z=A[D]>>>(7-Q%8)*4&15,G=this._m[Q][Z];B[0]^=G[0],B[1]^=G[1],B[2]^=G[2],B[3]^=G[3]}return B};V6.gcm.prototype.ghash=function(A,B,Q){return B[0]^=Q[0],B[1]^=Q[1],B[2]^=Q[2],B[3]^=Q[3],this.tableMultiply(B)};V6.gcm.prototype.generateHashTable=function(A,B){var Q=8/B,D=4*Q,Z=16*Q,G=new Array(Z);for(var F=0;F<Z;++F){var I=[0,0,0,0],Y=F/D|0,W=(D-1-F%D)*B;I[Y]=1<<B-1<<W,G[F]=this.generateSubHashTable(this.multiply(I,A),B)}return G};V6.gcm.prototype.generateSubHashTable=function(A,B){var Q=1<<B,D=Q>>>1,Z=new Array(Q);Z[D]=A.slice(0);var G=D>>>1;while(G>0)this.pow(Z[2*G],Z[G]=[]),G>>=1;G=2;while(G<D){for(var F=1;F<G;++F){var I=Z[G],Y=Z[F];Z[G+F]=[I[0]^Y[0],I[1]^Y[1],I[2]^Y[2],I[3]^Y[3]]}G*=2}Z[0]=[0,0,0,0];for(G=D+1;G<Q;++G){var W=Z[G^D];Z[G]=[A[0]^W[0],A[1]^W[1],A[2]^W[2],A[3]^W[3]]}return Z};function wv1(A,B){if(typeof A==="string")A=RI.util.createBuffer(A);if(RI.util.isArray(A)&&A.length>4){var Q=A;A=RI.util.createBuffer();for(var D=0;D<Q.length;++D)A.putByte(Q[D])}if(A.length()<B)throw new Error("Invalid IV length; got "+A.length()+" bytes and expected "+B+" bytes.");if(!RI.util.isArray(A)){var Z=[],G=B/4;for(var D=0;D<G;++D)Z.push(A.getInt32());A=Z}return A}function $v1(A){A[A.length-1]=A[A.length-1]+1&4294967295}function n$0(A){return[A/4294967296|0,A&4294967295]}});
var a11=E((LZ3,ORB)=>{var mM=M4();bM();N8();var MRB=ORB.exports=mM.sha1=mM.sha1||{};mM.md.sha1=mM.md.algorithms.sha1=MRB;MRB.create=function(){if(!RRB)nZ8();var A=null,B=mM.util.createBuffer(),Q=new Array(80),D={algorithm:"sha1",blockLength:64,digestLength:20,messageLength:0,fullMessageLength:null,messageLengthSize:8};return D.start=function(){D.messageLength=0,D.fullMessageLength=D.messageLength64=[];var Z=D.messageLengthSize/4;for(var G=0;G<Z;++G)D.fullMessageLength.push(0);return B=mM.util.createBuffer(),A={h0:1732584193,h1:4023233417,h2:2562383102,h3:271733878,h4:3285377520},D},D.start(),D.update=function(Z,G){if(G==="utf8")Z=mM.util.encodeUtf8(Z);var F=Z.length;D.messageLength+=F,F=[F/4294967296>>>0,F>>>0];for(var I=D.fullMessageLength.length-1;I>=0;--I)D.fullMessageLength[I]+=F[1],F[1]=F[0]+(D.fullMessageLength[I]/4294967296>>>0),D.fullMessageLength[I]=D.fullMessageLength[I]>>>0,F[0]=F[1]/4294967296>>>0;if(B.putBytes(Z),LRB(A,Q,B),B.read>2048||B.length()===0)B.compact();return D},D.digest=function(){var Z=mM.util.createBuffer();Z.putBytes(B.bytes());var G=D.fullMessageLength[D.fullMessageLength.length-1]+D.messageLengthSize,F=G&D.blockLength-1;Z.putBytes(Wq0.substr(0,D.blockLength-F));var I,Y,W=D.fullMessageLength[0]*8;for(var J=0;J<D.fullMessageLength.length-1;++J)I=D.fullMessageLength[J+1]*8,Y=I/4294967296>>>0,W+=Y,Z.putInt32(W>>>0),W=I>>>0;Z.putInt32(W);var X={h0:A.h0,h1:A.h1,h2:A.h2,h3:A.h3,h4:A.h4};LRB(X,Q,Z);var V=mM.util.createBuffer();return V.putInt32(X.h0),V.putInt32(X.h1),V.putInt32(X.h2),V.putInt32(X.h3),V.putInt32(X.h4),V},D};var Wq0=null,RRB=!1;function nZ8(){Wq0=String.fromCharCode(128),Wq0+=mM.util.fillString(String.fromCharCode(0),64),RRB=!0}function LRB(A,B,Q){var D,Z,G,F,I,Y,W,J,X=Q.length();while(X>=64){Z=A.h0,G=A.h1,F=A.h2,I=A.h3,Y=A.h4;for(J=0;J<16;++J)D=Q.getInt32(),B[J]=D,W=I^G&(F^I),D=(Z<<5|Z>>>27)+W+Y+1518500249+D,Y=I,I=F,F=(G<<30|G>>>2)>>>0,G=Z,Z=D;for(;J<20;++J)D=B[J-3]^B[J-8]^B[J-14]^B[J-16],D=D<<1|D>>>31,B[J]=D,W=I^G&(F^I),D=(Z<<5|Z>>>27)+W+Y+1518500249+D,Y=I,I=F,F=(G<<30|G>>>2)>>>0,G=Z,Z=D;for(;J<32;++J)D=B[J-3]^B[J-8]^B[J-14]^B[J-16],D=D<<1|D>>>31,B[J]=D,W=G^F^I,D=(Z<<5|Z>>>27)+W+Y+1859775393+D,Y=I,I=F,F=(G<<30|G>>>2)>>>0,G=Z,Z=D;for(;J<40;++J)D=B[J-6]^B[J-16]^B[J-28]^B[J-32],D=D<<2|D>>>30,B[J]=D,W=G^F^I,D=(Z<<5|Z>>>27)+W+Y+1859775393+D,Y=I,I=F,F=(G<<30|G>>>2)>>>0,G=Z,Z=D;for(;J<60;++J)D=B[J-6]^B[J-16]^B[J-28]^B[J-32],D=D<<2|D>>>30,B[J]=D,W=G&F|I&(G^F),D=(Z<<5|Z>>>27)+W+Y+2400959708+D,Y=I,I=F,F=(G<<30|G>>>2)>>>0,G=Z,Z=D;for(;J<80;++J)D=B[J-6]^B[J-16]^B[J-28]^B[J-32],D=D<<2|D>>>30,B[J]=D,W=G^F^I,D=(Z<<5|Z>>>27)+W+Y+3395469782+D,Y=I,I=F,F=(G<<30|G>>>2)>>>0,G=Z,Z=D;A.h0=A.h0+Z|0,A.h1=A.h1+G|0,A.h2=A.h2+F|0,A.h3=A.h3+I|0,A.h4=A.h4+Y|0,X-=64}}});
var am=E((HZ3,QRB)=>{var Tv1=M4();N8();var BRB=QRB.exports=Tv1.pem=Tv1.pem||{};BRB.encode=function(A,B){B=B||{};var Q="-----BEGIN "+A.type+`-----\r
`,D;if(A.procType)D={name:"Proc-Type",values:[String(A.procType.version),A.procType.type]},Q+=Ov1(D);if(A.contentDomain)D={name:"Content-Domain",values:[A.contentDomain]},Q+=Ov1(D);if(A.dekInfo){if(D={name:"DEK-Info",values:[A.dekInfo.algorithm]},A.dekInfo.parameters)D.values.push(A.dekInfo.parameters);Q+=Ov1(D)}if(A.headers)for(var Z=0;Z<A.headers.length;++Z)Q+=Ov1(A.headers[Z]);if(A.procType)Q+=`\r
`;return Q+=Tv1.util.encode64(A.body,B.maxline||64)+`\r
`,Q+="-----END "+A.type+`-----\r
`,Q};BRB.decode=function(A){var B=[],Q=/\s*-----BEGIN ([A-Z0-9- ]+)-----\r?\n?([\x21-\x7e\s]+?(?:\r?\n\r?\n))?([:A-Za-z0-9+\/=\s]+?)-----END \1-----/g,D=/([\x21-\x7e]+):\s*([\x21-\x7e\s^:]+)/,Z=/\r?\n/,G;while(!0){if(G=Q.exec(A),!G)break;var F=G[1];if(F==="NEW CERTIFICATE REQUEST")F="CERTIFICATE REQUEST";var I={type:F,procType:null,contentDomain:null,dekInfo:null,headers:[],body:Tv1.util.decode64(G[3])};if(B.push(I),!G[2])continue;var Y=G[2].split(Z),W=0;while(G&&W<Y.length){var J=Y[W].replace(/\s+$/,"");for(var X=W+1;X<Y.length;++X){var V=Y[X];if(!/\s/.test(V[0]))break;J+=V,W=X}if(G=J.match(D),G){var C={name:G[1],values:[]},K=G[2].split(",");for(var H=0;H<K.length;++H)C.values.push(i78(K[H]));if(!I.procType){if(C.name!=="Proc-Type")throw new Error('Invalid PEM formatted message. The first encapsulated header must be "Proc-Type".');else if(C.values.length!==2)throw new Error('Invalid PEM formatted message. The "Proc-Type" header must have two subfields.');I.procType={version:K[0],type:K[1]}}else if(!I.contentDomain&&C.name==="Content-Domain")I.contentDomain=K[0]||"";else if(!I.dekInfo&&C.name==="DEK-Info"){if(C.values.length===0)throw new Error('Invalid PEM formatted message. The "DEK-Info" header must have at least one subfield.');I.dekInfo={algorithm:K[0],parameters:K[1]||null}}else I.headers.push(C)}++W}if(I.procType==="ENCRYPTED"&&!I.dekInfo)throw new Error('Invalid PEM formatted message. The "DEK-Info" header must be present if "Proc-Type" is "ENCRYPTED".')}if(B.length===0)throw new Error("Invalid PEM formatted message.");return B};function Ov1(A){var B=A.name+": ",Q=[],D=function(Y,W){return" "+W};for(var Z=0;Z<A.values.length;++Z)Q.push(A.values[Z].replace(/^(\S+\r\n)/,D));B+=Q.join(",")+`\r
`;var G=0,F=-1;for(var Z=0;Z<B.length;++Z,++G)if(G>65&&F!==-1){var I=B[F];if(I===",")++F,B=B.substr(0,F)+`\r
 `+B.substr(F);else B=B.substr(0,F)+`\r
`+I+B.substr(F+1);G=Z-F-1,F=-1,++Z}else if(B[Z]===" "||B[Z]==="\t"||B[Z]===",")F=Z;return B}function i78(A){return A.replace(/^\s+/,"")}});
var bM=E((VZ3,sMB)=>{var Lv1=M4();sMB.exports=Lv1.md=Lv1.md||{};Lv1.md.algorithms=Lv1.md.algorithms||{}});
var bOB=E((dZ3,vOB)=>{vOB.exports=bM();Rv1();a11();Bq0();kq0()});
var bv1=E((kZ3,iRB)=>{var em=M4();eE();N8();var WG8=iRB.exports=em.pss=em.pss||{};WG8.create=function(A){if(arguments.length===3)A={md:arguments[0],mgf:arguments[1],saltLength:arguments[2]};var{md:B,mgf:Q}=A,D=B.digestLength,Z=A.salt||null;if(typeof Z==="string")Z=em.util.createBuffer(Z);var G;if("saltLength"in A)G=A.saltLength;else if(Z!==null)G=Z.length();else throw new Error("Salt length not specified or specific salt not given.");if(Z!==null&&Z.length()!==G)throw new Error("Given salt length does not match length of given salt.");var F=A.prng||em.random,I={};return I.encode=function(Y,W){var J,X=W-1,V=Math.ceil(X/8),C=Y.digest().getBytes();if(V<D+G+2)throw new Error("Message is too long to encrypt.");var K;if(Z===null)K=F.getBytesSync(G);else K=Z.bytes();var H=new em.util.ByteBuffer;H.fillWithByte(0,8),H.putBytes(C),H.putBytes(K),B.start(),B.update(H.getBytes());var z=B.digest().getBytes(),$=new em.util.ByteBuffer;$.fillWithByte(0,V-G-D-2),$.putByte(1),$.putBytes(K);var L=$.getBytes(),N=V-D-1,O=Q.generate(z,N),R="";for(J=0;J<N;J++)R+=String.fromCharCode(L.charCodeAt(J)^O.charCodeAt(J));var T=65280>>8*V-X&255;return R=String.fromCharCode(R.charCodeAt(0)&~T)+R.substr(1),R+z+String.fromCharCode(188)},I.verify=function(Y,W,J){var X,V=J-1,C=Math.ceil(V/8);if(W=W.substr(-C),C<D+G+2)throw new Error("Inconsistent parameters to PSS signature verification.");if(W.charCodeAt(C-1)!==188)throw new Error("Encoded message does not end in 0xBC.");var K=C-D-1,H=W.substr(0,K),z=W.substr(K,D),$=65280>>8*C-V&255;if((H.charCodeAt(0)&$)!==0)throw new Error("Bits beyond keysize not zero as expected.");var L=Q.generate(z,K),N="";for(X=0;X<K;X++)N+=String.fromCharCode(H.charCodeAt(X)^L.charCodeAt(X));N=String.fromCharCode(N.charCodeAt(0)&~$)+N.substr(1);var O=C-D-G-2;for(X=0;X<O;X++)if(N.charCodeAt(X)!==0)throw new Error("Leftmost octets not zero as expected");if(N.charCodeAt(O)!==1)throw new Error("Inconsistent PSS signature, 0x01 marker not found");var R=N.substr(-G),T=new em.util.ByteBuffer;T.fillWithByte(0,8),T.putBytes(Y),T.putBytes(R),B.start(),B.update(T.getBytes());var j=B.digest().getBytes();return z===j},I}});
var cOB=E((pZ3,dOB)=>{dOB.exports=M4();Mv();XOB();j$();Uv1();qG1();POB();l11();yOB();xOB();bOB();Eq0();Sv1();am();Jq0();$q0();gOB();Nq0();Vq0();Qq0();bv1();eE();Gq0();mOB();Pq0();N8()});
var eE=E(($Z3,Dq0)=>{var OI=M4();Mv();Bq0();Qq0();N8();(function(){if(OI.random&&OI.random.getBytes){Dq0.exports=OI.random;return}(function(A){var B={},Q=new Array(4),D=OI.util.createBuffer();B.formatKey=function(X){var V=OI.util.createBuffer(X);return X=new Array(4),X[0]=V.getInt32(),X[1]=V.getInt32(),X[2]=V.getInt32(),X[3]=V.getInt32(),OI.aes._expandKey(X,!1)},B.formatSeed=function(X){var V=OI.util.createBuffer(X);return X=new Array(4),X[0]=V.getInt32(),X[1]=V.getInt32(),X[2]=V.getInt32(),X[3]=V.getInt32(),X},B.cipher=function(X,V){return OI.aes._updateBlock(X,V,Q,!1),D.putInt32(Q[0]),D.putInt32(Q[1]),D.putInt32(Q[2]),D.putInt32(Q[3]),D.getBytes()},B.increment=function(X){return++X[3],X},B.md=OI.md.sha256;function Z(){var X=OI.prng.create(B);return X.getBytes=function(V,C){return X.generate(V,C)},X.getBytesSync=function(V){return X.generate(V)},X}var G=Z(),F=null,I=OI.util.globalScope,Y=I.crypto||I.msCrypto;if(Y&&Y.getRandomValues)F=function(X){return Y.getRandomValues(X)};if(OI.options.usePureJavaScript||!OI.util.isNodejs&&!F){if(typeof window==="undefined"||window.document===void 0);if(G.collectInt(+new Date,32),typeof navigator!=="undefined"){var W="";for(var J in navigator)try{if(typeof navigator[J]=="string")W+=navigator[J]}catch(X){}G.collect(W),W=null}if(A)A().mousemove(function(X){G.collectInt(X.clientX,16),G.collectInt(X.clientY,16)}),A().keypress(function(X){G.collectInt(X.charCode,8)})}if(!OI.random)OI.random=G;else for(var J in G)OI.random[J]=G[J];OI.random.createInstance=Z,Dq0.exports=OI.random})(typeof jQuery!=="undefined"?jQuery:null)})()});
var gOB=E((cZ3,hOB)=>{var _2=M4();Mv();j$();qG1();Rv();am();zq0();eE();N8();gv1();var N0=_2.asn1,LX=hOB.exports=_2.pkcs7=_2.pkcs7||{};LX.messageFromPem=function(A){var B=_2.pem.decode(A)[0];if(B.type!=="PKCS7"){var Q=new Error('Could not convert PKCS#7 message from PEM; PEM header type is not "PKCS#7".');throw Q.headerType=B.type,Q}if(B.procType&&B.procType.type==="ENCRYPTED")throw new Error("Could not convert PKCS#7 message from PEM; PEM is encrypted.");var D=N0.fromDer(B.body);return LX.messageFromAsn1(D)};LX.messageToPem=function(A,B){var Q={type:"PKCS7",body:N0.toDer(A.toAsn1()).getBytes()};return _2.pem.encode(Q,{maxline:B})};LX.messageFromAsn1=function(A){var B={},Q=[];if(!N0.validate(A,LX.asn1.contentInfoValidator,B,Q)){var D=new Error("Cannot read PKCS#7 message. ASN.1 object is not an PKCS#7 ContentInfo.");throw D.errors=Q,D}var Z=N0.derToOid(B.contentType),G;switch(Z){case _2.pki.oids.envelopedData:G=LX.createEnvelopedData();break;case _2.pki.oids.encryptedData:G=LX.createEncryptedData();break;case _2.pki.oids.signedData:G=LX.createSignedData();break;default:throw new Error("Cannot read PKCS#7 message. ContentType with OID "+Z+" is not (yet) supported.")}return G.fromAsn1(B.content.value[0]),G};LX.createSignedData=function(){var A=null;return A={type:_2.pki.oids.signedData,version:1,certificates:[],crls:[],signers:[],digestAlgorithmIdentifiers:[],contentInfo:null,signerInfos:[],fromAsn1:function(D){if(pq0(A,D,LX.asn1.signedDataValidator),A.certificates=[],A.crls=[],A.digestAlgorithmIdentifiers=[],A.contentInfo=null,A.signerInfos=[],A.rawCapture.certificates){var Z=A.rawCapture.certificates.value;for(var G=0;G<Z.length;++G)A.certificates.push(_2.pki.certificateFromAsn1(Z[G]))}},toAsn1:function(){if(!A.contentInfo)A.sign();var D=[];for(var Z=0;Z<A.certificates.length;++Z)D.push(_2.pki.certificateToAsn1(A.certificates[Z]));var G=[],F=N0.create(N0.Class.CONTEXT_SPECIFIC,0,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.INTEGER,!1,N0.integerToDer(A.version).getBytes()),N0.create(N0.Class.UNIVERSAL,N0.Type.SET,!0,A.digestAlgorithmIdentifiers),A.contentInfo])]);if(D.length>0)F.value[0].value.push(N0.create(N0.Class.CONTEXT_SPECIFIC,0,!0,D));if(G.length>0)F.value[0].value.push(N0.create(N0.Class.CONTEXT_SPECIFIC,1,!0,G));return F.value[0].value.push(N0.create(N0.Class.UNIVERSAL,N0.Type.SET,!0,A.signerInfos)),N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.OID,!1,N0.oidToDer(A.type).getBytes()),F])},addSigner:function(D){var{issuer:Z,serialNumber:G}=D;if(D.certificate){var F=D.certificate;if(typeof F==="string")F=_2.pki.certificateFromPem(F);Z=F.issuer.attributes,G=F.serialNumber}var I=D.key;if(!I)throw new Error("Could not add PKCS#7 signer; no private key specified.");if(typeof I==="string")I=_2.pki.privateKeyFromPem(I);var Y=D.digestAlgorithm||_2.pki.oids.sha1;switch(Y){case _2.pki.oids.sha1:case _2.pki.oids.sha256:case _2.pki.oids.sha384:case _2.pki.oids.sha512:case _2.pki.oids.md5:break;default:throw new Error("Could not add PKCS#7 signer; unknown message digest algorithm: "+Y)}var W=D.authenticatedAttributes||[];if(W.length>0){var J=!1,X=!1;for(var V=0;V<W.length;++V){var C=W[V];if(!J&&C.type===_2.pki.oids.contentType){if(J=!0,X)break;continue}if(!X&&C.type===_2.pki.oids.messageDigest){if(X=!0,J)break;continue}}if(!J||!X)throw new Error("Invalid signer.authenticatedAttributes. If signer.authenticatedAttributes is specified, then it must contain at least two attributes, PKCS #9 content-type and PKCS #9 message-digest.")}A.signers.push({key:I,version:1,issuer:Z,serialNumber:G,digestAlgorithm:Y,signatureAlgorithm:_2.pki.oids.rsaEncryption,signature:null,authenticatedAttributes:W,unauthenticatedAttributes:[]})},sign:function(D){if(D=D||{},typeof A.content!=="object"||A.contentInfo===null){if(A.contentInfo=N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.OID,!1,N0.oidToDer(_2.pki.oids.data).getBytes())]),"content"in A){var Z;if(A.content instanceof _2.util.ByteBuffer)Z=A.content.bytes();else if(typeof A.content==="string")Z=_2.util.encodeUtf8(A.content);if(D.detached)A.detachedContent=N0.create(N0.Class.UNIVERSAL,N0.Type.OCTETSTRING,!1,Z);else A.contentInfo.value.push(N0.create(N0.Class.CONTEXT_SPECIFIC,0,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.OCTETSTRING,!1,Z)]))}}if(A.signers.length===0)return;var G=B();Q(G)},verify:function(){throw new Error("PKCS#7 signature verification not yet implemented.")},addCertificate:function(D){if(typeof D==="string")D=_2.pki.certificateFromPem(D);A.certificates.push(D)},addCertificateRevokationList:function(D){throw new Error("PKCS#7 CRL support not yet implemented.")}},A;function B(){var D={};for(var Z=0;Z<A.signers.length;++Z){var G=A.signers[Z],F=G.digestAlgorithm;if(!(F in D))D[F]=_2.md[_2.pki.oids[F]].create();if(G.authenticatedAttributes.length===0)G.md=D[F];else G.md=_2.md[_2.pki.oids[F]].create()}A.digestAlgorithmIdentifiers=[];for(var F in D)A.digestAlgorithmIdentifiers.push(N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.OID,!1,N0.oidToDer(F).getBytes()),N0.create(N0.Class.UNIVERSAL,N0.Type.NULL,!1,"")]));return D}function Q(D){var Z;if(A.detachedContent)Z=A.detachedContent;else Z=A.contentInfo.value[1],Z=Z.value[0];if(!Z)throw new Error("Could not sign PKCS#7 message; there is no content to sign.");var G=N0.derToOid(A.contentInfo.value[0].value),F=N0.toDer(Z);F.getByte(),N0.getBerValueLength(F),F=F.getBytes();for(var I in D)D[I].start().update(F);var Y=new Date;for(var W=0;W<A.signers.length;++W){var J=A.signers[W];if(J.authenticatedAttributes.length===0){if(G!==_2.pki.oids.data)throw new Error("Invalid signer; authenticatedAttributes must be present when the ContentInfo content type is not PKCS#7 Data.")}else{J.authenticatedAttributesAsn1=N0.create(N0.Class.CONTEXT_SPECIFIC,0,!0,[]);var X=N0.create(N0.Class.UNIVERSAL,N0.Type.SET,!0,[]);for(var V=0;V<J.authenticatedAttributes.length;++V){var C=J.authenticatedAttributes[V];if(C.type===_2.pki.oids.messageDigest)C.value=D[J.digestAlgorithm].digest();else if(C.type===_2.pki.oids.signingTime){if(!C.value)C.value=Y}X.value.push(lq0(C)),J.authenticatedAttributesAsn1.value.push(lq0(C))}F=N0.toDer(X).getBytes(),J.md.start().update(F)}J.signature=J.key.sign(J.md,"RSASSA-PKCS1-V1_5")}A.signerInfos=wF8(A.signers)}};LX.createEncryptedData=function(){var A=null;return A={type:_2.pki.oids.encryptedData,version:0,encryptedContent:{algorithm:_2.pki.oids["aes256-CBC"]},fromAsn1:function(B){pq0(A,B,LX.asn1.encryptedDataValidator)},decrypt:function(B){if(B!==void 0)A.encryptedContent.key=B;fOB(A)}},A};LX.createEnvelopedData=function(){var A=null;return A={type:_2.pki.oids.envelopedData,version:0,recipients:[],encryptedContent:{algorithm:_2.pki.oids["aes256-CBC"]},fromAsn1:function(B){var Q=pq0(A,B,LX.asn1.envelopedDataValidator);A.recipients=zF8(Q.recipientInfos.value)},toAsn1:function(){return N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.OID,!1,N0.oidToDer(A.type).getBytes()),N0.create(N0.Class.CONTEXT_SPECIFIC,0,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.INTEGER,!1,N0.integerToDer(A.version).getBytes()),N0.create(N0.Class.UNIVERSAL,N0.Type.SET,!0,EF8(A.recipients)),N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,$F8(A.encryptedContent))])])])},findRecipient:function(B){var Q=B.issuer.attributes;for(var D=0;D<A.recipients.length;++D){var Z=A.recipients[D],G=Z.issuer;if(Z.serialNumber!==B.serialNumber)continue;if(G.length!==Q.length)continue;var F=!0;for(var I=0;I<Q.length;++I)if(G[I].type!==Q[I].type||G[I].value!==Q[I].value){F=!1;break}if(F)return Z}return null},decrypt:function(B,Q){if(A.encryptedContent.key===void 0&&B!==void 0&&Q!==void 0)switch(B.encryptedContent.algorithm){case _2.pki.oids.rsaEncryption:case _2.pki.oids.desCBC:var D=Q.decrypt(B.encryptedContent.content);A.encryptedContent.key=_2.util.createBuffer(D);break;default:throw new Error("Unsupported asymmetric cipher, OID "+B.encryptedContent.algorithm)}fOB(A)},addRecipient:function(B){A.recipients.push({version:0,issuer:B.issuer.attributes,serialNumber:B.serialNumber,encryptedContent:{algorithm:_2.pki.oids.rsaEncryption,key:B.publicKey}})},encrypt:function(B,Q){if(A.encryptedContent.content===void 0){Q=Q||A.encryptedContent.algorithm,B=B||A.encryptedContent.key;var D,Z,G;switch(Q){case _2.pki.oids["aes128-CBC"]:D=16,Z=16,G=_2.aes.createEncryptionCipher;break;case _2.pki.oids["aes192-CBC"]:D=24,Z=16,G=_2.aes.createEncryptionCipher;break;case _2.pki.oids["aes256-CBC"]:D=32,Z=16,G=_2.aes.createEncryptionCipher;break;case _2.pki.oids["des-EDE3-CBC"]:D=24,Z=8,G=_2.des.createEncryptionCipher;break;default:throw new Error("Unsupported symmetric cipher, OID "+Q)}if(B===void 0)B=_2.util.createBuffer(_2.random.getBytes(D));else if(B.length()!=D)throw new Error("Symmetric key has wrong length; got "+B.length()+" bytes, expected "+D+".");A.encryptedContent.algorithm=Q,A.encryptedContent.key=B,A.encryptedContent.parameter=_2.util.createBuffer(_2.random.getBytes(Z));var F=G(B);if(F.start(A.encryptedContent.parameter.copy()),F.update(A.content),!F.finish())throw new Error("Symmetric encryption failed.");A.encryptedContent.content=F.output}for(var I=0;I<A.recipients.length;++I){var Y=A.recipients[I];if(Y.encryptedContent.content!==void 0)continue;switch(Y.encryptedContent.algorithm){case _2.pki.oids.rsaEncryption:Y.encryptedContent.content=Y.encryptedContent.key.encrypt(A.encryptedContent.key.data);break;default:throw new Error("Unsupported asymmetric cipher, OID "+Y.encryptedContent.algorithm)}}}},A};function KF8(A){var B={},Q=[];if(!N0.validate(A,LX.asn1.recipientInfoValidator,B,Q)){var D=new Error("Cannot read PKCS#7 RecipientInfo. ASN.1 object is not an PKCS#7 RecipientInfo.");throw D.errors=Q,D}return{version:B.version.charCodeAt(0),issuer:_2.pki.RDNAttributesAsArray(B.issuer),serialNumber:_2.util.createBuffer(B.serial).toHex(),encryptedContent:{algorithm:N0.derToOid(B.encAlgorithm),parameter:B.encParameter?B.encParameter.value:void 0,content:B.encKey}}}function HF8(A){return N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.INTEGER,!1,N0.integerToDer(A.version).getBytes()),N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,[_2.pki.distinguishedNameToAsn1({attributes:A.issuer}),N0.create(N0.Class.UNIVERSAL,N0.Type.INTEGER,!1,_2.util.hexToBytes(A.serialNumber))]),N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.OID,!1,N0.oidToDer(A.encryptedContent.algorithm).getBytes()),N0.create(N0.Class.UNIVERSAL,N0.Type.NULL,!1,"")]),N0.create(N0.Class.UNIVERSAL,N0.Type.OCTETSTRING,!1,A.encryptedContent.content)])}function zF8(A){var B=[];for(var Q=0;Q<A.length;++Q)B.push(KF8(A[Q]));return B}function EF8(A){var B=[];for(var Q=0;Q<A.length;++Q)B.push(HF8(A[Q]));return B}function UF8(A){var B=N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.INTEGER,!1,N0.integerToDer(A.version).getBytes()),N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,[_2.pki.distinguishedNameToAsn1({attributes:A.issuer}),N0.create(N0.Class.UNIVERSAL,N0.Type.INTEGER,!1,_2.util.hexToBytes(A.serialNumber))]),N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.OID,!1,N0.oidToDer(A.digestAlgorithm).getBytes()),N0.create(N0.Class.UNIVERSAL,N0.Type.NULL,!1,"")])]);if(A.authenticatedAttributesAsn1)B.value.push(A.authenticatedAttributesAsn1);if(B.value.push(N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.OID,!1,N0.oidToDer(A.signatureAlgorithm).getBytes()),N0.create(N0.Class.UNIVERSAL,N0.Type.NULL,!1,"")])),B.value.push(N0.create(N0.Class.UNIVERSAL,N0.Type.OCTETSTRING,!1,A.signature)),A.unauthenticatedAttributes.length>0){var Q=N0.create(N0.Class.CONTEXT_SPECIFIC,1,!0,[]);for(var D=0;D<A.unauthenticatedAttributes.length;++D){var Z=A.unauthenticatedAttributes[D];Q.values.push(lq0(Z))}B.value.push(Q)}return B}function wF8(A){var B=[];for(var Q=0;Q<A.length;++Q)B.push(UF8(A[Q]));return B}function lq0(A){var B;if(A.type===_2.pki.oids.contentType)B=N0.create(N0.Class.UNIVERSAL,N0.Type.OID,!1,N0.oidToDer(A.value).getBytes());else if(A.type===_2.pki.oids.messageDigest)B=N0.create(N0.Class.UNIVERSAL,N0.Type.OCTETSTRING,!1,A.value.bytes());else if(A.type===_2.pki.oids.signingTime){var Q=new Date("1950-01-01T00:00:00Z"),D=new Date("2050-01-01T00:00:00Z"),Z=A.value;if(typeof Z==="string"){var G=Date.parse(Z);if(!isNaN(G))Z=new Date(G);else if(Z.length===13)Z=N0.utcTimeToDate(Z);else Z=N0.generalizedTimeToDate(Z)}if(Z>=Q&&Z<D)B=N0.create(N0.Class.UNIVERSAL,N0.Type.UTCTIME,!1,N0.dateToUtcTime(Z));else B=N0.create(N0.Class.UNIVERSAL,N0.Type.GENERALIZEDTIME,!1,N0.dateToGeneralizedTime(Z))}return N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.OID,!1,N0.oidToDer(A.type).getBytes()),N0.create(N0.Class.UNIVERSAL,N0.Type.SET,!0,[B])])}function $F8(A){return[N0.create(N0.Class.UNIVERSAL,N0.Type.OID,!1,N0.oidToDer(_2.pki.oids.data).getBytes()),N0.create(N0.Class.UNIVERSAL,N0.Type.SEQUENCE,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.OID,!1,N0.oidToDer(A.algorithm).getBytes()),!A.parameter?void 0:N0.create(N0.Class.UNIVERSAL,N0.Type.OCTETSTRING,!1,A.parameter.getBytes())]),N0.create(N0.Class.CONTEXT_SPECIFIC,0,!0,[N0.create(N0.Class.UNIVERSAL,N0.Type.OCTETSTRING,!1,A.content.getBytes())])]}function pq0(A,B,Q){var D={},Z=[];if(!N0.validate(B,Q,D,Z)){var G=new Error("Cannot read PKCS#7 message. ASN.1 object is not a supported PKCS#7 message.");throw G.errors=G,G}var F=N0.derToOid(D.contentType);if(F!==_2.pki.oids.data)throw new Error("Unsupported PKCS#7 message. Only wrapped ContentType Data supported.");if(D.encryptedContent){var I="";if(_2.util.isArray(D.encryptedContent))for(var Y=0;Y<D.encryptedContent.length;++Y){if(D.encryptedContent[Y].type!==N0.Type.OCTETSTRING)throw new Error("Malformed PKCS#7 message, expecting encrypted content constructed of only OCTET STRING objects.");I+=D.encryptedContent[Y].value}else I=D.encryptedContent;A.encryptedContent={algorithm:N0.derToOid(D.encAlgorithm),parameter:_2.util.createBuffer(D.encParameter.value),content:_2.util.createBuffer(I)}}if(D.content){var I="";if(_2.util.isArray(D.content))for(var Y=0;Y<D.content.length;++Y){if(D.content[Y].type!==N0.Type.OCTETSTRING)throw new Error("Malformed PKCS#7 message, expecting content constructed of only OCTET STRING objects.");I+=D.content[Y].value}else I=D.content;A.content=_2.util.createBuffer(I)}return A.version=D.version.charCodeAt(0),A.rawCapture=D,D}function fOB(A){if(A.encryptedContent.key===void 0)throw new Error("Symmetric key not available.");if(A.content===void 0){var B;switch(A.encryptedContent.algorithm){case _2.pki.oids["aes128-CBC"]:case _2.pki.oids["aes192-CBC"]:case _2.pki.oids["aes256-CBC"]:B=_2.aes.createDecryptionCipher(A.encryptedContent.key);break;case _2.pki.oids.desCBC:case _2.pki.oids["des-EDE3-CBC"]:B=_2.des.createDecryptionCipher(A.encryptedContent.key);break;default:throw new Error("Unsupported symmetric cipher, OID "+A.encryptedContent.algorithm)}if(B.start(A.encryptedContent.parameter),B.update(A.encryptedContent.content),!B.finish())throw new Error("Symmetric decryption failed.");A.content=B.output}}});
var gv1=E((yZ3,oRB)=>{var u9=M4();Mv();j$();qG1();bM();pRB();Rv();am();bv1();MG1();N8();var $1=u9.asn1,z2=oRB.exports=u9.pki=u9.pki||{},G8=z2.oids,CZ={};CZ.CN=G8.commonName;CZ.commonName="CN";CZ.C=G8.countryName;CZ.countryName="C";CZ.L=G8.localityName;CZ.localityName="L";CZ.ST=G8.stateOrProvinceName;CZ.stateOrProvinceName="ST";CZ.O=G8.organizationName;CZ.organizationName="O";CZ.OU=G8.organizationalUnitName;CZ.organizationalUnitName="OU";CZ.E=G8.emailAddress;CZ.emailAddress="E";var aRB=u9.pki.rsa.publicKeyValidator,JG8={name:"Certificate",tagClass:$1.Class.UNIVERSAL,type:$1.Type.SEQUENCE,constructed:!0,value:[{name:"Certificate.TBSCertificate",tagClass:$1.Class.UNIVERSAL,type:$1.Type.SEQUENCE,constructed:!0,captureAsn1:"tbsCertificate",value:[{name:"Certificate.TBSCertificate.version",tagClass:$1.Class.CONTEXT_SPECIFIC,type:0,constructed:!0,optional:!0,value:[{name:"Certificate.TBSCertificate.version.integer",tagClass:$1.Class.UNIVERSAL,type:$1.Type.INTEGER,constructed:!1,capture:"certVersion"}]},{name:"Certificate.TBSCertificate.serialNumber",tagClass:$1.Class.UNIVERSAL,type:$1.Type.INTEGER,constructed:!1,capture:"certSerialNumber"},{name:"Certificate.TBSCertificate.signature",tagClass:$1.Class.UNIVERSAL,type:$1.Type.SEQUENCE,constructed:!0,value:[{name:"Certificate.TBSCertificate.signature.algorithm",tagClass:$1.Class.UNIVERSAL,type:$1.Type.OID,constructed:!1,capture:"certinfoSignatureOid"},{name:"Certificate.TBSCertificate.signature.parameters",tagClass:$1.Class.UNIVERSAL,optional:!0,captureAsn1:"certinfoSignatureParams"}]},{name:"Certificate.TBSCertificate.issuer",tagClass:$1.Class.UNIVERSAL,type:$1.Type.SEQUENCE,constructed:!0,captureAsn1:"certIssuer"},{name:"Certificate.TBSCertificate.validity",tagClass:$1.Class.UNIVERSAL,type:$1.Type.SEQUENCE,constructed:!0,value:[{name:"Certificate.TBSCertificate.validity.notBefore (utc)",tagClass:$1.Class.UNIVERSAL,type:$1.Type.UTCTIME,constructed:!1,optional:!0,capture:"certValidity1UTCTime"},{name:"Certificate.TBSCertificate.validity.notBefore (generalized)",tagClass:$1.Class.UNIVERSAL,type:$1.Type.GENERALIZEDTIME,constructed:!1,optional:!0,capture:"certValidity2GeneralizedTime"},{name:"Certificate.TBSCertificate.validity.notAfter (utc)",tagClass:$1.Class.UNIVERSAL,type:$1.Type.UTCTIME,constructed:!1,optional:!0,capture:"certValidity3UTCTime"},{name:"Certificate.TBSCertificate.validity.notAfter (generalized)",tagClass:$1.Class.UNIVERSAL,type:$1.Type.GENERALIZEDTIME,constructed:!1,optional:!0,capture:"certValidity4GeneralizedTime"}]},{name:"Certificate.TBSCertificate.subject",tagClass:$1.Class.UNIVERSAL,type:$1.Type.SEQUENCE,constructed:!0,captureAsn1:"certSubject"},aRB,{name:"Certificate.TBSCertificate.issuerUniqueID",tagClass:$1.Class.CONTEXT_SPECIFIC,type:1,constructed:!0,optional:!0,value:[{name:"Certificate.TBSCertificate.issuerUniqueID.id",tagClass:$1.Class.UNIVERSAL,type:$1.Type.BITSTRING,constructed:!1,captureBitStringValue:"certIssuerUniqueId"}]},{name:"Certificate.TBSCertificate.subjectUniqueID",tagClass:$1.Class.CONTEXT_SPECIFIC,type:2,constructed:!0,optional:!0,value:[{name:"Certificate.TBSCertificate.subjectUniqueID.id",tagClass:$1.Class.UNIVERSAL,type:$1.Type.BITSTRING,constructed:!1,captureBitStringValue:"certSubjectUniqueId"}]},{name:"Certificate.TBSCertificate.extensions",tagClass:$1.Class.CONTEXT_SPECIFIC,type:3,constructed:!0,captureAsn1:"certExtensions",optional:!0}]},{name:"Certificate.signatureAlgorithm",tagClass:$1.Class.UNIVERSAL,type:$1.Type.SEQUENCE,constructed:!0,value:[{name:"Certificate.signatureAlgorithm.algorithm",tagClass:$1.Class.UNIVERSAL,type:$1.Type.OID,constructed:!1,capture:"certSignatureOid"},{name:"Certificate.TBSCertificate.signature.parameters",tagClass:$1.Class.UNIVERSAL,optional:!0,captureAsn1:"certSignatureParams"}]},{name:"Certificate.signatureValue",tagClass:$1.Class.UNIVERSAL,type:$1.Type.BITSTRING,constructed:!1,captureBitStringValue:"certSignature"}]},XG8={name:"rsapss",tagClass:$1.Class.UNIVERSAL,type:$1.Type.SEQUENCE,constructed:!0,value:[{name:"rsapss.hashAlgorithm",tagClass:$1.Class.CONTEXT_SPECIFIC,type:0,constructed:!0,value:[{name:"rsapss.hashAlgorithm.AlgorithmIdentifier",tagClass:$1.Class.UNIVERSAL,type:$1.Class.SEQUENCE,constructed:!0,optional:!0,value:[{name:"rsapss.hashAlgorithm.AlgorithmIdentifier.algorithm",tagClass:$1.Class.UNIVERSAL,type:$1.Type.OID,constructed:!1,capture:"hashOid"}]}]},{name:"rsapss.maskGenAlgorithm",tagClass:$1.Class.CONTEXT_SPECIFIC,type:1,constructed:!0,value:[{name:"rsapss.maskGenAlgorithm.AlgorithmIdentifier",tagClass:$1.Class.UNIVERSAL,type:$1.Class.SEQUENCE,constructed:!0,optional:!0,value:[{name:"rsapss.maskGenAlgorithm.AlgorithmIdentifier.algorithm",tagClass:$1.Class.UNIVERSAL,type:$1.Type.OID,constructed:!1,capture:"maskGenOid"},{name:"rsapss.maskGenAlgorithm.AlgorithmIdentifier.params",tagClass:$1.Class.UNIVERSAL,type:$1.Type.SEQUENCE,constructed:!0,value:[{name:"rsapss.maskGenAlgorithm.AlgorithmIdentifier.params.algorithm",tagClass:$1.Class.UNIVERSAL,type:$1.Type.OID,constructed:!1,capture:"maskGenHashOid"}]}]}]},{name:"rsapss.saltLength",tagClass:$1.Class.CONTEXT_SPECIFIC,type:2,optional:!0,value:[{name:"rsapss.saltLength.saltLength",tagClass:$1.Class.UNIVERSAL,type:$1.Class.INTEGER,constructed:!1,capture:"saltLength"}]},{name:"rsapss.trailerField",tagClass:$1.Class.CONTEXT_SPECIFIC,type:3,optional:!0,value:[{name:"rsapss.trailer.trailer",tagClass:$1.Class.UNIVERSAL,type:$1.Class.INTEGER,constructed:!1,capture:"trailer"}]}]},VG8={name:"CertificationRequestInfo",tagClass:$1.Class.UNIVERSAL,type:$1.Type.SEQUENCE,constructed:!0,captureAsn1:"certificationRequestInfo",value:[{name:"CertificationRequestInfo.integer",tagClass:$1.Class.UNIVERSAL,type:$1.Type.INTEGER,constructed:!1,capture:"certificationRequestInfoVersion"},{name:"CertificationRequestInfo.subject",tagClass:$1.Class.UNIVERSAL,type:$1.Type.SEQUENCE,constructed:!0,captureAsn1:"certificationRequestInfoSubject"},aRB,{name:"CertificationRequestInfo.attributes",tagClass:$1.Class.CONTEXT_SPECIFIC,type:0,constructed:!0,optional:!0,capture:"certificationRequestInfoAttributes",value:[{name:"CertificationRequestInfo.attributes",tagClass:$1.Class.UNIVERSAL,type:$1.Type.SEQUENCE,constructed:!0,value:[{name:"CertificationRequestInfo.attributes.type",tagClass:$1.Class.UNIVERSAL,type:$1.Type.OID,constructed:!1},{name:"CertificationRequestInfo.attributes.value",tagClass:$1.Class.UNIVERSAL,type:$1.Type.SET,constructed:!0}]}]}]},CG8={name:"CertificationRequest",tagClass:$1.Class.UNIVERSAL,type:$1.Type.SEQUENCE,constructed:!0,captureAsn1:"csr",value:[VG8,{name:"CertificationRequest.signatureAlgorithm",tagClass:$1.Class.UNIVERSAL,type:$1.Type.SEQUENCE,constructed:!0,value:[{name:"CertificationRequest.signatureAlgorithm.algorithm",tagClass:$1.Class.UNIVERSAL,type:$1.Type.OID,constructed:!1,capture:"csrSignatureOid"},{name:"CertificationRequest.signatureAlgorithm.parameters",tagClass:$1.Class.UNIVERSAL,optional:!0,captureAsn1:"csrSignatureParams"}]},{name:"CertificationRequest.signature",tagClass:$1.Class.UNIVERSAL,type:$1.Type.BITSTRING,constructed:!1,captureBitStringValue:"csrSignature"}]};z2.RDNAttributesAsArray=function(A,B){var Q=[],D,Z,G;for(var F=0;F<A.value.length;++F){D=A.value[F];for(var I=0;I<D.value.length;++I){if(G={},Z=D.value[I],G.type=$1.derToOid(Z.value[0].value),G.value=Z.value[1].value,G.valueTagClass=Z.value[1].type,G.type in G8){if(G.name=G8[G.type],G.name in CZ)G.shortName=CZ[G.name]}if(B)B.update(G.type),B.update(G.value);Q.push(G)}}return Q};z2.CRIAttributesAsArray=function(A){var B=[];for(var Q=0;Q<A.length;++Q){var D=A[Q],Z=$1.derToOid(D.value[0].value),G=D.value[1].value;for(var F=0;F<G.length;++F){var I={};if(I.type=Z,I.value=G[F].value,I.valueTagClass=G[F].type,I.type in G8){if(I.name=G8[I.type],I.name in CZ)I.shortName=CZ[I.name]}if(I.type===G8.extensionRequest){I.extensions=[];for(var Y=0;Y<I.value.length;++Y)I.extensions.push(z2.certificateExtensionFromAsn1(I.value[Y]))}B.push(I)}}return B};function Pv(A,B){if(typeof B==="string")B={shortName:B};var Q=null,D;for(var Z=0;Q===null&&Z<A.attributes.length;++Z)if(D=A.attributes[Z],B.type&&B.type===D.type)Q=D;else if(B.name&&B.name===D.name)Q=D;else if(B.shortName&&B.shortName===D.shortName)Q=D;return Q}var fv1=function(A,B,Q){var D={};if(A!==G8["RSASSA-PSS"])return D;if(Q)D={hash:{algorithmOid:G8.sha1},mgf:{algorithmOid:G8.mgf1,hash:{algorithmOid:G8.sha1}},saltLength:20};var Z={},G=[];if(!$1.validate(B,XG8,Z,G)){var F=new Error("Cannot read RSASSA-PSS parameter block.");throw F.errors=G,F}if(Z.hashOid!==void 0)D.hash=D.hash||{},D.hash.algorithmOid=$1.derToOid(Z.hashOid);if(Z.maskGenOid!==void 0)D.mgf=D.mgf||{},D.mgf.algorithmOid=$1.derToOid(Z.maskGenOid),D.mgf.hash=D.mgf.hash||{},D.mgf.hash.algorithmOid=$1.derToOid(Z.maskGenHashOid);if(Z.saltLength!==void 0)D.saltLength=Z.saltLength.charCodeAt(0);return D},hv1=function(A){switch(G8[A.signatureOid]){case"sha1WithRSAEncryption":case"sha1WithRSASignature":return u9.md.sha1.create();case"md5WithRSAEncryption":return u9.md.md5.create();case"sha256WithRSAEncryption":return u9.md.sha256.create();case"sha384WithRSAEncryption":return u9.md.sha384.create();case"sha512WithRSAEncryption":return u9.md.sha512.create();case"RSASSA-PSS":return u9.md.sha256.create();default:var B=new Error("Could not compute "+A.type+" digest. Unknown signature OID.");throw B.signatureOid=A.signatureOid,B}},sRB=function(A){var B=A.certificate,Q;switch(B.signatureOid){case G8.sha1WithRSAEncryption:case G8.sha1WithRSASignature:break;case G8["RSASSA-PSS"]:var D,Z;if(D=G8[B.signatureParameters.mgf.hash.algorithmOid],D===void 0||u9.md[D]===void 0){var G=new Error("Unsupported MGF hash function.");throw G.oid=B.signatureParameters.mgf.hash.algorithmOid,G.name=D,G}if(Z=G8[B.signatureParameters.mgf.algorithmOid],Z===void 0||u9.mgf[Z]===void 0){var G=new Error("Unsupported MGF function.");throw G.oid=B.signatureParameters.mgf.algorithmOid,G.name=Z,G}if(Z=u9.mgf[Z].create(u9.md[D].create()),D=G8[B.signatureParameters.hash.algorithmOid],D===void 0||u9.md[D]===void 0){var G=new Error("Unsupported RSASSA-PSS hash function.");throw G.oid=B.signatureParameters.hash.algorithmOid,G.name=D,G}Q=u9.pss.create(u9.md[D].create(),Z,B.signatureParameters.saltLength);break}return B.publicKey.verify(A.md.digest().getBytes(),A.signature,Q)};z2.certificateFromPem=function(A,B,Q){var D=u9.pem.decode(A)[0];if(D.type!=="CERTIFICATE"&&D.type!=="X509 CERTIFICATE"&&D.type!=="TRUSTED CERTIFICATE"){var Z=new Error('Could not convert certificate from PEM; PEM header type is not "CERTIFICATE", "X509 CERTIFICATE", or "TRUSTED CERTIFICATE".');throw Z.headerType=D.type,Z}if(D.procType&&D.procType.type==="ENCRYPTED")throw new Error("Could not convert certificate from PEM; PEM is encrypted.");var G=$1.fromDer(D.body,Q);return z2.certificateFromAsn1(G,B)};z2.certificateToPem=function(A,B){var Q={type:"CERTIFICATE",body:$1.toDer(z2.certificateToAsn1(A)).getBytes()};return u9.pem.encode(Q,{maxline:B})};z2.publicKeyFromPem=function(A){var B=u9.pem.decode(A)[0];if(B.type!=="PUBLIC KEY"&&B.type!=="RSA PUBLIC KEY"){var Q=new Error('Could not convert public key from PEM; PEM header type is not "PUBLIC KEY" or "RSA PUBLIC KEY".');throw Q.headerType=B.type,Q}if(B.procType&&B.procType.type==="ENCRYPTED")throw new Error("Could not convert public key from PEM; PEM is encrypted.");var D=$1.fromDer(B.body);return z2.publicKeyFromAsn1(D)};z2.publicKeyToPem=function(A,B){var Q={type:"PUBLIC KEY",body:$1.toDer(z2.publicKeyToAsn1(A)).getBytes()};return u9.pem.encode(Q,{maxline:B})};z2.publicKeyToRSAPublicKeyPem=function(A,B){var Q={type:"RSA PUBLIC KEY",body:$1.toDer(z2.publicKeyToRSAPublicKey(A)).getBytes()};return u9.pem.encode(Q,{maxline:B})};z2.getPublicKeyFingerprint=function(A,B){B=B||{};var Q=B.md||u9.md.sha1.create(),D=B.type||"RSAPublicKey",Z;switch(D){case"RSAPublicKey":Z=$1.toDer(z2.publicKeyToRSAPublicKey(A)).getBytes();break;case"SubjectPublicKeyInfo":Z=$1.toDer(z2.publicKeyToAsn1(A)).getBytes();break;default:throw new Error('Unknown fingerprint type "'+B.type+'".')}Q.start(),Q.update(Z);var G=Q.digest();if(B.encoding==="hex"){var F=G.toHex();if(B.delimiter)return F.match(/.{2}/g).join(B.delimiter);return F}else if(B.encoding==="binary")return G.getBytes();else if(B.encoding)throw new Error('Unknown encoding "'+B.encoding+'".');return G};z2.certificationRequestFromPem=function(A,B,Q){var D=u9.pem.decode(A)[0];if(D.type!=="CERTIFICATE REQUEST"){var Z=new Error('Could not convert certification request from PEM; PEM header type is not "CERTIFICATE REQUEST".');throw Z.headerType=D.type,Z}if(D.procType&&D.procType.type==="ENCRYPTED")throw new Error("Could not convert certification request from PEM; PEM is encrypted.");var G=$1.fromDer(D.body,Q);return z2.certificationRequestFromAsn1(G,B)};z2.certificationRequestToPem=function(A,B){var Q={type:"CERTIFICATE REQUEST",body:$1.toDer(z2.certificationRequestToAsn1(A)).getBytes()};return u9.pem.encode(Q,{maxline:B})};z2.createCertificate=function(){var A={};return A.version=2,A.serialNumber="00",A.signatureOid=null,A.signature=null,A.siginfo={},A.siginfo.algorithmOid=null,A.validity={},A.validity.notBefore=new Date,A.validity.notAfter=new Date,A.issuer={},A.issuer.getField=function(B){return Pv(A.issuer,B)},A.issuer.addField=function(B){QU([B]),A.issuer.attributes.push(B)},A.issuer.attributes=[],A.issuer.hash=null,A.subject={},A.subject.getField=function(B){return Pv(A.subject,B)},A.subject.addField=function(B){QU([B]),A.subject.attributes.push(B)},A.subject.attributes=[],A.subject.hash=null,A.extensions=[],A.publicKey=null,A.md=null,A.setSubject=function(B,Q){if(QU(B),A.subject.attributes=B,delete A.subject.uniqueId,Q)A.subject.uniqueId=Q;A.subject.hash=null},A.setIssuer=function(B,Q){if(QU(B),A.issuer.attributes=B,delete A.issuer.uniqueId,Q)A.issuer.uniqueId=Q;A.issuer.hash=null},A.setExtensions=function(B){for(var Q=0;Q<B.length;++Q)rRB(B[Q],{cert:A});A.extensions=B},A.getExtension=function(B){if(typeof B==="string")B={name:B};var Q=null,D;for(var Z=0;Q===null&&Z<A.extensions.length;++Z)if(D=A.extensions[Z],B.id&&D.id===B.id)Q=D;else if(B.name&&D.name===B.name)Q=D;return Q},A.sign=function(B,Q){A.md=Q||u9.md.sha1.create();var D=G8[A.md.algorithm+"WithRSAEncryption"];if(!D){var Z=new Error("Could not compute certificate digest. Unknown message digest algorithm OID.");throw Z.algorithm=A.md.algorithm,Z}A.signatureOid=A.siginfo.algorithmOid=D,A.tbsCertificate=z2.getTBSCertificate(A);var G=$1.toDer(A.tbsCertificate);A.md.update(G.getBytes()),A.signature=B.sign(A.md)},A.verify=function(B){var Q=!1;if(!A.issued(B)){var D=B.issuer,Z=A.subject,G=new Error("The parent certificate did not issue the given child certificate; the child certificate's issuer does not match the parent's subject.");throw G.expectedIssuer=Z.attributes,G.actualIssuer=D.attributes,G}var F=B.md;if(F===null){F=hv1({signatureOid:B.signatureOid,type:"certificate"});var I=B.tbsCertificate||z2.getTBSCertificate(B),Y=$1.toDer(I);F.update(Y.getBytes())}if(F!==null)Q=sRB({certificate:A,md:F,signature:B.signature});return Q},A.isIssuer=function(B){var Q=!1,D=A.issuer,Z=B.subject;if(D.hash&&Z.hash)Q=D.hash===Z.hash;else if(D.attributes.length===Z.attributes.length){Q=!0;var G,F;for(var I=0;Q&&I<D.attributes.length;++I)if(G=D.attributes[I],F=Z.attributes[I],G.type!==F.type||G.value!==F.value)Q=!1}return Q},A.issued=function(B){return B.isIssuer(A)},A.generateSubjectKeyIdentifier=function(){return z2.getPublicKeyFingerprint(A.publicKey,{type:"RSAPublicKey"})},A.verifySubjectKeyIdentifier=function(){var B=G8.subjectKeyIdentifier;for(var Q=0;Q<A.extensions.length;++Q){var D=A.extensions[Q];if(D.id===B){var Z=A.generateSubjectKeyIdentifier().getBytes();return u9.util.hexToBytes(D.subjectKeyIdentifier)===Z}}return!1},A};z2.certificateFromAsn1=function(A,B){var Q={},D=[];if(!$1.validate(A,JG8,Q,D)){var Z=new Error("Cannot read X.509 certificate. ASN.1 object is not an X509v3 Certificate.");throw Z.errors=D,Z}var G=$1.derToOid(Q.publicKeyOid);if(G!==z2.oids.rsaEncryption)throw new Error("Cannot read public key. OID is not RSA.");var F=z2.createCertificate();F.version=Q.certVersion?Q.certVersion.charCodeAt(0):0;var I=u9.util.createBuffer(Q.certSerialNumber);F.serialNumber=I.toHex(),F.signatureOid=u9.asn1.derToOid(Q.certSignatureOid),F.signatureParameters=fv1(F.signatureOid,Q.certSignatureParams,!0),F.siginfo.algorithmOid=u9.asn1.derToOid(Q.certinfoSignatureOid),F.siginfo.parameters=fv1(F.siginfo.algorithmOid,Q.certinfoSignatureParams,!1),F.signature=Q.certSignature;var Y=[];if(Q.certValidity1UTCTime!==void 0)Y.push($1.utcTimeToDate(Q.certValidity1UTCTime));if(Q.certValidity2GeneralizedTime!==void 0)Y.push($1.generalizedTimeToDate(Q.certValidity2GeneralizedTime));if(Q.certValidity3UTCTime!==void 0)Y.push($1.utcTimeToDate(Q.certValidity3UTCTime));if(Q.certValidity4GeneralizedTime!==void 0)Y.push($1.generalizedTimeToDate(Q.certValidity4GeneralizedTime));if(Y.length>2)throw new Error("Cannot read notBefore/notAfter validity times; more than two times were provided in the certificate.");if(Y.length<2)throw new Error("Cannot read notBefore/notAfter validity times; they were not provided as either UTCTime or GeneralizedTime.");if(F.validity.notBefore=Y[0],F.validity.notAfter=Y[1],F.tbsCertificate=Q.tbsCertificate,B){F.md=hv1({signatureOid:F.signatureOid,type:"certificate"});var W=$1.toDer(F.tbsCertificate);F.md.update(W.getBytes())}var J=u9.md.sha1.create(),X=$1.toDer(Q.certIssuer);if(J.update(X.getBytes()),F.issuer.getField=function(K){return Pv(F.issuer,K)},F.issuer.addField=function(K){QU([K]),F.issuer.attributes.push(K)},F.issuer.attributes=z2.RDNAttributesAsArray(Q.certIssuer),Q.certIssuerUniqueId)F.issuer.uniqueId=Q.certIssuerUniqueId;F.issuer.hash=J.digest().toHex();var V=u9.md.sha1.create(),C=$1.toDer(Q.certSubject);if(V.update(C.getBytes()),F.subject.getField=function(K){return Pv(F.subject,K)},F.subject.addField=function(K){QU([K]),F.subject.attributes.push(K)},F.subject.attributes=z2.RDNAttributesAsArray(Q.certSubject),Q.certSubjectUniqueId)F.subject.uniqueId=Q.certSubjectUniqueId;if(F.subject.hash=V.digest().toHex(),Q.certExtensions)F.extensions=z2.certificateExtensionsFromAsn1(Q.certExtensions);else F.extensions=[];return F.publicKey=z2.publicKeyFromAsn1(Q.subjectPublicKeyInfo),F};z2.certificateExtensionsFromAsn1=function(A){var B=[];for(var Q=0;Q<A.value.length;++Q){var D=A.value[Q];for(var Z=0;Z<D.value.length;++Z)B.push(z2.certificateExtensionFromAsn1(D.value[Z]))}return B};z2.certificateExtensionFromAsn1=function(A){var B={};if(B.id=$1.derToOid(A.value[0].value),B.critical=!1,A.value[1].type===$1.Type.BOOLEAN)B.critical=A.value[1].value.charCodeAt(0)!==0,B.value=A.value[2].value;else B.value=A.value[1].value;if(B.id in G8){if(B.name=G8[B.id],B.name==="keyUsage"){var Q=$1.fromDer(B.value),D=0,Z=0;if(Q.value.length>1)D=Q.value.charCodeAt(1),Z=Q.value.length>2?Q.value.charCodeAt(2):0;B.digitalSignature=(D&128)===128,B.nonRepudiation=(D&64)===64,B.keyEncipherment=(D&32)===32,B.dataEncipherment=(D&16)===16,B.keyAgreement=(D&8)===8,B.keyCertSign=(D&4)===4,B.cRLSign=(D&2)===2,B.encipherOnly=(D&1)===1,B.decipherOnly=(Z&128)===128}else if(B.name==="basicConstraints"){var Q=$1.fromDer(B.value);if(Q.value.length>0&&Q.value[0].type===$1.Type.BOOLEAN)B.cA=Q.value[0].value.charCodeAt(0)!==0;else B.cA=!1;var G=null;if(Q.value.length>0&&Q.value[0].type===$1.Type.INTEGER)G=Q.value[0].value;else if(Q.value.length>1)G=Q.value[1].value;if(G!==null)B.pathLenConstraint=$1.derToInteger(G)}else if(B.name==="extKeyUsage"){var Q=$1.fromDer(B.value);for(var F=0;F<Q.value.length;++F){var I=$1.derToOid(Q.value[F].value);if(I in G8)B[G8[I]]=!0;else B[I]=!0}}else if(B.name==="nsCertType"){var Q=$1.fromDer(B.value),D=0;if(Q.value.length>1)D=Q.value.charCodeAt(1);B.client=(D&128)===128,B.server=(D&64)===64,B.email=(D&32)===32,B.objsign=(D&16)===16,B.reserved=(D&8)===8,B.sslCA=(D&4)===4,B.emailCA=(D&2)===2,B.objCA=(D&1)===1}else if(B.name==="subjectAltName"||B.name==="issuerAltName"){B.altNames=[];var Y,Q=$1.fromDer(B.value);for(var W=0;W<Q.value.length;++W){Y=Q.value[W];var J={type:Y.type,value:Y.value};switch(B.altNames.push(J),Y.type){case 1:case 2:case 6:break;case 7:J.ip=u9.util.bytesToIP(Y.value);break;case 8:J.oid=$1.derToOid(Y.value);break;default:}}}else if(B.name==="subjectKeyIdentifier"){var Q=$1.fromDer(B.value);B.subjectKeyIdentifier=u9.util.bytesToHex(Q.value)}}return B};z2.certificationRequestFromAsn1=function(A,B){var Q={},D=[];if(!$1.validate(A,CG8,Q,D)){var Z=new Error("Cannot read PKCS#10 certificate request. ASN.1 object is not a PKCS#10 CertificationRequest.");throw Z.errors=D,Z}var G=$1.derToOid(Q.publicKeyOid);if(G!==z2.oids.rsaEncryption)throw new Error("Cannot read public key. OID is not RSA.");var F=z2.createCertificationRequest();if(F.version=Q.csrVersion?Q.csrVersion.charCodeAt(0):0,F.signatureOid=u9.asn1.derToOid(Q.csrSignatureOid),F.signatureParameters=fv1(F.signatureOid,Q.csrSignatureParams,!0),F.siginfo.algorithmOid=u9.asn1.derToOid(Q.csrSignatureOid),F.siginfo.parameters=fv1(F.siginfo.algorithmOid,Q.csrSignatureParams,!1),F.signature=Q.csrSignature,F.certificationRequestInfo=Q.certificationRequestInfo,B){F.md=hv1({signatureOid:F.signatureOid,type:"certification request"});var I=$1.toDer(F.certificationRequestInfo);F.md.update(I.getBytes())}var Y=u9.md.sha1.create();return F.subject.getField=function(W){return Pv(F.subject,W)},F.subject.addField=function(W){QU([W]),F.subject.attributes.push(W)},F.subject.attributes=z2.RDNAttributesAsArray(Q.certificationRequestInfoSubject,Y),F.subject.hash=Y.digest().toHex(),F.publicKey=z2.publicKeyFromAsn1(Q.subjectPublicKeyInfo),F.getAttribute=function(W){return Pv(F,W)},F.addAttribute=function(W){QU([W]),F.attributes.push(W)},F.attributes=z2.CRIAttributesAsArray(Q.certificationRequestInfoAttributes||[]),F};z2.createCertificationRequest=function(){var A={};return A.version=0,A.signatureOid=null,A.signature=null,A.siginfo={},A.siginfo.algorithmOid=null,A.subject={},A.subject.getField=function(B){return Pv(A.subject,B)},A.subject.addField=function(B){QU([B]),A.subject.attributes.push(B)},A.subject.attributes=[],A.subject.hash=null,A.publicKey=null,A.attributes=[],A.getAttribute=function(B){return Pv(A,B)},A.addAttribute=function(B){QU([B]),A.attributes.push(B)},A.md=null,A.setSubject=function(B){QU(B),A.subject.attributes=B,A.subject.hash=null},A.setAttributes=function(B){QU(B),A.attributes=B},A.sign=function(B,Q){A.md=Q||u9.md.sha1.create();var D=G8[A.md.algorithm+"WithRSAEncryption"];if(!D){var Z=new Error("Could not compute certification request digest. Unknown message digest algorithm OID.");throw Z.algorithm=A.md.algorithm,Z}A.signatureOid=A.siginfo.algorithmOid=D,A.certificationRequestInfo=z2.getCertificationRequestInfo(A);var G=$1.toDer(A.certificationRequestInfo);A.md.update(G.getBytes()),A.signature=B.sign(A.md)},A.verify=function(){var B=!1,Q=A.md;if(Q===null){Q=hv1({signatureOid:A.signatureOid,type:"certification request"});var D=A.certificationRequestInfo||z2.getCertificationRequestInfo(A),Z=$1.toDer(D);Q.update(Z.getBytes())}if(Q!==null)B=sRB({certificate:A,md:Q,signature:A.signature});return B},A};function o11(A){var B=$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[]),Q,D,Z=A.attributes;for(var G=0;G<Z.length;++G){Q=Z[G];var F=Q.value,I=$1.Type.PRINTABLESTRING;if("valueTagClass"in Q){if(I=Q.valueTagClass,I===$1.Type.UTF8)F=u9.util.encodeUtf8(F)}D=$1.create($1.Class.UNIVERSAL,$1.Type.SET,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.OID,!1,$1.oidToDer(Q.type).getBytes()),$1.create($1.Class.UNIVERSAL,I,!1,F)])]),B.value.push(D)}return B}function QU(A){var B;for(var Q=0;Q<A.length;++Q){if(B=A[Q],typeof B.name==="undefined"){if(B.type&&B.type in z2.oids)B.name=z2.oids[B.type];else if(B.shortName&&B.shortName in CZ)B.name=z2.oids[CZ[B.shortName]]}if(typeof B.type==="undefined")if(B.name&&B.name in z2.oids)B.type=z2.oids[B.name];else{var D=new Error("Attribute type not specified.");throw D.attribute=B,D}if(typeof B.shortName==="undefined"){if(B.name&&B.name in CZ)B.shortName=CZ[B.name]}if(B.type===G8.extensionRequest){if(B.valueConstructed=!0,B.valueTagClass=$1.Type.SEQUENCE,!B.value&&B.extensions){B.value=[];for(var Z=0;Z<B.extensions.length;++Z)B.value.push(z2.certificateExtensionToAsn1(rRB(B.extensions[Z])))}}if(typeof B.value==="undefined"){var D=new Error("Attribute value not specified.");throw D.attribute=B,D}}}function rRB(A,B){if(B=B||{},typeof A.name==="undefined"){if(A.id&&A.id in z2.oids)A.name=z2.oids[A.id]}if(typeof A.id==="undefined")if(A.name&&A.name in z2.oids)A.id=z2.oids[A.name];else{var Q=new Error("Extension ID not specified.");throw Q.extension=A,Q}if(typeof A.value!=="undefined")return A;if(A.name==="keyUsage"){var D=0,Z=0,G=0;if(A.digitalSignature)Z|=128,D=7;if(A.nonRepudiation)Z|=64,D=6;if(A.keyEncipherment)Z|=32,D=5;if(A.dataEncipherment)Z|=16,D=4;if(A.keyAgreement)Z|=8,D=3;if(A.keyCertSign)Z|=4,D=2;if(A.cRLSign)Z|=2,D=1;if(A.encipherOnly)Z|=1,D=0;if(A.decipherOnly)G|=128,D=7;var F=String.fromCharCode(D);if(G!==0)F+=String.fromCharCode(Z)+String.fromCharCode(G);else if(Z!==0)F+=String.fromCharCode(Z);A.value=$1.create($1.Class.UNIVERSAL,$1.Type.BITSTRING,!1,F)}else if(A.name==="basicConstraints"){if(A.value=$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[]),A.cA)A.value.value.push($1.create($1.Class.UNIVERSAL,$1.Type.BOOLEAN,!1,String.fromCharCode(255)));if("pathLenConstraint"in A)A.value.value.push($1.create($1.Class.UNIVERSAL,$1.Type.INTEGER,!1,$1.integerToDer(A.pathLenConstraint).getBytes()))}else if(A.name==="extKeyUsage"){A.value=$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[]);var I=A.value.value;for(var Y in A){if(A[Y]!==!0)continue;if(Y in G8)I.push($1.create($1.Class.UNIVERSAL,$1.Type.OID,!1,$1.oidToDer(G8[Y]).getBytes()));else if(Y.indexOf(".")!==-1)I.push($1.create($1.Class.UNIVERSAL,$1.Type.OID,!1,$1.oidToDer(Y).getBytes()))}}else if(A.name==="nsCertType"){var D=0,Z=0;if(A.client)Z|=128,D=7;if(A.server)Z|=64,D=6;if(A.email)Z|=32,D=5;if(A.objsign)Z|=16,D=4;if(A.reserved)Z|=8,D=3;if(A.sslCA)Z|=4,D=2;if(A.emailCA)Z|=2,D=1;if(A.objCA)Z|=1,D=0;var F=String.fromCharCode(D);if(Z!==0)F+=String.fromCharCode(Z);A.value=$1.create($1.Class.UNIVERSAL,$1.Type.BITSTRING,!1,F)}else if(A.name==="subjectAltName"||A.name==="issuerAltName"){A.value=$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[]);var W;for(var J=0;J<A.altNames.length;++J){W=A.altNames[J];var F=W.value;if(W.type===7&&W.ip){if(F=u9.util.bytesFromIP(W.ip),F===null){var Q=new Error('Extension "ip" value is not a valid IPv4 or IPv6 address.');throw Q.extension=A,Q}}else if(W.type===8)if(W.oid)F=$1.oidToDer($1.oidToDer(W.oid));else F=$1.oidToDer(F);A.value.value.push($1.create($1.Class.CONTEXT_SPECIFIC,W.type,!1,F))}}else if(A.name==="nsComment"&&B.cert){if(!/^[\x00-\x7F]*$/.test(A.comment)||A.comment.length<1||A.comment.length>128)throw new Error('Invalid "nsComment" content.');A.value=$1.create($1.Class.UNIVERSAL,$1.Type.IA5STRING,!1,A.comment)}else if(A.name==="subjectKeyIdentifier"&&B.cert){var X=B.cert.generateSubjectKeyIdentifier();A.subjectKeyIdentifier=X.toHex(),A.value=$1.create($1.Class.UNIVERSAL,$1.Type.OCTETSTRING,!1,X.getBytes())}else if(A.name==="authorityKeyIdentifier"&&B.cert){A.value=$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[]);var I=A.value.value;if(A.keyIdentifier){var V=A.keyIdentifier===!0?B.cert.generateSubjectKeyIdentifier().getBytes():A.keyIdentifier;I.push($1.create($1.Class.CONTEXT_SPECIFIC,0,!1,V))}if(A.authorityCertIssuer){var C=[$1.create($1.Class.CONTEXT_SPECIFIC,4,!0,[o11(A.authorityCertIssuer===!0?B.cert.issuer:A.authorityCertIssuer)])];I.push($1.create($1.Class.CONTEXT_SPECIFIC,1,!0,C))}if(A.serialNumber){var K=u9.util.hexToBytes(A.serialNumber===!0?B.cert.serialNumber:A.serialNumber);I.push($1.create($1.Class.CONTEXT_SPECIFIC,2,!1,K))}}else if(A.name==="cRLDistributionPoints"){A.value=$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[]);var I=A.value.value,H=$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[]),z=$1.create($1.Class.CONTEXT_SPECIFIC,0,!0,[]),W;for(var J=0;J<A.altNames.length;++J){W=A.altNames[J];var F=W.value;if(W.type===7&&W.ip){if(F=u9.util.bytesFromIP(W.ip),F===null){var Q=new Error('Extension "ip" value is not a valid IPv4 or IPv6 address.');throw Q.extension=A,Q}}else if(W.type===8)if(W.oid)F=$1.oidToDer($1.oidToDer(W.oid));else F=$1.oidToDer(F);z.value.push($1.create($1.Class.CONTEXT_SPECIFIC,W.type,!1,F))}H.value.push($1.create($1.Class.CONTEXT_SPECIFIC,0,!0,[z])),I.push(H)}if(typeof A.value==="undefined"){var Q=new Error("Extension value not specified.");throw Q.extension=A,Q}return A}function Uq0(A,B){switch(A){case G8["RSASSA-PSS"]:var Q=[];if(B.hash.algorithmOid!==void 0)Q.push($1.create($1.Class.CONTEXT_SPECIFIC,0,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.OID,!1,$1.oidToDer(B.hash.algorithmOid).getBytes()),$1.create($1.Class.UNIVERSAL,$1.Type.NULL,!1,"")])]));if(B.mgf.algorithmOid!==void 0)Q.push($1.create($1.Class.CONTEXT_SPECIFIC,1,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.OID,!1,$1.oidToDer(B.mgf.algorithmOid).getBytes()),$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.OID,!1,$1.oidToDer(B.mgf.hash.algorithmOid).getBytes()),$1.create($1.Class.UNIVERSAL,$1.Type.NULL,!1,"")])])]));if(B.saltLength!==void 0)Q.push($1.create($1.Class.CONTEXT_SPECIFIC,2,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.INTEGER,!1,$1.integerToDer(B.saltLength).getBytes())]));return $1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,Q);default:return $1.create($1.Class.UNIVERSAL,$1.Type.NULL,!1,"")}}function KG8(A){var B=$1.create($1.Class.CONTEXT_SPECIFIC,0,!0,[]);if(A.attributes.length===0)return B;var Q=A.attributes;for(var D=0;D<Q.length;++D){var Z=Q[D],G=Z.value,F=$1.Type.UTF8;if("valueTagClass"in Z)F=Z.valueTagClass;if(F===$1.Type.UTF8)G=u9.util.encodeUtf8(G);var I=!1;if("valueConstructed"in Z)I=Z.valueConstructed;var Y=$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.OID,!1,$1.oidToDer(Z.type).getBytes()),$1.create($1.Class.UNIVERSAL,$1.Type.SET,!0,[$1.create($1.Class.UNIVERSAL,F,I,G)])]);B.value.push(Y)}return B}var HG8=new Date("1950-01-01T00:00:00Z"),zG8=new Date("2050-01-01T00:00:00Z");function nRB(A){if(A>=HG8&&A<zG8)return $1.create($1.Class.UNIVERSAL,$1.Type.UTCTIME,!1,$1.dateToUtcTime(A));else return $1.create($1.Class.UNIVERSAL,$1.Type.GENERALIZEDTIME,!1,$1.dateToGeneralizedTime(A))}z2.getTBSCertificate=function(A){var B=nRB(A.validity.notBefore),Q=nRB(A.validity.notAfter),D=$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[$1.create($1.Class.CONTEXT_SPECIFIC,0,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.INTEGER,!1,$1.integerToDer(A.version).getBytes())]),$1.create($1.Class.UNIVERSAL,$1.Type.INTEGER,!1,u9.util.hexToBytes(A.serialNumber)),$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.OID,!1,$1.oidToDer(A.siginfo.algorithmOid).getBytes()),Uq0(A.siginfo.algorithmOid,A.siginfo.parameters)]),o11(A.issuer),$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[B,Q]),o11(A.subject),z2.publicKeyToAsn1(A.publicKey)]);if(A.issuer.uniqueId)D.value.push($1.create($1.Class.CONTEXT_SPECIFIC,1,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.BITSTRING,!1,String.fromCharCode(0)+A.issuer.uniqueId)]));if(A.subject.uniqueId)D.value.push($1.create($1.Class.CONTEXT_SPECIFIC,2,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.BITSTRING,!1,String.fromCharCode(0)+A.subject.uniqueId)]));if(A.extensions.length>0)D.value.push(z2.certificateExtensionsToAsn1(A.extensions));return D};z2.getCertificationRequestInfo=function(A){var B=$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.INTEGER,!1,$1.integerToDer(A.version).getBytes()),o11(A.subject),z2.publicKeyToAsn1(A.publicKey),KG8(A)]);return B};z2.distinguishedNameToAsn1=function(A){return o11(A)};z2.certificateToAsn1=function(A){var B=A.tbsCertificate||z2.getTBSCertificate(A);return $1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[B,$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.OID,!1,$1.oidToDer(A.signatureOid).getBytes()),Uq0(A.signatureOid,A.signatureParameters)]),$1.create($1.Class.UNIVERSAL,$1.Type.BITSTRING,!1,String.fromCharCode(0)+A.signature)])};z2.certificateExtensionsToAsn1=function(A){var B=$1.create($1.Class.CONTEXT_SPECIFIC,3,!0,[]),Q=$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[]);B.value.push(Q);for(var D=0;D<A.length;++D)Q.value.push(z2.certificateExtensionToAsn1(A[D]));return B};z2.certificateExtensionToAsn1=function(A){var B=$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[]);if(B.value.push($1.create($1.Class.UNIVERSAL,$1.Type.OID,!1,$1.oidToDer(A.id).getBytes())),A.critical)B.value.push($1.create($1.Class.UNIVERSAL,$1.Type.BOOLEAN,!1,String.fromCharCode(255)));var Q=A.value;if(typeof A.value!=="string")Q=$1.toDer(Q).getBytes();return B.value.push($1.create($1.Class.UNIVERSAL,$1.Type.OCTETSTRING,!1,Q)),B};z2.certificationRequestToAsn1=function(A){var B=A.certificationRequestInfo||z2.getCertificationRequestInfo(A);return $1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[B,$1.create($1.Class.UNIVERSAL,$1.Type.SEQUENCE,!0,[$1.create($1.Class.UNIVERSAL,$1.Type.OID,!1,$1.oidToDer(A.signatureOid).getBytes()),Uq0(A.signatureOid,A.signatureParameters)]),$1.create($1.Class.UNIVERSAL,$1.Type.BITSTRING,!1,String.fromCharCode(0)+A.signature)])};z2.createCaStore=function(A){var B={certs:{}};B.getIssuer=function(F){var I=Q(F.issuer);return I},B.addCertificate=function(F){if(typeof F==="string")F=u9.pki.certificateFromPem(F);if(D(F.subject),!B.hasCertificate(F))if(F.subject.hash in B.certs){var I=B.certs[F.subject.hash];if(!u9.util.isArray(I))I=[I];I.push(F),B.certs[F.subject.hash]=I}else B.certs[F.subject.hash]=F},B.hasCertificate=function(F){if(typeof F==="string")F=u9.pki.certificateFromPem(F);var I=Q(F.subject);if(!I)return!1;if(!u9.util.isArray(I))I=[I];var Y=$1.toDer(z2.certificateToAsn1(F)).getBytes();for(var W=0;W<I.length;++W){var J=$1.toDer(z2.certificateToAsn1(I[W])).getBytes();if(Y===J)return!0}return!1},B.listAllCertificates=function(){var F=[];for(var I in B.certs)if(B.certs.hasOwnProperty(I)){var Y=B.certs[I];if(!u9.util.isArray(Y))F.push(Y);else for(var W=0;W<Y.length;++W)F.push(Y[W])}return F},B.removeCertificate=function(F){var I;if(typeof F==="string")F=u9.pki.certificateFromPem(F);if(D(F.subject),!B.hasCertificate(F))return null;var Y=Q(F.subject);if(!u9.util.isArray(Y))return I=B.certs[F.subject.hash],delete B.certs[F.subject.hash],I;var W=$1.toDer(z2.certificateToAsn1(F)).getBytes();for(var J=0;J<Y.length;++J){var X=$1.toDer(z2.certificateToAsn1(Y[J])).getBytes();if(W===X)I=Y[J],Y.splice(J,1)}if(Y.length===0)delete B.certs[F.subject.hash];return I};function Q(F){return D(F),B.certs[F.hash]||null}function D(F){if(!F.hash){var I=u9.md.sha1.create();F.attributes=z2.RDNAttributesAsArray(o11(F),I),F.hash=I.digest().toHex()}}if(A)for(var Z=0;Z<A.length;++Z){var G=A[Z];B.addCertificate(G)}return B};z2.certificateError={bad_certificate:"forge.pki.BadCertificate",unsupported_certificate:"forge.pki.UnsupportedCertificate",certificate_revoked:"forge.pki.CertificateRevoked",certificate_expired:"forge.pki.CertificateExpired",certificate_unknown:"forge.pki.CertificateUnknown",unknown_ca:"forge.pki.UnknownCertificateAuthority"};z2.verifyCertificateChain=function(A,B,Q){if(typeof Q==="function")Q={verify:Q};Q=Q||{},B=B.slice(0);var D=B.slice(0),Z=Q.validityCheckDate;if(typeof Z==="undefined")Z=new Date;var G=!0,F=null,I=0;do{var Y=B.shift(),W=null,J=!1;if(Z){if(Z<Y.validity.notBefore||Z>Y.validity.notAfter)F={message:"Certificate is not valid yet or has expired.",error:z2.certificateError.certificate_expired,notBefore:Y.validity.notBefore,notAfter:Y.validity.notAfter,now:Z}}if(F===null){if(W=B[0]||A.getIssuer(Y),W===null){if(Y.isIssuer(Y))J=!0,W=Y}if(W){var X=W;if(!u9.util.isArray(X))X=[X];var V=!1;while(!V&&X.length>0){W=X.shift();try{V=W.verify(Y)}catch(R){}}if(!V)F={message:"Certificate signature is invalid.",error:z2.certificateError.bad_certificate}}if(F===null&&(!W||J)&&!A.hasCertificate(Y))F={message:"Certificate is not trusted.",error:z2.certificateError.unknown_ca}}if(F===null&&W&&!Y.isIssuer(W))F={message:"Certificate issuer is invalid.",error:z2.certificateError.bad_certificate};if(F===null){var C={keyUsage:!0,basicConstraints:!0};for(var K=0;F===null&&K<Y.extensions.length;++K){var H=Y.extensions[K];if(H.critical&&!(H.name in C))F={message:"Certificate has an unsupported critical extension.",error:z2.certificateError.unsupported_certificate}}}if(F===null&&(!G||B.length===0&&(!W||J))){var z=Y.getExtension("basicConstraints"),$=Y.getExtension("keyUsage");if($!==null){if(!$.keyCertSign||z===null)F={message:"Certificate keyUsage or basicConstraints conflict or indicate that the certificate is not a CA. If the certificate is the only one in the chain or isn't the first then the certificate must be a valid CA.",error:z2.certificateError.bad_certificate}}if(F===null&&z!==null&&!z.cA)F={message:"Certificate basicConstraints indicates the certificate is not a CA.",error:z2.certificateError.bad_certificate};if(F===null&&$!==null&&"pathLenConstraint"in z){var L=I-1;if(L>z.pathLenConstraint)F={message:"Certificate basicConstraints pathLenConstraint violated.",error:z2.certificateError.bad_certificate}}}var N=F===null?!0:F.error,O=Q.verify?Q.verify(N,I,D):N;if(O===!0)F=null;else{if(N===!0)F={message:"The application rejected the certificate.",error:z2.certificateError.bad_certificate};if(O||O===0){if(typeof O==="object"&&!u9.util.isArray(O)){if(O.message)F.message=O.message;if(O.error)F.error=O.error}else if(typeof O==="string")F.error=O}throw F}G=!1,++I}while(B.length>0);return!0}});
var j$=E((XZ3,aMB)=>{var R7=M4();N8();Rv();var a2=aMB.exports=R7.asn1=R7.asn1||{};a2.Class={UNIVERSAL:0,APPLICATION:64,CONTEXT_SPECIFIC:128,PRIVATE:192};a2.Type={NONE:0,BOOLEAN:1,INTEGER:2,BITSTRING:3,OCTETSTRING:4,NULL:5,OID:6,ODESC:7,EXTERNAL:8,REAL:9,ENUMERATED:10,EMBEDDED:11,UTF8:12,ROID:13,SEQUENCE:16,SET:17,PRINTABLESTRING:19,IA5STRING:22,UTCTIME:23,GENERALIZEDTIME:24,BMPSTRING:30};a2.create=function(A,B,Q,D,Z){if(R7.util.isArray(D)){var G=[];for(var F=0;F<D.length;++F)if(D[F]!==void 0)G.push(D[F]);D=G}var I={tagClass:A,type:B,constructed:Q,composed:Q||R7.util.isArray(D),value:D};if(Z&&"bitStringContents"in Z)I.bitStringContents=Z.bitStringContents,I.original=a2.copy(I);return I};a2.copy=function(A,B){var Q;if(R7.util.isArray(A)){Q=[];for(var D=0;D<A.length;++D)Q.push(a2.copy(A[D],B));return Q}if(typeof A==="string")return A;if(Q={tagClass:A.tagClass,type:A.type,constructed:A.constructed,composed:A.composed,value:a2.copy(A.value,B)},B&&!B.excludeBitStringContents)Q.bitStringContents=A.bitStringContents;return Q};a2.equals=function(A,B,Q){if(R7.util.isArray(A)){if(!R7.util.isArray(B))return!1;if(A.length!==B.length)return!1;for(var D=0;D<A.length;++D)if(!a2.equals(A[D],B[D]))return!1;return!0}if(typeof A!==typeof B)return!1;if(typeof A==="string")return A===B;var Z=A.tagClass===B.tagClass&&A.type===B.type&&A.constructed===B.constructed&&A.composed===B.composed&&a2.equals(A.value,B.value);if(Q&&Q.includeBitStringContents)Z=Z&&A.bitStringContents===B.bitStringContents;return Z};a2.getBerValueLength=function(A){var B=A.getByte();if(B===128)return;var Q,D=B&128;if(!D)Q=B;else Q=A.getInt((B&127)<<3);return Q};function wG1(A,B,Q){if(Q>B){var D=new Error("Too few bytes to parse DER.");throw D.available=A.length(),D.remaining=B,D.requested=Q,D}}var c78=function(A,B){var Q=A.getByte();if(B--,Q===128)return;var D,Z=Q&128;if(!Z)D=Q;else{var G=Q&127;wG1(A,B,G),D=A.getInt(G<<3)}if(D<0)throw new Error("Negative length: "+D);return D};a2.fromDer=function(A,B){if(B===void 0)B={strict:!0,parseAllBytes:!0,decodeBitStrings:!0};if(typeof B==="boolean")B={strict:B,parseAllBytes:!0,decodeBitStrings:!0};if(!("strict"in B))B.strict=!0;if(!("parseAllBytes"in B))B.parseAllBytes=!0;if(!("decodeBitStrings"in B))B.decodeBitStrings=!0;if(typeof A==="string")A=R7.util.createBuffer(A);var Q=A.length(),D=Nv1(A,A.length(),0,B);if(B.parseAllBytes&&A.length()!==0){var Z=new Error("Unparsed DER bytes remain after ASN.1 parsing.");throw Z.byteCount=Q,Z.remaining=A.length(),Z}return D};function Nv1(A,B,Q,D){var Z;wG1(A,B,2);var G=A.getByte();B--;var F=G&192,I=G&31;Z=A.length();var Y=c78(A,B);if(B-=Z-A.length(),Y!==void 0&&Y>B){if(D.strict){var W=new Error("Too few bytes to read ASN.1 value.");throw W.available=A.length(),W.remaining=B,W.requested=Y,W}Y=B}var J,X,V=(G&32)===32;if(V)if(J=[],Y===void 0)for(;;){if(wG1(A,B,2),A.bytes(2)===String.fromCharCode(0,0)){A.getBytes(2),B-=2;break}Z=A.length(),J.push(Nv1(A,B,Q+1,D)),B-=Z-A.length()}else while(Y>0)Z=A.length(),J.push(Nv1(A,Y,Q+1,D)),B-=Z-A.length(),Y-=Z-A.length();if(J===void 0&&F===a2.Class.UNIVERSAL&&I===a2.Type.BITSTRING)X=A.bytes(Y);if(J===void 0&&D.decodeBitStrings&&F===a2.Class.UNIVERSAL&&I===a2.Type.BITSTRING&&Y>1){var C=A.read,K=B,H=0;if(I===a2.Type.BITSTRING)wG1(A,B,1),H=A.getByte(),B--;if(H===0)try{Z=A.length();var z={strict:!0,decodeBitStrings:!0},$=Nv1(A,B,Q+1,z),L=Z-A.length();if(B-=L,I==a2.Type.BITSTRING)L++;var N=$.tagClass;if(L===Y&&(N===a2.Class.UNIVERSAL||N===a2.Class.CONTEXT_SPECIFIC))J=[$]}catch(R){}if(J===void 0)A.read=C,B=K}if(J===void 0){if(Y===void 0){if(D.strict)throw new Error("Non-constructed ASN.1 object of indefinite length.");Y=B}if(I===a2.Type.BMPSTRING){J="";for(;Y>0;Y-=2)wG1(A,B,2),J+=String.fromCharCode(A.getInt16()),B-=2}else J=A.getBytes(Y),B-=Y}var O=X===void 0?null:{bitStringContents:X};return a2.create(F,I,V,J,O)}a2.toDer=function(A){var B=R7.util.createBuffer(),Q=A.tagClass|A.type,D=R7.util.createBuffer(),Z=!1;if("bitStringContents"in A){if(Z=!0,A.original)Z=a2.equals(A,A.original)}if(Z)D.putBytes(A.bitStringContents);else if(A.composed){if(A.constructed)Q|=32;else D.putByte(0);for(var G=0;G<A.value.length;++G)if(A.value[G]!==void 0)D.putBuffer(a2.toDer(A.value[G]))}else if(A.type===a2.Type.BMPSTRING)for(var G=0;G<A.value.length;++G)D.putInt16(A.value.charCodeAt(G));else if(A.type===a2.Type.INTEGER&&A.value.length>1&&(A.value.charCodeAt(0)===0&&(A.value.charCodeAt(1)&128)===0||A.value.charCodeAt(0)===255&&(A.value.charCodeAt(1)&128)===128))D.putBytes(A.value.substr(1));else D.putBytes(A.value);if(B.putByte(Q),D.length()<=127)B.putByte(D.length()&127);else{var F=D.length(),I="";do I+=String.fromCharCode(F&255),F=F>>>8;while(F>0);B.putByte(I.length|128);for(var G=I.length-1;G>=0;--G)B.putByte(I.charCodeAt(G))}return B.putBuffer(D),B};a2.oidToDer=function(A){var B=A.split("."),Q=R7.util.createBuffer();Q.putByte(40*parseInt(B[0],10)+parseInt(B[1],10));var D,Z,G,F;for(var I=2;I<B.length;++I){D=!0,Z=[],G=parseInt(B[I],10);do{if(F=G&127,G=G>>>7,!D)F|=128;Z.push(F),D=!1}while(G>0);for(var Y=Z.length-1;Y>=0;--Y)Q.putByte(Z[Y])}return Q};a2.derToOid=function(A){var B;if(typeof A==="string")A=R7.util.createBuffer(A);var Q=A.getByte();B=Math.floor(Q/40)+"."+Q%40;var D=0;while(A.length()>0)if(Q=A.getByte(),D=D<<7,Q&128)D+=Q&127;else B+="."+(D+Q),D=0;return B};a2.utcTimeToDate=function(A){var B=new Date,Q=parseInt(A.substr(0,2),10);Q=Q>=50?1900+Q:2000+Q;var D=parseInt(A.substr(2,2),10)-1,Z=parseInt(A.substr(4,2),10),G=parseInt(A.substr(6,2),10),F=parseInt(A.substr(8,2),10),I=0;if(A.length>11){var Y=A.charAt(10),W=10;if(Y!=="+"&&Y!=="-")I=parseInt(A.substr(10,2),10),W+=2}if(B.setUTCFullYear(Q,D,Z),B.setUTCHours(G,F,I,0),W){if(Y=A.charAt(W),Y==="+"||Y==="-"){var J=parseInt(A.substr(W+1,2),10),X=parseInt(A.substr(W+4,2),10),V=J*60+X;if(V*=60000,Y==="+")B.setTime(+B-V);else B.setTime(+B+V)}}return B};a2.generalizedTimeToDate=function(A){var B=new Date,Q=parseInt(A.substr(0,4),10),D=parseInt(A.substr(4,2),10)-1,Z=parseInt(A.substr(6,2),10),G=parseInt(A.substr(8,2),10),F=parseInt(A.substr(10,2),10),I=parseInt(A.substr(12,2),10),Y=0,W=0,J=!1;if(A.charAt(A.length-1)==="Z")J=!0;var X=A.length-5,V=A.charAt(X);if(V==="+"||V==="-"){var C=parseInt(A.substr(X+1,2),10),K=parseInt(A.substr(X+4,2),10);if(W=C*60+K,W*=60000,V==="+")W*=-1;J=!0}if(A.charAt(14)===".")Y=parseFloat(A.substr(14),10)*1000;if(J)B.setUTCFullYear(Q,D,Z),B.setUTCHours(G,F,I,Y),B.setTime(+B+W);else B.setFullYear(Q,D,Z),B.setHours(G,F,I,Y);return B};a2.dateToUtcTime=function(A){if(typeof A==="string")return A;var B="",Q=[];Q.push((""+A.getUTCFullYear()).substr(2)),Q.push(""+(A.getUTCMonth()+1)),Q.push(""+A.getUTCDate()),Q.push(""+A.getUTCHours()),Q.push(""+A.getUTCMinutes()),Q.push(""+A.getUTCSeconds());for(var D=0;D<Q.length;++D){if(Q[D].length<2)B+="0";B+=Q[D]}return B+="Z",B};a2.dateToGeneralizedTime=function(A){if(typeof A==="string")return A;var B="",Q=[];Q.push(""+A.getUTCFullYear()),Q.push(""+(A.getUTCMonth()+1)),Q.push(""+A.getUTCDate()),Q.push(""+A.getUTCHours()),Q.push(""+A.getUTCMinutes()),Q.push(""+A.getUTCSeconds());for(var D=0;D<Q.length;++D){if(Q[D].length<2)B+="0";B+=Q[D]}return B+="Z",B};a2.integerToDer=function(A){var B=R7.util.createBuffer();if(A>=-128&&A<128)return B.putSignedInt(A,8);if(A>=-32768&&A<32768)return B.putSignedInt(A,16);if(A>=-8388608&&A<8388608)return B.putSignedInt(A,24);if(A>=-2147483648&&A<2147483648)return B.putSignedInt(A,32);var Q=new Error("Integer too large; max is 32-bits.");throw Q.integer=A,Q};a2.derToInteger=function(A){if(typeof A==="string")A=R7.util.createBuffer(A);var B=A.length()*8;if(B>32)throw new Error("Integer too large; max is 32-bits.");return A.getSignedInt(B)};a2.validate=function(A,B,Q,D){var Z=!1;if((A.tagClass===B.tagClass||typeof B.tagClass==="undefined")&&(A.type===B.type||typeof B.type==="undefined")){if(A.constructed===B.constructed||typeof B.constructed==="undefined"){if(Z=!0,B.value&&R7.util.isArray(B.value)){var G=0;for(var F=0;Z&&F<B.value.length;++F){if(Z=B.value[F].optional||!1,A.value[G]){if(Z=a2.validate(A.value[G],B.value[F],Q,D),Z)++G;else if(B.value[F].optional)Z=!0}if(!Z&&D)D.push("["+B.name+'] Tag class "'+B.tagClass+'", type "'+B.type+'" expected value length "'+B.value.length+'", got "'+A.value.length+'"')}}if(Z&&Q){if(B.capture)Q[B.capture]=A.value;if(B.captureAsn1)Q[B.captureAsn1]=A;if(B.captureBitStringContents&&"bitStringContents"in A)Q[B.captureBitStringContents]=A.bitStringContents;if(B.captureBitStringValue&&"bitStringContents"in A){var I;if(A.bitStringContents.length<2)Q[B.captureBitStringValue]="";else{var Y=A.bitStringContents.charCodeAt(0);if(Y!==0)throw new Error("captureBitStringValue only supported for zero unused bits");Q[B.captureBitStringValue]=A.bitStringContents.slice(1)}}}}else if(D)D.push("["+B.name+'] Expected constructed "'+B.constructed+'", got "'+A.constructed+'"')}else if(D){if(A.tagClass!==B.tagClass)D.push("["+B.name+'] Expected tag class "'+B.tagClass+'", got "'+A.tagClass+'"');if(A.type!==B.type)D.push("["+B.name+'] Expected type "'+B.type+'", got "'+A.type+'"')}return Z};var nMB=/[^\\u0000-\\u00ff]/;a2.prettyPrint=function(A,B,Q){var D="";if(B=B||0,Q=Q||2,B>0)D+=`
`;var Z="";for(var G=0;G<B*Q;++G)Z+=" ";switch(D+=Z+"Tag: ",A.tagClass){case a2.Class.UNIVERSAL:D+="Universal:";break;case a2.Class.APPLICATION:D+="Application:";break;case a2.Class.CONTEXT_SPECIFIC:D+="Context-Specific:";break;case a2.Class.PRIVATE:D+="Private:";break}if(A.tagClass===a2.Class.UNIVERSAL)switch(D+=A.type,A.type){case a2.Type.NONE:D+=" (None)";break;case a2.Type.BOOLEAN:D+=" (Boolean)";break;case a2.Type.INTEGER:D+=" (Integer)";break;case a2.Type.BITSTRING:D+=" (Bit string)";break;case a2.Type.OCTETSTRING:D+=" (Octet string)";break;case a2.Type.NULL:D+=" (Null)";break;case a2.Type.OID:D+=" (Object Identifier)";break;case a2.Type.ODESC:D+=" (Object Descriptor)";break;case a2.Type.EXTERNAL:D+=" (External or Instance of)";break;case a2.Type.REAL:D+=" (Real)";break;case a2.Type.ENUMERATED:D+=" (Enumerated)";break;case a2.Type.EMBEDDED:D+=" (Embedded PDV)";break;case a2.Type.UTF8:D+=" (UTF8)";break;case a2.Type.ROID:D+=" (Relative Object Identifier)";break;case a2.Type.SEQUENCE:D+=" (Sequence)";break;case a2.Type.SET:D+=" (Set)";break;case a2.Type.PRINTABLESTRING:D+=" (Printable String)";break;case a2.Type.IA5String:D+=" (IA5String (ASCII))";break;case a2.Type.UTCTIME:D+=" (UTC time)";break;case a2.Type.GENERALIZEDTIME:D+=" (Generalized time)";break;case a2.Type.BMPSTRING:D+=" (BMP String)";break}else D+=A.type;if(D+=`
`,D+=Z+"Constructed: "+A.constructed+`
`,A.composed){var F=0,I="";for(var G=0;G<A.value.length;++G)if(A.value[G]!==void 0){if(F+=1,I+=a2.prettyPrint(A.value[G],B+1,Q),G+1<A.value.length)I+=","}D+=Z+"Sub values: "+F+I}else{if(D+=Z+"Value: ",A.type===a2.Type.OID){var Y=a2.derToOid(A.value);if(D+=Y,R7.pki&&R7.pki.oids){if(Y in R7.pki.oids)D+=" ("+R7.pki.oids[Y]+") "}}if(A.type===a2.Type.INTEGER)try{D+=a2.derToInteger(A.value)}catch(J){D+="0x"+R7.util.bytesToHex(A.value)}else if(A.type===a2.Type.BITSTRING){if(A.value.length>1)D+="0x"+R7.util.bytesToHex(A.value.slice(1));else D+="(none)";if(A.value.length>0){var W=A.value.charCodeAt(0);if(W==1)D+=" (1 unused bit shown)";else if(W>1)D+=" ("+W+" unused bits shown)"}}else if(A.type===a2.Type.OCTETSTRING){if(!nMB.test(A.value))D+="("+A.value+") ";D+="0x"+R7.util.bytesToHex(A.value)}else if(A.type===a2.Type.UTF8)try{D+=R7.util.decodeUtf8(A.value)}catch(J){if(J.message==="URI malformed")D+="0x"+R7.util.bytesToHex(A.value)+" (malformed UTF8)";else throw J}else if(A.type===a2.Type.PRINTABLESTRING||A.type===a2.Type.IA5String)D+=A.value;else if(nMB.test(A.value))D+="0x"+R7.util.bytesToHex(A.value);else if(A.value.length===0)D+="[null]";else D+=A.value}return D}});
var kq0=E((fZ3,HOB)=>{var O7=M4();bM();N8();var PG1=HOB.exports=O7.sha512=O7.sha512||{};O7.md.sha512=O7.md.algorithms.sha512=PG1;var COB=O7.sha384=O7.sha512.sha384=O7.sha512.sha384||{};COB.create=function(){return PG1.create("SHA-384")};O7.md.sha384=O7.md.algorithms.sha384=COB;O7.sha512.sha256=O7.sha512.sha256||{create:function(){return PG1.create("SHA-512/256")}};O7.md["sha512/256"]=O7.md.algorithms["sha512/256"]=O7.sha512.sha256;O7.sha512.sha224=O7.sha512.sha224||{create:function(){return PG1.create("SHA-512/224")}};O7.md["sha512/224"]=O7.md.algorithms["sha512/224"]=O7.sha512.sha224;PG1.create=function(A){if(!KOB)rG8();if(typeof A==="undefined")A="SHA-512";if(!(A in Ad))throw new Error("Invalid SHA-512 algorithm: "+A);var B=Ad[A],Q=null,D=O7.util.createBuffer(),Z=new Array(80);for(var G=0;G<80;++G)Z[G]=new Array(2);var F=64;switch(A){case"SHA-384":F=48;break;case"SHA-512/256":F=32;break;case"SHA-512/224":F=28;break}var I={algorithm:A.replace("-","").toLowerCase(),blockLength:128,digestLength:F,messageLength:0,fullMessageLength:null,messageLengthSize:16};return I.start=function(){I.messageLength=0,I.fullMessageLength=I.messageLength128=[];var Y=I.messageLengthSize/4;for(var W=0;W<Y;++W)I.fullMessageLength.push(0);D=O7.util.createBuffer(),Q=new Array(B.length);for(var W=0;W<B.length;++W)Q[W]=B[W].slice(0);return I},I.start(),I.update=function(Y,W){if(W==="utf8")Y=O7.util.encodeUtf8(Y);var J=Y.length;I.messageLength+=J,J=[J/4294967296>>>0,J>>>0];for(var X=I.fullMessageLength.length-1;X>=0;--X)I.fullMessageLength[X]+=J[1],J[1]=J[0]+(I.fullMessageLength[X]/4294967296>>>0),I.fullMessageLength[X]=I.fullMessageLength[X]>>>0,J[0]=J[1]/4294967296>>>0;if(D.putBytes(Y),VOB(Q,Z,D),D.read>2048||D.length()===0)D.compact();return I},I.digest=function(){var Y=O7.util.createBuffer();Y.putBytes(D.bytes());var W=I.fullMessageLength[I.fullMessageLength.length-1]+I.messageLengthSize,J=W&I.blockLength-1;Y.putBytes(Sq0.substr(0,I.blockLength-J));var X,V,C=I.fullMessageLength[0]*8;for(var K=0;K<I.fullMessageLength.length-1;++K)X=I.fullMessageLength[K+1]*8,V=X/4294967296>>>0,C+=V,Y.putInt32(C>>>0),C=X>>>0;Y.putInt32(C);var H=new Array(Q.length);for(var K=0;K<Q.length;++K)H[K]=Q[K].slice(0);VOB(H,Z,Y);var z=O7.util.createBuffer(),$;if(A==="SHA-512")$=H.length;else if(A==="SHA-384")$=H.length-2;else $=H.length-4;for(var K=0;K<$;++K)if(z.putInt32(H[K][0]),K!==$-1||A!=="SHA-512/224")z.putInt32(H[K][1]);return z},I};var Sq0=null,KOB=!1,jq0=null,Ad=null;function rG8(){Sq0=String.fromCharCode(128),Sq0+=O7.util.fillString(String.fromCharCode(0),128),jq0=[[1116352408,3609767458],[1899447441,602891725],[3049323471,3964484399],[3921009573,2173295548],[961987163,4081628472],[1508970993,3053834265],[2453635748,2937671579],[2870763221,3664609560],[3624381080,2734883394],[310598401,1164996542],[607225278,1323610764],[1426881987,3590304994],[1925078388,4068182383],[2162078206,991336113],[2614888103,633803317],[3248222580,3479774868],[3835390401,2666613458],[4022224774,944711139],[264347078,2341262773],[604807628,2007800933],[770255983,1495990901],[1249150122,1856431235],[1555081692,3175218132],[1996064986,2198950837],[2554220882,3999719339],[2821834349,766784016],[2952996808,2566594879],[3210313671,3203337956],[3336571891,1034457026],[3584528711,2466948901],[113926993,3758326383],[338241895,168717936],[666307205,1188179964],[773529912,1546045734],[1294757372,1522805485],[1396182291,2643833823],[1695183700,2343527390],[1986661051,1014477480],[2177026350,1206759142],[2456956037,344077627],[2730485921,1290863460],[2820302411,3158454273],[3259730800,3505952657],[3345764771,106217008],[3516065817,3606008344],[3600352804,1432725776],[4094571909,1467031594],[275423344,851169720],[430227734,3100823752],[506948616,1363258195],[659060556,3750685593],[883997877,3785050280],[958139571,3318307427],[1322822218,3812723403],[1537002063,2003034995],[1747873779,3602036899],[1955562222,1575990012],[2024104815,1125592928],[2227730452,2716904306],[2361852424,442776044],[2428436474,593698344],[2756734187,3733110249],[3204031479,2999351573],[3329325298,3815920427],[3391569614,3928383900],[3515267271,566280711],[3940187606,3454069534],[4118630271,4000239992],[116418474,1914138554],[174292421,2731055270],[289380356,3203993006],[460393269,320620315],[685471733,587496836],[852142971,1086792851],[1017036298,365543100],[1126000580,2618297676],[1288033470,3409855158],[1501505948,4234509866],[1607167915,987167468],[1816402316,1246189591]],Ad={},Ad["SHA-512"]=[[1779033703,4089235720],[3144134277,2227873595],[1013904242,4271175723],[2773480762,1595750129],[1359893119,2917565137],[2600822924,725511199],[528734635,4215389547],[1541459225,327033209]],Ad["SHA-384"]=[[3418070365,3238371032],[1654270250,914150663],[2438529370,812702999],[355462360,4144912697],[1731405415,4290775857],[2394180231,1750603025],[3675008525,1694076839],[1203062813,3204075428]],Ad["SHA-512/256"]=[[573645204,4230739756],[2673172387,3360449730],[596883563,1867755857],[2520282905,1497426621],[2519219938,2827943907],[3193839141,1401305490],[721525244,746961066],[246885852,2177182882]],Ad["SHA-512/224"]=[[2352822216,424955298],[1944164710,2312950998],[502970286,855612546],[1738396948,1479516111],[258812777,2077511080],[2011393907,79989058],[1067287976,1780299464],[286451373,2446758561]],KOB=!0}function VOB(A,B,Q){var D,Z,G,F,I,Y,W,J,X,V,C,K,H,z,$,L,N,O,R,T,j,f,k,c,h,n,a,x,e,W1,U1,y1,W0,F0,g1,K1=Q.length();while(K1>=128){for(e=0;e<16;++e)B[e][0]=Q.getInt32()>>>0,B[e][1]=Q.getInt32()>>>0;for(;e<80;++e)y1=B[e-2],W1=y1[0],U1=y1[1],D=((W1>>>19|U1<<13)^(U1>>>29|W1<<3)^W1>>>6)>>>0,Z=((W1<<13|U1>>>19)^(U1<<3|W1>>>29)^(W1<<26|U1>>>6))>>>0,F0=B[e-15],W1=F0[0],U1=F0[1],G=((W1>>>1|U1<<31)^(W1>>>8|U1<<24)^W1>>>7)>>>0,F=((W1<<31|U1>>>1)^(W1<<24|U1>>>8)^(W1<<25|U1>>>7))>>>0,W0=B[e-7],g1=B[e-16],U1=Z+W0[1]+F+g1[1],B[e][0]=D+W0[0]+G+g1[0]+(U1/4294967296>>>0)>>>0,B[e][1]=U1>>>0;H=A[0][0],z=A[0][1],$=A[1][0],L=A[1][1],N=A[2][0],O=A[2][1],R=A[3][0],T=A[3][1],j=A[4][0],f=A[4][1],k=A[5][0],c=A[5][1],h=A[6][0],n=A[6][1],a=A[7][0],x=A[7][1];for(e=0;e<80;++e)W=((j>>>14|f<<18)^(j>>>18|f<<14)^(f>>>9|j<<23))>>>0,J=((j<<18|f>>>14)^(j<<14|f>>>18)^(f<<23|j>>>9))>>>0,X=(h^j&(k^h))>>>0,V=(n^f&(c^n))>>>0,I=((H>>>28|z<<4)^(z>>>2|H<<30)^(z>>>7|H<<25))>>>0,Y=((H<<4|z>>>28)^(z<<30|H>>>2)^(z<<25|H>>>7))>>>0,C=(H&$|N&(H^$))>>>0,K=(z&L|O&(z^L))>>>0,U1=x+J+V+jq0[e][1]+B[e][1],D=a+W+X+jq0[e][0]+B[e][0]+(U1/4294967296>>>0)>>>0,Z=U1>>>0,U1=Y+K,G=I+C+(U1/4294967296>>>0)>>>0,F=U1>>>0,a=h,x=n,h=k,n=c,k=j,c=f,U1=T+Z,j=R+D+(U1/4294967296>>>0)>>>0,f=U1>>>0,R=N,T=O,N=$,O=L,$=H,L=z,U1=Z+F,H=D+G+(U1/4294967296>>>0)>>>0,z=U1>>>0;U1=A[0][1]+z,A[0][0]=A[0][0]+H+(U1/4294967296>>>0)>>>0,A[0][1]=U1>>>0,U1=A[1][1]+L,A[1][0]=A[1][0]+$+(U1/4294967296>>>0)>>>0,A[1][1]=U1>>>0,U1=A[2][1]+O,A[2][0]=A[2][0]+N+(U1/4294967296>>>0)>>>0,A[2][1]=U1>>>0,U1=A[3][1]+T,A[3][0]=A[3][0]+R+(U1/4294967296>>>0)>>>0,A[3][1]=U1>>>0,U1=A[4][1]+f,A[4][0]=A[4][0]+j+(U1/4294967296>>>0)>>>0,A[4][1]=U1>>>0,U1=A[5][1]+c,A[5][0]=A[5][0]+k+(U1/4294967296>>>0)>>>0,A[5][1]=U1>>>0,U1=A[6][1]+n,A[6][0]=A[6][0]+h+(U1/4294967296>>>0)>>>0,A[6][1]=U1>>>0,U1=A[7][1]+x,A[7][0]=A[7][0]+a+(U1/4294967296>>>0)>>>0,A[7][1]=U1>>>0,K1-=128}}});
var l11=E((CZ3,rMB)=>{var DS=M4();bM();N8();var l78=rMB.exports=DS.hmac=DS.hmac||{};l78.create=function(){var A=null,B=null,Q=null,D=null,Z={};return Z.start=function(G,F){if(G!==null)if(typeof G==="string")if(G=G.toLowerCase(),G in DS.md.algorithms)B=DS.md.algorithms[G].create();else throw new Error('Unknown hash algorithm "'+G+'"');else B=G;if(F===null)F=A;else{if(typeof F==="string")F=DS.util.createBuffer(F);else if(DS.util.isArray(F)){var I=F;F=DS.util.createBuffer();for(var Y=0;Y<I.length;++Y)F.putByte(I[Y])}var W=F.length();if(W>B.blockLength)B.start(),B.update(F.bytes()),F=B.digest();Q=DS.util.createBuffer(),D=DS.util.createBuffer(),W=F.length();for(var Y=0;Y<W;++Y){var I=F.at(Y);Q.putByte(54^I),D.putByte(92^I)}if(W<B.blockLength){var I=B.blockLength-W;for(var Y=0;Y<I;++Y)Q.putByte(54),D.putByte(92)}A=F,Q=Q.bytes(),D=D.bytes()}B.start(),B.update(Q)},Z.update=function(G){B.update(G)},Z.getMac=function(){var G=B.digest().bytes();return B.start(),B.update(D),B.update(G),B.digest()},Z.digest=Z.getMac,Z}});
var mOB=E((lZ3,uOB)=>{var nG=M4();Mv();l11();Rv1();a11();N8();var sv1=uOB.exports=nG.ssh=nG.ssh||{};sv1.privateKeyToPutty=function(A,B,Q){Q=Q||"",B=B||"";var D="ssh-rsa",Z=B===""?"none":"aes256-cbc",G="PuTTY-User-Key-File-2: "+D+`\r
`;G+="Encryption: "+Z+`\r
`,G+="Comment: "+Q+`\r
`;var F=nG.util.createBuffer();D01(F,D),lM(F,A.e),lM(F,A.n);var I=nG.util.encode64(F.bytes(),64),Y=Math.floor(I.length/66)+1;G+="Public-Lines: "+Y+`\r
`,G+=I;var W=nG.util.createBuffer();lM(W,A.d),lM(W,A.p),lM(W,A.q),lM(W,A.qInv);var J;if(!B)J=nG.util.encode64(W.bytes(),64);else{var X=W.length()+16-1;X-=X%16;var V=av1(W.bytes());V.truncate(V.length()-X+W.length()),W.putBuffer(V);var C=nG.util.createBuffer();C.putBuffer(av1("\x00\x00\x00\x00",B)),C.putBuffer(av1("\x00\x00\x00\x01",B));var K=nG.aes.createEncryptionCipher(C.truncate(8),"CBC");K.start(nG.util.createBuffer().fillWithByte(0,16)),K.update(W.copy()),K.finish();var H=K.output;H.truncate(16),J=nG.util.encode64(H.bytes(),64)}Y=Math.floor(J.length/66)+1,G+=`\r
Private-Lines: `+Y+`\r
`,G+=J;var z=av1("putty-private-key-file-mac-key",B),$=nG.util.createBuffer();D01($,D),D01($,Z),D01($,Q),$.putInt32(F.length()),$.putBuffer(F),$.putInt32(W.length()),$.putBuffer(W);var L=nG.hmac.create();return L.start("sha1",z),L.update($.bytes()),G+=`\r
Private-MAC: `+L.digest().toHex()+`\r
`,G};sv1.publicKeyToOpenSSH=function(A,B){var Q="ssh-rsa";B=B||"";var D=nG.util.createBuffer();return D01(D,Q),lM(D,A.e),lM(D,A.n),Q+" "+nG.util.encode64(D.bytes())+" "+B};sv1.privateKeyToOpenSSH=function(A,B){if(!B)return nG.pki.privateKeyToPem(A);return nG.pki.encryptRsaPrivateKey(A,B,{legacy:!0,algorithm:"aes128"})};sv1.getPublicKeyFingerprint=function(A,B){B=B||{};var Q=B.md||nG.md.md5.create(),D="ssh-rsa",Z=nG.util.createBuffer();D01(Z,D),lM(Z,A.e),lM(Z,A.n),Q.start(),Q.update(Z.getBytes());var G=Q.digest();if(B.encoding==="hex"){var F=G.toHex();if(B.delimiter)return F.match(/.{2}/g).join(B.delimiter);return F}else if(B.encoding==="binary")return G.getBytes();else if(B.encoding)throw new Error('Unknown encoding "'+B.encoding+'".');return G};function lM(A,B){var Q=B.toString(16);if(Q[0]>="8")Q="00"+Q;var D=nG.util.hexToBytes(Q);A.putInt32(D.length),A.putBytes(D)}function D01(A,B){A.putInt32(B.length),A.putString(B)}function av1(){var A=nG.md.sha1.create(),B=arguments.length;for(var Q=0;Q<B;++Q)A.update(arguments[Q]);return A.digest()}});
var pRB=E((jZ3,lRB)=>{var vv1=M4();Eq0();lRB.exports=vv1.mgf=vv1.mgf||{};vv1.mgf.mgf1=vv1.mgf1});
var qG1=E((zZ3,ZRB)=>{var wD=M4();Uv1();a$0();N8();ZRB.exports=wD.des=wD.des||{};wD.des.startEncrypting=function(A,B,Q,D){var Z=Pv1({key:A,output:Q,decrypt:!1,mode:D||(B===null?"ECB":"CBC")});return Z.start(B),Z};wD.des.createEncryptionCipher=function(A,B){return Pv1({key:A,output:null,decrypt:!1,mode:B})};wD.des.startDecrypting=function(A,B,Q,D){var Z=Pv1({key:A,output:Q,decrypt:!0,mode:D||(B===null?"ECB":"CBC")});return Z.start(B),Z};wD.des.createDecryptionCipher=function(A,B){return Pv1({key:A,output:null,decrypt:!0,mode:B})};wD.des.Algorithm=function(A,B){var Q=this;Q.name=A,Q.mode=new B({blockSize:8,cipher:{encrypt:function(D,Z){return DRB(Q._keys,D,Z,!1)},decrypt:function(D,Z){return DRB(Q._keys,D,Z,!0)}}}),Q._init=!1};wD.des.Algorithm.prototype.initialize=function(A){if(this._init)return;var B=wD.util.createBuffer(A.key);if(this.name.indexOf("3DES")===0){if(B.length()!==24)throw new Error("Invalid Triple-DES key size: "+B.length()*8)}this._keys=BD8(B),this._init=!0};hM("DES-ECB",wD.cipher.modes.ecb);hM("DES-CBC",wD.cipher.modes.cbc);hM("DES-CFB",wD.cipher.modes.cfb);hM("DES-OFB",wD.cipher.modes.ofb);hM("DES-CTR",wD.cipher.modes.ctr);hM("3DES-ECB",wD.cipher.modes.ecb);hM("3DES-CBC",wD.cipher.modes.cbc);hM("3DES-CFB",wD.cipher.modes.cfb);hM("3DES-OFB",wD.cipher.modes.ofb);hM("3DES-CTR",wD.cipher.modes.ctr);function hM(A,B){var Q=function(){return new wD.des.Algorithm(A,B)};wD.cipher.registerAlgorithm(A,Q)}var n78=[16843776,0,65536,16843780,16842756,66564,4,65536,1024,16843776,16843780,1024,16778244,16842756,16777216,4,1028,16778240,16778240,66560,66560,16842752,16842752,16778244,65540,16777220,16777220,65540,0,1028,66564,16777216,65536,16843780,4,16842752,16843776,16777216,16777216,1024,16842756,65536,66560,16777220,1024,4,16778244,66564,16843780,65540,16842752,16778244,16777220,1028,66564,16843776,1028,16778240,16778240,0,65540,66560,0,16842756],a78=[-2146402272,-2147450880,32768,1081376,1048576,32,-2146435040,-2147450848,-2147483616,-2146402272,-2146402304,-2147483648,-2147450880,1048576,32,-2146435040,1081344,1048608,-2147450848,0,-2147483648,32768,1081376,-2146435072,1048608,-2147483616,0,1081344,32800,-2146402304,-2146435072,32800,0,1081376,-2146435040,1048576,-2147450848,-2146435072,-2146402304,32768,-2146435072,-2147450880,32,-2146402272,1081376,32,32768,-2147483648,32800,-2146402304,1048576,-2147483616,1048608,-2147450848,-2147483616,1048608,1081344,0,-2147450880,32800,-2147483648,-2146435040,-2146402272,1081344],s78=[520,134349312,0,134348808,134218240,0,131592,134218240,131080,134217736,134217736,131072,134349320,131080,134348800,520,134217728,8,134349312,512,131584,134348800,134348808,131592,134218248,131584,131072,134218248,8,134349320,512,134217728,134349312,134217728,131080,520,131072,134349312,134218240,0,512,131080,134349320,134218240,134217736,512,0,134348808,134218248,131072,134217728,134349320,8,131592,131584,134217736,134348800,134218248,520,134348800,131592,8,134348808,131584],r78=[8396801,8321,8321,128,8396928,8388737,8388609,8193,0,8396800,8396800,8396929,129,0,8388736,8388609,1,8192,8388608,8396801,128,8388608,8193,8320,8388737,1,8320,8388736,8192,8396928,8396929,129,8388736,8388609,8396800,8396929,129,0,0,8396800,8320,8388736,8388737,1,8396801,8321,8321,128,8396929,129,1,8192,8388609,8193,8396928,8388737,8193,8320,8388608,8396801,128,8388608,8192,8396928],o78=[256,34078976,34078720,1107296512,524288,256,1073741824,34078720,1074266368,524288,33554688,1074266368,1107296512,1107820544,524544,1073741824,33554432,1074266112,1074266112,0,1073742080,1107820800,1107820800,33554688,1107820544,1073742080,0,1107296256,34078976,33554432,1107296256,524544,524288,1107296512,256,33554432,1073741824,34078720,1107296512,1074266368,33554688,1073741824,1107820544,34078976,1074266368,256,33554432,1107820544,1107820800,524544,1107296256,1107820800,34078720,0,1074266112,1107296256,524544,33554688,1073742080,524288,0,1074266112,34078976,1073742080],t78=[536870928,541065216,16384,541081616,541065216,16,541081616,4194304,536887296,4210704,4194304,536870928,4194320,536887296,536870912,16400,0,4194320,536887312,16384,4210688,536887312,16,541065232,541065232,0,4210704,541081600,16400,4210688,541081600,536870912,536887296,16,541065232,4210688,541081616,4194304,16400,536870928,4194304,536887296,536870912,16400,536870928,541081616,4210688,541065216,4210704,541081600,0,541065232,16,16384,541065216,4210704,16384,4194320,536887312,0,541081600,536870912,4194320,536887312],e78=[2097152,69206018,67110914,0,2048,67110914,2099202,69208064,69208066,2097152,0,67108866,2,67108864,69206018,2050,67110912,2099202,2097154,67110912,67108866,69206016,69208064,2097154,69206016,2048,2050,69208066,2099200,2,67108864,2099200,67108864,2099200,2097152,67110914,67110914,69206018,69206018,2,2097154,67108864,67110912,2097152,69208064,2050,2099202,69208064,2050,67108866,69208066,69206016,2099200,0,2,69208066,0,2099202,69206016,2048,67108866,67110912,2048,2097154],AD8=[268439616,4096,262144,268701760,268435456,268439616,64,268435456,262208,268697600,268701760,266240,268701696,266304,4096,64,268697600,268435520,268439552,4160,266240,262208,268697664,268701696,4160,0,0,268697664,268435520,268439552,266304,262144,266304,262144,268701696,4096,64,268697664,4096,266304,268439552,64,268435520,268697600,268697664,268435456,262144,268439616,0,268701760,262208,268435520,268697600,268439552,268439616,0,268701760,266240,266240,4160,4160,262208,268435456,268701696];function BD8(A){var B=[0,4,536870912,536870916,65536,65540,536936448,536936452,512,516,536871424,536871428,66048,66052,536936960,536936964],Q=[0,1,1048576,1048577,67108864,67108865,68157440,68157441,256,257,1048832,1048833,67109120,67109121,68157696,68157697],D=[0,8,2048,2056,16777216,16777224,16779264,16779272,0,8,2048,2056,16777216,16777224,16779264,16779272],Z=[0,2097152,134217728,136314880,8192,2105344,134225920,136323072,131072,2228224,134348800,136445952,139264,2236416,134356992,136454144],G=[0,262144,16,262160,0,262144,16,262160,4096,266240,4112,266256,4096,266240,4112,266256],F=[0,1024,32,1056,0,1024,32,1056,33554432,33555456,33554464,33555488,33554432,33555456,33554464,33555488],I=[0,268435456,524288,268959744,2,268435458,524290,268959746,0,268435456,524288,268959744,2,268435458,524290,268959746],Y=[0,65536,2048,67584,536870912,536936448,536872960,536938496,131072,196608,133120,198656,537001984,537067520,537004032,537069568],W=[0,262144,0,262144,2,262146,2,262146,33554432,33816576,33554432,33816576,33554434,33816578,33554434,33816578],J=[0,268435456,8,268435464,0,268435456,8,268435464,1024,268436480,1032,268436488,1024,268436480,1032,268436488],X=[0,32,0,32,1048576,1048608,1048576,1048608,8192,8224,8192,8224,1056768,1056800,1056768,1056800],V=[0,16777216,512,16777728,2097152,18874368,2097664,18874880,67108864,83886080,67109376,83886592,69206016,85983232,69206528,85983744],C=[0,4096,134217728,134221824,524288,528384,134742016,134746112,16,4112,134217744,134221840,524304,528400,134742032,134746128],K=[0,4,256,260,0,4,256,260,1,5,257,261,1,5,257,261],H=A.length()>8?3:1,z=[],$=[0,0,1,1,1,1,1,1,0,1,1,1,1,1,1,0],L=0,N;for(var O=0;O<H;O++){var R=A.getInt32(),T=A.getInt32();N=(R>>>4^T)&252645135,T^=N,R^=N<<4,N=(T>>>-16^R)&65535,R^=N,T^=N<<-16,N=(R>>>2^T)&858993459,T^=N,R^=N<<2,N=(T>>>-16^R)&65535,R^=N,T^=N<<-16,N=(R>>>1^T)&1431655765,T^=N,R^=N<<1,N=(T>>>8^R)&16711935,R^=N,T^=N<<8,N=(R>>>1^T)&1431655765,T^=N,R^=N<<1,N=R<<8|T>>>20&240,R=T<<24|T<<8&16711680|T>>>8&65280|T>>>24&240,T=N;for(var j=0;j<$.length;++j){if($[j])R=R<<2|R>>>26,T=T<<2|T>>>26;else R=R<<1|R>>>27,T=T<<1|T>>>27;R&=-15,T&=-15;var f=B[R>>>28]|Q[R>>>24&15]|D[R>>>20&15]|Z[R>>>16&15]|G[R>>>12&15]|F[R>>>8&15]|I[R>>>4&15],k=Y[T>>>28]|W[T>>>24&15]|J[T>>>20&15]|X[T>>>16&15]|V[T>>>12&15]|C[T>>>8&15]|K[T>>>4&15];N=(k>>>16^f)&65535,z[L++]=f^N,z[L++]=k^N<<16}}return z}function DRB(A,B,Q,D){var Z=A.length===32?3:9,G;if(Z===3)G=D?[30,-2,-2]:[0,32,2];else G=D?[94,62,-2,32,64,2,30,-2,-2]:[0,32,2,62,30,-2,64,96,2];var F,I=B[0],Y=B[1];F=(I>>>4^Y)&252645135,Y^=F,I^=F<<4,F=(I>>>16^Y)&65535,Y^=F,I^=F<<16,F=(Y>>>2^I)&858993459,I^=F,Y^=F<<2,F=(Y>>>8^I)&16711935,I^=F,Y^=F<<8,F=(I>>>1^Y)&1431655765,Y^=F,I^=F<<1,I=I<<1|I>>>31,Y=Y<<1|Y>>>31;for(var W=0;W<Z;W+=3){var J=G[W+1],X=G[W+2];for(var V=G[W];V!=J;V+=X){var C=Y^A[V],K=(Y>>>4|Y<<28)^A[V+1];F=I,I=Y,Y=F^(a78[C>>>24&63]|r78[C>>>16&63]|t78[C>>>8&63]|AD8[C&63]|n78[K>>>24&63]|s78[K>>>16&63]|o78[K>>>8&63]|e78[K&63])}F=I,I=Y,Y=F}I=I>>>1|I<<31,Y=Y>>>1|Y<<31,F=(I>>>1^Y)&1431655765,Y^=F,I^=F<<1,F=(Y>>>8^I)&16711935,I^=F,Y^=F<<8,F=(Y>>>2^I)&858993459,I^=F,Y^=F<<2,F=(I>>>16^Y)&65535,Y^=F,I^=F<<16,F=(I>>>4^Y)&252645135,Y^=F,I^=F<<4,Q[0]=I,Q[1]=Y}function Pv1(A){A=A||{};var B=(A.mode||"CBC").toUpperCase(),Q="DES-"+B,D;if(A.decrypt)D=wD.cipher.createDecipher(Q,A.key);else D=wD.cipher.createCipher(Q,A.key);var Z=D.start;return D.start=function(G,F){var I=null;if(F instanceof wD.util.ByteBuffer)I=F,F={};F=F||{},F.output=I,F.iv=G,Z.call(D,F)},D}});
var vMB=E((GZ3,xMB)=>{var d$0={};xMB.exports=d$0;var _MB={};d$0.encode=function(A,B,Q){if(typeof B!=="string")throw new TypeError('"alphabet" must be a string.');if(Q!==void 0&&typeof Q!=="number")throw new TypeError('"maxline" must be a number.');var D="";if(!(A instanceof Uint8Array))D=b78(A,B);else{var Z=0,G=B.length,F=B.charAt(0),I=[0];for(Z=0;Z<A.length;++Z){for(var Y=0,W=A[Z];Y<I.length;++Y)W+=I[Y]<<8,I[Y]=W%G,W=W/G|0;while(W>0)I.push(W%G),W=W/G|0}for(Z=0;A[Z]===0&&Z<A.length-1;++Z)D+=F;for(Z=I.length-1;Z>=0;--Z)D+=B[I[Z]]}if(Q){var J=new RegExp(".{1,"+Q+"}","g");D=D.match(J).join(`\r
`)}return D};d$0.decode=function(A,B){if(typeof A!=="string")throw new TypeError('"input" must be a string.');if(typeof B!=="string")throw new TypeError('"alphabet" must be a string.');var Q=_MB[B];if(!Q){Q=_MB[B]=[];for(var D=0;D<B.length;++D)Q[B.charCodeAt(D)]=D}A=A.replace(/\s/g,"");var Z=B.length,G=B.charAt(0),F=[0];for(var D=0;D<A.length;D++){var I=Q[A.charCodeAt(D)];if(I===void 0)return;for(var Y=0,W=I;Y<F.length;++Y)W+=F[Y]*Z,F[Y]=W&255,W>>=8;while(W>0)F.push(W&255),W>>=8}for(var J=0;A[J]===G&&J<A.length-1;++J)F.push(0);if(typeof Buffer!=="undefined")return Buffer.from(F.reverse());return new Uint8Array(F.reverse())};function b78(A,B){var Q=0,D=B.length,Z=B.charAt(0),G=[0];for(Q=0;Q<A.length();++Q){for(var F=0,I=A.at(Q);F<G.length;++F)I+=G[F]<<8,G[F]=I%D,I=I/D|0;while(I>0)G.push(I%D),I=I/D|0}var Y="";for(Q=0;A.at(Q)===0&&Q<A.length()-1;++Q)Y+=Z;for(Q=G.length-1;Q>=0;--Q)Y+=B[G[Q]];return Y}});
var xOB=E((mZ3,_OB)=>{var k6=M4();N8();_OB.exports=k6.log=k6.log||{};k6.log.levels=["none","error","warning","info","debug","verbose","max"];var nv1={},mq0=[],jG1=null;k6.log.LEVEL_LOCKED=2;k6.log.NO_LEVEL_CHECK=4;k6.log.INTERPOLATE=8;for(GU=0;GU<k6.log.levels.length;++GU)pv1=k6.log.levels[GU],nv1[pv1]={index:GU,name:pv1.toUpperCase()};var pv1,GU;k6.log.logMessage=function(A){var B=nv1[A.level].index;for(var Q=0;Q<mq0.length;++Q){var D=mq0[Q];if(D.flags&k6.log.NO_LEVEL_CHECK)D.f(A);else{var Z=nv1[D.level].index;if(B<=Z)D.f(D,A)}}};k6.log.prepareStandard=function(A){if(!("standard"in A))A.standard=nv1[A.level].name+" ["+A.category+"] "+A.message};k6.log.prepareFull=function(A){if(!("full"in A)){var B=[A.message];B=B.concat([]),A.full=k6.util.format.apply(this,B)}};k6.log.prepareStandardFull=function(A){if(!("standardFull"in A))k6.log.prepareStandard(A),A.standardFull=A.standard};iv1=["error","warning","info","debug","verbose"];for(GU=0;GU<iv1.length;++GU)(function(B){k6.log[B]=function(Q,D){var Z=Array.prototype.slice.call(arguments).slice(2),G={timestamp:new Date,level:B,category:Q,message:D,arguments:Z};k6.log.logMessage(G)}})(iv1[GU]);var iv1,GU;k6.log.makeLogger=function(A){var B={flags:0,f:A};return k6.log.setLevel(B,"none"),B};k6.log.setLevel=function(A,B){var Q=!1;if(A&&!(A.flags&k6.log.LEVEL_LOCKED))for(var D=0;D<k6.log.levels.length;++D){var Z=k6.log.levels[D];if(B==Z){A.level=B,Q=!0;break}}return Q};k6.log.lock=function(A,B){if(typeof B==="undefined"||B)A.flags|=k6.log.LEVEL_LOCKED;else A.flags&=~k6.log.LEVEL_LOCKED};k6.log.addLogger=function(A){mq0.push(A)};if(typeof console!=="undefined"&&"log"in console){if(console.error&&console.warn&&console.info&&console.debug)dq0={error:console.error,warning:console.warn,info:console.info,debug:console.debug,verbose:console.debug},Q01=function(A,B){k6.log.prepareStandard(B);var Q=dq0[B.level],D=[B.standard];D=D.concat(B.arguments.slice()),Q.apply(console,D)},Qd=k6.log.makeLogger(Q01);else Q01=function(B,Q){k6.log.prepareStandardFull(Q),console.log(Q.standardFull)},Qd=k6.log.makeLogger(Q01);k6.log.setLevel(Qd,"debug"),k6.log.addLogger(Qd),jG1=Qd}else console={log:function(){}};var Qd,dq0,Q01;if(jG1!==null&&typeof window!=="undefined"&&window.location){if(B01=new URL(window.location.href).searchParams,B01.has("console.level"))k6.log.setLevel(jG1,B01.get("console.level").slice(-1)[0]);if(B01.has("console.lock")){if(cq0=B01.get("console.lock").slice(-1)[0],cq0=="true")k6.log.lock(jG1)}}var B01,cq0;k6.log.consoleLogger=jG1});
var yOB=E((uZ3,kOB)=>{var dK=M4();N8();eE();LG1();kOB.exports=dK.kem=dK.kem||{};var SOB=dK.jsbn.BigInteger;dK.kem.rsa={};dK.kem.rsa.create=function(A,B){B=B||{};var Q=B.prng||dK.random,D={};return D.encrypt=function(Z,G){var F=Math.ceil(Z.n.bitLength()/8),I;do I=new SOB(dK.util.bytesToHex(Q.getBytesSync(F)),16).mod(Z.n);while(I.compareTo(SOB.ONE)<=0);I=dK.util.hexToBytes(I.toString(16));var Y=F-I.length;if(Y>0)I=dK.util.fillString(String.fromCharCode(0),Y)+I;var W=Z.encrypt(I,"NONE"),J=A.generate(I,G);return{encapsulation:W,key:J}},D.decrypt=function(Z,G,F){var I=Z.decrypt(G,"NONE");return A.generate(I,F)},D};dK.kem.kdf1=function(A,B){jOB(this,A,0,B||A.digestLength)};dK.kem.kdf2=function(A,B){jOB(this,A,1,B||A.digestLength)};function jOB(A,B,Q,D){A.generate=function(Z,G){var F=new dK.util.ByteBuffer,I=Math.ceil(G/D)+Q,Y=new dK.util.ByteBuffer;for(var W=Q;W<I;++W){Y.putInt32(W),B.start(),B.update(Z+Y.getBytes());var J=B.digest();F.putBytes(J.getBytes(D))}return F.truncate(F.length()-G),F.getBytes()}}});
var zOB=E((tG8)=>{var oG8=M4();j$();var TI=oG8.asn1;tG8.privateKeyValidator={name:"PrivateKeyInfo",tagClass:TI.Class.UNIVERSAL,type:TI.Type.SEQUENCE,constructed:!0,value:[{name:"PrivateKeyInfo.version",tagClass:TI.Class.UNIVERSAL,type:TI.Type.INTEGER,constructed:!1,capture:"privateKeyVersion"},{name:"PrivateKeyInfo.privateKeyAlgorithm",tagClass:TI.Class.UNIVERSAL,type:TI.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:TI.Class.UNIVERSAL,type:TI.Type.OID,constructed:!1,capture:"privateKeyOid"}]},{name:"PrivateKeyInfo",tagClass:TI.Class.UNIVERSAL,type:TI.Type.OCTETSTRING,constructed:!1,capture:"privateKey"}]};tG8.publicKeyValidator={name:"SubjectPublicKeyInfo",tagClass:TI.Class.UNIVERSAL,type:TI.Type.SEQUENCE,constructed:!0,captureAsn1:"subjectPublicKeyInfo",value:[{name:"SubjectPublicKeyInfo.AlgorithmIdentifier",tagClass:TI.Class.UNIVERSAL,type:TI.Type.SEQUENCE,constructed:!0,value:[{name:"AlgorithmIdentifier.algorithm",tagClass:TI.Class.UNIVERSAL,type:TI.Type.OID,constructed:!1,capture:"publicKeyOid"}]},{tagClass:TI.Class.UNIVERSAL,type:TI.Type.BITSTRING,constructed:!1,composed:!0,captureBitStringValue:"ed25519PublicKey"}]}});
var zq0=E((PZ3,dRB)=>{var s11=M4();j$();N8();var e2=s11.asn1,r11=dRB.exports=s11.pkcs7asn1=s11.pkcs7asn1||{};s11.pkcs7=s11.pkcs7||{};s11.pkcs7.asn1=r11;var uRB={name:"ContentInfo",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SEQUENCE,constructed:!0,value:[{name:"ContentInfo.ContentType",tagClass:e2.Class.UNIVERSAL,type:e2.Type.OID,constructed:!1,capture:"contentType"},{name:"ContentInfo.content",tagClass:e2.Class.CONTEXT_SPECIFIC,type:0,constructed:!0,optional:!0,captureAsn1:"content"}]};r11.contentInfoValidator=uRB;var mRB={name:"EncryptedContentInfo",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SEQUENCE,constructed:!0,value:[{name:"EncryptedContentInfo.contentType",tagClass:e2.Class.UNIVERSAL,type:e2.Type.OID,constructed:!1,capture:"contentType"},{name:"EncryptedContentInfo.contentEncryptionAlgorithm",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SEQUENCE,constructed:!0,value:[{name:"EncryptedContentInfo.contentEncryptionAlgorithm.algorithm",tagClass:e2.Class.UNIVERSAL,type:e2.Type.OID,constructed:!1,capture:"encAlgorithm"},{name:"EncryptedContentInfo.contentEncryptionAlgorithm.parameter",tagClass:e2.Class.UNIVERSAL,captureAsn1:"encParameter"}]},{name:"EncryptedContentInfo.encryptedContent",tagClass:e2.Class.CONTEXT_SPECIFIC,type:0,capture:"encryptedContent",captureAsn1:"encryptedContentAsn1"}]};r11.envelopedDataValidator={name:"EnvelopedData",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SEQUENCE,constructed:!0,value:[{name:"EnvelopedData.Version",tagClass:e2.Class.UNIVERSAL,type:e2.Type.INTEGER,constructed:!1,capture:"version"},{name:"EnvelopedData.RecipientInfos",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SET,constructed:!0,captureAsn1:"recipientInfos"}].concat(mRB)};r11.encryptedDataValidator={name:"EncryptedData",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SEQUENCE,constructed:!0,value:[{name:"EncryptedData.Version",tagClass:e2.Class.UNIVERSAL,type:e2.Type.INTEGER,constructed:!1,capture:"version"}].concat(mRB)};var IG8={name:"SignerInfo",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SEQUENCE,constructed:!0,value:[{name:"SignerInfo.version",tagClass:e2.Class.UNIVERSAL,type:e2.Type.INTEGER,constructed:!1},{name:"SignerInfo.issuerAndSerialNumber",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SEQUENCE,constructed:!0,value:[{name:"SignerInfo.issuerAndSerialNumber.issuer",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SEQUENCE,constructed:!0,captureAsn1:"issuer"},{name:"SignerInfo.issuerAndSerialNumber.serialNumber",tagClass:e2.Class.UNIVERSAL,type:e2.Type.INTEGER,constructed:!1,capture:"serial"}]},{name:"SignerInfo.digestAlgorithm",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SEQUENCE,constructed:!0,value:[{name:"SignerInfo.digestAlgorithm.algorithm",tagClass:e2.Class.UNIVERSAL,type:e2.Type.OID,constructed:!1,capture:"digestAlgorithm"},{name:"SignerInfo.digestAlgorithm.parameter",tagClass:e2.Class.UNIVERSAL,constructed:!1,captureAsn1:"digestParameter",optional:!0}]},{name:"SignerInfo.authenticatedAttributes",tagClass:e2.Class.CONTEXT_SPECIFIC,type:0,constructed:!0,optional:!0,capture:"authenticatedAttributes"},{name:"SignerInfo.digestEncryptionAlgorithm",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SEQUENCE,constructed:!0,capture:"signatureAlgorithm"},{name:"SignerInfo.encryptedDigest",tagClass:e2.Class.UNIVERSAL,type:e2.Type.OCTETSTRING,constructed:!1,capture:"signature"},{name:"SignerInfo.unauthenticatedAttributes",tagClass:e2.Class.CONTEXT_SPECIFIC,type:1,constructed:!0,optional:!0,capture:"unauthenticatedAttributes"}]};r11.signedDataValidator={name:"SignedData",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SEQUENCE,constructed:!0,value:[{name:"SignedData.Version",tagClass:e2.Class.UNIVERSAL,type:e2.Type.INTEGER,constructed:!1,capture:"version"},{name:"SignedData.DigestAlgorithms",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SET,constructed:!0,captureAsn1:"digestAlgorithms"},uRB,{name:"SignedData.Certificates",tagClass:e2.Class.CONTEXT_SPECIFIC,type:0,optional:!0,captureAsn1:"certificates"},{name:"SignedData.CertificateRevocationLists",tagClass:e2.Class.CONTEXT_SPECIFIC,type:1,optional:!0,captureAsn1:"crls"},{name:"SignedData.SignerInfos",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SET,capture:"signerInfos",optional:!0,value:[IG8]}]};r11.recipientInfoValidator={name:"RecipientInfo",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SEQUENCE,constructed:!0,value:[{name:"RecipientInfo.version",tagClass:e2.Class.UNIVERSAL,type:e2.Type.INTEGER,constructed:!1,capture:"version"},{name:"RecipientInfo.issuerAndSerial",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SEQUENCE,constructed:!0,value:[{name:"RecipientInfo.issuerAndSerial.issuer",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SEQUENCE,constructed:!0,captureAsn1:"issuer"},{name:"RecipientInfo.issuerAndSerial.serialNumber",tagClass:e2.Class.UNIVERSAL,type:e2.Type.INTEGER,constructed:!1,capture:"serial"}]},{name:"RecipientInfo.keyEncryptionAlgorithm",tagClass:e2.Class.UNIVERSAL,type:e2.Type.SEQUENCE,constructed:!0,value:[{name:"RecipientInfo.keyEncryptionAlgorithm.algorithm",tagClass:e2.Class.UNIVERSAL,type:e2.Type.OID,constructed:!1,capture:"encAlgorithm"},{name:"RecipientInfo.keyEncryptionAlgorithm.parameter",tagClass:e2.Class.UNIVERSAL,constructed:!1,captureAsn1:"encParameter",optional:!0}]},{name:"RecipientInfo.encryptedKey",tagClass:e2.Class.UNIVERSAL,type:e2.Type.OCTETSTRING,constructed:!1,capture:"encKey"}]}});

module.exports = NF8;
