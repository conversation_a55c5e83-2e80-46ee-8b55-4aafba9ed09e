// E_module package extracted with entry point: oe2

var oe2=E((lJ0)=>{Object.defineProperty(lJ0,"__esModule",{value:!0});lJ0.OTLPLogExporter=void 0;var vE6=re2();Object.defineProperty(lJ0,"OTLPLogExporter",{enumerable:!0,get:function(){return vE6.OTLPLogExporter}})});
var re2=E((ae2)=>{Object.defineProperty(ae2,"__esModule",{value:!0});ae2.OTLPLogExporter=void 0;var ie2=kJ0(),_E6=ju(),xE6=Tu();class ne2 extends xE6.OTLPExporterBase{constructor(A={}){super(ie2.createOtlpGrpcExportDelegate(ie2.convertLegacyOtlpGrpcOptions(A,"LOGS"),_E6.ProtobufLogsSerializer,"LogsExportService","/opentelemetry.proto.collector.logs.v1.LogsService/Export"))}}ae2.OTLPLogExporter=ne2});

module.exports = oe2;
