// E_module package extracted with entry point: wjA

var $6=E((O35,VHA)=>{var{defineProperty:po1,getOwnPropertyDescriptor:LWQ,getOwnPropertyNames:MWQ}=Object,RWQ=Object.prototype.hasOwnProperty,OWQ=(A,B)=>{for(var Q in B)po1(A,Q,{get:B[Q],enumerable:!0})},TWQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of MWQ(B))if(!RWQ.call(A,Z)&&Z!==Q)po1(A,Z,{get:()=>B[Z],enumerable:!(D=LWQ(B,Z))||D.enumerable})}return A},PWQ=(A)=>TWQ(po1({},"__esModule",{value:!0}),A),FHA={};OWQ(FHA,{FromStringShapeDeserializer:()=>JHA,HttpBindingProtocol:()=>kWQ,HttpInterceptingShapeDeserializer:()=>hWQ,HttpInterceptingShapeSerializer:()=>uWQ,RequestBuilder:()=>WHA,RpcProtocol:()=>_WQ,ToStringShapeSerializer:()=>XHA,collectBody:()=>Yn,determineTimestampFormat:()=>io1,extendedEncodeURIComponent:()=>xQ1,requestBuilder:()=>vWQ,resolvedPath:()=>YHA});VHA.exports=PWQ(FHA);var do1=Fy(),Yn=async(A=new Uint8Array,B)=>{if(A instanceof Uint8Array)return do1.Uint8ArrayBlobAdapter.mutate(A);if(!A)return do1.Uint8ArrayBlobAdapter.mutate(new Uint8Array);let Q=B.streamCollector(A);return do1.Uint8ArrayBlobAdapter.mutate(await Q)};function xQ1(A){return encodeURIComponent(A).replace(/[!'()*]/g,function(B){return"%"+B.charCodeAt(0).toString(16).toUpperCase()})}var yQ1=jQ(),SWQ=Oh(),pz1=jQ(),BHA=Y6(),QHA=Oh(),jWQ=Fy(),IHA=class{constructor(A){this.options=A}getRequestType(){return QHA.HttpRequest}getResponseType(){return QHA.HttpResponse}setSerdeContext(A){if(this.serdeContext=A,this.serializer.setSerdeContext(A),this.deserializer.setSerdeContext(A),this.getPayloadCodec())this.getPayloadCodec().setSerdeContext(A)}updateServiceEndpoint(A,B){if("url"in B){A.protocol=B.url.protocol,A.hostname=B.url.hostname,A.port=B.url.port?Number(B.url.port):void 0,A.path=B.url.pathname,A.fragment=B.url.hash||void 0,A.username=B.url.username||void 0,A.password=B.url.password||void 0;for(let[Q,D]of B.url.searchParams.entries()){if(!A.query)A.query={};A.query[Q]=D}return A}else return A.protocol=B.protocol,A.hostname=B.hostname,A.port=B.port?Number(B.port):void 0,A.path=B.path,A.query={...B.query},A}setHostPrefix(A,B,Q){let D=pz1.NormalizedSchema.of(B),Z=pz1.NormalizedSchema.of(B.input);if(D.getMergedTraits().endpoint){let G=D.getMergedTraits().endpoint?.[0];if(typeof G==="string"){let F=[...Z.structIterator()].filter(([,I])=>I.getMergedTraits().hostLabel);for(let[I]of F){let Y=Q[I];if(typeof Y!=="string")throw new Error(`@smithy/core/schema - ${I} in input must be a string as hostLabel.`);G=G.replace(`{${I}}`,Y)}A.hostname=G+A.hostname}}}deserializeMetadata(A){return{httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}}async deserializeHttpMessage(A,B,Q,D,Z){let G;if(D instanceof Set)G=Z;else G=D;let F=this.deserializer,I=pz1.NormalizedSchema.of(A),Y=[];for(let[W,J]of I.structIterator()){let X=J.getMemberTraits();if(X.httpPayload){if(J.isStreaming())if(J.isStructSchema()){let K=this.serdeContext;if(!K.eventStreamMarshaller)throw new Error("@smithy/core - HttpProtocol: eventStreamMarshaller missing in serdeContext.");let H=J.getMemberSchemas();G[W]=K.eventStreamMarshaller.deserialize(Q.body,async(z)=>{let $=Object.keys(z).find((L)=>{return L!=="__type"})??"";if($ in H){let L=H[$];return{[$]:await F.read(L,z[$].body)}}else return{$unknown:z}})}else G[W]=jWQ.sdkStreamMixin(Q.body);else if(Q.body){let C=await Yn(Q.body,B);if(C.byteLength>0)G[W]=await F.read(J,C)}}else if(X.httpHeader){let V=String(X.httpHeader).toLowerCase(),C=Q.headers[V];if(C!=null)if(J.isListSchema()){let K=J.getValueSchema(),H;if(K.isTimestampSchema()&&K.getSchema()===pz1.SCHEMA.TIMESTAMP_DEFAULT)H=BHA.splitEvery(C,",",2);else H=BHA.splitHeader(C);let z=[];for(let $ of H)z.push(await F.read([K,{httpHeader:V}],$.trim()));G[W]=z}else G[W]=await F.read(J,C)}else if(X.httpPrefixHeaders!==void 0){G[W]={};for(let[V,C]of Object.entries(Q.headers))if(V.startsWith(X.httpPrefixHeaders))G[W][V.slice(X.httpPrefixHeaders.length)]=await F.read([J.getValueSchema(),{httpHeader:V}],C)}else if(X.httpResponseCode)G[W]=Q.statusCode;else Y.push(W)}return Y}},kWQ=class extends IHA{async serializeRequest(A,B,Q){let D=this.serializer,Z={},G={},F=await Q.endpoint(),I=yQ1.NormalizedSchema.of(A?.input),Y=I.getSchema(),W=!1,J,X=new SWQ.HttpRequest({protocol:"",hostname:"",port:void 0,path:"",fragment:void 0,query:Z,headers:G,body:void 0});if(F){this.updateServiceEndpoint(X,F),this.setHostPrefix(X,A,B);let C=yQ1.NormalizedSchema.translateTraits(A.traits);if(C.http){X.method=C.http[0];let[K,H]=C.http[1].split("?");if(X.path=="/")X.path=K;else X.path+=K;let z=new URLSearchParams(H??"");Object.assign(Z,Object.fromEntries(z))}}let V={...B};for(let C of Object.keys(V)){let K=I.getMemberSchema(C);if(K===void 0)continue;let H=K.getMergedTraits(),z=V[C];if(H.httpPayload)if(K.isStreaming())if(K.isStructSchema())throw new Error("serialization of event streams is not yet implemented");else J=z;else D.write(K,z),J=D.flush();else if(H.httpLabel){D.write(K,z);let $=D.flush();if(X.path.includes(`{${C}+}`))X.path=X.path.replace(`{${C}+}`,$.split("/").map(xQ1).join("/"));else if(X.path.includes(`{${C}}`))X.path=X.path.replace(`{${C}}`,xQ1($));delete V[C]}else if(H.httpHeader)D.write(K,z),G[H.httpHeader.toLowerCase()]=String(D.flush()),delete V[C];else if(typeof H.httpPrefixHeaders==="string"){for(let[$,L]of Object.entries(z)){let N=H.httpPrefixHeaders+$;D.write([K.getValueSchema(),{httpHeader:N}],L),G[N.toLowerCase()]=D.flush()}delete V[C]}else if(H.httpQuery||H.httpQueryParams)this.serializeQuery(K,z,Z),delete V[C];else W=!0}if(W&&B)D.write(Y,V),J=D.flush();return X.headers=G,X.query=Z,X.body=J,X}serializeQuery(A,B,Q){let D=this.serializer,Z=A.getMergedTraits();if(Z.httpQueryParams){for(let[G,F]of Object.entries(B))if(!(G in Q))this.serializeQuery(yQ1.NormalizedSchema.of([A.getValueSchema(),{...Z,httpQuery:G,httpQueryParams:void 0}]),F,Q);return}if(A.isListSchema()){let G=!!A.getMergedTraits().sparse,F=[];for(let I of B){D.write([A.getValueSchema(),Z],I);let Y=D.flush();if(G||Y!==void 0)F.push(Y)}Q[Z.httpQuery]=F}else D.write([A,Z],B),Q[Z.httpQuery]=D.flush()}async deserializeResponse(A,B,Q){let D=this.deserializer,Z=yQ1.NormalizedSchema.of(A.output),G={};if(Q.statusCode>=300){let Y=await Yn(Q.body,B);if(Y.byteLength>0)Object.assign(G,await D.read(yQ1.SCHEMA.DOCUMENT,Y));throw await this.handleError(A,B,Q,G,this.deserializeMetadata(Q)),new Error("@smithy/core/protocols - HTTP Protocol error handler failed to throw.")}for(let Y in Q.headers){let W=Q.headers[Y];delete Q.headers[Y],Q.headers[Y.toLowerCase()]=W}let F=await this.deserializeHttpMessage(Z,B,Q,G);if(F.length){let Y=await Yn(Q.body,B);if(Y.byteLength>0){let W=await D.read(Z,Y);for(let J of F)G[J]=W[J]}}return{$metadata:this.deserializeMetadata(Q),...G}}},co1=jQ(),yWQ=Oh(),_WQ=class extends IHA{async serializeRequest(A,B,Q){let D=this.serializer,Z={},G={},F=await Q.endpoint(),Y=co1.NormalizedSchema.of(A?.input).getSchema(),W,J=new yWQ.HttpRequest({protocol:"",hostname:"",port:void 0,path:"/",fragment:void 0,query:Z,headers:G,body:void 0});if(F)this.updateServiceEndpoint(J,F),this.setHostPrefix(J,A,B);let X={...B};if(B)D.write(Y,X),W=D.flush();return J.headers=G,J.query=Z,J.body=W,J.method="POST",J}async deserializeResponse(A,B,Q){let D=this.deserializer,Z=co1.NormalizedSchema.of(A.output),G={};if(Q.statusCode>=300){let Y=await Yn(Q.body,B);if(Y.byteLength>0)Object.assign(G,await D.read(co1.SCHEMA.DOCUMENT,Y));throw await this.handleError(A,B,Q,G,this.deserializeMetadata(Q)),new Error("@smithy/core/protocols - RPC Protocol error handler failed to throw.")}for(let Y in Q.headers){let W=Q.headers[Y];delete Q.headers[Y],Q.headers[Y.toLowerCase()]=W}let F=await Yn(Q.body,B);if(F.byteLength>0)Object.assign(G,await D.read(Z,F));return{$metadata:this.deserializeMetadata(Q),...G}}},xWQ=Oh(),YHA=(A,B,Q,D,Z,G)=>{if(B!=null&&B[Q]!==void 0){let F=D();if(F.length<=0)throw new Error("Empty value provided for input HTTP label: "+Q+".");A=A.replace(Z,G?F.split("/").map((I)=>xQ1(I)).join("/"):xQ1(F))}else throw new Error("No value provided for input HTTP label: "+Q+".");return A};function vWQ(A,B){return new WHA(A,B)}var WHA=class{constructor(A,B){this.input=A,this.context=B,this.query={},this.method="",this.headers={},this.path="",this.body=null,this.hostname="",this.resolvePathStack=[]}async build(){let{hostname:A,protocol:B="https",port:Q,path:D}=await this.context.endpoint();this.path=D;for(let Z of this.resolvePathStack)Z(this.path);return new xWQ.HttpRequest({protocol:B,hostname:this.hostname||A,port:Q,method:this.method,path:this.path,query:this.query,body:this.body,headers:this.headers})}hn(A){return this.hostname=A,this}bp(A){return this.resolvePathStack.push((B)=>{this.path=`${B?.endsWith("/")?B.slice(0,-1):B||""}`+A}),this}p(A,B,Q,D){return this.resolvePathStack.push((Z)=>{this.path=YHA(Z,this.input,A,B,Q,D)}),this}h(A){return this.headers=A,this}q(A){return this.query=A,this}b(A){return this.body=A,this}m(A){return this.method=A,this}},iz1=jQ(),In=Y6(),DHA=mo1(),bWQ=cB(),_Q1=jQ();function io1(A,B){if(B.timestampFormat.useTrait){if(A.isTimestampSchema()&&(A.getSchema()===_Q1.SCHEMA.TIMESTAMP_DATE_TIME||A.getSchema()===_Q1.SCHEMA.TIMESTAMP_HTTP_DATE||A.getSchema()===_Q1.SCHEMA.TIMESTAMP_EPOCH_SECONDS))return A.getSchema()}let{httpLabel:Q,httpPrefixHeaders:D,httpHeader:Z,httpQuery:G}=A.getMergedTraits();return(B.httpBindings?typeof D==="string"||Boolean(Z)?_Q1.SCHEMA.TIMESTAMP_HTTP_DATE:Boolean(G)||Boolean(Q)?_Q1.SCHEMA.TIMESTAMP_DATE_TIME:void 0:void 0)??B.timestampFormat.default}var JHA=class{constructor(A){this.settings=A}setSerdeContext(A){this.serdeContext=A}read(A,B){let Q=iz1.NormalizedSchema.of(A);if(Q.isListSchema())return In.splitHeader(B).map((D)=>this.read(Q.getValueSchema(),D));if(Q.isBlobSchema())return(this.serdeContext?.base64Decoder??DHA.fromBase64)(B);if(Q.isTimestampSchema())switch(io1(Q,this.settings)){case iz1.SCHEMA.TIMESTAMP_DATE_TIME:return In.parseRfc3339DateTimeWithOffset(B);case iz1.SCHEMA.TIMESTAMP_HTTP_DATE:return In.parseRfc7231DateTime(B);case iz1.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return In.parseEpochTimestamp(B);default:return console.warn("Missing timestamp format, parsing value with Date constructor:",B),new Date(B)}if(Q.isStringSchema()){let D=Q.getMergedTraits().mediaType,Z=B;if(D){if(Q.getMergedTraits().httpHeader)Z=this.base64ToUtf8(Z);if(D==="application/json"||D.endsWith("+json"))Z=In.LazyJsonString.from(Z);return Z}}switch(!0){case Q.isNumericSchema():return Number(B);case Q.isBigIntegerSchema():return BigInt(B);case Q.isBigDecimalSchema():return new In.NumericValue(B,"bigDecimal");case Q.isBooleanSchema():return String(B).toLowerCase()==="true"}return B}base64ToUtf8(A){return(this.serdeContext?.utf8Encoder??bWQ.toUtf8)((this.serdeContext?.base64Decoder??DHA.fromBase64)(A))}},fWQ=jQ(),ZHA=cB(),hWQ=class{constructor(A,B){this.codecDeserializer=A,this.stringDeserializer=new JHA(B)}setSerdeContext(A){this.stringDeserializer.setSerdeContext(A),this.codecDeserializer.setSerdeContext(A),this.serdeContext=A}read(A,B){let Q=fWQ.NormalizedSchema.of(A),D=Q.getMergedTraits(),Z=this.serdeContext?.utf8Encoder??ZHA.toUtf8;if(D.httpHeader||D.httpResponseCode)return this.stringDeserializer.read(Q,Z(B));if(D.httpPayload){if(Q.isBlobSchema()){let G=this.serdeContext?.utf8Decoder??ZHA.fromUtf8;if(typeof B==="string")return G(B);return B}else if(Q.isStringSchema()){if("byteLength"in B)return Z(B);return B}}return this.codecDeserializer.read(Q,B)}},gWQ=jQ(),nz1=jQ(),lo1=Y6(),GHA=mo1(),XHA=class{constructor(A){this.settings=A,this.stringBuffer="",this.serdeContext=void 0}setSerdeContext(A){this.serdeContext=A}write(A,B){let Q=nz1.NormalizedSchema.of(A);switch(typeof B){case"object":if(B===null){this.stringBuffer="null";return}if(Q.isTimestampSchema()){if(!(B instanceof Date))throw new Error(`@smithy/core/protocols - received non-Date value ${B} when schema expected Date in ${Q.getName(!0)}`);switch(io1(Q,this.settings)){case nz1.SCHEMA.TIMESTAMP_DATE_TIME:this.stringBuffer=B.toISOString().replace(".000Z","Z");break;case nz1.SCHEMA.TIMESTAMP_HTTP_DATE:this.stringBuffer=lo1.dateToUtcString(B);break;case nz1.SCHEMA.TIMESTAMP_EPOCH_SECONDS:this.stringBuffer=String(B.getTime()/1000);break;default:console.warn("Missing timestamp format, using epoch seconds",B),this.stringBuffer=String(B.getTime()/1000)}return}if(Q.isBlobSchema()&&"byteLength"in B){this.stringBuffer=(this.serdeContext?.base64Encoder??GHA.toBase64)(B);return}if(Q.isListSchema()&&Array.isArray(B)){let G="";for(let F of B){this.write([Q.getValueSchema(),Q.getMergedTraits()],F);let I=this.flush(),Y=Q.getValueSchema().isTimestampSchema()?I:lo1.quoteHeader(I);if(G!=="")G+=", ";G+=Y}this.stringBuffer=G;return}this.stringBuffer=JSON.stringify(B,null,2);break;case"string":let D=Q.getMergedTraits().mediaType,Z=B;if(D){if(D==="application/json"||D.endsWith("+json"))Z=lo1.LazyJsonString.from(Z);if(Q.getMergedTraits().httpHeader){this.stringBuffer=(this.serdeContext?.base64Encoder??GHA.toBase64)(Z.toString());return}}this.stringBuffer=B;break;default:this.stringBuffer=String(B)}}flush(){let A=this.stringBuffer;return this.stringBuffer="",A}},uWQ=class{constructor(A,B,Q=new XHA(B)){this.codecSerializer=A,this.stringSerializer=Q}setSerdeContext(A){this.codecSerializer.setSerdeContext(A),this.stringSerializer.setSerdeContext(A)}write(A,B){let Q=gWQ.NormalizedSchema.of(A),D=Q.getMergedTraits();if(D.httpHeader||D.httpLabel||D.httpQuery){this.stringSerializer.write(Q,B),this.buffer=this.stringSerializer.flush();return}return this.codecSerializer.write(Q,B)}flush(){if(this.buffer!==void 0){let A=this.buffer;return this.buffer=void 0,A}return this.codecSerializer.flush()}}});
var $7=E((m35,lHA)=>{var{defineProperty:tz1,getOwnPropertyDescriptor:NJQ,getOwnPropertyNames:LJQ}=Object,MJQ=Object.prototype.hasOwnProperty,f5=(A,B)=>tz1(A,"name",{value:B,configurable:!0}),RJQ=(A,B)=>{for(var Q in B)tz1(A,Q,{get:B[Q],enumerable:!0})},OJQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of LJQ(B))if(!MJQ.call(A,Z)&&Z!==Q)tz1(A,Z,{get:()=>B[Z],enumerable:!(D=NJQ(B,Z))||D.enumerable})}return A},TJQ=(A)=>OJQ(tz1({},"__esModule",{value:!0}),A),bHA={};RJQ(bHA,{EndpointCache:()=>PJQ,EndpointError:()=>WV,customEndpointFunctions:()=>ro1,isIpAddress:()=>fHA,isValidHostLabel:()=>to1,resolveEndpoint:()=>nJQ});lHA.exports=TJQ(bHA);var PJQ=class{constructor({size:A,params:B}){if(this.data=new Map,this.parameters=[],this.capacity=A??50,B)this.parameters=B}static{f5(this,"EndpointCache")}get(A,B){let Q=this.hash(A);if(Q===!1)return B();if(!this.data.has(Q)){if(this.data.size>this.capacity+10){let D=this.data.keys(),Z=0;while(!0){let{value:G,done:F}=D.next();if(this.data.delete(G),F||++Z>10)break}}this.data.set(Q,B())}return this.data.get(Q)}size(){return this.data.size}hash(A){let B="",{parameters:Q}=this;if(Q.length===0)return!1;for(let D of Q){let Z=String(A[D]??"");if(Z.includes("|;"))return!1;B+=Z+"|;"}return B}},SJQ=new RegExp("^(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}$"),fHA=f5((A)=>SJQ.test(A)||A.startsWith("[")&&A.endsWith("]"),"isIpAddress"),jJQ=new RegExp("^(?!.*-$)(?!-)[a-zA-Z0-9-]{1,63}$"),to1=f5((A,B=!1)=>{if(!B)return jJQ.test(A);let Q=A.split(".");for(let D of Q)if(!to1(D))return!1;return!0},"isValidHostLabel"),ro1={},vQ1="endpoints";function bO(A){if(typeof A!=="object"||A==null)return A;if("ref"in A)return`$${bO(A.ref)}`;if("fn"in A)return`${A.fn}(${(A.argv||[]).map(bO).join(", ")})`;return JSON.stringify(A,null,2)}f5(bO,"toDebugString");var WV=class extends Error{static{f5(this,"EndpointError")}constructor(A){super(A);this.name="EndpointError"}},kJQ=f5((A,B)=>A===B,"booleanEquals"),yJQ=f5((A)=>{let B=A.split("."),Q=[];for(let D of B){let Z=D.indexOf("[");if(Z!==-1){if(D.indexOf("]")!==D.length-1)throw new WV(`Path: '${A}' does not end with ']'`);let G=D.slice(Z+1,-1);if(Number.isNaN(parseInt(G)))throw new WV(`Invalid array index: '${G}' in path: '${A}'`);if(Z!==0)Q.push(D.slice(0,Z));Q.push(G)}else Q.push(D)}return Q},"getAttrPathList"),hHA=f5((A,B)=>yJQ(B).reduce((Q,D)=>{if(typeof Q!=="object")throw new WV(`Index '${D}' in '${B}' not found in '${JSON.stringify(A)}'`);else if(Array.isArray(Q))return Q[parseInt(D)];return Q[D]},A),"getAttr"),_JQ=f5((A)=>A!=null,"isSet"),xJQ=f5((A)=>!A,"not"),oo1=vHA(),so1={[oo1.EndpointURLScheme.HTTP]:80,[oo1.EndpointURLScheme.HTTPS]:443},vJQ=f5((A)=>{let B=(()=>{try{if(A instanceof URL)return A;if(typeof A==="object"&&"hostname"in A){let{hostname:V,port:C,protocol:K="",path:H="",query:z={}}=A,$=new URL(`${K}//${V}${C?`:${C}`:""}${H}`);return $.search=Object.entries(z).map(([L,N])=>`${L}=${N}`).join("&"),$}return new URL(A)}catch(V){return null}})();if(!B)return console.error(`Unable to parse ${JSON.stringify(A)} as a whatwg URL.`),null;let Q=B.href,{host:D,hostname:Z,pathname:G,protocol:F,search:I}=B;if(I)return null;let Y=F.slice(0,-1);if(!Object.values(oo1.EndpointURLScheme).includes(Y))return null;let W=fHA(Z),J=Q.includes(`${D}:${so1[Y]}`)||typeof A==="string"&&A.includes(`${D}:${so1[Y]}`),X=`${D}${J?`:${so1[Y]}`:""}`;return{scheme:Y,authority:X,path:G,normalizedPath:G.endsWith("/")?G:`${G}/`,isIp:W}},"parseURL"),bJQ=f5((A,B)=>A===B,"stringEquals"),fJQ=f5((A,B,Q,D)=>{if(B>=Q||A.length<Q)return null;if(!D)return A.substring(B,Q);return A.substring(A.length-Q,A.length-B)},"substring"),hJQ=f5((A)=>encodeURIComponent(A).replace(/[!*'()]/g,(B)=>`%${B.charCodeAt(0).toString(16).toUpperCase()}`),"uriEncode"),gJQ={booleanEquals:kJQ,getAttr:hHA,isSet:_JQ,isValidHostLabel:to1,not:xJQ,parseURL:vJQ,stringEquals:bJQ,substring:fJQ,uriEncode:hJQ},gHA=f5((A,B)=>{let Q=[],D={...B.endpointParams,...B.referenceRecord},Z=0;while(Z<A.length){let G=A.indexOf("{",Z);if(G===-1){Q.push(A.slice(Z));break}Q.push(A.slice(Z,G));let F=A.indexOf("}",G);if(F===-1){Q.push(A.slice(G));break}if(A[G+1]==="{"&&A[F+1]==="}")Q.push(A.slice(G+1,F)),Z=F+2;let I=A.substring(G+1,F);if(I.includes("#")){let[Y,W]=I.split("#");Q.push(hHA(D[Y],W))}else Q.push(D[I]);Z=F+1}return Q.join("")},"evaluateTemplate"),uJQ=f5(({ref:A},B)=>{return{...B.endpointParams,...B.referenceRecord}[A]},"getReferenceValue"),ez1=f5((A,B,Q)=>{if(typeof A==="string")return gHA(A,Q);else if(A.fn)return uHA(A,Q);else if(A.ref)return uJQ(A,Q);throw new WV(`'${B}': ${String(A)} is not a string, function or reference.`)},"evaluateExpression"),uHA=f5(({fn:A,argv:B},Q)=>{let D=B.map((G)=>["boolean","number"].includes(typeof G)?G:ez1(G,"arg",Q)),Z=A.split(".");if(Z[0]in ro1&&Z[1]!=null)return ro1[Z[0]][Z[1]](...D);return gJQ[A](...D)},"callFunction"),mJQ=f5(({assign:A,...B},Q)=>{if(A&&A in Q.referenceRecord)throw new WV(`'${A}' is already defined in Reference Record.`);let D=uHA(B,Q);return Q.logger?.debug?.(`${vQ1} evaluateCondition: ${bO(B)} = ${bO(D)}`),{result:D===""?!0:!!D,...A!=null&&{toAssign:{name:A,value:D}}}},"evaluateCondition"),eo1=f5((A=[],B)=>{let Q={};for(let D of A){let{result:Z,toAssign:G}=mJQ(D,{...B,referenceRecord:{...B.referenceRecord,...Q}});if(!Z)return{result:Z};if(G)Q[G.name]=G.value,B.logger?.debug?.(`${vQ1} assign: ${G.name} := ${bO(G.value)}`)}return{result:!0,referenceRecord:Q}},"evaluateConditions"),dJQ=f5((A,B)=>Object.entries(A).reduce((Q,[D,Z])=>({...Q,[D]:Z.map((G)=>{let F=ez1(G,"Header value entry",B);if(typeof F!=="string")throw new WV(`Header '${D}' value '${F}' is not a string`);return F})}),{}),"getEndpointHeaders"),mHA=f5((A,B)=>{if(Array.isArray(A))return A.map((Q)=>mHA(Q,B));switch(typeof A){case"string":return gHA(A,B);case"object":if(A===null)throw new WV(`Unexpected endpoint property: ${A}`);return dHA(A,B);case"boolean":return A;default:throw new WV(`Unexpected endpoint property type: ${typeof A}`)}},"getEndpointProperty"),dHA=f5((A,B)=>Object.entries(A).reduce((Q,[D,Z])=>({...Q,[D]:mHA(Z,B)}),{}),"getEndpointProperties"),cJQ=f5((A,B)=>{let Q=ez1(A,"Endpoint URL",B);if(typeof Q==="string")try{return new URL(Q)}catch(D){throw console.error(`Failed to construct URL with ${Q}`,D),D}throw new WV(`Endpoint URL must be a string, got ${typeof Q}`)},"getEndpointUrl"),lJQ=f5((A,B)=>{let{conditions:Q,endpoint:D}=A,{result:Z,referenceRecord:G}=eo1(Q,B);if(!Z)return;let F={...B,referenceRecord:{...B.referenceRecord,...G}},{url:I,properties:Y,headers:W}=D;return B.logger?.debug?.(`${vQ1} Resolving endpoint from template: ${bO(D)}`),{...W!=null&&{headers:dJQ(W,F)},...Y!=null&&{properties:dHA(Y,F)},url:cJQ(I,F)}},"evaluateEndpointRule"),pJQ=f5((A,B)=>{let{conditions:Q,error:D}=A,{result:Z,referenceRecord:G}=eo1(Q,B);if(!Z)return;throw new WV(ez1(D,"Error",{...B,referenceRecord:{...B.referenceRecord,...G}}))},"evaluateErrorRule"),iJQ=f5((A,B)=>{let{conditions:Q,rules:D}=A,{result:Z,referenceRecord:G}=eo1(Q,B);if(!Z)return;return cHA(D,{...B,referenceRecord:{...B.referenceRecord,...G}})},"evaluateTreeRule"),cHA=f5((A,B)=>{for(let Q of A)if(Q.type==="endpoint"){let D=lJQ(Q,B);if(D)return D}else if(Q.type==="error")pJQ(Q,B);else if(Q.type==="tree"){let D=iJQ(Q,B);if(D)return D}else throw new WV(`Unknown endpoint rule: ${Q}`);throw new WV("Rules evaluation failed")},"evaluateRules"),nJQ=f5((A,B)=>{let{endpointParams:Q,logger:D}=B,{parameters:Z,rules:G}=A;B.logger?.debug?.(`${vQ1} Initial EndpointParams: ${bO(Q)}`);let F=Object.entries(Z).filter(([,W])=>W.default!=null).map(([W,J])=>[W,J.default]);if(F.length>0)for(let[W,J]of F)Q[W]=Q[W]??J;let I=Object.entries(Z).filter(([,W])=>W.required).map(([W])=>W);for(let W of I)if(Q[W]==null)throw new WV(`Missing required parameter: '${W}'`);let Y=cHA(G,{endpointParams:Q,logger:D,referenceRecord:{}});return B.logger?.debug?.(`${vQ1} Resolved endpoint: ${bO(Y)}`),Y},"resolveEndpoint")});
var $F=E((eD5,JLA)=>{var{defineProperty:ZU1,getOwnPropertyDescriptor:RNQ,getOwnPropertyNames:ONQ}=Object,TNQ=Object.prototype.hasOwnProperty,SG=(A,B)=>ZU1(A,"name",{value:B,configurable:!0}),PNQ=(A,B)=>{for(var Q in B)ZU1(A,Q,{get:B[Q],enumerable:!0})},SNQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ONQ(B))if(!TNQ.call(A,Z)&&Z!==Q)ZU1(A,Z,{get:()=>B[Z],enumerable:!(D=RNQ(B,Z))||D.enumerable})}return A},jNQ=(A)=>SNQ(ZU1({},"__esModule",{value:!0}),A),BLA={};PNQ(BLA,{DEFAULT_MAX_RETRIES:()=>GLA,DEFAULT_TIMEOUT:()=>ZLA,ENV_CMDS_AUTH_TOKEN:()=>Oe1,ENV_CMDS_FULL_URI:()=>QU1,ENV_CMDS_RELATIVE_URI:()=>DU1,Endpoint:()=>FLA,fromContainerMetadata:()=>xNQ,fromInstanceMetadata:()=>BLQ,getInstanceMetadataEndpoint:()=>YLA,httpRequest:()=>$n,providerConfigFromInit:()=>Te1});JLA.exports=jNQ(BLA);var kNQ=J1("url"),dN=eB(),yNQ=J1("buffer"),_NQ=J1("http");function $n(A){return new Promise((B,Q)=>{let D=_NQ.request({method:"GET",...A,hostname:A.hostname?.replace(/^\[(.+)\]$/,"$1")});D.on("error",(Z)=>{Q(Object.assign(new dN.ProviderError("Unable to connect to instance metadata service"),Z)),D.destroy()}),D.on("timeout",()=>{Q(new dN.ProviderError("TimeoutError from instance metadata service")),D.destroy()}),D.on("response",(Z)=>{let{statusCode:G=400}=Z;if(G<200||300<=G)Q(Object.assign(new dN.ProviderError("Error response received from instance metadata service"),{statusCode:G})),D.destroy();let F=[];Z.on("data",(I)=>{F.push(I)}),Z.on("end",()=>{B(yNQ.Buffer.concat(F)),D.destroy()})}),D.end()})}SG($n,"httpRequest");var QLA=SG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.AccessKeyId==="string"&&typeof A.SecretAccessKey==="string"&&typeof A.Token==="string"&&typeof A.Expiration==="string","isImdsCredentials"),DLA=SG((A)=>({accessKeyId:A.AccessKeyId,secretAccessKey:A.SecretAccessKey,sessionToken:A.Token,expiration:new Date(A.Expiration),...A.AccountId&&{accountId:A.AccountId}}),"fromImdsCredentials"),ZLA=1000,GLA=0,Te1=SG(({maxRetries:A=GLA,timeout:B=ZLA})=>({maxRetries:A,timeout:B}),"providerConfigFromInit"),Re1=SG((A,B)=>{let Q=A();for(let D=0;D<B;D++)Q=Q.catch(A);return Q},"retry"),QU1="AWS_CONTAINER_CREDENTIALS_FULL_URI",DU1="AWS_CONTAINER_CREDENTIALS_RELATIVE_URI",Oe1="AWS_CONTAINER_AUTHORIZATION_TOKEN",xNQ=SG((A={})=>{let{timeout:B,maxRetries:Q}=Te1(A);return()=>Re1(async()=>{let D=await gNQ({logger:A.logger}),Z=JSON.parse(await vNQ(B,D));if(!QLA(Z))throw new dN.CredentialsProviderError("Invalid response received from instance metadata service.",{logger:A.logger});return DLA(Z)},Q)},"fromContainerMetadata"),vNQ=SG(async(A,B)=>{if(process.env[Oe1])B.headers={...B.headers,Authorization:process.env[Oe1]};return(await $n({...B,timeout:A})).toString()},"requestFromEcsImds"),bNQ="*************",fNQ={localhost:!0,"127.0.0.1":!0},hNQ={"http:":!0,"https:":!0},gNQ=SG(async({logger:A})=>{if(process.env[DU1])return{hostname:bNQ,path:process.env[DU1]};if(process.env[QU1]){let B=kNQ.parse(process.env[QU1]);if(!B.hostname||!(B.hostname in fNQ))throw new dN.CredentialsProviderError(`${B.hostname} is not a valid container metadata service hostname`,{tryNextLink:!1,logger:A});if(!B.protocol||!(B.protocol in hNQ))throw new dN.CredentialsProviderError(`${B.protocol} is not a valid container metadata service protocol`,{tryNextLink:!1,logger:A});return{...B,port:B.port?parseInt(B.port,10):void 0}}throw new dN.CredentialsProviderError(`The container metadata credential provider cannot be used unless the ${DU1} or ${QU1} environment variable is set`,{tryNextLink:!1,logger:A})},"getCmdsUri"),uNQ=class A extends dN.CredentialsProviderError{constructor(B,Q=!0){super(B,Q);this.tryNextLink=Q,this.name="InstanceMetadataV1FallbackError",Object.setPrototypeOf(this,A.prototype)}static{SG(this,"InstanceMetadataV1FallbackError")}},Pe1=QD(),mNQ=BZ(),FLA=((A)=>{return A.IPv4="http://***************",A.IPv6="http://[fd00:ec2::254]",A})(FLA||{}),dNQ="AWS_EC2_METADATA_SERVICE_ENDPOINT",cNQ="ec2_metadata_service_endpoint",lNQ={environmentVariableSelector:(A)=>A[dNQ],configFileSelector:(A)=>A[cNQ],default:void 0},ILA=((A)=>{return A.IPv4="IPv4",A.IPv6="IPv6",A})(ILA||{}),pNQ="AWS_EC2_METADATA_SERVICE_ENDPOINT_MODE",iNQ="ec2_metadata_service_endpoint_mode",nNQ={environmentVariableSelector:(A)=>A[pNQ],configFileSelector:(A)=>A[iNQ],default:"IPv4"},YLA=SG(async()=>mNQ.parseUrl(await aNQ()||await sNQ()),"getInstanceMetadataEndpoint"),aNQ=SG(async()=>Pe1.loadConfig(lNQ)(),"getFromEndpointConfig"),sNQ=SG(async()=>{let A=await Pe1.loadConfig(nNQ)();switch(A){case"IPv4":return"http://***************";case"IPv6":return"http://[fd00:ec2::254]";default:throw new Error(`Unsupported endpoint mode: ${A}. Select from ${Object.values(ILA)}`)}},"getFromEndpointModeConfig"),rNQ=300,oNQ=300,tNQ="https://docs.aws.amazon.com/sdkref/latest/guide/feature-static-credentials.html",tNA=SG((A,B)=>{let Q=rNQ+Math.floor(Math.random()*oNQ),D=new Date(Date.now()+Q*1000);B.warn(`Attempting credential expiration extension due to a credential service availability issue. A refresh of these credentials will be attempted after ${new Date(D)}.
For more information, please visit: `+tNQ);let Z=A.originalExpiration??A.expiration;return{...A,...Z?{originalExpiration:Z}:{},expiration:D}},"getExtendedInstanceMetadataCredentials"),eNQ=SG((A,B={})=>{let Q=B?.logger||console,D;return async()=>{let Z;try{if(Z=await A(),Z.expiration&&Z.expiration.getTime()<Date.now())Z=tNA(Z,Q)}catch(G){if(D)Q.warn("Credential renew failed: ",G),Z=tNA(D,Q);else throw G}return D=Z,Z}},"staticStabilityProvider"),WLA="/latest/meta-data/iam/security-credentials/",ALQ="/latest/api/token",Me1="AWS_EC2_METADATA_V1_DISABLED",eNA="ec2_metadata_v1_disabled",ALA="x-aws-ec2-metadata-token",BLQ=SG((A={})=>eNQ(QLQ(A),{logger:A.logger}),"fromInstanceMetadata"),QLQ=SG((A={})=>{let B=!1,{logger:Q,profile:D}=A,{timeout:Z,maxRetries:G}=Te1(A),F=SG(async(I,Y)=>{if(B||Y.headers?.[ALA]==null){let X=!1,V=!1,C=await Pe1.loadConfig({environmentVariableSelector:(K)=>{let H=K[Me1];if(V=!!H&&H!=="false",H===void 0)throw new dN.CredentialsProviderError(`${Me1} not set in env, checking config file next.`,{logger:A.logger});return V},configFileSelector:(K)=>{let H=K[eNA];return X=!!H&&H!=="false",X},default:!1},{profile:D})();if(A.ec2MetadataV1Disabled||C){let K=[];if(A.ec2MetadataV1Disabled)K.push("credential provider initialization (runtime option ec2MetadataV1Disabled)");if(X)K.push(`config file profile (${eNA})`);if(V)K.push(`process environment variable (${Me1})`);throw new uNQ(`AWS EC2 Metadata v1 fallback has been blocked by AWS SDK configuration in the following: [${K.join(", ")}].`)}}let J=(await Re1(async()=>{let X;try{X=await ZLQ(Y)}catch(V){if(V.statusCode===401)B=!1;throw V}return X},I)).trim();return Re1(async()=>{let X;try{X=await GLQ(J,Y,A)}catch(V){if(V.statusCode===401)B=!1;throw V}return X},I)},"getCredentials");return async()=>{let I=await YLA();if(B)return Q?.debug("AWS SDK Instance Metadata","using v1 fallback (no token fetch)"),F(G,{...I,timeout:Z});else{let Y;try{Y=(await DLQ({...I,timeout:Z})).toString()}catch(W){if(W?.statusCode===400)throw Object.assign(W,{message:"EC2 Metadata token request returned error"});else if(W.message==="TimeoutError"||[403,404,405].includes(W.statusCode))B=!0;return Q?.debug("AWS SDK Instance Metadata","using v1 fallback (initial)"),F(G,{...I,timeout:Z})}return F(G,{...I,headers:{[ALA]:Y},timeout:Z})}}},"getInstanceMetadataProvider"),DLQ=SG(async(A)=>$n({...A,path:ALQ,method:"PUT",headers:{"x-aws-ec2-metadata-token-ttl-seconds":"21600"}}),"getMetadataToken"),ZLQ=SG(async(A)=>(await $n({...A,path:WLA})).toString(),"getProfile"),GLQ=SG(async(A,B,Q)=>{let D=JSON.parse((await $n({...B,path:WLA+A})).toString());if(!QLA(D))throw new dN.CredentialsProviderError("Invalid response received from instance metadata service.",{logger:Q.logger});return DLA(D)},"getCredentialsFromProfile")});
var $SA=E((USA)=>{Object.defineProperty(USA,"__esModule",{value:!0});USA.getRuntimeConfig=void 0;var djQ=Sh(),cjQ=djQ.__importDefault(cNA()),B00=ZI(),KSA=vPA(),HSA=tQ1(),SU1=V4(),ljQ=VB(),pjQ=jG(),zSA=v4(),ch=QD(),ESA=S3(),ijQ=kG(),njQ=hZ(),ajQ=CSA(),sjQ=x4(),rjQ=yG(),ojQ=x4(),tjQ=(A)=>{ojQ.emitWarningIfUnsupportedVersion(process.version);let B=rjQ.resolveDefaultsModeConfig(A),Q=()=>B().then(sjQ.loadConfigsForDefaultMode),D=ajQ.getRuntimeConfig(A);B00.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??ch.loadConfig(B00.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??ijQ.calculateBodyLength,credentialDefaultProvider:A?.credentialDefaultProvider??KSA.defaultProvider,defaultUserAgentProvider:A?.defaultUserAgentProvider??HSA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:cjQ.default.version}),httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(G)=>G.getIdentityProvider("aws.auth#sigv4")||(async(F)=>await KSA.defaultProvider(F?.__config||{})()),signer:new B00.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(G)=>G.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new ljQ.NoAuthSigner}],maxAttempts:A?.maxAttempts??ch.loadConfig(zSA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??ch.loadConfig(SU1.NODE_REGION_CONFIG_OPTIONS,{...SU1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:ESA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??ch.loadConfig({...zSA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||njQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??pjQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??ESA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??ch.loadConfig(SU1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??ch.loadConfig(SU1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??ch.loadConfig(HSA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};USA.getRuntimeConfig=tjQ});
var $TA=E((UTA)=>{Object.defineProperty(UTA,"__esModule",{value:!0});UTA.resolveHttpAuthRuntimeConfig=UTA.getHttpAuthExtensionConfiguration=void 0;var HPQ=(A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}};UTA.getHttpAuthExtensionConfiguration=HPQ;var zPQ=(A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}};UTA.resolveHttpAuthRuntimeConfig=zPQ});
var A10=E((vZ5,POA)=>{var{defineProperty:NU1,getOwnPropertyDescriptor:OTQ,getOwnPropertyNames:NOA}=Object,TTQ=Object.prototype.hasOwnProperty,LU1=(A,B)=>NU1(A,"name",{value:B,configurable:!0}),PTQ=(A,B)=>function Q(){return A&&(B=A[NOA(A)[0]](A=0)),B},LOA=(A,B)=>{for(var Q in B)NU1(A,Q,{get:B[Q],enumerable:!0})},STQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of NOA(B))if(!TTQ.call(A,Z)&&Z!==Q)NU1(A,Z,{get:()=>B[Z],enumerable:!(D=OTQ(B,Z))||D.enumerable})}return A},jTQ=(A)=>STQ(NU1({},"__esModule",{value:!0}),A),MOA={};LOA(MOA,{GetRoleCredentialsCommand:()=>ee1.GetRoleCredentialsCommand,SSOClient:()=>ee1.SSOClient});var ee1,kTQ=PTQ({"src/loadSso.ts"(){ee1=ZRA()}}),ROA={};LOA(ROA,{fromSSO:()=>_TQ,isSsoProfile:()=>OOA,validateSsoProfile:()=>TOA});POA.exports=jTQ(ROA);var OOA=LU1((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),$OA=Qz(),yTQ=wOA(),Nw=eB(),qU1=e5(),I41=!1,qOA=LU1(async({ssoStartUrl:A,ssoSession:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoClient:G,clientConfig:F,parentClientConfig:I,profile:Y,logger:W})=>{let J,X="To refresh this SSO session run aws sso login with the corresponding profile.";if(B)try{let f=await yTQ.fromSso({profile:Y})();J={accessToken:f.token,expiresAt:new Date(f.expiration).toISOString()}}catch(f){throw new Nw.CredentialsProviderError(f.message,{tryNextLink:I41,logger:W})}else try{J=await qU1.getSSOTokenFromFile(A)}catch(f){throw new Nw.CredentialsProviderError("The SSO session associated with this profile is invalid. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:I41,logger:W})}if(new Date(J.expiresAt).getTime()-Date.now()<=0)throw new Nw.CredentialsProviderError("The SSO session associated with this profile has expired. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:I41,logger:W});let{accessToken:V}=J,{SSOClient:C,GetRoleCredentialsCommand:K}=await Promise.resolve().then(()=>(kTQ(),MOA)),H=G||new C(Object.assign({},F??{},{logger:F?.logger??I?.logger,region:F?.region??D})),z;try{z=await H.send(new K({accountId:Q,roleName:Z,accessToken:V}))}catch(f){throw new Nw.CredentialsProviderError(f,{tryNextLink:I41,logger:W})}let{roleCredentials:{accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:O,credentialScope:R,accountId:T}={}}=z;if(!$||!L||!N||!O)throw new Nw.CredentialsProviderError("SSO returns an invalid temporary credential.",{tryNextLink:I41,logger:W});let j={accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:new Date(O),...R&&{credentialScope:R},...T&&{accountId:T}};if(B)$OA.setCredentialFeature(j,"CREDENTIALS_SSO","s");else $OA.setCredentialFeature(j,"CREDENTIALS_SSO_LEGACY","u");return j},"resolveSSOCredentials"),TOA=LU1((A,B)=>{let{sso_start_url:Q,sso_account_id:D,sso_region:Z,sso_role_name:G}=A;if(!Q||!D||!Z||!G)throw new Nw.CredentialsProviderError(`Profile is configured with invalid SSO credentials. Required parameters "sso_account_id", "sso_region", "sso_role_name", "sso_start_url". Got ${Object.keys(A).join(", ")}
Reference: https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.html`,{tryNextLink:!1,logger:B});return A},"validateSsoProfile"),_TQ=LU1((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-sso - fromSSO");let{ssoStartUrl:Q,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoSession:F}=A,{ssoClient:I}=A,Y=qU1.getProfileName({profile:A.profile??B?.profile});if(!Q&&!D&&!Z&&!G&&!F){let J=(await qU1.parseKnownFiles(A))[Y];if(!J)throw new Nw.CredentialsProviderError(`Profile ${Y} was not found.`,{logger:A.logger});if(!OOA(J))throw new Nw.CredentialsProviderError(`Profile ${Y} is not configured with SSO credentials.`,{logger:A.logger});if(J?.sso_session){let $=(await qU1.loadSsoSessionData(A))[J.sso_session],L=` configurations in profile ${Y} and sso-session ${J.sso_session}`;if(Z&&Z!==$.sso_region)throw new Nw.CredentialsProviderError("Conflicting SSO region"+L,{tryNextLink:!1,logger:A.logger});if(Q&&Q!==$.sso_start_url)throw new Nw.CredentialsProviderError("Conflicting SSO start_url"+L,{tryNextLink:!1,logger:A.logger});J.sso_region=$.sso_region,J.sso_start_url=$.sso_start_url}let{sso_start_url:X,sso_account_id:V,sso_region:C,sso_role_name:K,sso_session:H}=TOA(J,A.logger);return qOA({ssoStartUrl:X,ssoSession:H,ssoAccountId:V,ssoRegion:C,ssoRoleName:K,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})}else if(!Q||!D||!Z||!G)throw new Nw.CredentialsProviderError('Incomplete configuration. The fromSSO() argument hash must include "ssoStartUrl", "ssoAccountId", "ssoRegion", "ssoRoleName"',{tryNextLink:!1,logger:A.logger});else return qOA({ssoStartUrl:Q,ssoSession:F,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})},"fromSSO")});
var ACA=E((d55,eVA)=>{var{defineProperty:Tz1,getOwnPropertyDescriptor:_GQ,getOwnPropertyNames:xGQ}=Object,vGQ=Object.prototype.hasOwnProperty,Pz1=(A,B)=>Tz1(A,"name",{value:B,configurable:!0}),bGQ=(A,B)=>{for(var Q in B)Tz1(A,Q,{get:B[Q],enumerable:!0})},fGQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of xGQ(B))if(!vGQ.call(A,Z)&&Z!==Q)Tz1(A,Z,{get:()=>B[Z],enumerable:!(D=_GQ(B,Z))||D.enumerable})}return A},hGQ=(A)=>fGQ(Tz1({},"__esModule",{value:!0}),A),pVA={};bGQ(pVA,{AlgorithmId:()=>sVA,EndpointURLScheme:()=>aVA,FieldPosition:()=>rVA,HttpApiKeyAuthLocation:()=>nVA,HttpAuthLocation:()=>iVA,IniSectionType:()=>oVA,RequestHandlerProtocol:()=>tVA,SMITHY_CONTEXT_KEY:()=>cGQ,getDefaultClientConfiguration:()=>mGQ,resolveDefaultRuntimeConfig:()=>dGQ});eVA.exports=hGQ(pVA);var iVA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(iVA||{}),nVA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(nVA||{}),aVA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(aVA||{}),sVA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(sVA||{}),gGQ=Pz1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),uGQ=Pz1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),mGQ=Pz1((A)=>{return gGQ(A)},"getDefaultClientConfiguration"),dGQ=Pz1((A)=>{return uGQ(A)},"resolveDefaultRuntimeConfig"),rVA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(rVA||{}),cGQ="__smithy_context",oVA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(oVA||{}),tVA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(tVA||{})});
var AD=E((O55,oXA)=>{var{defineProperty:Lz1,getOwnPropertyDescriptor:TZQ,getOwnPropertyNames:PZQ}=Object,SZQ=Object.prototype.hasOwnProperty,sXA=(A,B)=>Lz1(A,"name",{value:B,configurable:!0}),jZQ=(A,B)=>{for(var Q in B)Lz1(A,Q,{get:B[Q],enumerable:!0})},kZQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of PZQ(B))if(!SZQ.call(A,Z)&&Z!==Q)Lz1(A,Z,{get:()=>B[Z],enumerable:!(D=TZQ(B,Z))||D.enumerable})}return A},yZQ=(A)=>kZQ(Lz1({},"__esModule",{value:!0}),A),rXA={};jZQ(rXA,{fromArrayBuffer:()=>xZQ,fromString:()=>vZQ});oXA.exports=yZQ(rXA);var _ZQ=aXA(),Fo1=J1("buffer"),xZQ=sXA((A,B=0,Q=A.byteLength-B)=>{if(!_ZQ.isArrayBuffer(A))throw new TypeError(`The "input" argument must be ArrayBuffer. Received type ${typeof A} (${A})`);return Fo1.Buffer.from(A,B,Q)},"fromArrayBuffer"),vZQ=sXA((A,B)=>{if(typeof A!=="string")throw new TypeError(`The "input" argument must be of type string. Received type ${typeof A} (${A})`);return B?Fo1.Buffer.from(A,B):Fo1.Buffer.from(A)},"fromString")});
var AVA=E((tXA)=>{Object.defineProperty(tXA,"__esModule",{value:!0});tXA.fromBase64=void 0;var bZQ=AD(),fZQ=/^[A-Za-z0-9+/]*={0,2}$/,hZQ=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!fZQ.exec(A))throw new TypeError("Invalid base64 string.");let B=bZQ.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};tXA.fromBase64=hZQ});
var Ae1=E((ZqA)=>{Object.defineProperty(ZqA,"__esModule",{value:!0});ZqA.default=wwQ;var EwQ=UwQ(J1("crypto"));function UwQ(A){return A&&A.__esModule?A:{default:A}}var rE1=new Uint8Array(256),sE1=rE1.length;function wwQ(){if(sE1>rE1.length-16)EwQ.default.randomFillSync(rE1),sE1=0;return rE1.slice(sE1,sE1+=16)}});
var B41=E((zZ5,jMA)=>{var{defineProperty:XU1,getOwnPropertyDescriptor:DRQ,getOwnPropertyNames:ZRQ}=Object,GRQ=Object.prototype.hasOwnProperty,cN=(A,B)=>XU1(A,"name",{value:B,configurable:!0}),FRQ=(A,B)=>{for(var Q in B)XU1(A,Q,{get:B[Q],enumerable:!0})},IRQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ZRQ(B))if(!GRQ.call(A,Z)&&Z!==Q)XU1(A,Z,{get:()=>B[Z],enumerable:!(D=DRQ(B,Z))||D.enumerable})}return A},YRQ=(A)=>IRQ(XU1({},"__esModule",{value:!0}),A),OMA={};FRQ(OMA,{NODE_REGION_CONFIG_FILE_OPTIONS:()=>VRQ,NODE_REGION_CONFIG_OPTIONS:()=>XRQ,REGION_ENV_NAME:()=>TMA,REGION_INI_NAME:()=>PMA,getAwsRegionExtensionConfiguration:()=>WRQ,resolveAwsRegionExtensionConfiguration:()=>JRQ,resolveRegionConfig:()=>CRQ});jMA.exports=YRQ(OMA);var WRQ=cN((A)=>{return{setRegion(B){A.region=B},region(){return A.region}}},"getAwsRegionExtensionConfiguration"),JRQ=cN((A)=>{return{region:A.region()}},"resolveAwsRegionExtensionConfiguration"),TMA="AWS_REGION",PMA="region",XRQ={environmentVariableSelector:cN((A)=>A[TMA],"environmentVariableSelector"),configFileSelector:cN((A)=>A[PMA],"configFileSelector"),default:cN(()=>{throw new Error("Region is missing")},"default")},VRQ={preferredFile:"credentials"},SMA=cN((A)=>typeof A==="string"&&(A.startsWith("fips-")||A.endsWith("-fips")),"isFipsRegion"),RMA=cN((A)=>SMA(A)?["fips-aws-global","aws-fips"].includes(A)?"us-east-1":A.replace(/fips-(dkr-|prod-)?|-fips/,""):A,"getRealRegion"),CRQ=cN((A)=>{let{region:B,useFipsEndpoint:Q}=A;if(!B)throw new Error("Region is missing");return Object.assign(A,{region:cN(async()=>{if(typeof B==="string")return RMA(B);let D=await B();return RMA(D)},"region"),useFipsEndpoint:cN(async()=>{let D=typeof B==="string"?B:await B();if(SMA(D))return!0;return typeof Q!=="function"?Promise.resolve(!!Q):Q()},"useFipsEndpoint")})},"resolveRegionConfig")});
var BZ=E((XD5,v$A)=>{var{defineProperty:cE1,getOwnPropertyDescriptor:$UQ,getOwnPropertyNames:qUQ}=Object,NUQ=Object.prototype.hasOwnProperty,LUQ=(A,B)=>cE1(A,"name",{value:B,configurable:!0}),MUQ=(A,B)=>{for(var Q in B)cE1(A,Q,{get:B[Q],enumerable:!0})},RUQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of qUQ(B))if(!NUQ.call(A,Z)&&Z!==Q)cE1(A,Z,{get:()=>B[Z],enumerable:!(D=$UQ(B,Z))||D.enumerable})}return A},OUQ=(A)=>RUQ(cE1({},"__esModule",{value:!0}),A),_$A={};MUQ(_$A,{parseUrl:()=>x$A});v$A.exports=OUQ(_$A);var TUQ=y$A(),x$A=LUQ((A)=>{if(typeof A==="string")return x$A(new URL(A));let{hostname:B,pathname:Q,port:D,protocol:Z,search:G}=A,F;if(G)F=TUQ.parseQueryString(G);return{hostname:B,port:D?parseInt(D):void 0,protocol:Z,path:Q,query:F}},"parseUrl")});
var Bn=E((j55,Rz1)=>{var{defineProperty:YVA,getOwnPropertyDescriptor:rZQ,getOwnPropertyNames:oZQ}=Object,tZQ=Object.prototype.hasOwnProperty,Yo1=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of oZQ(B))if(!tZQ.call(A,Z)&&Z!==Q)YVA(A,Z,{get:()=>B[Z],enumerable:!(D=rZQ(B,Z))||D.enumerable})}return A},WVA=(A,B,Q)=>(Yo1(A,B,"default"),Q&&Yo1(Q,B,"default")),eZQ=(A)=>Yo1(YVA({},"__esModule",{value:!0}),A),Wo1={};Rz1.exports=eZQ(Wo1);WVA(Wo1,AVA(),Rz1.exports);WVA(Wo1,IVA(),Rz1.exports)});
var Bo1=E((Z55,jJA)=>{var{defineProperty:Dz1,getOwnPropertyDescriptor:I7Q,getOwnPropertyNames:Y7Q}=Object,W7Q=Object.prototype.hasOwnProperty,Zz1=(A,B)=>Dz1(A,"name",{value:B,configurable:!0}),J7Q=(A,B)=>{for(var Q in B)Dz1(A,Q,{get:B[Q],enumerable:!0})},X7Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Y7Q(B))if(!W7Q.call(A,Z)&&Z!==Q)Dz1(A,Z,{get:()=>B[Z],enumerable:!(D=I7Q(B,Z))||D.enumerable})}return A},V7Q=(A)=>X7Q(Dz1({},"__esModule",{value:!0}),A),NJA={};J7Q(NJA,{AlgorithmId:()=>OJA,EndpointURLScheme:()=>RJA,FieldPosition:()=>TJA,HttpApiKeyAuthLocation:()=>MJA,HttpAuthLocation:()=>LJA,IniSectionType:()=>PJA,RequestHandlerProtocol:()=>SJA,SMITHY_CONTEXT_KEY:()=>E7Q,getDefaultClientConfiguration:()=>H7Q,resolveDefaultRuntimeConfig:()=>z7Q});jJA.exports=V7Q(NJA);var LJA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(LJA||{}),MJA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(MJA||{}),RJA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(RJA||{}),OJA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(OJA||{}),C7Q=Zz1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),K7Q=Zz1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),H7Q=Zz1((A)=>{return C7Q(A)},"getDefaultClientConfiguration"),z7Q=Zz1((A)=>{return K7Q(A)},"resolveDefaultRuntimeConfig"),TJA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(TJA||{}),E7Q="__smithy_context",PJA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(PJA||{}),SJA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(SJA||{})});
var By=E((CVA)=>{Object.defineProperty(CVA,"__esModule",{value:!0});CVA.isBlob=CVA.isReadableStream=void 0;var QGQ=(A)=>{var B;return typeof ReadableStream==="function"&&(((B=A===null||A===void 0?void 0:A.constructor)===null||B===void 0?void 0:B.name)===ReadableStream.name||A instanceof ReadableStream)};CVA.isReadableStream=QGQ;var DGQ=(A)=>{var B;return typeof Blob==="function"&&(((B=A===null||A===void 0?void 0:A.constructor)===null||B===void 0?void 0:B.name)===Blob.name||A instanceof Blob)};CVA.isBlob=DGQ});
var CLA=E((XLA)=>{Object.defineProperty(XLA,"__esModule",{value:!0});XLA.checkUrl=void 0;var FLQ=eB(),ILQ="*************",YLQ="**************",WLQ="[fd00:ec2::23]",JLQ=(A,B)=>{if(A.protocol==="https:")return;if(A.hostname===ILQ||A.hostname===YLQ||A.hostname===WLQ)return;if(A.hostname.includes("[")){if(A.hostname==="[::1]"||A.hostname==="[0000:0000:0000:0000:0000:0000:0000:0001]")return}else{if(A.hostname==="localhost")return;let Q=A.hostname.split("."),D=(Z)=>{let G=parseInt(Z,10);return 0<=G&&G<=255};if(Q[0]==="127"&&D(Q[1])&&D(Q[2])&&D(Q[3])&&Q.length===4)return}throw new FLQ.CredentialsProviderError(`URL not accepted. It must either be HTTPS or match one of the following:
  - loopback CIDR *********/8 or [::1/128]
  - ECS container host *************
  - EKS container host ************** or [fd00:ec2::23]`,{logger:B})};XLA.checkUrl=JLQ});
var CSA=E((XSA)=>{Object.defineProperty(XSA,"__esModule",{value:!0});XSA.getRuntimeConfig=void 0;var vjQ=ZI(),bjQ=VB(),fjQ=x4(),hjQ=BZ(),WSA=Yy(),JSA=cB(),gjQ=Ne1(),ujQ=YSA(),mjQ=(A)=>{return{apiVersion:"2011-06-15",base64Decoder:A?.base64Decoder??WSA.fromBase64,base64Encoder:A?.base64Encoder??WSA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??ujQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??gjQ.defaultSTSHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new vjQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new bjQ.NoAuthSigner}],logger:A?.logger??new fjQ.NoOpLogger,serviceId:A?.serviceId??"STS",urlParser:A?.urlParser??hjQ.parseUrl,utf8Decoder:A?.utf8Decoder??JSA.fromUtf8,utf8Encoder:A?.utf8Encoder??JSA.toUtf8}};XSA.getRuntimeConfig=mjQ});
var CUA=E((w75,VUA)=>{var FKQ=/^[-+]?0x[a-fA-F0-9]+$/,IKQ=/^([\-\+])?(0*)(\.[0-9]+([eE]\-?[0-9]+)?|[0-9]+(\.[0-9]+([eE]\-?[0-9]+)?)?)$/;if(!Number.parseInt&&window.parseInt)Number.parseInt=window.parseInt;if(!Number.parseFloat&&window.parseFloat)Number.parseFloat=window.parseFloat;var YKQ={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0};function WKQ(A,B={}){if(B=Object.assign({},YKQ,B),!A||typeof A!=="string")return A;let Q=A.trim();if(B.skipLike!==void 0&&B.skipLike.test(Q))return A;else if(B.hex&&FKQ.test(Q))return Number.parseInt(Q,16);else{let D=IKQ.exec(Q);if(D){let Z=D[1],G=D[2],F=JKQ(D[3]),I=D[4]||D[6];if(!B.leadingZeros&&G.length>0&&Z&&Q[2]!==".")return A;else if(!B.leadingZeros&&G.length>0&&!Z&&Q[1]!==".")return A;else{let Y=Number(Q),W=""+Y;if(W.search(/[eE]/)!==-1)if(B.eNotation)return Y;else return A;else if(I)if(B.eNotation)return Y;else return A;else if(Q.indexOf(".")!==-1)if(W==="0"&&F==="")return Y;else if(W===F)return Y;else if(Z&&W==="-"+F)return Y;else return A;if(G)if(F===W)return Y;else if(Z+F===W)return Y;else return A;if(Q===W)return Y;else if(Q===Z+W)return Y;return A}}else return A}}function JKQ(A){if(A&&A.indexOf(".")!==-1){if(A=A.replace(/0+$/,""),A===".")A="0";else if(A[0]===".")A="0"+A;else if(A[A.length-1]===".")A=A.substr(0,A.length-1);return A}return A}VUA.exports=WKQ});
var CXA=E((C55,VXA)=>{var{defineProperty:Cz1,getOwnPropertyDescriptor:VDQ,getOwnPropertyNames:CDQ}=Object,KDQ=Object.prototype.hasOwnProperty,Kz1=(A,B)=>Cz1(A,"name",{value:B,configurable:!0}),HDQ=(A,B)=>{for(var Q in B)Cz1(A,Q,{get:B[Q],enumerable:!0})},zDQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of CDQ(B))if(!KDQ.call(A,Z)&&Z!==Q)Cz1(A,Z,{get:()=>B[Z],enumerable:!(D=VDQ(B,Z))||D.enumerable})}return A},EDQ=(A)=>zDQ(Cz1({},"__esModule",{value:!0}),A),ZXA={};HDQ(ZXA,{AlgorithmId:()=>YXA,EndpointURLScheme:()=>IXA,FieldPosition:()=>WXA,HttpApiKeyAuthLocation:()=>FXA,HttpAuthLocation:()=>GXA,IniSectionType:()=>JXA,RequestHandlerProtocol:()=>XXA,SMITHY_CONTEXT_KEY:()=>NDQ,getDefaultClientConfiguration:()=>$DQ,resolveDefaultRuntimeConfig:()=>qDQ});VXA.exports=EDQ(ZXA);var GXA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(GXA||{}),FXA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(FXA||{}),IXA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(IXA||{}),YXA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(YXA||{}),UDQ=Kz1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),wDQ=Kz1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),$DQ=Kz1((A)=>{return UDQ(A)},"getDefaultClientConfiguration"),qDQ=Kz1((A)=>{return wDQ(A)},"resolveDefaultRuntimeConfig"),WXA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(WXA||{}),NDQ="__smithy_context",JXA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(JXA||{}),XXA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(XXA||{})});
var DMA=E((BMA)=>{Object.defineProperty(BMA,"__esModule",{value:!0});BMA.ruleSet=void 0;var oLA="required",Yz="fn",Wz="argv",Ln="ref",mLA=!0,dLA="isSet",eQ1="booleanEquals",qn="error",Nn="endpoint",gO="tree",fe1="PartitionResult",he1="getAttr",cLA={[oLA]:!1,type:"String"},lLA={[oLA]:!0,default:!1,type:"Boolean"},pLA={[Ln]:"Endpoint"},tLA={[Yz]:eQ1,[Wz]:[{[Ln]:"UseFIPS"},!0]},eLA={[Yz]:eQ1,[Wz]:[{[Ln]:"UseDualStack"},!0]},Iz={},iLA={[Yz]:he1,[Wz]:[{[Ln]:fe1},"supportsFIPS"]},AMA={[Ln]:fe1},nLA={[Yz]:eQ1,[Wz]:[!0,{[Yz]:he1,[Wz]:[AMA,"supportsDualStack"]}]},aLA=[tLA],sLA=[eLA],rLA=[{[Ln]:"Region"}],HMQ={version:"1.0",parameters:{Region:cLA,UseDualStack:lLA,UseFIPS:lLA,Endpoint:cLA},rules:[{conditions:[{[Yz]:dLA,[Wz]:[pLA]}],rules:[{conditions:aLA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:qn},{conditions:sLA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:qn},{endpoint:{url:pLA,properties:Iz,headers:Iz},type:Nn}],type:gO},{conditions:[{[Yz]:dLA,[Wz]:rLA}],rules:[{conditions:[{[Yz]:"aws.partition",[Wz]:rLA,assign:fe1}],rules:[{conditions:[tLA,eLA],rules:[{conditions:[{[Yz]:eQ1,[Wz]:[mLA,iLA]},nLA],rules:[{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Iz,headers:Iz},type:Nn}],type:gO},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:qn}],type:gO},{conditions:aLA,rules:[{conditions:[{[Yz]:eQ1,[Wz]:[iLA,mLA]}],rules:[{conditions:[{[Yz]:"stringEquals",[Wz]:[{[Yz]:he1,[Wz]:[AMA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://portal.sso.{Region}.amazonaws.com",properties:Iz,headers:Iz},type:Nn},{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dnsSuffix}",properties:Iz,headers:Iz},type:Nn}],type:gO},{error:"FIPS is enabled but this partition does not support FIPS",type:qn}],type:gO},{conditions:sLA,rules:[{conditions:[nLA],rules:[{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Iz,headers:Iz},type:Nn}],type:gO},{error:"DualStack is enabled but this partition does not support DualStack",type:qn}],type:gO},{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dnsSuffix}",properties:Iz,headers:Iz},type:Nn}],type:gO}],type:gO},{error:"Invalid Configuration: Missing Region",type:qn}]};BMA.ruleSet=HMQ});
var DTA=E((BTA)=>{Object.defineProperty(BTA,"__esModule",{value:!0});BTA.ruleSet=void 0;var pOA="required",C4="type",_8="fn",x8="argv",Uy="ref",xOA=!1,D10=!0,Ey="booleanEquals",qY="stringEquals",iOA="sigv4",nOA="sts",aOA="us-east-1",DD="endpoint",vOA="https://sts.{Region}.{PartitionResult#dnsSuffix}",lN="tree",Sn="error",G10="getAttr",bOA={[pOA]:!1,[C4]:"String"},Z10={[pOA]:!0,default:!1,[C4]:"Boolean"},sOA={[Uy]:"Endpoint"},fOA={[_8]:"isSet",[x8]:[{[Uy]:"Region"}]},NY={[Uy]:"Region"},hOA={[_8]:"aws.partition",[x8]:[NY],assign:"PartitionResult"},rOA={[Uy]:"UseFIPS"},oOA={[Uy]:"UseDualStack"},xW={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:iOA,signingName:nOA,signingRegion:aOA}]},headers:{}},rC={},gOA={conditions:[{[_8]:qY,[x8]:[NY,"aws-global"]}],[DD]:xW,[C4]:DD},tOA={[_8]:Ey,[x8]:[rOA,!0]},eOA={[_8]:Ey,[x8]:[oOA,!0]},uOA={[_8]:G10,[x8]:[{[Uy]:"PartitionResult"},"supportsFIPS"]},ATA={[Uy]:"PartitionResult"},mOA={[_8]:Ey,[x8]:[!0,{[_8]:G10,[x8]:[ATA,"supportsDualStack"]}]},dOA=[{[_8]:"isSet",[x8]:[sOA]}],cOA=[tOA],lOA=[eOA],iTQ={version:"1.0",parameters:{Region:bOA,UseDualStack:Z10,UseFIPS:Z10,Endpoint:bOA,UseGlobalEndpoint:Z10},rules:[{conditions:[{[_8]:Ey,[x8]:[{[Uy]:"UseGlobalEndpoint"},D10]},{[_8]:"not",[x8]:dOA},fOA,hOA,{[_8]:Ey,[x8]:[rOA,xOA]},{[_8]:Ey,[x8]:[oOA,xOA]}],rules:[{conditions:[{[_8]:qY,[x8]:[NY,"ap-northeast-1"]}],endpoint:xW,[C4]:DD},{conditions:[{[_8]:qY,[x8]:[NY,"ap-south-1"]}],endpoint:xW,[C4]:DD},{conditions:[{[_8]:qY,[x8]:[NY,"ap-southeast-1"]}],endpoint:xW,[C4]:DD},{conditions:[{[_8]:qY,[x8]:[NY,"ap-southeast-2"]}],endpoint:xW,[C4]:DD},gOA,{conditions:[{[_8]:qY,[x8]:[NY,"ca-central-1"]}],endpoint:xW,[C4]:DD},{conditions:[{[_8]:qY,[x8]:[NY,"eu-central-1"]}],endpoint:xW,[C4]:DD},{conditions:[{[_8]:qY,[x8]:[NY,"eu-north-1"]}],endpoint:xW,[C4]:DD},{conditions:[{[_8]:qY,[x8]:[NY,"eu-west-1"]}],endpoint:xW,[C4]:DD},{conditions:[{[_8]:qY,[x8]:[NY,"eu-west-2"]}],endpoint:xW,[C4]:DD},{conditions:[{[_8]:qY,[x8]:[NY,"eu-west-3"]}],endpoint:xW,[C4]:DD},{conditions:[{[_8]:qY,[x8]:[NY,"sa-east-1"]}],endpoint:xW,[C4]:DD},{conditions:[{[_8]:qY,[x8]:[NY,aOA]}],endpoint:xW,[C4]:DD},{conditions:[{[_8]:qY,[x8]:[NY,"us-east-2"]}],endpoint:xW,[C4]:DD},{conditions:[{[_8]:qY,[x8]:[NY,"us-west-1"]}],endpoint:xW,[C4]:DD},{conditions:[{[_8]:qY,[x8]:[NY,"us-west-2"]}],endpoint:xW,[C4]:DD},{endpoint:{url:vOA,properties:{authSchemes:[{name:iOA,signingName:nOA,signingRegion:"{Region}"}]},headers:rC},[C4]:DD}],[C4]:lN},{conditions:dOA,rules:[{conditions:cOA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[C4]:Sn},{conditions:lOA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[C4]:Sn},{endpoint:{url:sOA,properties:rC,headers:rC},[C4]:DD}],[C4]:lN},{conditions:[fOA],rules:[{conditions:[hOA],rules:[{conditions:[tOA,eOA],rules:[{conditions:[{[_8]:Ey,[x8]:[D10,uOA]},mOA],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:rC,headers:rC},[C4]:DD}],[C4]:lN},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[C4]:Sn}],[C4]:lN},{conditions:cOA,rules:[{conditions:[{[_8]:Ey,[x8]:[uOA,D10]}],rules:[{conditions:[{[_8]:qY,[x8]:[{[_8]:G10,[x8]:[ATA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:rC,headers:rC},[C4]:DD},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:rC,headers:rC},[C4]:DD}],[C4]:lN},{error:"FIPS is enabled but this partition does not support FIPS",[C4]:Sn}],[C4]:lN},{conditions:lOA,rules:[{conditions:[mOA],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:rC,headers:rC},[C4]:DD}],[C4]:lN},{error:"DualStack is enabled but this partition does not support DualStack",[C4]:Sn}],[C4]:lN},gOA,{endpoint:{url:vOA,properties:rC,headers:rC},[C4]:DD}],[C4]:lN}],[C4]:lN},{error:"Invalid Configuration: Missing Region",[C4]:Sn}]};BTA.ruleSet=iTQ});
var DqA=E((KD5,QqA)=>{var{defineProperty:aE1,getOwnPropertyDescriptor:GwQ,getOwnPropertyNames:FwQ}=Object,IwQ=Object.prototype.hasOwnProperty,Ky=(A,B)=>aE1(A,"name",{value:B,configurable:!0}),YwQ=(A,B)=>{for(var Q in B)aE1(A,Q,{get:B[Q],enumerable:!0})},WwQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of FwQ(B))if(!IwQ.call(A,Z)&&Z!==Q)aE1(A,Z,{get:()=>B[Z],enumerable:!(D=GwQ(B,Z))||D.enumerable})}return A},JwQ=(A)=>WwQ(aE1({},"__esModule",{value:!0}),A),t$A={};YwQ(t$A,{Field:()=>CwQ,Fields:()=>KwQ,HttpRequest:()=>HwQ,HttpResponse:()=>zwQ,IHttpRequest:()=>e$A.HttpRequest,getHttpHandlerExtensionConfiguration:()=>XwQ,isValidHostname:()=>BqA,resolveHttpHandlerRuntimeConfig:()=>VwQ});QqA.exports=JwQ(t$A);var XwQ=Ky((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),VwQ=Ky((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),e$A=et1(),CwQ=class{static{Ky(this,"Field")}constructor({name:A,kind:B=e$A.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},KwQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{Ky(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},HwQ=class A{static{Ky(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=AqA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function AqA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}Ky(AqA,"cloneQuery");var zwQ=class{static{Ky(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function BqA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}Ky(BqA,"isValidHostname")});
var EPA=E((HPA)=>{Object.defineProperty(HPA,"__esModule",{value:!0});HPA.fromTokenFile=void 0;var lSQ=Qz(),pSQ=eB(),iSQ=J1("fs"),nSQ=l10(),KPA="AWS_WEB_IDENTITY_TOKEN_FILE",aSQ="AWS_ROLE_ARN",sSQ="AWS_ROLE_SESSION_NAME",rSQ=(A={})=>async()=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromTokenFile");let B=A?.webIdentityTokenFile??process.env[KPA],Q=A?.roleArn??process.env[aSQ],D=A?.roleSessionName??process.env[sSQ];if(!B||!Q)throw new pSQ.CredentialsProviderError("Web identity configuration not specified",{logger:A.logger});let Z=await nSQ.fromWebToken({...A,webIdentityToken:iSQ.readFileSync(B,{encoding:"ascii"}),roleArn:Q,roleSessionName:D})();if(B===process.env[KPA])lSQ.setCredentialFeature(Z,"CREDENTIALS_ENV_VARS_STS_WEB_ID_TOKEN","h");return Z};HPA.fromTokenFile=rSQ});
var ETA=E((HTA)=>{Object.defineProperty(HTA,"__esModule",{value:!0});HTA.getRuntimeConfig=void 0;var ZPQ=Sh(),GPQ=ZPQ.__importDefault(pe1()),I10=ZI(),VTA=tQ1(),MU1=V4(),FPQ=VB(),IPQ=jG(),CTA=v4(),mh=QD(),KTA=S3(),YPQ=kG(),WPQ=hZ(),JPQ=XTA(),XPQ=x4(),VPQ=yG(),CPQ=x4(),KPQ=(A)=>{CPQ.emitWarningIfUnsupportedVersion(process.version);let B=VPQ.resolveDefaultsModeConfig(A),Q=()=>B().then(XPQ.loadConfigsForDefaultMode),D=JPQ.getRuntimeConfig(A);I10.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??mh.loadConfig(I10.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??YPQ.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??VTA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:GPQ.default.version}),httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(G)=>G.getIdentityProvider("aws.auth#sigv4")||(async(F)=>await A.credentialDefaultProvider(F?.__config||{})()),signer:new I10.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(G)=>G.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new FPQ.NoAuthSigner}],maxAttempts:A?.maxAttempts??mh.loadConfig(CTA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??mh.loadConfig(MU1.NODE_REGION_CONFIG_OPTIONS,{...MU1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:KTA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??mh.loadConfig({...CTA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||WPQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??IPQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??KTA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??mh.loadConfig(MU1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??mh.loadConfig(MU1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??mh.loadConfig(VTA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};HTA.getRuntimeConfig=KPQ});
var EUA=E(($75,zUA)=>{var KUA=NE1(),hQ1=WUA(),XKQ=XUA(),VKQ=CUA();class HUA{constructor(A){this.options=A,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"¢"},pound:{regex:/&(pound|#163);/g,val:"£"},yen:{regex:/&(yen|#165);/g,val:"¥"},euro:{regex:/&(euro|#8364);/g,val:"€"},copyright:{regex:/&(copy|#169);/g,val:"©"},reg:{regex:/&(reg|#174);/g,val:"®"},inr:{regex:/&(inr|#8377);/g,val:"₹"},num_dec:{regex:/&#([0-9]{1,7});/g,val:(B,Q)=>String.fromCharCode(Number.parseInt(Q,10))},num_hex:{regex:/&#x([0-9a-fA-F]{1,6});/g,val:(B,Q)=>String.fromCharCode(Number.parseInt(Q,16))}},this.addExternalEntities=CKQ,this.parseXml=UKQ,this.parseTextData=KKQ,this.resolveNameSpace=HKQ,this.buildAttributesMap=EKQ,this.isItStopNode=NKQ,this.replaceEntitiesValue=$KQ,this.readStopNodeData=MKQ,this.saveTextToParentTag=qKQ,this.addChild=wKQ}}function CKQ(A){let B=Object.keys(A);for(let Q=0;Q<B.length;Q++){let D=B[Q];this.lastEntities[D]={regex:new RegExp("&"+D+";","g"),val:A[D]}}}function KKQ(A,B,Q,D,Z,G,F){if(A!==void 0){if(this.options.trimValues&&!D)A=A.trim();if(A.length>0){if(!F)A=this.replaceEntitiesValue(A);let I=this.options.tagValueProcessor(B,A,Q,Z,G);if(I===null||I===void 0)return A;else if(typeof I!==typeof A||I!==A)return I;else if(this.options.trimValues)return St1(A,this.options.parseTagValue,this.options.numberParseOptions);else if(A.trim()===A)return St1(A,this.options.parseTagValue,this.options.numberParseOptions);else return A}}}function HKQ(A){if(this.options.removeNSPrefix){let B=A.split(":"),Q=A.charAt(0)==="/"?"/":"";if(B[0]==="xmlns")return"";if(B.length===2)A=Q+B[1]}return A}var zKQ=new RegExp(`([^\\s=]+)\\s*(=\\s*(['"])([\\s\\S]*?)\\3)?`,"gm");function EKQ(A,B,Q){if(!this.options.ignoreAttributes&&typeof A==="string"){let D=KUA.getAllMatches(A,zKQ),Z=D.length,G={};for(let F=0;F<Z;F++){let I=this.resolveNameSpace(D[F][1]),Y=D[F][4],W=this.options.attributeNamePrefix+I;if(I.length){if(this.options.transformAttributeName)W=this.options.transformAttributeName(W);if(W==="__proto__")W="#__proto__";if(Y!==void 0){if(this.options.trimValues)Y=Y.trim();Y=this.replaceEntitiesValue(Y);let J=this.options.attributeValueProcessor(I,Y,B);if(J===null||J===void 0)G[W]=Y;else if(typeof J!==typeof Y||J!==Y)G[W]=J;else G[W]=St1(Y,this.options.parseAttributeValue,this.options.numberParseOptions)}else if(this.options.allowBooleanAttributes)G[W]=!0}}if(!Object.keys(G).length)return;if(this.options.attributesGroupName){let F={};return F[this.options.attributesGroupName]=G,F}return G}}var UKQ=function(A){A=A.replace(/\r\n?/g,`
`);let B=new hQ1("!xml"),Q=B,D="",Z="";for(let G=0;G<A.length;G++)if(A[G]==="<")if(A[G+1]==="/"){let I=_h(A,">",G,"Closing Tag is not closed."),Y=A.substring(G+2,I).trim();if(this.options.removeNSPrefix){let X=Y.indexOf(":");if(X!==-1)Y=Y.substr(X+1)}if(this.options.transformTagName)Y=this.options.transformTagName(Y);if(Q)D=this.saveTextToParentTag(D,Q,Z);let W=Z.substring(Z.lastIndexOf(".")+1);if(Y&&this.options.unpairedTags.indexOf(Y)!==-1)throw new Error(`Unpaired tag can not be used as closing tag: </${Y}>`);let J=0;if(W&&this.options.unpairedTags.indexOf(W)!==-1)J=Z.lastIndexOf(".",Z.lastIndexOf(".")-1),this.tagsNodeStack.pop();else J=Z.lastIndexOf(".");Z=Z.substring(0,J),Q=this.tagsNodeStack.pop(),D="",G=I}else if(A[G+1]==="?"){let I=Pt1(A,G,!1,"?>");if(!I)throw new Error("Pi Tag is not closed.");if(D=this.saveTextToParentTag(D,Q,Z),this.options.ignoreDeclaration&&I.tagName==="?xml"||this.options.ignorePiTags);else{let Y=new hQ1(I.tagName);if(Y.add(this.options.textNodeName,""),I.tagName!==I.tagExp&&I.attrExpPresent)Y[":@"]=this.buildAttributesMap(I.tagExp,Z,I.tagName);this.addChild(Q,Y,Z)}G=I.closeIndex+1}else if(A.substr(G+1,3)==="!--"){let I=_h(A,"-->",G+4,"Comment is not closed.");if(this.options.commentPropName){let Y=A.substring(G+4,I-2);D=this.saveTextToParentTag(D,Q,Z),Q.add(this.options.commentPropName,[{[this.options.textNodeName]:Y}])}G=I}else if(A.substr(G+1,2)==="!D"){let I=XKQ(A,G);this.docTypeEntities=I.entities,G=I.i}else if(A.substr(G+1,2)==="!["){let I=_h(A,"]]>",G,"CDATA is not closed.")-2,Y=A.substring(G+9,I);D=this.saveTextToParentTag(D,Q,Z);let W=this.parseTextData(Y,Q.tagname,Z,!0,!1,!0,!0);if(W==null)W="";if(this.options.cdataPropName)Q.add(this.options.cdataPropName,[{[this.options.textNodeName]:Y}]);else Q.add(this.options.textNodeName,W);G=I+2}else{let I=Pt1(A,G,this.options.removeNSPrefix),Y=I.tagName,W=I.rawTagName,J=I.tagExp,X=I.attrExpPresent,V=I.closeIndex;if(this.options.transformTagName)Y=this.options.transformTagName(Y);if(Q&&D){if(Q.tagname!=="!xml")D=this.saveTextToParentTag(D,Q,Z,!1)}let C=Q;if(C&&this.options.unpairedTags.indexOf(C.tagname)!==-1)Q=this.tagsNodeStack.pop(),Z=Z.substring(0,Z.lastIndexOf("."));if(Y!==B.tagname)Z+=Z?"."+Y:Y;if(this.isItStopNode(this.options.stopNodes,Z,Y)){let K="";if(J.length>0&&J.lastIndexOf("/")===J.length-1){if(Y[Y.length-1]==="/")Y=Y.substr(0,Y.length-1),Z=Z.substr(0,Z.length-1),J=Y;else J=J.substr(0,J.length-1);G=I.closeIndex}else if(this.options.unpairedTags.indexOf(Y)!==-1)G=I.closeIndex;else{let z=this.readStopNodeData(A,W,V+1);if(!z)throw new Error(`Unexpected end of ${W}`);G=z.i,K=z.tagContent}let H=new hQ1(Y);if(Y!==J&&X)H[":@"]=this.buildAttributesMap(J,Z,Y);if(K)K=this.parseTextData(K,Y,Z,!0,X,!0,!0);Z=Z.substr(0,Z.lastIndexOf(".")),H.add(this.options.textNodeName,K),this.addChild(Q,H,Z)}else{if(J.length>0&&J.lastIndexOf("/")===J.length-1){if(Y[Y.length-1]==="/")Y=Y.substr(0,Y.length-1),Z=Z.substr(0,Z.length-1),J=Y;else J=J.substr(0,J.length-1);if(this.options.transformTagName)Y=this.options.transformTagName(Y);let K=new hQ1(Y);if(Y!==J&&X)K[":@"]=this.buildAttributesMap(J,Z,Y);this.addChild(Q,K,Z),Z=Z.substr(0,Z.lastIndexOf("."))}else{let K=new hQ1(Y);if(this.tagsNodeStack.push(Q),Y!==J&&X)K[":@"]=this.buildAttributesMap(J,Z,Y);this.addChild(Q,K,Z),Q=K}D="",G=V}}else D+=A[G];return B.child};function wKQ(A,B,Q){let D=this.options.updateTag(B.tagname,Q,B[":@"]);if(D===!1);else if(typeof D==="string")B.tagname=D,A.addChild(B);else A.addChild(B)}var $KQ=function(A){if(this.options.processEntities){for(let B in this.docTypeEntities){let Q=this.docTypeEntities[B];A=A.replace(Q.regx,Q.val)}for(let B in this.lastEntities){let Q=this.lastEntities[B];A=A.replace(Q.regex,Q.val)}if(this.options.htmlEntities)for(let B in this.htmlEntities){let Q=this.htmlEntities[B];A=A.replace(Q.regex,Q.val)}A=A.replace(this.ampEntity.regex,this.ampEntity.val)}return A};function qKQ(A,B,Q,D){if(A){if(D===void 0)D=Object.keys(B.child).length===0;if(A=this.parseTextData(A,B.tagname,Q,!1,B[":@"]?Object.keys(B[":@"]).length!==0:!1,D),A!==void 0&&A!=="")B.add(this.options.textNodeName,A);A=""}return A}function NKQ(A,B,Q){let D="*."+Q;for(let Z in A){let G=A[Z];if(D===G||B===G)return!0}return!1}function LKQ(A,B,Q=">"){let D,Z="";for(let G=B;G<A.length;G++){let F=A[G];if(D){if(F===D)D=""}else if(F==='"'||F==="'")D=F;else if(F===Q[0])if(Q[1]){if(A[G+1]===Q[1])return{data:Z,index:G}}else return{data:Z,index:G};else if(F==="\t")F=" ";Z+=F}}function _h(A,B,Q,D){let Z=A.indexOf(B,Q);if(Z===-1)throw new Error(D);else return Z+B.length-1}function Pt1(A,B,Q,D=">"){let Z=LKQ(A,B+1,D);if(!Z)return;let{data:G,index:F}=Z,I=G.search(/\s/),Y=G,W=!0;if(I!==-1)Y=G.substring(0,I),G=G.substring(I+1).trimStart();let J=Y;if(Q){let X=Y.indexOf(":");if(X!==-1)Y=Y.substr(X+1),W=Y!==Z.data.substr(X+1)}return{tagName:Y,tagExp:G,closeIndex:F,attrExpPresent:W,rawTagName:J}}function MKQ(A,B,Q){let D=Q,Z=1;for(;Q<A.length;Q++)if(A[Q]==="<")if(A[Q+1]==="/"){let G=_h(A,">",Q,`${B} is not closed`);if(A.substring(Q+2,G).trim()===B){if(Z--,Z===0)return{tagContent:A.substring(D,Q),i:G}}Q=G}else if(A[Q+1]==="?")Q=_h(A,"?>",Q+1,"StopNode is not closed.");else if(A.substr(Q+1,3)==="!--")Q=_h(A,"-->",Q+3,"StopNode is not closed.");else if(A.substr(Q+1,2)==="![")Q=_h(A,"]]>",Q,"StopNode is not closed.")-2;else{let G=Pt1(A,Q,">");if(G){if((G&&G.tagName)===B&&G.tagExp[G.tagExp.length-1]!=="/")Z++;Q=G.closeIndex}}}function St1(A,B,Q){if(B&&typeof A==="string"){let D=A.trim();if(D==="true")return!0;else if(D==="false")return!1;else return VKQ(A,Q)}else if(KUA.isExist(A))return A;else return""}zUA.exports=HUA});
var En=E((nwA)=>{Object.defineProperty(nwA,"__esModule",{value:!0});nwA.getHomeDir=void 0;var GEQ=J1("os"),FEQ=J1("path"),ct1={},IEQ=()=>{if(process&&process.geteuid)return`${process.geteuid()}`;return"DEFAULT"},YEQ=()=>{let{HOME:A,USERPROFILE:B,HOMEPATH:Q,HOMEDRIVE:D=`C:${FEQ.sep}`}=process.env;if(A)return A;if(B)return B;if(Q)return`${D}${Q}`;let Z=IEQ();if(!ct1[Z])ct1[Z]=GEQ.homedir();return ct1[Z]};nwA.getHomeDir=YEQ});
var FCA=E((c55,GCA)=>{var{defineProperty:Sz1,getOwnPropertyDescriptor:lGQ,getOwnPropertyNames:pGQ}=Object,iGQ=Object.prototype.hasOwnProperty,Qy=(A,B)=>Sz1(A,"name",{value:B,configurable:!0}),nGQ=(A,B)=>{for(var Q in B)Sz1(A,Q,{get:B[Q],enumerable:!0})},aGQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of pGQ(B))if(!iGQ.call(A,Z)&&Z!==Q)Sz1(A,Z,{get:()=>B[Z],enumerable:!(D=lGQ(B,Z))||D.enumerable})}return A},sGQ=(A)=>aGQ(Sz1({},"__esModule",{value:!0}),A),BCA={};nGQ(BCA,{Field:()=>tGQ,Fields:()=>eGQ,HttpRequest:()=>AFQ,HttpResponse:()=>BFQ,IHttpRequest:()=>QCA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>rGQ,isValidHostname:()=>ZCA,resolveHttpHandlerRuntimeConfig:()=>oGQ});GCA.exports=sGQ(BCA);var rGQ=Qy((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),oGQ=Qy((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),QCA=ACA(),tGQ=class{static{Qy(this,"Field")}constructor({name:A,kind:B=QCA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},eGQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{Qy(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},AFQ=class A{static{Qy(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=DCA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function DCA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}Qy(DCA,"cloneQuery");var BFQ=class{static{Qy(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function ZCA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}Qy(ZCA,"isValidHostname")});
var FKA=E((F35,GKA)=>{var{defineProperty:hz1,getOwnPropertyDescriptor:NIQ,getOwnPropertyNames:LIQ}=Object,MIQ=Object.prototype.hasOwnProperty,vN=(A,B)=>hz1(A,"name",{value:B,configurable:!0}),RIQ=(A,B)=>{for(var Q in B)hz1(A,Q,{get:B[Q],enumerable:!0})},OIQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of LIQ(B))if(!MIQ.call(A,Z)&&Z!==Q)hz1(A,Z,{get:()=>B[Z],enumerable:!(D=NIQ(B,Z))||D.enumerable})}return A},TIQ=(A)=>OIQ(hz1({},"__esModule",{value:!0}),A),AKA={};RIQ(AKA,{FetchHttpHandler:()=>SIQ,keepAliveSupport:()=>fz1,streamCollector:()=>kIQ});GKA.exports=TIQ(AKA);var eCA=lCA(),PIQ=tCA();function wo1(A,B){return new Request(A,B)}vN(wo1,"createRequest");function BKA(A=0){return new Promise((B,Q)=>{if(A)setTimeout(()=>{let D=new Error(`Request did not complete within ${A} ms`);D.name="TimeoutError",Q(D)},A)})}vN(BKA,"requestTimeout");var fz1={supported:void 0},SIQ=class A{static{vN(this,"FetchHttpHandler")}static create(B){if(typeof B?.handle==="function")return B;return new A(B)}constructor(B){if(typeof B==="function")this.configProvider=B().then((Q)=>Q||{});else this.config=B??{},this.configProvider=Promise.resolve(this.config);if(fz1.supported===void 0)fz1.supported=Boolean(typeof Request!=="undefined"&&"keepalive"in wo1("https://[::1]"))}destroy(){}async handle(B,{abortSignal:Q}={}){if(!this.config)this.config=await this.configProvider;let D=this.config.requestTimeout,Z=this.config.keepAlive===!0,G=this.config.credentials;if(Q?.aborted){let $=new Error("Request aborted");return $.name="AbortError",Promise.reject($)}let F=B.path,I=PIQ.buildQueryString(B.query||{});if(I)F+=`?${I}`;if(B.fragment)F+=`#${B.fragment}`;let Y="";if(B.username!=null||B.password!=null){let $=B.username??"",L=B.password??"";Y=`${$}:${L}@`}let{port:W,method:J}=B,X=`${B.protocol}//${Y}${B.hostname}${W?`:${W}`:""}${F}`,V=J==="GET"||J==="HEAD"?void 0:B.body,C={body:V,headers:new Headers(B.headers),method:J,credentials:G};if(this.config?.cache)C.cache=this.config.cache;if(V)C.duplex="half";if(typeof AbortController!=="undefined")C.signal=Q;if(fz1.supported)C.keepalive=Z;if(typeof this.config.requestInit==="function")Object.assign(C,this.config.requestInit(B));let K=vN(()=>{},"removeSignalEventListener"),H=wo1(X,C),z=[fetch(H).then(($)=>{let L=$.headers,N={};for(let R of L.entries())N[R[0]]=R[1];if($.body==null)return $.blob().then((R)=>({response:new eCA.HttpResponse({headers:N,reason:$.statusText,statusCode:$.status,body:R})}));return{response:new eCA.HttpResponse({headers:N,reason:$.statusText,statusCode:$.status,body:$.body})}}),BKA(D)];if(Q)z.push(new Promise(($,L)=>{let N=vN(()=>{let O=new Error("Request aborted");O.name="AbortError",L(O)},"onAbort");if(typeof Q.addEventListener==="function"){let O=Q;O.addEventListener("abort",N,{once:!0}),K=vN(()=>O.removeEventListener("abort",N),"removeSignalEventListener")}else Q.onabort=N}));return Promise.race(z).finally(K)}updateHttpClientConfig(B,Q){this.config=void 0,this.configProvider=this.configProvider.then((D)=>{return D[B]=Q,D})}httpHandlerConfigs(){return this.config??{}}},jIQ=Bn(),kIQ=vN(async(A)=>{if(typeof Blob==="function"&&A instanceof Blob||A.constructor?.name==="Blob"){if(Blob.prototype.arrayBuffer!==void 0)return new Uint8Array(await A.arrayBuffer());return QKA(A)}return DKA(A)},"streamCollector");async function QKA(A){let B=await ZKA(A),Q=jIQ.fromBase64(B);return new Uint8Array(Q)}vN(QKA,"collectBlob");async function DKA(A){let B=[],Q=A.getReader(),D=!1,Z=0;while(!D){let{done:I,value:Y}=await Q.read();if(Y)B.push(Y),Z+=Y.length;D=I}let G=new Uint8Array(Z),F=0;for(let I of B)G.set(I,F),F+=I.length;return G}vN(DKA,"collectStream");function ZKA(A){return new Promise((B,Q)=>{let D=new FileReader;D.onloadend=()=>{if(D.readyState!==2)return Q(new Error("Reader aborted too early"));let Z=D.result??"",G=Z.indexOf(","),F=G>-1?G+1:Z.length;B(Z.substring(F))},D.onabort=()=>Q(new Error("Read aborted")),D.onerror=()=>Q(D.error),D.readAsDataURL(A)})}vN(ZKA,"readToBase64")});
var FMA=E((ZMA)=>{Object.defineProperty(ZMA,"__esModule",{value:!0});ZMA.defaultEndpointResolver=void 0;var zMQ=Jn(),ge1=$7(),EMQ=DMA(),UMQ=new ge1.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),wMQ=(A,B={})=>{return UMQ.get(A,()=>ge1.resolveEndpoint(EMQ.ruleSet,{endpointParams:A,logger:B.logger}))};ZMA.defaultEndpointResolver=wMQ;ge1.customEndpointFunctions.aws=zMQ.awsEndpointFunctions});
var FTA=E((ZTA)=>{Object.defineProperty(ZTA,"__esModule",{value:!0});ZTA.defaultEndpointResolver=void 0;var nTQ=Jn(),F10=$7(),aTQ=DTA(),sTQ=new F10.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),rTQ=(A,B={})=>{return sTQ.get(A,()=>F10.resolveEndpoint(aTQ.ruleSet,{endpointParams:A,logger:B.logger}))};ZTA.defaultEndpointResolver=rTQ;F10.customEndpointFunctions.aws=nTQ.awsEndpointFunctions});
var FUA=E((aCQ)=>{var GUA={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(A,B){return B},attributeValueProcessor:function(A,B){return B},stopNodes:[],alwaysCreateTextNode:!1,isArray:()=>!1,commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1,updateTag:function(A,B,Q){return A}},nCQ=function(A){return Object.assign({},GUA,A)};aCQ.buildOptions=nCQ;aCQ.defaultOptions=GUA});
var Fy=E((V35,fN)=>{var{defineProperty:uz1,getOwnPropertyDescriptor:tIQ,getOwnPropertyNames:eIQ}=Object,AYQ=Object.prototype.hasOwnProperty,Mo1=(A,B)=>uz1(A,"name",{value:B,configurable:!0}),BYQ=(A,B)=>{for(var Q in B)uz1(A,Q,{get:B[Q],enumerable:!0})},No1=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of eIQ(B))if(!AYQ.call(A,Z)&&Z!==Q)uz1(A,Z,{get:()=>B[Z],enumerable:!(D=tIQ(B,Z))||D.enumerable})}return A},Gy=(A,B,Q)=>(No1(A,B,"default"),Q&&No1(Q,B,"default")),QYQ=(A)=>No1(uz1({},"__esModule",{value:!0}),A),bN={};BYQ(bN,{Uint8ArrayBlobAdapter:()=>Lo1});fN.exports=QYQ(bN);var SKA=Bn(),jKA=cB();function kKA(A,B="utf-8"){if(B==="base64")return SKA.toBase64(A);return jKA.toUtf8(A)}Mo1(kKA,"transformToString");function yKA(A,B){if(B==="base64")return Lo1.mutate(SKA.fromBase64(A));return Lo1.mutate(jKA.fromUtf8(A))}Mo1(yKA,"transformFromString");var Lo1=class A extends Uint8Array{static{Mo1(this,"Uint8ArrayBlobAdapter")}static fromString(B,Q="utf-8"){switch(typeof B){case"string":return yKA(B,Q);default:throw new Error(`Unsupported conversion from ${typeof B} to Uint8ArrayBlobAdapter.`)}}static mutate(B){return Object.setPrototypeOf(B,A.prototype),B}transformToString(B="utf-8"){return kKA(this,B)}};Gy(bN,Jo1(),fN.exports);Gy(bN,LVA(),fN.exports);Gy(bN,vVA(),fN.exports);Gy(bN,hVA(),fN.exports);Gy(bN,lVA(),fN.exports);Gy(bN,NKA(),fN.exports);Gy(bN,PKA(),fN.exports);Gy(bN,By(),fN.exports)});
var Fz=E((mNA)=>{Object.defineProperty(mNA,"__esModule",{value:!0});mNA.commonParams=mNA.resolveClientEndpointParameters=void 0;var VNQ=(A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,useGlobalEndpoint:A.useGlobalEndpoint??!1,defaultSigningName:"sts"})};mNA.resolveClientEndpointParameters=VNQ;mNA.commonParams={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}});
var GSA=E((DSA)=>{Object.defineProperty(DSA,"__esModule",{value:!0});DSA.ruleSet=void 0;var nPA="required",K4="type",v8="fn",b8="argv",Ny="ref",bPA=!1,o10=!0,qy="booleanEquals",LY="stringEquals",aPA="sigv4",sPA="sts",rPA="us-east-1",ZD="endpoint",fPA="https://sts.{Region}.{PartitionResult#dnsSuffix}",iN="tree",gn="error",e10="getAttr",hPA={[nPA]:!1,[K4]:"String"},t10={[nPA]:!0,default:!1,[K4]:"Boolean"},oPA={[Ny]:"Endpoint"},gPA={[v8]:"isSet",[b8]:[{[Ny]:"Region"}]},MY={[Ny]:"Region"},uPA={[v8]:"aws.partition",[b8]:[MY],assign:"PartitionResult"},tPA={[Ny]:"UseFIPS"},ePA={[Ny]:"UseDualStack"},vW={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:aPA,signingName:sPA,signingRegion:rPA}]},headers:{}},oC={},mPA={conditions:[{[v8]:LY,[b8]:[MY,"aws-global"]}],[ZD]:vW,[K4]:ZD},ASA={[v8]:qy,[b8]:[tPA,!0]},BSA={[v8]:qy,[b8]:[ePA,!0]},dPA={[v8]:e10,[b8]:[{[Ny]:"PartitionResult"},"supportsFIPS"]},QSA={[Ny]:"PartitionResult"},cPA={[v8]:qy,[b8]:[!0,{[v8]:e10,[b8]:[QSA,"supportsDualStack"]}]},lPA=[{[v8]:"isSet",[b8]:[oPA]}],pPA=[ASA],iPA=[BSA],jjQ={version:"1.0",parameters:{Region:hPA,UseDualStack:t10,UseFIPS:t10,Endpoint:hPA,UseGlobalEndpoint:t10},rules:[{conditions:[{[v8]:qy,[b8]:[{[Ny]:"UseGlobalEndpoint"},o10]},{[v8]:"not",[b8]:lPA},gPA,uPA,{[v8]:qy,[b8]:[tPA,bPA]},{[v8]:qy,[b8]:[ePA,bPA]}],rules:[{conditions:[{[v8]:LY,[b8]:[MY,"ap-northeast-1"]}],endpoint:vW,[K4]:ZD},{conditions:[{[v8]:LY,[b8]:[MY,"ap-south-1"]}],endpoint:vW,[K4]:ZD},{conditions:[{[v8]:LY,[b8]:[MY,"ap-southeast-1"]}],endpoint:vW,[K4]:ZD},{conditions:[{[v8]:LY,[b8]:[MY,"ap-southeast-2"]}],endpoint:vW,[K4]:ZD},mPA,{conditions:[{[v8]:LY,[b8]:[MY,"ca-central-1"]}],endpoint:vW,[K4]:ZD},{conditions:[{[v8]:LY,[b8]:[MY,"eu-central-1"]}],endpoint:vW,[K4]:ZD},{conditions:[{[v8]:LY,[b8]:[MY,"eu-north-1"]}],endpoint:vW,[K4]:ZD},{conditions:[{[v8]:LY,[b8]:[MY,"eu-west-1"]}],endpoint:vW,[K4]:ZD},{conditions:[{[v8]:LY,[b8]:[MY,"eu-west-2"]}],endpoint:vW,[K4]:ZD},{conditions:[{[v8]:LY,[b8]:[MY,"eu-west-3"]}],endpoint:vW,[K4]:ZD},{conditions:[{[v8]:LY,[b8]:[MY,"sa-east-1"]}],endpoint:vW,[K4]:ZD},{conditions:[{[v8]:LY,[b8]:[MY,rPA]}],endpoint:vW,[K4]:ZD},{conditions:[{[v8]:LY,[b8]:[MY,"us-east-2"]}],endpoint:vW,[K4]:ZD},{conditions:[{[v8]:LY,[b8]:[MY,"us-west-1"]}],endpoint:vW,[K4]:ZD},{conditions:[{[v8]:LY,[b8]:[MY,"us-west-2"]}],endpoint:vW,[K4]:ZD},{endpoint:{url:fPA,properties:{authSchemes:[{name:aPA,signingName:sPA,signingRegion:"{Region}"}]},headers:oC},[K4]:ZD}],[K4]:iN},{conditions:lPA,rules:[{conditions:pPA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[K4]:gn},{conditions:iPA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[K4]:gn},{endpoint:{url:oPA,properties:oC,headers:oC},[K4]:ZD}],[K4]:iN},{conditions:[gPA],rules:[{conditions:[uPA],rules:[{conditions:[ASA,BSA],rules:[{conditions:[{[v8]:qy,[b8]:[o10,dPA]},cPA],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:oC,headers:oC},[K4]:ZD}],[K4]:iN},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[K4]:gn}],[K4]:iN},{conditions:pPA,rules:[{conditions:[{[v8]:qy,[b8]:[dPA,o10]}],rules:[{conditions:[{[v8]:LY,[b8]:[{[v8]:e10,[b8]:[QSA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:oC,headers:oC},[K4]:ZD},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:oC,headers:oC},[K4]:ZD}],[K4]:iN},{error:"FIPS is enabled but this partition does not support FIPS",[K4]:gn}],[K4]:iN},{conditions:iPA,rules:[{conditions:[cPA],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:oC,headers:oC},[K4]:ZD}],[K4]:iN},{error:"DualStack is enabled but this partition does not support DualStack",[K4]:gn}],[K4]:iN},mPA,{endpoint:{url:fPA,properties:oC,headers:oC},[K4]:ZD}],[K4]:iN}],[K4]:iN},{error:"Invalid Configuration: Missing Region",[K4]:gn}]};DSA.ruleSet=jjQ});
var Ge1=E((qqA)=>{Object.defineProperty(qqA,"__esModule",{value:!0});qqA.URL=qqA.DNS=void 0;qqA.default=lwQ;var uwQ=aQ1(),mwQ=dwQ(Ze1());function dwQ(A){return A&&A.__esModule?A:{default:A}}function cwQ(A){A=unescape(encodeURIComponent(A));let B=[];for(let Q=0;Q<A.length;++Q)B.push(A.charCodeAt(Q));return B}var wqA="6ba7b810-9dad-11d1-80b4-00c04fd430c8";qqA.DNS=wqA;var $qA="6ba7b811-9dad-11d1-80b4-00c04fd430c8";qqA.URL=$qA;function lwQ(A,B,Q){function D(Z,G,F,I){var Y;if(typeof Z==="string")Z=cwQ(Z);if(typeof G==="string")G=mwQ.default(G);if(((Y=G)===null||Y===void 0?void 0:Y.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let W=new Uint8Array(16+Z.length);if(W.set(G),W.set(Z,G.length),W=Q(W),W[6]=W[6]&15|B,W[8]=W[8]&63|128,F){I=I||0;for(let J=0;J<16;++J)F[I+J]=W[J];return F}return uwQ.unsafeStringify(W)}try{D.name=A}catch(Z){}return D.DNS=wqA,D.URL=$qA,D}});
var HLA=E((KLA)=>{Object.defineProperty(KLA,"__esModule",{value:!0});KLA.createGetRequest=KLQ;KLA.getCredentials=HLQ;var Se1=eB(),XLQ=mJ(),VLQ=x4(),CLQ=Fy();function KLQ(A){return new XLQ.HttpRequest({protocol:A.protocol,hostname:A.hostname,port:Number(A.port),path:A.pathname,query:Array.from(A.searchParams.entries()).reduce((B,[Q,D])=>{return B[Q]=D,B},{}),fragment:A.hash})}async function HLQ(A,B){let D=await CLQ.sdkStreamMixin(A.body).transformToString();if(A.statusCode===200){let Z=JSON.parse(D);if(typeof Z.AccessKeyId!=="string"||typeof Z.SecretAccessKey!=="string"||typeof Z.Token!=="string"||typeof Z.Expiration!=="string")throw new Se1.CredentialsProviderError("HTTP credential provider response not of the required format, an object matching: { AccessKeyId: string, SecretAccessKey: string, Token: string, Expiration: string(rfc3339) }",{logger:B});return{accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.Token,expiration:VLQ.parseRfc3339DateTime(Z.Expiration)}}if(A.statusCode>=400&&A.statusCode<500){let Z={};try{Z=JSON.parse(D)}catch(G){}throw Object.assign(new Se1.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B}),{Code:Z.Code,Message:Z.Message})}throw new Se1.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B})}});
var Ht1=E((A75,SEA)=>{var{defineProperty:EE1,getOwnPropertyDescriptor:FVQ,getOwnPropertyNames:IVQ}=Object,YVQ=Object.prototype.hasOwnProperty,bZ=(A,B)=>EE1(A,"name",{value:B,configurable:!0}),WVQ=(A,B)=>{for(var Q in B)EE1(A,Q,{get:B[Q],enumerable:!0})},JVQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of IVQ(B))if(!YVQ.call(A,Z)&&Z!==Q)EE1(A,Z,{get:()=>B[Z],enumerable:!(D=FVQ(B,Z))||D.enumerable})}return A},XVQ=(A)=>JVQ(EE1({},"__esModule",{value:!0}),A),MEA={};WVQ(MEA,{AWSSDKSigV4Signer:()=>HVQ,AwsSdkSigV4ASigner:()=>EVQ,AwsSdkSigV4Signer:()=>Kt1,NODE_AUTH_SCHEME_PREFERENCE_OPTIONS:()=>UVQ,NODE_SIGV4A_CONFIG_OPTIONS:()=>qVQ,getBearerTokenEnvKey:()=>REA,resolveAWSSDKSigV4Config:()=>LVQ,resolveAwsSdkSigV4AConfig:()=>$VQ,resolveAwsSdkSigV4Config:()=>OEA,validateSigningProperties:()=>Ct1});SEA.exports=XVQ(MEA);var VVQ=mJ(),CVQ=mJ(),EEA=bZ((A)=>CVQ.HttpResponse.isInstance(A)?A.headers?.date??A.headers?.Date:void 0,"getDateHeader"),Vt1=bZ((A)=>new Date(Date.now()+A),"getSkewCorrectedDate"),KVQ=bZ((A,B)=>Math.abs(Vt1(B).getTime()-A)>=300000,"isClockSkewed"),UEA=bZ((A,B)=>{let Q=Date.parse(A);if(KVQ(Q,B))return Q-Date.now();return B},"getUpdatedSystemClockOffset"),bQ1=bZ((A,B)=>{if(!B)throw new Error(`Property \`${A}\` is not resolved for AWS SDK SigV4Auth`);return B},"throwSigningPropertyError"),Ct1=bZ(async(A)=>{let B=bQ1("context",A.context),Q=bQ1("config",A.config),D=B.endpointV2?.properties?.authSchemes?.[0],G=await bQ1("signer",Q.signer)(D),F=A?.signingRegion,I=A?.signingRegionSet,Y=A?.signingName;return{config:Q,signer:G,signingRegion:F,signingRegionSet:I,signingName:Y}},"validateSigningProperties"),Kt1=class{static{bZ(this,"AwsSdkSigV4Signer")}async sign(A,B,Q){if(!VVQ.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let D=await Ct1(Q),{config:Z,signer:G}=D,{signingRegion:F,signingName:I}=D,Y=Q.context;if(Y?.authSchemes?.length??0>1){let[J,X]=Y.authSchemes;if(J?.name==="sigv4a"&&X?.name==="sigv4")F=X?.signingRegion??F,I=X?.signingName??I}return await G.sign(A,{signingDate:Vt1(Z.systemClockOffset),signingRegion:F,signingService:I})}errorHandler(A){return(B)=>{let Q=B.ServerTime??EEA(B.$response);if(Q){let D=bQ1("config",A.config),Z=D.systemClockOffset;if(D.systemClockOffset=UEA(Q,D.systemClockOffset),D.systemClockOffset!==Z&&B.$metadata)B.$metadata.clockSkewCorrected=!0}throw B}}successHandler(A,B){let Q=EEA(A);if(Q){let D=bQ1("config",B.config);D.systemClockOffset=UEA(Q,D.systemClockOffset)}}},HVQ=Kt1,zVQ=mJ(),EVQ=class extends Kt1{static{bZ(this,"AwsSdkSigV4ASigner")}async sign(A,B,Q){if(!zVQ.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let{config:D,signer:Z,signingRegion:G,signingRegionSet:F,signingName:I}=await Ct1(Q),W=(await D.sigv4aSigningRegionSet?.()??F??[G]).join(",");return await Z.sign(A,{signingDate:Vt1(D.systemClockOffset),signingRegion:W,signingService:I})}},wEA=bZ((A)=>typeof A==="string"&&A.length>0?A.split(",").map((B)=>B.trim()):[],"getArrayForCommaSeparatedString"),REA=bZ((A)=>`AWS_BEARER_TOKEN_${A.replace(/[\s-]/g,"_").toUpperCase()}`,"getBearerTokenEnvKey"),$EA="AWS_AUTH_SCHEME_PREFERENCE",qEA="auth_scheme_preference",UVQ={environmentVariableSelector:bZ((A,B)=>{if(B?.signingName){if(REA(B.signingName)in A)return["httpBearerAuth"]}if(!($EA in A))return;return wEA(A[$EA])},"environmentVariableSelector"),configFileSelector:bZ((A)=>{if(!(qEA in A))return;return wEA(A[qEA])},"configFileSelector"),default:[]},wVQ=VB(),NEA=eB(),$VQ=bZ((A)=>{return A.sigv4aSigningRegionSet=wVQ.normalizeProvider(A.sigv4aSigningRegionSet),A},"resolveAwsSdkSigV4AConfig"),qVQ={environmentVariableSelector(A){if(A.AWS_SIGV4A_SIGNING_REGION_SET)return A.AWS_SIGV4A_SIGNING_REGION_SET.split(",").map((B)=>B.trim());throw new NEA.ProviderError("AWS_SIGV4A_SIGNING_REGION_SET not set in env.",{tryNextLink:!0})},configFileSelector(A){if(A.sigv4a_signing_region_set)return(A.sigv4a_signing_region_set??"").split(",").map((B)=>B.trim());throw new NEA.ProviderError("sigv4a_signing_region_set not set in profile.",{tryNextLink:!0})},default:void 0},NVQ=Qz(),kh=VB(),LEA=zEA(),OEA=bZ((A)=>{let B=A.credentials,Q=!!A.credentials,D=void 0;Object.defineProperty(A,"credentials",{set(W){if(W&&W!==B&&W!==D)Q=!0;B=W;let J=TEA(A,{credentials:B,credentialDefaultProvider:A.credentialDefaultProvider}),X=PEA(A,J);if(Q&&!X.attributed)D=bZ(async(V)=>X(V).then((C)=>NVQ.setCredentialFeature(C,"CREDENTIALS_CODE","e")),"resolvedCredentials"),D.memoized=X.memoized,D.configBound=X.configBound,D.attributed=!0;else D=X},get(){return D},enumerable:!0,configurable:!0}),A.credentials=B;let{signingEscapePath:Z=!0,systemClockOffset:G=A.systemClockOffset||0,sha256:F}=A,I;if(A.signer)I=kh.normalizeProvider(A.signer);else if(A.regionInfoProvider)I=bZ(()=>kh.normalizeProvider(A.region)().then(async(W)=>[await A.regionInfoProvider(W,{useFipsEndpoint:await A.useFipsEndpoint(),useDualstackEndpoint:await A.useDualstackEndpoint()})||{},W]).then(([W,J])=>{let{signingRegion:X,signingService:V}=W;A.signingRegion=A.signingRegion||X||J,A.signingName=A.signingName||V||A.serviceId;let C={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||LEA.SignatureV4)(C)}),"signer");else I=bZ(async(W)=>{W=Object.assign({},{name:"sigv4",signingName:A.signingName||A.defaultSigningName,signingRegion:await kh.normalizeProvider(A.region)(),properties:{}},W);let{signingRegion:J,signingName:X}=W;A.signingRegion=A.signingRegion||J,A.signingName=A.signingName||X||A.serviceId;let V={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||LEA.SignatureV4)(V)},"signer");return Object.assign(A,{systemClockOffset:G,signingEscapePath:Z,signer:I})},"resolveAwsSdkSigV4Config"),LVQ=OEA;function TEA(A,{credentials:B,credentialDefaultProvider:Q}){let D;if(B)if(!B?.memoized)D=kh.memoizeIdentityProvider(B,kh.isIdentityExpired,kh.doesIdentityRequireRefresh);else D=B;else if(Q)D=kh.normalizeProvider(Q(Object.assign({},A,{parentClientConfig:A})));else D=bZ(async()=>{throw new Error("@aws-sdk/core::resolveAwsSdkSigV4Config - `credentials` not provided and no credentialDefaultProvider was configured.")},"credentialsProvider");return D.memoized=!0,D}bZ(TEA,"normalizeCredentialProvider");function PEA(A,B){if(B.configBound)return B;let Q=bZ(async(D)=>B({...D,callerClientConfig:A}),"fn");return Q.memoized=B.memoized,Q.configBound=!0,Q}bZ(PEA,"bindCallerConfig")});
var I5=E((K55,EXA)=>{var{defineProperty:Hz1,getOwnPropertyDescriptor:LDQ,getOwnPropertyNames:MDQ}=Object,RDQ=Object.prototype.hasOwnProperty,HXA=(A,B)=>Hz1(A,"name",{value:B,configurable:!0}),ODQ=(A,B)=>{for(var Q in B)Hz1(A,Q,{get:B[Q],enumerable:!0})},TDQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of MDQ(B))if(!RDQ.call(A,Z)&&Z!==Q)Hz1(A,Z,{get:()=>B[Z],enumerable:!(D=LDQ(B,Z))||D.enumerable})}return A},PDQ=(A)=>TDQ(Hz1({},"__esModule",{value:!0}),A),zXA={};ODQ(zXA,{getSmithyContext:()=>SDQ,normalizeProvider:()=>jDQ});EXA.exports=PDQ(zXA);var KXA=CXA(),SDQ=HXA((A)=>A[KXA.SMITHY_CONTEXT_KEY]||(A[KXA.SMITHY_CONTEXT_KEY]={}),"getSmithyContext"),jDQ=HXA((A)=>{if(typeof A==="function")return A;let B=Promise.resolve(A);return()=>B},"normalizeProvider")});
var IVA=E((GVA)=>{Object.defineProperty(GVA,"__esModule",{value:!0});GVA.toBase64=void 0;var nZQ=AD(),aZQ=cB(),sZQ=(A)=>{let B;if(typeof A==="string")B=aZQ.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return nZQ.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};GVA.toBase64=sZQ});
var Ie1=E((xD5,ANA)=>{var{defineProperty:oE1,getOwnPropertyDescriptor:j$Q,getOwnPropertyNames:k$Q}=Object,y$Q=Object.prototype.hasOwnProperty,fh=(A,B)=>oE1(A,"name",{value:B,configurable:!0}),_$Q=(A,B)=>{for(var Q in B)oE1(A,Q,{get:B[Q],enumerable:!0})},x$Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of k$Q(B))if(!y$Q.call(A,Z)&&Z!==Q)oE1(A,Z,{get:()=>B[Z],enumerable:!(D=j$Q(B,Z))||D.enumerable})}return A},v$Q=(A)=>x$Q(oE1({},"__esModule",{value:!0}),A),oqA={};_$Q(oqA,{isBrowserNetworkError:()=>eqA,isClockSkewCorrectedError:()=>tqA,isClockSkewError:()=>c$Q,isRetryableByTrait:()=>d$Q,isServerError:()=>p$Q,isThrottlingError:()=>l$Q,isTransientError:()=>Fe1});ANA.exports=v$Q(oqA);var b$Q=["AuthFailure","InvalidSignatureException","RequestExpired","RequestInTheFuture","RequestTimeTooSkewed","SignatureDoesNotMatch"],f$Q=["BandwidthLimitExceeded","EC2ThrottledException","LimitExceededException","PriorRequestNotComplete","ProvisionedThroughputExceededException","RequestLimitExceeded","RequestThrottled","RequestThrottledException","SlowDown","ThrottledException","Throttling","ThrottlingException","TooManyRequestsException","TransactionInProgressException"],h$Q=["TimeoutError","RequestTimeout","RequestTimeoutException"],g$Q=[500,502,503,504],u$Q=["ECONNRESET","ECONNREFUSED","EPIPE","ETIMEDOUT"],m$Q=["EHOSTUNREACH","ENETUNREACH","ENOTFOUND"],d$Q=fh((A)=>A.$retryable!==void 0,"isRetryableByTrait"),c$Q=fh((A)=>b$Q.includes(A.name),"isClockSkewError"),tqA=fh((A)=>A.$metadata?.clockSkewCorrected,"isClockSkewCorrectedError"),eqA=fh((A)=>{let B=new Set(["Failed to fetch","NetworkError when attempting to fetch resource","The Internet connection appears to be offline","Load failed","Network request failed"]);if(!(A&&A instanceof TypeError))return!1;return B.has(A.message)},"isBrowserNetworkError"),l$Q=fh((A)=>A.$metadata?.httpStatusCode===429||f$Q.includes(A.name)||A.$retryable?.throttling==!0,"isThrottlingError"),Fe1=fh((A,B=0)=>tqA(A)||h$Q.includes(A.name)||u$Q.includes(A?.code||"")||m$Q.includes(A?.code||"")||g$Q.includes(A.$metadata?.httpStatusCode||0)||eqA(A)||A.cause!==void 0&&B<=10&&Fe1(A.cause,B+1),"isTransientError"),p$Q=fh((A)=>{if(A.$metadata?.httpStatusCode!==void 0){let B=A.$metadata.httpStatusCode;if(500<=B&&B<=599&&!Fe1(A))return!0;return!1}return!1},"isServerError")});
var IqA=E((GqA)=>{Object.defineProperty(GqA,"__esModule",{value:!0});GqA.default=void 0;var qwQ=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;GqA.default=qwQ});
var JCA=E((n55,WCA)=>{var{defineProperty:jz1,getOwnPropertyDescriptor:QFQ,getOwnPropertyNames:DFQ}=Object,ZFQ=Object.prototype.hasOwnProperty,Vo1=(A,B)=>jz1(A,"name",{value:B,configurable:!0}),GFQ=(A,B)=>{for(var Q in B)jz1(A,Q,{get:B[Q],enumerable:!0})},FFQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of DFQ(B))if(!ZFQ.call(A,Z)&&Z!==Q)jz1(A,Z,{get:()=>B[Z],enumerable:!(D=QFQ(B,Z))||D.enumerable})}return A},IFQ=(A)=>FFQ(jz1({},"__esModule",{value:!0}),A),ICA={};GFQ(ICA,{escapeUri:()=>YCA,escapeUriPath:()=>WFQ});WCA.exports=IFQ(ICA);var YCA=Vo1((A)=>encodeURIComponent(A).replace(/[!'()*]/g,YFQ),"escapeUri"),YFQ=Vo1((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),WFQ=Vo1((A)=>A.split("/").map(YCA).join("/"),"escapeUriPath")});
var Jn=E((l35,AzA)=>{var{defineProperty:AE1,getOwnPropertyDescriptor:aJQ,getOwnPropertyNames:sJQ}=Object,rJQ=Object.prototype.hasOwnProperty,Wn=(A,B)=>AE1(A,"name",{value:B,configurable:!0}),oJQ=(A,B)=>{for(var Q in B)AE1(A,Q,{get:B[Q],enumerable:!0})},tJQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of sJQ(B))if(!rJQ.call(A,Z)&&Z!==Q)AE1(A,Z,{get:()=>B[Z],enumerable:!(D=aJQ(B,Z))||D.enumerable})}return A},eJQ=(A)=>tJQ(AE1({},"__esModule",{value:!0}),A),iHA={};oJQ(iHA,{ConditionObject:()=>o3.ConditionObject,DeprecatedObject:()=>o3.DeprecatedObject,EndpointError:()=>o3.EndpointError,EndpointObject:()=>o3.EndpointObject,EndpointObjectHeaders:()=>o3.EndpointObjectHeaders,EndpointObjectProperties:()=>o3.EndpointObjectProperties,EndpointParams:()=>o3.EndpointParams,EndpointResolverOptions:()=>o3.EndpointResolverOptions,EndpointRuleObject:()=>o3.EndpointRuleObject,ErrorRuleObject:()=>o3.ErrorRuleObject,EvaluateOptions:()=>o3.EvaluateOptions,Expression:()=>o3.Expression,FunctionArgv:()=>o3.FunctionArgv,FunctionObject:()=>o3.FunctionObject,FunctionReturn:()=>o3.FunctionReturn,ParameterObject:()=>o3.ParameterObject,ReferenceObject:()=>o3.ReferenceObject,ReferenceRecord:()=>o3.ReferenceRecord,RuleSetObject:()=>o3.RuleSetObject,RuleSetRules:()=>o3.RuleSetRules,TreeRuleObject:()=>o3.TreeRuleObject,awsEndpointFunctions:()=>eHA,getUserAgentPrefix:()=>DXQ,isIpAddress:()=>o3.isIpAddress,partition:()=>oHA,resolveEndpoint:()=>o3.resolveEndpoint,setPartitionInfo:()=>tHA,useDefaultPartitionInfo:()=>QXQ});AzA.exports=eJQ(iHA);var o3=$7(),nHA=Wn((A,B=!1)=>{if(B){for(let Q of A.split("."))if(!nHA(Q))return!1;return!0}if(!o3.isValidHostLabel(A))return!1;if(A.length<3||A.length>63)return!1;if(A!==A.toLowerCase())return!1;if(o3.isIpAddress(A))return!1;return!0},"isVirtualHostableS3Bucket"),pHA=":",AXQ="/",BXQ=Wn((A)=>{let B=A.split(pHA);if(B.length<6)return null;let[Q,D,Z,G,F,...I]=B;if(Q!=="arn"||D===""||Z===""||I.join(pHA)==="")return null;let Y=I.map((W)=>W.split(AXQ)).flat();return{partition:D,service:Z,region:G,accountId:F,resourceId:Y}},"parseArn"),aHA={partitions:[{id:"aws",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-east-1",name:"aws",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^(us|eu|ap|sa|ca|me|af|il|mx)\\-\\w+\\-\\d+$",regions:{"af-south-1":{description:"Africa (Cape Town)"},"ap-east-1":{description:"Asia Pacific (Hong Kong)"},"ap-east-2":{description:"Asia Pacific (Taipei)"},"ap-northeast-1":{description:"Asia Pacific (Tokyo)"},"ap-northeast-2":{description:"Asia Pacific (Seoul)"},"ap-northeast-3":{description:"Asia Pacific (Osaka)"},"ap-south-1":{description:"Asia Pacific (Mumbai)"},"ap-south-2":{description:"Asia Pacific (Hyderabad)"},"ap-southeast-1":{description:"Asia Pacific (Singapore)"},"ap-southeast-2":{description:"Asia Pacific (Sydney)"},"ap-southeast-3":{description:"Asia Pacific (Jakarta)"},"ap-southeast-4":{description:"Asia Pacific (Melbourne)"},"ap-southeast-5":{description:"Asia Pacific (Malaysia)"},"ap-southeast-7":{description:"Asia Pacific (Thailand)"},"aws-global":{description:"AWS Standard global region"},"ca-central-1":{description:"Canada (Central)"},"ca-west-1":{description:"Canada West (Calgary)"},"eu-central-1":{description:"Europe (Frankfurt)"},"eu-central-2":{description:"Europe (Zurich)"},"eu-north-1":{description:"Europe (Stockholm)"},"eu-south-1":{description:"Europe (Milan)"},"eu-south-2":{description:"Europe (Spain)"},"eu-west-1":{description:"Europe (Ireland)"},"eu-west-2":{description:"Europe (London)"},"eu-west-3":{description:"Europe (Paris)"},"il-central-1":{description:"Israel (Tel Aviv)"},"me-central-1":{description:"Middle East (UAE)"},"me-south-1":{description:"Middle East (Bahrain)"},"mx-central-1":{description:"Mexico (Central)"},"sa-east-1":{description:"South America (Sao Paulo)"},"us-east-1":{description:"US East (N. Virginia)"},"us-east-2":{description:"US East (Ohio)"},"us-west-1":{description:"US West (N. California)"},"us-west-2":{description:"US West (Oregon)"}}},{id:"aws-cn",outputs:{dnsSuffix:"amazonaws.com.cn",dualStackDnsSuffix:"api.amazonwebservices.com.cn",implicitGlobalRegion:"cn-northwest-1",name:"aws-cn",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^cn\\-\\w+\\-\\d+$",regions:{"aws-cn-global":{description:"AWS China global region"},"cn-north-1":{description:"China (Beijing)"},"cn-northwest-1":{description:"China (Ningxia)"}}},{id:"aws-us-gov",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-gov-west-1",name:"aws-us-gov",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^us\\-gov\\-\\w+\\-\\d+$",regions:{"aws-us-gov-global":{description:"AWS GovCloud (US) global region"},"us-gov-east-1":{description:"AWS GovCloud (US-East)"},"us-gov-west-1":{description:"AWS GovCloud (US-West)"}}},{id:"aws-iso",outputs:{dnsSuffix:"c2s.ic.gov",dualStackDnsSuffix:"c2s.ic.gov",implicitGlobalRegion:"us-iso-east-1",name:"aws-iso",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-iso\\-\\w+\\-\\d+$",regions:{"aws-iso-global":{description:"AWS ISO (US) global region"},"us-iso-east-1":{description:"US ISO East"},"us-iso-west-1":{description:"US ISO WEST"}}},{id:"aws-iso-b",outputs:{dnsSuffix:"sc2s.sgov.gov",dualStackDnsSuffix:"sc2s.sgov.gov",implicitGlobalRegion:"us-isob-east-1",name:"aws-iso-b",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isob\\-\\w+\\-\\d+$",regions:{"aws-iso-b-global":{description:"AWS ISOB (US) global region"},"us-isob-east-1":{description:"US ISOB East (Ohio)"}}},{id:"aws-iso-e",outputs:{dnsSuffix:"cloud.adc-e.uk",dualStackDnsSuffix:"cloud.adc-e.uk",implicitGlobalRegion:"eu-isoe-west-1",name:"aws-iso-e",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eu\\-isoe\\-\\w+\\-\\d+$",regions:{"aws-iso-e-global":{description:"AWS ISOE (Europe) global region"},"eu-isoe-west-1":{description:"EU ISOE West"}}},{id:"aws-iso-f",outputs:{dnsSuffix:"csp.hci.ic.gov",dualStackDnsSuffix:"csp.hci.ic.gov",implicitGlobalRegion:"us-isof-south-1",name:"aws-iso-f",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isof\\-\\w+\\-\\d+$",regions:{"aws-iso-f-global":{description:"AWS ISOF global region"},"us-isof-east-1":{description:"US ISOF EAST"},"us-isof-south-1":{description:"US ISOF SOUTH"}}},{id:"aws-eusc",outputs:{dnsSuffix:"amazonaws.eu",dualStackDnsSuffix:"amazonaws.eu",implicitGlobalRegion:"eusc-de-east-1",name:"aws-eusc",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eusc\\-(de)\\-\\w+\\-\\d+$",regions:{"eusc-de-east-1":{description:"EU (Germany)"}}}],version:"1.1"},sHA=aHA,rHA="",oHA=Wn((A)=>{let{partitions:B}=sHA;for(let D of B){let{regions:Z,outputs:G}=D;for(let[F,I]of Object.entries(Z))if(F===A)return{...G,...I}}for(let D of B){let{regionRegex:Z,outputs:G}=D;if(new RegExp(Z).test(A))return{...G}}let Q=B.find((D)=>D.id==="aws");if(!Q)throw new Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");return{...Q.outputs}},"partition"),tHA=Wn((A,B="")=>{sHA=A,rHA=B},"setPartitionInfo"),QXQ=Wn(()=>{tHA(aHA,"")},"useDefaultPartitionInfo"),DXQ=Wn(()=>rHA,"getUserAgentPrefix"),eHA={isVirtualHostableS3Bucket:nHA,parseArn:BXQ,partition:oHA};o3.customEndpointFunctions.aws=eHA});
var Jo1=E((XVA)=>{Object.defineProperty(XVA,"__esModule",{value:!0});XVA.ChecksumStream=void 0;var AGQ=Bn(),BGQ=J1("stream");class JVA extends BGQ.Duplex{constructor({expectedChecksum:A,checksum:B,source:Q,checksumSourceLocation:D,base64Encoder:Z}){var G,F;super();if(typeof Q.pipe==="function")this.source=Q;else throw new Error(`@smithy/util-stream: unsupported source type ${(F=(G=Q===null||Q===void 0?void 0:Q.constructor)===null||G===void 0?void 0:G.name)!==null&&F!==void 0?F:Q} in ChecksumStream.`);this.base64Encoder=Z!==null&&Z!==void 0?Z:AGQ.toBase64,this.expectedChecksum=A,this.checksum=B,this.checksumSourceLocation=D,this.source.pipe(this)}_read(A){}_write(A,B,Q){try{this.checksum.update(A),this.push(A)}catch(D){return Q(D)}return Q()}async _final(A){try{let B=await this.checksum.digest(),Q=this.base64Encoder(B);if(this.expectedChecksum!==Q)return A(new Error(`Checksum mismatch: expected "${this.expectedChecksum}" but received "${Q}" in response header "${this.checksumSourceLocation}".`))}catch(B){return A(B)}return this.push(null),A()}}XVA.ChecksumStream=JVA});
var KCA=E((a55,CCA)=>{var{defineProperty:kz1,getOwnPropertyDescriptor:JFQ,getOwnPropertyNames:XFQ}=Object,VFQ=Object.prototype.hasOwnProperty,CFQ=(A,B)=>kz1(A,"name",{value:B,configurable:!0}),KFQ=(A,B)=>{for(var Q in B)kz1(A,Q,{get:B[Q],enumerable:!0})},HFQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of XFQ(B))if(!VFQ.call(A,Z)&&Z!==Q)kz1(A,Z,{get:()=>B[Z],enumerable:!(D=JFQ(B,Z))||D.enumerable})}return A},zFQ=(A)=>HFQ(kz1({},"__esModule",{value:!0}),A),XCA={};KFQ(XCA,{buildQueryString:()=>VCA});CCA.exports=zFQ(XCA);var Co1=JCA();function VCA(A){let B=[];for(let Q of Object.keys(A).sort()){let D=A[Q];if(Q=Co1.escapeUri(Q),Array.isArray(D))for(let Z=0,G=D.length;Z<G;Z++)B.push(`${Q}=${Co1.escapeUri(D[Z])}`);else{let Z=Q;if(D||typeof D==="string")Z+=`=${Co1.escapeUri(D)}`;B.push(Z)}}return B.join("&")}CFQ(VCA,"buildQueryString")});
var KwA=E((n75,CwA)=>{var{defineProperty:SE1,getOwnPropertyDescriptor:AzQ,getOwnPropertyNames:BzQ}=Object,QzQ=Object.prototype.hasOwnProperty,JwA=(A,B)=>SE1(A,"name",{value:B,configurable:!0}),DzQ=(A,B)=>{for(var Q in B)SE1(A,Q,{get:B[Q],enumerable:!0})},ZzQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of BzQ(B))if(!QzQ.call(A,Z)&&Z!==Q)SE1(A,Z,{get:()=>B[Z],enumerable:!(D=AzQ(B,Z))||D.enumerable})}return A},GzQ=(A)=>ZzQ(SE1({},"__esModule",{value:!0}),A),XwA={};DzQ(XwA,{SelectorType:()=>VwA,booleanSelector:()=>FzQ,numberSelector:()=>IzQ});CwA.exports=GzQ(XwA);var FzQ=JwA((A,B,Q)=>{if(!(B in A))return;if(A[B]==="true")return!0;if(A[B]==="false")return!1;throw new Error(`Cannot load ${Q} "${B}". Expected "true" or "false", got ${A[B]}.`)},"booleanSelector"),IzQ=JwA((A,B,Q)=>{if(!(B in A))return;let D=parseInt(A[B],10);if(Number.isNaN(D))throw new TypeError(`Cannot load ${Q} '${B}'. Expected number, got '${A[B]}'.`);return D},"numberSelector"),VwA=((A)=>{return A.ENV="env",A.CONFIG="shared config entry",A})(VwA||{})});
var LLA=E((qLA)=>{Object.defineProperty(qLA,"__esModule",{value:!0});qLA.fromHttp=void 0;var wLQ=Sh(),$LQ=Qz(),qLQ=S3(),wLA=eB(),NLQ=wLQ.__importDefault(J1("fs/promises")),LLQ=CLA(),$LA=HLA(),MLQ=ULA(),RLQ="AWS_CONTAINER_CREDENTIALS_RELATIVE_URI",OLQ="http://*************",TLQ="AWS_CONTAINER_CREDENTIALS_FULL_URI",PLQ="AWS_CONTAINER_AUTHORIZATION_TOKEN_FILE",SLQ="AWS_CONTAINER_AUTHORIZATION_TOKEN",jLQ=(A={})=>{A.logger?.debug("@aws-sdk/credential-provider-http - fromHttp");let B,Q=A.awsContainerCredentialsRelativeUri??process.env[RLQ],D=A.awsContainerCredentialsFullUri??process.env[TLQ],Z=A.awsContainerAuthorizationToken??process.env[SLQ],G=A.awsContainerAuthorizationTokenFile??process.env[PLQ],F=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console.warn:A.logger.warn;if(Q&&D)F("@aws-sdk/credential-provider-http: you have set both awsContainerCredentialsRelativeUri and awsContainerCredentialsFullUri."),F("awsContainerCredentialsFullUri will take precedence.");if(Z&&G)F("@aws-sdk/credential-provider-http: you have set both awsContainerAuthorizationToken and awsContainerAuthorizationTokenFile."),F("awsContainerAuthorizationToken will take precedence.");if(D)B=D;else if(Q)B=`${OLQ}${Q}`;else throw new wLA.CredentialsProviderError(`No HTTP credential provider host provided.
Set AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI.`,{logger:A.logger});let I=new URL(B);LLQ.checkUrl(I,A.logger);let Y=new qLQ.NodeHttpHandler({requestTimeout:A.timeout??1000,connectionTimeout:A.timeout??1000});return MLQ.retryWrapper(async()=>{let W=$LA.createGetRequest(I);if(Z)W.headers.Authorization=Z;else if(G)W.headers.Authorization=(await NLQ.default.readFile(G)).toString();try{let J=await Y.handle(W);return $LA.getCredentials(J.response).then((X)=>$LQ.setCredentialFeature(X,"CREDENTIALS_HTTP","z"))}catch(J){throw new wLA.CredentialsProviderError(String(J),{logger:A.logger})}},A.maxRetries??3,A.timeout??1000)};qLA.fromHttp=jLQ});
var LQ1=E((J55,lJA)=>{var{defineProperty:Yz1,getOwnPropertyDescriptor:h7Q,getOwnPropertyNames:g7Q}=Object,u7Q=Object.prototype.hasOwnProperty,Qo1=(A,B)=>Yz1(A,"name",{value:B,configurable:!0}),m7Q=(A,B)=>{for(var Q in B)Yz1(A,Q,{get:B[Q],enumerable:!0})},d7Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of g7Q(B))if(!u7Q.call(A,Z)&&Z!==Q)Yz1(A,Z,{get:()=>B[Z],enumerable:!(D=h7Q(B,Z))||D.enumerable})}return A},c7Q=(A)=>d7Q(Yz1({},"__esModule",{value:!0}),A),mJA={};m7Q(mJA,{getLoggerPlugin:()=>l7Q,loggerMiddleware:()=>dJA,loggerMiddlewareOptions:()=>cJA});lJA.exports=c7Q(mJA);var dJA=Qo1(()=>(A,B)=>async(Q)=>{try{let D=await A(Q),{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y,overrideOutputFilterSensitiveLog:W}=I,J=Y??B.inputFilterSensitiveLog,X=W??B.outputFilterSensitiveLog,{$metadata:V,...C}=D.output;return F?.info?.({clientName:Z,commandName:G,input:J(Q.input),output:X(C),metadata:V}),D}catch(D){let{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y}=I,W=Y??B.inputFilterSensitiveLog;throw F?.error?.({clientName:Z,commandName:G,input:W(Q.input),error:D,metadata:D.$metadata}),D}},"loggerMiddleware"),cJA={name:"loggerMiddleware",tags:["LOGGER"],step:"initialize",override:!0},l7Q=Qo1((A)=>({applyToStack:Qo1((B)=>{B.add(dJA(),cJA)},"applyToStack")}),"getLoggerPlugin")});
var LRA=E((qRA)=>{Object.defineProperty(qRA,"__esModule",{value:!0});qRA.ruleSet=void 0;var ERA="required",Xz="fn",Vz="argv",Tn="ref",IRA=!0,YRA="isSet",Z41="booleanEquals",Rn="error",On="endpoint",mO="tree",ie1="PartitionResult",ne1="getAttr",WRA={[ERA]:!1,type:"String"},JRA={[ERA]:!0,default:!1,type:"Boolean"},XRA={[Tn]:"Endpoint"},URA={[Xz]:Z41,[Vz]:[{[Tn]:"UseFIPS"},!0]},wRA={[Xz]:Z41,[Vz]:[{[Tn]:"UseDualStack"},!0]},Jz={},VRA={[Xz]:ne1,[Vz]:[{[Tn]:ie1},"supportsFIPS"]},$RA={[Tn]:ie1},CRA={[Xz]:Z41,[Vz]:[!0,{[Xz]:ne1,[Vz]:[$RA,"supportsDualStack"]}]},KRA=[URA],HRA=[wRA],zRA=[{[Tn]:"Region"}],QOQ={version:"1.0",parameters:{Region:WRA,UseDualStack:JRA,UseFIPS:JRA,Endpoint:WRA},rules:[{conditions:[{[Xz]:YRA,[Vz]:[XRA]}],rules:[{conditions:KRA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:Rn},{conditions:HRA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:Rn},{endpoint:{url:XRA,properties:Jz,headers:Jz},type:On}],type:mO},{conditions:[{[Xz]:YRA,[Vz]:zRA}],rules:[{conditions:[{[Xz]:"aws.partition",[Vz]:zRA,assign:ie1}],rules:[{conditions:[URA,wRA],rules:[{conditions:[{[Xz]:Z41,[Vz]:[IRA,VRA]},CRA],rules:[{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Jz,headers:Jz},type:On}],type:mO},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:Rn}],type:mO},{conditions:KRA,rules:[{conditions:[{[Xz]:Z41,[Vz]:[VRA,IRA]}],rules:[{conditions:[{[Xz]:"stringEquals",[Vz]:[{[Xz]:ne1,[Vz]:[$RA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://oidc.{Region}.amazonaws.com",properties:Jz,headers:Jz},type:On},{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dnsSuffix}",properties:Jz,headers:Jz},type:On}],type:mO},{error:"FIPS is enabled but this partition does not support FIPS",type:Rn}],type:mO},{conditions:HRA,rules:[{conditions:[CRA],rules:[{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Jz,headers:Jz},type:On}],type:mO},{error:"DualStack is enabled but this partition does not support DualStack",type:Rn}],type:mO},{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dnsSuffix}",properties:Jz,headers:Jz},type:On}],type:mO}],type:mO},{error:"Invalid Configuration: Missing Region",type:Rn}]};qRA.ruleSet=QOQ});
var LSA=E((qSA)=>{Object.defineProperty(qSA,"__esModule",{value:!0});qSA.resolveHttpAuthRuntimeConfig=qSA.getHttpAuthExtensionConfiguration=void 0;var ejQ=(A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}};qSA.getHttpAuthExtensionConfiguration=ejQ;var AkQ=(A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}};qSA.resolveHttpAuthRuntimeConfig=AkQ});
var LVA=E((NVA)=>{Object.defineProperty(NVA,"__esModule",{value:!0});NVA.createChecksumStream=CGQ;var JGQ=By(),XGQ=Jo1(),VGQ=qVA();function CGQ(A){if(typeof ReadableStream==="function"&&JGQ.isReadableStream(A.source))return VGQ.createChecksumStream(A);return new XGQ.ChecksumStream(A)}});
var Le1=E((tD5,oNA)=>{var{defineProperty:BU1,getOwnPropertyDescriptor:HNQ,getOwnPropertyNames:zNQ}=Object,ENQ=Object.prototype.hasOwnProperty,UNQ=(A,B)=>BU1(A,"name",{value:B,configurable:!0}),wNQ=(A,B)=>{for(var Q in B)BU1(A,Q,{get:B[Q],enumerable:!0})},$NQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of zNQ(B))if(!ENQ.call(A,Z)&&Z!==Q)BU1(A,Z,{get:()=>B[Z],enumerable:!(D=HNQ(B,Z))||D.enumerable})}return A},qNQ=(A)=>$NQ(BU1({},"__esModule",{value:!0}),A),lNA={};wNQ(lNA,{ENV_ACCOUNT_ID:()=>rNA,ENV_CREDENTIAL_SCOPE:()=>sNA,ENV_EXPIRATION:()=>aNA,ENV_KEY:()=>pNA,ENV_SECRET:()=>iNA,ENV_SESSION:()=>nNA,fromEnv:()=>MNQ});oNA.exports=qNQ(lNA);var NNQ=Qz(),LNQ=eB(),pNA="AWS_ACCESS_KEY_ID",iNA="AWS_SECRET_ACCESS_KEY",nNA="AWS_SESSION_TOKEN",aNA="AWS_CREDENTIAL_EXPIRATION",sNA="AWS_CREDENTIAL_SCOPE",rNA="AWS_ACCOUNT_ID",MNQ=UNQ((A)=>async()=>{A?.logger?.debug("@aws-sdk/credential-provider-env - fromEnv");let B=process.env[pNA],Q=process.env[iNA],D=process.env[nNA],Z=process.env[aNA],G=process.env[sNA],F=process.env[rNA];if(B&&Q){let I={accessKeyId:B,secretAccessKey:Q,...D&&{sessionToken:D},...Z&&{expiration:new Date(Z)},...G&&{credentialScope:G},...F&&{accountId:F}};return NNQ.setCredentialFeature(I,"CREDENTIALS_ENV_VARS","g"),I}throw new LNQ.CredentialsProviderError("Unable to find environment variable credentials.",{logger:A?.logger})},"fromEnv")});
var MKA=E((LKA)=>{Object.defineProperty(LKA,"__esModule",{value:!0});LKA.splitStream=nIQ;async function nIQ(A){if(typeof A.stream==="function")A=A.stream();return A.tee()}});
var MMA=E((NMA)=>{Object.defineProperty(NMA,"__esModule",{value:!0});NMA.getRuntimeConfig=void 0;var nMQ=Sh(),aMQ=nMQ.__importDefault(OLA()),UMA=ZI(),wMA=tQ1(),JU1=V4(),sMQ=jG(),$MA=v4(),hh=QD(),qMA=S3(),rMQ=kG(),oMQ=hZ(),tMQ=XMA(),eMQ=x4(),ARQ=yG(),BRQ=x4(),QRQ=(A)=>{BRQ.emitWarningIfUnsupportedVersion(process.version);let B=ARQ.resolveDefaultsModeConfig(A),Q=()=>B().then(eMQ.loadConfigsForDefaultMode),D=tMQ.getRuntimeConfig(A);UMA.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??hh.loadConfig(UMA.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??rMQ.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??wMA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:aMQ.default.version}),maxAttempts:A?.maxAttempts??hh.loadConfig($MA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??hh.loadConfig(JU1.NODE_REGION_CONFIG_OPTIONS,{...JU1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:qMA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??hh.loadConfig({...$MA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||oMQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??sMQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??qMA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??hh.loadConfig(JU1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??hh.loadConfig(JU1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??hh.loadConfig(wMA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};NMA.getRuntimeConfig=QRQ});
var MQ1=E((X55,aJA)=>{var{defineProperty:Jz1,getOwnPropertyDescriptor:p7Q,getOwnPropertyNames:i7Q}=Object,n7Q=Object.prototype.hasOwnProperty,Wz1=(A,B)=>Jz1(A,"name",{value:B,configurable:!0}),a7Q=(A,B)=>{for(var Q in B)Jz1(A,Q,{get:B[Q],enumerable:!0})},s7Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of i7Q(B))if(!n7Q.call(A,Z)&&Z!==Q)Jz1(A,Z,{get:()=>B[Z],enumerable:!(D=p7Q(B,Z))||D.enumerable})}return A},r7Q=(A)=>s7Q(Jz1({},"__esModule",{value:!0}),A),pJA={};a7Q(pJA,{addRecursionDetectionMiddlewareOptions:()=>nJA,getRecursionDetectionPlugin:()=>ADQ,recursionDetectionMiddleware:()=>iJA});aJA.exports=r7Q(pJA);var o7Q=mJ(),Do1="X-Amzn-Trace-Id",t7Q="AWS_LAMBDA_FUNCTION_NAME",e7Q="_X_AMZN_TRACE_ID",iJA=Wz1((A)=>(B)=>async(Q)=>{let{request:D}=Q;if(!o7Q.HttpRequest.isInstance(D)||A.runtime!=="node")return B(Q);let Z=Object.keys(D.headers??{}).find((Y)=>Y.toLowerCase()===Do1.toLowerCase())??Do1;if(D.headers.hasOwnProperty(Z))return B(Q);let G=process.env[t7Q],F=process.env[e7Q],I=Wz1((Y)=>typeof Y==="string"&&Y.length>0,"nonEmptyString");if(I(G)&&I(F))D.headers[Do1]=F;return B({...Q,request:D})},"recursionDetectionMiddleware"),nJA={step:"build",tags:["RECURSION_DETECTION"],name:"recursionDetectionMiddleware",override:!0,priority:"low"},ADQ=Wz1((A)=>({applyToStack:Wz1((B)=>{B.add(iJA(A),nJA)},"applyToStack")}),"getRecursionDetectionPlugin")});
var NE1=E((SCQ)=>{var RCQ=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040",eEA="[:A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]["+RCQ+"]*",OCQ=new RegExp("^"+eEA+"$"),TCQ=function(A,B){let Q=[],D=B.exec(A);while(D){let Z=[];Z.startIndex=B.lastIndex-D[0].length;let G=D.length;for(let F=0;F<G;F++)Z.push(D[F]);Q.push(Z),D=B.exec(A)}return Q},PCQ=function(A){let B=OCQ.exec(A);return!(B===null||typeof B==="undefined")};SCQ.isExist=function(A){return typeof A!=="undefined"};SCQ.isEmptyObject=function(A){return Object.keys(A).length===0};SCQ.merge=function(A,B,Q){if(B){let D=Object.keys(B),Z=D.length;for(let G=0;G<Z;G++)if(Q==="strict")A[D[G]]=[B[D[G]]];else A[D[G]]=B[D[G]]}};SCQ.getValue=function(A){if(SCQ.isExist(A))return A;else return""};SCQ.isName=PCQ;SCQ.getAllMatches=TCQ;SCQ.nameRegexp=eEA});
var NKA=E(($KA)=>{Object.defineProperty($KA,"__esModule",{value:!0});$KA.sdkStreamMixin=void 0;var cIQ=S3(),lIQ=AD(),qo1=J1("stream"),pIQ=UKA(),wKA="The stream has already been transformed.",iIQ=(A)=>{var B,Q;if(!(A instanceof qo1.Readable))try{return pIQ.sdkStreamMixin(A)}catch(G){let F=((Q=(B=A===null||A===void 0?void 0:A.__proto__)===null||B===void 0?void 0:B.constructor)===null||Q===void 0?void 0:Q.name)||A;throw new Error(`Unexpected stream implementation, expect Stream.Readable instance, got ${F}`)}let D=!1,Z=async()=>{if(D)throw new Error(wKA);return D=!0,await cIQ.streamCollector(A)};return Object.assign(A,{transformToByteArray:Z,transformToString:async(G)=>{let F=await Z();if(G===void 0||Buffer.isEncoding(G))return lIQ.fromArrayBuffer(F.buffer,F.byteOffset,F.byteLength).toString(G);else return new TextDecoder(G).decode(F)},transformToWebStream:()=>{if(D)throw new Error(wKA);if(A.readableFlowing!==null)throw new Error("The stream has been consumed by other callbacks.");if(typeof qo1.Readable.toWeb!=="function")throw new Error("Readable.toWeb() is not supported. Please ensure a polyfill is available.");return D=!0,qo1.Readable.toWeb(A)}})};$KA.sdkStreamMixin=iIQ});
var NQ1=E((W55,uJA)=>{var{defineProperty:Iz1,getOwnPropertyDescriptor:j7Q,getOwnPropertyNames:k7Q}=Object,y7Q=Object.prototype.hasOwnProperty,Fz1=(A,B)=>Iz1(A,"name",{value:B,configurable:!0}),_7Q=(A,B)=>{for(var Q in B)Iz1(A,Q,{get:B[Q],enumerable:!0})},x7Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of k7Q(B))if(!y7Q.call(A,Z)&&Z!==Q)Iz1(A,Z,{get:()=>B[Z],enumerable:!(D=j7Q(B,Z))||D.enumerable})}return A},v7Q=(A)=>x7Q(Iz1({},"__esModule",{value:!0}),A),bJA={};_7Q(bJA,{getHostHeaderPlugin:()=>f7Q,hostHeaderMiddleware:()=>hJA,hostHeaderMiddlewareOptions:()=>gJA,resolveHostHeaderConfig:()=>fJA});uJA.exports=v7Q(bJA);var b7Q=mJ();function fJA(A){return A}Fz1(fJA,"resolveHostHeaderConfig");var hJA=Fz1((A)=>(B)=>async(Q)=>{if(!b7Q.HttpRequest.isInstance(Q.request))return B(Q);let{request:D}=Q,{handlerProtocol:Z=""}=A.requestHandler.metadata||{};if(Z.indexOf("h2")>=0&&!D.headers[":authority"])delete D.headers.host,D.headers[":authority"]=D.hostname+(D.port?":"+D.port:"");else if(!D.headers.host){let G=D.hostname;if(D.port!=null)G+=`:${D.port}`;D.headers.host=G}return B(Q)},"hostHeaderMiddleware"),gJA={name:"hostHeaderMiddleware",step:"build",priority:"low",tags:["HOST"],override:!0},f7Q=Fz1((A)=>({applyToStack:Fz1((B)=>{B.add(hJA(A),gJA)},"applyToStack")}),"getHostHeaderPlugin")});
var NUA=E((N75,qUA)=>{var{buildOptions:kKQ}=FUA(),yKQ=EUA(),{prettify:_KQ}=wUA(),xKQ=Tt1();class $UA{constructor(A){this.externalEntities={},this.options=kKQ(A)}parse(A,B){if(typeof A==="string");else if(A.toString)A=A.toString();else throw new Error("XML data is accepted in String or Bytes[] form.");if(B){if(B===!0)B={};let Z=xKQ.validate(A,B);if(Z!==!0)throw Error(`${Z.err.msg}:${Z.err.line}:${Z.err.col}`)}let Q=new yKQ(this.options);Q.addExternalEntities(this.externalEntities);let D=Q.parseXml(A);if(this.options.preserveOrder||D===void 0)return D;else return _KQ(D,this.options)}addEntity(A,B){if(B.indexOf("&")!==-1)throw new Error("Entity value can't have '&'");else if(A.indexOf("&")!==-1||A.indexOf(";")!==-1)throw new Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");else if(B==="&")throw new Error("An entity with value '&' is not permitted");else this.externalEntities[A]=B}}qUA.exports=$UA});
var Ne1=E((hNA)=>{Object.defineProperty(hNA,"__esModule",{value:!0});hNA.resolveHttpAuthSchemeConfig=hNA.resolveStsAuthConfig=hNA.defaultSTSHttpAuthSchemeProvider=hNA.defaultSTSHttpAuthSchemeParametersProvider=void 0;var DNQ=ZI(),qe1=I5(),ZNQ=oQ1(),GNQ=async(A,B,Q)=>{return{operation:qe1.getSmithyContext(B).operation,region:await qe1.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};hNA.defaultSTSHttpAuthSchemeParametersProvider=GNQ;function FNQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function fNA(A){return{schemeId:"smithy.api#noAuth"}}var INQ=(A)=>{let B=[];switch(A.operation){case"AssumeRoleWithSAML":{B.push(fNA(A));break}case"AssumeRoleWithWebIdentity":{B.push(fNA(A));break}default:B.push(FNQ(A))}return B};hNA.defaultSTSHttpAuthSchemeProvider=INQ;var YNQ=(A)=>Object.assign(A,{stsClientCtor:ZNQ.STSClient});hNA.resolveStsAuthConfig=YNQ;var WNQ=(A)=>{let B=hNA.resolveStsAuthConfig(A),Q=DNQ.resolveAwsSdkSigV4Config(B);return Object.assign(Q,{authSchemePreference:qe1.normalizeProvider(A.authSchemePreference??[])})};hNA.resolveHttpAuthSchemeConfig=WNQ});
var O$A=E((M$A)=>{Object.defineProperty(M$A,"__esModule",{value:!0});M$A.getEndpointUrlConfig=void 0;var q$A=e5(),N$A="AWS_ENDPOINT_URL",L$A="endpoint_url",WUQ=(A)=>({environmentVariableSelector:(B)=>{let Q=A.split(" ").map((G)=>G.toUpperCase()),D=B[[N$A,...Q].join("_")];if(D)return D;let Z=B[N$A];if(Z)return Z;return},configFileSelector:(B,Q)=>{if(Q&&B.services){let Z=Q[["services",B.services].join(q$A.CONFIG_PREFIX_SEPARATOR)];if(Z){let G=A.split(" ").map((I)=>I.toLowerCase()),F=Z[[G.join("_"),L$A].join(q$A.CONFIG_PREFIX_SEPARATOR)];if(F)return F}}let D=B[L$A];if(D)return D;return},default:void 0});M$A.getEndpointUrlConfig=WUQ});
var OLA=E((FZ5,uLQ)=>{uLQ.exports={name:"@aws-sdk/client-sso",description:"AWS SDK for JavaScript Sso Client for Node.js, Browser and React Native",version:"3.840.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-sso","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo sso"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sso",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-sso"}}});
var ORA=E((MRA)=>{Object.defineProperty(MRA,"__esModule",{value:!0});MRA.defaultEndpointResolver=void 0;var DOQ=Jn(),ae1=$7(),ZOQ=LRA(),GOQ=new ae1.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),FOQ=(A,B={})=>{return GOQ.get(A,()=>ae1.resolveEndpoint(ZOQ.ruleSet,{endpointParams:A,logger:B.logger}))};MRA.defaultEndpointResolver=FOQ;ae1.customEndpointFunctions.aws=DOQ.awsEndpointFunctions});
var Oh=E((q55,pXA)=>{var{defineProperty:qz1,getOwnPropertyDescriptor:IZQ,getOwnPropertyNames:YZQ}=Object,WZQ=Object.prototype.hasOwnProperty,Ay=(A,B)=>qz1(A,"name",{value:B,configurable:!0}),JZQ=(A,B)=>{for(var Q in B)qz1(A,Q,{get:B[Q],enumerable:!0})},XZQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of YZQ(B))if(!WZQ.call(A,Z)&&Z!==Q)qz1(A,Z,{get:()=>B[Z],enumerable:!(D=IZQ(B,Z))||D.enumerable})}return A},VZQ=(A)=>XZQ(qz1({},"__esModule",{value:!0}),A),mXA={};JZQ(mXA,{Field:()=>HZQ,Fields:()=>zZQ,HttpRequest:()=>EZQ,HttpResponse:()=>UZQ,IHttpRequest:()=>dXA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>CZQ,isValidHostname:()=>lXA,resolveHttpHandlerRuntimeConfig:()=>KZQ});pXA.exports=VZQ(mXA);var CZQ=Ay((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),KZQ=Ay((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),dXA=Zo1(),HZQ=class{static{Ay(this,"Field")}constructor({name:A,kind:B=dXA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},zZQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{Ay(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},EZQ=class A{static{Ay(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=cXA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function cXA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}Ay(cXA,"cloneQuery");var UZQ=class{static{Ay(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function lXA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}Ay(lXA,"isValidHostname")});
var PKA=E((TKA)=>{Object.defineProperty(TKA,"__esModule",{value:!0});TKA.splitStream=rIQ;var RKA=J1("stream"),sIQ=MKA(),OKA=By();async function rIQ(A){if(OKA.isReadableStream(A)||OKA.isBlob(A))return sIQ.splitStream(A);let B=new RKA.PassThrough,Q=new RKA.PassThrough;return A.pipe(B),A.pipe(Q),[B,Q]}});
var Q10=E((SOA)=>{Object.defineProperty(SOA,"__esModule",{value:!0});SOA.resolveHttpAuthSchemeConfig=SOA.resolveStsAuthConfig=SOA.defaultSTSHttpAuthSchemeProvider=SOA.defaultSTSHttpAuthSchemeParametersProvider=void 0;var xTQ=ZI(),B10=I5(),vTQ=Y41(),bTQ=async(A,B,Q)=>{return{operation:B10.getSmithyContext(B).operation,region:await B10.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};SOA.defaultSTSHttpAuthSchemeParametersProvider=bTQ;function fTQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function hTQ(A){return{schemeId:"smithy.api#noAuth"}}var gTQ=(A)=>{let B=[];switch(A.operation){case"AssumeRoleWithWebIdentity":{B.push(hTQ(A));break}default:B.push(fTQ(A))}return B};SOA.defaultSTSHttpAuthSchemeProvider=gTQ;var uTQ=(A)=>Object.assign(A,{stsClientCtor:vTQ.STSClient});SOA.resolveStsAuthConfig=uTQ;var mTQ=(A)=>{let B=SOA.resolveStsAuthConfig(A),Q=xTQ.resolveAwsSdkSigV4Config(B);return Object.assign(Q,{authSchemePreference:B10.normalizeProvider(A.authSchemePreference??[])})};SOA.resolveHttpAuthSchemeConfig=mTQ});
var QD=E((ID5,$$A)=>{var{defineProperty:mE1,getOwnPropertyDescriptor:tEQ,getOwnPropertyNames:eEQ}=Object,AUQ=Object.prototype.hasOwnProperty,Un=(A,B)=>mE1(A,"name",{value:B,configurable:!0}),BUQ=(A,B)=>{for(var Q in B)mE1(A,Q,{get:B[Q],enumerable:!0})},QUQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of eEQ(B))if(!AUQ.call(A,Z)&&Z!==Q)mE1(A,Z,{get:()=>B[Z],enumerable:!(D=tEQ(B,Z))||D.enumerable})}return A},DUQ=(A)=>QUQ(mE1({},"__esModule",{value:!0}),A),w$A={};BUQ(w$A,{loadConfig:()=>YUQ});$$A.exports=DUQ(w$A);var iQ1=eB();function rt1(A){try{let B=new Set(Array.from(A.match(/([A-Z_]){3,}/g)??[]));return B.delete("CONFIG"),B.delete("CONFIG_PREFIX_SEPARATOR"),B.delete("ENV"),[...B].join(", ")}catch(B){return A}}Un(rt1,"getSelectorName");var ZUQ=Un((A,B)=>async()=>{try{let Q=A(process.env,B);if(Q===void 0)throw new Error;return Q}catch(Q){throw new iQ1.CredentialsProviderError(Q.message||`Not found in ENV: ${rt1(A.toString())}`,{logger:B?.logger})}},"fromEnv"),U$A=e5(),GUQ=Un((A,{preferredFile:B="config",...Q}={})=>async()=>{let D=U$A.getProfileName(Q),{configFile:Z,credentialsFile:G}=await U$A.loadSharedConfigFiles(Q),F=G[D]||{},I=Z[D]||{},Y=B==="config"?{...F,...I}:{...I,...F};try{let J=A(Y,B==="config"?Z:G);if(J===void 0)throw new Error;return J}catch(W){throw new iQ1.CredentialsProviderError(W.message||`Not found in config files w/ profile [${D}]: ${rt1(A.toString())}`,{logger:Q.logger})}},"fromSharedConfigFiles"),FUQ=Un((A)=>typeof A==="function","isFunction"),IUQ=Un((A)=>FUQ(A)?async()=>await A():iQ1.fromStatic(A),"fromStatic"),YUQ=Un(({environmentVariableSelector:A,configFileSelector:B,default:Q},D={})=>{let{signingName:Z,logger:G}=D,F={signingName:Z,logger:G};return iQ1.memoize(iQ1.chain(ZUQ(A,F),GUQ(B,D),IUQ(Q)))},"loadConfig")});
var Qz=E((i35,_zA)=>{var{defineProperty:ZE1,getOwnPropertyDescriptor:ZXQ,getOwnPropertyNames:GXQ}=Object,FXQ=Object.prototype.hasOwnProperty,GE1=(A,B)=>ZE1(A,"name",{value:B,configurable:!0}),IXQ=(A,B)=>{for(var Q in B)ZE1(A,Q,{get:B[Q],enumerable:!0})},YXQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of GXQ(B))if(!FXQ.call(A,Z)&&Z!==Q)ZE1(A,Z,{get:()=>B[Z],enumerable:!(D=ZXQ(B,Z))||D.enumerable})}return A},WXQ=(A)=>YXQ(ZE1({},"__esModule",{value:!0}),A),SzA={};IXQ(SzA,{emitWarningIfUnsupportedVersion:()=>JXQ,setCredentialFeature:()=>jzA,setFeature:()=>kzA,setTokenFeature:()=>yzA,state:()=>Bt1});_zA.exports=WXQ(SzA);var Bt1={warningEmitted:!1},JXQ=GE1((A)=>{if(A&&!Bt1.warningEmitted&&parseInt(A.substring(1,A.indexOf(".")))<18)Bt1.warningEmitted=!0,process.emitWarning(`NodeDeprecationWarning: The AWS SDK for JavaScript (v3) will
no longer support Node.js 16.x on January 6, 2025.

To continue receiving updates to AWS services, bug fixes, and security
updates please upgrade to a supported Node.js LTS version.

More information can be found at: https://a.co/74kJMmI`)},"emitWarningIfUnsupportedVersion");function jzA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}GE1(jzA,"setCredentialFeature");function kzA(A,B,Q){if(!A.__aws_sdk_context)A.__aws_sdk_context={features:{}};else if(!A.__aws_sdk_context.features)A.__aws_sdk_context.features={};A.__aws_sdk_context.features[B]=Q}GE1(kzA,"setFeature");function yzA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}GE1(yzA,"setTokenFeature")});
var RqA=E((LqA)=>{Object.defineProperty(LqA,"__esModule",{value:!0});LqA.default=void 0;var nwQ=awQ(J1("crypto"));function awQ(A){return A&&A.__esModule?A:{default:A}}function swQ(A){if(Array.isArray(A))A=Buffer.from(A);else if(typeof A==="string")A=Buffer.from(A,"utf8");return nwQ.default.createHash("md5").update(A).digest()}var rwQ=swQ;LqA.default=rwQ});
var S3=E((s55,PCA)=>{var{create:EFQ,defineProperty:RQ1,getOwnPropertyDescriptor:UFQ,getOwnPropertyNames:wFQ,getPrototypeOf:$FQ}=Object,qFQ=Object.prototype.hasOwnProperty,P3=(A,B)=>RQ1(A,"name",{value:B,configurable:!0}),NFQ=(A,B)=>{for(var Q in B)RQ1(A,Q,{get:B[Q],enumerable:!0})},wCA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of wFQ(B))if(!qFQ.call(A,Z)&&Z!==Q)RQ1(A,Z,{get:()=>B[Z],enumerable:!(D=UFQ(B,Z))||D.enumerable})}return A},LFQ=(A,B,Q)=>(Q=A!=null?EFQ($FQ(A)):{},wCA(B||!A||!A.__esModule?RQ1(Q,"default",{value:A,enumerable:!0}):Q,A)),MFQ=(A)=>wCA(RQ1({},"__esModule",{value:!0}),A),$CA={};NFQ($CA,{DEFAULT_REQUEST_TIMEOUT:()=>OCA,NodeHttp2Handler:()=>xFQ,NodeHttpHandler:()=>jFQ,streamCollector:()=>bFQ});PCA.exports=MFQ($CA);var qCA=FCA(),NCA=KCA(),Ko1=J1("http"),Ho1=J1("https"),RFQ=["ECONNRESET","EPIPE","ETIMEDOUT"],LCA=P3((A)=>{let B={};for(let Q of Object.keys(A)){let D=A[Q];B[Q]=Array.isArray(D)?D.join(","):D}return B},"getTransformedHeaders"),YV={setTimeout:(A,B)=>setTimeout(A,B),clearTimeout:(A)=>clearTimeout(A)},HCA=1000,OFQ=P3((A,B,Q=0)=>{if(!Q)return-1;let D=P3((Z)=>{let G=YV.setTimeout(()=>{A.destroy(),B(Object.assign(new Error(`Socket timed out without establishing a connection within ${Q} ms`),{name:"TimeoutError"}))},Q-Z),F=P3((I)=>{if(I?.connecting)I.on("connect",()=>{YV.clearTimeout(G)});else YV.clearTimeout(G)},"doWithSocket");if(A.socket)F(A.socket);else A.on("socket",F)},"registerTimeout");if(Q<2000)return D(0),0;return YV.setTimeout(D.bind(null,HCA),HCA)},"setConnectionTimeout"),TFQ=3000,PFQ=P3((A,{keepAlive:B,keepAliveMsecs:Q},D=TFQ)=>{if(B!==!0)return-1;let Z=P3(()=>{if(A.socket)A.socket.setKeepAlive(B,Q||0);else A.on("socket",(G)=>{G.setKeepAlive(B,Q||0)})},"registerListener");if(D===0)return Z(),0;return YV.setTimeout(Z,D)},"setSocketKeepAlive"),zCA=3000,SFQ=P3((A,B,Q=OCA)=>{let D=P3((Z)=>{let G=Q-Z,F=P3(()=>{A.destroy(),B(Object.assign(new Error(`Connection timed out after ${Q} ms`),{name:"TimeoutError"}))},"onTimeout");if(A.socket)A.socket.setTimeout(G,F),A.on("close",()=>A.socket?.removeListener("timeout",F));else A.setTimeout(G,F)},"registerTimeout");if(0<Q&&Q<6000)return D(0),0;return YV.setTimeout(D.bind(null,Q===0?0:zCA),zCA)},"setSocketTimeout"),MCA=J1("stream"),ECA=6000;async function zo1(A,B,Q=ECA){let D=B.headers??{},Z=D.Expect||D.expect,G=-1,F=!0;if(Z==="100-continue")F=await Promise.race([new Promise((I)=>{G=Number(YV.setTimeout(()=>I(!0),Math.max(ECA,Q)))}),new Promise((I)=>{A.on("continue",()=>{YV.clearTimeout(G),I(!0)}),A.on("response",()=>{YV.clearTimeout(G),I(!1)}),A.on("error",()=>{YV.clearTimeout(G),I(!1)})})]);if(F)RCA(A,B.body)}P3(zo1,"writeRequestBody");function RCA(A,B){if(B instanceof MCA.Readable){B.pipe(A);return}if(B){if(Buffer.isBuffer(B)||typeof B==="string"){A.end(B);return}let Q=B;if(typeof Q==="object"&&Q.buffer&&typeof Q.byteOffset==="number"&&typeof Q.byteLength==="number"){A.end(Buffer.from(Q.buffer,Q.byteOffset,Q.byteLength));return}A.end(Buffer.from(B));return}A.end()}P3(RCA,"writeBody");var OCA=0,jFQ=class A{constructor(B){this.socketWarningTimestamp=0,this.metadata={handlerProtocol:"http/1.1"},this.configProvider=new Promise((Q,D)=>{if(typeof B==="function")B().then((Z)=>{Q(this.resolveDefaultConfig(Z))}).catch(D);else Q(this.resolveDefaultConfig(B))})}static{P3(this,"NodeHttpHandler")}static create(B){if(typeof B?.handle==="function")return B;return new A(B)}static checkSocketUsage(B,Q,D=console){let{sockets:Z,requests:G,maxSockets:F}=B;if(typeof F!=="number"||F===1/0)return Q;let I=15000;if(Date.now()-I<Q)return Q;if(Z&&G)for(let Y in Z){let W=Z[Y]?.length??0,J=G[Y]?.length??0;if(W>=F&&J>=2*F)return D?.warn?.(`@smithy/node-http-handler:WARN - socket usage at capacity=${W} and ${J} additional requests are enqueued.
See https://docs.aws.amazon.com/sdk-for-javascript/v3/developer-guide/node-configuring-maxsockets.html
or increase socketAcquisitionWarningTimeout=(millis) in the NodeHttpHandler config.`),Date.now()}return Q}resolveDefaultConfig(B){let{requestTimeout:Q,connectionTimeout:D,socketTimeout:Z,socketAcquisitionWarningTimeout:G,httpAgent:F,httpsAgent:I}=B||{},Y=!0,W=50;return{connectionTimeout:D,requestTimeout:Q??Z,socketAcquisitionWarningTimeout:G,httpAgent:(()=>{if(F instanceof Ko1.Agent||typeof F?.destroy==="function")return F;return new Ko1.Agent({keepAlive:!0,maxSockets:50,...F})})(),httpsAgent:(()=>{if(I instanceof Ho1.Agent||typeof I?.destroy==="function")return I;return new Ho1.Agent({keepAlive:!0,maxSockets:50,...I})})(),logger:console}}destroy(){this.config?.httpAgent?.destroy(),this.config?.httpsAgent?.destroy()}async handle(B,{abortSignal:Q}={}){if(!this.config)this.config=await this.configProvider;return new Promise((D,Z)=>{let G=void 0,F=[],I=P3(async(N)=>{await G,F.forEach(YV.clearTimeout),D(N)},"resolve"),Y=P3(async(N)=>{await G,F.forEach(YV.clearTimeout),Z(N)},"reject");if(!this.config)throw new Error("Node HTTP request handler config is not resolved");if(Q?.aborted){let N=new Error("Request aborted");N.name="AbortError",Y(N);return}let W=B.protocol==="https:",J=W?this.config.httpsAgent:this.config.httpAgent;F.push(YV.setTimeout(()=>{this.socketWarningTimestamp=A.checkSocketUsage(J,this.socketWarningTimestamp,this.config.logger)},this.config.socketAcquisitionWarningTimeout??(this.config.requestTimeout??2000)+(this.config.connectionTimeout??1000)));let X=NCA.buildQueryString(B.query||{}),V=void 0;if(B.username!=null||B.password!=null){let N=B.username??"",O=B.password??"";V=`${N}:${O}`}let C=B.path;if(X)C+=`?${X}`;if(B.fragment)C+=`#${B.fragment}`;let K=B.hostname??"";if(K[0]==="["&&K.endsWith("]"))K=B.hostname.slice(1,-1);else K=B.hostname;let H={headers:B.headers,host:K,method:B.method,path:C,port:B.port,agent:J,auth:V},$=(W?Ho1.request:Ko1.request)(H,(N)=>{let O=new qCA.HttpResponse({statusCode:N.statusCode||-1,reason:N.statusMessage,headers:LCA(N.headers),body:N});I({response:O})});if($.on("error",(N)=>{if(RFQ.includes(N.code))Y(Object.assign(N,{name:"TimeoutError"}));else Y(N)}),Q){let N=P3(()=>{$.destroy();let O=new Error("Request aborted");O.name="AbortError",Y(O)},"onAbort");if(typeof Q.addEventListener==="function"){let O=Q;O.addEventListener("abort",N,{once:!0}),$.once("close",()=>O.removeEventListener("abort",N))}else Q.onabort=N}F.push(OFQ($,Y,this.config.connectionTimeout)),F.push(SFQ($,Y,this.config.requestTimeout));let L=H.agent;if(typeof L==="object"&&"keepAlive"in L)F.push(PFQ($,{keepAlive:L.keepAlive,keepAliveMsecs:L.keepAliveMsecs}));G=zo1($,B,this.config.requestTimeout).catch((N)=>{return F.forEach(YV.clearTimeout),Z(N)})})}updateHttpClientConfig(B,Q){this.config=void 0,this.configProvider=this.configProvider.then((D)=>{return{...D,[B]:Q}})}httpHandlerConfigs(){return this.config??{}}},UCA=J1("http2"),kFQ=LFQ(J1("http2")),yFQ=class{constructor(A){this.sessions=[],this.sessions=A??[]}static{P3(this,"NodeHttp2ConnectionPool")}poll(){if(this.sessions.length>0)return this.sessions.shift()}offerLast(A){this.sessions.push(A)}contains(A){return this.sessions.includes(A)}remove(A){this.sessions=this.sessions.filter((B)=>B!==A)}[Symbol.iterator](){return this.sessions[Symbol.iterator]()}destroy(A){for(let B of this.sessions)if(B===A){if(!B.destroyed)B.destroy()}}},_FQ=class{constructor(A){if(this.sessionCache=new Map,this.config=A,this.config.maxConcurrency&&this.config.maxConcurrency<=0)throw new RangeError("maxConcurrency must be greater than zero.")}static{P3(this,"NodeHttp2ConnectionManager")}lease(A,B){let Q=this.getUrlString(A),D=this.sessionCache.get(Q);if(D){let I=D.poll();if(I&&!this.config.disableConcurrency)return I}let Z=kFQ.default.connect(Q);if(this.config.maxConcurrency)Z.settings({maxConcurrentStreams:this.config.maxConcurrency},(I)=>{if(I)throw new Error("Fail to set maxConcurrentStreams to "+this.config.maxConcurrency+"when creating new session for "+A.destination.toString())});Z.unref();let G=P3(()=>{Z.destroy(),this.deleteSession(Q,Z)},"destroySessionCb");if(Z.on("goaway",G),Z.on("error",G),Z.on("frameError",G),Z.on("close",()=>this.deleteSession(Q,Z)),B.requestTimeout)Z.setTimeout(B.requestTimeout,G);let F=this.sessionCache.get(Q)||new yFQ;return F.offerLast(Z),this.sessionCache.set(Q,F),Z}deleteSession(A,B){let Q=this.sessionCache.get(A);if(!Q)return;if(!Q.contains(B))return;Q.remove(B),this.sessionCache.set(A,Q)}release(A,B){let Q=this.getUrlString(A);this.sessionCache.get(Q)?.offerLast(B)}destroy(){for(let[A,B]of this.sessionCache){for(let Q of B){if(!Q.destroyed)Q.destroy();B.remove(Q)}this.sessionCache.delete(A)}}setMaxConcurrentStreams(A){if(A&&A<=0)throw new RangeError("maxConcurrentStreams must be greater than zero.");this.config.maxConcurrency=A}setDisableConcurrentStreams(A){this.config.disableConcurrency=A}getUrlString(A){return A.destination.toString()}},xFQ=class A{constructor(B){this.metadata={handlerProtocol:"h2"},this.connectionManager=new _FQ({}),this.configProvider=new Promise((Q,D)=>{if(typeof B==="function")B().then((Z)=>{Q(Z||{})}).catch(D);else Q(B||{})})}static{P3(this,"NodeHttp2Handler")}static create(B){if(typeof B?.handle==="function")return B;return new A(B)}destroy(){this.connectionManager.destroy()}async handle(B,{abortSignal:Q}={}){if(!this.config){if(this.config=await this.configProvider,this.connectionManager.setDisableConcurrentStreams(this.config.disableConcurrentStreams||!1),this.config.maxConcurrentStreams)this.connectionManager.setMaxConcurrentStreams(this.config.maxConcurrentStreams)}let{requestTimeout:D,disableConcurrentStreams:Z}=this.config;return new Promise((G,F)=>{let I=!1,Y=void 0,W=P3(async(f)=>{await Y,G(f)},"resolve"),J=P3(async(f)=>{await Y,F(f)},"reject");if(Q?.aborted){I=!0;let f=new Error("Request aborted");f.name="AbortError",J(f);return}let{hostname:X,method:V,port:C,protocol:K,query:H}=B,z="";if(B.username!=null||B.password!=null){let f=B.username??"",k=B.password??"";z=`${f}:${k}@`}let $=`${K}//${z}${X}${C?`:${C}`:""}`,L={destination:new URL($)},N=this.connectionManager.lease(L,{requestTimeout:this.config?.sessionTimeout,disableConcurrentStreams:Z||!1}),O=P3((f)=>{if(Z)this.destroySession(N);I=!0,J(f)},"rejectWithDestroy"),R=NCA.buildQueryString(H||{}),T=B.path;if(R)T+=`?${R}`;if(B.fragment)T+=`#${B.fragment}`;let j=N.request({...B.headers,[UCA.constants.HTTP2_HEADER_PATH]:T,[UCA.constants.HTTP2_HEADER_METHOD]:V});if(N.ref(),j.on("response",(f)=>{let k=new qCA.HttpResponse({statusCode:f[":status"]||-1,headers:LCA(f),body:j});if(I=!0,W({response:k}),Z)N.close(),this.connectionManager.deleteSession($,N)}),D)j.setTimeout(D,()=>{j.close();let f=new Error(`Stream timed out because of no activity for ${D} ms`);f.name="TimeoutError",O(f)});if(Q){let f=P3(()=>{j.close();let k=new Error("Request aborted");k.name="AbortError",O(k)},"onAbort");if(typeof Q.addEventListener==="function"){let k=Q;k.addEventListener("abort",f,{once:!0}),j.once("close",()=>k.removeEventListener("abort",f))}else Q.onabort=f}j.on("frameError",(f,k,c)=>{O(new Error(`Frame type id ${f} in stream id ${c} has failed with code ${k}.`))}),j.on("error",O),j.on("aborted",()=>{O(new Error(`HTTP/2 stream is abnormally aborted in mid-communication with result code ${j.rstCode}.`))}),j.on("close",()=>{if(N.unref(),Z)N.destroy();if(!I)O(new Error("Unexpected error: http2 request did not get a response"))}),Y=zo1(j,B,D)})}updateHttpClientConfig(B,Q){this.config=void 0,this.configProvider=this.configProvider.then((D)=>{return{...D,[B]:Q}})}httpHandlerConfigs(){return this.config??{}}destroySession(B){if(!B.destroyed)B.destroy()}},vFQ=class extends MCA.Writable{constructor(){super(...arguments);this.bufferedBytes=[]}static{P3(this,"Collector")}_write(A,B,Q){this.bufferedBytes.push(A),Q()}},bFQ=P3((A)=>{if(fFQ(A))return TCA(A);return new Promise((B,Q)=>{let D=new vFQ;A.pipe(D),A.on("error",(Z)=>{D.end(),Q(Z)}),D.on("error",Q),D.on("finish",function(){let Z=new Uint8Array(Buffer.concat(this.bufferedBytes));B(Z)})})},"streamCollector"),fFQ=P3((A)=>typeof ReadableStream==="function"&&A instanceof ReadableStream,"isReadableStreamInstance");async function TCA(A){let B=[],Q=A.getReader(),D=!1,Z=0;while(!D){let{done:I,value:Y}=await Q.read();if(Y)B.push(Y),Z+=Y.length;D=I}let G=new Uint8Array(Z),F=0;for(let I of B)G.set(I,F),F+=I.length;return G}P3(TCA,"collectReadableStream")});
var SUA=E((M75,PUA)=>{var hKQ=TUA(),gKQ={attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,cdataPropName:!1,format:!1,indentBy:"  ",suppressEmptyNode:!1,suppressUnpairedNode:!0,suppressBooleanAttributes:!0,tagValueProcessor:function(A,B){return B},attributeValueProcessor:function(A,B){return B},preserveOrder:!1,commentPropName:!1,unpairedTags:[],entities:[{regex:new RegExp("&","g"),val:"&amp;"},{regex:new RegExp(">","g"),val:"&gt;"},{regex:new RegExp("<","g"),val:"&lt;"},{regex:new RegExp("'","g"),val:"&apos;"},{regex:new RegExp('"',"g"),val:"&quot;"}],processEntities:!0,stopNodes:[],oneListGroup:!1};function Jy(A){if(this.options=Object.assign({},gKQ,A),this.options.ignoreAttributes||this.options.attributesGroupName)this.isAttribute=function(){return!1};else this.attrPrefixLen=this.options.attributeNamePrefix.length,this.isAttribute=dKQ;if(this.processTextOrObjNode=uKQ,this.options.format)this.indentate=mKQ,this.tagEndChar=`>
`,this.newLine=`
`;else this.indentate=function(){return""},this.tagEndChar=">",this.newLine=""}Jy.prototype.build=function(A){if(this.options.preserveOrder)return hKQ(A,this.options);else{if(Array.isArray(A)&&this.options.arrayNodeName&&this.options.arrayNodeName.length>1)A={[this.options.arrayNodeName]:A};return this.j2x(A,0).val}};Jy.prototype.j2x=function(A,B){let Q="",D="";for(let Z in A){if(!Object.prototype.hasOwnProperty.call(A,Z))continue;if(typeof A[Z]==="undefined"){if(this.isAttribute(Z))D+=""}else if(A[Z]===null)if(this.isAttribute(Z))D+="";else if(Z[0]==="?")D+=this.indentate(B)+"<"+Z+"?"+this.tagEndChar;else D+=this.indentate(B)+"<"+Z+"/"+this.tagEndChar;else if(A[Z]instanceof Date)D+=this.buildTextValNode(A[Z],Z,"",B);else if(typeof A[Z]!=="object"){let G=this.isAttribute(Z);if(G)Q+=this.buildAttrPairStr(G,""+A[Z]);else if(Z===this.options.textNodeName){let F=this.options.tagValueProcessor(Z,""+A[Z]);D+=this.replaceEntitiesValue(F)}else D+=this.buildTextValNode(A[Z],Z,"",B)}else if(Array.isArray(A[Z])){let G=A[Z].length,F="",I="";for(let Y=0;Y<G;Y++){let W=A[Z][Y];if(typeof W==="undefined");else if(W===null)if(Z[0]==="?")D+=this.indentate(B)+"<"+Z+"?"+this.tagEndChar;else D+=this.indentate(B)+"<"+Z+"/"+this.tagEndChar;else if(typeof W==="object")if(this.options.oneListGroup){let J=this.j2x(W,B+1);if(F+=J.val,this.options.attributesGroupName&&W.hasOwnProperty(this.options.attributesGroupName))I+=J.attrStr}else F+=this.processTextOrObjNode(W,Z,B);else if(this.options.oneListGroup){let J=this.options.tagValueProcessor(Z,W);J=this.replaceEntitiesValue(J),F+=J}else F+=this.buildTextValNode(W,Z,"",B)}if(this.options.oneListGroup)F=this.buildObjectNode(F,Z,I,B);D+=F}else if(this.options.attributesGroupName&&Z===this.options.attributesGroupName){let G=Object.keys(A[Z]),F=G.length;for(let I=0;I<F;I++)Q+=this.buildAttrPairStr(G[I],""+A[Z][G[I]])}else D+=this.processTextOrObjNode(A[Z],Z,B)}return{attrStr:Q,val:D}};Jy.prototype.buildAttrPairStr=function(A,B){if(B=this.options.attributeValueProcessor(A,""+B),B=this.replaceEntitiesValue(B),this.options.suppressBooleanAttributes&&B==="true")return" "+A;else return" "+A+'="'+B+'"'};function uKQ(A,B,Q){let D=this.j2x(A,Q+1);if(A[this.options.textNodeName]!==void 0&&Object.keys(A).length===1)return this.buildTextValNode(A[this.options.textNodeName],B,D.attrStr,Q);else return this.buildObjectNode(D.val,B,D.attrStr,Q)}Jy.prototype.buildObjectNode=function(A,B,Q,D){if(A==="")if(B[0]==="?")return this.indentate(D)+"<"+B+Q+"?"+this.tagEndChar;else return this.indentate(D)+"<"+B+Q+this.closeTag(B)+this.tagEndChar;else{let Z="</"+B+this.tagEndChar,G="";if(B[0]==="?")G="?",Z="";if((Q||Q==="")&&A.indexOf("<")===-1)return this.indentate(D)+"<"+B+Q+G+">"+A+Z;else if(this.options.commentPropName!==!1&&B===this.options.commentPropName&&G.length===0)return this.indentate(D)+`<!--${A}-->`+this.newLine;else return this.indentate(D)+"<"+B+Q+G+this.tagEndChar+A+this.indentate(D)+Z}};Jy.prototype.closeTag=function(A){let B="";if(this.options.unpairedTags.indexOf(A)!==-1){if(!this.options.suppressUnpairedNode)B="/"}else if(this.options.suppressEmptyNode)B="/";else B=`></${A}`;return B};Jy.prototype.buildTextValNode=function(A,B,Q,D){if(this.options.cdataPropName!==!1&&B===this.options.cdataPropName)return this.indentate(D)+`<![CDATA[${A}]]>`+this.newLine;else if(this.options.commentPropName!==!1&&B===this.options.commentPropName)return this.indentate(D)+`<!--${A}-->`+this.newLine;else if(B[0]==="?")return this.indentate(D)+"<"+B+Q+"?"+this.tagEndChar;else{let Z=this.options.tagValueProcessor(B,A);if(Z=this.replaceEntitiesValue(Z),Z==="")return this.indentate(D)+"<"+B+Q+this.closeTag(B)+this.tagEndChar;else return this.indentate(D)+"<"+B+Q+">"+Z+"</"+B+this.tagEndChar}};Jy.prototype.replaceEntitiesValue=function(A){if(A&&A.length>0&&this.options.processEntities)for(let B=0;B<this.options.entities.length;B++){let Q=this.options.entities[B];A=A.replace(Q.regex,Q.val)}return A};function mKQ(A){return this.options.indentBy.repeat(A)}function dKQ(A){if(A.startsWith(this.options.attributeNamePrefix)&&A!==this.options.textNodeName)return A.substr(this.attrPrefixLen);else return!1}PUA.exports=Jy});
var Sh=E((p35,DE1)=>{var BzA,QzA,DzA,ZzA,GzA,FzA,IzA,YzA,WzA,JzA,XzA,VzA,CzA,BE1,At1,KzA,HzA,zzA,Xn,EzA,UzA,wzA,$zA,qzA,NzA,LzA,MzA,RzA,QE1,OzA,TzA,PzA;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof DE1==="object"&&typeof p35==="object")A(Q(B,Q(p35)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};BzA=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},QzA=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},DzA=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},ZzA=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},GzA=function(G,F){return function(I,Y){F(I,Y,G)}},FzA=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},IzA=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},YzA=function(G){return typeof G==="symbol"?G:"".concat(G)},WzA=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},JzA=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},XzA=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},VzA=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},CzA=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))QE1(F,G,I)},QE1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},BE1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},At1=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},KzA=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(At1(arguments[F]));return G},HzA=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},zzA=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},Xn=function(G){return this instanceof Xn?(this.v=G,this):new Xn(G)},EzA=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof Xn?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},UzA=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:Xn(G[W](X)),done:!1}:J?J(X):X}:J}},wzA=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof BE1==="function"?BE1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},$zA=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};qzA=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")QE1(F,G,I[Y])}return Q(F,G),F},NzA=function(G){return G&&G.__esModule?G:{default:G}},LzA=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},MzA=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},RzA=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},OzA=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};TzA=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},PzA=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",BzA),A("__assign",QzA),A("__rest",DzA),A("__decorate",ZzA),A("__param",GzA),A("__esDecorate",FzA),A("__runInitializers",IzA),A("__propKey",YzA),A("__setFunctionName",WzA),A("__metadata",JzA),A("__awaiter",XzA),A("__generator",VzA),A("__exportStar",CzA),A("__createBinding",QE1),A("__values",BE1),A("__read",At1),A("__spread",KzA),A("__spreadArrays",HzA),A("__spreadArray",zzA),A("__await",Xn),A("__asyncGenerator",EzA),A("__asyncDelegator",UzA),A("__asyncValues",wzA),A("__makeTemplateObject",$zA),A("__importStar",qzA),A("__importDefault",NzA),A("__classPrivateFieldGet",LzA),A("__classPrivateFieldSet",MzA),A("__classPrivateFieldIn",RzA),A("__addDisposableResource",OzA),A("__disposeResources",TzA),A("__rewriteRelativeImportExtension",PzA)})});
var SqA=E((TqA)=>{Object.defineProperty(TqA,"__esModule",{value:!0});TqA.default=void 0;var owQ=OqA(Ge1()),twQ=OqA(RqA());function OqA(A){return A&&A.__esModule?A:{default:A}}var ewQ=owQ.default("v3",48,twQ.default),A$Q=ewQ;TqA.default=A$Q});
var T3=E(($55,uXA)=>{var{defineProperty:wz1,getOwnPropertyDescriptor:AZQ,getOwnPropertyNames:BZQ}=Object,QZQ=Object.prototype.hasOwnProperty,$z1=(A,B)=>wz1(A,"name",{value:B,configurable:!0}),DZQ=(A,B)=>{for(var Q in B)wz1(A,Q,{get:B[Q],enumerable:!0})},ZZQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of BZQ(B))if(!QZQ.call(A,Z)&&Z!==Q)wz1(A,Z,{get:()=>B[Z],enumerable:!(D=AZQ(B,Z))||D.enumerable})}return A},GZQ=(A)=>ZZQ(wz1({},"__esModule",{value:!0}),A),xXA={};DZQ(xXA,{deserializerMiddleware:()=>vXA,deserializerMiddlewareOption:()=>fXA,getSerdePlugin:()=>gXA,serializerMiddleware:()=>bXA,serializerMiddlewareOption:()=>hXA});uXA.exports=GZQ(xXA);var FZQ=_XA(),vXA=$z1((A,B)=>(Q,D)=>async(Z)=>{let{response:G}=await Q(Z);try{let F=await B(G,A);return{response:G,output:F}}catch(F){if(Object.defineProperty(F,"$response",{value:G}),!("$metadata"in F)){try{F.message+=`
  Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.`}catch(Y){if(!D.logger||D.logger?.constructor?.name==="NoOpLogger")console.warn("Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.");else D.logger?.warn?.("Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.")}if(typeof F.$responseBodyText!=="undefined"){if(F.$response)F.$response.body=F.$responseBodyText}try{if(FZQ.HttpResponse.isInstance(G)){let{headers:Y={}}=G,W=Object.entries(Y);F.$metadata={httpStatusCode:G.statusCode,requestId:Go1(/^x-[\w-]+-request-?id$/,W),extendedRequestId:Go1(/^x-[\w-]+-id-2$/,W),cfId:Go1(/^x-[\w-]+-cf-id$/,W)}}}catch(Y){}}throw F}},"deserializerMiddleware"),Go1=$z1((A,B)=>{return(B.find(([Q])=>{return Q.match(A)})||[void 0,void 0])[1]},"findHeader"),bXA=$z1((A,B)=>(Q,D)=>async(Z)=>{let G=A,F=D.endpointV2?.url&&G.urlParser?async()=>G.urlParser(D.endpointV2.url):G.endpoint;if(!F)throw new Error("No valid endpoint provider available.");let I=await B(Z.input,{...A,endpoint:F});return Q({...Z,request:I})},"serializerMiddleware"),fXA={name:"deserializerMiddleware",step:"deserialize",tags:["DESERIALIZER"],override:!0},hXA={name:"serializerMiddleware",step:"serialize",tags:["SERIALIZER"],override:!0};function gXA(A,B,Q){return{applyToStack:(D)=>{D.add(vXA(A,Q),fXA),D.add(bXA(A,B),hXA)}}}$z1(gXA,"getSerdePlugin")});
var TG=E((AD5,iwA)=>{var{defineProperty:vE1,getOwnPropertyDescriptor:ozQ,getOwnPropertyNames:tzQ}=Object,ezQ=Object.prototype.hasOwnProperty,cwA=(A,B)=>vE1(A,"name",{value:B,configurable:!0}),AEQ=(A,B)=>{for(var Q in B)vE1(A,Q,{get:B[Q],enumerable:!0})},BEQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of tzQ(B))if(!ezQ.call(A,Z)&&Z!==Q)vE1(A,Z,{get:()=>B[Z],enumerable:!(D=ozQ(B,Z))||D.enumerable})}return A},QEQ=(A)=>BEQ(vE1({},"__esModule",{value:!0}),A),lwA={};AEQ(lwA,{contentLengthMiddleware:()=>dt1,contentLengthMiddlewareOptions:()=>pwA,getContentLengthPlugin:()=>ZEQ});iwA.exports=QEQ(lwA);var DEQ=mwA(),dwA="content-length";function dt1(A){return(B)=>async(Q)=>{let D=Q.request;if(DEQ.HttpRequest.isInstance(D)){let{body:Z,headers:G}=D;if(Z&&Object.keys(G).map((F)=>F.toLowerCase()).indexOf(dwA)===-1)try{let F=A(Z);D.headers={...D.headers,[dwA]:String(F)}}catch(F){}}return B({...Q,request:D})}}cwA(dt1,"contentLengthMiddleware");var pwA={step:"build",tags:["SET_CONTENT_LENGTH","CONTENT_LENGTH"],name:"contentLengthMiddleware",override:!0},ZEQ=cwA((A)=>({applyToStack:(B)=>{B.add(dt1(A.bodyLengthChecker),pwA)}}),"getContentLengthPlugin")});
var TPA=E((AG5,OPA)=>{var{create:BjQ,defineProperty:X41,getOwnPropertyDescriptor:QjQ,getOwnPropertyNames:DjQ,getPrototypeOf:ZjQ}=Object,GjQ=Object.prototype.hasOwnProperty,_G=(A,B)=>X41(A,"name",{value:B,configurable:!0}),FjQ=(A,B)=>{for(var Q in B)X41(A,Q,{get:B[Q],enumerable:!0})},LPA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of DjQ(B))if(!GjQ.call(A,Z)&&Z!==Q)X41(A,Z,{get:()=>B[Z],enumerable:!(D=QjQ(B,Z))||D.enumerable})}return A},wy=(A,B,Q)=>(Q=A!=null?BjQ(ZjQ(A)):{},LPA(B||!A||!A.__esModule?X41(Q,"default",{value:A,enumerable:!0}):Q,A)),IjQ=(A)=>LPA(X41({},"__esModule",{value:!0}),A),MPA={};FjQ(MPA,{fromIni:()=>wjQ});OPA.exports=IjQ(MPA);var s10=e5(),$y=Qz(),J41=eB(),YjQ=_G((A,B,Q)=>{let D={EcsContainer:_G(async(Z)=>{let{fromHttp:G}=await Promise.resolve().then(()=>wy(ke1())),{fromContainerMetadata:F}=await Promise.resolve().then(()=>wy($F()));return Q?.debug("@aws-sdk/credential-provider-ini - credential_source is EcsContainer"),async()=>J41.chain(G(Z??{}),F(Z))().then(a10)},"EcsContainer"),Ec2InstanceMetadata:_G(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Ec2InstanceMetadata");let{fromInstanceMetadata:G}=await Promise.resolve().then(()=>wy($F()));return async()=>G(Z)().then(a10)},"Ec2InstanceMetadata"),Environment:_G(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Environment");let{fromEnv:G}=await Promise.resolve().then(()=>wy(Le1()));return async()=>G(Z)().then(a10)},"Environment")};if(A in D)return D[A];else throw new J41.CredentialsProviderError(`Unsupported credential source in profile ${B}. Got ${A}, expected EcsContainer or Ec2InstanceMetadata or Environment.`,{logger:Q})},"resolveCredentialSource"),a10=_G((A)=>$y.setCredentialFeature(A,"CREDENTIALS_PROFILE_NAMED_PROVIDER","p"),"setNamedProvider"),WjQ=_G((A,{profile:B="default",logger:Q}={})=>{return Boolean(A)&&typeof A==="object"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1&&["undefined","string"].indexOf(typeof A.external_id)>-1&&["undefined","string"].indexOf(typeof A.mfa_serial)>-1&&(JjQ(A,{profile:B,logger:Q})||XjQ(A,{profile:B,logger:Q}))},"isAssumeRoleProfile"),JjQ=_G((A,{profile:B,logger:Q})=>{let D=typeof A.source_profile==="string"&&typeof A.credential_source==="undefined";if(D)Q?.debug?.(`    ${B} isAssumeRoleWithSourceProfile source_profile=${A.source_profile}`);return D},"isAssumeRoleWithSourceProfile"),XjQ=_G((A,{profile:B,logger:Q})=>{let D=typeof A.credential_source==="string"&&typeof A.source_profile==="undefined";if(D)Q?.debug?.(`    ${B} isCredentialSourceProfile credential_source=${A.credential_source}`);return D},"isCredentialSourceProfile"),VjQ=_G(async(A,B,Q,D={})=>{Q.logger?.debug("@aws-sdk/credential-provider-ini - resolveAssumeRoleCredentials (STS)");let Z=B[A],{source_profile:G,region:F}=Z;if(!Q.roleAssumer){let{getDefaultRoleAssumer:Y}=await Promise.resolve().then(()=>wy(u10()));Q.roleAssumer=Y({...Q.clientConfig,credentialProviderLogger:Q.logger,parentClientConfig:{...Q?.parentClientConfig,region:F??Q?.parentClientConfig?.region}},Q.clientPlugins)}if(G&&G in D)throw new J41.CredentialsProviderError(`Detected a cycle attempting to resolve credentials for profile ${s10.getProfileName(Q)}. Profiles visited: `+Object.keys(D).join(", "),{logger:Q.logger});Q.logger?.debug(`@aws-sdk/credential-provider-ini - finding credential resolver using ${G?`source_profile=[${G}]`:`profile=[${A}]`}`);let I=G?RPA(G,B,Q,{...D,[G]:!0},$PA(B[G]??{})):(await YjQ(Z.credential_source,A,Q.logger)(Q))();if($PA(Z))return I.then((Y)=>$y.setCredentialFeature(Y,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"));else{let Y={RoleArn:Z.role_arn,RoleSessionName:Z.role_session_name||`aws-sdk-js-${Date.now()}`,ExternalId:Z.external_id,DurationSeconds:parseInt(Z.duration_seconds||"3600",10)},{mfa_serial:W}=Z;if(W){if(!Q.mfaCodeProvider)throw new J41.CredentialsProviderError(`Profile ${A} requires multi-factor authentication, but no MFA code callback was provided.`,{logger:Q.logger,tryNextLink:!1});Y.SerialNumber=W,Y.TokenCode=await Q.mfaCodeProvider(W)}let J=await I;return Q.roleAssumer(J,Y).then((X)=>$y.setCredentialFeature(X,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"))}},"resolveAssumeRoleCredentials"),$PA=_G((A)=>{return!A.role_arn&&!!A.credential_source},"isCredentialSourceWithoutRoleArn"),CjQ=_G((A)=>Boolean(A)&&typeof A==="object"&&typeof A.credential_process==="string","isProcessProfile"),KjQ=_G(async(A,B)=>Promise.resolve().then(()=>wy(c10())).then(({fromProcess:Q})=>Q({...A,profile:B})().then((D)=>$y.setCredentialFeature(D,"CREDENTIALS_PROFILE_PROCESS","v"))),"resolveProcessCredentials"),HjQ=_G(async(A,B,Q={})=>{let{fromSSO:D}=await Promise.resolve().then(()=>wy(A10()));return D({profile:A,logger:Q.logger,parentClientConfig:Q.parentClientConfig,clientConfig:Q.clientConfig})().then((Z)=>{if(B.sso_session)return $y.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO","r");else return $y.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO_LEGACY","t")})},"resolveSsoCredentials"),zjQ=_G((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),qPA=_G((A)=>Boolean(A)&&typeof A==="object"&&typeof A.aws_access_key_id==="string"&&typeof A.aws_secret_access_key==="string"&&["undefined","string"].indexOf(typeof A.aws_session_token)>-1&&["undefined","string"].indexOf(typeof A.aws_account_id)>-1,"isStaticCredsProfile"),NPA=_G(async(A,B)=>{B?.logger?.debug("@aws-sdk/credential-provider-ini - resolveStaticCredentials");let Q={accessKeyId:A.aws_access_key_id,secretAccessKey:A.aws_secret_access_key,sessionToken:A.aws_session_token,...A.aws_credential_scope&&{credentialScope:A.aws_credential_scope},...A.aws_account_id&&{accountId:A.aws_account_id}};return $y.setCredentialFeature(Q,"CREDENTIALS_PROFILE","n")},"resolveStaticCredentials"),EjQ=_G((A)=>Boolean(A)&&typeof A==="object"&&typeof A.web_identity_token_file==="string"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1,"isWebIdentityProfile"),UjQ=_G(async(A,B)=>Promise.resolve().then(()=>wy(n10())).then(({fromTokenFile:Q})=>Q({webIdentityTokenFile:A.web_identity_token_file,roleArn:A.role_arn,roleSessionName:A.role_session_name,roleAssumerWithWebIdentity:B.roleAssumerWithWebIdentity,logger:B.logger,parentClientConfig:B.parentClientConfig})().then((D)=>$y.setCredentialFeature(D,"CREDENTIALS_PROFILE_STS_WEB_ID_TOKEN","q"))),"resolveWebIdentityCredentials"),RPA=_G(async(A,B,Q,D={},Z=!1)=>{let G=B[A];if(Object.keys(D).length>0&&qPA(G))return NPA(G,Q);if(Z||WjQ(G,{profile:A,logger:Q.logger}))return VjQ(A,B,Q,D);if(qPA(G))return NPA(G,Q);if(EjQ(G))return UjQ(G,Q);if(CjQ(G))return KjQ(Q,A);if(zjQ(G))return await HjQ(A,G,Q);throw new J41.CredentialsProviderError(`Could not resolve credentials using profile: [${A}] in configuration/credentials file(s).`,{logger:Q.logger})},"resolveProfileData"),wjQ=_G((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/credential-provider-ini - fromIni");let D=await s10.parseKnownFiles(Q);return RPA(s10.getProfileName({profile:A.profile??B?.profile}),D,Q)},"fromIni")});
var TTA=E((RTA)=>{Object.defineProperty(RTA,"__esModule",{value:!0});RTA.resolveRuntimeExtensions=void 0;var qTA=B41(),NTA=mJ(),LTA=x4(),MTA=$TA(),UPQ=(A,B)=>{let Q=Object.assign(qTA.getAwsRegionExtensionConfiguration(A),LTA.getDefaultExtensionConfiguration(A),NTA.getHttpHandlerExtensionConfiguration(A),MTA.getHttpAuthExtensionConfiguration(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,qTA.resolveAwsRegionExtensionConfiguration(Q),LTA.resolveDefaultRuntimeConfig(Q),NTA.resolveHttpHandlerRuntimeConfig(Q),MTA.resolveHttpAuthRuntimeConfig(Q))};RTA.resolveRuntimeExtensions=UPQ});
var TUA=E((L75,OUA)=>{function vKQ(A,B){let Q="";if(B.format&&B.indentBy.length>0)Q=`
`;return MUA(A,B,"",Q)}function MUA(A,B,Q,D){let Z="",G=!1;for(let F=0;F<A.length;F++){let I=A[F],Y=bKQ(I);if(Y===void 0)continue;let W="";if(Q.length===0)W=Y;else W=`${Q}.${Y}`;if(Y===B.textNodeName){let K=I[Y];if(!fKQ(W,B))K=B.tagValueProcessor(Y,K),K=RUA(K,B);if(G)Z+=D;Z+=K,G=!1;continue}else if(Y===B.cdataPropName){if(G)Z+=D;Z+=`<![CDATA[${I[Y][0][B.textNodeName]}]]>`,G=!1;continue}else if(Y===B.commentPropName){Z+=D+`<!--${I[Y][0][B.textNodeName]}-->`,G=!0;continue}else if(Y[0]==="?"){let K=LUA(I[":@"],B),H=Y==="?xml"?"":D,z=I[Y][0][B.textNodeName];z=z.length!==0?" "+z:"",Z+=H+`<${Y}${z}${K}?>`,G=!0;continue}let J=D;if(J!=="")J+=B.indentBy;let X=LUA(I[":@"],B),V=D+`<${Y}${X}`,C=MUA(I[Y],B,W,J);if(B.unpairedTags.indexOf(Y)!==-1)if(B.suppressUnpairedNode)Z+=V+">";else Z+=V+"/>";else if((!C||C.length===0)&&B.suppressEmptyNode)Z+=V+"/>";else if(C&&C.endsWith(">"))Z+=V+`>${C}${D}</${Y}>`;else{if(Z+=V+">",C&&D!==""&&(C.includes("/>")||C.includes("</")))Z+=D+B.indentBy+C+D;else Z+=C;Z+=`</${Y}>`}G=!0}return Z}function bKQ(A){let B=Object.keys(A);for(let Q=0;Q<B.length;Q++){let D=B[Q];if(!A.hasOwnProperty(D))continue;if(D!==":@")return D}}function LUA(A,B){let Q="";if(A&&!B.ignoreAttributes)for(let D in A){if(!A.hasOwnProperty(D))continue;let Z=B.attributeValueProcessor(D,A[D]);if(Z=RUA(Z,B),Z===!0&&B.suppressBooleanAttributes)Q+=` ${D.substr(B.attributeNamePrefix.length)}`;else Q+=` ${D.substr(B.attributeNamePrefix.length)}="${Z}"`}return Q}function fKQ(A,B){A=A.substr(0,A.length-B.textNodeName.length-1);let Q=A.substr(A.lastIndexOf(".")+1);for(let D in B.stopNodes)if(B.stopNodes[D]===A||B.stopNodes[D]==="*."+Q)return!0;return!1}function RUA(A,B){if(A&&A.length>0&&B.processEntities)for(let Q=0;Q<B.entities.length;Q++){let D=B.entities[Q];A=A.replace(D.regex,D.val)}return A}OUA.exports=vKQ});
var TXA=E((H55,OXA)=>{var{defineProperty:zz1,getOwnPropertyDescriptor:kDQ,getOwnPropertyNames:yDQ}=Object,_DQ=Object.prototype.hasOwnProperty,Ez1=(A,B)=>zz1(A,"name",{value:B,configurable:!0}),xDQ=(A,B)=>{for(var Q in B)zz1(A,Q,{get:B[Q],enumerable:!0})},vDQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of yDQ(B))if(!_DQ.call(A,Z)&&Z!==Q)zz1(A,Z,{get:()=>B[Z],enumerable:!(D=kDQ(B,Z))||D.enumerable})}return A},bDQ=(A)=>vDQ(zz1({},"__esModule",{value:!0}),A),UXA={};xDQ(UXA,{AlgorithmId:()=>NXA,EndpointURLScheme:()=>qXA,FieldPosition:()=>LXA,HttpApiKeyAuthLocation:()=>$XA,HttpAuthLocation:()=>wXA,IniSectionType:()=>MXA,RequestHandlerProtocol:()=>RXA,SMITHY_CONTEXT_KEY:()=>mDQ,getDefaultClientConfiguration:()=>gDQ,resolveDefaultRuntimeConfig:()=>uDQ});OXA.exports=bDQ(UXA);var wXA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(wXA||{}),$XA=((A)=>{return A.HEADER="header",A.QUERY="query",A})($XA||{}),qXA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(qXA||{}),NXA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(NXA||{}),fDQ=Ez1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),hDQ=Ez1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),gDQ=Ez1((A)=>{return fDQ(A)},"getDefaultClientConfiguration"),uDQ=Ez1((A)=>{return hDQ(A)},"resolveDefaultRuntimeConfig"),LXA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(LXA||{}),mDQ="__smithy_context",MXA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(MXA||{}),RXA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(RXA||{})});
var Tt1=E((pCQ)=>{var Ot1=NE1(),bCQ={allowBooleanAttributes:!1,unpairedTags:[]};pCQ.validate=function(A,B){B=Object.assign({},bCQ,B);let Q=[],D=!1,Z=!1;if(A[0]==="\uFEFF")A=A.substr(1);for(let G=0;G<A.length;G++)if(A[G]==="<"&&A[G+1]==="?"){if(G+=2,G=QUA(A,G),G.err)return G}else if(A[G]==="<"){let F=G;if(G++,A[G]==="!"){G=DUA(A,G);continue}else{let I=!1;if(A[G]==="/")I=!0,G++;let Y="";for(;G<A.length&&A[G]!==">"&&A[G]!==" "&&A[G]!=="\t"&&A[G]!==`
`&&A[G]!=="\r";G++)Y+=A[G];if(Y=Y.trim(),Y[Y.length-1]==="/")Y=Y.substring(0,Y.length-1),G--;if(!lCQ(Y)){let X;if(Y.trim().length===0)X="Invalid space after '<'.";else X="Tag '"+Y+"' is an invalid name.";return fZ("InvalidTag",X,dJ(A,G))}let W=gCQ(A,G);if(W===!1)return fZ("InvalidAttr","Attributes for '"+Y+"' have open quote.",dJ(A,G));let J=W.value;if(G=W.index,J[J.length-1]==="/"){let X=G-J.length;J=J.substring(0,J.length-1);let V=ZUA(J,B);if(V===!0)D=!0;else return fZ(V.err.code,V.err.msg,dJ(A,X+V.err.line))}else if(I)if(!W.tagClosed)return fZ("InvalidTag","Closing tag '"+Y+"' doesn't have proper closing.",dJ(A,G));else if(J.trim().length>0)return fZ("InvalidTag","Closing tag '"+Y+"' can't have attributes or invalid starting.",dJ(A,F));else if(Q.length===0)return fZ("InvalidTag","Closing tag '"+Y+"' has not been opened.",dJ(A,F));else{let X=Q.pop();if(Y!==X.tagName){let V=dJ(A,X.tagStartPos);return fZ("InvalidTag","Expected closing tag '"+X.tagName+"' (opened in line "+V.line+", col "+V.col+") instead of closing tag '"+Y+"'.",dJ(A,F))}if(Q.length==0)Z=!0}else{let X=ZUA(J,B);if(X!==!0)return fZ(X.err.code,X.err.msg,dJ(A,G-J.length+X.err.line));if(Z===!0)return fZ("InvalidXml","Multiple possible root nodes found.",dJ(A,G));else if(B.unpairedTags.indexOf(Y)!==-1);else Q.push({tagName:Y,tagStartPos:F});D=!0}for(G++;G<A.length;G++)if(A[G]==="<")if(A[G+1]==="!"){G++,G=DUA(A,G);continue}else if(A[G+1]==="?"){if(G=QUA(A,++G),G.err)return G}else break;else if(A[G]==="&"){let X=dCQ(A,G);if(X==-1)return fZ("InvalidChar","char '&' is not expected.",dJ(A,G));G=X}else if(Z===!0&&!BUA(A[G]))return fZ("InvalidXml","Extra text at the end",dJ(A,G));if(A[G]==="<")G--}}else{if(BUA(A[G]))continue;return fZ("InvalidChar","char '"+A[G]+"' is not expected.",dJ(A,G))}if(!D)return fZ("InvalidXml","Start tag expected.",1);else if(Q.length==1)return fZ("InvalidTag","Unclosed tag '"+Q[0].tagName+"'.",dJ(A,Q[0].tagStartPos));else if(Q.length>0)return fZ("InvalidXml","Invalid '"+JSON.stringify(Q.map((G)=>G.tagName),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1});return!0};function BUA(A){return A===" "||A==="\t"||A===`
`||A==="\r"}function QUA(A,B){let Q=B;for(;B<A.length;B++)if(A[B]=="?"||A[B]==" "){let D=A.substr(Q,B-Q);if(B>5&&D==="xml")return fZ("InvalidXml","XML declaration allowed only at the start of the document.",dJ(A,B));else if(A[B]=="?"&&A[B+1]==">"){B++;break}else continue}return B}function DUA(A,B){if(A.length>B+5&&A[B+1]==="-"&&A[B+2]==="-"){for(B+=3;B<A.length;B++)if(A[B]==="-"&&A[B+1]==="-"&&A[B+2]===">"){B+=2;break}}else if(A.length>B+8&&A[B+1]==="D"&&A[B+2]==="O"&&A[B+3]==="C"&&A[B+4]==="T"&&A[B+5]==="Y"&&A[B+6]==="P"&&A[B+7]==="E"){let Q=1;for(B+=8;B<A.length;B++)if(A[B]==="<")Q++;else if(A[B]===">"){if(Q--,Q===0)break}}else if(A.length>B+9&&A[B+1]==="["&&A[B+2]==="C"&&A[B+3]==="D"&&A[B+4]==="A"&&A[B+5]==="T"&&A[B+6]==="A"&&A[B+7]==="["){for(B+=8;B<A.length;B++)if(A[B]==="]"&&A[B+1]==="]"&&A[B+2]===">"){B+=2;break}}return B}var fCQ='"',hCQ="'";function gCQ(A,B){let Q="",D="",Z=!1;for(;B<A.length;B++){if(A[B]===fCQ||A[B]===hCQ)if(D==="")D=A[B];else if(D!==A[B]);else D="";else if(A[B]===">"){if(D===""){Z=!0;break}}Q+=A[B]}if(D!=="")return!1;return{value:Q,index:B,tagClosed:Z}}var uCQ=new RegExp(`(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['"])(([\\s\\S])*?)\\5)?`,"g");function ZUA(A,B){let Q=Ot1.getAllMatches(A,uCQ),D={};for(let Z=0;Z<Q.length;Z++){if(Q[Z][1].length===0)return fZ("InvalidAttr","Attribute '"+Q[Z][2]+"' has no space in starting.",fQ1(Q[Z]));else if(Q[Z][3]!==void 0&&Q[Z][4]===void 0)return fZ("InvalidAttr","Attribute '"+Q[Z][2]+"' is without value.",fQ1(Q[Z]));else if(Q[Z][3]===void 0&&!B.allowBooleanAttributes)return fZ("InvalidAttr","boolean attribute '"+Q[Z][2]+"' is not allowed.",fQ1(Q[Z]));let G=Q[Z][2];if(!cCQ(G))return fZ("InvalidAttr","Attribute '"+G+"' is an invalid name.",fQ1(Q[Z]));if(!D.hasOwnProperty(G))D[G]=1;else return fZ("InvalidAttr","Attribute '"+G+"' is repeated.",fQ1(Q[Z]))}return!0}function mCQ(A,B){let Q=/\d/;if(A[B]==="x")B++,Q=/[\da-fA-F]/;for(;B<A.length;B++){if(A[B]===";")return B;if(!A[B].match(Q))break}return-1}function dCQ(A,B){if(B++,A[B]===";")return-1;if(A[B]==="#")return B++,mCQ(A,B);let Q=0;for(;B<A.length;B++,Q++){if(A[B].match(/\w/)&&Q<20)continue;if(A[B]===";")break;return-1}return B}function fZ(A,B,Q){return{err:{code:A,msg:B,line:Q.line||Q,col:Q.col}}}function cCQ(A){return Ot1.isName(A)}function lCQ(A){return Ot1.isName(A)}function dJ(A,B){let Q=A.substring(0,B).split(/\r?\n/);return{line:Q.length,col:Q[Q.length-1].length+1}}function fQ1(A){return A.startIndex+A[1].length}});
var UKA=E((zKA)=>{Object.defineProperty(zKA,"__esModule",{value:!0});zKA.sdkStreamMixin=void 0;var hIQ=FKA(),gIQ=Bn(),uIQ=Zy(),mIQ=cB(),CKA=By(),KKA="The stream has already been transformed.",dIQ=(A)=>{var B,Q;if(!HKA(A)&&!CKA.isReadableStream(A)){let F=((Q=(B=A===null||A===void 0?void 0:A.__proto__)===null||B===void 0?void 0:B.constructor)===null||Q===void 0?void 0:Q.name)||A;throw new Error(`Unexpected stream implementation, expect Blob or ReadableStream, got ${F}`)}let D=!1,Z=async()=>{if(D)throw new Error(KKA);return D=!0,await hIQ.streamCollector(A)},G=(F)=>{if(typeof F.stream!=="function")throw new Error(`Cannot transform payload Blob to web stream. Please make sure the Blob.stream() is polyfilled.
If you are using React Native, this API is not yet supported, see: https://react-native.canny.io/feature-requests/p/fetch-streaming-body`);return F.stream()};return Object.assign(A,{transformToByteArray:Z,transformToString:async(F)=>{let I=await Z();if(F==="base64")return gIQ.toBase64(I);else if(F==="hex")return uIQ.toHex(I);else if(F===void 0||F==="utf8"||F==="utf-8")return mIQ.toUtf8(I);else if(typeof TextDecoder==="function")return new TextDecoder(F).decode(I);else throw new Error("TextDecoder is not available, please make sure polyfill is provided.")},transformToWebStream:()=>{if(D)throw new Error(KKA);if(D=!0,HKA(A))return G(A);else if(CKA.isReadableStream(A))return A;else throw new Error(`Cannot transform payload to web stream, got ${A}`)}})};zKA.sdkStreamMixin=dIQ;var HKA=(A)=>typeof Blob==="function"&&A instanceof Blob});
var ULA=E((zLA)=>{Object.defineProperty(zLA,"__esModule",{value:!0});zLA.retryWrapper=void 0;var ULQ=(A,B,Q)=>{return async()=>{for(let D=0;D<B;++D)try{return await A()}catch(Z){await new Promise((G)=>setTimeout(G,Q))}return await A()}};zLA.retryWrapper=ULQ});
var UNA=E((gD5,Ue1)=>{var{defineProperty:eE1,getOwnPropertyDescriptor:GqQ,getOwnPropertyNames:FqQ}=Object,IqQ=Object.prototype.hasOwnProperty,V8=(A,B)=>eE1(A,"name",{value:B,configurable:!0}),YqQ=(A,B)=>{for(var Q in B)eE1(A,Q,{get:B[Q],enumerable:!0})},Ce1=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of FqQ(B))if(!IqQ.call(A,Z)&&Z!==Q)eE1(A,Z,{get:()=>B[Z],enumerable:!(D=GqQ(B,Z))||D.enumerable})}return A},WqQ=(A,B,Q)=>(Ce1(A,B,"default"),Q&&Ce1(Q,B,"default")),JqQ=(A)=>Ce1(eE1({},"__esModule",{value:!0}),A),ze1={};YqQ(ze1,{Client:()=>XqQ,Command:()=>VNA,NoOpLogger:()=>PqQ,SENSITIVE_STRING:()=>CqQ,ServiceException:()=>HqQ,_json:()=>He1,collectBody:()=>Ve1.collectBody,convertMap:()=>SqQ,createAggregatedClient:()=>KqQ,decorateServiceException:()=>CNA,emitWarningIfUnsupportedVersion:()=>wqQ,extendedEncodeURIComponent:()=>Ve1.extendedEncodeURIComponent,getArrayIfSingleItem:()=>OqQ,getDefaultClientConfiguration:()=>MqQ,getDefaultExtensionConfiguration:()=>HNA,getValueFromTextNode:()=>zNA,isSerializableHeaderValue:()=>TqQ,loadConfigsForDefaultMode:()=>UqQ,map:()=>Ee1,resolveDefaultRuntimeConfig:()=>RqQ,resolvedPath:()=>Ve1.resolvedPath,serializeDateTime:()=>vqQ,serializeFloat:()=>xqQ,take:()=>jqQ,throwDefaultError:()=>KNA,withBaseException:()=>zqQ});Ue1.exports=JqQ(ze1);var XNA=Uw(),XqQ=class{constructor(A){this.config=A,this.middlewareStack=XNA.constructStack()}static{V8(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},Ve1=$6(),Ke1=et1(),VNA=class{constructor(){this.middlewareStack=XNA.constructStack()}static{V8(this,"Command")}static classBuilder(){return new VqQ}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[Ke1.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},VqQ=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{V8(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}sc(A){return this._operationSchema=A,this._smithyContext.operationSchema=A,this}build(){let A=this,B;return B=class extends VNA{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this),this.schema=A._operationSchema}static{V8(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},CqQ="***SensitiveInformation***",KqQ=V8((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=V8(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),HqQ=class A extends Error{static{V8(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},CNA=V8((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),KNA=V8(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=EqQ(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw CNA(F,B)},"throwDefaultError"),zqQ=V8((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{KNA({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),EqQ=V8((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),UqQ=V8((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),JNA=!1,wqQ=V8((A)=>{if(A&&!JNA&&parseInt(A.substring(1,A.indexOf(".")))<16)JNA=!0},"emitWarningIfUnsupportedVersion"),$qQ=V8((A)=>{let B=[];for(let Q in Ke1.AlgorithmId){let D=Ke1.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),qqQ=V8((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),NqQ=V8((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),LqQ=V8((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),HNA=V8((A)=>{return Object.assign($qQ(A),NqQ(A))},"getDefaultExtensionConfiguration"),MqQ=HNA,RqQ=V8((A)=>{return Object.assign(qqQ(A),LqQ(A))},"resolveDefaultRuntimeConfig"),OqQ=V8((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),zNA=V8((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=zNA(A[Q]);return A},"getValueFromTextNode"),TqQ=V8((A)=>{return A!=null},"isSerializableHeaderValue"),PqQ=class{static{V8(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function Ee1(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,kqQ(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}ENA(D,null,G,F)}return D}V8(Ee1,"map");var SqQ=V8((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),jqQ=V8((A,B)=>{let Q={};for(let D in B)ENA(Q,A,B,D);return Q},"take"),kqQ=V8((A,B,Q)=>{return Ee1(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),ENA=V8((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=yqQ,Y=_qQ,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),yqQ=V8((A)=>A!=null,"nonNullish"),_qQ=V8((A)=>A,"pass"),xqQ=V8((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),vqQ=V8((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),He1=V8((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(He1);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=He1(A[Q])}return B}return A},"_json");WqQ(ze1,Y6(),Ue1.exports)});
var UVA=E((zVA)=>{Object.defineProperty(zVA,"__esModule",{value:!0});zVA.ChecksumStream=void 0;var GGQ=typeof ReadableStream==="function"?ReadableStream:function(){};class HVA extends GGQ{}zVA.ChecksumStream=HVA});
var Uw=E((I75,lEA)=>{var{defineProperty:$E1,getOwnPropertyDescriptor:dVQ,getOwnPropertyNames:cVQ}=Object,lVQ=Object.prototype.hasOwnProperty,Dz=(A,B)=>$E1(A,"name",{value:B,configurable:!0}),pVQ=(A,B)=>{for(var Q in B)$E1(A,Q,{get:B[Q],enumerable:!0})},iVQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of cVQ(B))if(!lVQ.call(A,Z)&&Z!==Q)$E1(A,Z,{get:()=>B[Z],enumerable:!(D=dVQ(B,Z))||D.enumerable})}return A},nVQ=(A)=>iVQ($E1({},"__esModule",{value:!0}),A),cEA={};pVQ(cEA,{constructStack:()=>Ut1});lEA.exports=nVQ(cEA);var yh=Dz((A,B)=>{let Q=[];if(A)Q.push(A);if(B)for(let D of B)Q.push(D);return Q},"getAllAliases"),Wy=Dz((A,B)=>{return`${A||"anonymous"}${B&&B.length>0?` (a.k.a. ${B.join(",")})`:""}`},"getMiddlewareNameWithAliases"),Ut1=Dz(()=>{let A=[],B=[],Q=!1,D=new Set,Z=Dz((X)=>X.sort((V,C)=>mEA[C.step]-mEA[V.step]||dEA[C.priority||"normal"]-dEA[V.priority||"normal"]),"sort"),G=Dz((X)=>{let V=!1,C=Dz((K)=>{let H=yh(K.name,K.aliases);if(H.includes(X)){V=!0;for(let z of H)D.delete(z);return!1}return!0},"filterCb");return A=A.filter(C),B=B.filter(C),V},"removeByName"),F=Dz((X)=>{let V=!1,C=Dz((K)=>{if(K.middleware===X){V=!0;for(let H of yh(K.name,K.aliases))D.delete(H);return!1}return!0},"filterCb");return A=A.filter(C),B=B.filter(C),V},"removeByReference"),I=Dz((X)=>{return A.forEach((V)=>{X.add(V.middleware,{...V})}),B.forEach((V)=>{X.addRelativeTo(V.middleware,{...V})}),X.identifyOnResolve?.(J.identifyOnResolve()),X},"cloneTo"),Y=Dz((X)=>{let V=[];return X.before.forEach((C)=>{if(C.before.length===0&&C.after.length===0)V.push(C);else V.push(...Y(C))}),V.push(X),X.after.reverse().forEach((C)=>{if(C.before.length===0&&C.after.length===0)V.push(C);else V.push(...Y(C))}),V},"expandRelativeMiddlewareList"),W=Dz((X=!1)=>{let V=[],C=[],K={};return A.forEach((z)=>{let $={...z,before:[],after:[]};for(let L of yh($.name,$.aliases))K[L]=$;V.push($)}),B.forEach((z)=>{let $={...z,before:[],after:[]};for(let L of yh($.name,$.aliases))K[L]=$;C.push($)}),C.forEach((z)=>{if(z.toMiddleware){let $=K[z.toMiddleware];if($===void 0){if(X)return;throw new Error(`${z.toMiddleware} is not found when adding ${Wy(z.name,z.aliases)} middleware ${z.relation} ${z.toMiddleware}`)}if(z.relation==="after")$.after.push(z);if(z.relation==="before")$.before.push(z)}}),Z(V).map(Y).reduce((z,$)=>{return z.push(...$),z},[])},"getMiddlewareList"),J={add:(X,V={})=>{let{name:C,override:K,aliases:H}=V,z={step:"initialize",priority:"normal",middleware:X,...V},$=yh(C,H);if($.length>0){if($.some((L)=>D.has(L))){if(!K)throw new Error(`Duplicate middleware name '${Wy(C,H)}'`);for(let L of $){let N=A.findIndex((R)=>R.name===L||R.aliases?.some((T)=>T===L));if(N===-1)continue;let O=A[N];if(O.step!==z.step||z.priority!==O.priority)throw new Error(`"${Wy(O.name,O.aliases)}" middleware with ${O.priority} priority in ${O.step} step cannot be overridden by "${Wy(C,H)}" middleware with ${z.priority} priority in ${z.step} step.`);A.splice(N,1)}}for(let L of $)D.add(L)}A.push(z)},addRelativeTo:(X,V)=>{let{name:C,override:K,aliases:H}=V,z={middleware:X,...V},$=yh(C,H);if($.length>0){if($.some((L)=>D.has(L))){if(!K)throw new Error(`Duplicate middleware name '${Wy(C,H)}'`);for(let L of $){let N=B.findIndex((R)=>R.name===L||R.aliases?.some((T)=>T===L));if(N===-1)continue;let O=B[N];if(O.toMiddleware!==z.toMiddleware||O.relation!==z.relation)throw new Error(`"${Wy(O.name,O.aliases)}" middleware ${O.relation} "${O.toMiddleware}" middleware cannot be overridden by "${Wy(C,H)}" middleware ${z.relation} "${z.toMiddleware}" middleware.`);B.splice(N,1)}}for(let L of $)D.add(L)}B.push(z)},clone:()=>I(Ut1()),use:(X)=>{X.applyToStack(J)},remove:(X)=>{if(typeof X==="string")return G(X);else return F(X)},removeByTag:(X)=>{let V=!1,C=Dz((K)=>{let{tags:H,name:z,aliases:$}=K;if(H&&H.includes(X)){let L=yh(z,$);for(let N of L)D.delete(N);return V=!0,!1}return!0},"filterCb");return A=A.filter(C),B=B.filter(C),V},concat:(X)=>{let V=I(Ut1());return V.use(X),V.identifyOnResolve(Q||V.identifyOnResolve()||(X.identifyOnResolve?.()??!1)),V},applyToStack:I,identify:()=>{return W(!0).map((X)=>{let V=X.step??X.relation+" "+X.toMiddleware;return Wy(X.name,X.aliases)+" - "+V})},identifyOnResolve(X){if(typeof X==="boolean")Q=X;return Q},resolve:(X,V)=>{for(let C of W().map((K)=>K.middleware).reverse())X=C(X,V);if(Q)console.log(J.identify());return X}};return J},"constructStack"),mEA={initialize:5,serialize:4,build:3,finalizeRequest:2,deserialize:1},dEA={high:3,normal:2,low:1}});
var V4=E((a75,RwA)=>{var{defineProperty:kE1,getOwnPropertyDescriptor:YzQ,getOwnPropertyNames:WzQ}=Object,JzQ=Object.prototype.hasOwnProperty,ww=(A,B)=>kE1(A,"name",{value:B,configurable:!0}),XzQ=(A,B)=>{for(var Q in B)kE1(A,Q,{get:B[Q],enumerable:!0})},VzQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of WzQ(B))if(!JzQ.call(A,Z)&&Z!==Q)kE1(A,Z,{get:()=>B[Z],enumerable:!(D=YzQ(B,Z))||D.enumerable})}return A},CzQ=(A)=>VzQ(kE1({},"__esModule",{value:!0}),A),EwA={};XzQ(EwA,{CONFIG_USE_DUALSTACK_ENDPOINT:()=>wwA,CONFIG_USE_FIPS_ENDPOINT:()=>qwA,DEFAULT_USE_DUALSTACK_ENDPOINT:()=>KzQ,DEFAULT_USE_FIPS_ENDPOINT:()=>zzQ,ENV_USE_DUALSTACK_ENDPOINT:()=>UwA,ENV_USE_FIPS_ENDPOINT:()=>$wA,NODE_REGION_CONFIG_FILE_OPTIONS:()=>NzQ,NODE_REGION_CONFIG_OPTIONS:()=>qzQ,NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS:()=>HzQ,NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS:()=>EzQ,REGION_ENV_NAME:()=>NwA,REGION_INI_NAME:()=>LwA,getRegionInfo:()=>TzQ,resolveCustomEndpointsConfig:()=>UzQ,resolveEndpointsConfig:()=>$zQ,resolveRegionConfig:()=>LzQ});RwA.exports=CzQ(EwA);var Vy=KwA(),UwA="AWS_USE_DUALSTACK_ENDPOINT",wwA="use_dualstack_endpoint",KzQ=!1,HzQ={environmentVariableSelector:(A)=>Vy.booleanSelector(A,UwA,Vy.SelectorType.ENV),configFileSelector:(A)=>Vy.booleanSelector(A,wwA,Vy.SelectorType.CONFIG),default:!1},$wA="AWS_USE_FIPS_ENDPOINT",qwA="use_fips_endpoint",zzQ=!1,EzQ={environmentVariableSelector:(A)=>Vy.booleanSelector(A,$wA,Vy.SelectorType.ENV),configFileSelector:(A)=>Vy.booleanSelector(A,qwA,Vy.SelectorType.CONFIG),default:!1},jE1=I5(),UzQ=ww((A)=>{let{tls:B,endpoint:Q,urlParser:D,useDualstackEndpoint:Z}=A;return Object.assign(A,{tls:B??!0,endpoint:jE1.normalizeProvider(typeof Q==="string"?D(Q):Q),isCustomEndpoint:!0,useDualstackEndpoint:jE1.normalizeProvider(Z??!1)})},"resolveCustomEndpointsConfig"),wzQ=ww(async(A)=>{let{tls:B=!0}=A,Q=await A.region();if(!new RegExp(/^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])$/).test(Q))throw new Error("Invalid region in client config");let Z=await A.useDualstackEndpoint(),G=await A.useFipsEndpoint(),{hostname:F}=await A.regionInfoProvider(Q,{useDualstackEndpoint:Z,useFipsEndpoint:G})??{};if(!F)throw new Error("Cannot resolve hostname from client config");return A.urlParser(`${B?"https:":"http:"}//${F}`)},"getEndpointFromRegion"),$zQ=ww((A)=>{let B=jE1.normalizeProvider(A.useDualstackEndpoint??!1),{endpoint:Q,useFipsEndpoint:D,urlParser:Z,tls:G}=A;return Object.assign(A,{tls:G??!0,endpoint:Q?jE1.normalizeProvider(typeof Q==="string"?Z(Q):Q):()=>wzQ({...A,useDualstackEndpoint:B,useFipsEndpoint:D}),isCustomEndpoint:!!Q,useDualstackEndpoint:B})},"resolveEndpointsConfig"),NwA="AWS_REGION",LwA="region",qzQ={environmentVariableSelector:(A)=>A[NwA],configFileSelector:(A)=>A[LwA],default:()=>{throw new Error("Region is missing")}},NzQ={preferredFile:"credentials"},MwA=ww((A)=>typeof A==="string"&&(A.startsWith("fips-")||A.endsWith("-fips")),"isFipsRegion"),HwA=ww((A)=>MwA(A)?["fips-aws-global","aws-fips"].includes(A)?"us-east-1":A.replace(/fips-(dkr-|prod-)?|-fips/,""):A,"getRealRegion"),LzQ=ww((A)=>{let{region:B,useFipsEndpoint:Q}=A;if(!B)throw new Error("Region is missing");return Object.assign(A,{region:async()=>{if(typeof B==="string")return HwA(B);let D=await B();return HwA(D)},useFipsEndpoint:async()=>{let D=typeof B==="string"?B:await B();if(MwA(D))return!0;return typeof Q!=="function"?Promise.resolve(!!Q):Q()}})},"resolveRegionConfig"),zwA=ww((A=[],{useFipsEndpoint:B,useDualstackEndpoint:Q})=>A.find(({tags:D})=>B===D.includes("fips")&&Q===D.includes("dualstack"))?.hostname,"getHostnameFromVariants"),MzQ=ww((A,{regionHostname:B,partitionHostname:Q})=>B?B:Q?Q.replace("{region}",A):void 0,"getResolvedHostname"),RzQ=ww((A,{partitionHash:B})=>Object.keys(B||{}).find((Q)=>B[Q].regions.includes(A))??"aws","getResolvedPartition"),OzQ=ww((A,{signingRegion:B,regionRegex:Q,useFipsEndpoint:D})=>{if(B)return B;else if(D){let Z=Q.replace("\\\\","\\").replace(/^\^/g,"\\.").replace(/\$$/g,"\\."),G=A.match(Z);if(G)return G[0].slice(1,-1)}},"getResolvedSigningRegion"),TzQ=ww((A,{useFipsEndpoint:B=!1,useDualstackEndpoint:Q=!1,signingService:D,regionHash:Z,partitionHash:G})=>{let F=RzQ(A,{partitionHash:G}),I=A in Z?A:G[F]?.endpoint??A,Y={useFipsEndpoint:B,useDualstackEndpoint:Q},W=zwA(Z[I]?.variants,Y),J=zwA(G[F]?.variants,Y),X=MzQ(I,{regionHostname:W,partitionHostname:J});if(X===void 0)throw new Error(`Endpoint resolution failed for: ${{resolvedRegion:I,useFipsEndpoint:B,useDualstackEndpoint:Q}}`);let V=OzQ(X,{signingRegion:Z[I]?.signingRegion,regionRegex:G[F].regionRegex,useFipsEndpoint:B});return{partition:F,signingService:D,hostname:X,...V&&{signingRegion:V},...Z[I]?.signingService&&{signingService:Z[I].signingService}}},"getRegionInfo")});
var VB=E((v35,RHA)=>{var{defineProperty:sz1,getOwnPropertyDescriptor:mWQ,getOwnPropertyNames:dWQ}=Object,cWQ=Object.prototype.hasOwnProperty,BD=(A,B)=>sz1(A,"name",{value:B,configurable:!0}),lWQ=(A,B)=>{for(var Q in B)sz1(A,Q,{get:B[Q],enumerable:!0})},pWQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of dWQ(B))if(!cWQ.call(A,Z)&&Z!==Q)sz1(A,Z,{get:()=>B[Z],enumerable:!(D=mWQ(B,Z))||D.enumerable})}return A},iWQ=(A)=>pWQ(sz1({},"__esModule",{value:!0}),A),CHA={};lWQ(CHA,{DefaultIdentityProviderConfig:()=>GJQ,EXPIRATION_MS:()=>LHA,HttpApiKeyAuthSigner:()=>FJQ,HttpBearerAuthSigner:()=>IJQ,NoAuthSigner:()=>YJQ,createIsIdentityExpiredFunction:()=>NHA,createPaginator:()=>$HA,doesIdentityRequireRefresh:()=>MHA,getHttpAuthSchemeEndpointRuleSetPlugin:()=>sWQ,getHttpAuthSchemePlugin:()=>oWQ,getHttpSigningPlugin:()=>AJQ,getSmithyContext:()=>nWQ,httpAuthSchemeEndpointRuleSetMiddlewareOptions:()=>zHA,httpAuthSchemeMiddleware:()=>no1,httpAuthSchemeMiddlewareOptions:()=>EHA,httpSigningMiddleware:()=>UHA,httpSigningMiddlewareOptions:()=>wHA,isIdentityExpired:()=>WJQ,memoizeIdentityProvider:()=>JJQ,normalizeProvider:()=>BJQ,requestBuilder:()=>ZJQ.requestBuilder,setFeature:()=>qHA});RHA.exports=iWQ(CHA);var az1=Zo1(),nWQ=BD((A)=>A[az1.SMITHY_CONTEXT_KEY]||(A[az1.SMITHY_CONTEXT_KEY]={}),"getSmithyContext"),KHA=I5(),aWQ=BD((A,B)=>{if(!B||B.length===0)return A;let Q=[];for(let D of B)for(let Z of A)if(Z.schemeId.split("#")[1]===D)Q.push(Z);for(let D of A)if(!Q.find(({schemeId:Z})=>Z===D.schemeId))Q.push(D);return Q},"resolveAuthOptions");function HHA(A){let B=new Map;for(let Q of A)B.set(Q.schemeId,Q);return B}BD(HHA,"convertHttpAuthSchemesToMap");var no1=BD((A,B)=>(Q,D)=>async(Z)=>{let G=A.httpAuthSchemeProvider(await B.httpAuthSchemeParametersProvider(A,D,Z.input)),F=A.authSchemePreference?await A.authSchemePreference():[],I=aWQ(G,F),Y=HHA(A.httpAuthSchemes),W=KHA.getSmithyContext(D),J=[];for(let X of I){let V=Y.get(X.schemeId);if(!V){J.push(`HttpAuthScheme \`${X.schemeId}\` was not enabled for this service.`);continue}let C=V.identityProvider(await B.identityProviderConfigProvider(A));if(!C){J.push(`HttpAuthScheme \`${X.schemeId}\` did not have an IdentityProvider configured.`);continue}let{identityProperties:K={},signingProperties:H={}}=X.propertiesExtractor?.(A,D)||{};X.identityProperties=Object.assign(X.identityProperties||{},K),X.signingProperties=Object.assign(X.signingProperties||{},H),W.selectedHttpAuthScheme={httpAuthOption:X,identity:await C(X.identityProperties),signer:V.signer};break}if(!W.selectedHttpAuthScheme)throw new Error(J.join(`
`));return Q(Z)},"httpAuthSchemeMiddleware"),zHA={step:"serialize",tags:["HTTP_AUTH_SCHEME"],name:"httpAuthSchemeMiddleware",override:!0,relation:"before",toMiddleware:"endpointV2Middleware"},sWQ=BD((A,{httpAuthSchemeParametersProvider:B,identityProviderConfigProvider:Q})=>({applyToStack:(D)=>{D.addRelativeTo(no1(A,{httpAuthSchemeParametersProvider:B,identityProviderConfigProvider:Q}),zHA)}}),"getHttpAuthSchemeEndpointRuleSetPlugin"),rWQ=T3(),EHA={step:"serialize",tags:["HTTP_AUTH_SCHEME"],name:"httpAuthSchemeMiddleware",override:!0,relation:"before",toMiddleware:rWQ.serializerMiddlewareOption.name},oWQ=BD((A,{httpAuthSchemeParametersProvider:B,identityProviderConfigProvider:Q})=>({applyToStack:(D)=>{D.addRelativeTo(no1(A,{httpAuthSchemeParametersProvider:B,identityProviderConfigProvider:Q}),EHA)}}),"getHttpAuthSchemePlugin"),ao1=Oh(),tWQ=BD((A)=>(B)=>{throw B},"defaultErrorHandler"),eWQ=BD((A,B)=>{},"defaultSuccessHandler"),UHA=BD((A)=>(B,Q)=>async(D)=>{if(!ao1.HttpRequest.isInstance(D.request))return B(D);let G=KHA.getSmithyContext(Q).selectedHttpAuthScheme;if(!G)throw new Error("No HttpAuthScheme was selected: unable to sign request");let{httpAuthOption:{signingProperties:F={}},identity:I,signer:Y}=G,W=await B({...D,request:await Y.sign(D.request,I,F)}).catch((Y.errorHandler||tWQ)(F));return(Y.successHandler||eWQ)(W.response,F),W},"httpSigningMiddleware"),wHA={step:"finalizeRequest",tags:["HTTP_SIGNING"],name:"httpSigningMiddleware",aliases:["apiKeyMiddleware","tokenMiddleware","awsAuthMiddleware"],override:!0,relation:"after",toMiddleware:"retryMiddleware"},AJQ=BD((A)=>({applyToStack:(B)=>{B.addRelativeTo(UHA(A),wHA)}}),"getHttpSigningPlugin"),BJQ=BD((A)=>{if(typeof A==="function")return A;let B=Promise.resolve(A);return()=>B},"normalizeProvider"),QJQ=BD(async(A,B,Q,D=(G)=>G,...Z)=>{let G=new A(Q);return G=D(G)??G,await B.send(G,...Z)},"makePagedClientRequest");function $HA(A,B,Q,D,Z){return BD(async function*G(F,I,...Y){let W=I,J=F.startingToken??W[Q],X=!0,V;while(X){if(W[Q]=J,Z)W[Z]=W[Z]??F.pageSize;if(F.client instanceof A)V=await QJQ(B,F.client,I,F.withCommand,...Y);else throw new Error(`Invalid client, expected instance of ${A.name}`);yield V;let C=J;J=DJQ(V,D),X=!!(J&&(!F.stopOnSameToken||J!==C))}return},"paginateOperation")}BD($HA,"createPaginator");var DJQ=BD((A,B)=>{let Q=A,D=B.split(".");for(let Z of D){if(!Q||typeof Q!=="object")return;Q=Q[Z]}return Q},"get"),ZJQ=$6();function qHA(A,B,Q){if(!A.__smithy_context)A.__smithy_context={features:{}};else if(!A.__smithy_context.features)A.__smithy_context.features={};A.__smithy_context.features[B]=Q}BD(qHA,"setFeature");var GJQ=class{constructor(A){this.authSchemes=new Map;for(let[B,Q]of Object.entries(A))if(Q!==void 0)this.authSchemes.set(B,Q)}static{BD(this,"DefaultIdentityProviderConfig")}getIdentityProvider(A){return this.authSchemes.get(A)}},FJQ=class{static{BD(this,"HttpApiKeyAuthSigner")}async sign(A,B,Q){if(!Q)throw new Error("request could not be signed with `apiKey` since the `name` and `in` signer properties are missing");if(!Q.name)throw new Error("request could not be signed with `apiKey` since the `name` signer property is missing");if(!Q.in)throw new Error("request could not be signed with `apiKey` since the `in` signer property is missing");if(!B.apiKey)throw new Error("request could not be signed with `apiKey` since the `apiKey` is not defined");let D=ao1.HttpRequest.clone(A);if(Q.in===az1.HttpApiKeyAuthLocation.QUERY)D.query[Q.name]=B.apiKey;else if(Q.in===az1.HttpApiKeyAuthLocation.HEADER)D.headers[Q.name]=Q.scheme?`${Q.scheme} ${B.apiKey}`:B.apiKey;else throw new Error("request can only be signed with `apiKey` locations `query` or `header`, but found: `"+Q.in+"`");return D}},IJQ=class{static{BD(this,"HttpBearerAuthSigner")}async sign(A,B,Q){let D=ao1.HttpRequest.clone(A);if(!B.token)throw new Error("request could not be signed with `token` since the `token` is not defined");return D.headers.Authorization=`Bearer ${B.token}`,D}},YJQ=class{static{BD(this,"NoAuthSigner")}async sign(A,B,Q){return A}},NHA=BD((A)=>(B)=>MHA(B)&&B.expiration.getTime()-Date.now()<A,"createIsIdentityExpiredFunction"),LHA=300000,WJQ=NHA(LHA),MHA=BD((A)=>A.expiration!==void 0,"doesIdentityRequireRefresh"),JJQ=BD((A,B,Q)=>{if(A===void 0)return;let D=typeof A!=="function"?async()=>Promise.resolve(A):A,Z,G,F,I=!1,Y=BD(async(W)=>{if(!G)G=D(W);try{Z=await G,F=!0,I=!1}finally{G=void 0}return Z},"coalesceProvider");if(B===void 0)return async(W)=>{if(!F||W?.forceRefresh)Z=await Y(W);return Z};return async(W)=>{if(!F||W?.forceRefresh)Z=await Y(W);if(I)return Z;if(!Q(Z))return I=!0,Z;if(B(Z))return await Y(W),Z;return Z}},"memoizeIdentityProvider")});
var W$A=E((ZD5,Y$A)=>{var{defineProperty:bE1,getOwnPropertyDescriptor:EEQ,getOwnPropertyNames:UEQ}=Object,wEQ=Object.prototype.hasOwnProperty,fE1=(A,B)=>bE1(A,"name",{value:B,configurable:!0}),$EQ=(A,B)=>{for(var Q in B)bE1(A,Q,{get:B[Q],enumerable:!0})},qEQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of UEQ(B))if(!wEQ.call(A,Z)&&Z!==Q)bE1(A,Z,{get:()=>B[Z],enumerable:!(D=EEQ(B,Z))||D.enumerable})}return A},NEQ=(A)=>qEQ(bE1({},"__esModule",{value:!0}),A),A$A={};$EQ(A$A,{AlgorithmId:()=>Z$A,EndpointURLScheme:()=>D$A,FieldPosition:()=>G$A,HttpApiKeyAuthLocation:()=>Q$A,HttpAuthLocation:()=>B$A,IniSectionType:()=>F$A,RequestHandlerProtocol:()=>I$A,SMITHY_CONTEXT_KEY:()=>TEQ,getDefaultClientConfiguration:()=>REQ,resolveDefaultRuntimeConfig:()=>OEQ});Y$A.exports=NEQ(A$A);var B$A=((A)=>{return A.HEADER="header",A.QUERY="query",A})(B$A||{}),Q$A=((A)=>{return A.HEADER="header",A.QUERY="query",A})(Q$A||{}),D$A=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(D$A||{}),Z$A=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(Z$A||{}),LEQ=fE1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),MEQ=fE1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),REQ=fE1((A)=>{return LEQ(A)},"getDefaultClientConfiguration"),OEQ=fE1((A)=>{return MEQ(A)},"resolveDefaultRuntimeConfig"),G$A=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(G$A||{}),TEQ="__smithy_context",F$A=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(F$A||{}),I$A=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(I$A||{})});
var W41=E((yOA)=>{Object.defineProperty(yOA,"__esModule",{value:!0});yOA.commonParams=yOA.resolveClientEndpointParameters=void 0;var lTQ=(A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,useGlobalEndpoint:A.useGlobalEndpoint??!1,defaultSigningName:"sts"})};yOA.resolveClientEndpointParameters=lTQ;yOA.commonParams={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}});
var WUA=E((E75,YUA)=>{class IUA{constructor(A){this.tagname=A,this.child=[],this[":@"]={}}add(A,B){if(A==="__proto__")A="#__proto__";this.child.push({[A]:B})}addChild(A){if(A.tagname==="__proto__")A.tagname="#__proto__";if(A[":@"]&&Object.keys(A[":@"]).length>0)this.child.push({[A.tagname]:A.child,[":@"]:A[":@"]});else this.child.push({[A.tagname]:A.child})}}YUA.exports=IUA});
var XMA=E((WMA)=>{Object.defineProperty(WMA,"__esModule",{value:!0});WMA.getRuntimeConfig=void 0;var $MQ=ZI(),qMQ=VB(),NMQ=x4(),LMQ=BZ(),IMA=Yy(),YMA=cB(),MMQ=_e1(),RMQ=FMA(),OMQ=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??IMA.fromBase64,base64Encoder:A?.base64Encoder??IMA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??RMQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??MMQ.defaultSSOHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new $MQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new qMQ.NoAuthSigner}],logger:A?.logger??new NMQ.NoOpLogger,serviceId:A?.serviceId??"SSO",urlParser:A?.urlParser??LMQ.parseUrl,utf8Decoder:A?.utf8Decoder??YMA.fromUtf8,utf8Encoder:A?.utf8Encoder??YMA.toUtf8}};WMA.getRuntimeConfig=OMQ});
var XTA=E((WTA)=>{Object.defineProperty(WTA,"__esModule",{value:!0});WTA.getRuntimeConfig=void 0;var oTQ=ZI(),tTQ=VB(),eTQ=x4(),APQ=BZ(),ITA=Yy(),YTA=cB(),BPQ=Q10(),QPQ=FTA(),DPQ=(A)=>{return{apiVersion:"2011-06-15",base64Decoder:A?.base64Decoder??ITA.fromBase64,base64Encoder:A?.base64Encoder??ITA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??QPQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??BPQ.defaultSTSHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new oTQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new tTQ.NoAuthSigner}],logger:A?.logger??new eTQ.NoOpLogger,serviceId:A?.serviceId??"STS",urlParser:A?.urlParser??APQ.parseUrl,utf8Decoder:A?.utf8Decoder??YTA.fromUtf8,utf8Encoder:A?.utf8Encoder??YTA.toUtf8}};WTA.getRuntimeConfig=DPQ});
var XUA=E((U75,JUA)=>{var oCQ=NE1();function tCQ(A,B){let Q={};if(A[B+3]==="O"&&A[B+4]==="C"&&A[B+5]==="T"&&A[B+6]==="Y"&&A[B+7]==="P"&&A[B+8]==="E"){B=B+9;let D=1,Z=!1,G=!1,F="";for(;B<A.length;B++)if(A[B]==="<"&&!G){if(Z&&BKQ(A,B)){if(B+=7,[entityName,val,B]=eCQ(A,B+1),val.indexOf("&")===-1)Q[GKQ(entityName)]={regx:RegExp(`&${entityName};`,"g"),val}}else if(Z&&QKQ(A,B))B+=8;else if(Z&&DKQ(A,B))B+=8;else if(Z&&ZKQ(A,B))B+=9;else if(AKQ)G=!0;else throw new Error("Invalid DOCTYPE");D++,F=""}else if(A[B]===">"){if(G){if(A[B-1]==="-"&&A[B-2]==="-")G=!1,D--}else D--;if(D===0)break}else if(A[B]==="[")Z=!0;else F+=A[B];if(D!==0)throw new Error("Unclosed DOCTYPE")}else throw new Error("Invalid Tag instead of DOCTYPE");return{entities:Q,i:B}}function eCQ(A,B){let Q="";for(;B<A.length&&(A[B]!=="'"&&A[B]!=='"');B++)Q+=A[B];if(Q=Q.trim(),Q.indexOf(" ")!==-1)throw new Error("External entites are not supported");let D=A[B++],Z="";for(;B<A.length&&A[B]!==D;B++)Z+=A[B];return[Q,Z,B]}function AKQ(A,B){if(A[B+1]==="!"&&A[B+2]==="-"&&A[B+3]==="-")return!0;return!1}function BKQ(A,B){if(A[B+1]==="!"&&A[B+2]==="E"&&A[B+3]==="N"&&A[B+4]==="T"&&A[B+5]==="I"&&A[B+6]==="T"&&A[B+7]==="Y")return!0;return!1}function QKQ(A,B){if(A[B+1]==="!"&&A[B+2]==="E"&&A[B+3]==="L"&&A[B+4]==="E"&&A[B+5]==="M"&&A[B+6]==="E"&&A[B+7]==="N"&&A[B+8]==="T")return!0;return!1}function DKQ(A,B){if(A[B+1]==="!"&&A[B+2]==="A"&&A[B+3]==="T"&&A[B+4]==="T"&&A[B+5]==="L"&&A[B+6]==="I"&&A[B+7]==="S"&&A[B+8]==="T")return!0;return!1}function ZKQ(A,B){if(A[B+1]==="!"&&A[B+2]==="N"&&A[B+3]==="O"&&A[B+4]==="T"&&A[B+5]==="A"&&A[B+6]==="T"&&A[B+7]==="I"&&A[B+8]==="O"&&A[B+9]==="N")return!0;return!1}function GKQ(A){if(oCQ.isName(A))return A;else throw new Error(`Invalid entity name ${A}`)}JUA.exports=tCQ});
var Xo1=E((RVA)=>{Object.defineProperty(RVA,"__esModule",{value:!0});RVA.ByteArrayCollector=void 0;class MVA{constructor(A){this.allocByteArray=A,this.byteLength=0,this.byteArrays=[]}push(A){this.byteArrays.push(A),this.byteLength+=A.byteLength}flush(){if(this.byteArrays.length===1){let Q=this.byteArrays[0];return this.reset(),Q}let A=this.allocByteArray(this.byteLength),B=0;for(let Q=0;Q<this.byteArrays.length;++Q){let D=this.byteArrays[Q];A.set(D,B),B+=D.byteLength}return this.reset(),A}reset(){this.byteArrays=[],this.byteLength=0}}RVA.ByteArrayCollector=MVA});
var Y41=E((W10)=>{Object.defineProperty(W10,"__esModule",{value:!0});W10.STSClient=W10.__Client=void 0;var PTA=NQ1(),wPQ=LQ1(),$PQ=MQ1(),STA=zn(),qPQ=V4(),Y10=VB(),NPQ=TG(),LPQ=q6(),jTA=v4(),yTA=x4();Object.defineProperty(W10,"__Client",{enumerable:!0,get:function(){return yTA.Client}});var kTA=Q10(),MPQ=W41(),RPQ=ETA(),OPQ=TTA();class _TA extends yTA.Client{config;constructor(...[A]){let B=RPQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=MPQ.resolveClientEndpointParameters(B),D=STA.resolveUserAgentConfig(Q),Z=jTA.resolveRetryConfig(D),G=qPQ.resolveRegionConfig(Z),F=PTA.resolveHostHeaderConfig(G),I=LPQ.resolveEndpointConfig(F),Y=kTA.resolveHttpAuthSchemeConfig(I),W=OPQ.resolveRuntimeExtensions(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(STA.getUserAgentPlugin(this.config)),this.middlewareStack.use(jTA.getRetryPlugin(this.config)),this.middlewareStack.use(NPQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(PTA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(wPQ.getLoggerPlugin(this.config)),this.middlewareStack.use($PQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(Y10.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:kTA.defaultSTSHttpAuthSchemeParametersProvider,identityProviderConfigProvider:async(J)=>new Y10.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials})})),this.middlewareStack.use(Y10.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}}W10.STSClient=_TA});
var Y6=E((q35,iKA)=>{var{defineProperty:xo1,getOwnPropertyDescriptor:NYQ,getOwnPropertyNames:LYQ}=Object,MYQ=Object.prototype.hasOwnProperty,RYQ=(A,B)=>{for(var Q in B)xo1(A,Q,{get:B[Q],enumerable:!0})},OYQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of LYQ(B))if(!MYQ.call(A,Z)&&Z!==Q)xo1(A,Z,{get:()=>B[Z],enumerable:!(D=NYQ(B,Z))||D.enumerable})}return A},TYQ=(A)=>OYQ(xo1({},"__esModule",{value:!0}),A),gKA={};RYQ(gKA,{LazyJsonString:()=>Ph,NumericValue:()=>pKA,copyDocumentWithTransform:()=>TQ1,dateToUtcString:()=>pYQ,expectBoolean:()=>jYQ,expectByte:()=>_o1,expectFloat32:()=>dz1,expectInt:()=>yYQ,expectInt32:()=>ko1,expectLong:()=>jQ1,expectNonNull:()=>xYQ,expectNumber:()=>SQ1,expectObject:()=>uKA,expectShort:()=>yo1,expectString:()=>vYQ,expectUnion:()=>bYQ,handleFloat:()=>gYQ,limitedParseDouble:()=>fo1,limitedParseFloat:()=>uYQ,limitedParseFloat32:()=>mYQ,logger:()=>kQ1,nv:()=>VWQ,parseBoolean:()=>SYQ,parseEpochTimestamp:()=>AWQ,parseRfc3339DateTime:()=>nYQ,parseRfc3339DateTimeWithOffset:()=>sYQ,parseRfc7231DateTime:()=>eYQ,quoteHeader:()=>WWQ,splitEvery:()=>JWQ,splitHeader:()=>XWQ,strictParseByte:()=>lKA,strictParseDouble:()=>bo1,strictParseFloat:()=>fYQ,strictParseFloat32:()=>mKA,strictParseInt:()=>dYQ,strictParseInt32:()=>cYQ,strictParseLong:()=>cKA,strictParseShort:()=>Zn});iKA.exports=TYQ(gKA);var PYQ=jQ(),TQ1=(A,B,Q=(D)=>D)=>{let D=PYQ.NormalizedSchema.of(B);switch(typeof A){case"undefined":case"boolean":case"number":case"string":case"bigint":case"symbol":return Q(A,D);case"function":case"object":if(A===null)return Q(null,D);if(Array.isArray(A)){let G=new Array(A.length),F=0;for(let I of A)G[F++]=TQ1(I,D.getValueSchema(),Q);return Q(G,D)}if("byteLength"in A){let G=new Uint8Array(A.byteLength);return G.set(A,0),Q(G,D)}if(A instanceof Date)return Q(A,D);let Z={};if(D.isMapSchema())for(let G of Object.keys(A))Z[G]=TQ1(A[G],D.getValueSchema(),Q);else if(D.isStructSchema())for(let[G,F]of D.structIterator())Z[G]=TQ1(A[G],F,Q);else if(D.isDocumentSchema())for(let G of Object.keys(A))Z[G]=TQ1(A[G],D.getValueSchema(),Q);return Q(Z,D);default:return Q(A,D)}},SYQ=(A)=>{switch(A){case"true":return!0;case"false":return!1;default:throw new Error(`Unable to parse boolean value "${A}"`)}},jYQ=(A)=>{if(A===null||A===void 0)return;if(typeof A==="number"){if(A===0||A===1)kQ1.warn(cz1(`Expected boolean, got ${typeof A}: ${A}`));if(A===0)return!1;if(A===1)return!0}if(typeof A==="string"){let B=A.toLowerCase();if(B==="false"||B==="true")kQ1.warn(cz1(`Expected boolean, got ${typeof A}: ${A}`));if(B==="false")return!1;if(B==="true")return!0}if(typeof A==="boolean")return A;throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)},SQ1=(A)=>{if(A===null||A===void 0)return;if(typeof A==="string"){let B=parseFloat(A);if(!Number.isNaN(B)){if(String(B)!==String(A))kQ1.warn(cz1(`Expected number but observed string: ${A}`));return B}}if(typeof A==="number")return A;throw new TypeError(`Expected number, got ${typeof A}: ${A}`)},kYQ=Math.ceil(340282346638528860000000000000000000000),dz1=(A)=>{let B=SQ1(A);if(B!==void 0&&!Number.isNaN(B)&&B!==1/0&&B!==-1/0){if(Math.abs(B)>kYQ)throw new TypeError(`Expected 32-bit float, got ${A}`)}return B},jQ1=(A)=>{if(A===null||A===void 0)return;if(Number.isInteger(A)&&!Number.isNaN(A))return A;throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)},yYQ=jQ1,ko1=(A)=>vo1(A,32),yo1=(A)=>vo1(A,16),_o1=(A)=>vo1(A,8),vo1=(A,B)=>{let Q=jQ1(A);if(Q!==void 0&&_YQ(Q,B)!==Q)throw new TypeError(`Expected ${B}-bit integer, got ${A}`);return Q},_YQ=(A,B)=>{switch(B){case 32:return Int32Array.of(A)[0];case 16:return Int16Array.of(A)[0];case 8:return Int8Array.of(A)[0]}},xYQ=(A,B)=>{if(A===null||A===void 0){if(B)throw new TypeError(`Expected a non-null value for ${B}`);throw new TypeError("Expected a non-null value")}return A},uKA=(A)=>{if(A===null||A===void 0)return;if(typeof A==="object"&&!Array.isArray(A))return A;let B=Array.isArray(A)?"array":typeof A;throw new TypeError(`Expected object, got ${B}: ${A}`)},vYQ=(A)=>{if(A===null||A===void 0)return;if(typeof A==="string")return A;if(["boolean","number","bigint"].includes(typeof A))return kQ1.warn(cz1(`Expected string, got ${typeof A}: ${A}`)),String(A);throw new TypeError(`Expected string, got ${typeof A}: ${A}`)},bYQ=(A)=>{if(A===null||A===void 0)return;let B=uKA(A),Q=Object.entries(B).filter(([,D])=>D!=null).map(([D])=>D);if(Q.length===0)throw new TypeError("Unions must have exactly one non-null member. None were found.");if(Q.length>1)throw new TypeError(`Unions must have exactly one non-null member. Keys ${Q} were not null.`);return B},bo1=(A)=>{if(typeof A=="string")return SQ1(Fn(A));return SQ1(A)},fYQ=bo1,mKA=(A)=>{if(typeof A=="string")return dz1(Fn(A));return dz1(A)},hYQ=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,Fn=(A)=>{let B=A.match(hYQ);if(B===null||B[0].length!==A.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(A)},fo1=(A)=>{if(typeof A=="string")return dKA(A);return SQ1(A)},gYQ=fo1,uYQ=fo1,mYQ=(A)=>{if(typeof A=="string")return dKA(A);return dz1(A)},dKA=(A)=>{switch(A){case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error(`Unable to parse float value: ${A}`)}},cKA=(A)=>{if(typeof A==="string")return jQ1(Fn(A));return jQ1(A)},dYQ=cKA,cYQ=(A)=>{if(typeof A==="string")return ko1(Fn(A));return ko1(A)},Zn=(A)=>{if(typeof A==="string")return yo1(Fn(A));return yo1(A)},lKA=(A)=>{if(typeof A==="string")return _o1(Fn(A));return _o1(A)},cz1=(A)=>{return String(new TypeError(A).stack||A).split(`
`).slice(0,5).filter((B)=>!B.includes("stackTraceWarning")).join(`
`)},kQ1={warn:console.warn},lYQ=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],ho1=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function pYQ(A){let B=A.getUTCFullYear(),Q=A.getUTCMonth(),D=A.getUTCDay(),Z=A.getUTCDate(),G=A.getUTCHours(),F=A.getUTCMinutes(),I=A.getUTCSeconds(),Y=Z<10?`0${Z}`:`${Z}`,W=G<10?`0${G}`:`${G}`,J=F<10?`0${F}`:`${F}`,X=I<10?`0${I}`:`${I}`;return`${lYQ[D]}, ${Y} ${ho1[Q]} ${B} ${W}:${J}:${X} GMT`}var iYQ=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),nYQ=(A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=iYQ.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W]=B,J=Zn(Gn(D)),X=hN(Z,"month",1,12),V=hN(G,"day",1,31);return PQ1(J,X,V,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})},aYQ=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),sYQ=(A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=aYQ.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W,J]=B,X=Zn(Gn(D)),V=hN(Z,"month",1,12),C=hN(G,"day",1,31),K=PQ1(X,V,C,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W});if(J.toUpperCase()!="Z")K.setTime(K.getTime()-YWQ(J));return K},rYQ=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),oYQ=new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),tYQ=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/),eYQ=(A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-7231 date-times must be expressed as strings");let B=rYQ.exec(A);if(B){let[Q,D,Z,G,F,I,Y,W]=B;return PQ1(Zn(Gn(G)),jo1(Z),hN(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})}if(B=oYQ.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return DWQ(PQ1(BWQ(G),jo1(Z),hN(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W}))}if(B=tYQ.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return PQ1(Zn(Gn(W)),jo1(D),hN(Z.trimLeft(),"day",1,31),{hours:G,minutes:F,seconds:I,fractionalMilliseconds:Y})}throw new TypeError("Invalid RFC-7231 date-time value")},AWQ=(A)=>{if(A===null||A===void 0)return;let B;if(typeof A==="number")B=A;else if(typeof A==="string")B=bo1(A);else if(typeof A==="object"&&A.tag===1)B=A.value;else throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(B)||B===1/0||B===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(B*1000))},PQ1=(A,B,Q,D)=>{let Z=B-1;return GWQ(A,Z,Q),new Date(Date.UTC(A,Z,Q,hN(D.hours,"hour",0,23),hN(D.minutes,"minute",0,59),hN(D.seconds,"seconds",0,60),IWQ(D.fractionalMilliseconds)))},BWQ=(A)=>{let B=new Date().getUTCFullYear(),Q=Math.floor(B/100)*100+Zn(Gn(A));if(Q<B)return Q+100;return Q},QWQ=1576800000000,DWQ=(A)=>{if(A.getTime()-new Date().getTime()>QWQ)return new Date(Date.UTC(A.getUTCFullYear()-100,A.getUTCMonth(),A.getUTCDate(),A.getUTCHours(),A.getUTCMinutes(),A.getUTCSeconds(),A.getUTCMilliseconds()));return A},jo1=(A)=>{let B=ho1.indexOf(A);if(B<0)throw new TypeError(`Invalid month: ${A}`);return B+1},ZWQ=[31,28,31,30,31,30,31,31,30,31,30,31],GWQ=(A,B,Q)=>{let D=ZWQ[B];if(B===1&&FWQ(A))D=29;if(Q>D)throw new TypeError(`Invalid day for ${ho1[B]} in ${A}: ${Q}`)},FWQ=(A)=>{return A%4===0&&(A%100!==0||A%400===0)},hN=(A,B,Q,D)=>{let Z=lKA(Gn(A));if(Z<Q||Z>D)throw new TypeError(`${B} must be between ${Q} and ${D}, inclusive`);return Z},IWQ=(A)=>{if(A===null||A===void 0)return 0;return mKA("0."+A)*1000},YWQ=(A)=>{let B=A[0],Q=1;if(B=="+")Q=1;else if(B=="-")Q=-1;else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);let D=Number(A.substring(1,3)),Z=Number(A.substring(4,6));return Q*(D*60+Z)*60*1000},Gn=(A)=>{let B=0;while(B<A.length-1&&A.charAt(B)==="0")B++;if(B===0)return A;return A.slice(B)},Ph=function A(B){return Object.assign(new String(B),{deserializeJSON(){return JSON.parse(String(B))},toString(){return String(B)},toJSON(){return String(B)}})};Ph.from=(A)=>{if(A&&typeof A==="object"&&(A instanceof Ph||("deserializeJSON"in A)))return A;else if(typeof A==="string"||Object.getPrototypeOf(A)===String.prototype)return Ph(String(A));return Ph(JSON.stringify(A))};Ph.fromObject=Ph.from;function WWQ(A){if(A.includes(",")||A.includes('"'))A=`"${A.replace(/"/g,"\\\"")}"`;return A}function JWQ(A,B,Q){if(Q<=0||!Number.isInteger(Q))throw new Error("Invalid number of delimiters ("+Q+") for splitEvery.");let D=A.split(B);if(Q===1)return D;let Z=[],G="";for(let F=0;F<D.length;F++){if(G==="")G=D[F];else G+=B+D[F];if((F+1)%Q===0)Z.push(G),G=""}if(G!=="")Z.push(G);return Z}var XWQ=(A)=>{let B=A.length,Q=[],D=!1,Z=void 0,G=0;for(let F=0;F<B;++F){let I=A[F];switch(I){case'"':if(Z!=="\\")D=!D;break;case",":if(!D)Q.push(A.slice(G,F)),G=F+1;break;default:}Z=I}return Q.push(A.slice(G)),Q.map((F)=>{F=F.trim();let I=F.length;if(I<2)return F;if(F[0]==='"'&&F[I-1]==='"')F=F.slice(1,I-1);return F.replace(/\\"/g,'"')})},pKA=class{constructor(A,B){this.string=A,this.type=B;let Q=0;for(let D=0;D<A.length;++D){let Z=A.charCodeAt(D);if(D===0&&Z===45)continue;if(Z===46){if(Q)throw new Error("@smithy/core/serde - NumericValue must contain at most one decimal point.");Q=1;continue}if(Z<48||Z>57)throw new Error('@smithy/core/serde - NumericValue must only contain [0-9], at most one decimal point ".", and an optional negation prefix "-".')}}toString(){return this.string}[Symbol.hasInstance](A){if(!A||typeof A!=="object")return!1;let B=A;if(typeof B.string==="string"&&typeof B.type==="string"&&B.constructor?.name==="NumericValue")return!0;return!1}};function VWQ(A){return new pKA(String(A),"bigDecimal")}});
var YSA=E((FSA)=>{Object.defineProperty(FSA,"__esModule",{value:!0});FSA.defaultEndpointResolver=void 0;var kjQ=Jn(),A00=$7(),yjQ=GSA(),_jQ=new A00.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),xjQ=(A,B={})=>{return _jQ.get(A,()=>A00.resolveEndpoint(yjQ.ruleSet,{endpointParams:A,logger:B.logger}))};FSA.defaultEndpointResolver=xjQ;A00.customEndpointFunctions.aws=kjQ.awsEndpointFunctions});
var Yy=E((F75,wE1)=>{var{defineProperty:gEA,getOwnPropertyDescriptor:hVQ,getOwnPropertyNames:gVQ}=Object,uVQ=Object.prototype.hasOwnProperty,zt1=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of gVQ(B))if(!uVQ.call(A,Z)&&Z!==Q)gEA(A,Z,{get:()=>B[Z],enumerable:!(D=hVQ(B,Z))||D.enumerable})}return A},uEA=(A,B,Q)=>(zt1(A,B,"default"),Q&&zt1(Q,B,"default")),mVQ=(A)=>zt1(gEA({},"__esModule",{value:!0}),A),Et1={};wE1.exports=mVQ(Et1);uEA(Et1,vEA(),wE1.exports);uEA(Et1,hEA(),wE1.exports)});
var ZI=E((cQ1)=>{Object.defineProperty(cQ1,"__esModule",{value:!0});var gt1=Sh();gt1.__exportStar(Qz(),cQ1);gt1.__exportStar(Ht1(),cQ1);gt1.__exportStar(oUA(),cQ1)});
var ZRA=E((EZ5,DRA)=>{var{defineProperty:VU1,getOwnPropertyDescriptor:KRQ,getOwnPropertyNames:HRQ}=Object,zRQ=Object.prototype.hasOwnProperty,N6=(A,B)=>VU1(A,"name",{value:B,configurable:!0}),ERQ=(A,B)=>{for(var Q in B)VU1(A,Q,{get:B[Q],enumerable:!0})},URQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of HRQ(B))if(!zRQ.call(A,Z)&&Z!==Q)VU1(A,Z,{get:()=>B[Z],enumerable:!(D=KRQ(B,Z))||D.enumerable})}return A},wRQ=(A)=>URQ(VU1({},"__esModule",{value:!0}),A),fMA={};ERQ(fMA,{GetRoleCredentialsCommand:()=>ARA,GetRoleCredentialsRequestFilterSensitiveLog:()=>dMA,GetRoleCredentialsResponseFilterSensitiveLog:()=>lMA,InvalidRequestException:()=>hMA,ListAccountRolesCommand:()=>me1,ListAccountRolesRequestFilterSensitiveLog:()=>pMA,ListAccountsCommand:()=>de1,ListAccountsRequestFilterSensitiveLog:()=>iMA,LogoutCommand:()=>BRA,LogoutRequestFilterSensitiveLog:()=>nMA,ResourceNotFoundException:()=>gMA,RoleCredentialsFilterSensitiveLog:()=>cMA,SSO:()=>QRA,SSOClient:()=>KU1,SSOServiceException:()=>Mn,TooManyRequestsException:()=>uMA,UnauthorizedException:()=>mMA,__Client:()=>MB.Client,paginateListAccountRoles:()=>pRQ,paginateListAccounts:()=>iRQ});DRA.exports=wRQ(fMA);var kMA=NQ1(),$RQ=LQ1(),qRQ=MQ1(),yMA=zn(),NRQ=V4(),uO=VB(),LRQ=TG(),D41=q6(),_MA=v4(),xMA=_e1(),MRQ=N6((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"awsssoportal"})},"resolveClientEndpointParameters"),CU1={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},RRQ=MMA(),vMA=B41(),bMA=mJ(),MB=x4(),ORQ=N6((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),TRQ=N6((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),PRQ=N6((A,B)=>{let Q=Object.assign(vMA.getAwsRegionExtensionConfiguration(A),MB.getDefaultExtensionConfiguration(A),bMA.getHttpHandlerExtensionConfiguration(A),ORQ(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,vMA.resolveAwsRegionExtensionConfiguration(Q),MB.resolveDefaultRuntimeConfig(Q),bMA.resolveHttpHandlerRuntimeConfig(Q),TRQ(Q))},"resolveRuntimeExtensions"),KU1=class extends MB.Client{static{N6(this,"SSOClient")}config;constructor(...[A]){let B=RRQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=MRQ(B),D=yMA.resolveUserAgentConfig(Q),Z=_MA.resolveRetryConfig(D),G=NRQ.resolveRegionConfig(Z),F=kMA.resolveHostHeaderConfig(G),I=D41.resolveEndpointConfig(F),Y=xMA.resolveHttpAuthSchemeConfig(I),W=PRQ(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(yMA.getUserAgentPlugin(this.config)),this.middlewareStack.use(_MA.getRetryPlugin(this.config)),this.middlewareStack.use(LRQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(kMA.getHostHeaderPlugin(this.config)),this.middlewareStack.use($RQ.getLoggerPlugin(this.config)),this.middlewareStack.use(qRQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(uO.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:xMA.defaultSSOHttpAuthSchemeParametersProvider,identityProviderConfigProvider:N6(async(J)=>new uO.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(uO.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},HU1=T3(),Mn=class A extends MB.ServiceException{static{N6(this,"SSOServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},hMA=class A extends Mn{static{N6(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},gMA=class A extends Mn{static{N6(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},uMA=class A extends Mn{static{N6(this,"TooManyRequestsException")}name="TooManyRequestsException";$fault="client";constructor(B){super({name:"TooManyRequestsException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},mMA=class A extends Mn{static{N6(this,"UnauthorizedException")}name="UnauthorizedException";$fault="client";constructor(B){super({name:"UnauthorizedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},dMA=N6((A)=>({...A,...A.accessToken&&{accessToken:MB.SENSITIVE_STRING}}),"GetRoleCredentialsRequestFilterSensitiveLog"),cMA=N6((A)=>({...A,...A.secretAccessKey&&{secretAccessKey:MB.SENSITIVE_STRING},...A.sessionToken&&{sessionToken:MB.SENSITIVE_STRING}}),"RoleCredentialsFilterSensitiveLog"),lMA=N6((A)=>({...A,...A.roleCredentials&&{roleCredentials:cMA(A.roleCredentials)}}),"GetRoleCredentialsResponseFilterSensitiveLog"),pMA=N6((A)=>({...A,...A.accessToken&&{accessToken:MB.SENSITIVE_STRING}}),"ListAccountRolesRequestFilterSensitiveLog"),iMA=N6((A)=>({...A,...A.accessToken&&{accessToken:MB.SENSITIVE_STRING}}),"ListAccountsRequestFilterSensitiveLog"),nMA=N6((A)=>({...A,...A.accessToken&&{accessToken:MB.SENSITIVE_STRING}}),"LogoutRequestFilterSensitiveLog"),Q41=ZI(),SRQ=N6(async(A,B)=>{let Q=uO.requestBuilder(A,B),D=MB.map({},MB.isSerializableHeaderValue,{[UU1]:A[EU1]});Q.bp("/federation/credentials");let Z=MB.map({[cRQ]:[,MB.expectNonNull(A[dRQ],"roleName")],[sMA]:[,MB.expectNonNull(A[aMA],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_GetRoleCredentialsCommand"),jRQ=N6(async(A,B)=>{let Q=uO.requestBuilder(A,B),D=MB.map({},MB.isSerializableHeaderValue,{[UU1]:A[EU1]});Q.bp("/assignment/roles");let Z=MB.map({[eMA]:[,A[tMA]],[oMA]:[()=>A.maxResults!==void 0,()=>A[rMA].toString()],[sMA]:[,MB.expectNonNull(A[aMA],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountRolesCommand"),kRQ=N6(async(A,B)=>{let Q=uO.requestBuilder(A,B),D=MB.map({},MB.isSerializableHeaderValue,{[UU1]:A[EU1]});Q.bp("/assignment/accounts");let Z=MB.map({[eMA]:[,A[tMA]],[oMA]:[()=>A.maxResults!==void 0,()=>A[rMA].toString()]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountsCommand"),yRQ=N6(async(A,B)=>{let Q=uO.requestBuilder(A,B),D=MB.map({},MB.isSerializableHeaderValue,{[UU1]:A[EU1]});Q.bp("/logout");let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_LogoutCommand"),_RQ=N6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return zU1(A,B);let Q=MB.map({$metadata:zy(A)}),D=MB.expectNonNull(MB.expectObject(await Q41.parseJsonBody(A.body,B)),"body"),Z=MB.take(D,{roleCredentials:MB._json});return Object.assign(Q,Z),Q},"de_GetRoleCredentialsCommand"),xRQ=N6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return zU1(A,B);let Q=MB.map({$metadata:zy(A)}),D=MB.expectNonNull(MB.expectObject(await Q41.parseJsonBody(A.body,B)),"body"),Z=MB.take(D,{nextToken:MB.expectString,roleList:MB._json});return Object.assign(Q,Z),Q},"de_ListAccountRolesCommand"),vRQ=N6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return zU1(A,B);let Q=MB.map({$metadata:zy(A)}),D=MB.expectNonNull(MB.expectObject(await Q41.parseJsonBody(A.body,B)),"body"),Z=MB.take(D,{accountList:MB._json,nextToken:MB.expectString});return Object.assign(Q,Z),Q},"de_ListAccountsCommand"),bRQ=N6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return zU1(A,B);let Q=MB.map({$metadata:zy(A)});return await MB.collectBody(A.body,B),Q},"de_LogoutCommand"),zU1=N6(async(A,B)=>{let Q={...A,body:await Q41.parseJsonErrorBody(A.body,B)},D=Q41.loadRestJsonErrorCode(A,Q.body);switch(D){case"InvalidRequestException":case"com.amazonaws.sso#InvalidRequestException":throw await hRQ(Q,B);case"ResourceNotFoundException":case"com.amazonaws.sso#ResourceNotFoundException":throw await gRQ(Q,B);case"TooManyRequestsException":case"com.amazonaws.sso#TooManyRequestsException":throw await uRQ(Q,B);case"UnauthorizedException":case"com.amazonaws.sso#UnauthorizedException":throw await mRQ(Q,B);default:let Z=Q.body;return fRQ({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),fRQ=MB.withBaseException(Mn),hRQ=N6(async(A,B)=>{let Q=MB.map({}),D=A.body,Z=MB.take(D,{message:MB.expectString});Object.assign(Q,Z);let G=new hMA({$metadata:zy(A),...Q});return MB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),gRQ=N6(async(A,B)=>{let Q=MB.map({}),D=A.body,Z=MB.take(D,{message:MB.expectString});Object.assign(Q,Z);let G=new gMA({$metadata:zy(A),...Q});return MB.decorateServiceException(G,A.body)},"de_ResourceNotFoundExceptionRes"),uRQ=N6(async(A,B)=>{let Q=MB.map({}),D=A.body,Z=MB.take(D,{message:MB.expectString});Object.assign(Q,Z);let G=new uMA({$metadata:zy(A),...Q});return MB.decorateServiceException(G,A.body)},"de_TooManyRequestsExceptionRes"),mRQ=N6(async(A,B)=>{let Q=MB.map({}),D=A.body,Z=MB.take(D,{message:MB.expectString});Object.assign(Q,Z);let G=new mMA({$metadata:zy(A),...Q});return MB.decorateServiceException(G,A.body)},"de_UnauthorizedExceptionRes"),zy=N6((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),aMA="accountId",EU1="accessToken",sMA="account_id",rMA="maxResults",oMA="max_result",tMA="nextToken",eMA="next_token",dRQ="roleName",cRQ="role_name",UU1="x-amz-sso_bearer_token",ARA=class extends MB.Command.classBuilder().ep(CU1).m(function(A,B,Q,D){return[HU1.getSerdePlugin(Q,this.serialize,this.deserialize),D41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","GetRoleCredentials",{}).n("SSOClient","GetRoleCredentialsCommand").f(dMA,lMA).ser(SRQ).de(_RQ).build(){static{N6(this,"GetRoleCredentialsCommand")}},me1=class extends MB.Command.classBuilder().ep(CU1).m(function(A,B,Q,D){return[HU1.getSerdePlugin(Q,this.serialize,this.deserialize),D41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccountRoles",{}).n("SSOClient","ListAccountRolesCommand").f(pMA,void 0).ser(jRQ).de(xRQ).build(){static{N6(this,"ListAccountRolesCommand")}},de1=class extends MB.Command.classBuilder().ep(CU1).m(function(A,B,Q,D){return[HU1.getSerdePlugin(Q,this.serialize,this.deserialize),D41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccounts",{}).n("SSOClient","ListAccountsCommand").f(iMA,void 0).ser(kRQ).de(vRQ).build(){static{N6(this,"ListAccountsCommand")}},BRA=class extends MB.Command.classBuilder().ep(CU1).m(function(A,B,Q,D){return[HU1.getSerdePlugin(Q,this.serialize,this.deserialize),D41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","Logout",{}).n("SSOClient","LogoutCommand").f(nMA,void 0).ser(yRQ).de(bRQ).build(){static{N6(this,"LogoutCommand")}},lRQ={GetRoleCredentialsCommand:ARA,ListAccountRolesCommand:me1,ListAccountsCommand:de1,LogoutCommand:BRA},QRA=class extends KU1{static{N6(this,"SSO")}};MB.createAggregatedClient(lRQ,QRA);var pRQ=uO.createPaginator(KU1,me1,"nextToken","nextToken","maxResults"),iRQ=uO.createPaginator(KU1,de1,"nextToken","nextToken","maxResults")});
var Ze1=E((EqA)=>{Object.defineProperty(EqA,"__esModule",{value:!0});EqA.default=void 0;var bwQ=fwQ(nQ1());function fwQ(A){return A&&A.__esModule?A:{default:A}}function hwQ(A){if(!bwQ.default(A))throw TypeError("Invalid UUID");let B,Q=new Uint8Array(16);return Q[0]=(B=parseInt(A.slice(0,8),16))>>>24,Q[1]=B>>>16&255,Q[2]=B>>>8&255,Q[3]=B&255,Q[4]=(B=parseInt(A.slice(9,13),16))>>>8,Q[5]=B&255,Q[6]=(B=parseInt(A.slice(14,18),16))>>>8,Q[7]=B&255,Q[8]=(B=parseInt(A.slice(19,23),16))>>>8,Q[9]=B&255,Q[10]=(B=parseInt(A.slice(24,36),16))/*************&255,Q[11]=B/**********&255,Q[12]=B>>>24&255,Q[13]=B>>>16&255,Q[14]=B>>>8&255,Q[15]=B&255,Q}var gwQ=hwQ;EqA.default=gwQ});
var Zo1=E((V55,DXA)=>{var{defineProperty:Xz1,getOwnPropertyDescriptor:BDQ,getOwnPropertyNames:QDQ}=Object,DDQ=Object.prototype.hasOwnProperty,Vz1=(A,B)=>Xz1(A,"name",{value:B,configurable:!0}),ZDQ=(A,B)=>{for(var Q in B)Xz1(A,Q,{get:B[Q],enumerable:!0})},GDQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of QDQ(B))if(!DDQ.call(A,Z)&&Z!==Q)Xz1(A,Z,{get:()=>B[Z],enumerable:!(D=BDQ(B,Z))||D.enumerable})}return A},FDQ=(A)=>GDQ(Xz1({},"__esModule",{value:!0}),A),sJA={};ZDQ(sJA,{AlgorithmId:()=>eJA,EndpointURLScheme:()=>tJA,FieldPosition:()=>AXA,HttpApiKeyAuthLocation:()=>oJA,HttpAuthLocation:()=>rJA,IniSectionType:()=>BXA,RequestHandlerProtocol:()=>QXA,SMITHY_CONTEXT_KEY:()=>XDQ,getDefaultClientConfiguration:()=>WDQ,resolveDefaultRuntimeConfig:()=>JDQ});DXA.exports=FDQ(sJA);var rJA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(rJA||{}),oJA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(oJA||{}),tJA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(tJA||{}),eJA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(eJA||{}),IDQ=Vz1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),YDQ=Vz1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),WDQ=Vz1((A)=>{return IDQ(A)},"getDefaultClientConfiguration"),JDQ=Vz1((A)=>{return YDQ(A)},"resolveDefaultRuntimeConfig"),AXA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(AXA||{}),XDQ="__smithy_context",BXA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(BXA||{}),QXA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(QXA||{})});
var Zy=E((I35,VKA)=>{var{defineProperty:gz1,getOwnPropertyDescriptor:yIQ,getOwnPropertyNames:_IQ}=Object,xIQ=Object.prototype.hasOwnProperty,IKA=(A,B)=>gz1(A,"name",{value:B,configurable:!0}),vIQ=(A,B)=>{for(var Q in B)gz1(A,Q,{get:B[Q],enumerable:!0})},bIQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of _IQ(B))if(!xIQ.call(A,Z)&&Z!==Q)gz1(A,Z,{get:()=>B[Z],enumerable:!(D=yIQ(B,Z))||D.enumerable})}return A},fIQ=(A)=>bIQ(gz1({},"__esModule",{value:!0}),A),YKA={};vIQ(YKA,{fromHex:()=>JKA,toHex:()=>XKA});VKA.exports=fIQ(YKA);var WKA={},$o1={};for(let A=0;A<256;A++){let B=A.toString(16).toLowerCase();if(B.length===1)B=`0${B}`;WKA[A]=B,$o1[B]=A}function JKA(A){if(A.length%2!==0)throw new Error("Hex encoded strings must have an even number length");let B=new Uint8Array(A.length/2);for(let Q=0;Q<A.length;Q+=2){let D=A.slice(Q,Q+2).toLowerCase();if(D in $o1)B[Q/2]=$o1[D];else throw new Error(`Cannot decode unrecognized sequence ${D} as hexadecimal`)}return B}IKA(JKA,"fromHex");function XKA(A){let B="";for(let Q=0;Q<A.byteLength;Q++)B+=WKA[A[Q]];return B}IKA(XKA,"toHex")});
var _XA=E((z55,yXA)=>{var{defineProperty:Uz1,getOwnPropertyDescriptor:dDQ,getOwnPropertyNames:cDQ}=Object,lDQ=Object.prototype.hasOwnProperty,ek=(A,B)=>Uz1(A,"name",{value:B,configurable:!0}),pDQ=(A,B)=>{for(var Q in B)Uz1(A,Q,{get:B[Q],enumerable:!0})},iDQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of cDQ(B))if(!lDQ.call(A,Z)&&Z!==Q)Uz1(A,Z,{get:()=>B[Z],enumerable:!(D=dDQ(B,Z))||D.enumerable})}return A},nDQ=(A)=>iDQ(Uz1({},"__esModule",{value:!0}),A),PXA={};pDQ(PXA,{Field:()=>rDQ,Fields:()=>oDQ,HttpRequest:()=>tDQ,HttpResponse:()=>eDQ,IHttpRequest:()=>SXA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>aDQ,isValidHostname:()=>kXA,resolveHttpHandlerRuntimeConfig:()=>sDQ});yXA.exports=nDQ(PXA);var aDQ=ek((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),sDQ=ek((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),SXA=TXA(),rDQ=class{static{ek(this,"Field")}constructor({name:A,kind:B=SXA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},oDQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{ek(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},tDQ=class A{static{ek(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=jXA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function jXA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}ek(jXA,"cloneQuery");var eDQ=class{static{ek(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function kXA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}ek(kXA,"isValidHostname")});
var _e1=E((MLA)=>{Object.defineProperty(MLA,"__esModule",{value:!0});MLA.resolveHttpAuthSchemeConfig=MLA.defaultSSOHttpAuthSchemeProvider=MLA.defaultSSOHttpAuthSchemeParametersProvider=void 0;var _LQ=ZI(),ye1=I5(),xLQ=async(A,B,Q)=>{return{operation:ye1.getSmithyContext(B).operation,region:await ye1.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};MLA.defaultSSOHttpAuthSchemeParametersProvider=xLQ;function vLQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"awsssoportal",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function GU1(A){return{schemeId:"smithy.api#noAuth"}}var bLQ=(A)=>{let B=[];switch(A.operation){case"GetRoleCredentials":{B.push(GU1(A));break}case"ListAccountRoles":{B.push(GU1(A));break}case"ListAccounts":{B.push(GU1(A));break}case"Logout":{B.push(GU1(A));break}default:B.push(vLQ(A))}return B};MLA.defaultSSOHttpAuthSchemeProvider=bLQ;var fLQ=(A)=>{let B=_LQ.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:ye1.normalizeProvider(A.authSchemePreference??[])})};MLA.resolveHttpAuthSchemeConfig=fLQ});
var aCA=E((Z35,nCA)=>{var{defineProperty:vz1,getOwnPropertyDescriptor:IIQ,getOwnPropertyNames:YIQ}=Object,WIQ=Object.prototype.hasOwnProperty,Eo1=(A,B)=>vz1(A,"name",{value:B,configurable:!0}),JIQ=(A,B)=>{for(var Q in B)vz1(A,Q,{get:B[Q],enumerable:!0})},XIQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of YIQ(B))if(!WIQ.call(A,Z)&&Z!==Q)vz1(A,Z,{get:()=>B[Z],enumerable:!(D=IIQ(B,Z))||D.enumerable})}return A},VIQ=(A)=>XIQ(vz1({},"__esModule",{value:!0}),A),pCA={};JIQ(pCA,{escapeUri:()=>iCA,escapeUriPath:()=>KIQ});nCA.exports=VIQ(pCA);var iCA=Eo1((A)=>encodeURIComponent(A).replace(/[!'()*]/g,CIQ),"escapeUri"),CIQ=Eo1((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),KIQ=Eo1((A)=>A.split("/").map(iCA).join("/"),"escapeUriPath")});
var aQ1=E((XqA)=>{Object.defineProperty(XqA,"__esModule",{value:!0});XqA.default=void 0;XqA.unsafeStringify=JqA;var OwQ=TwQ(nQ1());function TwQ(A){return A&&A.__esModule?A:{default:A}}var $Y=[];for(let A=0;A<256;++A)$Y.push((A+256).toString(16).slice(1));function JqA(A,B=0){return $Y[A[B+0]]+$Y[A[B+1]]+$Y[A[B+2]]+$Y[A[B+3]]+"-"+$Y[A[B+4]]+$Y[A[B+5]]+"-"+$Y[A[B+6]]+$Y[A[B+7]]+"-"+$Y[A[B+8]]+$Y[A[B+9]]+"-"+$Y[A[B+10]]+$Y[A[B+11]]+$Y[A[B+12]]+$Y[A[B+13]]+$Y[A[B+14]]+$Y[A[B+15]]}function PwQ(A,B=0){let Q=JqA(A,B);if(!OwQ.default(Q))throw TypeError("Stringified UUID is invalid");return Q}var SwQ=PwQ;XqA.default=SwQ});
var aXA=E((R55,nXA)=>{var{defineProperty:Nz1,getOwnPropertyDescriptor:wZQ,getOwnPropertyNames:$ZQ}=Object,qZQ=Object.prototype.hasOwnProperty,NZQ=(A,B)=>Nz1(A,"name",{value:B,configurable:!0}),LZQ=(A,B)=>{for(var Q in B)Nz1(A,Q,{get:B[Q],enumerable:!0})},MZQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of $ZQ(B))if(!qZQ.call(A,Z)&&Z!==Q)Nz1(A,Z,{get:()=>B[Z],enumerable:!(D=wZQ(B,Z))||D.enumerable})}return A},RZQ=(A)=>MZQ(Nz1({},"__esModule",{value:!0}),A),iXA={};LZQ(iXA,{isArrayBuffer:()=>OZQ});nXA.exports=RZQ(iXA);var OZQ=NZQ((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var c10=E((rZ5,CPA)=>{var{defineProperty:OU1,getOwnPropertyDescriptor:PSQ,getOwnPropertyNames:SSQ}=Object,jSQ=Object.prototype.hasOwnProperty,d10=(A,B)=>OU1(A,"name",{value:B,configurable:!0}),kSQ=(A,B)=>{for(var Q in B)OU1(A,Q,{get:B[Q],enumerable:!0})},ySQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of SSQ(B))if(!jSQ.call(A,Z)&&Z!==Q)OU1(A,Z,{get:()=>B[Z],enumerable:!(D=PSQ(B,Z))||D.enumerable})}return A},_SQ=(A)=>ySQ(OU1({},"__esModule",{value:!0}),A),VPA={};kSQ(VPA,{fromProcess:()=>gSQ});CPA.exports=_SQ(VPA);var XPA=e5(),m10=eB(),xSQ=J1("child_process"),vSQ=J1("util"),bSQ=Qz(),fSQ=d10((A,B,Q)=>{if(B.Version!==1)throw Error(`Profile ${A} credential_process did not return Version 1.`);if(B.AccessKeyId===void 0||B.SecretAccessKey===void 0)throw Error(`Profile ${A} credential_process returned invalid credentials.`);if(B.Expiration){let G=new Date;if(new Date(B.Expiration)<G)throw Error(`Profile ${A} credential_process returned expired credentials.`)}let D=B.AccountId;if(!D&&Q?.[A]?.aws_account_id)D=Q[A].aws_account_id;let Z={accessKeyId:B.AccessKeyId,secretAccessKey:B.SecretAccessKey,...B.SessionToken&&{sessionToken:B.SessionToken},...B.Expiration&&{expiration:new Date(B.Expiration)},...B.CredentialScope&&{credentialScope:B.CredentialScope},...D&&{accountId:D}};return bSQ.setCredentialFeature(Z,"CREDENTIALS_PROCESS","w"),Z},"getValidatedProcessCredentials"),hSQ=d10(async(A,B,Q)=>{let D=B[A];if(B[A]){let Z=D.credential_process;if(Z!==void 0){let G=vSQ.promisify(xSQ.exec);try{let{stdout:F}=await G(Z),I;try{I=JSON.parse(F.trim())}catch{throw Error(`Profile ${A} credential_process returned invalid JSON.`)}return fSQ(A,I,B)}catch(F){throw new m10.CredentialsProviderError(F.message,{logger:Q})}}else throw new m10.CredentialsProviderError(`Profile ${A} did not contain credential_process.`,{logger:Q})}else throw new m10.CredentialsProviderError(`Profile ${A} could not be found in shared credentials file.`,{logger:Q})},"resolveProcessCredentials"),gSQ=d10((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-process - fromProcess");let Q=await XPA.parseKnownFiles(A);return hSQ(XPA.getProfileName({profile:A.profile??B?.profile}),Q,A.logger)},"fromProcess")});
var cB=E((P55,ZVA)=>{var{defineProperty:Mz1,getOwnPropertyDescriptor:gZQ,getOwnPropertyNames:uZQ}=Object,mZQ=Object.prototype.hasOwnProperty,Io1=(A,B)=>Mz1(A,"name",{value:B,configurable:!0}),dZQ=(A,B)=>{for(var Q in B)Mz1(A,Q,{get:B[Q],enumerable:!0})},cZQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of uZQ(B))if(!mZQ.call(A,Z)&&Z!==Q)Mz1(A,Z,{get:()=>B[Z],enumerable:!(D=gZQ(B,Z))||D.enumerable})}return A},lZQ=(A)=>cZQ(Mz1({},"__esModule",{value:!0}),A),BVA={};dZQ(BVA,{fromUtf8:()=>DVA,toUint8Array:()=>pZQ,toUtf8:()=>iZQ});ZVA.exports=lZQ(BVA);var QVA=AD(),DVA=Io1((A)=>{let B=QVA.fromString(A,"utf8");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength/Uint8Array.BYTES_PER_ELEMENT)},"fromUtf8"),pZQ=Io1((A)=>{if(typeof A==="string")return DVA(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)},"toUint8Array"),iZQ=Io1((A)=>{if(typeof A==="string")return A;if(typeof A!=="object"||typeof A.byteOffset!=="number"||typeof A.byteLength!=="number")throw new Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return QVA.fromArrayBuffer(A.buffer,A.byteOffset,A.byteLength).toString("utf8")},"toUtf8")});
var cNA=E((oD5,KNQ)=>{KNQ.exports={name:"@aws-sdk/client-sts",description:"AWS SDK for JavaScript Sts Client for Node.js, Browser and React Native",version:"3.840.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-sts","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"rimraf ./dist-types tsconfig.types.tsbuildinfo && tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo sts",test:"yarn g:vitest run","test:watch":"yarn g:vitest watch"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/credential-provider-node":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sts",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-sts"}}});
var dzA=E((s35,mzA)=>{var{defineProperty:WE1,getOwnPropertyDescriptor:jXQ,getOwnPropertyNames:kXQ}=Object,yXQ=Object.prototype.hasOwnProperty,Qt1=(A,B)=>WE1(A,"name",{value:B,configurable:!0}),_XQ=(A,B)=>{for(var Q in B)WE1(A,Q,{get:B[Q],enumerable:!0})},xXQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of kXQ(B))if(!yXQ.call(A,Z)&&Z!==Q)WE1(A,Z,{get:()=>B[Z],enumerable:!(D=jXQ(B,Z))||D.enumerable})}return A},vXQ=(A)=>xXQ(WE1({},"__esModule",{value:!0}),A),gzA={};_XQ(gzA,{escapeUri:()=>uzA,escapeUriPath:()=>fXQ});mzA.exports=vXQ(gzA);var uzA=Qt1((A)=>encodeURIComponent(A).replace(/[!'()*]/g,bXQ),"escapeUri"),bXQ=Qt1((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),fXQ=Qt1((A)=>A.split("/").map(uzA).join("/"),"escapeUriPath")});
var e5=E((FD5,pQ1)=>{var{defineProperty:uE1,getOwnPropertyDescriptor:kEQ,getOwnPropertyNames:yEQ}=Object,_EQ=Object.prototype.hasOwnProperty,Zz=(A,B)=>uE1(A,"name",{value:B,configurable:!0}),xEQ=(A,B)=>{for(var Q in B)uE1(A,Q,{get:B[Q],enumerable:!0})},nt1=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of yEQ(B))if(!_EQ.call(A,Z)&&Z!==Q)uE1(A,Z,{get:()=>B[Z],enumerable:!(D=kEQ(B,Z))||D.enumerable})}return A},st1=(A,B,Q)=>(nt1(A,B,"default"),Q&&nt1(Q,B,"default")),vEQ=(A)=>nt1(uE1({},"__esModule",{value:!0}),A),lQ1={};xEQ(lQ1,{CONFIG_PREFIX_SEPARATOR:()=>bh,DEFAULT_PROFILE:()=>H$A,ENV_PROFILE:()=>K$A,getProfileName:()=>bEQ,loadSharedConfigFiles:()=>E$A,loadSsoSessionData:()=>sEQ,parseKnownFiles:()=>oEQ});pQ1.exports=vEQ(lQ1);st1(lQ1,En(),pQ1.exports);var K$A="AWS_PROFILE",H$A="default",bEQ=Zz((A)=>A.profile||process.env[K$A]||H$A,"getProfileName");st1(lQ1,lt1(),pQ1.exports);st1(lQ1,ewA(),pQ1.exports);var hE1=W$A(),fEQ=Zz((A)=>Object.entries(A).filter(([B])=>{let Q=B.indexOf(bh);if(Q===-1)return!1;return Object.values(hE1.IniSectionType).includes(B.substring(0,Q))}).reduce((B,[Q,D])=>{let Z=Q.indexOf(bh),G=Q.substring(0,Z)===hE1.IniSectionType.PROFILE?Q.substring(Z+1):Q;return B[G]=D,B},{...A.default&&{default:A.default}}),"getConfigData"),gE1=J1("path"),hEQ=En(),gEQ="AWS_CONFIG_FILE",z$A=Zz(()=>process.env[gEQ]||gE1.join(hEQ.getHomeDir(),".aws","config"),"getConfigFilepath"),uEQ=En(),mEQ="AWS_SHARED_CREDENTIALS_FILE",dEQ=Zz(()=>process.env[mEQ]||gE1.join(uEQ.getHomeDir(),".aws","credentials"),"getCredentialsFilepath"),cEQ=En(),lEQ=/^([\w-]+)\s(["'])?([\w-@\+\.%:/]+)\2$/,pEQ=["__proto__","profile __proto__"],at1=Zz((A)=>{let B={},Q,D;for(let Z of A.split(/\r?\n/)){let G=Z.split(/(^|\s)[;#]/)[0].trim();if(G[0]==="["&&G[G.length-1]==="]"){Q=void 0,D=void 0;let I=G.substring(1,G.length-1),Y=lEQ.exec(I);if(Y){let[,W,,J]=Y;if(Object.values(hE1.IniSectionType).includes(W))Q=[W,J].join(bh)}else Q=I;if(pEQ.includes(I))throw new Error(`Found invalid profile name "${I}"`)}else if(Q){let I=G.indexOf("=");if(![0,-1].includes(I)){let[Y,W]=[G.substring(0,I).trim(),G.substring(I+1).trim()];if(W==="")D=Y;else{if(D&&Z.trimStart()===Z)D=void 0;B[Q]=B[Q]||{};let J=D?[D,Y].join(bh):Y;B[Q][J]=W}}}}return B},"parseIni"),V$A=it1(),C$A=Zz(()=>({}),"swallowError"),bh=".",E$A=Zz(async(A={})=>{let{filepath:B=dEQ(),configFilepath:Q=z$A()}=A,D=cEQ.getHomeDir(),Z="~/",G=B;if(B.startsWith("~/"))G=gE1.join(D,B.slice(2));let F=Q;if(Q.startsWith("~/"))F=gE1.join(D,Q.slice(2));let I=await Promise.all([V$A.slurpFile(F,{ignoreCache:A.ignoreCache}).then(at1).then(fEQ).catch(C$A),V$A.slurpFile(G,{ignoreCache:A.ignoreCache}).then(at1).catch(C$A)]);return{configFile:I[0],credentialsFile:I[1]}},"loadSharedConfigFiles"),iEQ=Zz((A)=>Object.entries(A).filter(([B])=>B.startsWith(hE1.IniSectionType.SSO_SESSION+bh)).reduce((B,[Q,D])=>({...B,[Q.substring(Q.indexOf(bh)+1)]:D}),{}),"getSsoSessionData"),nEQ=it1(),aEQ=Zz(()=>({}),"swallowError"),sEQ=Zz(async(A={})=>nEQ.slurpFile(A.configFilepath??z$A()).then(at1).then(iEQ).catch(aEQ),"loadSsoSessionData"),rEQ=Zz((...A)=>{let B={};for(let Q of A)for(let[D,Z]of Object.entries(Q))if(B[D]!==void 0)Object.assign(B[D],Z);else B[D]=Z;return B},"mergeConfigFiles"),oEQ=Zz(async(A)=>{let B=await E$A(A);return rEQ(B.configFile,B.credentialsFile)},"parseKnownFiles")});
var eB=E((n35,vzA)=>{var{defineProperty:FE1,getOwnPropertyDescriptor:XXQ,getOwnPropertyNames:VXQ}=Object,CXQ=Object.prototype.hasOwnProperty,jh=(A,B)=>FE1(A,"name",{value:B,configurable:!0}),KXQ=(A,B)=>{for(var Q in B)FE1(A,Q,{get:B[Q],enumerable:!0})},HXQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of VXQ(B))if(!CXQ.call(A,Z)&&Z!==Q)FE1(A,Z,{get:()=>B[Z],enumerable:!(D=XXQ(B,Z))||D.enumerable})}return A},zXQ=(A)=>HXQ(FE1({},"__esModule",{value:!0}),A),xzA={};KXQ(xzA,{CredentialsProviderError:()=>EXQ,ProviderError:()=>IE1,TokenProviderError:()=>UXQ,chain:()=>wXQ,fromStatic:()=>$XQ,memoize:()=>qXQ});vzA.exports=zXQ(xzA);var IE1=class A extends Error{constructor(B,Q=!0){let D,Z=!0;if(typeof Q==="boolean")D=void 0,Z=Q;else if(Q!=null&&typeof Q==="object")D=Q.logger,Z=Q.tryNextLink??!0;super(B);this.name="ProviderError",this.tryNextLink=Z,Object.setPrototypeOf(this,A.prototype),D?.debug?.(`@smithy/property-provider ${Z?"->":"(!)"} ${B}`)}static{jh(this,"ProviderError")}static from(B,Q=!0){return Object.assign(new this(B.message,Q),B)}},EXQ=class A extends IE1{constructor(B,Q=!0){super(B,Q);this.name="CredentialsProviderError",Object.setPrototypeOf(this,A.prototype)}static{jh(this,"CredentialsProviderError")}},UXQ=class A extends IE1{constructor(B,Q=!0){super(B,Q);this.name="TokenProviderError",Object.setPrototypeOf(this,A.prototype)}static{jh(this,"TokenProviderError")}},wXQ=jh((...A)=>async()=>{if(A.length===0)throw new IE1("No providers in chain");let B;for(let Q of A)try{return await Q()}catch(D){if(B=D,D?.tryNextLink)continue;throw D}throw B},"chain"),$XQ=jh((A)=>()=>Promise.resolve(A),"fromStatic"),qXQ=jh((A,B,Q)=>{let D,Z,G,F=!1,I=jh(async()=>{if(!Z)Z=A();try{D=await Z,G=!0,F=!1}finally{Z=void 0}return D},"coalesceProvider");if(B===void 0)return async(Y)=>{if(!G||Y?.forceRefresh)D=await I();return D};return async(Y)=>{if(!G||Y?.forceRefresh)D=await I();if(F)return D;if(Q&&!Q(D))return F=!0,D;if(B(D))return await I(),D;return D}},"memoize")});
var et1=E((CD5,o$A)=>{var{defineProperty:iE1,getOwnPropertyDescriptor:aUQ,getOwnPropertyNames:sUQ}=Object,rUQ=Object.prototype.hasOwnProperty,nE1=(A,B)=>iE1(A,"name",{value:B,configurable:!0}),oUQ=(A,B)=>{for(var Q in B)iE1(A,Q,{get:B[Q],enumerable:!0})},tUQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of sUQ(B))if(!rUQ.call(A,Z)&&Z!==Q)iE1(A,Z,{get:()=>B[Z],enumerable:!(D=aUQ(B,Z))||D.enumerable})}return A},eUQ=(A)=>tUQ(iE1({},"__esModule",{value:!0}),A),c$A={};oUQ(c$A,{AlgorithmId:()=>n$A,EndpointURLScheme:()=>i$A,FieldPosition:()=>a$A,HttpApiKeyAuthLocation:()=>p$A,HttpAuthLocation:()=>l$A,IniSectionType:()=>s$A,RequestHandlerProtocol:()=>r$A,SMITHY_CONTEXT_KEY:()=>ZwQ,getDefaultClientConfiguration:()=>QwQ,resolveDefaultRuntimeConfig:()=>DwQ});o$A.exports=eUQ(c$A);var l$A=((A)=>{return A.HEADER="header",A.QUERY="query",A})(l$A||{}),p$A=((A)=>{return A.HEADER="header",A.QUERY="query",A})(p$A||{}),i$A=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(i$A||{}),n$A=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(n$A||{}),AwQ=nE1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),BwQ=nE1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),QwQ=nE1((A)=>{return AwQ(A)},"getDefaultClientConfiguration"),DwQ=nE1((A)=>{return BwQ(A)},"resolveDefaultRuntimeConfig"),a$A=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(a$A||{}),ZwQ="__smithy_context",s$A=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(s$A||{}),r$A=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(r$A||{})});
var ewA=E((owA)=>{Object.defineProperty(owA,"__esModule",{value:!0});owA.getSSOTokenFromFile=void 0;var CEQ=J1("fs"),KEQ=lt1(),{readFile:HEQ}=CEQ.promises,zEQ=async(A)=>{let B=KEQ.getSSOTokenFilepath(A),Q=await HEQ(B,"utf8");return JSON.parse(Q)};owA.getSSOTokenFromFile=zEQ});
var fqA=E((vqA)=>{Object.defineProperty(vqA,"__esModule",{value:!0});vqA.default=void 0;var _qA=xqA(yqA()),Z$Q=xqA(Ae1()),G$Q=aQ1();function xqA(A){return A&&A.__esModule?A:{default:A}}function F$Q(A,B,Q){if(_qA.default.randomUUID&&!B&&!A)return _qA.default.randomUUID();A=A||{};let D=A.random||(A.rng||Z$Q.default)();if(D[6]=D[6]&15|64,D[8]=D[8]&63|128,B){Q=Q||0;for(let Z=0;Z<16;++Z)B[Q+Z]=D[Z];return B}return G$Q.unsafeStringify(D)}var I$Q=F$Q;vqA.default=I$Q});
var gN=E((R75,jUA)=>{var cKQ=Tt1(),lKQ=NUA(),pKQ=SUA();jUA.exports={XMLParser:lKQ,XMLValidator:cKQ,XMLBuilder:pKQ}});
var gQ1=E((O75,vUA)=>{var{defineProperty:LE1,getOwnPropertyDescriptor:iKQ,getOwnPropertyNames:nKQ}=Object,aKQ=Object.prototype.hasOwnProperty,ME1=(A,B)=>LE1(A,"name",{value:B,configurable:!0}),sKQ=(A,B)=>{for(var Q in B)LE1(A,Q,{get:B[Q],enumerable:!0})},rKQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of nKQ(B))if(!aKQ.call(A,Z)&&Z!==Q)LE1(A,Z,{get:()=>B[Z],enumerable:!(D=iKQ(B,Z))||D.enumerable})}return A},oKQ=(A)=>rKQ(LE1({},"__esModule",{value:!0}),A),kUA={};sKQ(kUA,{XmlNode:()=>tKQ,XmlText:()=>xUA});vUA.exports=oKQ(kUA);function yUA(A){return A.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")}ME1(yUA,"escapeAttribute");function _UA(A){return A.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r/g,"&#x0D;").replace(/\n/g,"&#x0A;").replace(/\u0085/g,"&#x85;").replace(/\u2028/,"&#x2028;")}ME1(_UA,"escapeElement");var xUA=class{constructor(A){this.value=A}static{ME1(this,"XmlText")}toString(){return _UA(""+this.value)}},tKQ=class A{constructor(B,Q=[]){this.name=B,this.children=Q}static{ME1(this,"XmlNode")}attributes={};static of(B,Q,D){let Z=new A(B);if(Q!==void 0)Z.addChildNode(new xUA(Q));if(D!==void 0)Z.withName(D);return Z}withName(B){return this.name=B,this}addAttribute(B,Q){return this.attributes[B]=Q,this}addChildNode(B){return this.children.push(B),this}removeAttribute(B){return delete this.attributes[B],this}n(B){return this.name=B,this}c(B){return this.children.push(B),this}a(B,Q){if(Q!=null)this.attributes[B]=Q;return this}cc(B,Q,D=Q){if(B[Q]!=null){let Z=A.of(Q,B[Q]).withName(D);this.c(Z)}}l(B,Q,D,Z){if(B[Q]!=null)Z().map((F)=>{F.withName(D),this.c(F)})}lc(B,Q,D,Z){if(B[Q]!=null){let G=Z(),F=new A(D);G.map((I)=>{F.c(I)}),this.c(F)}}toString(){let B=Boolean(this.children.length),Q=`<${this.name}`,D=this.attributes;for(let Z of Object.keys(D)){let G=D[Z];if(G!=null)Q+=` ${Z}="${yUA(""+G)}"`}return Q+=!B?"/>":`>${this.children.map((Z)=>Z.toString()).join("")}</${this.name}>`}}});
var hCA=E((e55,fCA)=>{var{defineProperty:yz1,getOwnPropertyDescriptor:hFQ,getOwnPropertyNames:gFQ}=Object,uFQ=Object.prototype.hasOwnProperty,_z1=(A,B)=>yz1(A,"name",{value:B,configurable:!0}),mFQ=(A,B)=>{for(var Q in B)yz1(A,Q,{get:B[Q],enumerable:!0})},dFQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of gFQ(B))if(!uFQ.call(A,Z)&&Z!==Q)yz1(A,Z,{get:()=>B[Z],enumerable:!(D=hFQ(B,Z))||D.enumerable})}return A},cFQ=(A)=>dFQ(yz1({},"__esModule",{value:!0}),A),SCA={};mFQ(SCA,{AlgorithmId:()=>_CA,EndpointURLScheme:()=>yCA,FieldPosition:()=>xCA,HttpApiKeyAuthLocation:()=>kCA,HttpAuthLocation:()=>jCA,IniSectionType:()=>vCA,RequestHandlerProtocol:()=>bCA,SMITHY_CONTEXT_KEY:()=>aFQ,getDefaultClientConfiguration:()=>iFQ,resolveDefaultRuntimeConfig:()=>nFQ});fCA.exports=cFQ(SCA);var jCA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(jCA||{}),kCA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(kCA||{}),yCA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(yCA||{}),_CA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(_CA||{}),lFQ=_z1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),pFQ=_z1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),iFQ=_z1((A)=>{return lFQ(A)},"getDefaultClientConfiguration"),nFQ=_z1((A)=>{return pFQ(A)},"resolveDefaultRuntimeConfig"),xCA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(xCA||{}),aFQ="__smithy_context",vCA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(vCA||{}),bCA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(bCA||{})});
var hEA=E((bEA)=>{Object.defineProperty(bEA,"__esModule",{value:!0});bEA.toBase64=void 0;var vVQ=AD(),bVQ=cB(),fVQ=(A)=>{let B;if(typeof A==="string")B=bVQ.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return vVQ.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};bEA.toBase64=fVQ});
var hRA=E((bRA)=>{Object.defineProperty(bRA,"__esModule",{value:!0});bRA.getRuntimeConfig=void 0;var KOQ=Sh(),HOQ=KOQ.__importDefault(pe1()),yRA=ZI(),_RA=tQ1(),wU1=V4(),zOQ=jG(),xRA=v4(),gh=QD(),vRA=S3(),EOQ=kG(),UOQ=hZ(),wOQ=kRA(),$OQ=x4(),qOQ=yG(),NOQ=x4(),LOQ=(A)=>{NOQ.emitWarningIfUnsupportedVersion(process.version);let B=qOQ.resolveDefaultsModeConfig(A),Q=()=>B().then($OQ.loadConfigsForDefaultMode),D=wOQ.getRuntimeConfig(A);yRA.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??gh.loadConfig(yRA.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??EOQ.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??_RA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:HOQ.default.version}),maxAttempts:A?.maxAttempts??gh.loadConfig(xRA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??gh.loadConfig(wU1.NODE_REGION_CONFIG_OPTIONS,{...wU1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:vRA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??gh.loadConfig({...xRA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||UOQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??zOQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??vRA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??gh.loadConfig(wU1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??gh.loadConfig(wU1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??gh.loadConfig(_RA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};bRA.getRuntimeConfig=LOQ});
var hVA=E((bVA)=>{Object.defineProperty(bVA,"__esModule",{value:!0});bVA.getAwsChunkedEncodingStream=void 0;var RGQ=J1("stream"),OGQ=(A,B)=>{let{base64Encoder:Q,bodyLengthChecker:D,checksumAlgorithmFn:Z,checksumLocationName:G,streamHasher:F}=B,I=Q!==void 0&&Z!==void 0&&G!==void 0&&F!==void 0,Y=I?F(Z,A):void 0,W=new RGQ.Readable({read:()=>{}});return A.on("data",(J)=>{let X=D(J)||0;W.push(`${X.toString(16)}\r
`),W.push(J),W.push(`\r
`)}),A.on("end",async()=>{if(W.push(`0\r
`),I){let J=Q(await Y);W.push(`${G}:${J}\r
`),W.push(`\r
`)}W.push(null)}),W};bVA.getAwsChunkedEncodingStream=OGQ});
var hZ=E((vD5,WNA)=>{var{defineProperty:tE1,getOwnPropertyDescriptor:i$Q,getOwnPropertyNames:n$Q}=Object,a$Q=Object.prototype.hasOwnProperty,qw=(A,B)=>tE1(A,"name",{value:B,configurable:!0}),s$Q=(A,B)=>{for(var Q in B)tE1(A,Q,{get:B[Q],enumerable:!0})},r$Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of n$Q(B))if(!a$Q.call(A,Z)&&Z!==Q)tE1(A,Z,{get:()=>B[Z],enumerable:!(D=i$Q(B,Z))||D.enumerable})}return A},o$Q=(A)=>r$Q(tE1({},"__esModule",{value:!0}),A),QNA={};s$Q(QNA,{AdaptiveRetryStrategy:()=>DqQ,ConfiguredRetryStrategy:()=>ZqQ,DEFAULT_MAX_ATTEMPTS:()=>Ye1,DEFAULT_RETRY_DELAY_BASE:()=>rQ1,DEFAULT_RETRY_MODE:()=>t$Q,DefaultRateLimiter:()=>ZNA,INITIAL_RETRY_TOKENS:()=>We1,INVOCATION_ID_HEADER:()=>AqQ,MAXIMUM_RETRY_DELAY:()=>Je1,NO_RETRY_INCREMENT:()=>YNA,REQUEST_HEADER:()=>BqQ,RETRY_COST:()=>FNA,RETRY_MODES:()=>DNA,StandardRetryStrategy:()=>Xe1,THROTTLING_RETRY_DELAY_BASE:()=>GNA,TIMEOUT_RETRY_COST:()=>INA});WNA.exports=o$Q(QNA);var DNA=((A)=>{return A.STANDARD="standard",A.ADAPTIVE="adaptive",A})(DNA||{}),Ye1=3,t$Q="standard",e$Q=Ie1(),ZNA=class A{constructor(B){this.currentCapacity=0,this.enabled=!1,this.lastMaxRate=0,this.measuredTxRate=0,this.requestCount=0,this.lastTimestamp=0,this.timeWindow=0,this.beta=B?.beta??0.7,this.minCapacity=B?.minCapacity??1,this.minFillRate=B?.minFillRate??0.5,this.scaleConstant=B?.scaleConstant??0.4,this.smooth=B?.smooth??0.8;let Q=this.getCurrentTimeInSeconds();this.lastThrottleTime=Q,this.lastTxRateBucket=Math.floor(this.getCurrentTimeInSeconds()),this.fillRate=this.minFillRate,this.maxCapacity=this.minCapacity}static{qw(this,"DefaultRateLimiter")}static{this.setTimeoutFn=setTimeout}getCurrentTimeInSeconds(){return Date.now()/1000}async getSendToken(){return this.acquireTokenBucket(1)}async acquireTokenBucket(B){if(!this.enabled)return;if(this.refillTokenBucket(),B>this.currentCapacity){let Q=(B-this.currentCapacity)/this.fillRate*1000;await new Promise((D)=>A.setTimeoutFn(D,Q))}this.currentCapacity=this.currentCapacity-B}refillTokenBucket(){let B=this.getCurrentTimeInSeconds();if(!this.lastTimestamp){this.lastTimestamp=B;return}let Q=(B-this.lastTimestamp)*this.fillRate;this.currentCapacity=Math.min(this.maxCapacity,this.currentCapacity+Q),this.lastTimestamp=B}updateClientSendingRate(B){let Q;if(this.updateMeasuredRate(),e$Q.isThrottlingError(B)){let Z=!this.enabled?this.measuredTxRate:Math.min(this.measuredTxRate,this.fillRate);this.lastMaxRate=Z,this.calculateTimeWindow(),this.lastThrottleTime=this.getCurrentTimeInSeconds(),Q=this.cubicThrottle(Z),this.enableTokenBucket()}else this.calculateTimeWindow(),Q=this.cubicSuccess(this.getCurrentTimeInSeconds());let D=Math.min(Q,2*this.measuredTxRate);this.updateTokenBucketRate(D)}calculateTimeWindow(){this.timeWindow=this.getPrecise(Math.pow(this.lastMaxRate*(1-this.beta)/this.scaleConstant,0.3333333333333333))}cubicThrottle(B){return this.getPrecise(B*this.beta)}cubicSuccess(B){return this.getPrecise(this.scaleConstant*Math.pow(B-this.lastThrottleTime-this.timeWindow,3)+this.lastMaxRate)}enableTokenBucket(){this.enabled=!0}updateTokenBucketRate(B){this.refillTokenBucket(),this.fillRate=Math.max(B,this.minFillRate),this.maxCapacity=Math.max(B,this.minCapacity),this.currentCapacity=Math.min(this.currentCapacity,this.maxCapacity)}updateMeasuredRate(){let B=this.getCurrentTimeInSeconds(),Q=Math.floor(B*2)/2;if(this.requestCount++,Q>this.lastTxRateBucket){let D=this.requestCount/(Q-this.lastTxRateBucket);this.measuredTxRate=this.getPrecise(D*this.smooth+this.measuredTxRate*(1-this.smooth)),this.requestCount=0,this.lastTxRateBucket=Q}}getPrecise(B){return parseFloat(B.toFixed(8))}},rQ1=100,Je1=20000,GNA=500,We1=500,FNA=5,INA=10,YNA=1,AqQ="amz-sdk-invocation-id",BqQ="amz-sdk-request",QqQ=qw(()=>{let A=rQ1;return{computeNextBackoffDelay:qw((D)=>{return Math.floor(Math.min(Je1,Math.random()*2**D*A))},"computeNextBackoffDelay"),setDelayBase:qw((D)=>{A=D},"setDelayBase")}},"getDefaultRetryBackoffStrategy"),BNA=qw(({retryDelay:A,retryCount:B,retryCost:Q})=>{return{getRetryCount:qw(()=>B,"getRetryCount"),getRetryDelay:qw(()=>Math.min(Je1,A),"getRetryDelay"),getRetryCost:qw(()=>Q,"getRetryCost")}},"createDefaultRetryToken"),Xe1=class{constructor(A){this.maxAttempts=A,this.mode="standard",this.capacity=We1,this.retryBackoffStrategy=QqQ(),this.maxAttemptsProvider=typeof A==="function"?A:async()=>A}static{qw(this,"StandardRetryStrategy")}async acquireInitialRetryToken(A){return BNA({retryDelay:rQ1,retryCount:0})}async refreshRetryTokenForRetry(A,B){let Q=await this.getMaxAttempts();if(this.shouldRetry(A,B,Q)){let D=B.errorType;this.retryBackoffStrategy.setDelayBase(D==="THROTTLING"?GNA:rQ1);let Z=this.retryBackoffStrategy.computeNextBackoffDelay(A.getRetryCount()),G=B.retryAfterHint?Math.max(B.retryAfterHint.getTime()-Date.now()||0,Z):Z,F=this.getCapacityCost(D);return this.capacity-=F,BNA({retryDelay:G,retryCount:A.getRetryCount()+1,retryCost:F})}throw new Error("No retry token available")}recordSuccess(A){this.capacity=Math.max(We1,this.capacity+(A.getRetryCost()??YNA))}getCapacity(){return this.capacity}async getMaxAttempts(){try{return await this.maxAttemptsProvider()}catch(A){return console.warn(`Max attempts provider could not resolve. Using default of ${Ye1}`),Ye1}}shouldRetry(A,B,Q){return A.getRetryCount()+1<Q&&this.capacity>=this.getCapacityCost(B.errorType)&&this.isRetryableError(B.errorType)}getCapacityCost(A){return A==="TRANSIENT"?INA:FNA}isRetryableError(A){return A==="THROTTLING"||A==="TRANSIENT"}},DqQ=class{constructor(A,B){this.maxAttemptsProvider=A,this.mode="adaptive";let{rateLimiter:Q}=B??{};this.rateLimiter=Q??new ZNA,this.standardRetryStrategy=new Xe1(A)}static{qw(this,"AdaptiveRetryStrategy")}async acquireInitialRetryToken(A){return await this.rateLimiter.getSendToken(),this.standardRetryStrategy.acquireInitialRetryToken(A)}async refreshRetryTokenForRetry(A,B){return this.rateLimiter.updateClientSendingRate(B),this.standardRetryStrategy.refreshRetryTokenForRetry(A,B)}recordSuccess(A){this.rateLimiter.updateClientSendingRate({}),this.standardRetryStrategy.recordSuccess(A)}},ZqQ=class extends Xe1{static{qw(this,"ConfiguredRetryStrategy")}constructor(A,B=rQ1){super(typeof A==="function"?A:async()=>A);if(typeof B==="number")this.computeNextBackoffDelay=()=>B;else this.computeNextBackoffDelay=B}async refreshRetryTokenForRetry(A,B){let Q=await super.refreshRetryTokenForRetry(A,B);return Q.getRetryDelay=()=>this.computeNextBackoffDelay(Q.getRetryCount()),Q}}});
var hzA=E((a35,fzA)=>{var{defineProperty:YE1,getOwnPropertyDescriptor:NXQ,getOwnPropertyNames:LXQ}=Object,MXQ=Object.prototype.hasOwnProperty,RXQ=(A,B)=>YE1(A,"name",{value:B,configurable:!0}),OXQ=(A,B)=>{for(var Q in B)YE1(A,Q,{get:B[Q],enumerable:!0})},TXQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of LXQ(B))if(!MXQ.call(A,Z)&&Z!==Q)YE1(A,Z,{get:()=>B[Z],enumerable:!(D=NXQ(B,Z))||D.enumerable})}return A},PXQ=(A)=>TXQ(YE1({},"__esModule",{value:!0}),A),bzA={};OXQ(bzA,{isArrayBuffer:()=>SXQ});fzA.exports=PXQ(bzA);var SXQ=RXQ((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var it1=E((J$A)=>{Object.defineProperty(J$A,"__esModule",{value:!0});J$A.slurpFile=void 0;var PEQ=J1("fs"),{readFile:SEQ}=PEQ.promises,pt1={},jEQ=(A,B)=>{if(!pt1[A]||(B===null||B===void 0?void 0:B.ignoreCache))pt1[A]=SEQ(A,"utf8");return pt1[A]};J$A.slurpFile=jEQ});
var jG=E((YZ5,fLA)=>{var{defineProperty:YU1,getOwnPropertyDescriptor:tLQ,getOwnPropertyNames:eLQ}=Object,AMQ=Object.prototype.hasOwnProperty,vLA=(A,B)=>YU1(A,"name",{value:B,configurable:!0}),BMQ=(A,B)=>{for(var Q in B)YU1(A,Q,{get:B[Q],enumerable:!0})},QMQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of eLQ(B))if(!AMQ.call(A,Z)&&Z!==Q)YU1(A,Z,{get:()=>B[Z],enumerable:!(D=tLQ(B,Z))||D.enumerable})}return A},DMQ=(A)=>QMQ(YU1({},"__esModule",{value:!0}),A),bLA={};BMQ(bLA,{Hash:()=>FMQ});fLA.exports=DMQ(bLA);var ve1=AD(),ZMQ=cB(),GMQ=J1("buffer"),xLA=J1("crypto"),FMQ=class{static{vLA(this,"Hash")}constructor(A,B){this.algorithmIdentifier=A,this.secret=B,this.reset()}update(A,B){this.hash.update(ZMQ.toUint8Array(be1(A,B)))}digest(){return Promise.resolve(this.hash.digest())}reset(){this.hash=this.secret?xLA.createHmac(this.algorithmIdentifier,be1(this.secret)):xLA.createHash(this.algorithmIdentifier)}};function be1(A,B){if(GMQ.Buffer.isBuffer(A))return A;if(typeof A==="string")return ve1.fromString(A,B);if(ArrayBuffer.isView(A))return ve1.fromArrayBuffer(A.buffer,A.byteOffset,A.byteLength);return ve1.fromArrayBuffer(A)}vLA(be1,"castSourceData")});
var jQ=E((C35,hKA)=>{var{defineProperty:Oo1,getOwnPropertyDescriptor:DYQ,getOwnPropertyNames:ZYQ}=Object,GYQ=Object.prototype.hasOwnProperty,FYQ=(A,B)=>{for(var Q in B)Oo1(A,Q,{get:B[Q],enumerable:!0})},IYQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ZYQ(B))if(!GYQ.call(A,Z)&&Z!==Q)Oo1(A,Z,{get:()=>B[Z],enumerable:!(D=DYQ(B,Z))||D.enumerable})}return A},YYQ=(A)=>IYQ(Oo1({},"__esModule",{value:!0}),A),_KA={};FYQ(_KA,{ErrorSchema:()=>fKA,ListSchema:()=>To1,MapSchema:()=>Po1,NormalizedSchema:()=>qYQ,OperationSchema:()=>bKA,SCHEMA:()=>vZ,Schema:()=>Dn,SimpleSchema:()=>So1,StructureSchema:()=>mz1,TypeRegistry:()=>Th,deref:()=>OQ1,deserializerMiddlewareOption:()=>xKA,error:()=>wYQ,getSchemaSerdePlugin:()=>KYQ,list:()=>HYQ,map:()=>zYQ,op:()=>EYQ,serializerMiddlewareOption:()=>vKA,sim:()=>$YQ,struct:()=>UYQ});hKA.exports=YYQ(_KA);var OQ1=(A)=>{if(typeof A==="function")return A();return A},WYQ=Oh(),JYQ=I5(),XYQ=(A)=>(B,Q)=>async(D)=>{let{response:Z}=await B(D),{operationSchema:G}=JYQ.getSmithyContext(Q);try{let F=await A.protocol.deserializeResponse(G,{...A,...Q},Z);return{response:Z,output:F}}catch(F){if(Object.defineProperty(F,"$response",{value:Z}),!("$metadata"in F)){try{F.message+=`
  Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.`}catch(Y){if(!Q.logger||Q.logger?.constructor?.name==="NoOpLogger")console.warn("Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.");else Q.logger?.warn?.("Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.")}if(typeof F.$responseBodyText!=="undefined"){if(F.$response)F.$response.body=F.$responseBodyText}try{if(WYQ.HttpResponse.isInstance(Z)){let{headers:Y={}}=Z,W=Object.entries(Y);F.$metadata={httpStatusCode:Z.statusCode,requestId:Ro1(/^x-[\w-]+-request-?id$/,W),extendedRequestId:Ro1(/^x-[\w-]+-id-2$/,W),cfId:Ro1(/^x-[\w-]+-cf-id$/,W)}}}catch(Y){}}throw F}},Ro1=(A,B)=>{return(B.find(([Q])=>{return Q.match(A)})||[void 0,void 0])[1]},VYQ=I5(),CYQ=(A)=>(B,Q)=>async(D)=>{let{operationSchema:Z}=VYQ.getSmithyContext(Q),G=Q.endpointV2?.url&&A.urlParser?async()=>A.urlParser(Q.endpointV2.url):A.endpoint,F=await A.protocol.serializeRequest(Z,D.input,{...A,...Q,endpoint:G});return B({...D,request:F})},xKA={name:"deserializerMiddleware",step:"deserialize",tags:["DESERIALIZER"],override:!0},vKA={name:"serializerMiddleware",step:"serialize",tags:["SERIALIZER"],override:!0};function KYQ(A){return{applyToStack:(B)=>{B.add(CYQ(A),vKA),B.add(XYQ(A),xKA),A.protocol.setSerdeContext(A)}}}var Th=class A{constructor(B,Q=new Map){this.namespace=B,this.schemas=Q}static{this.registries=new Map}static for(B){if(!A.registries.has(B))A.registries.set(B,new A(B));return A.registries.get(B)}register(B,Q){let D=this.normalizeShapeId(B);A.for(this.getNamespace(B)).schemas.set(D,Q)}getSchema(B){let Q=this.normalizeShapeId(B);if(!this.schemas.has(Q))throw new Error(`@smithy/core/schema - schema not found for ${Q}`);return this.schemas.get(Q)}getBaseException(){for(let[B,Q]of this.schemas.entries())if(B.startsWith("smithy.ts.sdk.synthetic.")&&B.endsWith("ServiceException"))return Q;return}find(B){return[...this.schemas.values()].find(B)}destroy(){A.registries.delete(this.namespace),this.schemas.clear()}normalizeShapeId(B){if(B.includes("#"))return B;return this.namespace+"#"+B}getNamespace(B){return this.normalizeShapeId(B).split("#")[0]}},Dn=class{constructor(A,B){this.name=A,this.traits=B}},To1=class extends Dn{constructor(A,B,Q){super(A,B);this.name=A,this.traits=B,this.valueSchema=Q}};function HYQ(A,B,Q={},D){let Z=new To1(A+"#"+B,Q,typeof D==="function"?D():D);return Th.for(A).register(B,Z),Z}var Po1=class extends Dn{constructor(A,B,Q,D){super(A,B);this.name=A,this.traits=B,this.keySchema=Q,this.valueSchema=D}};function zYQ(A,B,Q={},D,Z){let G=new Po1(A+"#"+B,Q,D,typeof Z==="function"?Z():Z);return Th.for(A).register(B,G),G}var bKA=class extends Dn{constructor(A,B,Q,D){super(A,B);this.name=A,this.traits=B,this.input=Q,this.output=D}};function EYQ(A,B,Q={},D,Z){let G=new bKA(A+"#"+B,Q,D,Z);return Th.for(A).register(B,G),G}var mz1=class extends Dn{constructor(A,B,Q,D){super(A,B);this.name=A,this.traits=B,this.memberNames=Q,this.memberList=D,this.members={};for(let Z=0;Z<Q.length;++Z)this.members[Q[Z]]=Array.isArray(D[Z])?D[Z]:[D[Z],0]}};function UYQ(A,B,Q,D,Z){let G=new mz1(A+"#"+B,Q,D,Z);return Th.for(A).register(B,G),G}var fKA=class extends mz1{constructor(A,B,Q,D,Z){super(A,B,Q,D);this.name=A,this.traits=B,this.memberNames=Q,this.memberList=D,this.ctor=Z}};function wYQ(A,B,Q={},D,Z,G){let F=new fKA(A+"#"+B,Q,D,Z,G);return Th.for(A).register(B,F),F}var vZ={BLOB:21,STREAMING_BLOB:42,BOOLEAN:2,STRING:0,NUMERIC:1,BIG_INTEGER:17,BIG_DECIMAL:19,DOCUMENT:15,TIMESTAMP_DEFAULT:4,TIMESTAMP_DATE_TIME:5,TIMESTAMP_HTTP_DATE:6,TIMESTAMP_EPOCH_SECONDS:7,LIST_MODIFIER:64,MAP_MODIFIER:128},So1=class extends Dn{constructor(A,B,Q){super(A,Q);this.name=A,this.schemaRef=B,this.traits=Q}};function $YQ(A,B,Q,D){let Z=new So1(A+"#"+B,Q,D);return Th.for(A).register(B,Z),Z}var qYQ=class A{constructor(B,Q){this.ref=B,this.memberName=Q;let D=[],Z=B,G=B;this._isMemberSchema=!1;while(Array.isArray(Z))D.push(Z[1]),Z=Z[0],G=OQ1(Z),this._isMemberSchema=!0;if(D.length>0){this.memberTraits={};for(let F=D.length-1;F>=0;--F){let I=D[F];Object.assign(this.memberTraits,A.translateTraits(I))}}else this.memberTraits=0;if(G instanceof A){this.name=G.name,this.traits=G.traits,this._isMemberSchema=G._isMemberSchema,this.schema=G.schema,this.memberTraits=Object.assign({},G.getMemberTraits(),this.getMemberTraits()),this.normalizedTraits=void 0,this.ref=G.ref,this.memberName=Q??G.memberName;return}if(this.schema=OQ1(G),this.schema&&typeof this.schema==="object")this.traits=this.schema?.traits??{};else this.traits=0;if(this.name=(typeof this.schema==="object"?this.schema?.name:void 0)??this.memberName??this.getSchemaName(),this._isMemberSchema&&!Q)throw new Error(`@smithy/core/schema - NormalizedSchema member schema ${this.getName(!0)} must initialize with memberName argument.`)}static of(B,Q){if(B instanceof A)return B;return new A(B,Q)}static translateTraits(B){if(typeof B==="object")return B;B=B|0;let Q={};if((B&1)===1)Q.httpLabel=1;if((B>>1&1)===1)Q.idempotent=1;if((B>>2&1)===1)Q.idempotencyToken=1;if((B>>3&1)===1)Q.sensitive=1;if((B>>4&1)===1)Q.httpPayload=1;if((B>>5&1)===1)Q.httpResponseCode=1;if((B>>6&1)===1)Q.httpQueryParams=1;return Q}static memberFrom(B,Q){if(B instanceof A)return B.memberName=Q,B._isMemberSchema=!0,B;return new A(B,Q)}getSchema(){if(this.schema instanceof A)return this.schema=this.schema.getSchema();if(this.schema instanceof So1)return OQ1(this.schema.schemaRef);return OQ1(this.schema)}getName(B=!1){if(!B){if(this.name&&this.name.includes("#"))return this.name.split("#")[1]}return this.name||void 0}getMemberName(){if(!this.isMemberSchema())throw new Error(`@smithy/core/schema - cannot get member name on non-member schema: ${this.getName(!0)}`);return this.memberName}isMemberSchema(){return this._isMemberSchema}isUnitSchema(){return this.getSchema()==="unit"}isListSchema(){let B=this.getSchema();if(typeof B==="number")return B>=vZ.LIST_MODIFIER&&B<vZ.MAP_MODIFIER;return B instanceof To1}isMapSchema(){let B=this.getSchema();if(typeof B==="number")return B>=vZ.MAP_MODIFIER&&B<=255;return B instanceof Po1}isDocumentSchema(){return this.getSchema()===vZ.DOCUMENT}isStructSchema(){let B=this.getSchema();return B!==null&&typeof B==="object"&&"members"in B||B instanceof mz1}isBlobSchema(){return this.getSchema()===vZ.BLOB||this.getSchema()===vZ.STREAMING_BLOB}isTimestampSchema(){let B=this.getSchema();return typeof B==="number"&&B>=vZ.TIMESTAMP_DEFAULT&&B<=vZ.TIMESTAMP_EPOCH_SECONDS}isStringSchema(){return this.getSchema()===vZ.STRING}isBooleanSchema(){return this.getSchema()===vZ.BOOLEAN}isNumericSchema(){return this.getSchema()===vZ.NUMERIC}isBigIntegerSchema(){return this.getSchema()===vZ.BIG_INTEGER}isBigDecimalSchema(){return this.getSchema()===vZ.BIG_DECIMAL}isStreaming(){if(!!this.getMergedTraits().streaming)return!0;return this.getSchema()===vZ.STREAMING_BLOB}getMergedTraits(){if(this.normalizedTraits)return this.normalizedTraits;return this.normalizedTraits={...this.getOwnTraits(),...this.getMemberTraits()},this.normalizedTraits}getMemberTraits(){return A.translateTraits(this.memberTraits)}getOwnTraits(){return A.translateTraits(this.traits)}getKeySchema(){if(this.isDocumentSchema())return A.memberFrom([vZ.DOCUMENT,0],"key");if(!this.isMapSchema())throw new Error(`@smithy/core/schema - cannot get key schema for non-map schema: ${this.getName(!0)}`);let B=this.getSchema();if(typeof B==="number")return A.memberFrom([63&B,0],"key");return A.memberFrom([B.keySchema,0],"key")}getValueSchema(){let B=this.getSchema();if(typeof B==="number"){if(this.isMapSchema())return A.memberFrom([63&B,0],"value");else if(this.isListSchema())return A.memberFrom([63&B,0],"member")}if(B&&typeof B==="object"){if(this.isStructSchema())throw new Error(`cannot call getValueSchema() with StructureSchema ${this.getName(!0)}`);let Q=B;if("valueSchema"in Q){if(this.isMapSchema())return A.memberFrom([Q.valueSchema,0],"value");else if(this.isListSchema())return A.memberFrom([Q.valueSchema,0],"member")}}if(this.isDocumentSchema())return A.memberFrom([vZ.DOCUMENT,0],"value");throw new Error(`@smithy/core/schema - the schema ${this.getName(!0)} does not have a value member.`)}getMemberSchema(B){if(this.isStructSchema()){let Q=this.getSchema();if(!(B in Q.members))throw new Error(`@smithy/core/schema - the schema ${this.getName(!0)} does not have a member with name=${B}.`);return A.memberFrom(Q.members[B],B)}if(this.isDocumentSchema())return A.memberFrom([vZ.DOCUMENT,0],B);throw new Error(`@smithy/core/schema - the schema ${this.getName(!0)} does not have members.`)}getMemberSchemas(){let{schema:B}=this,Q=B;if(!Q||typeof Q!=="object")return{};if("members"in Q){let D={};for(let Z of Q.memberNames)D[Z]=this.getMemberSchema(Z);return D}return{}}*structIterator(){if(this.isUnitSchema())return;if(!this.isStructSchema())throw new Error("@smithy/core/schema - cannot acquire structIterator on non-struct schema.");let B=this.getSchema();for(let Q=0;Q<B.memberNames.length;++Q)yield[B.memberNames[Q],A.memberFrom([B.memberList[Q],0],B.memberNames[Q])]}getSchemaName(){let B=this.getSchema();if(typeof B==="number"){let Q=63&B,D=192&B,Z=Object.entries(vZ).find(([,G])=>{return G===Q})?.[0]??"Unknown";switch(D){case vZ.MAP_MODIFIER:return`${Z}Map`;case vZ.LIST_MODIFIER:return`${Z}List`;case 0:return Z}}return"Unknown"}}});
var jSA=E((PSA)=>{Object.defineProperty(PSA,"__esModule",{value:!0});PSA.resolveRuntimeExtensions=void 0;var MSA=B41(),RSA=mJ(),OSA=x4(),TSA=LSA(),QkQ=(A,B)=>{let Q=Object.assign(MSA.getAwsRegionExtensionConfiguration(A),OSA.getDefaultExtensionConfiguration(A),RSA.getHttpHandlerExtensionConfiguration(A),TSA.getHttpAuthExtensionConfiguration(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,MSA.resolveAwsRegionExtensionConfiguration(Q),OSA.resolveDefaultRuntimeConfig(Q),RSA.resolveHttpHandlerRuntimeConfig(Q),TSA.resolveHttpAuthRuntimeConfig(Q))};PSA.resolveRuntimeExtensions=QkQ});
var kG=E((JZ5,uLA)=>{var{defineProperty:WU1,getOwnPropertyDescriptor:IMQ,getOwnPropertyNames:YMQ}=Object,WMQ=Object.prototype.hasOwnProperty,JMQ=(A,B)=>WU1(A,"name",{value:B,configurable:!0}),XMQ=(A,B)=>{for(var Q in B)WU1(A,Q,{get:B[Q],enumerable:!0})},VMQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of YMQ(B))if(!WMQ.call(A,Z)&&Z!==Q)WU1(A,Z,{get:()=>B[Z],enumerable:!(D=IMQ(B,Z))||D.enumerable})}return A},CMQ=(A)=>VMQ(WU1({},"__esModule",{value:!0}),A),gLA={};XMQ(gLA,{calculateBodyLength:()=>KMQ});uLA.exports=CMQ(gLA);var hLA=J1("fs"),KMQ=JMQ((A)=>{if(!A)return 0;if(typeof A==="string")return Buffer.byteLength(A);else if(typeof A.byteLength==="number")return A.byteLength;else if(typeof A.size==="number")return A.size;else if(typeof A.start==="number"&&typeof A.end==="number")return A.end+1-A.start;else if(typeof A.path==="string"||Buffer.isBuffer(A.path))return hLA.lstatSync(A.path).size;else if(typeof A.fd==="number")return hLA.fstatSync(A.fd).size;throw new Error(`Body Length computation failed for ${A}`)},"calculateBodyLength")});
var kRA=E((SRA)=>{Object.defineProperty(SRA,"__esModule",{value:!0});SRA.getRuntimeConfig=void 0;var IOQ=ZI(),YOQ=VB(),WOQ=x4(),JOQ=BZ(),TRA=Yy(),PRA=cB(),XOQ=le1(),VOQ=ORA(),COQ=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??TRA.fromBase64,base64Encoder:A?.base64Encoder??TRA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??VOQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??XOQ.defaultSSOOIDCHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new IOQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new YOQ.NoAuthSigner}],logger:A?.logger??new WOQ.NoOpLogger,serviceId:A?.serviceId??"SSO OIDC",urlParser:A?.urlParser??JOQ.parseUrl,utf8Decoder:A?.utf8Decoder??PRA.fromUtf8,utf8Encoder:A?.utf8Encoder??PRA.toUtf8}};SRA.getRuntimeConfig=COQ});
var ke1=E((je1)=>{Object.defineProperty(je1,"__esModule",{value:!0});je1.fromHttp=void 0;var kLQ=LLA();Object.defineProperty(je1,"fromHttp",{enumerable:!0,get:function(){return kLQ.fromHttp}})});
var l10=E((pN)=>{var uSQ=pN&&pN.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),mSQ=pN&&pN.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),dSQ=pN&&pN.__importStar||function(){var A=function(B){return A=Object.getOwnPropertyNames||function(Q){var D=[];for(var Z in Q)if(Object.prototype.hasOwnProperty.call(Q,Z))D[D.length]=Z;return D},A(B)};return function(B){if(B&&B.__esModule)return B;var Q={};if(B!=null){for(var D=A(B),Z=0;Z<D.length;Z++)if(D[Z]!=="default")uSQ(Q,B,D[Z])}return mSQ(Q,B),Q}}();Object.defineProperty(pN,"__esModule",{value:!0});pN.fromWebToken=void 0;var cSQ=(A)=>async(B)=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromWebToken");let{roleArn:Q,roleSessionName:D,webIdentityToken:Z,providerId:G,policyArns:F,policy:I,durationSeconds:Y}=A,{roleAssumerWithWebIdentity:W}=A;if(!W){let{getDefaultRoleAssumerWithWebIdentity:J}=await Promise.resolve().then(()=>dSQ(u10()));W=J({...A.clientConfig,credentialProviderLogger:A.logger,parentClientConfig:{...B?.callerClientConfig,...A.parentClientConfig}},A.clientPlugins)}return W({RoleArn:Q,RoleSessionName:D??`aws-sdk-js-session-${Date.now()}`,WebIdentityToken:Z,ProviderId:G,PolicyArns:F,Policy:I,DurationSeconds:Y})};pN.fromWebToken=cSQ});
var lCA=E((A35,cCA)=>{var{defineProperty:xz1,getOwnPropertyDescriptor:sFQ,getOwnPropertyNames:rFQ}=Object,oFQ=Object.prototype.hasOwnProperty,Dy=(A,B)=>xz1(A,"name",{value:B,configurable:!0}),tFQ=(A,B)=>{for(var Q in B)xz1(A,Q,{get:B[Q],enumerable:!0})},eFQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of rFQ(B))if(!oFQ.call(A,Z)&&Z!==Q)xz1(A,Z,{get:()=>B[Z],enumerable:!(D=sFQ(B,Z))||D.enumerable})}return A},AIQ=(A)=>eFQ(xz1({},"__esModule",{value:!0}),A),gCA={};tFQ(gCA,{Field:()=>DIQ,Fields:()=>ZIQ,HttpRequest:()=>GIQ,HttpResponse:()=>FIQ,IHttpRequest:()=>uCA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>BIQ,isValidHostname:()=>dCA,resolveHttpHandlerRuntimeConfig:()=>QIQ});cCA.exports=AIQ(gCA);var BIQ=Dy((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),QIQ=Dy((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),uCA=hCA(),DIQ=class{static{Dy(this,"Field")}constructor({name:A,kind:B=uCA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},ZIQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{Dy(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},GIQ=class A{static{Dy(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=mCA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function mCA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}Dy(mCA,"cloneQuery");var FIQ=class{static{Dy(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function dCA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}Dy(dCA,"isValidHostname")});
var lVA=E((dVA)=>{Object.defineProperty(dVA,"__esModule",{value:!0});dVA.headStream=void 0;var SGQ=J1("stream"),jGQ=uVA(),kGQ=By(),yGQ=(A,B)=>{if(kGQ.isReadableStream(A))return jGQ.headStream(A,B);return new Promise((Q,D)=>{let Z=new mVA;Z.limit=B,A.pipe(Z),A.on("error",(G)=>{Z.end(),D(G)}),Z.on("error",D),Z.on("finish",function(){let G=new Uint8Array(Buffer.concat(this.buffers));Q(G)})})};dVA.headStream=yGQ;class mVA extends SGQ.Writable{constructor(){super(...arguments);this.buffers=[],this.limit=1/0,this.bytesBuffered=0}_write(A,B,Q){var D;if(this.buffers.push(A),this.bytesBuffered+=(D=A.byteLength)!==null&&D!==void 0?D:0,this.bytesBuffered>=this.limit){let Z=this.bytesBuffered-this.limit,G=this.buffers[this.buffers.length-1];this.buffers[this.buffers.length-1]=G.subarray(0,G.byteLength-Z),this.emit("finish")}Q()}}});
var le1=E((GRA)=>{Object.defineProperty(GRA,"__esModule",{value:!0});GRA.resolveHttpAuthSchemeConfig=GRA.defaultSSOOIDCHttpAuthSchemeProvider=GRA.defaultSSOOIDCHttpAuthSchemeParametersProvider=void 0;var nRQ=ZI(),ce1=I5(),aRQ=async(A,B,Q)=>{return{operation:ce1.getSmithyContext(B).operation,region:await ce1.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};GRA.defaultSSOOIDCHttpAuthSchemeParametersProvider=aRQ;function sRQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sso-oauth",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function rRQ(A){return{schemeId:"smithy.api#noAuth"}}var oRQ=(A)=>{let B=[];switch(A.operation){case"CreateToken":{B.push(rRQ(A));break}default:B.push(sRQ(A))}return B};GRA.defaultSSOOIDCHttpAuthSchemeProvider=oRQ;var tRQ=(A)=>{let B=nRQ.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:ce1.normalizeProvider(A.authSchemePreference??[])})};GRA.resolveHttpAuthSchemeConfig=tRQ});
var lqA=E((dqA)=>{Object.defineProperty(dqA,"__esModule",{value:!0});dqA.default=void 0;var V$Q=mqA(Ge1()),C$Q=mqA(uqA());function mqA(A){return A&&A.__esModule?A:{default:A}}var K$Q=V$Q.default("v5",80,C$Q.default),H$Q=K$Q;dqA.default=H$Q});
var lt1=E((swA)=>{Object.defineProperty(swA,"__esModule",{value:!0});swA.getSSOTokenFilepath=void 0;var WEQ=J1("crypto"),JEQ=J1("path"),XEQ=En(),VEQ=(A)=>{let Q=WEQ.createHash("sha1").update(A).digest("hex");return JEQ.join(XEQ.getHomeDir(),".aws","sso","cache",`${Q}.json`)};swA.getSSOTokenFilepath=VEQ});
var mJ=E((G55,vJA)=>{var{defineProperty:Gz1,getOwnPropertyDescriptor:U7Q,getOwnPropertyNames:w7Q}=Object,$7Q=Object.prototype.hasOwnProperty,tk=(A,B)=>Gz1(A,"name",{value:B,configurable:!0}),q7Q=(A,B)=>{for(var Q in B)Gz1(A,Q,{get:B[Q],enumerable:!0})},N7Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of w7Q(B))if(!$7Q.call(A,Z)&&Z!==Q)Gz1(A,Z,{get:()=>B[Z],enumerable:!(D=U7Q(B,Z))||D.enumerable})}return A},L7Q=(A)=>N7Q(Gz1({},"__esModule",{value:!0}),A),kJA={};q7Q(kJA,{Field:()=>O7Q,Fields:()=>T7Q,HttpRequest:()=>P7Q,HttpResponse:()=>S7Q,IHttpRequest:()=>yJA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>M7Q,isValidHostname:()=>xJA,resolveHttpHandlerRuntimeConfig:()=>R7Q});vJA.exports=L7Q(kJA);var M7Q=tk((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),R7Q=tk((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),yJA=Bo1(),O7Q=class{static{tk(this,"Field")}constructor({name:A,kind:B=yJA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},T7Q=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{tk(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},P7Q=class A{static{tk(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=_JA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function _JA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}tk(_JA,"cloneQuery");var S7Q=class{static{tk(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function xJA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}tk(xJA,"isValidHostname")});
var mo1=E((R35,lz1)=>{var{defineProperty:eKA,getOwnPropertyDescriptor:wWQ,getOwnPropertyNames:$WQ}=Object,qWQ=Object.prototype.hasOwnProperty,go1=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of $WQ(B))if(!qWQ.call(A,Z)&&Z!==Q)eKA(A,Z,{get:()=>B[Z],enumerable:!(D=wWQ(B,Z))||D.enumerable})}return A},AHA=(A,B,Q)=>(go1(A,B,"default"),Q&&go1(Q,B,"default")),NWQ=(A)=>go1(eKA({},"__esModule",{value:!0}),A),uo1={};lz1.exports=NWQ(uo1);AHA(uo1,sKA(),lz1.exports);AHA(uo1,tKA(),lz1.exports)});
var mwA=E((r75,uwA)=>{var{defineProperty:xE1,getOwnPropertyDescriptor:gzQ,getOwnPropertyNames:uzQ}=Object,mzQ=Object.prototype.hasOwnProperty,Cy=(A,B)=>xE1(A,"name",{value:B,configurable:!0}),dzQ=(A,B)=>{for(var Q in B)xE1(A,Q,{get:B[Q],enumerable:!0})},czQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of uzQ(B))if(!mzQ.call(A,Z)&&Z!==Q)xE1(A,Z,{get:()=>B[Z],enumerable:!(D=gzQ(B,Z))||D.enumerable})}return A},lzQ=(A)=>czQ(xE1({},"__esModule",{value:!0}),A),bwA={};dzQ(bwA,{Field:()=>nzQ,Fields:()=>azQ,HttpRequest:()=>szQ,HttpResponse:()=>rzQ,IHttpRequest:()=>fwA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>pzQ,isValidHostname:()=>gwA,resolveHttpHandlerRuntimeConfig:()=>izQ});uwA.exports=lzQ(bwA);var pzQ=Cy((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),izQ=Cy((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),fwA=vwA(),nzQ=class{static{Cy(this,"Field")}constructor({name:A,kind:B=fwA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},azQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{Cy(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},szQ=class A{static{Cy(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=hwA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function hwA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}Cy(hwA,"cloneQuery");var rzQ=class{static{Cy(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function gwA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}Cy(gwA,"isValidHostname")});
var n10=E((eZ5,TU1)=>{var{defineProperty:UPA,getOwnPropertyDescriptor:oSQ,getOwnPropertyNames:tSQ}=Object,eSQ=Object.prototype.hasOwnProperty,p10=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of tSQ(B))if(!eSQ.call(A,Z)&&Z!==Q)UPA(A,Z,{get:()=>B[Z],enumerable:!(D=oSQ(B,Z))||D.enumerable})}return A},wPA=(A,B,Q)=>(p10(A,B,"default"),Q&&p10(Q,B,"default")),AjQ=(A)=>p10(UPA({},"__esModule",{value:!0}),A),i10={};TU1.exports=AjQ(i10);wPA(i10,EPA(),TU1.exports);wPA(i10,l10(),TU1.exports)});
var nQ1=E((YqA)=>{Object.defineProperty(YqA,"__esModule",{value:!0});YqA.default=void 0;var NwQ=LwQ(IqA());function LwQ(A){return A&&A.__esModule?A:{default:A}}function MwQ(A){return typeof A==="string"&&NwQ.default.test(A)}var RwQ=MwQ;YqA.default=RwQ});
var nqA=E((pqA)=>{Object.defineProperty(pqA,"__esModule",{value:!0});pqA.default=void 0;var z$Q="00000000-0000-0000-0000-000000000000";pqA.default=z$Q});
var oQ1=E((D00)=>{Object.defineProperty(D00,"__esModule",{value:!0});D00.STSClient=D00.__Client=void 0;var kSA=NQ1(),DkQ=LQ1(),ZkQ=MQ1(),ySA=zn(),GkQ=V4(),Q00=VB(),FkQ=TG(),IkQ=q6(),_SA=v4(),vSA=x4();Object.defineProperty(D00,"__Client",{enumerable:!0,get:function(){return vSA.Client}});var xSA=Ne1(),YkQ=Fz(),WkQ=$SA(),JkQ=jSA();class bSA extends vSA.Client{config;constructor(...[A]){let B=WkQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=YkQ.resolveClientEndpointParameters(B),D=ySA.resolveUserAgentConfig(Q),Z=_SA.resolveRetryConfig(D),G=GkQ.resolveRegionConfig(Z),F=kSA.resolveHostHeaderConfig(G),I=IkQ.resolveEndpointConfig(F),Y=xSA.resolveHttpAuthSchemeConfig(I),W=JkQ.resolveRuntimeExtensions(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(ySA.getUserAgentPlugin(this.config)),this.middlewareStack.use(_SA.getRetryPlugin(this.config)),this.middlewareStack.use(FkQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(kSA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(DkQ.getLoggerPlugin(this.config)),this.middlewareStack.use(ZkQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(Q00.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:xSA.defaultSTSHttpAuthSchemeParametersProvider,identityProviderConfigProvider:async(J)=>new Q00.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials})})),this.middlewareStack.use(Q00.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}}D00.STSClient=bSA});
var oUA=E((P75,rUA)=>{var{defineProperty:OE1,getOwnPropertyDescriptor:eKQ,getOwnPropertyNames:AHQ}=Object,BHQ=Object.prototype.hasOwnProperty,s6=(A,B)=>OE1(A,"name",{value:B,configurable:!0}),QHQ=(A,B)=>{for(var Q in B)OE1(A,Q,{get:B[Q],enumerable:!0})},DHQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of AHQ(B))if(!BHQ.call(A,Z)&&Z!==Q)OE1(A,Z,{get:()=>B[Z],enumerable:!(D=eKQ(B,Z))||D.enumerable})}return A},ZHQ=(A)=>DHQ(OE1({},"__esModule",{value:!0}),A),uUA={};QHQ(uUA,{AwsEc2QueryProtocol:()=>_HQ,AwsJson1_0Protocol:()=>UHQ,AwsJson1_1Protocol:()=>wHQ,AwsJsonRpcProtocol:()=>ft1,AwsQueryProtocol:()=>pUA,AwsRestJsonProtocol:()=>qHQ,AwsRestXmlProtocol:()=>gHQ,JsonCodec:()=>bt1,JsonShapeDeserializer:()=>cUA,JsonShapeSerializer:()=>lUA,XmlCodec:()=>sUA,XmlShapeDeserializer:()=>ht1,XmlShapeSerializer:()=>aUA,_toBool:()=>FHQ,_toNum:()=>IHQ,_toStr:()=>GHQ,awsExpectUnion:()=>LHQ,loadRestJsonErrorCode:()=>vt1,loadRestXmlErrorCode:()=>nUA,parseJsonBody:()=>xt1,parseJsonErrorBody:()=>CHQ,parseXmlBody:()=>iUA,parseXmlErrorBody:()=>fHQ});rUA.exports=ZHQ(uUA);var GHQ=s6((A)=>{if(A==null)return A;if(typeof A==="number"||typeof A==="bigint"){let B=new Error(`Received number ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}if(typeof A==="boolean"){let B=new Error(`Received boolean ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}return A},"_toStr"),FHQ=s6((A)=>{if(A==null)return A;if(typeof A==="string"){let B=A.toLowerCase();if(A!==""&&B!=="false"&&B!=="true"){let Q=new Error(`Received string "${A}" where a boolean was expected.`);Q.name="Warning",console.warn(Q)}return A!==""&&B!=="false"}return A},"_toBool"),IHQ=s6((A)=>{if(A==null)return A;if(typeof A==="string"){let B=Number(A);if(B.toString()!==A){let Q=new Error(`Received string "${A}" where a number was expected.`);return Q.name="Warning",console.warn(Q),A}return B}return A},"_toNum"),YHQ=$6(),Cn=jQ(),WHQ=wY(),vh=class{static{s6(this,"SerdeContextConfig")}serdeContext;setSerdeContext(A){this.serdeContext=A}},uQ1=jQ(),Kn=Y6(),JHQ=Yy(),XHQ=Y6();function mUA(A,B,Q){if(Q?.source){let D=Q.source;if(typeof B==="number"){if(B>Number.MAX_SAFE_INTEGER||B<Number.MIN_SAFE_INTEGER||D!==String(B))if(D.includes("."))return new XHQ.NumericValue(D,"bigDecimal");else return BigInt(D)}}return B}s6(mUA,"jsonReviver");var VHQ=x4(),dUA=s6((A,B)=>VHQ.collectBody(A,B).then((Q)=>B.utf8Encoder(Q)),"collectBodyString"),xt1=s6((A,B)=>dUA(A,B).then((Q)=>{if(Q.length)try{return JSON.parse(Q)}catch(D){if(D?.name==="SyntaxError")Object.defineProperty(D,"$responseBodyText",{value:Q});throw D}return{}}),"parseJsonBody"),CHQ=s6(async(A,B)=>{let Q=await xt1(A,B);return Q.message=Q.message??Q.Message,Q},"parseJsonErrorBody"),vt1=s6((A,B)=>{let Q=s6((G,F)=>Object.keys(G).find((I)=>I.toLowerCase()===F.toLowerCase()),"findKey"),D=s6((G)=>{let F=G;if(typeof F==="number")F=F.toString();if(F.indexOf(",")>=0)F=F.split(",")[0];if(F.indexOf(":")>=0)F=F.split(":")[0];if(F.indexOf("#")>=0)F=F.split("#")[1];return F},"sanitizeErrorCode"),Z=Q(A.headers,"x-amzn-errortype");if(Z!==void 0)return D(A.headers[Z]);if(B&&typeof B==="object"){let G=Q(B,"code");if(G&&B[G]!==void 0)return D(B[G]);if(B.__type!==void 0)return D(B.__type)}},"loadRestJsonErrorCode"),cUA=class extends vh{constructor(A){super();this.settings=A}static{s6(this,"JsonShapeDeserializer")}async read(A,B){return this._read(A,typeof B==="string"?JSON.parse(B,mUA):await xt1(B,this.serdeContext))}readObject(A,B){return this._read(A,B)}_read(A,B){let Q=B!==null&&typeof B==="object",D=uQ1.NormalizedSchema.of(A);if(D.isListSchema()&&Array.isArray(B)){let G=D.getValueSchema(),F=[],I=!!D.getMergedTraits().sparse;for(let Y of B)if(I||Y!=null)F.push(this._read(G,Y));return F}else if(D.isMapSchema()&&Q){let G=D.getValueSchema(),F={},I=!!D.getMergedTraits().sparse;for(let[Y,W]of Object.entries(B))if(I||W!=null)F[Y]=this._read(G,W);return F}else if(D.isStructSchema()&&Q){let G={};for(let[F,I]of D.structIterator()){let Y=this.settings.jsonName?I.getMergedTraits().jsonName??F:F,W=this._read(I,B[Y]);if(W!=null)G[F]=W}return G}if(D.isBlobSchema()&&typeof B==="string")return JHQ.fromBase64(B);let Z=D.getMergedTraits().mediaType;if(D.isStringSchema()&&typeof B==="string"&&Z){if(Z==="application/json"||Z.endsWith("+json"))return Kn.LazyJsonString.from(B)}if(D.isTimestampSchema()){let G=this.settings.timestampFormat;switch(G.useTrait?D.getSchema()===uQ1.SCHEMA.TIMESTAMP_DEFAULT?G.default:D.getSchema()??G.default:G.default){case uQ1.SCHEMA.TIMESTAMP_DATE_TIME:return Kn.parseRfc3339DateTimeWithOffset(B);case uQ1.SCHEMA.TIMESTAMP_HTTP_DATE:return Kn.parseRfc7231DateTime(B);case uQ1.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return Kn.parseEpochTimestamp(B);default:return console.warn("Missing timestamp format, parsing value with Date constructor:",B),new Date(B)}}if(D.isBigIntegerSchema()&&(typeof B==="number"||typeof B==="string"))return BigInt(B);if(D.isBigDecimalSchema()&&B!=null){if(B instanceof Kn.NumericValue)return B;return new Kn.NumericValue(String(B),"bigDecimal")}if(D.isNumericSchema()&&typeof B==="string")switch(B){case"Infinity":return 1/0;case"-Infinity":return-1/0;case"NaN":return NaN}return B}},Hn=jQ(),KHQ=Y6(),HHQ=Y6(),zHQ=Y6(),bUA=String.fromCharCode(925),EHQ=class{static{s6(this,"JsonReplacer")}values=new Map;counter=0;stage=0;createReplacer(){if(this.stage===1)throw new Error("@aws-sdk/core/protocols - JsonReplacer already created.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");return this.stage=1,(A,B)=>{if(B instanceof zHQ.NumericValue){let Q=`${bUA+NaN+this.counter++}_`+B.string;return this.values.set(`"${Q}"`,B.string),Q}if(typeof B==="bigint"){let Q=B.toString(),D=`${bUA+"b"+this.counter++}_`+Q;return this.values.set(`"${D}"`,Q),D}return B}}replaceInJson(A){if(this.stage===0)throw new Error("@aws-sdk/core/protocols - JsonReplacer not created yet.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");if(this.stage=2,this.counter===0)return A;for(let[B,Q]of this.values)A=A.replace(B,Q);return A}},lUA=class extends vh{constructor(A){super();this.settings=A}static{s6(this,"JsonShapeSerializer")}buffer;rootSchema;write(A,B){this.rootSchema=Hn.NormalizedSchema.of(A),this.buffer=this._write(this.rootSchema,B)}flush(){if(this.rootSchema?.isStructSchema()||this.rootSchema?.isDocumentSchema()){let A=new EHQ;return A.replaceInJson(JSON.stringify(this.buffer,A.createReplacer(),0))}return this.buffer}_write(A,B,Q){let D=B!==null&&typeof B==="object",Z=Hn.NormalizedSchema.of(A);if(Z.isListSchema()&&Array.isArray(B)){let F=Z.getValueSchema(),I=[],Y=!!Z.getMergedTraits().sparse;for(let W of B)if(Y||W!=null)I.push(this._write(F,W));return I}else if(Z.isMapSchema()&&D){let F=Z.getValueSchema(),I={},Y=!!Z.getMergedTraits().sparse;for(let[W,J]of Object.entries(B))if(Y||J!=null)I[W]=this._write(F,J);return I}else if(Z.isStructSchema()&&D){let F={};for(let[I,Y]of Z.structIterator()){let W=this.settings.jsonName?Y.getMergedTraits().jsonName??I:I,J=this._write(Y,B[I],Z);if(J!==void 0)F[W]=J}return F}if(B===null&&Q?.isStructSchema())return;if(Z.isBlobSchema()&&(B instanceof Uint8Array||typeof B==="string")){if(Z===this.rootSchema)return B;if(!this.serdeContext?.base64Encoder)throw new Error("Missing base64Encoder in serdeContext");return this.serdeContext?.base64Encoder(B)}if(Z.isTimestampSchema()&&B instanceof Date){let F=this.settings.timestampFormat;switch(F.useTrait?Z.getSchema()===Hn.SCHEMA.TIMESTAMP_DEFAULT?F.default:Z.getSchema()??F.default:F.default){case Hn.SCHEMA.TIMESTAMP_DATE_TIME:return B.toISOString().replace(".000Z","Z");case Hn.SCHEMA.TIMESTAMP_HTTP_DATE:return KHQ.dateToUtcString(B);case Hn.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return B.getTime()/1000;default:return console.warn("Missing timestamp format, using epoch seconds",B),B.getTime()/1000}}if(Z.isNumericSchema()&&typeof B==="number"){if(Math.abs(B)===1/0||isNaN(B))return String(B)}let G=Z.getMergedTraits().mediaType;if(Z.isStringSchema()&&typeof B==="string"&&G){if(G==="application/json"||G.endsWith("+json"))return HHQ.LazyJsonString.from(B)}return B}},bt1=class extends vh{constructor(A){super();this.settings=A}static{s6(this,"JsonCodec")}createSerializer(){let A=new lUA(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new cUA(this.settings);return A.setSerdeContext(this.serdeContext),A}},ft1=class extends YHQ.RpcProtocol{static{s6(this,"AwsJsonRpcProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});this.codec=new bt1({timestampFormat:{useTrait:!0,default:Cn.SCHEMA.TIMESTAMP_EPOCH_SECONDS},jsonName:!1}),this.serializer=this.codec.createSerializer(),this.deserializer=this.codec.createDeserializer()}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":`application/x-amz-json-${this.getJsonRpcVersion()}`,"x-amz-target":(this.getJsonRpcVersion()==="1.0"?"JsonRpc10.":"JsonProtocol.")+Cn.NormalizedSchema.of(A).getName()}),Cn.deref(A.input)==="unit"||!D.body)D.body="{}";try{D.headers["content-length"]=String(WHQ.calculateBodyLength(D.body))}catch(Z){}return D}getPayloadCodec(){return this.codec}async handleError(A,B,Q,D,Z){let G=vt1(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=Cn.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=Cn.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=Cn.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},UHQ=class extends ft1{static{s6(this,"AwsJson1_0Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_0"}getJsonRpcVersion(){return"1.0"}},wHQ=class extends ft1{static{s6(this,"AwsJson1_1Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_1"}getJsonRpcVersion(){return"1.1"}},jt1=$6(),mQ1=jQ(),$HQ=wY(),qHQ=class extends jt1.HttpBindingProtocol{static{s6(this,"AwsRestJsonProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});let B={timestampFormat:{useTrait:!0,default:mQ1.SCHEMA.TIMESTAMP_EPOCH_SECONDS},httpBindings:!0,jsonName:!0};this.codec=new bt1(B),this.serializer=new jt1.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new jt1.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getShapeId(){return"aws.protocols#restJson1"}getPayloadCodec(){return this.codec}setSerdeContext(A){this.codec.setSerdeContext(A),super.setSerdeContext(A)}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=mQ1.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/json"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/json"}}if(D.headers["content-type"]&&!D.body)D.body="{}";if(D.body)try{D.headers["content-length"]=String($HQ.calculateBodyLength(D.body))}catch(F){}return D}async handleError(A,B,Q,D,Z){let G=vt1(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=mQ1.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=mQ1.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=mQ1.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},NHQ=x4(),LHQ=s6((A)=>{if(A==null)return;if(typeof A==="object"&&"__type"in A)delete A.__type;return NHQ.expectUnion(A)},"awsExpectUnion"),kt1=$6(),Xy=jQ(),MHQ=wY(),RHQ=$6(),fUA=jQ(),OHQ=x4(),THQ=cB(),PHQ=gN(),ht1=class extends vh{constructor(A){super();this.settings=A,this.stringDeserializer=new RHQ.FromStringShapeDeserializer(A)}static{s6(this,"XmlShapeDeserializer")}stringDeserializer;setSerdeContext(A){this.serdeContext=A,this.stringDeserializer.setSerdeContext(A)}read(A,B,Q){let D=fUA.NormalizedSchema.of(A),Z=D.getMemberSchemas();if(D.isStructSchema()&&D.isMemberSchema()&&!!Object.values(Z).find((Y)=>{return!!Y.getMemberTraits().eventPayload})){let Y={},W=Object.keys(Z)[0];if(Z[W].isBlobSchema())Y[W]=B;else Y[W]=this.read(Z[W],B);return Y}let F=(this.serdeContext?.utf8Encoder??THQ.toUtf8)(B),I=this.parseXml(F);return this.readSchema(A,Q?I[Q]:I)}readSchema(A,B){let Q=fUA.NormalizedSchema.of(A),D=Q.getMergedTraits(),Z=Q.getSchema();if(Q.isListSchema()&&!Array.isArray(B))return this.readSchema(Z,[B]);if(B==null)return B;if(typeof B==="object"){let G=!!D.sparse,F=!!D.xmlFlattened;if(Q.isListSchema()){let Y=Q.getValueSchema(),W=[],J=Y.getMergedTraits().xmlName??"member",X=F?B:(B[0]??B)[J],V=Array.isArray(X)?X:[X];for(let C of V)if(C!=null||G)W.push(this.readSchema(Y,C));return W}let I={};if(Q.isMapSchema()){let Y=Q.getKeySchema(),W=Q.getValueSchema(),J;if(F)J=Array.isArray(B)?B:[B];else J=Array.isArray(B.entry)?B.entry:[B.entry];let X=Y.getMergedTraits().xmlName??"key",V=W.getMergedTraits().xmlName??"value";for(let C of J){let K=C[X],H=C[V];if(H!=null||G)I[K]=this.readSchema(W,H)}return I}if(Q.isStructSchema()){for(let[Y,W]of Q.structIterator()){let J=W.getMergedTraits(),X=!J.httpPayload?W.getMemberTraits().xmlName??Y:J.xmlName??W.getName();if(B[X]!=null)I[Y]=this.readSchema(W,B[X])}return I}if(Q.isDocumentSchema())return B;throw new Error(`@aws-sdk/core/protocols - xml deserializer unhandled schema type for ${Q.getName(!0)}`)}else{if(Q.isListSchema())return[];else if(Q.isMapSchema()||Q.isStructSchema())return{};return this.stringDeserializer.read(Q,B)}}parseXml(A){if(A.length){let B=new PHQ.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:s6((F,I)=>I.trim()===""&&I.includes(`
`)?"":void 0,"tagValueProcessor")});B.addEntity("#xD","\r"),B.addEntity("#10",`
`);let Q;try{Q=B.parse(A,!0)}catch(F){if(F&&typeof F==="object")Object.defineProperty(F,"$responseBodyText",{value:A});throw F}let D="#text",Z=Object.keys(Q)[0],G=Q[Z];if(G[D])G[Z]=G[D],delete G[D];return OHQ.getValueFromTextNode(G)}return{}}},yt1=$6(),RE1=jQ(),SHQ=Y6(),jHQ=x4(),kHQ=Yy(),yHQ=class extends vh{constructor(A){super();this.settings=A}static{s6(this,"QueryShapeSerializer")}buffer;write(A,B,Q=""){if(this.buffer===void 0)this.buffer="";let D=RE1.NormalizedSchema.of(A);if(Q&&!Q.endsWith("."))Q+=".";if(D.isBlobSchema()){if(typeof B==="string"||B instanceof Uint8Array)this.writeKey(Q),this.writeValue((this.serdeContext?.base64Encoder??kHQ.toBase64)(B))}else if(D.isBooleanSchema()||D.isNumericSchema()||D.isStringSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigIntegerSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigDecimalSchema()){if(B!=null)this.writeKey(Q),this.writeValue(B instanceof SHQ.NumericValue?B.string:String(B))}else if(D.isTimestampSchema()){if(B instanceof Date)switch(this.writeKey(Q),yt1.determineTimestampFormat(D,this.settings)){case RE1.SCHEMA.TIMESTAMP_DATE_TIME:this.writeValue(B.toISOString().replace(".000Z","Z"));break;case RE1.SCHEMA.TIMESTAMP_HTTP_DATE:this.writeValue(jHQ.dateToUtcString(B));break;case RE1.SCHEMA.TIMESTAMP_EPOCH_SECONDS:this.writeValue(String(B.getTime()/1000));break}}else if(D.isDocumentSchema())throw new Error(`@aws-sdk/core/protocols - QuerySerializer unsupported document type ${D.getName(!0)}`);else if(D.isListSchema()){if(Array.isArray(B))if(B.length===0){if(this.settings.serializeEmptyLists)this.writeKey(Q),this.writeValue("")}else{let Z=D.getValueSchema(),G=this.settings.flattenLists||D.getMergedTraits().xmlFlattened,F=1;for(let I of B){if(I==null)continue;let Y=this.getKey("member",Z.getMergedTraits().xmlName),W=G?`${Q}${F}`:`${Q}${Y}.${F}`;this.write(Z,I,W),++F}}}else if(D.isMapSchema()){if(B&&typeof B==="object"){let Z=D.getKeySchema(),G=D.getValueSchema(),F=D.getMergedTraits().xmlFlattened,I=1;for(let[Y,W]of Object.entries(B)){if(W==null)continue;let J=this.getKey("key",Z.getMergedTraits().xmlName),X=F?`${Q}${I}.${J}`:`${Q}entry.${I}.${J}`,V=this.getKey("value",G.getMergedTraits().xmlName),C=F?`${Q}${I}.${V}`:`${Q}entry.${I}.${V}`;this.write(Z,Y,X),this.write(G,W,C),++I}}}else if(D.isStructSchema()){if(B&&typeof B==="object")for(let[Z,G]of D.structIterator()){if(B[Z]==null)continue;let F=this.getKey(Z,G.getMergedTraits().xmlName),I=`${Q}${F}`;this.write(G,B[Z],I)}}else if(D.isUnitSchema());else throw new Error(`@aws-sdk/core/protocols - QuerySerializer unrecognized schema type ${D.getName(!0)}`)}flush(){if(this.buffer===void 0)throw new Error("@aws-sdk/core/protocols - QuerySerializer cannot flush with nothing written to buffer.");let A=this.buffer;return delete this.buffer,A}getKey(A,B){let Q=B??A;if(this.settings.capitalizeKeys)return Q[0].toUpperCase()+Q.slice(1);return Q}writeKey(A){if(A.endsWith("."))A=A.slice(0,A.length-1);this.buffer+=`&${yt1.extendedEncodeURIComponent(A)}=`}writeValue(A){this.buffer+=yt1.extendedEncodeURIComponent(A)}},pUA=class extends kt1.RpcProtocol{constructor(A){super({defaultNamespace:A.defaultNamespace});this.options=A;let B={timestampFormat:{useTrait:!0,default:Xy.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!1,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace,serializeEmptyLists:!0};this.serializer=new yHQ(B),this.deserializer=new ht1(B)}static{s6(this,"AwsQueryProtocol")}serializer;deserializer;getShapeId(){return"aws.protocols#awsQuery"}setSerdeContext(A){this.serializer.setSerdeContext(A),this.deserializer.setSerdeContext(A)}getPayloadCodec(){throw new Error("AWSQuery protocol has no payload codec.")}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":"application/x-www-form-urlencoded"}),Xy.deref(A.input)==="unit"||!D.body)D.body="";if(D.body=`Action=${A.name.split("#")[1]}&Version=${this.options.version}`+D.body,D.body.endsWith("&"))D.body=D.body.slice(-1);try{D.headers["content-length"]=String(MHQ.calculateBodyLength(D.body))}catch(Z){}return D}async deserializeResponse(A,B,Q){let D=this.deserializer,Z=Xy.NormalizedSchema.of(A.output),G={};if(Q.statusCode>=300){let W=await kt1.collectBody(Q.body,B);if(W.byteLength>0)Object.assign(G,await D.read(Xy.SCHEMA.DOCUMENT,W));await this.handleError(A,B,Q,G,this.deserializeMetadata(Q))}for(let W in Q.headers){let J=Q.headers[W];delete Q.headers[W],Q.headers[W.toLowerCase()]=J}let F=Z.isStructSchema()&&this.useNestedResult()?A.name.split("#")[1]+"Result":void 0,I=await kt1.collectBody(Q.body,B);if(I.byteLength>0)Object.assign(G,await D.read(Z,I,F));return{$metadata:this.deserializeMetadata(Q),...G}}useNestedResult(){return!0}async handleError(A,B,Q,D,Z){let G=this.loadQueryErrorCode(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=this.loadQueryError(D),W=Xy.TypeRegistry.for(F),J;try{if(J=W.find((H)=>Xy.NormalizedSchema.of(H).getMergedTraits().awsQueryError?.[0]===I),!J)J=W.getSchema(G)}catch(H){let z=Xy.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(z){let $=z.ctor;throw Object.assign(new $(I),Y)}throw new Error(I)}let X=Xy.NormalizedSchema.of(J),V=this.loadQueryErrorMessage(D),C=new J.ctor(V),K={};for(let[H,z]of X.structIterator()){let $=z.getMergedTraits().xmlName??H,L=Y[$]??D[$];K[H]=this.deserializer.readSchema(z,L)}throw Object.assign(C,{$metadata:Z,$response:Q,$fault:X.getMergedTraits().error,message:V,...K}),C}loadQueryErrorCode(A,B){let Q=(B.Errors?.[0]?.Error??B.Errors?.Error??B.Error)?.Code;if(Q!==void 0)return Q;if(A.statusCode==404)return"NotFound"}loadQueryError(A){return A.Errors?.[0]?.Error??A.Errors?.Error??A.Error}loadQueryErrorMessage(A){let B=this.loadQueryError(A);return B?.message??B?.Message??A.message??A.Message??"Unknown"}},_HQ=class extends pUA{constructor(A){super(A);this.options=A;let B={capitalizeKeys:!0,flattenLists:!0,serializeEmptyLists:!1};Object.assign(this.serializer.settings,B)}static{s6(this,"AwsEc2QueryProtocol")}useNestedResult(){return!1}},_t1=$6(),dQ1=jQ(),xHQ=wY(),vHQ=x4(),bHQ=gN(),iUA=s6((A,B)=>dUA(A,B).then((Q)=>{if(Q.length){let D=new bHQ.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:s6((Y,W)=>W.trim()===""&&W.includes(`
`)?"":void 0,"tagValueProcessor")});D.addEntity("#xD","\r"),D.addEntity("#10",`
`);let Z;try{Z=D.parse(Q,!0)}catch(Y){if(Y&&typeof Y==="object")Object.defineProperty(Y,"$responseBodyText",{value:Q});throw Y}let G="#text",F=Object.keys(Z)[0],I=Z[F];if(I[G])I[F]=I[G],delete I[G];return vHQ.getValueFromTextNode(I)}return{}}),"parseXmlBody"),fHQ=s6(async(A,B)=>{let Q=await iUA(A,B);if(Q.Error)Q.Error.message=Q.Error.message??Q.Error.Message;return Q},"parseXmlErrorBody"),nUA=s6((A,B)=>{if(B?.Error?.Code!==void 0)return B.Error.Code;if(B?.Code!==void 0)return B.Code;if(A.statusCode==404)return"NotFound"},"loadRestXmlErrorCode"),uN=gQ1(),xh=jQ(),hHQ=Y6(),hUA=x4(),gUA=Yy(),aUA=class extends vh{constructor(A){super();this.settings=A}static{s6(this,"XmlShapeSerializer")}stringBuffer;byteBuffer;buffer;write(A,B){let Q=xh.NormalizedSchema.of(A);if(Q.isStringSchema()&&typeof B==="string")this.stringBuffer=B;else if(Q.isBlobSchema())this.byteBuffer="byteLength"in B?B:(this.serdeContext?.base64Decoder??gUA.fromBase64)(B);else{this.buffer=this.writeStruct(Q,B,void 0);let D=Q.getMergedTraits();if(D.httpPayload&&!D.xmlName)this.buffer.withName(Q.getName())}}flush(){if(this.byteBuffer!==void 0){let B=this.byteBuffer;return delete this.byteBuffer,B}if(this.stringBuffer!==void 0){let B=this.stringBuffer;return delete this.stringBuffer,B}let A=this.buffer;if(this.settings.xmlNamespace){if(!A?.attributes?.xmlns)A.addAttribute("xmlns",this.settings.xmlNamespace)}return delete this.buffer,A.toString()}writeStruct(A,B,Q){let D=A.getMergedTraits(),Z=A.isMemberSchema()&&!D.httpPayload?A.getMemberTraits().xmlName??A.getMemberName():D.xmlName??A.getName();if(!Z||!A.isStructSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write struct with empty name or non-struct, schema=${A.getName(!0)}.`);let G=uN.XmlNode.of(Z),[F,I]=this.getXmlnsAttribute(A,Q);if(I)G.addAttribute(F,I);for(let[Y,W]of A.structIterator()){let J=B[Y];if(J!=null){if(W.getMergedTraits().xmlAttribute){G.addAttribute(W.getMergedTraits().xmlName??Y,this.writeSimple(W,J));continue}if(W.isListSchema())this.writeList(W,J,G,I);else if(W.isMapSchema())this.writeMap(W,J,G,I);else if(W.isStructSchema())G.addChildNode(this.writeStruct(W,J,I));else{let X=uN.XmlNode.of(W.getMergedTraits().xmlName??W.getMemberName());this.writeSimpleInto(W,J,X,I),G.addChildNode(X)}}}return G}writeList(A,B,Q,D){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member list: ${A.getName(!0)}`);let Z=A.getMergedTraits(),G=A.getValueSchema(),F=G.getMergedTraits(),I=!!F.sparse,Y=!!Z.xmlFlattened,[W,J]=this.getXmlnsAttribute(A,D),X=s6((V,C)=>{if(G.isListSchema())this.writeList(G,Array.isArray(C)?C:[C],V,J);else if(G.isMapSchema())this.writeMap(G,C,V,J);else if(G.isStructSchema()){let K=this.writeStruct(G,C,J);V.addChildNode(K.withName(Y?Z.xmlName??A.getMemberName():F.xmlName??"member"))}else{let K=uN.XmlNode.of(Y?Z.xmlName??A.getMemberName():F.xmlName??"member");this.writeSimpleInto(G,C,K,J),V.addChildNode(K)}},"writeItem");if(Y){for(let V of B)if(I||V!=null)X(Q,V)}else{let V=uN.XmlNode.of(Z.xmlName??A.getMemberName());if(J)V.addAttribute(W,J);for(let C of B)if(I||C!=null)X(V,C);Q.addChildNode(V)}}writeMap(A,B,Q,D,Z=!1){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member map: ${A.getName(!0)}`);let G=A.getMergedTraits(),F=A.getKeySchema(),Y=F.getMergedTraits().xmlName??"key",W=A.getValueSchema(),J=W.getMergedTraits(),X=J.xmlName??"value",V=!!J.sparse,C=!!G.xmlFlattened,[K,H]=this.getXmlnsAttribute(A,D),z=s6(($,L,N)=>{let O=uN.XmlNode.of(Y,L),[R,T]=this.getXmlnsAttribute(F,H);if(T)O.addAttribute(R,T);$.addChildNode(O);let j=uN.XmlNode.of(X);if(W.isListSchema())this.writeList(W,N,j,H);else if(W.isMapSchema())this.writeMap(W,N,j,H,!0);else if(W.isStructSchema())j=this.writeStruct(W,N,H);else this.writeSimpleInto(W,N,j,H);$.addChildNode(j)},"addKeyValue");if(C){for(let[$,L]of Object.entries(B))if(V||L!=null){let N=uN.XmlNode.of(G.xmlName??A.getMemberName());z(N,$,L),Q.addChildNode(N)}}else{let $;if(!Z){if($=uN.XmlNode.of(G.xmlName??A.getMemberName()),H)$.addAttribute(K,H);Q.addChildNode($)}for(let[L,N]of Object.entries(B))if(V||N!=null){let O=uN.XmlNode.of("entry");z(O,L,N),(Z?Q:$).addChildNode(O)}}}writeSimple(A,B){if(B===null)throw new Error("@aws-sdk/core/protocols - (XML serializer) cannot write null value.");let Q=xh.NormalizedSchema.of(A),D=null;if(B&&typeof B==="object")if(Q.isBlobSchema())D=(this.serdeContext?.base64Encoder??gUA.toBase64)(B);else if(Q.isTimestampSchema()&&B instanceof Date){let Z=this.settings.timestampFormat;switch(Z.useTrait?Q.getSchema()===xh.SCHEMA.TIMESTAMP_DEFAULT?Z.default:Q.getSchema()??Z.default:Z.default){case xh.SCHEMA.TIMESTAMP_DATE_TIME:D=B.toISOString().replace(".000Z","Z");break;case xh.SCHEMA.TIMESTAMP_HTTP_DATE:D=hUA.dateToUtcString(B);break;case xh.SCHEMA.TIMESTAMP_EPOCH_SECONDS:D=String(B.getTime()/1000);break;default:console.warn("Missing timestamp format, using http date",B),D=hUA.dateToUtcString(B);break}}else if(Q.isBigDecimalSchema()&&B){if(B instanceof hHQ.NumericValue)return B.string;return String(B)}else if(Q.isMapSchema()||Q.isListSchema())throw new Error("@aws-sdk/core/protocols - xml serializer, cannot call _write() on List/Map schema, call writeList or writeMap() instead.");else throw new Error(`@aws-sdk/core/protocols - xml serializer, unhandled schema type for object value and schema: ${Q.getName(!0)}`);if(Q.isStringSchema()||Q.isBooleanSchema()||Q.isNumericSchema()||Q.isBigIntegerSchema()||Q.isBigDecimalSchema())D=String(B);if(D===null)throw new Error(`Unhandled schema-value pair ${Q.getName(!0)}=${B}`);return D}writeSimpleInto(A,B,Q,D){let Z=this.writeSimple(A,B),G=xh.NormalizedSchema.of(A),F=new uN.XmlText(Z),[I,Y]=this.getXmlnsAttribute(G,D);if(Y)Q.addAttribute(I,Y);Q.addChildNode(F)}getXmlnsAttribute(A,B){let Q=A.getMergedTraits(),[D,Z]=Q.xmlNamespace??[];if(Z&&Z!==B)return[D?`xmlns:${D}`:"xmlns",Z];return[void 0,void 0]}},sUA=class extends vh{constructor(A){super();this.settings=A}static{s6(this,"XmlCodec")}createSerializer(){let A=new aUA(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new ht1(this.settings);return A.setSerdeContext(this.serdeContext),A}},gHQ=class extends _t1.HttpBindingProtocol{static{s6(this,"AwsRestXmlProtocol")}codec;serializer;deserializer;constructor(A){super(A);let B={timestampFormat:{useTrait:!0,default:dQ1.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!0,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace};this.codec=new sUA(B),this.serializer=new _t1.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new _t1.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getPayloadCodec(){return this.codec}getShapeId(){return"aws.protocols#restXml"}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=dQ1.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(D.path=String(D.path).split("/").filter((F)=>{return F!=="{Bucket}"}).join("/")||"/",!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/xml"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/xml"}}if(D.headers["content-type"]==="application/xml"){if(typeof D.body==="string")D.body='<?xml version="1.0" encoding="UTF-8"?>'+D.body}if(D.body)try{D.headers["content-length"]=String(xHQ.calculateBodyLength(D.body))}catch(F){}return D}async deserializeResponse(A,B,Q){return super.deserializeResponse(A,B,Q)}async handleError(A,B,Q,D,Z){let G=nUA(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=dQ1.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=dQ1.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=dQ1.NormalizedSchema.of(W),X=D.Error?.message??D.Error?.Message??D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().xmlName??K,$=D.Error?.[z]??D[z];C[K]=this.codec.createDeserializer().readSchema(H,$)}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}}});
var oe1=E((jZ5,XOA)=>{var{defineProperty:$U1,getOwnPropertyDescriptor:MOQ,getOwnPropertyNames:ROQ}=Object,OOQ=Object.prototype.hasOwnProperty,l4=(A,B)=>$U1(A,"name",{value:B,configurable:!0}),TOQ=(A,B)=>{for(var Q in B)$U1(A,Q,{get:B[Q],enumerable:!0})},POQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ROQ(B))if(!OOQ.call(A,Z)&&Z!==Q)$U1(A,Z,{get:()=>B[Z],enumerable:!(D=MOQ(B,Z))||D.enumerable})}return A},SOQ=(A)=>POQ($U1({},"__esModule",{value:!0}),A),iRA={};TOQ(iRA,{$Command:()=>sRA.Command,AccessDeniedException:()=>rRA,AuthorizationPendingException:()=>oRA,CreateTokenCommand:()=>WOA,CreateTokenRequestFilterSensitiveLog:()=>tRA,CreateTokenResponseFilterSensitiveLog:()=>eRA,ExpiredTokenException:()=>AOA,InternalServerException:()=>BOA,InvalidClientException:()=>QOA,InvalidGrantException:()=>DOA,InvalidRequestException:()=>ZOA,InvalidScopeException:()=>GOA,SSOOIDC:()=>JOA,SSOOIDCClient:()=>aRA,SSOOIDCServiceException:()=>aC,SlowDownException:()=>FOA,UnauthorizedClientException:()=>IOA,UnsupportedGrantTypeException:()=>YOA,__Client:()=>nRA.Client});XOA.exports=SOQ(iRA);var gRA=NQ1(),jOQ=LQ1(),kOQ=MQ1(),uRA=zn(),yOQ=V4(),se1=VB(),_OQ=TG(),xOQ=q6(),mRA=v4(),nRA=x4(),dRA=le1(),vOQ=l4((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"sso-oauth"})},"resolveClientEndpointParameters"),bOQ={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},fOQ=hRA(),cRA=B41(),lRA=mJ(),pRA=x4(),hOQ=l4((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),gOQ=l4((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),uOQ=l4((A,B)=>{let Q=Object.assign(cRA.getAwsRegionExtensionConfiguration(A),pRA.getDefaultExtensionConfiguration(A),lRA.getHttpHandlerExtensionConfiguration(A),hOQ(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,cRA.resolveAwsRegionExtensionConfiguration(Q),pRA.resolveDefaultRuntimeConfig(Q),lRA.resolveHttpHandlerRuntimeConfig(Q),gOQ(Q))},"resolveRuntimeExtensions"),aRA=class extends nRA.Client{static{l4(this,"SSOOIDCClient")}config;constructor(...[A]){let B=fOQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=vOQ(B),D=uRA.resolveUserAgentConfig(Q),Z=mRA.resolveRetryConfig(D),G=yOQ.resolveRegionConfig(Z),F=gRA.resolveHostHeaderConfig(G),I=xOQ.resolveEndpointConfig(F),Y=dRA.resolveHttpAuthSchemeConfig(I),W=uOQ(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(uRA.getUserAgentPlugin(this.config)),this.middlewareStack.use(mRA.getRetryPlugin(this.config)),this.middlewareStack.use(_OQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(gRA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(jOQ.getLoggerPlugin(this.config)),this.middlewareStack.use(kOQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(se1.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:dRA.defaultSSOOIDCHttpAuthSchemeParametersProvider,identityProviderConfigProvider:l4(async(J)=>new se1.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(se1.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},mOQ=x4(),dOQ=q6(),cOQ=T3(),sRA=x4(),Pn=x4(),lOQ=x4(),aC=class A extends lOQ.ServiceException{static{l4(this,"SSOOIDCServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},rRA=class A extends aC{static{l4(this,"AccessDeniedException")}name="AccessDeniedException";$fault="client";error;error_description;constructor(B){super({name:"AccessDeniedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},oRA=class A extends aC{static{l4(this,"AuthorizationPendingException")}name="AuthorizationPendingException";$fault="client";error;error_description;constructor(B){super({name:"AuthorizationPendingException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},tRA=l4((A)=>({...A,...A.clientSecret&&{clientSecret:Pn.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:Pn.SENSITIVE_STRING},...A.codeVerifier&&{codeVerifier:Pn.SENSITIVE_STRING}}),"CreateTokenRequestFilterSensitiveLog"),eRA=l4((A)=>({...A,...A.accessToken&&{accessToken:Pn.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:Pn.SENSITIVE_STRING},...A.idToken&&{idToken:Pn.SENSITIVE_STRING}}),"CreateTokenResponseFilterSensitiveLog"),AOA=class A extends aC{static{l4(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";error;error_description;constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},BOA=class A extends aC{static{l4(this,"InternalServerException")}name="InternalServerException";$fault="server";error;error_description;constructor(B){super({name:"InternalServerException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},QOA=class A extends aC{static{l4(this,"InvalidClientException")}name="InvalidClientException";$fault="client";error;error_description;constructor(B){super({name:"InvalidClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},DOA=class A extends aC{static{l4(this,"InvalidGrantException")}name="InvalidGrantException";$fault="client";error;error_description;constructor(B){super({name:"InvalidGrantException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},ZOA=class A extends aC{static{l4(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";error;error_description;constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},GOA=class A extends aC{static{l4(this,"InvalidScopeException")}name="InvalidScopeException";$fault="client";error;error_description;constructor(B){super({name:"InvalidScopeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},FOA=class A extends aC{static{l4(this,"SlowDownException")}name="SlowDownException";$fault="client";error;error_description;constructor(B){super({name:"SlowDownException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},IOA=class A extends aC{static{l4(this,"UnauthorizedClientException")}name="UnauthorizedClientException";$fault="client";error;error_description;constructor(B){super({name:"UnauthorizedClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},YOA=class A extends aC{static{l4(this,"UnsupportedGrantTypeException")}name="UnsupportedGrantTypeException";$fault="client";error;error_description;constructor(B){super({name:"UnsupportedGrantTypeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},re1=ZI(),pOQ=VB(),zB=x4(),iOQ=l4(async(A,B)=>{let Q=pOQ.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/token");let Z;return Z=JSON.stringify(zB.take(A,{clientId:[],clientSecret:[],code:[],codeVerifier:[],deviceCode:[],grantType:[],redirectUri:[],refreshToken:[],scope:l4((G)=>zB._json(G),"scope")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateTokenCommand"),nOQ=l4(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return aOQ(A,B);let Q=zB.map({$metadata:Cz(A)}),D=zB.expectNonNull(zB.expectObject(await re1.parseJsonBody(A.body,B)),"body"),Z=zB.take(D,{accessToken:zB.expectString,expiresIn:zB.expectInt32,idToken:zB.expectString,refreshToken:zB.expectString,tokenType:zB.expectString});return Object.assign(Q,Z),Q},"de_CreateTokenCommand"),aOQ=l4(async(A,B)=>{let Q={...A,body:await re1.parseJsonErrorBody(A.body,B)},D=re1.loadRestJsonErrorCode(A,Q.body);switch(D){case"AccessDeniedException":case"com.amazonaws.ssooidc#AccessDeniedException":throw await rOQ(Q,B);case"AuthorizationPendingException":case"com.amazonaws.ssooidc#AuthorizationPendingException":throw await oOQ(Q,B);case"ExpiredTokenException":case"com.amazonaws.ssooidc#ExpiredTokenException":throw await tOQ(Q,B);case"InternalServerException":case"com.amazonaws.ssooidc#InternalServerException":throw await eOQ(Q,B);case"InvalidClientException":case"com.amazonaws.ssooidc#InvalidClientException":throw await ATQ(Q,B);case"InvalidGrantException":case"com.amazonaws.ssooidc#InvalidGrantException":throw await BTQ(Q,B);case"InvalidRequestException":case"com.amazonaws.ssooidc#InvalidRequestException":throw await QTQ(Q,B);case"InvalidScopeException":case"com.amazonaws.ssooidc#InvalidScopeException":throw await DTQ(Q,B);case"SlowDownException":case"com.amazonaws.ssooidc#SlowDownException":throw await ZTQ(Q,B);case"UnauthorizedClientException":case"com.amazonaws.ssooidc#UnauthorizedClientException":throw await GTQ(Q,B);case"UnsupportedGrantTypeException":case"com.amazonaws.ssooidc#UnsupportedGrantTypeException":throw await FTQ(Q,B);default:let Z=Q.body;return sOQ({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),sOQ=zB.withBaseException(aC),rOQ=l4(async(A,B)=>{let Q=zB.map({}),D=A.body,Z=zB.take(D,{error:zB.expectString,error_description:zB.expectString});Object.assign(Q,Z);let G=new rRA({$metadata:Cz(A),...Q});return zB.decorateServiceException(G,A.body)},"de_AccessDeniedExceptionRes"),oOQ=l4(async(A,B)=>{let Q=zB.map({}),D=A.body,Z=zB.take(D,{error:zB.expectString,error_description:zB.expectString});Object.assign(Q,Z);let G=new oRA({$metadata:Cz(A),...Q});return zB.decorateServiceException(G,A.body)},"de_AuthorizationPendingExceptionRes"),tOQ=l4(async(A,B)=>{let Q=zB.map({}),D=A.body,Z=zB.take(D,{error:zB.expectString,error_description:zB.expectString});Object.assign(Q,Z);let G=new AOA({$metadata:Cz(A),...Q});return zB.decorateServiceException(G,A.body)},"de_ExpiredTokenExceptionRes"),eOQ=l4(async(A,B)=>{let Q=zB.map({}),D=A.body,Z=zB.take(D,{error:zB.expectString,error_description:zB.expectString});Object.assign(Q,Z);let G=new BOA({$metadata:Cz(A),...Q});return zB.decorateServiceException(G,A.body)},"de_InternalServerExceptionRes"),ATQ=l4(async(A,B)=>{let Q=zB.map({}),D=A.body,Z=zB.take(D,{error:zB.expectString,error_description:zB.expectString});Object.assign(Q,Z);let G=new QOA({$metadata:Cz(A),...Q});return zB.decorateServiceException(G,A.body)},"de_InvalidClientExceptionRes"),BTQ=l4(async(A,B)=>{let Q=zB.map({}),D=A.body,Z=zB.take(D,{error:zB.expectString,error_description:zB.expectString});Object.assign(Q,Z);let G=new DOA({$metadata:Cz(A),...Q});return zB.decorateServiceException(G,A.body)},"de_InvalidGrantExceptionRes"),QTQ=l4(async(A,B)=>{let Q=zB.map({}),D=A.body,Z=zB.take(D,{error:zB.expectString,error_description:zB.expectString});Object.assign(Q,Z);let G=new ZOA({$metadata:Cz(A),...Q});return zB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),DTQ=l4(async(A,B)=>{let Q=zB.map({}),D=A.body,Z=zB.take(D,{error:zB.expectString,error_description:zB.expectString});Object.assign(Q,Z);let G=new GOA({$metadata:Cz(A),...Q});return zB.decorateServiceException(G,A.body)},"de_InvalidScopeExceptionRes"),ZTQ=l4(async(A,B)=>{let Q=zB.map({}),D=A.body,Z=zB.take(D,{error:zB.expectString,error_description:zB.expectString});Object.assign(Q,Z);let G=new FOA({$metadata:Cz(A),...Q});return zB.decorateServiceException(G,A.body)},"de_SlowDownExceptionRes"),GTQ=l4(async(A,B)=>{let Q=zB.map({}),D=A.body,Z=zB.take(D,{error:zB.expectString,error_description:zB.expectString});Object.assign(Q,Z);let G=new IOA({$metadata:Cz(A),...Q});return zB.decorateServiceException(G,A.body)},"de_UnauthorizedClientExceptionRes"),FTQ=l4(async(A,B)=>{let Q=zB.map({}),D=A.body,Z=zB.take(D,{error:zB.expectString,error_description:zB.expectString});Object.assign(Q,Z);let G=new YOA({$metadata:Cz(A),...Q});return zB.decorateServiceException(G,A.body)},"de_UnsupportedGrantTypeExceptionRes"),Cz=l4((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),WOA=class extends sRA.Command.classBuilder().ep(bOQ).m(function(A,B,Q,D){return[cOQ.getSerdePlugin(Q,this.serialize,this.deserialize),dOQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSSOOIDCService","CreateToken",{}).n("SSOOIDCClient","CreateTokenCommand").f(tRA,eRA).ser(iOQ).de(nOQ).build(){static{l4(this,"CreateTokenCommand")}},ITQ={CreateTokenCommand:WOA},JOA=class extends aRA{static{l4(this,"SSOOIDC")}};mOQ.createAggregatedClient(ITQ,JOA)});
var ot1=E((T$A)=>{Object.defineProperty(T$A,"__esModule",{value:!0});T$A.getEndpointFromConfig=void 0;var JUQ=QD(),XUQ=O$A(),VUQ=async(A)=>JUQ.loadConfig(XUQ.getEndpointUrlConfig(A!==null&&A!==void 0?A:""))();T$A.getEndpointFromConfig=VUQ});
var pe1=E((RZ5,BOQ)=>{BOQ.exports={name:"@aws-sdk/nested-clients",version:"3.840.0",description:"Nested clients for AWS SDK packages.",main:"./dist-cjs/index.js",module:"./dist-es/index.js",types:"./dist-types/index.d.ts",scripts:{build:"yarn lint && concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline nested-clients","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo",lint:"node ../../scripts/validation/submodules-linter.js --pkg nested-clients",test:"yarn g:vitest run","test:watch":"yarn g:vitest watch"},engines:{node:">=18.0.0"},author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["./sso-oidc.d.ts","./sso-oidc.js","./sts.d.ts","./sts.js","dist-*/**"],browser:{"./dist-es/submodules/sso-oidc/runtimeConfig":"./dist-es/submodules/sso-oidc/runtimeConfig.browser","./dist-es/submodules/sts/runtimeConfig":"./dist-es/submodules/sts/runtimeConfig.browser"},"react-native":{},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/packages/nested-clients",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"packages/nested-clients"},exports:{"./sso-oidc":{types:"./dist-types/submodules/sso-oidc/index.d.ts",module:"./dist-es/submodules/sso-oidc/index.js",node:"./dist-cjs/submodules/sso-oidc/index.js",import:"./dist-es/submodules/sso-oidc/index.js",require:"./dist-cjs/submodules/sso-oidc/index.js"},"./sts":{types:"./dist-types/submodules/sts/index.d.ts",module:"./dist-es/submodules/sts/index.js",node:"./dist-cjs/submodules/sts/index.js",import:"./dist-es/submodules/sts/index.js",require:"./dist-cjs/submodules/sts/index.js"}}}});
var q6=E((VD5,d$A)=>{var{defineProperty:pE1,getOwnPropertyDescriptor:PUQ,getOwnPropertyNames:SUQ}=Object,jUQ=Object.prototype.hasOwnProperty,Gz=(A,B)=>pE1(A,"name",{value:B,configurable:!0}),kUQ=(A,B)=>{for(var Q in B)pE1(A,Q,{get:B[Q],enumerable:!0})},yUQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of SUQ(B))if(!jUQ.call(A,Z)&&Z!==Q)pE1(A,Z,{get:()=>B[Z],enumerable:!(D=PUQ(B,Z))||D.enumerable})}return A},_UQ=(A)=>yUQ(pE1({},"__esModule",{value:!0}),A),f$A={};kUQ(f$A,{endpointMiddleware:()=>u$A,endpointMiddlewareOptions:()=>m$A,getEndpointFromInstructions:()=>h$A,getEndpointPlugin:()=>lUQ,resolveEndpointConfig:()=>iUQ,resolveEndpointRequiredConfig:()=>nUQ,resolveParams:()=>g$A,toEndpointV1:()=>tt1});d$A.exports=_UQ(f$A);var xUQ=Gz(async(A)=>{let B=A?.Bucket||"";if(typeof A.Bucket==="string")A.Bucket=B.replace(/#/g,encodeURIComponent("#")).replace(/\?/g,encodeURIComponent("?"));if(gUQ(B)){if(A.ForcePathStyle===!0)throw new Error("Path-style addressing cannot be used with ARN buckets")}else if(!hUQ(B)||B.indexOf(".")!==-1&&!String(A.Endpoint).startsWith("http:")||B.toLowerCase()!==B||B.length<3)A.ForcePathStyle=!0;if(A.DisableMultiRegionAccessPoints)A.disableMultiRegionAccessPoints=!0,A.DisableMRAP=!0;return A},"resolveParamsForS3"),vUQ=/^[a-z0-9][a-z0-9\.\-]{1,61}[a-z0-9]$/,bUQ=/(\d+\.){3}\d+/,fUQ=/\.\./,hUQ=Gz((A)=>vUQ.test(A)&&!bUQ.test(A)&&!fUQ.test(A),"isDnsCompatibleBucketName"),gUQ=Gz((A)=>{let[B,Q,D,,,Z]=A.split(":"),G=B==="arn"&&A.split(":").length>=6,F=Boolean(G&&Q&&D&&Z);if(G&&!F)throw new Error(`Invalid ARN: ${A} was an invalid ARN.`);return F},"isArnBucketName"),uUQ=Gz((A,B,Q)=>{let D=Gz(async()=>{let Z=Q[A]??Q[B];if(typeof Z==="function")return Z();return Z},"configProvider");if(A==="credentialScope"||B==="CredentialScope")return async()=>{let Z=typeof Q.credentials==="function"?await Q.credentials():Q.credentials;return Z?.credentialScope??Z?.CredentialScope};if(A==="accountId"||B==="AccountId")return async()=>{let Z=typeof Q.credentials==="function"?await Q.credentials():Q.credentials;return Z?.accountId??Z?.AccountId};if(A==="endpoint"||B==="endpoint")return async()=>{let Z=await D();if(Z&&typeof Z==="object"){if("url"in Z)return Z.url.href;if("hostname"in Z){let{protocol:G,hostname:F,port:I,path:Y}=Z;return`${G}//${F}${I?":"+I:""}${Y}`}}return Z};return D},"createConfigValueProvider"),mUQ=ot1(),b$A=BZ(),tt1=Gz((A)=>{if(typeof A==="object"){if("url"in A)return b$A.parseUrl(A.url);return A}return b$A.parseUrl(A)},"toEndpointV1"),h$A=Gz(async(A,B,Q,D)=>{if(!Q.endpoint){let F;if(Q.serviceConfiguredEndpoint)F=await Q.serviceConfiguredEndpoint();else F=await mUQ.getEndpointFromConfig(Q.serviceId);if(F)Q.endpoint=()=>Promise.resolve(tt1(F))}let Z=await g$A(A,B,Q);if(typeof Q.endpointProvider!=="function")throw new Error("config.endpointProvider is not set.");return Q.endpointProvider(Z,D)},"getEndpointFromInstructions"),g$A=Gz(async(A,B,Q)=>{let D={},Z=B?.getEndpointParameterInstructions?.()||{};for(let[G,F]of Object.entries(Z))switch(F.type){case"staticContextParams":D[G]=F.value;break;case"contextParams":D[G]=A[F.name];break;case"clientContextParams":case"builtInParams":D[G]=await uUQ(F.name,G,Q)();break;case"operationContextParams":D[G]=F.get(A);break;default:throw new Error("Unrecognized endpoint parameter instruction: "+JSON.stringify(F))}if(Object.keys(Z).length===0)Object.assign(D,Q);if(String(Q.serviceId).toLowerCase()==="s3")await xUQ(D);return D},"resolveParams"),dUQ=VB(),lE1=I5(),u$A=Gz(({config:A,instructions:B})=>{return(Q,D)=>async(Z)=>{if(A.endpoint)dUQ.setFeature(D,"ENDPOINT_OVERRIDE","N");let G=await h$A(Z.input,{getEndpointParameterInstructions(){return B}},{...A},D);D.endpointV2=G,D.authSchemes=G.properties?.authSchemes;let F=D.authSchemes?.[0];if(F){D.signing_region=F.signingRegion,D.signing_service=F.signingName;let Y=lE1.getSmithyContext(D)?.selectedHttpAuthScheme?.httpAuthOption;if(Y)Y.signingProperties=Object.assign(Y.signingProperties||{},{signing_region:F.signingRegion,signingRegion:F.signingRegion,signing_service:F.signingName,signingName:F.signingName,signingRegionSet:F.signingRegionSet},F.properties)}return Q({...Z})}},"endpointMiddleware"),cUQ=T3(),m$A={step:"serialize",tags:["ENDPOINT_PARAMETERS","ENDPOINT_V2","ENDPOINT"],name:"endpointV2Middleware",override:!0,relation:"before",toMiddleware:cUQ.serializerMiddlewareOption.name},lUQ=Gz((A,B)=>({applyToStack:(Q)=>{Q.addRelativeTo(u$A({config:A,instructions:B}),m$A)}}),"getEndpointPlugin"),pUQ=ot1(),iUQ=Gz((A)=>{let B=A.tls??!0,{endpoint:Q,useDualstackEndpoint:D,useFipsEndpoint:Z}=A,G=Q!=null?async()=>tt1(await lE1.normalizeProvider(Q)()):void 0,I=Object.assign(A,{endpoint:G,tls:B,isCustomEndpoint:!!Q,useDualstackEndpoint:lE1.normalizeProvider(D??!1),useFipsEndpoint:lE1.normalizeProvider(Z??!1)}),Y=void 0;return I.serviceConfiguredEndpoint=async()=>{if(A.serviceId&&!Y)Y=pUQ.getEndpointFromConfig(A.serviceId);return Y},I},"resolveEndpointConfig"),nUQ=Gz((A)=>{let{endpoint:B}=A;if(B===void 0)A.endpoint=async()=>{throw new Error("@smithy/middleware-endpoint: (default endpointRuleSet) endpoint is not set - you must configure an endpoint.")};return A},"resolveEndpointRequiredConfig")});
var qNA=E((wNA)=>{Object.defineProperty(wNA,"__esModule",{value:!0});wNA.isStreamingPayload=void 0;var bqQ=J1("stream"),fqQ=(A)=>(A===null||A===void 0?void 0:A.body)instanceof bqQ.Readable||typeof ReadableStream!=="undefined"&&(A===null||A===void 0?void 0:A.body)instanceof ReadableStream;wNA.isStreamingPayload=fqQ});
var qVA=E((wVA)=>{Object.defineProperty(wVA,"__esModule",{value:!0});wVA.createChecksumStream=void 0;var FGQ=Bn(),IGQ=By(),YGQ=UVA(),WGQ=({expectedChecksum:A,checksum:B,source:Q,checksumSourceLocation:D,base64Encoder:Z})=>{var G,F;if(!IGQ.isReadableStream(Q))throw new Error(`@smithy/util-stream: unsupported source type ${(F=(G=Q===null||Q===void 0?void 0:Q.constructor)===null||G===void 0?void 0:G.name)!==null&&F!==void 0?F:Q} in ChecksumStream.`);let I=Z!==null&&Z!==void 0?Z:FGQ.toBase64;if(typeof TransformStream!=="function")throw new Error("@smithy/util-stream: unable to instantiate ChecksumStream because API unavailable: ReadableStream/TransformStream.");let Y=new TransformStream({start(){},async transform(J,X){B.update(J),X.enqueue(J)},async flush(J){let X=await B.digest(),V=I(X);if(A!==V){let C=new Error(`Checksum mismatch: expected "${A}" but received "${V}" in response header "${D}".`);J.error(C)}else J.terminate()}});Q.pipeThrough(Y);let W=Y.readable;return Object.setPrototypeOf(W,YGQ.ChecksumStream.prototype),W};wVA.createChecksumStream=WGQ});
var rqA=E((aqA)=>{Object.defineProperty(aqA,"__esModule",{value:!0});aqA.default=void 0;var E$Q=U$Q(nQ1());function U$Q(A){return A&&A.__esModule?A:{default:A}}function w$Q(A){if(!E$Q.default(A))throw TypeError("Invalid UUID");return parseInt(A.slice(14,15),16)}var $$Q=w$Q;aqA.default=$$Q});
var sKA=E((nKA)=>{Object.defineProperty(nKA,"__esModule",{value:!0});nKA.fromBase64=void 0;var CWQ=AD(),KWQ=/^[A-Za-z0-9+/]*={0,2}$/,HWQ=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!KWQ.exec(A))throw new TypeError("Invalid base64 string.");let B=CWQ.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};nKA.fromBase64=HWQ});
var sQ1=E(($w)=>{Object.defineProperty($w,"__esModule",{value:!0});Object.defineProperty($w,"NIL",{enumerable:!0,get:function(){return R$Q.default}});Object.defineProperty($w,"parse",{enumerable:!0,get:function(){return S$Q.default}});Object.defineProperty($w,"stringify",{enumerable:!0,get:function(){return P$Q.default}});Object.defineProperty($w,"v1",{enumerable:!0,get:function(){return q$Q.default}});Object.defineProperty($w,"v3",{enumerable:!0,get:function(){return N$Q.default}});Object.defineProperty($w,"v4",{enumerable:!0,get:function(){return L$Q.default}});Object.defineProperty($w,"v5",{enumerable:!0,get:function(){return M$Q.default}});Object.defineProperty($w,"validate",{enumerable:!0,get:function(){return T$Q.default}});Object.defineProperty($w,"version",{enumerable:!0,get:function(){return O$Q.default}});var q$Q=hO(zqA()),N$Q=hO(SqA()),L$Q=hO(fqA()),M$Q=hO(lqA()),R$Q=hO(nqA()),O$Q=hO(rqA()),T$Q=hO(nQ1()),P$Q=hO(aQ1()),S$Q=hO(Ze1());function hO(A){return A&&A.__esModule?A:{default:A}}});
var tCA=E((G35,oCA)=>{var{defineProperty:bz1,getOwnPropertyDescriptor:HIQ,getOwnPropertyNames:zIQ}=Object,EIQ=Object.prototype.hasOwnProperty,UIQ=(A,B)=>bz1(A,"name",{value:B,configurable:!0}),wIQ=(A,B)=>{for(var Q in B)bz1(A,Q,{get:B[Q],enumerable:!0})},$IQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of zIQ(B))if(!EIQ.call(A,Z)&&Z!==Q)bz1(A,Z,{get:()=>B[Z],enumerable:!(D=HIQ(B,Z))||D.enumerable})}return A},qIQ=(A)=>$IQ(bz1({},"__esModule",{value:!0}),A),sCA={};wIQ(sCA,{buildQueryString:()=>rCA});oCA.exports=qIQ(sCA);var Uo1=aCA();function rCA(A){let B=[];for(let Q of Object.keys(A).sort()){let D=A[Q];if(Q=Uo1.escapeUri(Q),Array.isArray(D))for(let Z=0,G=D.length;Z<G;Z++)B.push(`${Q}=${Uo1.escapeUri(D[Z])}`);else{let Z=Q;if(D||typeof D==="string")Z+=`=${Uo1.escapeUri(D)}`;B.push(Z)}}return B.join("&")}UIQ(rCA,"buildQueryString")});
var tKA=E((rKA)=>{Object.defineProperty(rKA,"__esModule",{value:!0});rKA.toBase64=void 0;var zWQ=AD(),EWQ=cB(),UWQ=(A)=>{let B;if(typeof A==="string")B=EWQ.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return zWQ.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};rKA.toBase64=UWQ});
var tQ1=E((IZ5,_LA)=>{var{defineProperty:IU1,getOwnPropertyDescriptor:mLQ,getOwnPropertyNames:dLQ}=Object,cLQ=Object.prototype.hasOwnProperty,FU1=(A,B)=>IU1(A,"name",{value:B,configurable:!0}),lLQ=(A,B)=>{for(var Q in B)IU1(A,Q,{get:B[Q],enumerable:!0})},pLQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of dLQ(B))if(!cLQ.call(A,Z)&&Z!==Q)IU1(A,Z,{get:()=>B[Z],enumerable:!(D=mLQ(B,Z))||D.enumerable})}return A},iLQ=(A)=>pLQ(IU1({},"__esModule",{value:!0}),A),PLA={};lLQ(PLA,{NODE_APP_ID_CONFIG_OPTIONS:()=>oLQ,UA_APP_ID_ENV_NAME:()=>kLA,UA_APP_ID_INI_NAME:()=>yLA,createDefaultUserAgentProvider:()=>jLA,crtAvailability:()=>SLA,defaultUserAgent:()=>aLQ});_LA.exports=iLQ(PLA);var TLA=J1("os"),xe1=J1("process"),SLA={isCrtAvailable:!1},nLQ=FU1(()=>{if(SLA.isCrtAvailable)return["md/crt-avail"];return null},"isCrtAvailable"),jLA=FU1(({serviceId:A,clientVersion:B})=>{return async(Q)=>{let D=[["aws-sdk-js",B],["ua","2.1"],[`os/${TLA.platform()}`,TLA.release()],["lang/js"],["md/nodejs",`${xe1.versions.node}`]],Z=nLQ();if(Z)D.push(Z);if(A)D.push([`api/${A}`,B]);if(xe1.env.AWS_EXECUTION_ENV)D.push([`exec-env/${xe1.env.AWS_EXECUTION_ENV}`]);let G=await Q?.userAgentAppId?.();return G?[...D,[`app/${G}`]]:[...D]}},"createDefaultUserAgentProvider"),aLQ=jLA,sLQ=zn(),kLA="AWS_SDK_UA_APP_ID",yLA="sdk_ua_app_id",rLQ="sdk-ua-app-id",oLQ={environmentVariableSelector:FU1((A)=>A[kLA],"environmentVariableSelector"),configFileSelector:FU1((A)=>A[yLA]??A[rLQ],"configFileSelector"),default:sLQ.DEFAULT_UA_APP_ID}});
var u10=E((iZ5,g10)=>{var{defineProperty:RU1,getOwnPropertyDescriptor:TPQ,getOwnPropertyNames:PPQ}=Object,SPQ=Object.prototype.hasOwnProperty,A9=(A,B)=>RU1(A,"name",{value:B,configurable:!0}),jPQ=(A,B)=>{for(var Q in B)RU1(A,Q,{get:B[Q],enumerable:!0})},y10=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of PPQ(B))if(!SPQ.call(A,Z)&&Z!==Q)RU1(A,Z,{get:()=>B[Z],enumerable:!(D=TPQ(B,Z))||D.enumerable})}return A},kPQ=(A,B,Q)=>(y10(A,B,"default"),Q&&y10(Q,B,"default")),yPQ=(A)=>y10(RU1({},"__esModule",{value:!0}),A),x10={};jPQ(x10,{AssumeRoleCommand:()=>f10,AssumeRoleResponseFilterSensitiveLog:()=>fTA,AssumeRoleWithWebIdentityCommand:()=>h10,AssumeRoleWithWebIdentityRequestFilterSensitiveLog:()=>lTA,AssumeRoleWithWebIdentityResponseFilterSensitiveLog:()=>pTA,ClientInputEndpointParameters:()=>MSQ.ClientInputEndpointParameters,CredentialsFilterSensitiveLog:()=>v10,ExpiredTokenException:()=>hTA,IDPCommunicationErrorException:()=>iTA,IDPRejectedClaimException:()=>dTA,InvalidIdentityTokenException:()=>cTA,MalformedPolicyDocumentException:()=>gTA,PackedPolicyTooLargeException:()=>uTA,RegionDisabledException:()=>mTA,STS:()=>DPA,STSServiceException:()=>lO,decorateDefaultCredentialProvider:()=>TSQ,getDefaultRoleAssumer:()=>WPA,getDefaultRoleAssumerWithWebIdentity:()=>JPA});g10.exports=yPQ(x10);kPQ(x10,Y41(),g10.exports);var _PQ=x4(),xPQ=q6(),vPQ=T3(),bPQ=x4(),fPQ=W41(),bTA=x4(),hPQ=x4(),lO=class A extends hPQ.ServiceException{static{A9(this,"STSServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},v10=A9((A)=>({...A,...A.SecretAccessKey&&{SecretAccessKey:bTA.SENSITIVE_STRING}}),"CredentialsFilterSensitiveLog"),fTA=A9((A)=>({...A,...A.Credentials&&{Credentials:v10(A.Credentials)}}),"AssumeRoleResponseFilterSensitiveLog"),hTA=class A extends lO{static{A9(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},gTA=class A extends lO{static{A9(this,"MalformedPolicyDocumentException")}name="MalformedPolicyDocumentException";$fault="client";constructor(B){super({name:"MalformedPolicyDocumentException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},uTA=class A extends lO{static{A9(this,"PackedPolicyTooLargeException")}name="PackedPolicyTooLargeException";$fault="client";constructor(B){super({name:"PackedPolicyTooLargeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},mTA=class A extends lO{static{A9(this,"RegionDisabledException")}name="RegionDisabledException";$fault="client";constructor(B){super({name:"RegionDisabledException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},dTA=class A extends lO{static{A9(this,"IDPRejectedClaimException")}name="IDPRejectedClaimException";$fault="client";constructor(B){super({name:"IDPRejectedClaimException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},cTA=class A extends lO{static{A9(this,"InvalidIdentityTokenException")}name="InvalidIdentityTokenException";$fault="client";constructor(B){super({name:"InvalidIdentityTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},lTA=A9((A)=>({...A,...A.WebIdentityToken&&{WebIdentityToken:bTA.SENSITIVE_STRING}}),"AssumeRoleWithWebIdentityRequestFilterSensitiveLog"),pTA=A9((A)=>({...A,...A.Credentials&&{Credentials:v10(A.Credentials)}}),"AssumeRoleWithWebIdentityResponseFilterSensitiveLog"),iTA=class A extends lO{static{A9(this,"IDPCommunicationErrorException")}name="IDPCommunicationErrorException";$fault="client";constructor(B){super({name:"IDPCommunicationErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},b10=ZI(),gPQ=mJ(),Y5=x4(),uPQ=A9(async(A,B)=>{let Q=tTA,D;return D=QPA({...oPQ(A,B),[APA]:HSQ,[BPA]:eTA}),oTA(B,Q,"/",void 0,D)},"se_AssumeRoleCommand"),mPQ=A9(async(A,B)=>{let Q=tTA,D;return D=QPA({...tPQ(A,B),[APA]:zSQ,[BPA]:eTA}),oTA(B,Q,"/",void 0,D)},"se_AssumeRoleWithWebIdentityCommand"),dPQ=A9(async(A,B)=>{if(A.statusCode>=300)return nTA(A,B);let Q=await b10.parseXmlBody(A.body,B),D={};return D=GSQ(Q.AssumeRoleResult,B),{$metadata:pO(A),...D}},"de_AssumeRoleCommand"),cPQ=A9(async(A,B)=>{if(A.statusCode>=300)return nTA(A,B);let Q=await b10.parseXmlBody(A.body,B),D={};return D=FSQ(Q.AssumeRoleWithWebIdentityResult,B),{$metadata:pO(A),...D}},"de_AssumeRoleWithWebIdentityCommand"),nTA=A9(async(A,B)=>{let Q={...A,body:await b10.parseXmlErrorBody(A.body,B)},D=ESQ(A,Q.body);switch(D){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await lPQ(Q,B);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await aPQ(Q,B);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await sPQ(Q,B);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await rPQ(Q,B);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await pPQ(Q,B);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await iPQ(Q,B);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await nPQ(Q,B);default:let Z=Q.body;return KSQ({output:A,parsedBody:Z.Error,errorCode:D})}},"de_CommandError"),lPQ=A9(async(A,B)=>{let Q=A.body,D=ISQ(Q.Error,B),Z=new hTA({$metadata:pO(A),...D});return Y5.decorateServiceException(Z,Q)},"de_ExpiredTokenExceptionRes"),pPQ=A9(async(A,B)=>{let Q=A.body,D=YSQ(Q.Error,B),Z=new iTA({$metadata:pO(A),...D});return Y5.decorateServiceException(Z,Q)},"de_IDPCommunicationErrorExceptionRes"),iPQ=A9(async(A,B)=>{let Q=A.body,D=WSQ(Q.Error,B),Z=new dTA({$metadata:pO(A),...D});return Y5.decorateServiceException(Z,Q)},"de_IDPRejectedClaimExceptionRes"),nPQ=A9(async(A,B)=>{let Q=A.body,D=JSQ(Q.Error,B),Z=new cTA({$metadata:pO(A),...D});return Y5.decorateServiceException(Z,Q)},"de_InvalidIdentityTokenExceptionRes"),aPQ=A9(async(A,B)=>{let Q=A.body,D=XSQ(Q.Error,B),Z=new gTA({$metadata:pO(A),...D});return Y5.decorateServiceException(Z,Q)},"de_MalformedPolicyDocumentExceptionRes"),sPQ=A9(async(A,B)=>{let Q=A.body,D=VSQ(Q.Error,B),Z=new uTA({$metadata:pO(A),...D});return Y5.decorateServiceException(Z,Q)},"de_PackedPolicyTooLargeExceptionRes"),rPQ=A9(async(A,B)=>{let Q=A.body,D=CSQ(Q.Error,B),Z=new mTA({$metadata:pO(A),...D});return Y5.decorateServiceException(Z,Q)},"de_RegionDisabledExceptionRes"),oPQ=A9((A,B)=>{let Q={};if(A[bn]!=null)Q[bn]=A[bn];if(A[fn]!=null)Q[fn]=A[fn];if(A[xn]!=null){let D=aTA(A[xn],B);if(A[xn]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[_n]!=null)Q[_n]=A[_n];if(A[yn]!=null)Q[yn]=A[yn];if(A[O10]!=null){let D=ZSQ(A[O10],B);if(A[O10]?.length===0)Q.Tags=[];Object.entries(D).forEach(([Z,G])=>{let F=`Tags.${Z}`;Q[F]=G})}if(A[P10]!=null){let D=DSQ(A[P10],B);if(A[P10]?.length===0)Q.TransitiveTagKeys=[];Object.entries(D).forEach(([Z,G])=>{let F=`TransitiveTagKeys.${Z}`;Q[F]=G})}if(A[z10]!=null)Q[z10]=A[z10];if(A[M10]!=null)Q[M10]=A[M10];if(A[T10]!=null)Q[T10]=A[T10];if(A[cO]!=null)Q[cO]=A[cO];if(A[w10]!=null){let D=BSQ(A[w10],B);if(A[w10]?.length===0)Q.ProvidedContexts=[];Object.entries(D).forEach(([Z,G])=>{let F=`ProvidedContexts.${Z}`;Q[F]=G})}return Q},"se_AssumeRoleRequest"),tPQ=A9((A,B)=>{let Q={};if(A[bn]!=null)Q[bn]=A[bn];if(A[fn]!=null)Q[fn]=A[fn];if(A[j10]!=null)Q[j10]=A[j10];if(A[$10]!=null)Q[$10]=A[$10];if(A[xn]!=null){let D=aTA(A[xn],B);if(A[xn]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[_n]!=null)Q[_n]=A[_n];if(A[yn]!=null)Q[yn]=A[yn];return Q},"se_AssumeRoleWithWebIdentityRequest"),aTA=A9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=ePQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_policyDescriptorListType"),ePQ=A9((A,B)=>{let Q={};if(A[k10]!=null)Q[k10]=A[k10];return Q},"se_PolicyDescriptorType"),ASQ=A9((A,B)=>{let Q={};if(A[U10]!=null)Q[U10]=A[U10];if(A[K10]!=null)Q[K10]=A[K10];return Q},"se_ProvidedContext"),BSQ=A9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=ASQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_ProvidedContextsListType"),QSQ=A9((A,B)=>{let Q={};if(A[E10]!=null)Q[E10]=A[E10];if(A[S10]!=null)Q[S10]=A[S10];return Q},"se_Tag"),DSQ=A9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;Q[`member.${D}`]=Z,D++}return Q},"se_tagKeyListType"),ZSQ=A9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=QSQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_tagListType"),sTA=A9((A,B)=>{let Q={};if(A[X10]!=null)Q[X10]=Y5.expectString(A[X10]);if(A[V10]!=null)Q[V10]=Y5.expectString(A[V10]);return Q},"de_AssumedRoleUser"),GSQ=A9((A,B)=>{let Q={};if(A[kn]!=null)Q[kn]=rTA(A[kn],B);if(A[jn]!=null)Q[jn]=sTA(A[jn],B);if(A[vn]!=null)Q[vn]=Y5.strictParseInt32(A[vn]);if(A[cO]!=null)Q[cO]=Y5.expectString(A[cO]);return Q},"de_AssumeRoleResponse"),FSQ=A9((A,B)=>{let Q={};if(A[kn]!=null)Q[kn]=rTA(A[kn],B);if(A[L10]!=null)Q[L10]=Y5.expectString(A[L10]);if(A[jn]!=null)Q[jn]=sTA(A[jn],B);if(A[vn]!=null)Q[vn]=Y5.strictParseInt32(A[vn]);if(A[q10]!=null)Q[q10]=Y5.expectString(A[q10]);if(A[C10]!=null)Q[C10]=Y5.expectString(A[C10]);if(A[cO]!=null)Q[cO]=Y5.expectString(A[cO]);return Q},"de_AssumeRoleWithWebIdentityResponse"),rTA=A9((A,B)=>{let Q={};if(A[J10]!=null)Q[J10]=Y5.expectString(A[J10]);if(A[N10]!=null)Q[N10]=Y5.expectString(A[N10]);if(A[R10]!=null)Q[R10]=Y5.expectString(A[R10]);if(A[H10]!=null)Q[H10]=Y5.expectNonNull(Y5.parseRfc3339DateTimeWithOffset(A[H10]));return Q},"de_Credentials"),ISQ=A9((A,B)=>{let Q={};if(A[gZ]!=null)Q[gZ]=Y5.expectString(A[gZ]);return Q},"de_ExpiredTokenException"),YSQ=A9((A,B)=>{let Q={};if(A[gZ]!=null)Q[gZ]=Y5.expectString(A[gZ]);return Q},"de_IDPCommunicationErrorException"),WSQ=A9((A,B)=>{let Q={};if(A[gZ]!=null)Q[gZ]=Y5.expectString(A[gZ]);return Q},"de_IDPRejectedClaimException"),JSQ=A9((A,B)=>{let Q={};if(A[gZ]!=null)Q[gZ]=Y5.expectString(A[gZ]);return Q},"de_InvalidIdentityTokenException"),XSQ=A9((A,B)=>{let Q={};if(A[gZ]!=null)Q[gZ]=Y5.expectString(A[gZ]);return Q},"de_MalformedPolicyDocumentException"),VSQ=A9((A,B)=>{let Q={};if(A[gZ]!=null)Q[gZ]=Y5.expectString(A[gZ]);return Q},"de_PackedPolicyTooLargeException"),CSQ=A9((A,B)=>{let Q={};if(A[gZ]!=null)Q[gZ]=Y5.expectString(A[gZ]);return Q},"de_RegionDisabledException"),pO=A9((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),KSQ=Y5.withBaseException(lO),oTA=A9(async(A,B,Q,D,Z)=>{let{hostname:G,protocol:F="https",port:I,path:Y}=await A.endpoint(),W={protocol:F,hostname:G,port:I,method:"POST",path:Y.endsWith("/")?Y.slice(0,-1)+Q:Y+Q,headers:B};if(D!==void 0)W.hostname=D;if(Z!==void 0)W.body=Z;return new gPQ.HttpRequest(W)},"buildHttpRpcRequest"),tTA={"content-type":"application/x-www-form-urlencoded"},eTA="2011-06-15",APA="Action",J10="AccessKeyId",HSQ="AssumeRole",X10="AssumedRoleId",jn="AssumedRoleUser",zSQ="AssumeRoleWithWebIdentity",V10="Arn",C10="Audience",kn="Credentials",K10="ContextAssertion",yn="DurationSeconds",H10="Expiration",z10="ExternalId",E10="Key",_n="Policy",xn="PolicyArns",U10="ProviderArn",w10="ProvidedContexts",$10="ProviderId",vn="PackedPolicySize",q10="Provider",bn="RoleArn",fn="RoleSessionName",N10="SecretAccessKey",L10="SubjectFromWebIdentityToken",cO="SourceIdentity",M10="SerialNumber",R10="SessionToken",O10="Tags",T10="TokenCode",P10="TransitiveTagKeys",BPA="Version",S10="Value",j10="WebIdentityToken",k10="arn",gZ="message",QPA=A9((A)=>Object.entries(A).map(([B,Q])=>Y5.extendedEncodeURIComponent(B)+"="+Y5.extendedEncodeURIComponent(Q)).join("&"),"buildFormUrlencodedString"),ESQ=A9((A,B)=>{if(B.Error?.Code!==void 0)return B.Error.Code;if(A.statusCode==404)return"NotFound"},"loadQueryErrorCode"),f10=class extends bPQ.Command.classBuilder().ep(fPQ.commonParams).m(function(A,B,Q,D){return[vPQ.getSerdePlugin(Q,this.serialize,this.deserialize),xPQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRole",{}).n("STSClient","AssumeRoleCommand").f(void 0,fTA).ser(uPQ).de(dPQ).build(){static{A9(this,"AssumeRoleCommand")}},USQ=q6(),wSQ=T3(),$SQ=x4(),qSQ=W41(),h10=class extends $SQ.Command.classBuilder().ep(qSQ.commonParams).m(function(A,B,Q,D){return[wSQ.getSerdePlugin(Q,this.serialize,this.deserialize),USQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithWebIdentity",{}).n("STSClient","AssumeRoleWithWebIdentityCommand").f(lTA,pTA).ser(mPQ).de(cPQ).build(){static{A9(this,"AssumeRoleWithWebIdentityCommand")}},NSQ=Y41(),LSQ={AssumeRoleCommand:f10,AssumeRoleWithWebIdentityCommand:h10},DPA=class extends NSQ.STSClient{static{A9(this,"STS")}};_PQ.createAggregatedClient(LSQ,DPA);var MSQ=W41(),_10=Qz(),vTA="us-east-1",ZPA=A9((A)=>{if(typeof A?.Arn==="string"){let B=A.Arn.split(":");if(B.length>4&&B[4]!=="")return B[4]}return},"getAccountIdFromAssumedRoleUser"),GPA=A9(async(A,B,Q)=>{let D=typeof A==="function"?await A():A,Z=typeof B==="function"?await B():B;return Q?.debug?.("@aws-sdk/client-sts::resolveRegion","accepting first of:",`${D} (provider)`,`${Z} (parent client)`,`${vTA} (STS default)`),D??Z??vTA},"resolveRegion"),RSQ=A9((A,B)=>{let Q,D;return async(Z,G)=>{if(D=Z,!Q){let{logger:J=A?.parentClientConfig?.logger,region:X,requestHandler:V=A?.parentClientConfig?.requestHandler,credentialProviderLogger:C}=A,K=await GPA(X,A?.parentClientConfig?.region,C),H=!FPA(V);Q=new B({profile:A?.parentClientConfig?.profile,credentialDefaultProvider:A9(()=>async()=>D,"credentialDefaultProvider"),region:K,requestHandler:H?V:void 0,logger:J})}let{Credentials:F,AssumedRoleUser:I}=await Q.send(new f10(G));if(!F||!F.AccessKeyId||!F.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRole call with role ${G.RoleArn}`);let Y=ZPA(I),W={accessKeyId:F.AccessKeyId,secretAccessKey:F.SecretAccessKey,sessionToken:F.SessionToken,expiration:F.Expiration,...F.CredentialScope&&{credentialScope:F.CredentialScope},...Y&&{accountId:Y}};return _10.setCredentialFeature(W,"CREDENTIALS_STS_ASSUME_ROLE","i"),W}},"getDefaultRoleAssumer"),OSQ=A9((A,B)=>{let Q;return async(D)=>{if(!Q){let{logger:Y=A?.parentClientConfig?.logger,region:W,requestHandler:J=A?.parentClientConfig?.requestHandler,credentialProviderLogger:X}=A,V=await GPA(W,A?.parentClientConfig?.region,X),C=!FPA(J);Q=new B({profile:A?.parentClientConfig?.profile,region:V,requestHandler:C?J:void 0,logger:Y})}let{Credentials:Z,AssumedRoleUser:G}=await Q.send(new h10(D));if(!Z||!Z.AccessKeyId||!Z.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${D.RoleArn}`);let F=ZPA(G),I={accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.SessionToken,expiration:Z.Expiration,...Z.CredentialScope&&{credentialScope:Z.CredentialScope},...F&&{accountId:F}};if(F)_10.setCredentialFeature(I,"RESOLVED_ACCOUNT_ID","T");return _10.setCredentialFeature(I,"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID","k"),I}},"getDefaultRoleAssumerWithWebIdentity"),FPA=A9((A)=>{return A?.metadata?.handlerProtocol==="h2"},"isH2"),IPA=Y41(),YPA=A9((A,B)=>{if(!B)return A;else return class Q extends A{static{A9(this,"CustomizableSTSClient")}constructor(D){super(D);for(let Z of B)this.middlewareStack.use(Z)}}},"getCustomizableStsClientCtor"),WPA=A9((A={},B)=>RSQ(A,YPA(IPA.STSClient,B)),"getDefaultRoleAssumer"),JPA=A9((A={},B)=>OSQ(A,YPA(IPA.STSClient,B)),"getDefaultRoleAssumerWithWebIdentity"),TSQ=A9((A)=>(B)=>A({roleAssumer:WPA(B),roleAssumerWithWebIdentity:JPA(B),...B}),"decorateDefaultCredentialProvider")});
var uVA=E((gVA)=>{Object.defineProperty(gVA,"__esModule",{value:!0});gVA.headStream=TGQ;async function TGQ(A,B){var Q;let D=0,Z=[],G=A.getReader(),F=!1;while(!F){let{done:W,value:J}=await G.read();if(J)Z.push(J),D+=(Q=J===null||J===void 0?void 0:J.byteLength)!==null&&Q!==void 0?Q:0;if(D>=B)break;F=W}G.releaseLock();let I=new Uint8Array(Math.min(B,D)),Y=0;for(let W of Z){if(W.byteLength>I.byteLength-Y){I.set(W.subarray(0,I.byteLength-Y),Y);break}else I.set(W,Y);Y+=W.length}return I}});
var uqA=E((hqA)=>{Object.defineProperty(hqA,"__esModule",{value:!0});hqA.default=void 0;var Y$Q=W$Q(J1("crypto"));function W$Q(A){return A&&A.__esModule?A:{default:A}}function J$Q(A){if(Array.isArray(A))A=Buffer.from(A);else if(typeof A==="string")A=Buffer.from(A,"utf8");return Y$Q.default.createHash("sha1").update(A).digest()}var X$Q=J$Q;hqA.default=X$Q});
var v4=E((iD5,bNA)=>{var{defineProperty:AU1,getOwnPropertyDescriptor:hqQ,getOwnPropertyNames:gqQ}=Object,uqQ=Object.prototype.hasOwnProperty,PG=(A,B)=>AU1(A,"name",{value:B,configurable:!0}),mqQ=(A,B)=>{for(var Q in B)AU1(A,Q,{get:B[Q],enumerable:!0})},dqQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of gqQ(B))if(!uqQ.call(A,Z)&&Z!==Q)AU1(A,Z,{get:()=>B[Z],enumerable:!(D=hqQ(B,Z))||D.enumerable})}return A},cqQ=(A)=>dqQ(AU1({},"__esModule",{value:!0}),A),LNA={};mqQ(LNA,{AdaptiveRetryStrategy:()=>iqQ,CONFIG_MAX_ATTEMPTS:()=>$e1,CONFIG_RETRY_MODE:()=>jNA,ENV_MAX_ATTEMPTS:()=>we1,ENV_RETRY_MODE:()=>SNA,NODE_MAX_ATTEMPT_CONFIG_OPTIONS:()=>nqQ,NODE_RETRY_MODE_CONFIG_OPTIONS:()=>sqQ,StandardRetryStrategy:()=>PNA,defaultDelayDecider:()=>RNA,defaultRetryDecider:()=>ONA,getOmitRetryHeadersPlugin:()=>rqQ,getRetryAfterHint:()=>vNA,getRetryPlugin:()=>QNQ,omitRetryHeadersMiddleware:()=>kNA,omitRetryHeadersMiddlewareOptions:()=>yNA,resolveRetryConfig:()=>aqQ,retryMiddleware:()=>_NA,retryMiddlewareOptions:()=>xNA});bNA.exports=cqQ(LNA);var wn=DqA(),MNA=sQ1(),_D=hZ(),lqQ=PG((A,B)=>{let Q=A,D=B?.noRetryIncrement??_D.NO_RETRY_INCREMENT,Z=B?.retryCost??_D.RETRY_COST,G=B?.timeoutRetryCost??_D.TIMEOUT_RETRY_COST,F=A,I=PG((X)=>X.name==="TimeoutError"?G:Z,"getCapacityAmount"),Y=PG((X)=>I(X)<=F,"hasRetryTokens");return Object.freeze({hasRetryTokens:Y,retrieveRetryTokens:PG((X)=>{if(!Y(X))throw new Error("No retry token available");let V=I(X);return F-=V,V},"retrieveRetryTokens"),releaseRetryTokens:PG((X)=>{F+=X??D,F=Math.min(F,Q)},"releaseRetryTokens")})},"getDefaultRetryQuota"),RNA=PG((A,B)=>Math.floor(Math.min(_D.MAXIMUM_RETRY_DELAY,Math.random()*2**B*A)),"defaultDelayDecider"),Hy=Ie1(),ONA=PG((A)=>{if(!A)return!1;return Hy.isRetryableByTrait(A)||Hy.isClockSkewError(A)||Hy.isThrottlingError(A)||Hy.isTransientError(A)},"defaultRetryDecider"),TNA=PG((A)=>{if(A instanceof Error)return A;if(A instanceof Object)return Object.assign(new Error,A);if(typeof A==="string")return new Error(A);return new Error(`AWS SDK error wrapper for ${A}`)},"asSdkError"),PNA=class{constructor(A,B){this.maxAttemptsProvider=A,this.mode=_D.RETRY_MODES.STANDARD,this.retryDecider=B?.retryDecider??ONA,this.delayDecider=B?.delayDecider??RNA,this.retryQuota=B?.retryQuota??lqQ(_D.INITIAL_RETRY_TOKENS)}static{PG(this,"StandardRetryStrategy")}shouldRetry(A,B,Q){return B<Q&&this.retryDecider(A)&&this.retryQuota.hasRetryTokens(A)}async getMaxAttempts(){let A;try{A=await this.maxAttemptsProvider()}catch(B){A=_D.DEFAULT_MAX_ATTEMPTS}return A}async retry(A,B,Q){let D,Z=0,G=0,F=await this.getMaxAttempts(),{request:I}=B;if(wn.HttpRequest.isInstance(I))I.headers[_D.INVOCATION_ID_HEADER]=MNA.v4();while(!0)try{if(wn.HttpRequest.isInstance(I))I.headers[_D.REQUEST_HEADER]=`attempt=${Z+1}; max=${F}`;if(Q?.beforeRequest)await Q.beforeRequest();let{response:Y,output:W}=await A(B);if(Q?.afterRequest)Q.afterRequest(Y);return this.retryQuota.releaseRetryTokens(D),W.$metadata.attempts=Z+1,W.$metadata.totalRetryDelay=G,{response:Y,output:W}}catch(Y){let W=TNA(Y);if(Z++,this.shouldRetry(W,Z,F)){D=this.retryQuota.retrieveRetryTokens(W);let J=this.delayDecider(Hy.isThrottlingError(W)?_D.THROTTLING_RETRY_DELAY_BASE:_D.DEFAULT_RETRY_DELAY_BASE,Z),X=pqQ(W.$response),V=Math.max(X||0,J);G+=V,await new Promise((C)=>setTimeout(C,V));continue}if(!W.$metadata)W.$metadata={};throw W.$metadata.attempts=Z,W.$metadata.totalRetryDelay=G,W}}},pqQ=PG((A)=>{if(!wn.HttpResponse.isInstance(A))return;let B=Object.keys(A.headers).find((G)=>G.toLowerCase()==="retry-after");if(!B)return;let Q=A.headers[B],D=Number(Q);if(!Number.isNaN(D))return D*1000;return new Date(Q).getTime()-Date.now()},"getDelayFromRetryAfterHeader"),iqQ=class extends PNA{static{PG(this,"AdaptiveRetryStrategy")}constructor(A,B){let{rateLimiter:Q,...D}=B??{};super(A,D);this.rateLimiter=Q??new _D.DefaultRateLimiter,this.mode=_D.RETRY_MODES.ADAPTIVE}async retry(A,B){return super.retry(A,B,{beforeRequest:async()=>{return this.rateLimiter.getSendToken()},afterRequest:(Q)=>{this.rateLimiter.updateClientSendingRate(Q)}})}},NNA=I5(),we1="AWS_MAX_ATTEMPTS",$e1="max_attempts",nqQ={environmentVariableSelector:(A)=>{let B=A[we1];if(!B)return;let Q=parseInt(B);if(Number.isNaN(Q))throw new Error(`Environment variable ${we1} mast be a number, got "${B}"`);return Q},configFileSelector:(A)=>{let B=A[$e1];if(!B)return;let Q=parseInt(B);if(Number.isNaN(Q))throw new Error(`Shared config file entry ${$e1} mast be a number, got "${B}"`);return Q},default:_D.DEFAULT_MAX_ATTEMPTS},aqQ=PG((A)=>{let{retryStrategy:B,retryMode:Q,maxAttempts:D}=A,Z=NNA.normalizeProvider(D??_D.DEFAULT_MAX_ATTEMPTS);return Object.assign(A,{maxAttempts:Z,retryStrategy:async()=>{if(B)return B;if(await NNA.normalizeProvider(Q)()===_D.RETRY_MODES.ADAPTIVE)return new _D.AdaptiveRetryStrategy(Z);return new _D.StandardRetryStrategy(Z)}})},"resolveRetryConfig"),SNA="AWS_RETRY_MODE",jNA="retry_mode",sqQ={environmentVariableSelector:(A)=>A[SNA],configFileSelector:(A)=>A[jNA],default:_D.DEFAULT_RETRY_MODE},kNA=PG(()=>(A)=>async(B)=>{let{request:Q}=B;if(wn.HttpRequest.isInstance(Q))delete Q.headers[_D.INVOCATION_ID_HEADER],delete Q.headers[_D.REQUEST_HEADER];return A(B)},"omitRetryHeadersMiddleware"),yNA={name:"omitRetryHeadersMiddleware",tags:["RETRY","HEADERS","OMIT_RETRY_HEADERS"],relation:"before",toMiddleware:"awsAuthMiddleware",override:!0},rqQ=PG((A)=>({applyToStack:(B)=>{B.addRelativeTo(kNA(),yNA)}}),"getOmitRetryHeadersPlugin"),oqQ=UNA(),tqQ=qNA(),_NA=PG((A)=>(B,Q)=>async(D)=>{let Z=await A.retryStrategy(),G=await A.maxAttempts();if(eqQ(Z)){Z=Z;let F=await Z.acquireInitialRetryToken(Q.partition_id),I=new Error,Y=0,W=0,{request:J}=D,X=wn.HttpRequest.isInstance(J);if(X)J.headers[_D.INVOCATION_ID_HEADER]=MNA.v4();while(!0)try{if(X)J.headers[_D.REQUEST_HEADER]=`attempt=${Y+1}; max=${G}`;let{response:V,output:C}=await B(D);return Z.recordSuccess(F),C.$metadata.attempts=Y+1,C.$metadata.totalRetryDelay=W,{response:V,output:C}}catch(V){let C=ANQ(V);if(I=TNA(V),X&&tqQ.isStreamingPayload(J))throw(Q.logger instanceof oqQ.NoOpLogger?console:Q.logger)?.warn("An error was encountered in a non-retryable streaming request."),I;try{F=await Z.refreshRetryTokenForRetry(F,C)}catch(H){if(!I.$metadata)I.$metadata={};throw I.$metadata.attempts=Y+1,I.$metadata.totalRetryDelay=W,I}Y=F.getRetryCount();let K=F.getRetryDelay();W+=K,await new Promise((H)=>setTimeout(H,K))}}else{if(Z=Z,Z?.mode)Q.userAgent=[...Q.userAgent||[],["cfg/retry-mode",Z.mode]];return Z.retry(B,D)}},"retryMiddleware"),eqQ=PG((A)=>typeof A.acquireInitialRetryToken!=="undefined"&&typeof A.refreshRetryTokenForRetry!=="undefined"&&typeof A.recordSuccess!=="undefined","isRetryStrategyV2"),ANQ=PG((A)=>{let B={error:A,errorType:BNQ(A)},Q=vNA(A.$response);if(Q)B.retryAfterHint=Q;return B},"getRetryErrorInfo"),BNQ=PG((A)=>{if(Hy.isThrottlingError(A))return"THROTTLING";if(Hy.isTransientError(A))return"TRANSIENT";if(Hy.isServerError(A))return"SERVER_ERROR";return"CLIENT_ERROR"},"getRetryErrorType"),xNA={name:"retryMiddleware",tags:["RETRY"],step:"finalizeRequest",priority:"high",override:!0},QNQ=PG((A)=>({applyToStack:(B)=>{B.add(_NA(A),xNA)}}),"getRetryPlugin"),vNA=PG((A)=>{if(!wn.HttpResponse.isInstance(A))return;let B=Object.keys(A.headers).find((G)=>G.toLowerCase()==="retry-after");if(!B)return;let Q=A.headers[B],D=Number(Q);if(!Number.isNaN(D))return new Date(D*1000);return new Date(Q)},"getRetryAfterHint")});
var vEA=E((_EA)=>{Object.defineProperty(_EA,"__esModule",{value:!0});_EA.fromBase64=void 0;var yVQ=AD(),_VQ=/^[A-Za-z0-9+/]*={0,2}$/,xVQ=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!_VQ.exec(A))throw new TypeError("Invalid base64 string.");let B=yVQ.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};_EA.fromBase64=xVQ});
var vHA=E((u35,xHA)=>{var{defineProperty:rz1,getOwnPropertyDescriptor:XJQ,getOwnPropertyNames:VJQ}=Object,CJQ=Object.prototype.hasOwnProperty,oz1=(A,B)=>rz1(A,"name",{value:B,configurable:!0}),KJQ=(A,B)=>{for(var Q in B)rz1(A,Q,{get:B[Q],enumerable:!0})},HJQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of VJQ(B))if(!CJQ.call(A,Z)&&Z!==Q)rz1(A,Z,{get:()=>B[Z],enumerable:!(D=XJQ(B,Z))||D.enumerable})}return A},zJQ=(A)=>HJQ(rz1({},"__esModule",{value:!0}),A),OHA={};KJQ(OHA,{AlgorithmId:()=>jHA,EndpointURLScheme:()=>SHA,FieldPosition:()=>kHA,HttpApiKeyAuthLocation:()=>PHA,HttpAuthLocation:()=>THA,IniSectionType:()=>yHA,RequestHandlerProtocol:()=>_HA,SMITHY_CONTEXT_KEY:()=>qJQ,getDefaultClientConfiguration:()=>wJQ,resolveDefaultRuntimeConfig:()=>$JQ});xHA.exports=zJQ(OHA);var THA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(THA||{}),PHA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(PHA||{}),SHA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(SHA||{}),jHA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(jHA||{}),EJQ=oz1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),UJQ=oz1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),wJQ=oz1((A)=>{return EJQ(A)},"getDefaultClientConfiguration"),$JQ=oz1((A)=>{return UJQ(A)},"resolveDefaultRuntimeConfig"),kHA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(kHA||{}),qJQ="__smithy_context",yHA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(yHA||{}),_HA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(_HA||{})});
var vPA=E((BG5,xPA)=>{var{create:$jQ,defineProperty:V41,getOwnPropertyDescriptor:qjQ,getOwnPropertyNames:NjQ,getPrototypeOf:LjQ}=Object,MjQ=Object.prototype.hasOwnProperty,PU1=(A,B)=>V41(A,"name",{value:B,configurable:!0}),RjQ=(A,B)=>{for(var Q in B)V41(A,Q,{get:B[Q],enumerable:!0})},jPA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of NjQ(B))if(!MjQ.call(A,Z)&&Z!==Q)V41(A,Z,{get:()=>B[Z],enumerable:!(D=qjQ(B,Z))||D.enumerable})}return A},hn=(A,B,Q)=>(Q=A!=null?$jQ(LjQ(A)):{},jPA(B||!A||!A.__esModule?V41(Q,"default",{value:A,enumerable:!0}):Q,A)),OjQ=(A)=>jPA(V41({},"__esModule",{value:!0}),A),kPA={};RjQ(kPA,{credentialsTreatedAsExpired:()=>_PA,credentialsWillNeedRefresh:()=>yPA,defaultProvider:()=>SjQ});xPA.exports=OjQ(kPA);var r10=Le1(),TjQ=e5(),dh=eB(),PPA="AWS_EC2_METADATA_DISABLED",PjQ=PU1(async(A)=>{let{ENV_CMDS_FULL_URI:B,ENV_CMDS_RELATIVE_URI:Q,fromContainerMetadata:D,fromInstanceMetadata:Z}=await Promise.resolve().then(()=>hn($F()));if(process.env[Q]||process.env[B]){A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromHttp/fromContainerMetadata");let{fromHttp:G}=await Promise.resolve().then(()=>hn(ke1()));return dh.chain(G(A),D(A))}if(process.env[PPA]&&process.env[PPA]!=="false")return async()=>{throw new dh.CredentialsProviderError("EC2 Instance Metadata Service access disabled",{logger:A.logger})};return A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromInstanceMetadata"),Z(A)},"remoteProvider"),SPA=!1,SjQ=PU1((A={})=>dh.memoize(dh.chain(async()=>{if(A.profile??process.env[TjQ.ENV_PROFILE]){if(process.env[r10.ENV_KEY]&&process.env[r10.ENV_SECRET]){if(!SPA)(A.logger?.warn&&A.logger?.constructor?.name!=="NoOpLogger"?A.logger.warn:console.warn)(`@aws-sdk/credential-provider-node - defaultProvider::fromEnv WARNING:
    Multiple credential sources detected: 
    Both AWS_PROFILE and the pair AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY static credentials are set.
    This SDK will proceed with the AWS_PROFILE value.
    
    However, a future version may change this behavior to prefer the ENV static credentials.
    Please ensure that your environment only sets either the AWS_PROFILE or the
    AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY pair.
`),SPA=!0}throw new dh.CredentialsProviderError("AWS_PROFILE is set, skipping fromEnv provider.",{logger:A.logger,tryNextLink:!0})}return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromEnv"),r10.fromEnv(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromSSO");let{ssoStartUrl:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoSession:G}=A;if(!B&&!Q&&!D&&!Z&&!G)throw new dh.CredentialsProviderError("Skipping SSO provider in default chain (inputs do not include SSO fields).",{logger:A.logger});let{fromSSO:F}=await Promise.resolve().then(()=>hn(A10()));return F(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromIni");let{fromIni:B}=await Promise.resolve().then(()=>hn(TPA()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromProcess");let{fromProcess:B}=await Promise.resolve().then(()=>hn(c10()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromTokenFile");let{fromTokenFile:B}=await Promise.resolve().then(()=>hn(n10()));return B(A)()},async()=>{return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::remoteProvider"),(await PjQ(A))()},async()=>{throw new dh.CredentialsProviderError("Could not load credentials from any providers",{tryNextLink:!1,logger:A.logger})}),_PA,yPA),"defaultProvider"),yPA=PU1((A)=>A?.expiration!==void 0,"credentialsWillNeedRefresh"),_PA=PU1((A)=>A?.expiration!==void 0&&A.expiration.getTime()-Date.now()<300000,"credentialsTreatedAsExpired")});
var vVA=E((xVA)=>{Object.defineProperty(xVA,"__esModule",{value:!0});xVA.createBufferedReadable=LGQ;var qGQ=J1("node:stream"),_VA=Xo1(),vO=yVA(),NGQ=By();function LGQ(A,B,Q){if(NGQ.isReadableStream(A))return vO.createBufferedReadableStream(A,B,Q);let D=new qGQ.Readable({read(){}}),Z=!1,G=0,F=["",new _VA.ByteArrayCollector((Y)=>new Uint8Array(Y)),new _VA.ByteArrayCollector((Y)=>Buffer.from(new Uint8Array(Y)))],I=-1;return A.on("data",(Y)=>{let W=vO.modeOf(Y,!0);if(I!==W){if(I>=0)D.push(vO.flush(F,I));I=W}if(I===-1){D.push(Y);return}let J=vO.sizeOf(Y);G+=J;let X=vO.sizeOf(F[I]);if(J>=B&&X===0)D.push(Y);else{let V=vO.merge(F,I,Y);if(!Z&&G>B*2)Z=!0,Q===null||Q===void 0||Q.warn(`@smithy/util-stream - stream chunk size ${J} is below threshold of ${B}, automatically buffering.`);if(V>=B)D.push(vO.flush(F,I))}}),A.on("end",()=>{if(I!==-1){let Y=vO.flush(F,I);if(vO.sizeOf(Y)>0)D.push(Y)}D.push(null)}),D}});
var vwA=E((s75,xwA)=>{var{defineProperty:yE1,getOwnPropertyDescriptor:PzQ,getOwnPropertyNames:SzQ}=Object,jzQ=Object.prototype.hasOwnProperty,_E1=(A,B)=>yE1(A,"name",{value:B,configurable:!0}),kzQ=(A,B)=>{for(var Q in B)yE1(A,Q,{get:B[Q],enumerable:!0})},yzQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of SzQ(B))if(!jzQ.call(A,Z)&&Z!==Q)yE1(A,Z,{get:()=>B[Z],enumerable:!(D=PzQ(B,Z))||D.enumerable})}return A},_zQ=(A)=>yzQ(yE1({},"__esModule",{value:!0}),A),OwA={};kzQ(OwA,{AlgorithmId:()=>jwA,EndpointURLScheme:()=>SwA,FieldPosition:()=>kwA,HttpApiKeyAuthLocation:()=>PwA,HttpAuthLocation:()=>TwA,IniSectionType:()=>ywA,RequestHandlerProtocol:()=>_wA,SMITHY_CONTEXT_KEY:()=>hzQ,getDefaultClientConfiguration:()=>bzQ,resolveDefaultRuntimeConfig:()=>fzQ});xwA.exports=_zQ(OwA);var TwA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(TwA||{}),PwA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(PwA||{}),SwA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(SwA||{}),jwA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(jwA||{}),xzQ=_E1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),vzQ=_E1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),bzQ=_E1((A)=>{return xzQ(A)},"getDefaultClientConfiguration"),fzQ=_E1((A)=>{return vzQ(A)},"resolveDefaultRuntimeConfig"),kwA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(kwA||{}),hzQ="__smithy_context",ywA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(ywA||{}),_wA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(_wA||{})});
var wOA=E((xZ5,UOA)=>{var{create:YTQ,defineProperty:F41,getOwnPropertyDescriptor:WTQ,getOwnPropertyNames:JTQ,getPrototypeOf:XTQ}=Object,VTQ=Object.prototype.hasOwnProperty,dO=(A,B)=>F41(A,"name",{value:B,configurable:!0}),CTQ=(A,B)=>{for(var Q in B)F41(A,Q,{get:B[Q],enumerable:!0})},KOA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of JTQ(B))if(!VTQ.call(A,Z)&&Z!==Q)F41(A,Z,{get:()=>B[Z],enumerable:!(D=WTQ(B,Z))||D.enumerable})}return A},HOA=(A,B,Q)=>(Q=A!=null?YTQ(XTQ(A)):{},KOA(B||!A||!A.__esModule?F41(Q,"default",{value:A,enumerable:!0}):Q,A)),KTQ=(A)=>KOA(F41({},"__esModule",{value:!0}),A),zOA={};CTQ(zOA,{fromEnvSigningName:()=>ETQ,fromSso:()=>EOA,fromStatic:()=>MTQ,nodeProvider:()=>RTQ});UOA.exports=KTQ(zOA);var HTQ=Qz(),zTQ=Ht1(),sC=eB(),ETQ=dO(({logger:A,signingName:B}={})=>async()=>{if(A?.debug?.("@aws-sdk/token-providers - fromEnvSigningName"),!B)throw new sC.TokenProviderError("Please pass 'signingName' to compute environment variable key",{logger:A});let Q=zTQ.getBearerTokenEnvKey(B);if(!(Q in process.env))throw new sC.TokenProviderError(`Token not present in '${Q}' environment variable`,{logger:A});let D={token:process.env[Q]};return HTQ.setTokenFeature(D,"BEARER_SERVICE_ENV_VARS","3"),D},"fromEnvSigningName"),UTQ=300000,te1="To refresh this SSO session run 'aws sso login' with the corresponding profile.",wTQ=dO(async(A,B={})=>{let{SSOOIDCClient:Q}=await Promise.resolve().then(()=>HOA(oe1()));return new Q(Object.assign({},B.clientConfig??{},{region:A??B.clientConfig?.region,logger:B.clientConfig?.logger??B.parentClientConfig?.logger}))},"getSsoOidcClient"),$TQ=dO(async(A,B,Q={})=>{let{CreateTokenCommand:D}=await Promise.resolve().then(()=>HOA(oe1()));return(await wTQ(B,Q)).send(new D({clientId:A.clientId,clientSecret:A.clientSecret,refreshToken:A.refreshToken,grantType:"refresh_token"}))},"getNewSsoOidcToken"),VOA=dO((A)=>{if(A.expiration&&A.expiration.getTime()<Date.now())throw new sC.TokenProviderError(`Token is expired. ${te1}`,!1)},"validateTokenExpiry"),uh=dO((A,B,Q=!1)=>{if(typeof B==="undefined")throw new sC.TokenProviderError(`Value not present for '${A}' in SSO Token${Q?". Cannot refresh":""}. ${te1}`,!1)},"validateTokenKey"),G41=e5(),qTQ=J1("fs"),{writeFile:NTQ}=qTQ.promises,LTQ=dO((A,B)=>{let Q=G41.getSSOTokenFilepath(A),D=JSON.stringify(B,null,2);return NTQ(Q,D)},"writeSSOTokenToFile"),COA=new Date(0),EOA=dO((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/token-providers - fromSso");let D=await G41.parseKnownFiles(Q),Z=G41.getProfileName({profile:Q.profile??B?.profile}),G=D[Z];if(!G)throw new sC.TokenProviderError(`Profile '${Z}' could not be found in shared credentials file.`,!1);else if(!G.sso_session)throw new sC.TokenProviderError(`Profile '${Z}' is missing required property 'sso_session'.`);let F=G.sso_session,Y=(await G41.loadSsoSessionData(Q))[F];if(!Y)throw new sC.TokenProviderError(`Sso session '${F}' could not be found in shared credentials file.`,!1);for(let H of["sso_start_url","sso_region"])if(!Y[H])throw new sC.TokenProviderError(`Sso session '${F}' is missing required property '${H}'.`,!1);let{sso_start_url:W,sso_region:J}=Y,X;try{X=await G41.getSSOTokenFromFile(F)}catch(H){throw new sC.TokenProviderError(`The SSO session token associated with profile=${Z} was not found or is invalid. ${te1}`,!1)}uh("accessToken",X.accessToken),uh("expiresAt",X.expiresAt);let{accessToken:V,expiresAt:C}=X,K={token:V,expiration:new Date(C)};if(K.expiration.getTime()-Date.now()>UTQ)return K;if(Date.now()-COA.getTime()<30000)return VOA(K),K;uh("clientId",X.clientId,!0),uh("clientSecret",X.clientSecret,!0),uh("refreshToken",X.refreshToken,!0);try{COA.setTime(Date.now());let H=await $TQ(X,J,Q);uh("accessToken",H.accessToken),uh("expiresIn",H.expiresIn);let z=new Date(Date.now()+H.expiresIn*1000);try{await LTQ(F,{...X,accessToken:H.accessToken,expiresAt:z.toISOString(),refreshToken:H.refreshToken})}catch($){}return{token:H.accessToken,expiration:z}}catch(H){return VOA(K),K}},"fromSso"),MTQ=dO(({token:A,logger:B})=>async()=>{if(B?.debug("@aws-sdk/token-providers - fromStatic"),!A||!A.token)throw new sC.TokenProviderError("Please pass a valid token to fromStatic",!1);return A},"fromStatic"),RTQ=dO((A={})=>sC.memoize(sC.chain(EOA(A),async()=>{throw new sC.TokenProviderError("Could not load token from any providers",!1)}),(B)=>B.expiration!==void 0&&B.expiration.getTime()-Date.now()<300000,(B)=>B.expiration!==void 0),"nodeProvider")});
var wUA=E((SKQ)=>{function RKQ(A,B){return UUA(A,B)}function UUA(A,B,Q){let D,Z={};for(let G=0;G<A.length;G++){let F=A[G],I=OKQ(F),Y="";if(Q===void 0)Y=I;else Y=Q+"."+I;if(I===B.textNodeName)if(D===void 0)D=F[I];else D+=""+F[I];else if(I===void 0)continue;else if(F[I]){let W=UUA(F[I],B,Y),J=PKQ(W,B);if(F[":@"])TKQ(W,F[":@"],Y,B);else if(Object.keys(W).length===1&&W[B.textNodeName]!==void 0&&!B.alwaysCreateTextNode)W=W[B.textNodeName];else if(Object.keys(W).length===0)if(B.alwaysCreateTextNode)W[B.textNodeName]="";else W="";if(Z[I]!==void 0&&Z.hasOwnProperty(I)){if(!Array.isArray(Z[I]))Z[I]=[Z[I]];Z[I].push(W)}else if(B.isArray(I,Y,J))Z[I]=[W];else Z[I]=W}}if(typeof D==="string"){if(D.length>0)Z[B.textNodeName]=D}else if(D!==void 0)Z[B.textNodeName]=D;return Z}function OKQ(A){let B=Object.keys(A);for(let Q=0;Q<B.length;Q++){let D=B[Q];if(D!==":@")return D}}function TKQ(A,B,Q,D){if(B){let Z=Object.keys(B),G=Z.length;for(let F=0;F<G;F++){let I=Z[F];if(D.isArray(I,Q+"."+I,!0,!0))A[I]=[B[I]];else A[I]=B[I]}}}function PKQ(A,B){let{textNodeName:Q}=B,D=Object.keys(A).length;if(D===0)return!0;if(D===1&&(A[Q]||typeof A[Q]==="boolean"||A[Q]===0))return!0;return!1}SKQ.prettify=RKQ});
var wY=E((D75,yEA)=>{var{defineProperty:UE1,getOwnPropertyDescriptor:MVQ,getOwnPropertyNames:RVQ}=Object,OVQ=Object.prototype.hasOwnProperty,TVQ=(A,B)=>UE1(A,"name",{value:B,configurable:!0}),PVQ=(A,B)=>{for(var Q in B)UE1(A,Q,{get:B[Q],enumerable:!0})},SVQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of RVQ(B))if(!OVQ.call(A,Z)&&Z!==Q)UE1(A,Z,{get:()=>B[Z],enumerable:!(D=MVQ(B,Z))||D.enumerable})}return A},jVQ=(A)=>SVQ(UE1({},"__esModule",{value:!0}),A),kEA={};PVQ(kEA,{calculateBodyLength:()=>kVQ});yEA.exports=jVQ(kEA);var jEA=typeof TextEncoder=="function"?new TextEncoder:null,kVQ=TVQ((A)=>{if(typeof A==="string"){if(jEA)return jEA.encode(A).byteLength;let B=A.length;for(let Q=B-1;Q>=0;Q--){let D=A.charCodeAt(Q);if(D>127&&D<=2047)B++;else if(D>2047&&D<=65535)B+=2;if(D>=56320&&D<=57343)Q--}return B}else if(typeof A.byteLength==="number")return A.byteLength;else if(typeof A.size==="number")return A.size;throw new Error(`Body Length computation failed for ${A}`)},"calculateBodyLength")});
var wjA=E((JG5,g00)=>{var{defineProperty:jU1,getOwnPropertyDescriptor:XkQ,getOwnPropertyNames:VkQ}=Object,CkQ=Object.prototype.hasOwnProperty,jA=(A,B)=>jU1(A,"name",{value:B,configurable:!0}),KkQ=(A,B)=>{for(var Q in B)jU1(A,Q,{get:B[Q],enumerable:!0})},_00=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of VkQ(B))if(!CkQ.call(A,Z)&&Z!==Q)jU1(A,Z,{get:()=>B[Z],enumerable:!(D=XkQ(B,Z))||D.enumerable})}return A},HkQ=(A,B,Q)=>(_00(A,B,"default"),Q&&_00(Q,B,"default")),zkQ=(A)=>_00(jU1({},"__esModule",{value:!0}),A),v00={};KkQ(v00,{AssumeRoleCommand:()=>f00,AssumeRoleResponseFilterSensitiveLog:()=>aSA,AssumeRoleWithSAMLCommand:()=>ZjA,AssumeRoleWithSAMLRequestFilterSensitiveLog:()=>sSA,AssumeRoleWithSAMLResponseFilterSensitiveLog:()=>rSA,AssumeRoleWithWebIdentityCommand:()=>h00,AssumeRoleWithWebIdentityRequestFilterSensitiveLog:()=>oSA,AssumeRoleWithWebIdentityResponseFilterSensitiveLog:()=>tSA,AssumeRootCommand:()=>GjA,AssumeRootResponseFilterSensitiveLog:()=>eSA,ClientInputEndpointParameters:()=>cyQ.ClientInputEndpointParameters,CredentialsFilterSensitiveLog:()=>lh,DecodeAuthorizationMessageCommand:()=>FjA,ExpiredTokenException:()=>uSA,GetAccessKeyInfoCommand:()=>IjA,GetCallerIdentityCommand:()=>YjA,GetFederationTokenCommand:()=>WjA,GetFederationTokenResponseFilterSensitiveLog:()=>AjA,GetSessionTokenCommand:()=>JjA,GetSessionTokenResponseFilterSensitiveLog:()=>BjA,IDPCommunicationErrorException:()=>iSA,IDPRejectedClaimException:()=>lSA,InvalidAuthorizationMessageException:()=>nSA,InvalidIdentityTokenException:()=>pSA,MalformedPolicyDocumentException:()=>mSA,PackedPolicyTooLargeException:()=>dSA,RegionDisabledException:()=>cSA,STS:()=>XjA,STSServiceException:()=>nN,decorateDefaultCredentialProvider:()=>iyQ,getDefaultRoleAssumer:()=>EjA,getDefaultRoleAssumerWithWebIdentity:()=>UjA});g00.exports=zkQ(v00);HkQ(v00,oQ1(),g00.exports);var sO=q6(),rO=T3(),EkQ=Fz(),nB=x4(),nN=class A extends nB.ServiceException{static{jA(this,"STSServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},uSA=class A extends nN{static{jA(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},mSA=class A extends nN{static{jA(this,"MalformedPolicyDocumentException")}name="MalformedPolicyDocumentException";$fault="client";constructor(B){super({name:"MalformedPolicyDocumentException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},dSA=class A extends nN{static{jA(this,"PackedPolicyTooLargeException")}name="PackedPolicyTooLargeException";$fault="client";constructor(B){super({name:"PackedPolicyTooLargeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},cSA=class A extends nN{static{jA(this,"RegionDisabledException")}name="RegionDisabledException";$fault="client";constructor(B){super({name:"RegionDisabledException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},lSA=class A extends nN{static{jA(this,"IDPRejectedClaimException")}name="IDPRejectedClaimException";$fault="client";constructor(B){super({name:"IDPRejectedClaimException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},pSA=class A extends nN{static{jA(this,"InvalidIdentityTokenException")}name="InvalidIdentityTokenException";$fault="client";constructor(B){super({name:"InvalidIdentityTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},iSA=class A extends nN{static{jA(this,"IDPCommunicationErrorException")}name="IDPCommunicationErrorException";$fault="client";constructor(B){super({name:"IDPCommunicationErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},nSA=class A extends nN{static{jA(this,"InvalidAuthorizationMessageException")}name="InvalidAuthorizationMessageException";$fault="client";constructor(B){super({name:"InvalidAuthorizationMessageException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},lh=jA((A)=>({...A,...A.SecretAccessKey&&{SecretAccessKey:nB.SENSITIVE_STRING}}),"CredentialsFilterSensitiveLog"),aSA=jA((A)=>({...A,...A.Credentials&&{Credentials:lh(A.Credentials)}}),"AssumeRoleResponseFilterSensitiveLog"),sSA=jA((A)=>({...A,...A.SAMLAssertion&&{SAMLAssertion:nB.SENSITIVE_STRING}}),"AssumeRoleWithSAMLRequestFilterSensitiveLog"),rSA=jA((A)=>({...A,...A.Credentials&&{Credentials:lh(A.Credentials)}}),"AssumeRoleWithSAMLResponseFilterSensitiveLog"),oSA=jA((A)=>({...A,...A.WebIdentityToken&&{WebIdentityToken:nB.SENSITIVE_STRING}}),"AssumeRoleWithWebIdentityRequestFilterSensitiveLog"),tSA=jA((A)=>({...A,...A.Credentials&&{Credentials:lh(A.Credentials)}}),"AssumeRoleWithWebIdentityResponseFilterSensitiveLog"),eSA=jA((A)=>({...A,...A.Credentials&&{Credentials:lh(A.Credentials)}}),"AssumeRootResponseFilterSensitiveLog"),AjA=jA((A)=>({...A,...A.Credentials&&{Credentials:lh(A.Credentials)}}),"GetFederationTokenResponseFilterSensitiveLog"),BjA=jA((A)=>({...A,...A.Credentials&&{Credentials:lh(A.Credentials)}}),"GetSessionTokenResponseFilterSensitiveLog"),aN=ZI(),UkQ=mJ(),wkQ=jA(async(A,B)=>{let Q=eO,D;return D=DT({...pkQ(A,B),[BT]:LyQ,[QT]:AT}),tO(B,Q,"/",void 0,D)},"se_AssumeRoleCommand"),$kQ=jA(async(A,B)=>{let Q=eO,D;return D=DT({...ikQ(A,B),[BT]:MyQ,[QT]:AT}),tO(B,Q,"/",void 0,D)},"se_AssumeRoleWithSAMLCommand"),qkQ=jA(async(A,B)=>{let Q=eO,D;return D=DT({...nkQ(A,B),[BT]:RyQ,[QT]:AT}),tO(B,Q,"/",void 0,D)},"se_AssumeRoleWithWebIdentityCommand"),NkQ=jA(async(A,B)=>{let Q=eO,D;return D=DT({...akQ(A,B),[BT]:OyQ,[QT]:AT}),tO(B,Q,"/",void 0,D)},"se_AssumeRootCommand"),LkQ=jA(async(A,B)=>{let Q=eO,D;return D=DT({...skQ(A,B),[BT]:TyQ,[QT]:AT}),tO(B,Q,"/",void 0,D)},"se_DecodeAuthorizationMessageCommand"),MkQ=jA(async(A,B)=>{let Q=eO,D;return D=DT({...rkQ(A,B),[BT]:PyQ,[QT]:AT}),tO(B,Q,"/",void 0,D)},"se_GetAccessKeyInfoCommand"),RkQ=jA(async(A,B)=>{let Q=eO,D;return D=DT({...okQ(A,B),[BT]:SyQ,[QT]:AT}),tO(B,Q,"/",void 0,D)},"se_GetCallerIdentityCommand"),OkQ=jA(async(A,B)=>{let Q=eO,D;return D=DT({...tkQ(A,B),[BT]:jyQ,[QT]:AT}),tO(B,Q,"/",void 0,D)},"se_GetFederationTokenCommand"),TkQ=jA(async(A,B)=>{let Q=eO,D;return D=DT({...ekQ(A,B),[BT]:kyQ,[QT]:AT}),tO(B,Q,"/",void 0,D)},"se_GetSessionTokenCommand"),PkQ=jA(async(A,B)=>{if(A.statusCode>=300)return oO(A,B);let Q=await aN.parseXmlBody(A.body,B),D={};return D=ZyQ(Q.AssumeRoleResult,B),{$metadata:RY(A),...D}},"de_AssumeRoleCommand"),SkQ=jA(async(A,B)=>{if(A.statusCode>=300)return oO(A,B);let Q=await aN.parseXmlBody(A.body,B),D={};return D=GyQ(Q.AssumeRoleWithSAMLResult,B),{$metadata:RY(A),...D}},"de_AssumeRoleWithSAMLCommand"),jkQ=jA(async(A,B)=>{if(A.statusCode>=300)return oO(A,B);let Q=await aN.parseXmlBody(A.body,B),D={};return D=FyQ(Q.AssumeRoleWithWebIdentityResult,B),{$metadata:RY(A),...D}},"de_AssumeRoleWithWebIdentityCommand"),kkQ=jA(async(A,B)=>{if(A.statusCode>=300)return oO(A,B);let Q=await aN.parseXmlBody(A.body,B),D={};return D=IyQ(Q.AssumeRootResult,B),{$metadata:RY(A),...D}},"de_AssumeRootCommand"),ykQ=jA(async(A,B)=>{if(A.statusCode>=300)return oO(A,B);let Q=await aN.parseXmlBody(A.body,B),D={};return D=YyQ(Q.DecodeAuthorizationMessageResult,B),{$metadata:RY(A),...D}},"de_DecodeAuthorizationMessageCommand"),_kQ=jA(async(A,B)=>{if(A.statusCode>=300)return oO(A,B);let Q=await aN.parseXmlBody(A.body,B),D={};return D=XyQ(Q.GetAccessKeyInfoResult,B),{$metadata:RY(A),...D}},"de_GetAccessKeyInfoCommand"),xkQ=jA(async(A,B)=>{if(A.statusCode>=300)return oO(A,B);let Q=await aN.parseXmlBody(A.body,B),D={};return D=VyQ(Q.GetCallerIdentityResult,B),{$metadata:RY(A),...D}},"de_GetCallerIdentityCommand"),vkQ=jA(async(A,B)=>{if(A.statusCode>=300)return oO(A,B);let Q=await aN.parseXmlBody(A.body,B),D={};return D=CyQ(Q.GetFederationTokenResult,B),{$metadata:RY(A),...D}},"de_GetFederationTokenCommand"),bkQ=jA(async(A,B)=>{if(A.statusCode>=300)return oO(A,B);let Q=await aN.parseXmlBody(A.body,B),D={};return D=KyQ(Q.GetSessionTokenResult,B),{$metadata:RY(A),...D}},"de_GetSessionTokenCommand"),oO=jA(async(A,B)=>{let Q={...A,body:await aN.parseXmlErrorBody(A.body,B)},D=yyQ(A,Q.body);switch(D){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await fkQ(Q,B);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await dkQ(Q,B);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await ckQ(Q,B);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await lkQ(Q,B);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await gkQ(Q,B);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await mkQ(Q,B);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await hkQ(Q,B);case"InvalidAuthorizationMessageException":case"com.amazonaws.sts#InvalidAuthorizationMessageException":throw await ukQ(Q,B);default:let Z=Q.body;return NyQ({output:A,parsedBody:Z.Error,errorCode:D})}},"de_CommandError"),fkQ=jA(async(A,B)=>{let Q=A.body,D=WyQ(Q.Error,B),Z=new uSA({$metadata:RY(A),...D});return nB.decorateServiceException(Z,Q)},"de_ExpiredTokenExceptionRes"),hkQ=jA(async(A,B)=>{let Q=A.body,D=HyQ(Q.Error,B),Z=new iSA({$metadata:RY(A),...D});return nB.decorateServiceException(Z,Q)},"de_IDPCommunicationErrorExceptionRes"),gkQ=jA(async(A,B)=>{let Q=A.body,D=zyQ(Q.Error,B),Z=new lSA({$metadata:RY(A),...D});return nB.decorateServiceException(Z,Q)},"de_IDPRejectedClaimExceptionRes"),ukQ=jA(async(A,B)=>{let Q=A.body,D=EyQ(Q.Error,B),Z=new nSA({$metadata:RY(A),...D});return nB.decorateServiceException(Z,Q)},"de_InvalidAuthorizationMessageExceptionRes"),mkQ=jA(async(A,B)=>{let Q=A.body,D=UyQ(Q.Error,B),Z=new pSA({$metadata:RY(A),...D});return nB.decorateServiceException(Z,Q)},"de_InvalidIdentityTokenExceptionRes"),dkQ=jA(async(A,B)=>{let Q=A.body,D=wyQ(Q.Error,B),Z=new mSA({$metadata:RY(A),...D});return nB.decorateServiceException(Z,Q)},"de_MalformedPolicyDocumentExceptionRes"),ckQ=jA(async(A,B)=>{let Q=A.body,D=$yQ(Q.Error,B),Z=new dSA({$metadata:RY(A),...D});return nB.decorateServiceException(Z,Q)},"de_PackedPolicyTooLargeExceptionRes"),lkQ=jA(async(A,B)=>{let Q=A.body,D=qyQ(Q.Error,B),Z=new cSA({$metadata:RY(A),...D});return nB.decorateServiceException(Z,Q)},"de_RegionDisabledExceptionRes"),pkQ=jA((A,B)=>{let Q={};if(A[aO]!=null)Q[aO]=A[aO];if(A[cn]!=null)Q[cn]=A[cn];if(A[Hz]!=null){let D=kU1(A[Hz],B);if(A[Hz]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[Kz]!=null)Q[Kz]=A[Kz];if(A[FI]!=null)Q[FI]=A[FI];if(A[pn]!=null){let D=DjA(A[pn],B);if(A[pn]?.length===0)Q.Tags=[];Object.entries(D).forEach(([Z,G])=>{let F=`Tags.${Z}`;Q[F]=G})}if(A[P00]!=null){let D=DyQ(A[P00],B);if(A[P00]?.length===0)Q.TransitiveTagKeys=[];Object.entries(D).forEach(([Z,G])=>{let F=`TransitiveTagKeys.${Z}`;Q[F]=G})}if(A[Y00]!=null)Q[Y00]=A[Y00];if(A[ln]!=null)Q[ln]=A[ln];if(A[nn]!=null)Q[nn]=A[nn];if(A[cJ]!=null)Q[cJ]=A[cJ];if(A[U00]!=null){let D=ByQ(A[U00],B);if(A[U00]?.length===0)Q.ProvidedContexts=[];Object.entries(D).forEach(([Z,G])=>{let F=`ProvidedContexts.${Z}`;Q[F]=G})}return Q},"se_AssumeRoleRequest"),ikQ=jA((A,B)=>{let Q={};if(A[aO]!=null)Q[aO]=A[aO];if(A[z00]!=null)Q[z00]=A[z00];if(A[L00]!=null)Q[L00]=A[L00];if(A[Hz]!=null){let D=kU1(A[Hz],B);if(A[Hz]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[Kz]!=null)Q[Kz]=A[Kz];if(A[FI]!=null)Q[FI]=A[FI];return Q},"se_AssumeRoleWithSAMLRequest"),nkQ=jA((A,B)=>{let Q={};if(A[aO]!=null)Q[aO]=A[aO];if(A[cn]!=null)Q[cn]=A[cn];if(A[k00]!=null)Q[k00]=A[k00];if(A[w00]!=null)Q[w00]=A[w00];if(A[Hz]!=null){let D=kU1(A[Hz],B);if(A[Hz]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[Kz]!=null)Q[Kz]=A[Kz];if(A[FI]!=null)Q[FI]=A[FI];return Q},"se_AssumeRoleWithWebIdentityRequest"),akQ=jA((A,B)=>{let Q={};if(A[T00]!=null)Q[T00]=A[T00];if(A[hSA]!=null){let D=QjA(A[hSA],B);Object.entries(D).forEach(([Z,G])=>{let F=`TaskPolicyArn.${Z}`;Q[F]=G})}if(A[FI]!=null)Q[FI]=A[FI];return Q},"se_AssumeRootRequest"),skQ=jA((A,B)=>{let Q={};if(A[W00]!=null)Q[W00]=A[W00];return Q},"se_DecodeAuthorizationMessageRequest"),rkQ=jA((A,B)=>{let Q={};if(A[un]!=null)Q[un]=A[un];return Q},"se_GetAccessKeyInfoRequest"),okQ=jA((A,B)=>{return{}},"se_GetCallerIdentityRequest"),tkQ=jA((A,B)=>{let Q={};if(A[K00]!=null)Q[K00]=A[K00];if(A[Kz]!=null)Q[Kz]=A[Kz];if(A[Hz]!=null){let D=kU1(A[Hz],B);if(A[Hz]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[FI]!=null)Q[FI]=A[FI];if(A[pn]!=null){let D=DjA(A[pn],B);if(A[pn]?.length===0)Q.Tags=[];Object.entries(D).forEach(([Z,G])=>{let F=`Tags.${Z}`;Q[F]=G})}return Q},"se_GetFederationTokenRequest"),ekQ=jA((A,B)=>{let Q={};if(A[FI]!=null)Q[FI]=A[FI];if(A[ln]!=null)Q[ln]=A[ln];if(A[nn]!=null)Q[nn]=A[nn];return Q},"se_GetSessionTokenRequest"),kU1=jA((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=QjA(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_policyDescriptorListType"),QjA=jA((A,B)=>{let Q={};if(A[y00]!=null)Q[y00]=A[y00];return Q},"se_PolicyDescriptorType"),AyQ=jA((A,B)=>{let Q={};if(A[E00]!=null)Q[E00]=A[E00];if(A[G00]!=null)Q[G00]=A[G00];return Q},"se_ProvidedContext"),ByQ=jA((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=AyQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_ProvidedContextsListType"),QyQ=jA((A,B)=>{let Q={};if(A[C00]!=null)Q[C00]=A[C00];if(A[j00]!=null)Q[j00]=A[j00];return Q},"se_Tag"),DyQ=jA((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;Q[`member.${D}`]=Z,D++}return Q},"se_tagKeyListType"),DjA=jA((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=QyQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_tagListType"),b00=jA((A,B)=>{let Q={};if(A[Z00]!=null)Q[Z00]=nB.expectString(A[Z00]);if(A[nO]!=null)Q[nO]=nB.expectString(A[nO]);return Q},"de_AssumedRoleUser"),ZyQ=jA((A,B)=>{let Q={};if(A[GI]!=null)Q[GI]=an(A[GI],B);if(A[iO]!=null)Q[iO]=b00(A[iO],B);if(A[zz]!=null)Q[zz]=nB.strictParseInt32(A[zz]);if(A[cJ]!=null)Q[cJ]=nB.expectString(A[cJ]);return Q},"de_AssumeRoleResponse"),GyQ=jA((A,B)=>{let Q={};if(A[GI]!=null)Q[GI]=an(A[GI],B);if(A[iO]!=null)Q[iO]=b00(A[iO],B);if(A[zz]!=null)Q[zz]=nB.strictParseInt32(A[zz]);if(A[q00]!=null)Q[q00]=nB.expectString(A[q00]);if(A[R00]!=null)Q[R00]=nB.expectString(A[R00]);if(A[V00]!=null)Q[V00]=nB.expectString(A[V00]);if(A[dn]!=null)Q[dn]=nB.expectString(A[dn]);if(A[H00]!=null)Q[H00]=nB.expectString(A[H00]);if(A[cJ]!=null)Q[cJ]=nB.expectString(A[cJ]);return Q},"de_AssumeRoleWithSAMLResponse"),FyQ=jA((A,B)=>{let Q={};if(A[GI]!=null)Q[GI]=an(A[GI],B);if(A[M00]!=null)Q[M00]=nB.expectString(A[M00]);if(A[iO]!=null)Q[iO]=b00(A[iO],B);if(A[zz]!=null)Q[zz]=nB.strictParseInt32(A[zz]);if(A[$00]!=null)Q[$00]=nB.expectString(A[$00]);if(A[dn]!=null)Q[dn]=nB.expectString(A[dn]);if(A[cJ]!=null)Q[cJ]=nB.expectString(A[cJ]);return Q},"de_AssumeRoleWithWebIdentityResponse"),IyQ=jA((A,B)=>{let Q={};if(A[GI]!=null)Q[GI]=an(A[GI],B);if(A[cJ]!=null)Q[cJ]=nB.expectString(A[cJ]);return Q},"de_AssumeRootResponse"),an=jA((A,B)=>{let Q={};if(A[un]!=null)Q[un]=nB.expectString(A[un]);if(A[N00]!=null)Q[N00]=nB.expectString(A[N00]);if(A[O00]!=null)Q[O00]=nB.expectString(A[O00]);if(A[I00]!=null)Q[I00]=nB.expectNonNull(nB.parseRfc3339DateTimeWithOffset(A[I00]));return Q},"de_Credentials"),YyQ=jA((A,B)=>{let Q={};if(A[F00]!=null)Q[F00]=nB.expectString(A[F00]);return Q},"de_DecodeAuthorizationMessageResponse"),WyQ=jA((A,B)=>{let Q={};if(A[GD]!=null)Q[GD]=nB.expectString(A[GD]);return Q},"de_ExpiredTokenException"),JyQ=jA((A,B)=>{let Q={};if(A[X00]!=null)Q[X00]=nB.expectString(A[X00]);if(A[nO]!=null)Q[nO]=nB.expectString(A[nO]);return Q},"de_FederatedUser"),XyQ=jA((A,B)=>{let Q={};if(A[mn]!=null)Q[mn]=nB.expectString(A[mn]);return Q},"de_GetAccessKeyInfoResponse"),VyQ=jA((A,B)=>{let Q={};if(A[S00]!=null)Q[S00]=nB.expectString(A[S00]);if(A[mn]!=null)Q[mn]=nB.expectString(A[mn]);if(A[nO]!=null)Q[nO]=nB.expectString(A[nO]);return Q},"de_GetCallerIdentityResponse"),CyQ=jA((A,B)=>{let Q={};if(A[GI]!=null)Q[GI]=an(A[GI],B);if(A[J00]!=null)Q[J00]=JyQ(A[J00],B);if(A[zz]!=null)Q[zz]=nB.strictParseInt32(A[zz]);return Q},"de_GetFederationTokenResponse"),KyQ=jA((A,B)=>{let Q={};if(A[GI]!=null)Q[GI]=an(A[GI],B);return Q},"de_GetSessionTokenResponse"),HyQ=jA((A,B)=>{let Q={};if(A[GD]!=null)Q[GD]=nB.expectString(A[GD]);return Q},"de_IDPCommunicationErrorException"),zyQ=jA((A,B)=>{let Q={};if(A[GD]!=null)Q[GD]=nB.expectString(A[GD]);return Q},"de_IDPRejectedClaimException"),EyQ=jA((A,B)=>{let Q={};if(A[GD]!=null)Q[GD]=nB.expectString(A[GD]);return Q},"de_InvalidAuthorizationMessageException"),UyQ=jA((A,B)=>{let Q={};if(A[GD]!=null)Q[GD]=nB.expectString(A[GD]);return Q},"de_InvalidIdentityTokenException"),wyQ=jA((A,B)=>{let Q={};if(A[GD]!=null)Q[GD]=nB.expectString(A[GD]);return Q},"de_MalformedPolicyDocumentException"),$yQ=jA((A,B)=>{let Q={};if(A[GD]!=null)Q[GD]=nB.expectString(A[GD]);return Q},"de_PackedPolicyTooLargeException"),qyQ=jA((A,B)=>{let Q={};if(A[GD]!=null)Q[GD]=nB.expectString(A[GD]);return Q},"de_RegionDisabledException"),RY=jA((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),NyQ=nB.withBaseException(nN),tO=jA(async(A,B,Q,D,Z)=>{let{hostname:G,protocol:F="https",port:I,path:Y}=await A.endpoint(),W={protocol:F,hostname:G,port:I,method:"POST",path:Y.endsWith("/")?Y.slice(0,-1)+Q:Y+Q,headers:B};if(D!==void 0)W.hostname=D;if(Z!==void 0)W.body=Z;return new UkQ.HttpRequest(W)},"buildHttpRpcRequest"),eO={"content-type":"application/x-www-form-urlencoded"},AT="2011-06-15",BT="Action",un="AccessKeyId",LyQ="AssumeRole",Z00="AssumedRoleId",iO="AssumedRoleUser",MyQ="AssumeRoleWithSAML",RyQ="AssumeRoleWithWebIdentity",OyQ="AssumeRoot",mn="Account",nO="Arn",dn="Audience",GI="Credentials",G00="ContextAssertion",TyQ="DecodeAuthorizationMessage",F00="DecodedMessage",FI="DurationSeconds",I00="Expiration",Y00="ExternalId",W00="EncodedMessage",J00="FederatedUser",X00="FederatedUserId",PyQ="GetAccessKeyInfo",SyQ="GetCallerIdentity",jyQ="GetFederationToken",kyQ="GetSessionToken",V00="Issuer",C00="Key",K00="Name",H00="NameQualifier",Kz="Policy",Hz="PolicyArns",z00="PrincipalArn",E00="ProviderArn",U00="ProvidedContexts",w00="ProviderId",zz="PackedPolicySize",$00="Provider",aO="RoleArn",cn="RoleSessionName",q00="Subject",N00="SecretAccessKey",L00="SAMLAssertion",M00="SubjectFromWebIdentityToken",cJ="SourceIdentity",ln="SerialNumber",R00="SubjectType",O00="SessionToken",pn="Tags",nn="TokenCode",T00="TargetPrincipal",hSA="TaskPolicyArn",P00="TransitiveTagKeys",S00="UserId",QT="Version",j00="Value",k00="WebIdentityToken",y00="arn",GD="message",DT=jA((A)=>Object.entries(A).map(([B,Q])=>nB.extendedEncodeURIComponent(B)+"="+nB.extendedEncodeURIComponent(Q)).join("&"),"buildFormUrlencodedString"),yyQ=jA((A,B)=>{if(B.Error?.Code!==void 0)return B.Error.Code;if(A.statusCode==404)return"NotFound"},"loadQueryErrorCode"),f00=class extends nB.Command.classBuilder().ep(EkQ.commonParams).m(function(A,B,Q,D){return[rO.getSerdePlugin(Q,this.serialize,this.deserialize),sO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRole",{}).n("STSClient","AssumeRoleCommand").f(void 0,aSA).ser(wkQ).de(PkQ).build(){static{jA(this,"AssumeRoleCommand")}},_yQ=Fz(),ZjA=class extends nB.Command.classBuilder().ep(_yQ.commonParams).m(function(A,B,Q,D){return[rO.getSerdePlugin(Q,this.serialize,this.deserialize),sO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithSAML",{}).n("STSClient","AssumeRoleWithSAMLCommand").f(sSA,rSA).ser($kQ).de(SkQ).build(){static{jA(this,"AssumeRoleWithSAMLCommand")}},xyQ=Fz(),h00=class extends nB.Command.classBuilder().ep(xyQ.commonParams).m(function(A,B,Q,D){return[rO.getSerdePlugin(Q,this.serialize,this.deserialize),sO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithWebIdentity",{}).n("STSClient","AssumeRoleWithWebIdentityCommand").f(oSA,tSA).ser(qkQ).de(jkQ).build(){static{jA(this,"AssumeRoleWithWebIdentityCommand")}},vyQ=Fz(),GjA=class extends nB.Command.classBuilder().ep(vyQ.commonParams).m(function(A,B,Q,D){return[rO.getSerdePlugin(Q,this.serialize,this.deserialize),sO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoot",{}).n("STSClient","AssumeRootCommand").f(void 0,eSA).ser(NkQ).de(kkQ).build(){static{jA(this,"AssumeRootCommand")}},byQ=Fz(),FjA=class extends nB.Command.classBuilder().ep(byQ.commonParams).m(function(A,B,Q,D){return[rO.getSerdePlugin(Q,this.serialize,this.deserialize),sO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","DecodeAuthorizationMessage",{}).n("STSClient","DecodeAuthorizationMessageCommand").f(void 0,void 0).ser(LkQ).de(ykQ).build(){static{jA(this,"DecodeAuthorizationMessageCommand")}},fyQ=Fz(),IjA=class extends nB.Command.classBuilder().ep(fyQ.commonParams).m(function(A,B,Q,D){return[rO.getSerdePlugin(Q,this.serialize,this.deserialize),sO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","GetAccessKeyInfo",{}).n("STSClient","GetAccessKeyInfoCommand").f(void 0,void 0).ser(MkQ).de(_kQ).build(){static{jA(this,"GetAccessKeyInfoCommand")}},hyQ=Fz(),YjA=class extends nB.Command.classBuilder().ep(hyQ.commonParams).m(function(A,B,Q,D){return[rO.getSerdePlugin(Q,this.serialize,this.deserialize),sO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","GetCallerIdentity",{}).n("STSClient","GetCallerIdentityCommand").f(void 0,void 0).ser(RkQ).de(xkQ).build(){static{jA(this,"GetCallerIdentityCommand")}},gyQ=Fz(),WjA=class extends nB.Command.classBuilder().ep(gyQ.commonParams).m(function(A,B,Q,D){return[rO.getSerdePlugin(Q,this.serialize,this.deserialize),sO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","GetFederationToken",{}).n("STSClient","GetFederationTokenCommand").f(void 0,AjA).ser(OkQ).de(vkQ).build(){static{jA(this,"GetFederationTokenCommand")}},uyQ=Fz(),JjA=class extends nB.Command.classBuilder().ep(uyQ.commonParams).m(function(A,B,Q,D){return[rO.getSerdePlugin(Q,this.serialize,this.deserialize),sO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","GetSessionToken",{}).n("STSClient","GetSessionTokenCommand").f(void 0,BjA).ser(TkQ).de(bkQ).build(){static{jA(this,"GetSessionTokenCommand")}},myQ=oQ1(),dyQ={AssumeRoleCommand:f00,AssumeRoleWithSAMLCommand:ZjA,AssumeRoleWithWebIdentityCommand:h00,AssumeRootCommand:GjA,DecodeAuthorizationMessageCommand:FjA,GetAccessKeyInfoCommand:IjA,GetCallerIdentityCommand:YjA,GetFederationTokenCommand:WjA,GetSessionTokenCommand:JjA},XjA=class extends myQ.STSClient{static{jA(this,"STS")}};nB.createAggregatedClient(dyQ,XjA);var cyQ=Fz(),x00=Qz(),gSA="us-east-1",VjA=jA((A)=>{if(typeof A?.Arn==="string"){let B=A.Arn.split(":");if(B.length>4&&B[4]!=="")return B[4]}return},"getAccountIdFromAssumedRoleUser"),CjA=jA(async(A,B,Q)=>{let D=typeof A==="function"?await A():A,Z=typeof B==="function"?await B():B;return Q?.debug?.("@aws-sdk/client-sts::resolveRegion","accepting first of:",`${D} (provider)`,`${Z} (parent client)`,`${gSA} (STS default)`),D??Z??gSA},"resolveRegion"),lyQ=jA((A,B)=>{let Q,D;return async(Z,G)=>{if(D=Z,!Q){let{logger:J=A?.parentClientConfig?.logger,region:X,requestHandler:V=A?.parentClientConfig?.requestHandler,credentialProviderLogger:C}=A,K=await CjA(X,A?.parentClientConfig?.region,C),H=!KjA(V);Q=new B({profile:A?.parentClientConfig?.profile,credentialDefaultProvider:jA(()=>async()=>D,"credentialDefaultProvider"),region:K,requestHandler:H?V:void 0,logger:J})}let{Credentials:F,AssumedRoleUser:I}=await Q.send(new f00(G));if(!F||!F.AccessKeyId||!F.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRole call with role ${G.RoleArn}`);let Y=VjA(I),W={accessKeyId:F.AccessKeyId,secretAccessKey:F.SecretAccessKey,sessionToken:F.SessionToken,expiration:F.Expiration,...F.CredentialScope&&{credentialScope:F.CredentialScope},...Y&&{accountId:Y}};return x00.setCredentialFeature(W,"CREDENTIALS_STS_ASSUME_ROLE","i"),W}},"getDefaultRoleAssumer"),pyQ=jA((A,B)=>{let Q;return async(D)=>{if(!Q){let{logger:Y=A?.parentClientConfig?.logger,region:W,requestHandler:J=A?.parentClientConfig?.requestHandler,credentialProviderLogger:X}=A,V=await CjA(W,A?.parentClientConfig?.region,X),C=!KjA(J);Q=new B({profile:A?.parentClientConfig?.profile,region:V,requestHandler:C?J:void 0,logger:Y})}let{Credentials:Z,AssumedRoleUser:G}=await Q.send(new h00(D));if(!Z||!Z.AccessKeyId||!Z.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${D.RoleArn}`);let F=VjA(G),I={accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.SessionToken,expiration:Z.Expiration,...Z.CredentialScope&&{credentialScope:Z.CredentialScope},...F&&{accountId:F}};if(F)x00.setCredentialFeature(I,"RESOLVED_ACCOUNT_ID","T");return x00.setCredentialFeature(I,"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID","k"),I}},"getDefaultRoleAssumerWithWebIdentity"),KjA=jA((A)=>{return A?.metadata?.handlerProtocol==="h2"},"isH2"),HjA=oQ1(),zjA=jA((A,B)=>{if(!B)return A;else return class Q extends A{static{jA(this,"CustomizableSTSClient")}constructor(D){super(D);for(let Z of B)this.middlewareStack.use(Z)}}},"getCustomizableStsClientCtor"),EjA=jA((A={},B)=>lyQ(A,zjA(HjA.STSClient,B)),"getDefaultRoleAssumer"),UjA=jA((A={},B)=>pyQ(A,zjA(HjA.STSClient,B)),"getDefaultRoleAssumerWithWebIdentity"),iyQ=jA((A)=>(B)=>A({roleAssumer:EjA(B),roleAssumerWithWebIdentity:UjA(B),...B}),"decorateDefaultCredentialProvider")});
var x4=E((Y75,Rt1)=>{var{defineProperty:qE1,getOwnPropertyDescriptor:aVQ,getOwnPropertyNames:sVQ}=Object,rVQ=Object.prototype.hasOwnProperty,X8=(A,B)=>qE1(A,"name",{value:B,configurable:!0}),oVQ=(A,B)=>{for(var Q in B)qE1(A,Q,{get:B[Q],enumerable:!0})},$t1=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of sVQ(B))if(!rVQ.call(A,Z)&&Z!==Q)qE1(A,Z,{get:()=>B[Z],enumerable:!(D=aVQ(B,Z))||D.enumerable})}return A},tVQ=(A,B,Q)=>($t1(A,B,"default"),Q&&$t1(Q,B,"default")),eVQ=(A)=>$t1(qE1({},"__esModule",{value:!0}),A),Lt1={};oVQ(Lt1,{Client:()=>ACQ,Command:()=>nEA,NoOpLogger:()=>ECQ,SENSITIVE_STRING:()=>QCQ,ServiceException:()=>ZCQ,_json:()=>Nt1,collectBody:()=>wt1.collectBody,convertMap:()=>UCQ,createAggregatedClient:()=>DCQ,decorateServiceException:()=>aEA,emitWarningIfUnsupportedVersion:()=>YCQ,extendedEncodeURIComponent:()=>wt1.extendedEncodeURIComponent,getArrayIfSingleItem:()=>HCQ,getDefaultClientConfiguration:()=>CCQ,getDefaultExtensionConfiguration:()=>rEA,getValueFromTextNode:()=>oEA,isSerializableHeaderValue:()=>zCQ,loadConfigsForDefaultMode:()=>ICQ,map:()=>Mt1,resolveDefaultRuntimeConfig:()=>KCQ,resolvedPath:()=>wt1.resolvedPath,serializeDateTime:()=>MCQ,serializeFloat:()=>LCQ,take:()=>wCQ,throwDefaultError:()=>sEA,withBaseException:()=>GCQ});Rt1.exports=eVQ(Lt1);var iEA=Uw(),ACQ=class{constructor(A){this.config=A,this.middlewareStack=iEA.constructStack()}static{X8(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},wt1=$6(),qt1=Bo1(),nEA=class{constructor(){this.middlewareStack=iEA.constructStack()}static{X8(this,"Command")}static classBuilder(){return new BCQ}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[qt1.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},BCQ=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{X8(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}sc(A){return this._operationSchema=A,this._smithyContext.operationSchema=A,this}build(){let A=this,B;return B=class extends nEA{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this),this.schema=A._operationSchema}static{X8(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},QCQ="***SensitiveInformation***",DCQ=X8((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=X8(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),ZCQ=class A extends Error{static{X8(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},aEA=X8((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),sEA=X8(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=FCQ(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw aEA(F,B)},"throwDefaultError"),GCQ=X8((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{sEA({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),FCQ=X8((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),ICQ=X8((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),pEA=!1,YCQ=X8((A)=>{if(A&&!pEA&&parseInt(A.substring(1,A.indexOf(".")))<16)pEA=!0},"emitWarningIfUnsupportedVersion"),WCQ=X8((A)=>{let B=[];for(let Q in qt1.AlgorithmId){let D=qt1.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),JCQ=X8((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),XCQ=X8((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),VCQ=X8((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),rEA=X8((A)=>{return Object.assign(WCQ(A),XCQ(A))},"getDefaultExtensionConfiguration"),CCQ=rEA,KCQ=X8((A)=>{return Object.assign(JCQ(A),VCQ(A))},"resolveDefaultRuntimeConfig"),HCQ=X8((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),oEA=X8((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=oEA(A[Q]);return A},"getValueFromTextNode"),zCQ=X8((A)=>{return A!=null},"isSerializableHeaderValue"),ECQ=class{static{X8(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function Mt1(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,$CQ(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}tEA(D,null,G,F)}return D}X8(Mt1,"map");var UCQ=X8((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),wCQ=X8((A,B)=>{let Q={};for(let D in B)tEA(Q,A,B,D);return Q},"take"),$CQ=X8((A,B,Q)=>{return Mt1(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),tEA=X8((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=qCQ,Y=NCQ,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),qCQ=X8((A)=>A!=null,"nonNullish"),NCQ=X8((A)=>A,"pass"),LCQ=X8((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),MCQ=X8((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),Nt1=X8((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(Nt1);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=Nt1(A[Q])}return B}return A},"_json");tVQ(Lt1,Y6(),Rt1.exports)});
var y$A=E((JD5,k$A)=>{var{defineProperty:dE1,getOwnPropertyDescriptor:CUQ,getOwnPropertyNames:KUQ}=Object,HUQ=Object.prototype.hasOwnProperty,zUQ=(A,B)=>dE1(A,"name",{value:B,configurable:!0}),EUQ=(A,B)=>{for(var Q in B)dE1(A,Q,{get:B[Q],enumerable:!0})},UUQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of KUQ(B))if(!HUQ.call(A,Z)&&Z!==Q)dE1(A,Z,{get:()=>B[Z],enumerable:!(D=CUQ(B,Z))||D.enumerable})}return A},wUQ=(A)=>UUQ(dE1({},"__esModule",{value:!0}),A),S$A={};EUQ(S$A,{parseQueryString:()=>j$A});k$A.exports=wUQ(S$A);function j$A(A){let B={};if(A=A.replace(/^\?/,""),A)for(let Q of A.split("&")){let[D,Z=null]=Q.split("=");if(D=decodeURIComponent(D),Z)Z=decodeURIComponent(Z);if(!(D in B))B[D]=Z;else if(Array.isArray(B[D]))B[D].push(Z);else B[D]=[B[D],Z]}return B}zUQ(j$A,"parseQueryString")});
var yG=E((KZ5,EMA)=>{var{create:TMQ,defineProperty:A41,getOwnPropertyDescriptor:PMQ,getOwnPropertyNames:SMQ,getPrototypeOf:jMQ}=Object,kMQ=Object.prototype.hasOwnProperty,ue1=(A,B)=>A41(A,"name",{value:B,configurable:!0}),yMQ=(A,B)=>{for(var Q in B)A41(A,Q,{get:B[Q],enumerable:!0})},HMA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of SMQ(B))if(!kMQ.call(A,Z)&&Z!==Q)A41(A,Z,{get:()=>B[Z],enumerable:!(D=PMQ(B,Z))||D.enumerable})}return A},_MQ=(A,B,Q)=>(Q=A!=null?TMQ(jMQ(A)):{},HMA(B||!A||!A.__esModule?A41(Q,"default",{value:A,enumerable:!0}):Q,A)),xMQ=(A)=>HMA(A41({},"__esModule",{value:!0}),A),zMA={};yMQ(zMA,{resolveDefaultsModeConfig:()=>lMQ});EMA.exports=xMQ(zMA);var vMQ=V4(),VMA=QD(),bMQ=eB(),fMQ="AWS_EXECUTION_ENV",CMA="AWS_REGION",KMA="AWS_DEFAULT_REGION",hMQ="AWS_EC2_METADATA_DISABLED",gMQ=["in-region","cross-region","mobile","standard","legacy"],uMQ="/latest/meta-data/placement/region",mMQ="AWS_DEFAULTS_MODE",dMQ="defaults_mode",cMQ={environmentVariableSelector:(A)=>{return A[mMQ]},configFileSelector:(A)=>{return A[dMQ]},default:"legacy"},lMQ=ue1(({region:A=VMA.loadConfig(vMQ.NODE_REGION_CONFIG_OPTIONS),defaultsMode:B=VMA.loadConfig(cMQ)}={})=>bMQ.memoize(async()=>{let Q=typeof B==="function"?await B():B;switch(Q?.toLowerCase()){case"auto":return pMQ(A);case"in-region":case"cross-region":case"mobile":case"standard":case"legacy":return Promise.resolve(Q?.toLocaleLowerCase());case void 0:return Promise.resolve("legacy");default:throw new Error(`Invalid parameter for "defaultsMode", expect ${gMQ.join(", ")}, got ${Q}`)}}),"resolveDefaultsModeConfig"),pMQ=ue1(async(A)=>{if(A){let B=typeof A==="function"?await A():A,Q=await iMQ();if(!Q)return"standard";if(B===Q)return"in-region";else return"cross-region"}return"standard"},"resolveNodeDefaultsModeAuto"),iMQ=ue1(async()=>{if(process.env[fMQ]&&(process.env[CMA]||process.env[KMA]))return process.env[CMA]??process.env[KMA];if(!process.env[hMQ])try{let{getInstanceMetadataEndpoint:A,httpRequest:B}=await Promise.resolve().then(()=>_MQ($F())),Q=await A();return(await B({...Q,path:uMQ})).toString()}catch(A){}},"inferPhysicalRegion")});
var yVA=E((jVA)=>{Object.defineProperty(jVA,"__esModule",{value:!0});jVA.createBufferedReadable=void 0;jVA.createBufferedReadableStream=TVA;jVA.merge=PVA;jVA.flush=Oz1;jVA.sizeOf=Qn;jVA.modeOf=SVA;var HGQ=Xo1();function TVA(A,B,Q){let D=A.getReader(),Z=!1,G=0,F=["",new HGQ.ByteArrayCollector((W)=>new Uint8Array(W))],I=-1,Y=async(W)=>{let{value:J,done:X}=await D.read(),V=J;if(X){if(I!==-1){let C=Oz1(F,I);if(Qn(C)>0)W.enqueue(C)}W.close()}else{let C=SVA(V,!1);if(I!==C){if(I>=0)W.enqueue(Oz1(F,I));I=C}if(I===-1){W.enqueue(V);return}let K=Qn(V);G+=K;let H=Qn(F[I]);if(K>=B&&H===0)W.enqueue(V);else{let z=PVA(F,I,V);if(!Z&&G>B*2)Z=!0,Q===null||Q===void 0||Q.warn(`@smithy/util-stream - stream chunk size ${K} is below threshold of ${B}, automatically buffering.`);if(z>=B)W.enqueue(Oz1(F,I));else await Y(W)}}};return new ReadableStream({pull:Y})}jVA.createBufferedReadable=TVA;function PVA(A,B,Q){switch(B){case 0:return A[0]+=Q,Qn(A[0]);case 1:case 2:return A[B].push(Q),Qn(A[B])}}function Oz1(A,B){switch(B){case 0:let Q=A[0];return A[0]="",Q;case 1:case 2:return A[B].flush()}throw new Error(`@smithy/util-stream - invalid index ${B} given to flush()`)}function Qn(A){var B,Q;return(Q=(B=A===null||A===void 0?void 0:A.byteLength)!==null&&B!==void 0?B:A===null||A===void 0?void 0:A.length)!==null&&Q!==void 0?Q:0}function SVA(A,B=!0){if(B&&typeof Buffer!=="undefined"&&A instanceof Buffer)return 2;if(A instanceof Uint8Array)return 1;if(typeof A==="string")return 0;return-1}});
var yqA=E((jqA)=>{Object.defineProperty(jqA,"__esModule",{value:!0});jqA.default=void 0;var B$Q=Q$Q(J1("crypto"));function Q$Q(A){return A&&A.__esModule?A:{default:A}}var D$Q={randomUUID:B$Q.default.randomUUID};jqA.default=D$Q});
var zEA=E((r35,HEA)=>{var{defineProperty:zE1,getOwnPropertyDescriptor:hXQ,getOwnPropertyNames:gXQ}=Object,uXQ=Object.prototype.hasOwnProperty,UY=(A,B)=>zE1(A,"name",{value:B,configurable:!0}),mXQ=(A,B)=>{for(var Q in B)zE1(A,Q,{get:B[Q],enumerable:!0})},dXQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of gXQ(B))if(!uXQ.call(A,Z)&&Z!==Q)zE1(A,Z,{get:()=>B[Z],enumerable:!(D=hXQ(B,Z))||D.enumerable})}return A},cXQ=(A)=>dXQ(zE1({},"__esModule",{value:!0}),A),nzA={};mXQ(nzA,{ALGORITHM_IDENTIFIER:()=>JE1,ALGORITHM_IDENTIFIER_V4A:()=>nXQ,ALGORITHM_QUERY_PARAM:()=>azA,ALWAYS_UNSIGNABLE_HEADERS:()=>QEA,AMZ_DATE_HEADER:()=>Jt1,AMZ_DATE_QUERY_PARAM:()=>Ft1,AUTH_HEADER:()=>Wt1,CREDENTIAL_QUERY_PARAM:()=>szA,DATE_HEADER:()=>tzA,EVENT_ALGORITHM_IDENTIFIER:()=>GEA,EXPIRES_QUERY_PARAM:()=>ozA,GENERATED_HEADERS:()=>ezA,HOST_HEADER:()=>pXQ,KEY_TYPE_IDENTIFIER:()=>Xt1,MAX_CACHE_SIZE:()=>IEA,MAX_PRESIGNED_TTL:()=>YEA,PROXY_HEADER_PATTERN:()=>DEA,REGION_SET_PARAM:()=>lXQ,SEC_HEADER_PATTERN:()=>ZEA,SHA256_HEADER:()=>HE1,SIGNATURE_HEADER:()=>AEA,SIGNATURE_QUERY_PARAM:()=>It1,SIGNED_HEADERS_QUERY_PARAM:()=>rzA,SignatureV4:()=>ZVQ,SignatureV4Base:()=>KEA,TOKEN_HEADER:()=>BEA,TOKEN_QUERY_PARAM:()=>Yt1,UNSIGNABLE_PATTERNS:()=>iXQ,UNSIGNED_PAYLOAD:()=>FEA,clearCredentialCache:()=>sXQ,createScope:()=>VE1,getCanonicalHeaders:()=>Dt1,getCanonicalQuery:()=>CEA,getPayloadHash:()=>CE1,getSigningKey:()=>WEA,hasHeader:()=>JEA,moveHeadersToQuery:()=>VEA,prepareRequest:()=>Gt1,signatureV4aContainer:()=>GVQ});HEA.exports=cXQ(nzA);var czA=cB(),azA="X-Amz-Algorithm",szA="X-Amz-Credential",Ft1="X-Amz-Date",rzA="X-Amz-SignedHeaders",ozA="X-Amz-Expires",It1="X-Amz-Signature",Yt1="X-Amz-Security-Token",lXQ="X-Amz-Region-Set",Wt1="authorization",Jt1=Ft1.toLowerCase(),tzA="date",ezA=[Wt1,Jt1,tzA],AEA=It1.toLowerCase(),HE1="x-amz-content-sha256",BEA=Yt1.toLowerCase(),pXQ="host",QEA={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},DEA=/^proxy-/,ZEA=/^sec-/,iXQ=[/^proxy-/i,/^sec-/i],JE1="AWS4-HMAC-SHA256",nXQ="AWS4-ECDSA-P256-SHA256",GEA="AWS4-HMAC-SHA256-PAYLOAD",FEA="UNSIGNED-PAYLOAD",IEA=50,Xt1="aws4_request",YEA=604800,Iy=Zy(),aXQ=cB(),Vn={},XE1=[],VE1=UY((A,B,Q)=>`${A}/${B}/${Q}/${Xt1}`,"createScope"),WEA=UY(async(A,B,Q,D,Z)=>{let G=await lzA(A,B.secretAccessKey,B.accessKeyId),F=`${Q}:${D}:${Z}:${Iy.toHex(G)}:${B.sessionToken}`;if(F in Vn)return Vn[F];XE1.push(F);while(XE1.length>IEA)delete Vn[XE1.shift()];let I=`AWS4${B.secretAccessKey}`;for(let Y of[Q,D,Z,Xt1])I=await lzA(A,I,Y);return Vn[F]=I},"getSigningKey"),sXQ=UY(()=>{XE1.length=0,Object.keys(Vn).forEach((A)=>{delete Vn[A]})},"clearCredentialCache"),lzA=UY((A,B,Q)=>{let D=new A(B);return D.update(aXQ.toUint8Array(Q)),D.digest()},"hmac"),Dt1=UY(({headers:A},B,Q)=>{let D={};for(let Z of Object.keys(A).sort()){if(A[Z]==null)continue;let G=Z.toLowerCase();if(G in QEA||B?.has(G)||DEA.test(G)||ZEA.test(G)){if(!Q||Q&&!Q.has(G))continue}D[G]=A[Z].trim().replace(/\s+/g," ")}return D},"getCanonicalHeaders"),rXQ=hzA(),oXQ=cB(),CE1=UY(async({headers:A,body:B},Q)=>{for(let D of Object.keys(A))if(D.toLowerCase()===HE1)return A[D];if(B==null)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";else if(typeof B==="string"||ArrayBuffer.isView(B)||rXQ.isArrayBuffer(B)){let D=new Q;return D.update(oXQ.toUint8Array(B)),Iy.toHex(await D.digest())}return FEA},"getPayloadHash"),pzA=cB(),tXQ=class{static{UY(this,"HeaderFormatter")}format(A){let B=[];for(let Z of Object.keys(A)){let G=pzA.fromUtf8(Z);B.push(Uint8Array.from([G.byteLength]),G,this.formatHeaderValue(A[Z]))}let Q=new Uint8Array(B.reduce((Z,G)=>Z+G.byteLength,0)),D=0;for(let Z of B)Q.set(Z,D),D+=Z.byteLength;return Q}formatHeaderValue(A){switch(A.type){case"boolean":return Uint8Array.from([A.value?0:1]);case"byte":return Uint8Array.from([2,A.value]);case"short":let B=new DataView(new ArrayBuffer(3));return B.setUint8(0,3),B.setInt16(1,A.value,!1),new Uint8Array(B.buffer);case"integer":let Q=new DataView(new ArrayBuffer(5));return Q.setUint8(0,4),Q.setInt32(1,A.value,!1),new Uint8Array(Q.buffer);case"long":let D=new Uint8Array(9);return D[0]=5,D.set(A.value.bytes,1),D;case"binary":let Z=new DataView(new ArrayBuffer(3+A.value.byteLength));Z.setUint8(0,6),Z.setUint16(1,A.value.byteLength,!1);let G=new Uint8Array(Z.buffer);return G.set(A.value,3),G;case"string":let F=pzA.fromUtf8(A.value),I=new DataView(new ArrayBuffer(3+F.byteLength));I.setUint8(0,7),I.setUint16(1,F.byteLength,!1);let Y=new Uint8Array(I.buffer);return Y.set(F,3),Y;case"timestamp":let W=new Uint8Array(9);return W[0]=8,W.set(AVQ.fromNumber(A.value.valueOf()).bytes,1),W;case"uuid":if(!eXQ.test(A.value))throw new Error(`Invalid UUID received: ${A.value}`);let J=new Uint8Array(17);return J[0]=9,J.set(Iy.fromHex(A.value.replace(/\-/g,"")),1),J}}},eXQ=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,AVQ=class A{constructor(B){if(this.bytes=B,B.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static{UY(this,"Int64")}static fromNumber(B){if(B>9223372036854776000||B<-9223372036854776000)throw new Error(`${B} is too large (or, if negative, too small) to represent as an Int64`);let Q=new Uint8Array(8);for(let D=7,Z=Math.abs(Math.round(B));D>-1&&Z>0;D--,Z/=256)Q[D]=Z;if(B<0)Zt1(Q);return new A(Q)}valueOf(){let B=this.bytes.slice(0),Q=B[0]&128;if(Q)Zt1(B);return parseInt(Iy.toHex(B),16)*(Q?-1:1)}toString(){return String(this.valueOf())}};function Zt1(A){for(let B=0;B<8;B++)A[B]^=255;for(let B=7;B>-1;B--)if(A[B]++,A[B]!==0)break}UY(Zt1,"negate");var JEA=UY((A,B)=>{A=A.toLowerCase();for(let Q of Object.keys(B))if(A===Q.toLowerCase())return!0;return!1},"hasHeader"),XEA=mJ(),VEA=UY((A,B={})=>{let{headers:Q,query:D={}}=XEA.HttpRequest.clone(A);for(let Z of Object.keys(Q)){let G=Z.toLowerCase();if(G.slice(0,6)==="x-amz-"&&!B.unhoistableHeaders?.has(G)||B.hoistableHeaders?.has(G))D[Z]=Q[Z],delete Q[Z]}return{...A,headers:Q,query:D}},"moveHeadersToQuery"),Gt1=UY((A)=>{A=XEA.HttpRequest.clone(A);for(let B of Object.keys(A.headers))if(ezA.indexOf(B.toLowerCase())>-1)delete A.headers[B];return A},"prepareRequest"),izA=I5(),BVQ=cB(),KE1=dzA(),CEA=UY(({query:A={}})=>{let B=[],Q={};for(let D of Object.keys(A)){if(D.toLowerCase()===AEA)continue;let Z=KE1.escapeUri(D);B.push(Z);let G=A[D];if(typeof G==="string")Q[Z]=`${Z}=${KE1.escapeUri(G)}`;else if(Array.isArray(G))Q[Z]=G.slice(0).reduce((F,I)=>F.concat([`${Z}=${KE1.escapeUri(I)}`]),[]).sort().join("&")}return B.sort().map((D)=>Q[D]).filter((D)=>D).join("&")},"getCanonicalQuery"),QVQ=UY((A)=>DVQ(A).toISOString().replace(/\.\d{3}Z$/,"Z"),"iso8601"),DVQ=UY((A)=>{if(typeof A==="number")return new Date(A*1000);if(typeof A==="string"){if(Number(A))return new Date(Number(A)*1000);return new Date(A)}return A},"toDate"),KEA=class{static{UY(this,"SignatureV4Base")}constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){this.service=D,this.sha256=Z,this.uriEscapePath=G,this.applyChecksum=typeof A==="boolean"?A:!0,this.regionProvider=izA.normalizeProvider(Q),this.credentialProvider=izA.normalizeProvider(B)}createCanonicalRequest(A,B,Q){let D=Object.keys(B).sort();return`${A.method}
${this.getCanonicalPath(A)}
${CEA(A)}
${D.map((Z)=>`${Z}:${B[Z]}`).join(`
`)}

${D.join(";")}
${Q}`}async createStringToSign(A,B,Q,D){let Z=new this.sha256;Z.update(BVQ.toUint8Array(Q));let G=await Z.digest();return`${D}
${A}
${B}
${Iy.toHex(G)}`}getCanonicalPath({path:A}){if(this.uriEscapePath){let B=[];for(let Z of A.split("/")){if(Z?.length===0)continue;if(Z===".")continue;if(Z==="..")B.pop();else B.push(Z)}let Q=`${A?.startsWith("/")?"/":""}${B.join("/")}${B.length>0&&A?.endsWith("/")?"/":""}`;return KE1.escapeUri(Q).replace(/%2F/g,"/")}return A}validateResolvedCredentials(A){if(typeof A!=="object"||typeof A.accessKeyId!=="string"||typeof A.secretAccessKey!=="string")throw new Error("Resolved credential object is not valid")}formatDate(A){let B=QVQ(A).replace(/[\-:]/g,"");return{longDate:B,shortDate:B.slice(0,8)}}getCanonicalHeaderList(A){return Object.keys(A).sort().join(";")}},ZVQ=class extends KEA{constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){super({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G});this.headerFormatter=new tXQ}static{UY(this,"SignatureV4")}async presign(A,B={}){let{signingDate:Q=new Date,expiresIn:D=3600,unsignableHeaders:Z,unhoistableHeaders:G,signableHeaders:F,hoistableHeaders:I,signingRegion:Y,signingService:W}=B,J=await this.credentialProvider();this.validateResolvedCredentials(J);let X=Y??await this.regionProvider(),{longDate:V,shortDate:C}=this.formatDate(Q);if(D>YEA)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");let K=VE1(C,X,W??this.service),H=VEA(Gt1(A),{unhoistableHeaders:G,hoistableHeaders:I});if(J.sessionToken)H.query[Yt1]=J.sessionToken;H.query[azA]=JE1,H.query[szA]=`${J.accessKeyId}/${K}`,H.query[Ft1]=V,H.query[ozA]=D.toString(10);let z=Dt1(H,Z,F);return H.query[rzA]=this.getCanonicalHeaderList(z),H.query[It1]=await this.getSignature(V,K,this.getSigningKey(J,X,C,W),this.createCanonicalRequest(H,z,await CE1(A,this.sha256))),H}async sign(A,B){if(typeof A==="string")return this.signString(A,B);else if(A.headers&&A.payload)return this.signEvent(A,B);else if(A.message)return this.signMessage(A,B);else return this.signRequest(A,B)}async signEvent({headers:A,payload:B},{signingDate:Q=new Date,priorSignature:D,signingRegion:Z,signingService:G}){let F=Z??await this.regionProvider(),{shortDate:I,longDate:Y}=this.formatDate(Q),W=VE1(I,F,G??this.service),J=await CE1({headers:{},body:B},this.sha256),X=new this.sha256;X.update(A);let V=Iy.toHex(await X.digest()),C=[GEA,Y,W,D,V,J].join(`
`);return this.signString(C,{signingDate:Q,signingRegion:F,signingService:G})}async signMessage(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}){return this.signEvent({headers:this.headerFormatter.format(A.message.headers),payload:A.message.body},{signingDate:B,signingRegion:Q,signingService:D,priorSignature:A.priorSignature}).then((G)=>{return{message:A.message,signature:G}})}async signString(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}={}){let Z=await this.credentialProvider();this.validateResolvedCredentials(Z);let G=Q??await this.regionProvider(),{shortDate:F}=this.formatDate(B),I=new this.sha256(await this.getSigningKey(Z,G,F,D));return I.update(czA.toUint8Array(A)),Iy.toHex(await I.digest())}async signRequest(A,{signingDate:B=new Date,signableHeaders:Q,unsignableHeaders:D,signingRegion:Z,signingService:G}={}){let F=await this.credentialProvider();this.validateResolvedCredentials(F);let I=Z??await this.regionProvider(),Y=Gt1(A),{longDate:W,shortDate:J}=this.formatDate(B),X=VE1(J,I,G??this.service);if(Y.headers[Jt1]=W,F.sessionToken)Y.headers[BEA]=F.sessionToken;let V=await CE1(Y,this.sha256);if(!JEA(HE1,Y.headers)&&this.applyChecksum)Y.headers[HE1]=V;let C=Dt1(Y,D,Q),K=await this.getSignature(W,X,this.getSigningKey(F,I,J,G),this.createCanonicalRequest(Y,C,V));return Y.headers[Wt1]=`${JE1} Credential=${F.accessKeyId}/${X}, SignedHeaders=${this.getCanonicalHeaderList(C)}, Signature=${K}`,Y}async getSignature(A,B,Q,D){let Z=await this.createStringToSign(A,B,D,JE1),G=new this.sha256(await Q);return G.update(czA.toUint8Array(Z)),Iy.toHex(await G.digest())}getSigningKey(A,B,Q,D){return WEA(this.sha256,A,Q,B,D||this.service)}},GVQ={SignatureV4a:null}});
var zn=E((i75,WwA)=>{var{defineProperty:PE1,getOwnPropertyDescriptor:uHQ,getOwnPropertyNames:mHQ}=Object,dHQ=Object.prototype.hasOwnProperty,fO=(A,B)=>PE1(A,"name",{value:B,configurable:!0}),cHQ=(A,B)=>{for(var Q in B)PE1(A,Q,{get:B[Q],enumerable:!0})},lHQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of mHQ(B))if(!dHQ.call(A,Z)&&Z!==Q)PE1(A,Z,{get:()=>B[Z],enumerable:!(D=uHQ(B,Z))||D.enumerable})}return A},pHQ=(A)=>lHQ(PE1({},"__esModule",{value:!0}),A),BwA={};cHQ(BwA,{DEFAULT_UA_APP_ID:()=>QwA,getUserAgentMiddlewareOptions:()=>YwA,getUserAgentPlugin:()=>eHQ,resolveUserAgentConfig:()=>ZwA,userAgentMiddleware:()=>IwA});WwA.exports=pHQ(BwA);var iHQ=VB(),QwA=void 0;function DwA(A){if(A===void 0)return!0;return typeof A==="string"&&A.length<=50}fO(DwA,"isValidUserAgentAppId");function ZwA(A){let B=iHQ.normalizeProvider(A.userAgentAppId??QwA),{customUserAgent:Q}=A;return Object.assign(A,{customUserAgent:typeof Q==="string"?[[Q]]:Q,userAgentAppId:fO(async()=>{let D=await B();if(!DwA(D)){let Z=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console:A.logger;if(typeof D!=="string")Z?.warn("userAgentAppId must be a string or undefined.");else if(D.length>50)Z?.warn("The provided userAgentAppId exceeds the maximum length of 50 characters.")}return D},"userAgentAppId")})}fO(ZwA,"resolveUserAgentConfig");var nHQ=Jn(),aHQ=mJ(),mN=ZI(),sHQ=/\d{12}\.ddb/;async function GwA(A,B,Q){if(Q.request?.headers?.["smithy-protocol"]==="rpc-v2-cbor")mN.setFeature(A,"PROTOCOL_RPC_V2_CBOR","M");if(typeof B.retryStrategy==="function"){let G=await B.retryStrategy();if(typeof G.acquireInitialRetryToken==="function")if(G.constructor?.name?.includes("Adaptive"))mN.setFeature(A,"RETRY_MODE_ADAPTIVE","F");else mN.setFeature(A,"RETRY_MODE_STANDARD","E");else mN.setFeature(A,"RETRY_MODE_LEGACY","D")}if(typeof B.accountIdEndpointMode==="function"){let G=A.endpointV2;if(String(G?.url?.hostname).match(sHQ))mN.setFeature(A,"ACCOUNT_ID_ENDPOINT","O");switch(await B.accountIdEndpointMode?.()){case"disabled":mN.setFeature(A,"ACCOUNT_ID_MODE_DISABLED","Q");break;case"preferred":mN.setFeature(A,"ACCOUNT_ID_MODE_PREFERRED","P");break;case"required":mN.setFeature(A,"ACCOUNT_ID_MODE_REQUIRED","R");break}}let Z=A.__smithy_context?.selectedHttpAuthScheme?.identity;if(Z?.$source){let G=Z;if(G.accountId)mN.setFeature(A,"RESOLVED_ACCOUNT_ID","T");for(let[F,I]of Object.entries(G.$source??{}))mN.setFeature(A,F,I)}}fO(GwA,"checkFeatures");var tUA="user-agent",ut1="x-amz-user-agent",eUA=" ",mt1="/",rHQ=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g,oHQ=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g,AwA="-",tHQ=1024;function FwA(A){let B="";for(let Q in A){let D=A[Q];if(B.length+D.length+1<=tHQ){if(B.length)B+=","+D;else B+=D;continue}break}return B}fO(FwA,"encodeFeatures");var IwA=fO((A)=>(B,Q)=>async(D)=>{let{request:Z}=D;if(!aHQ.HttpRequest.isInstance(Z))return B(D);let{headers:G}=Z,F=Q?.userAgent?.map(TE1)||[],I=(await A.defaultUserAgentProvider()).map(TE1);await GwA(Q,A,D);let Y=Q;I.push(`m/${FwA(Object.assign({},Q.__smithy_context?.features,Y.__aws_sdk_context?.features))}`);let W=A?.customUserAgent?.map(TE1)||[],J=await A.userAgentAppId();if(J)I.push(TE1([`app/${J}`]));let X=nHQ.getUserAgentPrefix(),V=(X?[X]:[]).concat([...I,...F,...W]).join(eUA),C=[...I.filter((K)=>K.startsWith("aws-sdk-")),...W].join(eUA);if(A.runtime!=="browser"){if(C)G[ut1]=G[ut1]?`${G[tUA]} ${C}`:C;G[tUA]=V}else G[ut1]=V;return B({...D,request:Z})},"userAgentMiddleware"),TE1=fO((A)=>{let B=A[0].split(mt1).map((F)=>F.replace(rHQ,AwA)).join(mt1),Q=A[1]?.replace(oHQ,AwA),D=B.indexOf(mt1),Z=B.substring(0,D),G=B.substring(D+1);if(Z==="api")G=G.toLowerCase();return[Z,G,Q].filter((F)=>F&&F.length>0).reduce((F,I,Y)=>{switch(Y){case 0:return I;case 1:return`${F}/${I}`;default:return`${F}#${I}`}},"")},"escapeUserAgent"),YwA={name:"getUserAgentMiddleware",step:"build",priority:"low",tags:["SET_USER_AGENT","USER_AGENT"],override:!0},eHQ=fO((A)=>({applyToStack:fO((B)=>{B.add(IwA(A),YwA)},"applyToStack")}),"getUserAgentPlugin")});
var zqA=E((KqA)=>{Object.defineProperty(KqA,"__esModule",{value:!0});KqA.default=void 0;var kwQ=_wQ(Ae1()),ywQ=aQ1();function _wQ(A){return A&&A.__esModule?A:{default:A}}var CqA,Be1,Qe1=0,De1=0;function xwQ(A,B,Q){let D=B&&Q||0,Z=B||new Array(16);A=A||{};let G=A.node||CqA,F=A.clockseq!==void 0?A.clockseq:Be1;if(G==null||F==null){let V=A.random||(A.rng||kwQ.default)();if(G==null)G=CqA=[V[0]|1,V[1],V[2],V[3],V[4],V[5]];if(F==null)F=Be1=(V[6]<<8|V[7])&16383}let I=A.msecs!==void 0?A.msecs:Date.now(),Y=A.nsecs!==void 0?A.nsecs:De1+1,W=I-Qe1+(Y-De1)/1e4;if(W<0&&A.clockseq===void 0)F=F+1&16383;if((W<0||I>Qe1)&&A.nsecs===void 0)Y=0;if(Y>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");Qe1=I,De1=Y,Be1=F,I+=12219292800000;let J=((I&268435455)*1e4+Y)%**********;Z[D++]=J>>>24&255,Z[D++]=J>>>16&255,Z[D++]=J>>>8&255,Z[D++]=J&255;let X=I/***********1e4&268435455;Z[D++]=X>>>8&255,Z[D++]=X&255,Z[D++]=X>>>24&15|16,Z[D++]=X>>>16&255,Z[D++]=F>>>8|128,Z[D++]=F&255;for(let V=0;V<6;++V)Z[D+V]=G[V];return B||ywQ.unsafeStringify(Z)}var vwQ=xwQ;KqA.default=vwQ});

module.exports = wjA;
