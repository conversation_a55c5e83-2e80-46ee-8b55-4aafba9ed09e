// dependency_chain package extracted with entry point: Ie0

var et0=E((da8,tt0)=>{var it0=pt0();tt0.exports=g_9;var nt0="\x00SLASH"+Math.random()+"\x00",at0="\x00OPEN"+Math.random()+"\x00",Pi1="\x00CLOSE"+Math.random()+"\x00",st0="\x00COMMA"+Math.random()+"\x00",rt0="\x00PERIOD"+Math.random()+"\x00";function Ti1(A){return parseInt(A,10)==A?parseInt(A,10):A.charCodeAt(0)}function f_9(A){return A.split("\\\\").join(nt0).split("\\{").join(at0).split("\\}").join(Pi1).split("\\,").join(st0).split("\\.").join(rt0)}function h_9(A){return A.split(nt0).join("\\").split(at0).join("{").split(Pi1).join("}").split(st0).join(",").split(rt0).join(".")}function ot0(A){if(!A)return[""];var B=[],Q=it0("{","}",A);if(!Q)return A.split(",");var{pre:D,body:Z,post:G}=Q,F=D.split(",");F[F.length-1]+="{"+Z+"}";var I=ot0(G);if(G.length)F[F.length-1]+=I.shift(),F.push.apply(F,I);return B.push.apply(B,F),B}function g_9(A){if(!A)return[];if(A.substr(0,2)==="{}")A="\\{\\}"+A.substr(2);return G91(f_9(A),!0).map(h_9)}function u_9(A){return"{"+A+"}"}function m_9(A){return/^-?0\d/.test(A)}function d_9(A,B){return A<=B}function c_9(A,B){return A>=B}function G91(A,B){var Q=[],D=it0("{","}",A);if(!D)return[A];var Z=D.pre,G=D.post.length?G91(D.post,!1):[""];if(/\$$/.test(D.pre))for(var F=0;F<G.length;F++){var I=Z+"{"+D.body+"}"+G[F];Q.push(I)}else{var Y=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(D.body),W=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(D.body),J=Y||W,X=D.body.indexOf(",")>=0;if(!J&&!X){if(D.post.match(/,.*\}/))return A=D.pre+"{"+D.body+Pi1+D.post,G91(A);return[A]}var V;if(J)V=D.body.split(/\.\./);else if(V=ot0(D.body),V.length===1){if(V=G91(V[0],!1).map(u_9),V.length===1)return G.map(function(c){return D.pre+V[0]+c})}var C;if(J){var K=Ti1(V[0]),H=Ti1(V[1]),z=Math.max(V[0].length,V[1].length),$=V.length==3?Math.abs(Ti1(V[2])):1,L=d_9,N=H<K;if(N)$*=-1,L=c_9;var O=V.some(m_9);C=[];for(var R=K;L(R,H);R+=$){var T;if(W){if(T=String.fromCharCode(R),T==="\\")T=""}else if(T=String(R),O){var j=z-T.length;if(j>0){var f=new Array(j+1).join("0");if(R<0)T="-"+f+T.slice(1);else T=f+T}}C.push(T)}}else{C=[];for(var k=0;k<V.length;k++)C.push.apply(C,G91(V[k],!1))}for(var k=0;k<C.length;k++)for(var F=0;F<G.length;F++){var I=Z+C[k]+G[F];if(!B||J||I)Q.push(I)}}return Q}});
var pt0=E((ma8,lt0)=>{lt0.exports=dt0;function dt0(A,B,Q){if(A instanceof RegExp)A=mt0(A,Q);if(B instanceof RegExp)B=mt0(B,Q);var D=ct0(A,B,Q);return D&&{start:D[0],end:D[1],pre:Q.slice(0,D[0]),body:Q.slice(D[0]+A.length,D[1]),post:Q.slice(D[1]+B.length)}}function mt0(A,B){var Q=B.match(A);return Q?Q[0]:null}dt0.range=ct0;function ct0(A,B,Q){var D,Z,G,F,I,Y=Q.indexOf(A),W=Q.indexOf(B,Y+1),J=Y;if(Y>=0&&W>0){if(A===B)return[Y,W];D=[],G=Q.length;while(J>=0&&!I){if(J==Y)D.push(J),Y=Q.indexOf(A,J+1);else if(D.length==1)I=[D.pop(),W];else{if(Z=D.pop(),Z<G)G=Z,F=W;W=Q.indexOf(B,J+1)}J=Y<W&&Y>=0?Y:W}if(D.length)I=[G,F]}return I}});

module.exports = Ie0;
