// dependency_chain package extracted with entry point: rA1

var fS0=E((Py8,bS0)=>{bS0.exports=function A(B){return B.map(function(Q){if(Q==="")return"''";if(Q&&typeof Q==="object")return Q.op.replace(/(.)/g,"\\$1");if(/["\s\\]/.test(Q)&&!/'/.test(Q))return"'"+Q.replace(/(['])/g,"\\$1")+"'";if(/["'\s]/.test(Q))return'"'+Q.replace(/(["\\$`!])/g,"\\$1")+'"';return String(Q).replace(/([A-Za-z]:)?([#!"$&'()*,:;<=>?@[\\\]^`{|}])/g,"$1\\$2")}).join(" ")}});
var kj=E((vaB)=>{vaB.quote=fS0();vaB.parse=lS0()});
var lS0=E((Sy8,cS0)=>{var dS0="(?:"+["\\|\\|","\\&\\&",";;","\\|\\&","\\<\\(","\\<\\<\\<",">>",">\\&","<\\&","[&;()|<>]"].join("|")+")",hS0=new RegExp("^"+dS0+"$"),gS0="|&;()<> \\t",TaB='"((\\\\"|[^"])*?)"',PaB="'((\\\\'|[^'])*?)'",SaB=/^#$/,uS0="'",mS0='"',Zu1="$",tb="",jaB=4294967296;for(AW1=0;AW1<4;AW1++)tb+=(jaB*Math.random()).toString(16);var AW1,kaB=new RegExp("^"+tb);function yaB(A,B){var Q=B.lastIndex,D=[],Z;while(Z=B.exec(A))if(D.push(Z),B.lastIndex===Z.index)B.lastIndex+=1;return B.lastIndex=Q,D}function _aB(A,B,Q){var D=typeof A==="function"?A(Q):A[Q];if(typeof D==="undefined"&&Q!="")D="";else if(typeof D==="undefined")D="$";if(typeof D==="object")return B+tb+JSON.stringify(D)+tb;return B+D}function xaB(A,B,Q){if(!Q)Q={};var D=Q.escape||"\\",Z="(\\"+D+`['"`+gS0+`]|[^\\s'"`+gS0+"])+",G=new RegExp(["("+dS0+")","("+Z+"|"+TaB+"|"+PaB+")+"].join("|"),"g"),F=yaB(A,G);if(F.length===0)return[];if(!B)B={};var I=!1;return F.map(function(Y){var W=Y[0];if(!W||I)return;if(hS0.test(W))return{op:W};var J=!1,X=!1,V="",C=!1,K;function H(){K+=1;var L,N,O=W.charAt(K);if(O==="{"){if(K+=1,W.charAt(K)==="}")throw new Error("Bad substitution: "+W.slice(K-2,K+1));if(L=W.indexOf("}",K),L<0)throw new Error("Bad substitution: "+W.slice(K));N=W.slice(K,L),K=L}else if(/[*@#?$!_-]/.test(O))N=O,K+=1;else{var R=W.slice(K);if(L=R.match(/[^\w\d_]/),!L)N=R,K=W.length;else N=R.slice(0,L.index),K+=L.index-1}return _aB(B,"",N)}for(K=0;K<W.length;K++){var z=W.charAt(K);if(C=C||!J&&(z==="*"||z==="?"),X)V+=z,X=!1;else if(J)if(z===J)J=!1;else if(J==uS0)V+=z;else if(z===D)if(K+=1,z=W.charAt(K),z===mS0||z===D||z===Zu1)V+=z;else V+=D+z;else if(z===Zu1)V+=H();else V+=z;else if(z===mS0||z===uS0)J=z;else if(hS0.test(z))return{op:W};else if(SaB.test(z)){I=!0;var $={comment:A.slice(Y.index+K+1)};if(V.length)return[V,$];return[$]}else if(z===D)X=!0;else if(z===Zu1)V+=H();else V+=z}if(C)return{op:"glob",pattern:V};return V}).reduce(function(Y,W){return typeof W==="undefined"?Y:Y.concat(W)},[])}cS0.exports=function A(B,Q,D){var Z=xaB(B,Q,D);if(typeof Q!=="function")return Z;return Z.reduce(function(G,F){if(typeof F==="object")return G.concat(F);var I=F.split(RegExp("("+tb+".*?"+tb+")","g"));if(I.length===1)return G.concat(I[0]);return G.concat(I.filter(Boolean).map(function(Y){if(kaB.test(Y))return JSON.parse(Y.split(tb)[1]);return Y}))},[])}});

module.exports = rA1;
