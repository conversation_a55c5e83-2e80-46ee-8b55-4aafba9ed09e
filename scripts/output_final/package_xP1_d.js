// dependency_chain package extracted with entry point: xP1

var $R2=E((UR2)=>{Object.defineProperty(UR2,"__esModule",{value:!0});UR2.deleteBaggage=UR2.setBaggage=UR2.getActiveBaggage=UR2.getBaggage=void 0;var nn4=g51(),an4=f51(),iG0=an4.createContextKey("OpenTelemetry Baggage Key");function ER2(A){return A.getValue(iG0)||void 0}UR2.getBaggage=ER2;function sn4(){return ER2(nn4.ContextAPI.getInstance().active())}UR2.getActiveBaggage=sn4;function rn4(A,B){return A.setValue(iG0,B)}UR2.setBaggage=rn4;function on4(A){return A.deleteValue(iG0)}UR2.deleteBaggage=on4});
var AL2=E((Yu)=>{var Fi4=Yu&&Yu.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;Object.defineProperty(A,D,{enumerable:!0,get:function(){return B[Q]}})}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),Ii4=Yu&&Yu.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))Fi4(B,A,Q)};Object.defineProperty(Yu,"__esModule",{value:!0});Ii4(eN2(),Yu)});
var CL2=E((XL2)=>{Object.defineProperty(XL2,"__esModule",{value:!0});XL2.DiagComponentLogger=void 0;var Ui4=Wu();class JL2{constructor(A){this._namespace=A.namespace||"DiagComponentLogger"}debug(...A){return b51("debug",this._namespace,A)}error(...A){return b51("error",this._namespace,A)}info(...A){return b51("info",this._namespace,A)}warn(...A){return b51("warn",this._namespace,A)}verbose(...A){return b51("verbose",this._namespace,A)}}XL2.DiagComponentLogger=JL2;function b51(A,B,Q){let D=Ui4.getGlobal("diag");if(!D)return;return Q.unshift(B),D[A](...Q)}});
var CO1=E((zM2)=>{Object.defineProperty(zM2,"__esModule",{value:!0});zM2.wrapSpanContext=zM2.isSpanContextValid=zM2.isValidSpanId=zM2.isValidTraceId=void 0;var CM2=XO1(),Jn4=VO1(),Xn4=/^([0-9a-f]{32})$/i,Vn4=/^[0-9a-f]{16}$/i;function KM2(A){return Xn4.test(A)&&A!==CM2.INVALID_TRACEID}zM2.isValidTraceId=KM2;function HM2(A){return Vn4.test(A)&&A!==CM2.INVALID_SPANID}zM2.isValidSpanId=HM2;function Cn4(A){return KM2(A.traceId)&&HM2(A.spanId)}zM2.isSpanContextValid=Cn4;function Kn4(A){return new Jn4.NonRecordingSpan(A)}zM2.wrapSpanContext=Kn4});
var EL2=E((HL2)=>{Object.defineProperty(HL2,"__esModule",{value:!0});HL2.createLogLevelDiagLogger=void 0;var DP=YO1();function $i4(A,B){if(A<DP.DiagLogLevel.NONE)A=DP.DiagLogLevel.NONE;else if(A>DP.DiagLogLevel.ALL)A=DP.DiagLogLevel.ALL;B=B||{};function Q(D,Z){let G=B[D];if(typeof G==="function"&&A>=Z)return G.bind(B);return function(){}}return{error:Q("error",DP.DiagLogLevel.ERROR),warn:Q("warn",DP.DiagLogLevel.WARN),info:Q("info",DP.DiagLogLevel.INFO),debug:Q("debug",DP.DiagLogLevel.DEBUG),verbose:Q("verbose",DP.DiagLogLevel.VERBOSE)}}HL2.createLogLevelDiagLogger=$i4});
var GR2=E((DR2)=>{Object.defineProperty(DR2,"__esModule",{value:!0});DR2.NOOP_METER_PROVIDER=DR2.NoopMeterProvider=void 0;var cn4=qG0();class dG0{getMeter(A,B,Q){return cn4.NOOP_METER}}DR2.NoopMeterProvider=dG0;DR2.NOOP_METER_PROVIDER=new dG0});
var IL2=E((GL2)=>{Object.defineProperty(GL2,"__esModule",{value:!0});GL2.isCompatible=GL2._makeCompatibilityCheck=void 0;var Yi4=YG0(),DL2=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function ZL2(A){let B=new Set([A]),Q=new Set,D=A.match(DL2);if(!D)return()=>!1;let Z={major:+D[1],minor:+D[2],patch:+D[3],prerelease:D[4]};if(Z.prerelease!=null)return function I(Y){return Y===A};function G(I){return Q.add(I),!1}function F(I){return B.add(I),!0}return function I(Y){if(B.has(Y))return!0;if(Q.has(Y))return!1;let W=Y.match(DL2);if(!W)return G(Y);let J={major:+W[1],minor:+W[2],patch:+W[3],prerelease:W[4]};if(J.prerelease!=null)return G(Y);if(Z.major!==J.major)return G(Y);if(Z.major===0){if(Z.minor===J.minor&&Z.patch<=J.patch)return F(Y);return G(Y)}if(Z.minor<=J.minor)return F(Y);return G(Y)}}GL2._makeCompatibilityCheck=ZL2;GL2.isCompatible=ZL2(Yi4.VERSION)});
var Ju=E((wL2)=>{Object.defineProperty(wL2,"__esModule",{value:!0});wL2.DiagAPI=void 0;var qi4=CL2(),Ni4=EL2(),UL2=YO1(),WO1=Wu(),Li4="diag";class JG0{constructor(){function A(D){return function(...Z){let G=WO1.getGlobal("diag");if(!G)return;return G[D](...Z)}}let B=this,Q=(D,Z={logLevel:UL2.DiagLogLevel.INFO})=>{var G,F,I;if(D===B){let J=new Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return B.error((G=J.stack)!==null&&G!==void 0?G:J.message),!1}if(typeof Z==="number")Z={logLevel:Z};let Y=WO1.getGlobal("diag"),W=Ni4.createLogLevelDiagLogger((F=Z.logLevel)!==null&&F!==void 0?F:UL2.DiagLogLevel.INFO,D);if(Y&&!Z.suppressOverrideMessage){let J=(I=new Error().stack)!==null&&I!==void 0?I:"<failed to generate stacktrace>";Y.warn(`Current logger will be overwritten from ${J}`),W.warn(`Current logger will overwrite one already registered from ${J}`)}return WO1.registerGlobal("diag",W,B,!0)};B.setLogger=Q,B.disable=()=>{WO1.unregisterGlobal(Li4,B)},B.createComponentLogger=(D)=>{return new qi4.DiagComponentLogger(D)},B.verbose=A("verbose"),B.debug=A("debug"),B.info=A("info"),B.warn=A("warn"),B.error=A("error")}static instance(){if(!this._instance)this._instance=new JG0;return this._instance}}wL2.DiagAPI=JG0});
var LG0=E((nL2)=>{Object.defineProperty(nL2,"__esModule",{value:!0});nL2.defaultTextMapSetter=nL2.defaultTextMapGetter=void 0;nL2.defaultTextMapGetter={get(A,B){if(A==null)return;return A[B]},keys(A){if(A==null)return[];return Object.keys(A)}};nL2.defaultTextMapSetter={set(A,B,Q){if(A==null)return;A[B]=Q}}});
var LL2=E((qL2)=>{Object.defineProperty(qL2,"__esModule",{value:!0});qL2.BaggageImpl=void 0;class Mo{constructor(A){this._entries=A?new Map(A):new Map}getEntry(A){let B=this._entries.get(A);if(!B)return;return Object.assign({},B)}getAllEntries(){return Array.from(this._entries.entries()).map(([A,B])=>[A,B])}setEntry(A,B){let Q=new Mo(this._entries);return Q._entries.set(A,B),Q}removeEntry(A){let B=new Mo(this._entries);return B._entries.delete(A),B}removeEntries(...A){let B=new Mo(this._entries);for(let Q of A)B._entries.delete(Q);return B}clear(){return new Mo}}qL2.BaggageImpl=Mo});
var OL2=E((ML2)=>{Object.defineProperty(ML2,"__esModule",{value:!0});ML2.baggageEntryMetadataSymbol=void 0;ML2.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")});
var PG0=E((QM2)=>{Object.defineProperty(QM2,"__esModule",{value:!0});QM2.TraceFlags=void 0;var si4;(function(A){A[A.NONE=0]="NONE",A[A.SAMPLED=1]="SAMPLED"})(si4=QM2.TraceFlags||(QM2.TraceFlags={}))});
var PM2=E((OM2)=>{Object.defineProperty(OM2,"__esModule",{value:!0});OM2.NoopTracerProvider=void 0;var Ln4=xG0();class RM2{getTracer(A,B,Q){return new Ln4.NoopTracer}}OM2.NoopTracerProvider=RM2});
var PR2=E((OR2)=>{Object.defineProperty(OR2,"__esModule",{value:!0});OR2.propagation=void 0;var Za4=RR2();OR2.propagation=Za4.PropagationAPI.getInstance()});
var QR2=E((AR2)=>{Object.defineProperty(AR2,"__esModule",{value:!0});AR2.diag=void 0;var dn4=Ju();AR2.diag=dn4.DiagAPI.instance()});
var RR2=E((LR2)=>{Object.defineProperty(LR2,"__esModule",{value:!0});LR2.PropagationAPI=void 0;var nG0=Wu(),Ba4=zR2(),qR2=LG0(),KO1=$R2(),Qa4=XG0(),NR2=Ju(),aG0="propagation",Da4=new Ba4.NoopTextMapPropagator;class sG0{constructor(){this.createBaggage=Qa4.createBaggage,this.getBaggage=KO1.getBaggage,this.getActiveBaggage=KO1.getActiveBaggage,this.setBaggage=KO1.setBaggage,this.deleteBaggage=KO1.deleteBaggage}static getInstance(){if(!this._instance)this._instance=new sG0;return this._instance}setGlobalPropagator(A){return nG0.registerGlobal(aG0,A,NR2.DiagAPI.instance())}inject(A,B,Q=qR2.defaultTextMapSetter){return this._getGlobalPropagator().inject(A,B,Q)}extract(A,B,Q=qR2.defaultTextMapGetter){return this._getGlobalPropagator().extract(A,B,Q)}fields(){return this._getGlobalPropagator().fields()}disable(){nG0.unregisterGlobal(aG0,NR2.DiagAPI.instance())}_getGlobalPropagator(){return nG0.getGlobal(aG0)||Da4}}LR2.PropagationAPI=sG0});
var VO1=E((YM2)=>{Object.defineProperty(YM2,"__esModule",{value:!0});YM2.NonRecordingSpan=void 0;var oi4=XO1();class IM2{constructor(A=oi4.INVALID_SPAN_CONTEXT){this._spanContext=A}spanContext(){return this._spanContext}setAttribute(A,B){return this}setAttributes(A){return this}addEvent(A,B){return this}addLink(A){return this}addLinks(A){return this}setStatus(A){return this}updateName(A){return this}end(A){}isRecording(){return!1}recordException(A,B){}}YM2.NonRecordingSpan=IM2});
var VR2=E((JR2)=>{Object.defineProperty(JR2,"__esModule",{value:!0});JR2.metrics=void 0;var in4=WR2();JR2.metrics=in4.MetricsAPI.getInstance()});
var WR2=E((IR2)=>{Object.defineProperty(IR2,"__esModule",{value:!0});IR2.MetricsAPI=void 0;var pn4=GR2(),cG0=Wu(),FR2=Ju(),lG0="metrics";class pG0{constructor(){}static getInstance(){if(!this._instance)this._instance=new pG0;return this._instance}setGlobalMeterProvider(A){return cG0.registerGlobal(lG0,A,FR2.DiagAPI.instance())}getMeterProvider(){return cG0.getGlobal(lG0)||pn4.NOOP_METER_PROVIDER}getMeter(A,B,Q){return this.getMeterProvider().getMeter(A,B,Q)}disable(){cG0.unregisterGlobal(lG0,FR2.DiagAPI.instance())}}IR2.MetricsAPI=pG0});
var Wu=E((YL2)=>{Object.defineProperty(YL2,"__esModule",{value:!0});YL2.unregisterGlobal=YL2.getGlobal=YL2.registerGlobal=void 0;var Ji4=AL2(),Lo=YG0(),Xi4=IL2(),Vi4=Lo.VERSION.split(".")[0],x51=Symbol.for(`opentelemetry.js.api.${Vi4}`),v51=Ji4._globalThis;function Ci4(A,B,Q,D=!1){var Z;let G=v51[x51]=(Z=v51[x51])!==null&&Z!==void 0?Z:{version:Lo.VERSION};if(!D&&G[A]){let F=new Error(`@opentelemetry/api: Attempted duplicate registration of API: ${A}`);return Q.error(F.stack||F.message),!1}if(G.version!==Lo.VERSION){let F=new Error(`@opentelemetry/api: Registration of version v${G.version} for ${A} does not match previously registered API v${Lo.VERSION}`);return Q.error(F.stack||F.message),!1}return G[A]=B,Q.debug(`@opentelemetry/api: Registered a global for ${A} v${Lo.VERSION}.`),!0}YL2.registerGlobal=Ci4;function Ki4(A){var B,Q;let D=(B=v51[x51])===null||B===void 0?void 0:B.version;if(!D||!Xi4.isCompatible(D))return;return(Q=v51[x51])===null||Q===void 0?void 0:Q[A]}YL2.getGlobal=Ki4;function Hi4(A,B){B.debug(`@opentelemetry/api: Unregistering a global for ${A} v${Lo.VERSION}.`);let Q=v51[x51];if(Q)delete Q[A]}YL2.unregisterGlobal=Hi4});
var XG0=E((TL2)=>{Object.defineProperty(TL2,"__esModule",{value:!0});TL2.baggageEntryMetadataFromString=TL2.createBaggage=void 0;var Mi4=Ju(),Ri4=LL2(),Oi4=OL2(),Ti4=Mi4.DiagAPI.instance();function Pi4(A={}){return new Ri4.BaggageImpl(new Map(Object.entries(A)))}TL2.createBaggage=Pi4;function Si4(A){if(typeof A!=="string")Ti4.error(`Cannot create baggage metadata from unknown type: ${typeof A}`),A="";return{__TYPE__:Oi4.baggageEntryMetadataSymbol,toString(){return A}}}TL2.baggageEntryMetadataFromString=Si4});
var XO1=E((DM2)=>{Object.defineProperty(DM2,"__esModule",{value:!0});DM2.INVALID_SPAN_CONTEXT=DM2.INVALID_TRACEID=DM2.INVALID_SPANID=void 0;var ri4=PG0();DM2.INVALID_SPANID="0000000000000000";DM2.INVALID_TRACEID="00000000000000000000000000000000";DM2.INVALID_SPAN_CONTEXT={traceId:DM2.INVALID_TRACEID,spanId:DM2.INVALID_SPANID,traceFlags:ri4.TraceFlags.NONE}});
var YG0=E((BL2)=>{Object.defineProperty(BL2,"__esModule",{value:!0});BL2.VERSION=void 0;BL2.VERSION="1.9.0"});
var YO1=E((KL2)=>{Object.defineProperty(KL2,"__esModule",{value:!0});KL2.DiagLogLevel=void 0;var wi4;(function(A){A[A.NONE=0]="NONE",A[A.ERROR=30]="ERROR",A[A.WARN=50]="WARN",A[A.INFO=60]="INFO",A[A.DEBUG=70]="DEBUG",A[A.VERBOSE=80]="VERBOSE",A[A.ALL=9999]="ALL"})(wi4=KL2.DiagLogLevel||(KL2.DiagLogLevel={}))});
var ZQ=E((d5)=>{Object.defineProperty(d5,"__esModule",{value:!0});d5.trace=d5.propagation=d5.metrics=d5.diag=d5.context=d5.INVALID_SPAN_CONTEXT=d5.INVALID_TRACEID=d5.INVALID_SPANID=d5.isValidSpanId=d5.isValidTraceId=d5.isSpanContextValid=d5.createTraceState=d5.TraceFlags=d5.SpanStatusCode=d5.SpanKind=d5.SamplingDecision=d5.ProxyTracerProvider=d5.ProxyTracer=d5.defaultTextMapSetter=d5.defaultTextMapGetter=d5.ValueType=d5.createNoopMeter=d5.DiagLogLevel=d5.DiagConsoleLogger=d5.ROOT_CONTEXT=d5.createContextKey=d5.baggageEntryMetadataFromString=void 0;var Fa4=XG0();Object.defineProperty(d5,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return Fa4.baggageEntryMetadataFromString}});var hR2=f51();Object.defineProperty(d5,"createContextKey",{enumerable:!0,get:function(){return hR2.createContextKey}});Object.defineProperty(d5,"ROOT_CONTEXT",{enumerable:!0,get:function(){return hR2.ROOT_CONTEXT}});var Ia4=xL2();Object.defineProperty(d5,"DiagConsoleLogger",{enumerable:!0,get:function(){return Ia4.DiagConsoleLogger}});var Ya4=YO1();Object.defineProperty(d5,"DiagLogLevel",{enumerable:!0,get:function(){return Ya4.DiagLogLevel}});var Wa4=qG0();Object.defineProperty(d5,"createNoopMeter",{enumerable:!0,get:function(){return Wa4.createNoopMeter}});var Ja4=iL2();Object.defineProperty(d5,"ValueType",{enumerable:!0,get:function(){return Ja4.ValueType}});var gR2=LG0();Object.defineProperty(d5,"defaultTextMapGetter",{enumerable:!0,get:function(){return gR2.defaultTextMapGetter}});Object.defineProperty(d5,"defaultTextMapSetter",{enumerable:!0,get:function(){return gR2.defaultTextMapSetter}});var Xa4=vG0();Object.defineProperty(d5,"ProxyTracer",{enumerable:!0,get:function(){return Xa4.ProxyTracer}});var Va4=bG0();Object.defineProperty(d5,"ProxyTracerProvider",{enumerable:!0,get:function(){return Va4.ProxyTracerProvider}});var Ca4=_M2();Object.defineProperty(d5,"SamplingDecision",{enumerable:!0,get:function(){return Ca4.SamplingDecision}});var Ka4=vM2();Object.defineProperty(d5,"SpanKind",{enumerable:!0,get:function(){return Ka4.SpanKind}});var Ha4=fM2();Object.defineProperty(d5,"SpanStatusCode",{enumerable:!0,get:function(){return Ha4.SpanStatusCode}});var za4=PG0();Object.defineProperty(d5,"TraceFlags",{enumerable:!0,get:function(){return za4.TraceFlags}});var Ea4=rM2();Object.defineProperty(d5,"createTraceState",{enumerable:!0,get:function(){return Ea4.createTraceState}});var eG0=CO1();Object.defineProperty(d5,"isSpanContextValid",{enumerable:!0,get:function(){return eG0.isSpanContextValid}});Object.defineProperty(d5,"isValidTraceId",{enumerable:!0,get:function(){return eG0.isValidTraceId}});Object.defineProperty(d5,"isValidSpanId",{enumerable:!0,get:function(){return eG0.isValidSpanId}});var AF0=XO1();Object.defineProperty(d5,"INVALID_SPANID",{enumerable:!0,get:function(){return AF0.INVALID_SPANID}});Object.defineProperty(d5,"INVALID_TRACEID",{enumerable:!0,get:function(){return AF0.INVALID_TRACEID}});Object.defineProperty(d5,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return AF0.INVALID_SPAN_CONTEXT}});var uR2=eM2();Object.defineProperty(d5,"context",{enumerable:!0,get:function(){return uR2.context}});var mR2=QR2();Object.defineProperty(d5,"diag",{enumerable:!0,get:function(){return mR2.diag}});var dR2=VR2();Object.defineProperty(d5,"metrics",{enumerable:!0,get:function(){return dR2.metrics}});var cR2=PR2();Object.defineProperty(d5,"propagation",{enumerable:!0,get:function(){return cR2.propagation}});var lR2=fR2();Object.defineProperty(d5,"trace",{enumerable:!0,get:function(){return lR2.trace}});d5.default={context:uR2.context,diag:mR2.diag,metrics:dR2.metrics,propagation:cR2.propagation,trace:lR2.trace}});
var _M2=E((yM2)=>{Object.defineProperty(yM2,"__esModule",{value:!0});yM2.SamplingDecision=void 0;var Tn4;(function(A){A[A.NOT_RECORD=0]="NOT_RECORD",A[A.RECORD=1]="RECORD",A[A.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"})(Tn4=yM2.SamplingDecision||(yM2.SamplingDecision={}))});
var bG0=E((jM2)=>{Object.defineProperty(jM2,"__esModule",{value:!0});jM2.ProxyTracerProvider=void 0;var Mn4=vG0(),Rn4=PM2(),On4=new Rn4.NoopTracerProvider;class SM2{getTracer(A,B,Q){var D;return(D=this.getDelegateTracer(A,B,Q))!==null&&D!==void 0?D:new Mn4.ProxyTracer(this,A,B,Q)}getDelegate(){var A;return(A=this._delegate)!==null&&A!==void 0?A:On4}setDelegate(A){this._delegate=A}getDelegateTracer(A,B,Q){var D;return(D=this._delegate)===null||D===void 0?void 0:D.getTracer(A,B,Q)}}jM2.ProxyTracerProvider=SM2});
var eM2=E((oM2)=>{Object.defineProperty(oM2,"__esModule",{value:!0});oM2.context=void 0;var mn4=g51();oM2.context=mn4.ContextAPI.getInstance()});
var eN2=E((Iu)=>{var Zi4=Iu&&Iu.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;Object.defineProperty(A,D,{enumerable:!0,get:function(){return B[Q]}})}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),Gi4=Iu&&Iu.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))Zi4(B,A,Q)};Object.defineProperty(Iu,"__esModule",{value:!0});Gi4(tN2(),Iu)});
var f51=E((SL2)=>{Object.defineProperty(SL2,"__esModule",{value:!0});SL2.ROOT_CONTEXT=SL2.createContextKey=void 0;function ki4(A){return Symbol.for(A)}SL2.createContextKey=ki4;class JO1{constructor(A){let B=this;B._currentContext=A?new Map(A):new Map,B.getValue=(Q)=>B._currentContext.get(Q),B.setValue=(Q,D)=>{let Z=new JO1(B._currentContext);return Z._currentContext.set(Q,D),Z},B.deleteValue=(Q)=>{let D=new JO1(B._currentContext);return D._currentContext.delete(Q),D}}}SL2.ROOT_CONTEXT=new JO1});
var fM2=E((bM2)=>{Object.defineProperty(bM2,"__esModule",{value:!0});bM2.SpanStatusCode=void 0;var Sn4;(function(A){A[A.UNSET=0]="UNSET",A[A.OK=1]="OK",A[A.ERROR=2]="ERROR"})(Sn4=bM2.SpanStatusCode||(bM2.SpanStatusCode={}))});
var fR2=E((vR2)=>{Object.defineProperty(vR2,"__esModule",{value:!0});vR2.trace=void 0;var Ga4=xR2();vR2.trace=Ga4.TraceAPI.getInstance()});
var g51=E((AM2)=>{Object.defineProperty(AM2,"__esModule",{value:!0});AM2.ContextAPI=void 0;var ni4=tL2(),MG0=Wu(),eL2=Ju(),RG0="context",ai4=new ni4.NoopContextManager;class OG0{constructor(){}static getInstance(){if(!this._instance)this._instance=new OG0;return this._instance}setGlobalContextManager(A){return MG0.registerGlobal(RG0,A,eL2.DiagAPI.instance())}active(){return this._getContextManager().active()}with(A,B,Q,...D){return this._getContextManager().with(A,B,Q,...D)}bind(A,B){return this._getContextManager().bind(A,B)}_getContextManager(){return MG0.getGlobal(RG0)||ai4}disable(){this._getContextManager().disable(),MG0.unregisterGlobal(RG0,eL2.DiagAPI.instance())}}AM2.ContextAPI=OG0});
var iL2=E((pL2)=>{Object.defineProperty(pL2,"__esModule",{value:!0});pL2.ValueType=void 0;var li4;(function(A){A[A.INT=0]="INT",A[A.DOUBLE=1]="DOUBLE"})(li4=pL2.ValueType||(pL2.ValueType={}))});
var kG0=E((XM2)=>{Object.defineProperty(XM2,"__esModule",{value:!0});XM2.getSpanContext=XM2.setSpanContext=XM2.deleteSpan=XM2.setSpan=XM2.getActiveSpan=XM2.getSpan=void 0;var ti4=f51(),ei4=VO1(),An4=g51(),SG0=ti4.createContextKey("OpenTelemetry Context Key SPAN");function jG0(A){return A.getValue(SG0)||void 0}XM2.getSpan=jG0;function Bn4(){return jG0(An4.ContextAPI.getInstance().active())}XM2.getActiveSpan=Bn4;function JM2(A,B){return A.setValue(SG0,B)}XM2.setSpan=JM2;function Qn4(A){return A.deleteValue(SG0)}XM2.deleteSpan=Qn4;function Dn4(A,B){return JM2(A,new ei4.NonRecordingSpan(B))}XM2.setSpanContext=Dn4;function Zn4(A){var B;return(B=jG0(A))===null||B===void 0?void 0:B.spanContext()}XM2.getSpanContext=Zn4});
var nM2=E((pM2)=>{Object.defineProperty(pM2,"__esModule",{value:!0});pM2.TraceStateImpl=void 0;var mM2=uM2(),dM2=32,hn4=512,cM2=",",lM2="=";class mG0{constructor(A){if(this._internalState=new Map,A)this._parse(A)}set(A,B){let Q=this._clone();if(Q._internalState.has(A))Q._internalState.delete(A);return Q._internalState.set(A,B),Q}unset(A){let B=this._clone();return B._internalState.delete(A),B}get(A){return this._internalState.get(A)}serialize(){return this._keys().reduce((A,B)=>{return A.push(B+lM2+this.get(B)),A},[]).join(cM2)}_parse(A){if(A.length>hn4)return;if(this._internalState=A.split(cM2).reverse().reduce((B,Q)=>{let D=Q.trim(),Z=D.indexOf(lM2);if(Z!==-1){let G=D.slice(0,Z),F=D.slice(Z+1,Q.length);if(mM2.validateKey(G)&&mM2.validateValue(F))B.set(G,F)}return B},new Map),this._internalState.size>dM2)this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,dM2))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let A=new mG0;return A._internalState=new Map(this._internalState),A}}pM2.TraceStateImpl=mG0});
var qG0=E((vL2)=>{Object.defineProperty(vL2,"__esModule",{value:!0});vL2.createNoopMeter=vL2.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=vL2.NOOP_OBSERVABLE_GAUGE_METRIC=vL2.NOOP_OBSERVABLE_COUNTER_METRIC=vL2.NOOP_UP_DOWN_COUNTER_METRIC=vL2.NOOP_HISTOGRAM_METRIC=vL2.NOOP_GAUGE_METRIC=vL2.NOOP_COUNTER_METRIC=vL2.NOOP_METER=vL2.NoopObservableUpDownCounterMetric=vL2.NoopObservableGaugeMetric=vL2.NoopObservableCounterMetric=vL2.NoopObservableMetric=vL2.NoopHistogramMetric=vL2.NoopGaugeMetric=vL2.NoopUpDownCounterMetric=vL2.NoopCounterMetric=vL2.NoopMetric=vL2.NoopMeter=void 0;class CG0{constructor(){}createGauge(A,B){return vL2.NOOP_GAUGE_METRIC}createHistogram(A,B){return vL2.NOOP_HISTOGRAM_METRIC}createCounter(A,B){return vL2.NOOP_COUNTER_METRIC}createUpDownCounter(A,B){return vL2.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(A,B){return vL2.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(A,B){return vL2.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(A,B){return vL2.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(A,B){}removeBatchObservableCallback(A){}}vL2.NoopMeter=CG0;class Ro{}vL2.NoopMetric=Ro;class KG0 extends Ro{add(A,B){}}vL2.NoopCounterMetric=KG0;class HG0 extends Ro{add(A,B){}}vL2.NoopUpDownCounterMetric=HG0;class zG0 extends Ro{record(A,B){}}vL2.NoopGaugeMetric=zG0;class EG0 extends Ro{record(A,B){}}vL2.NoopHistogramMetric=EG0;class h51{addCallback(A){}removeCallback(A){}}vL2.NoopObservableMetric=h51;class UG0 extends h51{}vL2.NoopObservableCounterMetric=UG0;class wG0 extends h51{}vL2.NoopObservableGaugeMetric=wG0;class $G0 extends h51{}vL2.NoopObservableUpDownCounterMetric=$G0;vL2.NOOP_METER=new CG0;vL2.NOOP_COUNTER_METRIC=new KG0;vL2.NOOP_GAUGE_METRIC=new zG0;vL2.NOOP_HISTOGRAM_METRIC=new EG0;vL2.NOOP_UP_DOWN_COUNTER_METRIC=new HG0;vL2.NOOP_OBSERVABLE_COUNTER_METRIC=new UG0;vL2.NOOP_OBSERVABLE_GAUGE_METRIC=new wG0;vL2.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new $G0;function _i4(){return vL2.NOOP_METER}vL2.createNoopMeter=_i4});
var rM2=E((aM2)=>{Object.defineProperty(aM2,"__esModule",{value:!0});aM2.createTraceState=void 0;var gn4=nM2();function un4(A){return new gn4.TraceStateImpl(A)}aM2.createTraceState=un4});
var tL2=E((rL2)=>{Object.defineProperty(rL2,"__esModule",{value:!0});rL2.NoopContextManager=void 0;var ii4=f51();class sL2{active(){return ii4.ROOT_CONTEXT}with(A,B,Q,...D){return B.call(Q,...D)}bind(A,B){return B}enable(){return this}disable(){return this}}rL2.NoopContextManager=sL2});
var tN2=E((rN2)=>{Object.defineProperty(rN2,"__esModule",{value:!0});rN2._globalThis=void 0;rN2._globalThis=typeof globalThis==="object"?globalThis:global});
var uM2=E((hM2)=>{Object.defineProperty(hM2,"__esModule",{value:!0});hM2.validateValue=hM2.validateKey=void 0;var uG0="[_0-9a-z-*/]",jn4=`[a-z]${uG0}{0,255}`,kn4=`[a-z0-9]${uG0}{0,240}@[a-z]${uG0}{0,13}`,yn4=new RegExp(`^(?:${jn4}|${kn4})$`),_n4=/^[ -~]{0,255}[!-~]$/,xn4=/,|=/;function vn4(A){return yn4.test(A)}hM2.validateKey=vn4;function bn4(A){return _n4.test(A)&&!xn4.test(A)}hM2.validateValue=bn4});
var vG0=E((LM2)=>{Object.defineProperty(LM2,"__esModule",{value:!0});LM2.ProxyTracer=void 0;var qn4=xG0(),Nn4=new qn4.NoopTracer;class NM2{constructor(A,B,Q,D){this._provider=A,this.name=B,this.version=Q,this.options=D}startSpan(A,B,Q){return this._getTracer().startSpan(A,B,Q)}startActiveSpan(A,B,Q,D){let Z=this._getTracer();return Reflect.apply(Z.startActiveSpan,Z,arguments)}_getTracer(){if(this._delegate)return this._delegate;let A=this._provider.getDelegateTracer(this.name,this.version,this.options);if(!A)return Nn4;return this._delegate=A,this._delegate}}LM2.ProxyTracer=NM2});
var vM2=E((xM2)=>{Object.defineProperty(xM2,"__esModule",{value:!0});xM2.SpanKind=void 0;var Pn4;(function(A){A[A.INTERNAL=0]="INTERNAL",A[A.SERVER=1]="SERVER",A[A.CLIENT=2]="CLIENT",A[A.PRODUCER=3]="PRODUCER",A[A.CONSUMER=4]="CONSUMER"})(Pn4=xM2.SpanKind||(xM2.SpanKind={}))});
var xG0=E(($M2)=>{Object.defineProperty($M2,"__esModule",{value:!0});$M2.NoopTracer=void 0;var Un4=g51(),UM2=kG0(),yG0=VO1(),wn4=CO1(),_G0=Un4.ContextAPI.getInstance();class wM2{startSpan(A,B,Q=_G0.active()){if(Boolean(B===null||B===void 0?void 0:B.root))return new yG0.NonRecordingSpan;let Z=Q&&UM2.getSpanContext(Q);if($n4(Z)&&wn4.isSpanContextValid(Z))return new yG0.NonRecordingSpan(Z);else return new yG0.NonRecordingSpan}startActiveSpan(A,B,Q,D){let Z,G,F;if(arguments.length<2)return;else if(arguments.length===2)F=B;else if(arguments.length===3)Z=B,F=Q;else Z=B,G=Q,F=D;let I=G!==null&&G!==void 0?G:_G0.active(),Y=this.startSpan(A,Z,I),W=UM2.setSpan(I,Y);return _G0.with(W,F,void 0,Y)}}$M2.NoopTracer=wM2;function $n4(A){return typeof A==="object"&&typeof A.spanId==="string"&&typeof A.traceId==="string"&&typeof A.traceFlags==="number"}});
var xL2=E((yL2)=>{Object.defineProperty(yL2,"__esModule",{value:!0});yL2.DiagConsoleLogger=void 0;var VG0=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class kL2{constructor(){function A(B){return function(...Q){if(console){let D=console[B];if(typeof D!=="function")D=console.log;if(typeof D==="function")return D.apply(console,Q)}}}for(let B=0;B<VG0.length;B++)this[VG0[B].n]=A(VG0[B].c)}}yL2.DiagConsoleLogger=kL2});
var xR2=E((yR2)=>{Object.defineProperty(yR2,"__esModule",{value:!0});yR2.TraceAPI=void 0;var rG0=Wu(),SR2=bG0(),jR2=CO1(),Oo=kG0(),kR2=Ju(),oG0="trace";class tG0{constructor(){this._proxyTracerProvider=new SR2.ProxyTracerProvider,this.wrapSpanContext=jR2.wrapSpanContext,this.isSpanContextValid=jR2.isSpanContextValid,this.deleteSpan=Oo.deleteSpan,this.getSpan=Oo.getSpan,this.getActiveSpan=Oo.getActiveSpan,this.getSpanContext=Oo.getSpanContext,this.setSpan=Oo.setSpan,this.setSpanContext=Oo.setSpanContext}static getInstance(){if(!this._instance)this._instance=new tG0;return this._instance}setGlobalTracerProvider(A){let B=rG0.registerGlobal(oG0,this._proxyTracerProvider,kR2.DiagAPI.instance());if(B)this._proxyTracerProvider.setDelegate(A);return B}getTracerProvider(){return rG0.getGlobal(oG0)||this._proxyTracerProvider}getTracer(A,B){return this.getTracerProvider().getTracer(A,B)}disable(){rG0.unregisterGlobal(oG0,kR2.DiagAPI.instance()),this._proxyTracerProvider=new SR2.ProxyTracerProvider}}yR2.TraceAPI=tG0});
var zR2=E((KR2)=>{Object.defineProperty(KR2,"__esModule",{value:!0});KR2.NoopTextMapPropagator=void 0;class CR2{inject(A,B){}extract(A,B){return A}fields(){return[]}}KR2.NoopTextMapPropagator=CR2});

module.exports = xP1;
