// E_module package extracted with entry point: zt0

var Vt0=E((fi8,Xt0)=>{var sy9=/[|\\{}()[\]^$+*?.-]/g;Xt0.exports=(A)=>{if(typeof A!=="string")throw new TypeError("Expected a string");return A.replace(sy9,"\\$&")}});
var zt0=E((hi8,Ht0)=>{var ry9=Vt0(),oy9=typeof process==="object"&&process&&typeof process.cwd==="function"?process.cwd():".",Kt0=[].concat(J1("module").builtinModules,"bootstrap_node","node").map((A)=>new RegExp(`(?:\\((?:node:)?${A}(?:\\.js)?:\\d+:\\d+\\)$|^\\s*at (?:node:)?${A}(?:\\.js)?:\\d+:\\d+$)`));Kt0.push(/\((?:node:)?internal\/[^:]+:\d+:\d+\)$/,/\s*at (?:node:)?internal\/[^:]+:\d+:\d+$/,/\/\.node-spawn-wrap-\w+-\w+\/node:\d+:\d+\)?$/);class Vi1{constructor(A){if(A={ignoredPackages:[],...A},"internals"in A===!1)A.internals=Vi1.nodeInternals();if("cwd"in A===!1)A.cwd=oy9;this._cwd=A.cwd.replace(/\\/g,"/"),this._internals=[].concat(A.internals,ty9(A.ignoredPackages)),this._wrapCallSite=A.wrapCallSite||!1}static nodeInternals(){return[...Kt0]}clean(A,B=0){if(B=" ".repeat(B),!Array.isArray(A))A=A.split(`
`);if(!/^\s*at /.test(A[0])&&/^\s*at /.test(A[1]))A=A.slice(1);let Q=!1,D=null,Z=[];return A.forEach((G)=>{if(G=G.replace(/\\/g,"/"),this._internals.some((I)=>I.test(G)))return;let F=/^\s*at /.test(G);if(Q)G=G.trimEnd().replace(/^(\s+)at /,"$1");else if(G=G.trim(),F)G=G.slice(3);if(G=G.replace(`${this._cwd}/`,""),G)if(F){if(D)Z.push(D),D=null;Z.push(G)}else Q=!0,D=G}),Z.map((G)=>`${B}${G}
`).join("")}captureString(A,B=this.captureString){if(typeof A==="function")B=A,A=1/0;let{stackTraceLimit:Q}=Error;if(A)Error.stackTraceLimit=A;let D={};Error.captureStackTrace(D,B);let{stack:Z}=D;return Error.stackTraceLimit=Q,this.clean(Z)}capture(A,B=this.capture){if(typeof A==="function")B=A,A=1/0;let{prepareStackTrace:Q,stackTraceLimit:D}=Error;if(Error.prepareStackTrace=(F,I)=>{if(this._wrapCallSite)return I.map(this._wrapCallSite);return I},A)Error.stackTraceLimit=A;let Z={};Error.captureStackTrace(Z,B);let{stack:G}=Z;return Object.assign(Error,{prepareStackTrace:Q,stackTraceLimit:D}),G}at(A=this.at){let[B]=this.capture(1,A);if(!B)return{};let Q={line:B.getLineNumber(),column:B.getColumnNumber()};if(Ct0(Q,B.getFileName(),this._cwd),B.isConstructor())Object.defineProperty(Q,"constructor",{value:!0,configurable:!0});if(B.isEval())Q.evalOrigin=B.getEvalOrigin();if(B.isNative())Q.native=!0;let D;try{D=B.getTypeName()}catch(F){}if(D&&D!=="Object"&&D!=="[object Object]")Q.type=D;let Z=B.getFunctionName();if(Z)Q.function=Z;let G=B.getMethodName();if(G&&Z!==G)Q.method=G;return Q}parseLine(A){let B=A&&A.match(ey9);if(!B)return null;let Q=B[1]==="new",D=B[2],Z=B[3],G=B[4],F=Number(B[5]),I=Number(B[6]),Y=B[7],W=B[8],J=B[9],X=B[10]==="native",V=B[11]===")",C,K={};if(W)K.line=Number(W);if(J)K.column=Number(J);if(V&&Y){let H=0;for(let z=Y.length-1;z>0;z--)if(Y.charAt(z)===")")H++;else if(Y.charAt(z)==="("&&Y.charAt(z-1)===" "){if(H--,H===-1&&Y.charAt(z-1)===" "){let $=Y.slice(0,z-1);Y=Y.slice(z+1),D+=` (${$}`;break}}}if(D){let H=D.match(A_9);if(H)D=H[1],C=H[2]}if(Ct0(K,Y,this._cwd),Q)Object.defineProperty(K,"constructor",{value:!0,configurable:!0});if(Z)K.evalOrigin=Z,K.evalLine=F,K.evalColumn=I,K.evalFile=G&&G.replace(/\\/g,"/");if(X)K.native=!0;if(D)K.function=D;if(C&&D!==C)K.method=C;return K}}function Ct0(A,B,Q){if(B){if(B=B.replace(/\\/g,"/"),B.startsWith(`${Q}/`))B=B.slice(Q.length+1);A.file=B}}function ty9(A){if(A.length===0)return[];let B=A.map((Q)=>ry9(Q));return new RegExp(`[/\\\\]node_modules[/\\\\](?:${B.join("|")})[/\\\\][^:]+:\\d+:\\d+`)}var ey9=new RegExp("^(?:\\s*at )?(?:(new) )?(?:(.*?) \\()?(?:eval at ([^ ]+) \\((.+?):(\\d+):(\\d+)\\), )?(?:(.+?):(\\d+):(\\d+)|(native))(\\)?)$"),A_9=/^(.*?) \[as (.*?)\]$/;Ht0.exports=Vi1});

module.exports = zt0;
