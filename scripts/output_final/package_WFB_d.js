// E_module package extracted with entry point: WFB

var BGB=E((Is5,AGB)=>{var{defineProperty:bk1,getOwnPropertyDescriptor:hv6,getOwnPropertyNames:gv6}=Object,uv6=Object.prototype.hasOwnProperty,dK0=(A,B)=>bk1(A,"name",{value:B,configurable:!0}),mv6=(A,B)=>{for(var Q in B)bk1(A,Q,{get:B[Q],enumerable:!0})},dv6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of gv6(B))if(!uv6.call(A,Z)&&Z!==Q)bk1(A,Z,{get:()=>B[Z],enumerable:!(D=hv6(B,Z))||D.enumerable})}return A},cv6=(A)=>dv6(bk1({},"__esModule",{value:!0}),A),oZB={};mv6(oZB,{fromUtf8:()=>eZB,toUint8Array:()=>lv6,toUtf8:()=>pv6});AGB.exports=cv6(oZB);var tZB=mK0(),eZB=dK0((A)=>{let B=tZB.fromString(A,"utf8");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength/Uint8Array.BYTES_PER_ELEMENT)},"fromUtf8"),lv6=dK0((A)=>{if(typeof A==="string")return eZB(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)},"toUint8Array"),pv6=dK0((A)=>{if(typeof A==="string")return A;if(typeof A!=="object"||typeof A.byteOffset!=="number"||typeof A.byteLength!=="number")throw new Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return tZB.fromArrayBuffer(A.buffer,A.byteOffset,A.byteLength).toString("utf8")},"toUtf8")});
var CK0=E((Ra5,u3B)=>{var{defineProperty:Zk1,getOwnPropertyDescriptor:Ck6,getOwnPropertyNames:Kk6}=Object,Hk6=Object.prototype.hasOwnProperty,Gk1=(A,B)=>Zk1(A,"name",{value:B,configurable:!0}),zk6=(A,B)=>{for(var Q in B)Zk1(A,Q,{get:B[Q],enumerable:!0})},Ek6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Kk6(B))if(!Hk6.call(A,Z)&&Z!==Q)Zk1(A,Z,{get:()=>B[Z],enumerable:!(D=Ck6(B,Z))||D.enumerable})}return A},Uk6=(A)=>Ek6(Zk1({},"__esModule",{value:!0}),A),y3B={};zk6(y3B,{AlgorithmId:()=>b3B,EndpointURLScheme:()=>v3B,FieldPosition:()=>f3B,HttpApiKeyAuthLocation:()=>x3B,HttpAuthLocation:()=>_3B,IniSectionType:()=>h3B,RequestHandlerProtocol:()=>g3B,SMITHY_CONTEXT_KEY:()=>Lk6,getDefaultClientConfiguration:()=>qk6,resolveDefaultRuntimeConfig:()=>Nk6});u3B.exports=Uk6(y3B);var _3B=((A)=>{return A.HEADER="header",A.QUERY="query",A})(_3B||{}),x3B=((A)=>{return A.HEADER="header",A.QUERY="query",A})(x3B||{}),v3B=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(v3B||{}),b3B=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(b3B||{}),wk6=Gk1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{_checksumAlgorithms:B,addChecksumAlgorithm(Q){this._checksumAlgorithms.push(Q)},checksumAlgorithms(){return this._checksumAlgorithms}}},"getChecksumConfiguration"),$k6=Gk1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),qk6=Gk1((A)=>{return{...wk6(A)}},"getDefaultClientConfiguration"),Nk6=Gk1((A)=>{return{...$k6(A)}},"resolveDefaultRuntimeConfig"),f3B=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(f3B||{}),Lk6="__smithy_context",h3B=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(h3B||{}),g3B=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(g3B||{})});
var KK0=E((Oa5,a3B)=>{var{defineProperty:Fk1,getOwnPropertyDescriptor:Mk6,getOwnPropertyNames:Rk6}=Object,Ok6=Object.prototype.hasOwnProperty,Px=(A,B)=>Fk1(A,"name",{value:B,configurable:!0}),Tk6=(A,B)=>{for(var Q in B)Fk1(A,Q,{get:B[Q],enumerable:!0})},Pk6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Rk6(B))if(!Ok6.call(A,Z)&&Z!==Q)Fk1(A,Z,{get:()=>B[Z],enumerable:!(D=Mk6(B,Z))||D.enumerable})}return A},Sk6=(A)=>Pk6(Fk1({},"__esModule",{value:!0}),A),m3B={};Tk6(m3B,{Field:()=>_k6,Fields:()=>xk6,HttpRequest:()=>vk6,HttpResponse:()=>bk6,getHttpHandlerExtensionConfiguration:()=>jk6,isValidHostname:()=>n3B,resolveHttpHandlerRuntimeConfig:()=>kk6});a3B.exports=Sk6(m3B);var jk6=Px((A)=>{let B=A.httpHandler;return{setHttpHandler(Q){B=Q},httpHandler(){return B},updateHttpClientConfig(Q,D){B.updateHttpClientConfig(Q,D)},httpHandlerConfigs(){return B.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),kk6=Px((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),yk6=CK0(),d3B=class A{constructor({name:B,kind:Q=yk6.FieldPosition.HEADER,values:D=[]}){this.name=B,this.kind=Q,this.values=D}add(B){this.values.push(B)}set(B){this.values=B}remove(B){this.values=this.values.filter((Q)=>Q!==B)}toString(){return this.values.map((B)=>B.includes(",")||B.includes(" ")?`"${B}"`:B).join(", ")}get(){return this.values}};Px(d3B,"Field");var _k6=d3B,c3B=class A{constructor({fields:B=[],encoding:Q="utf-8"}){this.entries={},B.forEach(this.setField.bind(this)),this.encoding=Q}setField(B){this.entries[B.name.toLowerCase()]=B}getField(B){return this.entries[B.toLowerCase()]}removeField(B){delete this.entries[B.toLowerCase()]}getByType(B){return Object.values(this.entries).filter((Q)=>Q.kind===B)}};Px(c3B,"Fields");var xk6=c3B,l3B=class A{constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){let B=new A({...this,headers:{...this.headers}});if(B.query)B.query=p3B(B.query);return B}};Px(l3B,"HttpRequest");var vk6=l3B;function p3B(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}Px(p3B,"cloneQuery");var i3B=class A{constructor(B){this.statusCode=B.statusCode,this.reason=B.reason,this.headers=B.headers||{},this.body=B.body}static isInstance(B){if(!B)return!1;let Q=B;return typeof Q.statusCode==="number"&&typeof Q.headers==="object"}};Px(i3B,"HttpResponse");var bk6=i3B;function n3B(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}Px(n3B,"isValidHostname")});
var LZB=E((ta5,NZB)=>{var{defineProperty:Pk1,getOwnPropertyDescriptor:xx6,getOwnPropertyNames:vx6}=Object,bx6=Object.prototype.hasOwnProperty,fx6=(A,B)=>Pk1(A,"name",{value:B,configurable:!0}),hx6=(A,B)=>{for(var Q in B)Pk1(A,Q,{get:B[Q],enumerable:!0})},gx6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of vx6(B))if(!bx6.call(A,Z)&&Z!==Q)Pk1(A,Z,{get:()=>B[Z],enumerable:!(D=xx6(B,Z))||D.enumerable})}return A},ux6=(A)=>gx6(Pk1({},"__esModule",{value:!0}),A),qZB={};hx6(qZB,{isArrayBuffer:()=>mx6});NZB.exports=ux6(qZB);var mx6=fx6((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var PGB=E((Xs5,TGB)=>{var{create:Wb6,defineProperty:ND1,getOwnPropertyDescriptor:Jb6,getOwnPropertyNames:Xb6,getPrototypeOf:Vb6}=Object,Cb6=Object.prototype.hasOwnProperty,UI=(A,B)=>ND1(A,"name",{value:B,configurable:!0}),Kb6=(A,B)=>{for(var Q in B)ND1(A,Q,{get:B[Q],enumerable:!0})},HGB=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Xb6(B))if(!Cb6.call(A,Z)&&Z!==Q)ND1(A,Z,{get:()=>B[Z],enumerable:!(D=Jb6(B,Z))||D.enumerable})}return A},Hb6=(A,B,Q)=>(Q=A!=null?Wb6(Vb6(A)):{},HGB(B||!A||!A.__esModule?ND1(Q,"default",{value:A,enumerable:!0}):Q,A)),zb6=(A)=>HGB(ND1({},"__esModule",{value:!0}),A),zGB={};Kb6(zGB,{DEFAULT_REQUEST_TIMEOUT:()=>qb6,NodeHttp2Handler:()=>Ob6,NodeHttpHandler:()=>Nb6,streamCollector:()=>Pb6});TGB.exports=zb6(zGB);var EGB=KK0(),UGB=VGB(),pK0=J1("http"),iK0=J1("https"),Eb6=["ECONNRESET","EPIPE","ETIMEDOUT"],wGB=UI((A)=>{let B={};for(let Q of Object.keys(A)){let D=A[Q];B[Q]=Array.isArray(D)?D.join(","):D}return B},"getTransformedHeaders"),Ub6=UI((A,B,Q=0)=>{if(!Q)return;let D=setTimeout(()=>{A.destroy(),B(Object.assign(new Error(`Socket timed out without establishing a connection within ${Q} ms`),{name:"TimeoutError"}))},Q);A.on("socket",(Z)=>{if(Z.connecting)Z.on("connect",()=>{clearTimeout(D)});else clearTimeout(D)})},"setConnectionTimeout"),wb6=UI((A,{keepAlive:B,keepAliveMsecs:Q})=>{if(B!==!0)return;A.on("socket",(D)=>{D.setKeepAlive(B,Q||0)})},"setSocketKeepAlive"),$b6=UI((A,B,Q=0)=>{A.setTimeout(Q,()=>{A.destroy(),B(Object.assign(new Error(`Connection timed out after ${Q} ms`),{name:"TimeoutError"}))})},"setSocketTimeout"),$GB=J1("stream"),CGB=1000;async function nK0(A,B,Q=CGB){let D=B.headers??{},Z=D.Expect||D.expect,G=-1,F=!1;if(Z==="100-continue")await Promise.race([new Promise((I)=>{G=Number(setTimeout(I,Math.max(CGB,Q)))}),new Promise((I)=>{A.on("continue",()=>{clearTimeout(G),I()}),A.on("error",()=>{F=!0,clearTimeout(G),I()})})]);if(!F)qGB(A,B.body)}UI(nK0,"writeRequestBody");function qGB(A,B){if(B instanceof $GB.Readable){B.pipe(A);return}if(B){if(Buffer.isBuffer(B)||typeof B==="string"){A.end(B);return}let Q=B;if(typeof Q==="object"&&Q.buffer&&typeof Q.byteOffset==="number"&&typeof Q.byteLength==="number"){A.end(Buffer.from(Q.buffer,Q.byteOffset,Q.byteLength));return}A.end(Buffer.from(B));return}A.end()}UI(qGB,"writeBody");var qb6=0,NGB=class A{constructor(B){this.socketWarningTimestamp=0,this.metadata={handlerProtocol:"http/1.1"},this.configProvider=new Promise((Q,D)=>{if(typeof B==="function")B().then((Z)=>{Q(this.resolveDefaultConfig(Z))}).catch(D);else Q(this.resolveDefaultConfig(B))})}static create(B){if(typeof(B==null?void 0:B.handle)==="function")return B;return new A(B)}static checkSocketUsage(B,Q){var D,Z;let{sockets:G,requests:F,maxSockets:I}=B;if(typeof I!=="number"||I===1/0)return Q;let Y=15000;if(Date.now()-Y<Q)return Q;if(G&&F)for(let W in G){let J=((D=G[W])==null?void 0:D.length)??0,X=((Z=F[W])==null?void 0:Z.length)??0;if(J>=I&&X>=2*I)return console.warn("@smithy/node-http-handler:WARN",`socket usage at capacity=${J} and ${X} additional requests are enqueued.`,"See https://docs.aws.amazon.com/sdk-for-javascript/v3/developer-guide/node-configuring-maxsockets.html","or increase socketAcquisitionWarningTimeout=(millis) in the NodeHttpHandler config."),Date.now()}return Q}resolveDefaultConfig(B){let{requestTimeout:Q,connectionTimeout:D,socketTimeout:Z,httpAgent:G,httpsAgent:F}=B||{},I=!0,Y=50;return{connectionTimeout:D,requestTimeout:Q??Z,httpAgent:(()=>{if(G instanceof pK0.Agent||typeof(G==null?void 0:G.destroy)==="function")return G;return new pK0.Agent({keepAlive:!0,maxSockets:50,...G})})(),httpsAgent:(()=>{if(F instanceof iK0.Agent||typeof(F==null?void 0:F.destroy)==="function")return F;return new iK0.Agent({keepAlive:!0,maxSockets:50,...F})})()}}destroy(){var B,Q,D,Z;(Q=(B=this.config)==null?void 0:B.httpAgent)==null||Q.destroy(),(Z=(D=this.config)==null?void 0:D.httpsAgent)==null||Z.destroy()}async handle(B,{abortSignal:Q}={}){if(!this.config)this.config=await this.configProvider;let D;return new Promise((Z,G)=>{let F=void 0,I=UI(async(L)=>{await F,clearTimeout(D),Z(L)},"resolve"),Y=UI(async(L)=>{await F,G(L)},"reject");if(!this.config)throw new Error("Node HTTP request handler config is not resolved");if(Q==null?void 0:Q.aborted){let L=new Error("Request aborted");L.name="AbortError",Y(L);return}let W=B.protocol==="https:",J=W?this.config.httpsAgent:this.config.httpAgent;D=setTimeout(()=>{this.socketWarningTimestamp=A.checkSocketUsage(J,this.socketWarningTimestamp)},this.config.socketAcquisitionWarningTimeout??(this.config.requestTimeout??2000)+(this.config.connectionTimeout??1000));let X=UGB.buildQueryString(B.query||{}),V=void 0;if(B.username!=null||B.password!=null){let L=B.username??"",N=B.password??"";V=`${L}:${N}`}let C=B.path;if(X)C+=`?${X}`;if(B.fragment)C+=`#${B.fragment}`;let K={headers:B.headers,host:B.hostname,method:B.method,path:C,port:B.port,agent:J,auth:V},z=(W?iK0.request:pK0.request)(K,(L)=>{let N=new EGB.HttpResponse({statusCode:L.statusCode||-1,reason:L.statusMessage,headers:wGB(L.headers),body:L});I({response:N})});if(z.on("error",(L)=>{if(Eb6.includes(L.code))Y(Object.assign(L,{name:"TimeoutError"}));else Y(L)}),Ub6(z,Y,this.config.connectionTimeout),$b6(z,Y,this.config.requestTimeout),Q)Q.onabort=()=>{z.abort();let L=new Error("Request aborted");L.name="AbortError",Y(L)};let $=K.agent;if(typeof $==="object"&&"keepAlive"in $)wb6(z,{keepAlive:$.keepAlive,keepAliveMsecs:$.keepAliveMsecs});F=nK0(z,B,this.config.requestTimeout).catch(G)})}updateHttpClientConfig(B,Q){this.config=void 0,this.configProvider=this.configProvider.then((D)=>{return{...D,[B]:Q}})}httpHandlerConfigs(){return this.config??{}}};UI(NGB,"NodeHttpHandler");var Nb6=NGB,KGB=J1("http2"),Lb6=Hb6(J1("http2")),LGB=class A{constructor(B){this.sessions=[],this.sessions=B??[]}poll(){if(this.sessions.length>0)return this.sessions.shift()}offerLast(B){this.sessions.push(B)}contains(B){return this.sessions.includes(B)}remove(B){this.sessions=this.sessions.filter((Q)=>Q!==B)}[Symbol.iterator](){return this.sessions[Symbol.iterator]()}destroy(B){for(let Q of this.sessions)if(Q===B){if(!Q.destroyed)Q.destroy()}}};UI(LGB,"NodeHttp2ConnectionPool");var Mb6=LGB,MGB=class A{constructor(B){if(this.sessionCache=new Map,this.config=B,this.config.maxConcurrency&&this.config.maxConcurrency<=0)throw new RangeError("maxConcurrency must be greater than zero.")}lease(B,Q){let D=this.getUrlString(B),Z=this.sessionCache.get(D);if(Z){let Y=Z.poll();if(Y&&!this.config.disableConcurrency)return Y}let G=Lb6.default.connect(D);if(this.config.maxConcurrency)G.settings({maxConcurrentStreams:this.config.maxConcurrency},(Y)=>{if(Y)throw new Error("Fail to set maxConcurrentStreams to "+this.config.maxConcurrency+"when creating new session for "+B.destination.toString())});G.unref();let F=UI(()=>{G.destroy(),this.deleteSession(D,G)},"destroySessionCb");if(G.on("goaway",F),G.on("error",F),G.on("frameError",F),G.on("close",()=>this.deleteSession(D,G)),Q.requestTimeout)G.setTimeout(Q.requestTimeout,F);let I=this.sessionCache.get(D)||new Mb6;return I.offerLast(G),this.sessionCache.set(D,I),G}deleteSession(B,Q){let D=this.sessionCache.get(B);if(!D)return;if(!D.contains(Q))return;D.remove(Q),this.sessionCache.set(B,D)}release(B,Q){var D;let Z=this.getUrlString(B);(D=this.sessionCache.get(Z))==null||D.offerLast(Q)}destroy(){for(let[B,Q]of this.sessionCache){for(let D of Q){if(!D.destroyed)D.destroy();Q.remove(D)}this.sessionCache.delete(B)}}setMaxConcurrentStreams(B){if(this.config.maxConcurrency&&this.config.maxConcurrency<=0)throw new RangeError("maxConcurrentStreams must be greater than zero.");this.config.maxConcurrency=B}setDisableConcurrentStreams(B){this.config.disableConcurrency=B}getUrlString(B){return B.destination.toString()}};UI(MGB,"NodeHttp2ConnectionManager");var Rb6=MGB,RGB=class A{constructor(B){this.metadata={handlerProtocol:"h2"},this.connectionManager=new Rb6({}),this.configProvider=new Promise((Q,D)=>{if(typeof B==="function")B().then((Z)=>{Q(Z||{})}).catch(D);else Q(B||{})})}static create(B){if(typeof(B==null?void 0:B.handle)==="function")return B;return new A(B)}destroy(){this.connectionManager.destroy()}async handle(B,{abortSignal:Q}={}){if(!this.config){if(this.config=await this.configProvider,this.connectionManager.setDisableConcurrentStreams(this.config.disableConcurrentStreams||!1),this.config.maxConcurrentStreams)this.connectionManager.setMaxConcurrentStreams(this.config.maxConcurrentStreams)}let{requestTimeout:D,disableConcurrentStreams:Z}=this.config;return new Promise((G,F)=>{var I;let Y=!1,W=void 0,J=UI(async(k)=>{await W,G(k)},"resolve"),X=UI(async(k)=>{await W,F(k)},"reject");if(Q==null?void 0:Q.aborted){Y=!0;let k=new Error("Request aborted");k.name="AbortError",X(k);return}let{hostname:V,method:C,port:K,protocol:H,query:z}=B,$="";if(B.username!=null||B.password!=null){let k=B.username??"",c=B.password??"";$=`${k}:${c}@`}let L=`${H}//${$}${V}${K?`:${K}`:""}`,N={destination:new URL(L)},O=this.connectionManager.lease(N,{requestTimeout:(I=this.config)==null?void 0:I.sessionTimeout,disableConcurrentStreams:Z||!1}),R=UI((k)=>{if(Z)this.destroySession(O);Y=!0,X(k)},"rejectWithDestroy"),T=UGB.buildQueryString(z||{}),j=B.path;if(T)j+=`?${T}`;if(B.fragment)j+=`#${B.fragment}`;let f=O.request({...B.headers,[KGB.constants.HTTP2_HEADER_PATH]:j,[KGB.constants.HTTP2_HEADER_METHOD]:C});if(O.ref(),f.on("response",(k)=>{let c=new EGB.HttpResponse({statusCode:k[":status"]||-1,headers:wGB(k),body:f});if(Y=!0,J({response:c}),Z)O.close(),this.connectionManager.deleteSession(L,O)}),D)f.setTimeout(D,()=>{f.close();let k=new Error(`Stream timed out because of no activity for ${D} ms`);k.name="TimeoutError",R(k)});if(Q)Q.onabort=()=>{f.close();let k=new Error("Request aborted");k.name="AbortError",R(k)};f.on("frameError",(k,c,h)=>{R(new Error(`Frame type id ${k} in stream id ${h} has failed with code ${c}.`))}),f.on("error",R),f.on("aborted",()=>{R(new Error(`HTTP/2 stream is abnormally aborted in mid-communication with result code ${f.rstCode}.`))}),f.on("close",()=>{if(O.unref(),Z)O.destroy();if(!Y)R(new Error("Unexpected error: http2 request did not get a response"))}),W=nK0(f,B,D)})}updateHttpClientConfig(B,Q){this.config=void 0,this.configProvider=this.configProvider.then((D)=>{return{...D,[B]:Q}})}httpHandlerConfigs(){return this.config??{}}destroySession(B){if(!B.destroyed)B.destroy()}};UI(RGB,"NodeHttp2Handler");var Ob6=RGB,OGB=class A extends $GB.Writable{constructor(){super(...arguments);this.bufferedBytes=[]}_write(B,Q,D){this.bufferedBytes.push(B),D()}};UI(OGB,"Collector");var Tb6=OGB,Pb6=UI((A)=>new Promise((B,Q)=>{let D=new Tb6;A.pipe(D),A.on("error",(Z)=>{D.end(),Q(Z)}),D.on("error",Q),D.on("finish",function(){let Z=new Uint8Array(Buffer.concat(this.bufferedBytes));B(Z)})}),"streamCollector")});
var SZB=E((TZB)=>{Object.defineProperty(TZB,"__esModule",{value:!0});TZB.fromBase64=void 0;var ox6=jk1(),tx6=/^[A-Za-z0-9+/]*={0,2}$/,ex6=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!tx6.exec(A))throw new TypeError("Invalid base64 string.");let B=ox6.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};TZB.fromBase64=ex6});
var VGB=E((Js5,XGB)=>{var{defineProperty:hk1,getOwnPropertyDescriptor:Qb6,getOwnPropertyNames:Db6}=Object,Zb6=Object.prototype.hasOwnProperty,Gb6=(A,B)=>hk1(A,"name",{value:B,configurable:!0}),Fb6=(A,B)=>{for(var Q in B)hk1(A,Q,{get:B[Q],enumerable:!0})},Ib6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Db6(B))if(!Zb6.call(A,Z)&&Z!==Q)hk1(A,Z,{get:()=>B[Z],enumerable:!(D=Qb6(B,Z))||D.enumerable})}return A},Yb6=(A)=>Ib6(hk1({},"__esModule",{value:!0}),A),WGB={};Fb6(WGB,{buildQueryString:()=>JGB});XGB.exports=Yb6(WGB);var lK0=YGB();function JGB(A){let B=[];for(let Q of Object.keys(A).sort()){let D=A[Q];if(Q=lK0.escapeUri(Q),Array.isArray(D))for(let Z=0,G=D.length;Z<G;Z++)B.push(`${Q}=${lK0.escapeUri(D[Z])}`);else{let Z=Q;if(D||typeof D==="string")Z+=`=${lK0.escapeUri(D)}`;B.push(Z)}}return B.join("&")}Gb6(JGB,"buildQueryString")});
var WFB=E((Ks5,YFB)=>{var{defineProperty:pk1,getOwnPropertyDescriptor:hb6,getOwnPropertyNames:gb6}=Object,ub6=Object.prototype.hasOwnProperty,k2=(A,B)=>pk1(A,"name",{value:B,configurable:!0}),mb6=(A,B)=>{for(var Q in B)pk1(A,Q,{get:B[Q],enumerable:!0})},db6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of gb6(B))if(!ub6.call(A,Z)&&Z!==Q)pk1(A,Z,{get:()=>B[Z],enumerable:!(D=hb6(B,Z))||D.enumerable})}return A},cb6=(A)=>db6(pk1({},"__esModule",{value:!0}),A),mGB={};mb6(mGB,{Client:()=>pb6,Command:()=>iGB,LazyJsonString:()=>mf6,NoOpLogger:()=>lb6,SENSITIVE_STRING:()=>ab6,ServiceException:()=>Sf6,StringWrapper:()=>TD1,_json:()=>ZH0,collectBody:()=>ib6,convertMap:()=>df6,createAggregatedClient:()=>sb6,dateToUtcString:()=>eGB,decorateServiceException:()=>BFB,emitWarningIfUnsupportedVersion:()=>_f6,expectBoolean:()=>ob6,expectByte:()=>DH0,expectFloat32:()=>dk1,expectInt:()=>eb6,expectInt32:()=>BH0,expectLong:()=>RD1,expectNonNull:()=>Bf6,expectNumber:()=>MD1,expectObject:()=>aGB,expectShort:()=>QH0,expectString:()=>Qf6,expectUnion:()=>Df6,extendedEncodeURIComponent:()=>lk1,getArrayIfSingleItem:()=>uf6,getDefaultClientConfiguration:()=>hf6,getDefaultExtensionConfiguration:()=>DFB,getValueFromTextNode:()=>ZFB,handleFloat:()=>Ff6,limitedParseDouble:()=>IH0,limitedParseFloat:()=>If6,limitedParseFloat32:()=>Yf6,loadConfigsForDefaultMode:()=>yf6,logger:()=>OD1,map:()=>WH0,parseBoolean:()=>rb6,parseEpochTimestamp:()=>$f6,parseRfc3339DateTime:()=>Cf6,parseRfc3339DateTimeWithOffset:()=>Hf6,parseRfc7231DateTime:()=>wf6,resolveDefaultRuntimeConfig:()=>gf6,resolvedPath:()=>nf6,serializeFloat:()=>af6,splitEvery:()=>IFB,strictParseByte:()=>tGB,strictParseDouble:()=>FH0,strictParseFloat:()=>Zf6,strictParseFloat32:()=>sGB,strictParseInt:()=>Wf6,strictParseInt32:()=>Jf6,strictParseLong:()=>oGB,strictParseShort:()=>Ce,take:()=>cf6,throwDefaultError:()=>QFB,withBaseException:()=>jf6});YFB.exports=cb6(mGB);var dGB=class A{trace(){}debug(){}info(){}warn(){}error(){}};k2(dGB,"NoOpLogger");var lb6=dGB,cGB=lZB(),lGB=class A{constructor(B){this.middlewareStack=cGB.constructStack(),this.config=B}send(B,Q,D){let Z=typeof Q!=="function"?Q:void 0,G=typeof Q==="function"?Q:D,F=B.resolveMiddleware(this.middlewareStack,this.config,Z);if(G)F(B).then((I)=>G(null,I.output),(I)=>G(I)).catch(()=>{});else return F(B).then((I)=>I.output)}destroy(){if(this.config.requestHandler.destroy)this.config.requestHandler.destroy()}};k2(lGB,"Client");var pb6=lGB,tK0=gGB(),ib6=k2(async(A=new Uint8Array,B)=>{if(A instanceof Uint8Array)return tK0.Uint8ArrayBlobAdapter.mutate(A);if(!A)return tK0.Uint8ArrayBlobAdapter.mutate(new Uint8Array);let Q=B.streamCollector(A);return tK0.Uint8ArrayBlobAdapter.mutate(await Q)},"collectBody"),AH0=CK0(),pGB=class A{constructor(){this.middlewareStack=cGB.constructStack()}static classBuilder(){return new nb6}resolveMiddlewareWithContext(B,Q,D,{middlewareFn:Z,clientName:G,commandName:F,inputFilterSensitiveLog:I,outputFilterSensitiveLog:Y,smithyContext:W,additionalContext:J,CommandCtor:X}){for(let z of Z.bind(this)(X,B,Q,D))this.middlewareStack.use(z);let V=B.concat(this.middlewareStack),{logger:C}=Q,K={logger:C,clientName:G,commandName:F,inputFilterSensitiveLog:I,outputFilterSensitiveLog:Y,[AH0.SMITHY_CONTEXT_KEY]:{...W},...J},{requestHandler:H}=Q;return V.resolve((z)=>H.handle(z.request,D||{}),K)}};k2(pGB,"Command");var iGB=pGB,nGB=class A{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(B)=>B,this._outputFilterSensitiveLog=(B)=>B,this._serializer=null,this._deserializer=null}init(B){this._init=B}ep(B){return this._ep=B,this}m(B){return this._middlewareFn=B,this}s(B,Q,D={}){return this._smithyContext={service:B,operation:Q,...D},this}c(B={}){return this._additionalContext=B,this}n(B,Q){return this._clientName=B,this._commandName=Q,this}f(B=(D)=>D,Q=(D)=>D){return this._inputFilterSensitiveLog=B,this._outputFilterSensitiveLog=Q,this}ser(B){return this._serializer=B,this}de(B){return this._deserializer=B,this}build(){var B;let Q=this,D;return D=(B=class extends iGB{constructor(...[Z]){super();this.serialize=Q._serializer,this.deserialize=Q._deserializer,this.input=Z??{},Q._init(this)}static getEndpointParameterInstructions(){return Q._ep}resolveMiddleware(Z,G,F){return this.resolveMiddlewareWithContext(Z,G,F,{CommandCtor:D,middlewareFn:Q._middlewareFn,clientName:Q._clientName,commandName:Q._commandName,inputFilterSensitiveLog:Q._inputFilterSensitiveLog,outputFilterSensitiveLog:Q._outputFilterSensitiveLog,smithyContext:Q._smithyContext,additionalContext:Q._additionalContext})}},k2(B,"CommandRef"),B)}};k2(nGB,"ClassBuilder");var nb6=nGB,ab6="***SensitiveInformation***",sb6=k2((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=k2(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),rb6=k2((A)=>{switch(A){case"true":return!0;case"false":return!1;default:throw new Error(`Unable to parse boolean value "${A}"`)}},"parseBoolean"),ob6=k2((A)=>{if(A===null||A===void 0)return;if(typeof A==="number"){if(A===0||A===1)OD1.warn(ck1(`Expected boolean, got ${typeof A}: ${A}`));if(A===0)return!1;if(A===1)return!0}if(typeof A==="string"){let B=A.toLowerCase();if(B==="false"||B==="true")OD1.warn(ck1(`Expected boolean, got ${typeof A}: ${A}`));if(B==="false")return!1;if(B==="true")return!0}if(typeof A==="boolean")return A;throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)},"expectBoolean"),MD1=k2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string"){let B=parseFloat(A);if(!Number.isNaN(B)){if(String(B)!==String(A))OD1.warn(ck1(`Expected number but observed string: ${A}`));return B}}if(typeof A==="number")return A;throw new TypeError(`Expected number, got ${typeof A}: ${A}`)},"expectNumber"),tb6=Math.ceil(340282346638528860000000000000000000000),dk1=k2((A)=>{let B=MD1(A);if(B!==void 0&&!Number.isNaN(B)&&B!==1/0&&B!==-1/0){if(Math.abs(B)>tb6)throw new TypeError(`Expected 32-bit float, got ${A}`)}return B},"expectFloat32"),RD1=k2((A)=>{if(A===null||A===void 0)return;if(Number.isInteger(A)&&!Number.isNaN(A))return A;throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)},"expectLong"),eb6=RD1,BH0=k2((A)=>GH0(A,32),"expectInt32"),QH0=k2((A)=>GH0(A,16),"expectShort"),DH0=k2((A)=>GH0(A,8),"expectByte"),GH0=k2((A,B)=>{let Q=RD1(A);if(Q!==void 0&&Af6(Q,B)!==Q)throw new TypeError(`Expected ${B}-bit integer, got ${A}`);return Q},"expectSizedInt"),Af6=k2((A,B)=>{switch(B){case 32:return Int32Array.of(A)[0];case 16:return Int16Array.of(A)[0];case 8:return Int8Array.of(A)[0]}},"castInt"),Bf6=k2((A,B)=>{if(A===null||A===void 0){if(B)throw new TypeError(`Expected a non-null value for ${B}`);throw new TypeError("Expected a non-null value")}return A},"expectNonNull"),aGB=k2((A)=>{if(A===null||A===void 0)return;if(typeof A==="object"&&!Array.isArray(A))return A;let B=Array.isArray(A)?"array":typeof A;throw new TypeError(`Expected object, got ${B}: ${A}`)},"expectObject"),Qf6=k2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string")return A;if(["boolean","number","bigint"].includes(typeof A))return OD1.warn(ck1(`Expected string, got ${typeof A}: ${A}`)),String(A);throw new TypeError(`Expected string, got ${typeof A}: ${A}`)},"expectString"),Df6=k2((A)=>{if(A===null||A===void 0)return;let B=aGB(A),Q=Object.entries(B).filter(([,D])=>D!=null).map(([D])=>D);if(Q.length===0)throw new TypeError("Unions must have exactly one non-null member. None were found.");if(Q.length>1)throw new TypeError(`Unions must have exactly one non-null member. Keys ${Q} were not null.`);return B},"expectUnion"),FH0=k2((A)=>{if(typeof A=="string")return MD1(He(A));return MD1(A)},"strictParseDouble"),Zf6=FH0,sGB=k2((A)=>{if(typeof A=="string")return dk1(He(A));return dk1(A)},"strictParseFloat32"),Gf6=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,He=k2((A)=>{let B=A.match(Gf6);if(B===null||B[0].length!==A.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(A)},"parseNumber"),IH0=k2((A)=>{if(typeof A=="string")return rGB(A);return MD1(A)},"limitedParseDouble"),Ff6=IH0,If6=IH0,Yf6=k2((A)=>{if(typeof A=="string")return rGB(A);return dk1(A)},"limitedParseFloat32"),rGB=k2((A)=>{switch(A){case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error(`Unable to parse float value: ${A}`)}},"parseFloatString"),oGB=k2((A)=>{if(typeof A==="string")return RD1(He(A));return RD1(A)},"strictParseLong"),Wf6=oGB,Jf6=k2((A)=>{if(typeof A==="string")return BH0(He(A));return BH0(A)},"strictParseInt32"),Ce=k2((A)=>{if(typeof A==="string")return QH0(He(A));return QH0(A)},"strictParseShort"),tGB=k2((A)=>{if(typeof A==="string")return DH0(He(A));return DH0(A)},"strictParseByte"),ck1=k2((A)=>{return String(new TypeError(A).stack||A).split(`
`).slice(0,5).filter((B)=>!B.includes("stackTraceWarning")).join(`
`)},"stackTraceWarning"),OD1={warn:console.warn},Xf6=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],YH0=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function eGB(A){let B=A.getUTCFullYear(),Q=A.getUTCMonth(),D=A.getUTCDay(),Z=A.getUTCDate(),G=A.getUTCHours(),F=A.getUTCMinutes(),I=A.getUTCSeconds(),Y=Z<10?`0${Z}`:`${Z}`,W=G<10?`0${G}`:`${G}`,J=F<10?`0${F}`:`${F}`,X=I<10?`0${I}`:`${I}`;return`${Xf6[D]}, ${Y} ${YH0[Q]} ${B} ${W}:${J}:${X} GMT`}k2(eGB,"dateToUtcString");var Vf6=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),Cf6=k2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=Vf6.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W]=B,J=Ce(Ke(D)),X=EM(Z,"month",1,12),V=EM(G,"day",1,31);return LD1(J,X,V,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})},"parseRfc3339DateTime"),Kf6=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),Hf6=k2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=Kf6.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W,J]=B,X=Ce(Ke(D)),V=EM(Z,"month",1,12),C=EM(G,"day",1,31),K=LD1(X,V,C,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W});if(J.toUpperCase()!="Z")K.setTime(K.getTime()-Pf6(J));return K},"parseRfc3339DateTimeWithOffset"),zf6=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),Ef6=new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),Uf6=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/),wf6=k2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-7231 date-times must be expressed as strings");let B=zf6.exec(A);if(B){let[Q,D,Z,G,F,I,Y,W]=B;return LD1(Ce(Ke(G)),eK0(Z),EM(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})}if(B=Ef6.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return Lf6(LD1(qf6(G),eK0(Z),EM(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W}))}if(B=Uf6.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return LD1(Ce(Ke(W)),eK0(D),EM(Z.trimLeft(),"day",1,31),{hours:G,minutes:F,seconds:I,fractionalMilliseconds:Y})}throw new TypeError("Invalid RFC-7231 date-time value")},"parseRfc7231DateTime"),$f6=k2((A)=>{if(A===null||A===void 0)return;let B;if(typeof A==="number")B=A;else if(typeof A==="string")B=FH0(A);else throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(B)||B===1/0||B===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(B*1000))},"parseEpochTimestamp"),LD1=k2((A,B,Q,D)=>{let Z=B-1;return Rf6(A,Z,Q),new Date(Date.UTC(A,Z,Q,EM(D.hours,"hour",0,23),EM(D.minutes,"minute",0,59),EM(D.seconds,"seconds",0,60),Tf6(D.fractionalMilliseconds)))},"buildDate"),qf6=k2((A)=>{let B=new Date().getUTCFullYear(),Q=Math.floor(B/100)*100+Ce(Ke(A));if(Q<B)return Q+100;return Q},"parseTwoDigitYear"),Nf6=1576800000000,Lf6=k2((A)=>{if(A.getTime()-new Date().getTime()>Nf6)return new Date(Date.UTC(A.getUTCFullYear()-100,A.getUTCMonth(),A.getUTCDate(),A.getUTCHours(),A.getUTCMinutes(),A.getUTCSeconds(),A.getUTCMilliseconds()));return A},"adjustRfc850Year"),eK0=k2((A)=>{let B=YH0.indexOf(A);if(B<0)throw new TypeError(`Invalid month: ${A}`);return B+1},"parseMonthByShortName"),Mf6=[31,28,31,30,31,30,31,31,30,31,30,31],Rf6=k2((A,B,Q)=>{let D=Mf6[B];if(B===1&&Of6(A))D=29;if(Q>D)throw new TypeError(`Invalid day for ${YH0[B]} in ${A}: ${Q}`)},"validateDayOfMonth"),Of6=k2((A)=>{return A%4===0&&(A%100!==0||A%400===0)},"isLeapYear"),EM=k2((A,B,Q,D)=>{let Z=tGB(Ke(A));if(Z<Q||Z>D)throw new TypeError(`${B} must be between ${Q} and ${D}, inclusive`);return Z},"parseDateValue"),Tf6=k2((A)=>{if(A===null||A===void 0)return 0;return sGB("0."+A)*1000},"parseMilliseconds"),Pf6=k2((A)=>{let B=A[0],Q=1;if(B=="+")Q=1;else if(B=="-")Q=-1;else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);let D=Number(A.substring(1,3)),Z=Number(A.substring(4,6));return Q*(D*60+Z)*60*1000},"parseOffsetToMilliseconds"),Ke=k2((A)=>{let B=0;while(B<A.length-1&&A.charAt(B)==="0")B++;if(B===0)return A;return A.slice(B)},"stripLeadingZeroes"),AFB=class A extends Error{constructor(B){super(B.message);Object.setPrototypeOf(this,A.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}};k2(AFB,"ServiceException");var Sf6=AFB,BFB=k2((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),QFB=k2(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=kf6(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:(B==null?void 0:B.code)||(B==null?void 0:B.Code)||D||G||"UnknownError",$fault:"client",$metadata:Z});throw BFB(F,B)},"throwDefaultError"),jf6=k2((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{QFB({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),kf6=k2((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),yf6=k2((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),uGB=!1,_f6=k2((A)=>{if(A&&!uGB&&parseInt(A.substring(1,A.indexOf(".")))<14)uGB=!0},"emitWarningIfUnsupportedVersion"),xf6=k2((A)=>{let B=[];for(let Q in AH0.AlgorithmId){let D=AH0.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{_checksumAlgorithms:B,addChecksumAlgorithm(Q){this._checksumAlgorithms.push(Q)},checksumAlgorithms(){return this._checksumAlgorithms}}},"getChecksumConfiguration"),vf6=k2((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),bf6=k2((A)=>{let B=A.retryStrategy;return{setRetryStrategy(Q){B=Q},retryStrategy(){return B}}},"getRetryConfiguration"),ff6=k2((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),DFB=k2((A)=>{return{...xf6(A),...bf6(A)}},"getDefaultExtensionConfiguration"),hf6=DFB,gf6=k2((A)=>{return{...vf6(A),...ff6(A)}},"resolveDefaultRuntimeConfig");function lk1(A){return encodeURIComponent(A).replace(/[!'()*]/g,function(B){return"%"+B.charCodeAt(0).toString(16).toUpperCase()})}k2(lk1,"extendedEncodeURIComponent");var uf6=k2((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),ZFB=k2((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=ZFB(A[Q]);return A},"getValueFromTextNode"),TD1=k2(function(){let A=Object.getPrototypeOf(this).constructor,Q=new(Function.bind.apply(String,[null,...arguments]));return Object.setPrototypeOf(Q,A.prototype),Q},"StringWrapper");TD1.prototype=Object.create(String.prototype,{constructor:{value:TD1,enumerable:!1,writable:!0,configurable:!0}});Object.setPrototypeOf(TD1,String);var GFB=class A extends TD1{deserializeJSON(){return JSON.parse(super.toString())}toJSON(){return super.toString()}static fromObject(B){if(B instanceof A)return B;else if(B instanceof String||typeof B==="string")return new A(B);return new A(JSON.stringify(B))}};k2(GFB,"LazyJsonString");var mf6=GFB;function WH0(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,lf6(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}FFB(D,null,G,F)}return D}k2(WH0,"map");var df6=k2((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),cf6=k2((A,B)=>{let Q={};for(let D in B)FFB(Q,A,B,D);return Q},"take"),lf6=k2((A,B,Q)=>{return WH0(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),FFB=k2((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=pf6,Y=if6,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),pf6=k2((A)=>A!=null,"nonNullish"),if6=k2((A)=>A,"pass"),nf6=k2((A,B,Q,D,Z,G)=>{if(B!=null&&B[Q]!==void 0){let F=D();if(F.length<=0)throw new Error("Empty value provided for input HTTP label: "+Q+".");A=A.replace(Z,G?F.split("/").map((I)=>lk1(I)).join("/"):lk1(F))}else throw new Error("No value provided for input HTTP label: "+Q+".");return A},"resolvedPath"),af6=k2((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),ZH0=k2((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(ZH0);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=ZH0(A[Q])}return B}return A},"_json");function IFB(A,B,Q){if(Q<=0||!Number.isInteger(Q))throw new Error("Invalid number of delimiters ("+Q+") for splitEvery.");let D=A.split(B);if(Q===1)return D;let Z=[],G="";for(let F=0;F<D.length;F++){if(G==="")G=D[F];else G+=B+D[F];if((F+1)%Q===0)Z.push(G),G=""}if(G!=="")Z.push(G);return Z}k2(IFB,"splitEvery")});
var YGB=E((Ws5,IGB)=>{var{defineProperty:fk1,getOwnPropertyDescriptor:av6,getOwnPropertyNames:sv6}=Object,rv6=Object.prototype.hasOwnProperty,cK0=(A,B)=>fk1(A,"name",{value:B,configurable:!0}),ov6=(A,B)=>{for(var Q in B)fk1(A,Q,{get:B[Q],enumerable:!0})},tv6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of sv6(B))if(!rv6.call(A,Z)&&Z!==Q)fk1(A,Z,{get:()=>B[Z],enumerable:!(D=av6(B,Z))||D.enumerable})}return A},ev6=(A)=>tv6(fk1({},"__esModule",{value:!0}),A),GGB={};ov6(GGB,{escapeUri:()=>FGB,escapeUriPath:()=>Bb6});IGB.exports=ev6(GGB);var FGB=cK0((A)=>encodeURIComponent(A).replace(/[!'()*]/g,Ab6),"escapeUri"),Ab6=cK0((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),Bb6=cK0((A)=>A.split("/").map(FGB).join("/"),"escapeUriPath")});
var ZGB=E((QGB)=>{Object.defineProperty(QGB,"__esModule",{value:!0});QGB.getAwsChunkedEncodingStream=void 0;var iv6=J1("stream"),nv6=(A,B)=>{let{base64Encoder:Q,bodyLengthChecker:D,checksumAlgorithmFn:Z,checksumLocationName:G,streamHasher:F}=B,I=Q!==void 0&&Z!==void 0&&G!==void 0&&F!==void 0,Y=I?F(Z,A):void 0,W=new iv6.Readable({read:()=>{}});return A.on("data",(J)=>{let X=D(J)||0;W.push(`${X.toString(16)}\r
`),W.push(J),W.push(`\r
`)}),A.on("end",async()=>{if(W.push(`0\r
`),I){let J=Q(await Y);W.push(`${G}:${J}\r
`),W.push(`\r
`)}W.push(null)}),W};QGB.getAwsChunkedEncodingStream=nv6});
var fZB=E((vZB)=>{Object.defineProperty(vZB,"__esModule",{value:!0});vZB.toBase64=void 0;var Yv6=jk1(),Wv6=xZB(),Jv6=(A)=>{let B;if(typeof A==="string")B=Wv6.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return Yv6.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};vZB.toBase64=Jv6});
var gGB=E((Cs5,mk1)=>{var{defineProperty:gk1,getOwnPropertyDescriptor:_b6,getOwnPropertyNames:xb6}=Object,vb6=Object.prototype.hasOwnProperty,oK0=(A,B)=>gk1(A,"name",{value:B,configurable:!0}),bb6=(A,B)=>{for(var Q in B)gk1(A,Q,{get:B[Q],enumerable:!0})},sK0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of xb6(B))if(!vb6.call(A,Z)&&Z!==Q)gk1(A,Z,{get:()=>B[Z],enumerable:!(D=_b6(B,Z))||D.enumerable})}return A},_GB=(A,B,Q)=>(sK0(A,B,"default"),Q&&sK0(Q,B,"default")),fb6=(A)=>sK0(gk1({},"__esModule",{value:!0}),A),uk1={};bb6(uk1,{Uint8ArrayBlobAdapter:()=>rK0});mk1.exports=fb6(uk1);var xGB=hK0(),vGB=BGB();function bGB(A,B="utf-8"){if(B==="base64")return xGB.toBase64(A);return vGB.toUtf8(A)}oK0(bGB,"transformToString");function fGB(A,B){if(B==="base64")return rK0.mutate(xGB.fromBase64(A));return rK0.mutate(vGB.fromUtf8(A))}oK0(fGB,"transformFromString");var hGB=class A extends Uint8Array{static fromString(B,Q="utf-8"){switch(typeof B){case"string":return fGB(B,Q);default:throw new Error(`Unsupported conversion from ${typeof B} to Uint8ArrayBlobAdapter.`)}}static mutate(B){return Object.setPrototypeOf(B,A.prototype),B}transformToString(B="utf-8"){return bGB(this,B)}};oK0(hGB,"Uint8ArrayBlobAdapter");var rK0=hGB;_GB(uk1,ZGB(),mk1.exports);_GB(uk1,yGB(),mk1.exports)});
var hK0=E((Ds5,yk1)=>{var{defineProperty:hZB,getOwnPropertyDescriptor:Xv6,getOwnPropertyNames:Vv6}=Object,Cv6=Object.prototype.hasOwnProperty,bK0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Vv6(B))if(!Cv6.call(A,Z)&&Z!==Q)hZB(A,Z,{get:()=>B[Z],enumerable:!(D=Xv6(B,Z))||D.enumerable})}return A},gZB=(A,B,Q)=>(bK0(A,B,"default"),Q&&bK0(Q,B,"default")),Kv6=(A)=>bK0(hZB({},"__esModule",{value:!0}),A),fK0={};yk1.exports=Kv6(fK0);gZB(fK0,SZB(),yk1.exports);gZB(fK0,fZB(),yk1.exports)});
var jk1=E((ea5,OZB)=>{var{defineProperty:Sk1,getOwnPropertyDescriptor:dx6,getOwnPropertyNames:cx6}=Object,lx6=Object.prototype.hasOwnProperty,MZB=(A,B)=>Sk1(A,"name",{value:B,configurable:!0}),px6=(A,B)=>{for(var Q in B)Sk1(A,Q,{get:B[Q],enumerable:!0})},ix6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of cx6(B))if(!lx6.call(A,Z)&&Z!==Q)Sk1(A,Z,{get:()=>B[Z],enumerable:!(D=dx6(B,Z))||D.enumerable})}return A},nx6=(A)=>ix6(Sk1({},"__esModule",{value:!0}),A),RZB={};px6(RZB,{fromArrayBuffer:()=>sx6,fromString:()=>rx6});OZB.exports=nx6(RZB);var ax6=LZB(),xK0=J1("buffer"),sx6=MZB((A,B=0,Q=A.byteLength-B)=>{if(!ax6.isArrayBuffer(A))throw new TypeError(`The "input" argument must be ArrayBuffer. Received type ${typeof A} (${A})`);return xK0.Buffer.from(A,B,Q)},"fromArrayBuffer"),rx6=MZB((A,B)=>{if(typeof A!=="string")throw new TypeError(`The "input" argument must be of type string. Received type ${typeof A} (${A})`);return B?xK0.Buffer.from(A,B):xK0.Buffer.from(A)},"fromString")});
var lZB=E((Zs5,cZB)=>{var{defineProperty:_k1,getOwnPropertyDescriptor:Hv6,getOwnPropertyNames:zv6}=Object,Ev6=Object.prototype.hasOwnProperty,kE=(A,B)=>_k1(A,"name",{value:B,configurable:!0}),Uv6=(A,B)=>{for(var Q in B)_k1(A,Q,{get:B[Q],enumerable:!0})},wv6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of zv6(B))if(!Ev6.call(A,Z)&&Z!==Q)_k1(A,Z,{get:()=>B[Z],enumerable:!(D=Hv6(B,Z))||D.enumerable})}return A},$v6=(A)=>wv6(_k1({},"__esModule",{value:!0}),A),dZB={};Uv6(dZB,{constructStack:()=>gK0});cZB.exports=$v6(dZB);var Xm=kE((A,B)=>{let Q=[];if(A)Q.push(A);if(B)for(let D of B)Q.push(D);return Q},"getAllAliases"),kx=kE((A,B)=>{return`${A||"anonymous"}${B&&B.length>0?` (a.k.a. ${B.join(",")})`:""}`},"getMiddlewareNameWithAliases"),gK0=kE(()=>{let A=[],B=[],Q=!1,D=new Set,Z=kE((X)=>X.sort((V,C)=>uZB[C.step]-uZB[V.step]||mZB[C.priority||"normal"]-mZB[V.priority||"normal"]),"sort"),G=kE((X)=>{let V=!1,C=kE((K)=>{let H=Xm(K.name,K.aliases);if(H.includes(X)){V=!0;for(let z of H)D.delete(z);return!1}return!0},"filterCb");return A=A.filter(C),B=B.filter(C),V},"removeByName"),F=kE((X)=>{let V=!1,C=kE((K)=>{if(K.middleware===X){V=!0;for(let H of Xm(K.name,K.aliases))D.delete(H);return!1}return!0},"filterCb");return A=A.filter(C),B=B.filter(C),V},"removeByReference"),I=kE((X)=>{var V;return A.forEach((C)=>{X.add(C.middleware,{...C})}),B.forEach((C)=>{X.addRelativeTo(C.middleware,{...C})}),(V=X.identifyOnResolve)==null||V.call(X,J.identifyOnResolve()),X},"cloneTo"),Y=kE((X)=>{let V=[];return X.before.forEach((C)=>{if(C.before.length===0&&C.after.length===0)V.push(C);else V.push(...Y(C))}),V.push(X),X.after.reverse().forEach((C)=>{if(C.before.length===0&&C.after.length===0)V.push(C);else V.push(...Y(C))}),V},"expandRelativeMiddlewareList"),W=kE((X=!1)=>{let V=[],C=[],K={};return A.forEach((z)=>{let $={...z,before:[],after:[]};for(let L of Xm($.name,$.aliases))K[L]=$;V.push($)}),B.forEach((z)=>{let $={...z,before:[],after:[]};for(let L of Xm($.name,$.aliases))K[L]=$;C.push($)}),C.forEach((z)=>{if(z.toMiddleware){let $=K[z.toMiddleware];if($===void 0){if(X)return;throw new Error(`${z.toMiddleware} is not found when adding ${kx(z.name,z.aliases)} middleware ${z.relation} ${z.toMiddleware}`)}if(z.relation==="after")$.after.push(z);if(z.relation==="before")$.before.push(z)}}),Z(V).map(Y).reduce((z,$)=>{return z.push(...$),z},[])},"getMiddlewareList"),J={add:(X,V={})=>{let{name:C,override:K,aliases:H}=V,z={step:"initialize",priority:"normal",middleware:X,...V},$=Xm(C,H);if($.length>0){if($.some((L)=>D.has(L))){if(!K)throw new Error(`Duplicate middleware name '${kx(C,H)}'`);for(let L of $){let N=A.findIndex((R)=>{var T;return R.name===L||((T=R.aliases)==null?void 0:T.some((j)=>j===L))});if(N===-1)continue;let O=A[N];if(O.step!==z.step||z.priority!==O.priority)throw new Error(`"${kx(O.name,O.aliases)}" middleware with ${O.priority} priority in ${O.step} step cannot be overridden by "${kx(C,H)}" middleware with ${z.priority} priority in ${z.step} step.`);A.splice(N,1)}}for(let L of $)D.add(L)}A.push(z)},addRelativeTo:(X,V)=>{let{name:C,override:K,aliases:H}=V,z={middleware:X,...V},$=Xm(C,H);if($.length>0){if($.some((L)=>D.has(L))){if(!K)throw new Error(`Duplicate middleware name '${kx(C,H)}'`);for(let L of $){let N=B.findIndex((R)=>{var T;return R.name===L||((T=R.aliases)==null?void 0:T.some((j)=>j===L))});if(N===-1)continue;let O=B[N];if(O.toMiddleware!==z.toMiddleware||O.relation!==z.relation)throw new Error(`"${kx(O.name,O.aliases)}" middleware ${O.relation} "${O.toMiddleware}" middleware cannot be overridden by "${kx(C,H)}" middleware ${z.relation} "${z.toMiddleware}" middleware.`);B.splice(N,1)}}for(let L of $)D.add(L)}B.push(z)},clone:()=>I(gK0()),use:(X)=>{X.applyToStack(J)},remove:(X)=>{if(typeof X==="string")return G(X);else return F(X)},removeByTag:(X)=>{let V=!1,C=kE((K)=>{let{tags:H,name:z,aliases:$}=K;if(H&&H.includes(X)){let L=Xm(z,$);for(let N of L)D.delete(N);return V=!0,!1}return!0},"filterCb");return A=A.filter(C),B=B.filter(C),V},concat:(X)=>{var V;let C=I(gK0());return C.use(X),C.identifyOnResolve(Q||C.identifyOnResolve()||(((V=X.identifyOnResolve)==null?void 0:V.call(X))??!1)),C},applyToStack:I,identify:()=>{return W(!0).map((X)=>{let V=X.step??X.relation+" "+X.toMiddleware;return kx(X.name,X.aliases)+" - "+V})},identifyOnResolve(X){if(typeof X==="boolean")Q=X;return Q},resolve:(X,V)=>{for(let C of W().map((K)=>K.middleware).reverse())X=C(X,V);if(Q)console.log(J.identify());return X}};return J},"constructStack"),uZB={initialize:5,serialize:4,build:3,finalizeRequest:2,deserialize:1},mZB={high:3,normal:2,low:1}});
var mK0=E((Fs5,rZB)=>{var{defineProperty:vk1,getOwnPropertyDescriptor:Sv6,getOwnPropertyNames:jv6}=Object,kv6=Object.prototype.hasOwnProperty,aZB=(A,B)=>vk1(A,"name",{value:B,configurable:!0}),yv6=(A,B)=>{for(var Q in B)vk1(A,Q,{get:B[Q],enumerable:!0})},_v6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of jv6(B))if(!kv6.call(A,Z)&&Z!==Q)vk1(A,Z,{get:()=>B[Z],enumerable:!(D=Sv6(B,Z))||D.enumerable})}return A},xv6=(A)=>_v6(vk1({},"__esModule",{value:!0}),A),sZB={};yv6(sZB,{fromArrayBuffer:()=>bv6,fromString:()=>fv6});rZB.exports=xv6(sZB);var vv6=nZB(),uK0=J1("buffer"),bv6=aZB((A,B=0,Q=A.byteLength-B)=>{if(!vv6.isArrayBuffer(A))throw new TypeError(`The "input" argument must be ArrayBuffer. Received type ${typeof A} (${A})`);return uK0.Buffer.from(A,B,Q)},"fromArrayBuffer"),fv6=aZB((A,B)=>{if(typeof A!=="string")throw new TypeError(`The "input" argument must be of type string. Received type ${typeof A} (${A})`);return B?uK0.Buffer.from(A,B):uK0.Buffer.from(A)},"fromString")});
var nZB=E((Gs5,iZB)=>{var{defineProperty:xk1,getOwnPropertyDescriptor:qv6,getOwnPropertyNames:Nv6}=Object,Lv6=Object.prototype.hasOwnProperty,Mv6=(A,B)=>xk1(A,"name",{value:B,configurable:!0}),Rv6=(A,B)=>{for(var Q in B)xk1(A,Q,{get:B[Q],enumerable:!0})},Ov6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Nv6(B))if(!Lv6.call(A,Z)&&Z!==Q)xk1(A,Z,{get:()=>B[Z],enumerable:!(D=qv6(B,Z))||D.enumerable})}return A},Tv6=(A)=>Ov6(xk1({},"__esModule",{value:!0}),A),pZB={};Rv6(pZB,{isArrayBuffer:()=>Pv6});iZB.exports=Tv6(pZB);var Pv6=Mv6((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var xZB=E((Bs5,_ZB)=>{var{defineProperty:kk1,getOwnPropertyDescriptor:Av6,getOwnPropertyNames:Bv6}=Object,Qv6=Object.prototype.hasOwnProperty,vK0=(A,B)=>kk1(A,"name",{value:B,configurable:!0}),Dv6=(A,B)=>{for(var Q in B)kk1(A,Q,{get:B[Q],enumerable:!0})},Zv6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Bv6(B))if(!Qv6.call(A,Z)&&Z!==Q)kk1(A,Z,{get:()=>B[Z],enumerable:!(D=Av6(B,Z))||D.enumerable})}return A},Gv6=(A)=>Zv6(kk1({},"__esModule",{value:!0}),A),jZB={};Dv6(jZB,{fromUtf8:()=>yZB,toUint8Array:()=>Fv6,toUtf8:()=>Iv6});_ZB.exports=Gv6(jZB);var kZB=jk1(),yZB=vK0((A)=>{let B=kZB.fromString(A,"utf8");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength/Uint8Array.BYTES_PER_ELEMENT)},"fromUtf8"),Fv6=vK0((A)=>{if(typeof A==="string")return yZB(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)},"toUint8Array"),Iv6=vK0((A)=>{if(typeof A==="string")return A;if(typeof A!=="object"||typeof A.byteOffset!=="number"||typeof A.byteLength!=="number")throw new Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return kZB.fromArrayBuffer(A.buffer,A.byteOffset,A.byteLength).toString("utf8")},"toUtf8")});
var yGB=E((jGB)=>{Object.defineProperty(jGB,"__esModule",{value:!0});jGB.sdkStreamMixin=void 0;var Sb6=PGB(),jb6=mK0(),aK0=J1("stream"),kb6=J1("util"),SGB="The stream has already been transformed.",yb6=(A)=>{var B,Q;if(!(A instanceof aK0.Readable)){let G=((Q=(B=A===null||A===void 0?void 0:A.__proto__)===null||B===void 0?void 0:B.constructor)===null||Q===void 0?void 0:Q.name)||A;throw new Error(`Unexpected stream implementation, expect Stream.Readable instance, got ${G}`)}let D=!1,Z=async()=>{if(D)throw new Error(SGB);return D=!0,await Sb6.streamCollector(A)};return Object.assign(A,{transformToByteArray:Z,transformToString:async(G)=>{let F=await Z();if(G===void 0||Buffer.isEncoding(G))return jb6.fromArrayBuffer(F.buffer,F.byteOffset,F.byteLength).toString(G);else return new kb6.TextDecoder(G).decode(F)},transformToWebStream:()=>{if(D)throw new Error(SGB);if(A.readableFlowing!==null)throw new Error("The stream has been consumed by other callbacks.");if(typeof aK0.Readable.toWeb!=="function")throw new Error("Readable.toWeb() is not supported. Please make sure you are using Node.js >= 17.0.0, or polyfill is available.");return D=!0,aK0.Readable.toWeb(A)}})};jGB.sdkStreamMixin=yb6});

module.exports = WFB;
