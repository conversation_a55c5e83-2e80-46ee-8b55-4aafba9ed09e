// E_module package extracted with entry point: Y1B

var A1B=E((te2)=>{Object.defineProperty(te2,"__esModule",{value:!0});te2.VERSION=void 0;te2.VERSION="0.200.0"});
var F1B=E((pJ0)=>{Object.defineProperty(pJ0,"__esModule",{value:!0});pJ0.OTLPLogExporter=void 0;var uE6=G1B();Object.defineProperty(pJ0,"OTLPLogExporter",{enumerable:!0,get:function(){return uE6.OTLPLogExporter}})});
var G1B=E((D1B)=>{Object.defineProperty(D1B,"__esModule",{value:!0});D1B.OTLPLogExporter=void 0;var fE6=Tu(),hE6=ju(),gE6=A1B(),B1B=uo();class Q1B extends fE6.OTLPExporterBase{constructor(A={}){super(B1B.createOtlpHttpExportDelegate(B1B.convertLegacyHttpOptions(A,"LOGS","v1/logs",{"User-Agent":`OTel-OTLP-Exporter-JavaScript/${gE6.VERSION}`,"Content-Type":"application/json"}),hE6.JsonLogsSerializer))}}D1B.OTLPLogExporter=Q1B});
var I1B=E((iJ0)=>{Object.defineProperty(iJ0,"__esModule",{value:!0});iJ0.OTLPLogExporter=void 0;var dE6=F1B();Object.defineProperty(iJ0,"OTLPLogExporter",{enumerable:!0,get:function(){return dE6.OTLPLogExporter}})});
var Y1B=E((nJ0)=>{Object.defineProperty(nJ0,"__esModule",{value:!0});nJ0.OTLPLogExporter=void 0;var lE6=I1B();Object.defineProperty(nJ0,"OTLPLogExporter",{enumerable:!0,get:function(){return lE6.OTLPLogExporter}})});

module.exports = Y1B;
