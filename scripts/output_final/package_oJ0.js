// dependency_chain package extracted with entry point: oJ0

var FO2=E((FF0)=>{Object.defineProperty(FF0,"__esModule",{value:!0});FF0._globalThis=void 0;var Ta4=GO2();Object.defineProperty(FF0,"_globalThis",{enumerable:!0,get:function(){return Ta4._globalThis}})});
var GF0=E((BO2)=>{Object.defineProperty(BO2,"__esModule",{value:!0});BO2.ProxyLoggerProvider=void 0;var Ra4=zO1(),Oa4=ZF0();class AO2{getLogger(A,B,Q){var D;return(D=this.getDelegateLogger(A,B,Q))!==null&&D!==void 0?D:new Oa4.ProxyLogger(this,A,B,Q)}getDelegate(){var A;return(A=this._delegate)!==null&&A!==void 0?A:Ra4.NOOP_LOGGER_PROVIDER}setDelegate(A){this._delegate=A}getDelegateLogger(A,B,Q){var D;return(D=this._delegate)===null||D===void 0?void 0:D.getLogger(A,B,Q)}}BO2.ProxyLoggerProvider=AO2});
var GO2=E((DO2)=>{Object.defineProperty(DO2,"__esModule",{value:!0});DO2._globalThis=void 0;DO2._globalThis=typeof globalThis==="object"?globalThis:global});
var HO1=E((nR2)=>{Object.defineProperty(nR2,"__esModule",{value:!0});nR2.NOOP_LOGGER=nR2.NoopLogger=void 0;class QF0{emit(A){}}nR2.NoopLogger=QF0;nR2.NOOP_LOGGER=new QF0});
var IO2=E((IF0)=>{Object.defineProperty(IF0,"__esModule",{value:!0});IF0._globalThis=void 0;var Sa4=FO2();Object.defineProperty(IF0,"_globalThis",{enumerable:!0,get:function(){return Sa4._globalThis}})});
var JO2=E((YO2)=>{Object.defineProperty(YO2,"__esModule",{value:!0});YO2.API_BACKWARDS_COMPATIBILITY_VERSION=YO2.makeGetter=YO2._global=YO2.GLOBAL_LOGS_API_KEY=void 0;var ka4=IO2();YO2.GLOBAL_LOGS_API_KEY=Symbol.for("io.opentelemetry.js.api.logs");YO2._global=ka4._globalThis;function ya4(A,B,Q){return(D)=>D===A?B:Q}YO2.makeGetter=ya4;YO2.API_BACKWARDS_COMPATIBILITY_VERSION=1});
var KO2=E((VO2)=>{Object.defineProperty(VO2,"__esModule",{value:!0});VO2.LogsAPI=void 0;var FE=JO2(),ba4=zO1(),XO2=GF0();class YF0{constructor(){this._proxyLoggerProvider=new XO2.ProxyLoggerProvider}static getInstance(){if(!this._instance)this._instance=new YF0;return this._instance}setGlobalLoggerProvider(A){if(FE._global[FE.GLOBAL_LOGS_API_KEY])return this.getLoggerProvider();return FE._global[FE.GLOBAL_LOGS_API_KEY]=FE.makeGetter(FE.API_BACKWARDS_COMPATIBILITY_VERSION,A,ba4.NOOP_LOGGER_PROVIDER),this._proxyLoggerProvider.setDelegate(A),A}getLoggerProvider(){var A,B;return(B=(A=FE._global[FE.GLOBAL_LOGS_API_KEY])===null||A===void 0?void 0:A.call(FE._global,FE.API_BACKWARDS_COMPATIBILITY_VERSION))!==null&&B!==void 0?B:this._proxyLoggerProvider}getLogger(A,B,Q){return this.getLoggerProvider().getLogger(A,B,Q)}disable(){delete FE._global[FE.GLOBAL_LOGS_API_KEY],this._proxyLoggerProvider=new XO2.ProxyLoggerProvider}}VO2.LogsAPI=YF0});
var WF0=E((ZP)=>{Object.defineProperty(ZP,"__esModule",{value:!0});ZP.logs=ZP.ProxyLoggerProvider=ZP.ProxyLogger=ZP.NoopLoggerProvider=ZP.NOOP_LOGGER_PROVIDER=ZP.NoopLogger=ZP.NOOP_LOGGER=ZP.SeverityNumber=void 0;var fa4=iR2();Object.defineProperty(ZP,"SeverityNumber",{enumerable:!0,get:function(){return fa4.SeverityNumber}});var HO2=HO1();Object.defineProperty(ZP,"NOOP_LOGGER",{enumerable:!0,get:function(){return HO2.NOOP_LOGGER}});Object.defineProperty(ZP,"NoopLogger",{enumerable:!0,get:function(){return HO2.NoopLogger}});var zO2=zO1();Object.defineProperty(ZP,"NOOP_LOGGER_PROVIDER",{enumerable:!0,get:function(){return zO2.NOOP_LOGGER_PROVIDER}});Object.defineProperty(ZP,"NoopLoggerProvider",{enumerable:!0,get:function(){return zO2.NoopLoggerProvider}});var ha4=ZF0();Object.defineProperty(ZP,"ProxyLogger",{enumerable:!0,get:function(){return ha4.ProxyLogger}});var ga4=GF0();Object.defineProperty(ZP,"ProxyLoggerProvider",{enumerable:!0,get:function(){return ga4.ProxyLoggerProvider}});var ua4=KO2();ZP.logs=ua4.LogsAPI.getInstance()});
var ZF0=E((tR2)=>{Object.defineProperty(tR2,"__esModule",{value:!0});tR2.ProxyLogger=void 0;var Ma4=HO1();class oR2{constructor(A,B,Q,D){this._provider=A,this.name=B,this.version=Q,this.options=D}emit(A){this._getLogger().emit(A)}_getLogger(){if(this._delegate)return this._delegate;let A=this._provider.getDelegateLogger(this.name,this.version,this.options);if(!A)return Ma4.NOOP_LOGGER;return this._delegate=A,this._delegate}}tR2.ProxyLogger=oR2});
var iR2=E((pR2)=>{Object.defineProperty(pR2,"__esModule",{value:!0});pR2.SeverityNumber=void 0;var $a4;(function(A){A[A.UNSPECIFIED=0]="UNSPECIFIED",A[A.TRACE=1]="TRACE",A[A.TRACE2=2]="TRACE2",A[A.TRACE3=3]="TRACE3",A[A.TRACE4=4]="TRACE4",A[A.DEBUG=5]="DEBUG",A[A.DEBUG2=6]="DEBUG2",A[A.DEBUG3=7]="DEBUG3",A[A.DEBUG4=8]="DEBUG4",A[A.INFO=9]="INFO",A[A.INFO2=10]="INFO2",A[A.INFO3=11]="INFO3",A[A.INFO4=12]="INFO4",A[A.WARN=13]="WARN",A[A.WARN2=14]="WARN2",A[A.WARN3=15]="WARN3",A[A.WARN4=16]="WARN4",A[A.ERROR=17]="ERROR",A[A.ERROR2=18]="ERROR2",A[A.ERROR3=19]="ERROR3",A[A.ERROR4=20]="ERROR4",A[A.FATAL=21]="FATAL",A[A.FATAL2=22]="FATAL2",A[A.FATAL3=23]="FATAL3",A[A.FATAL4=24]="FATAL4"})($a4=pR2.SeverityNumber||(pR2.SeverityNumber={}))});
var zO1=E((sR2)=>{Object.defineProperty(sR2,"__esModule",{value:!0});sR2.NOOP_LOGGER_PROVIDER=sR2.NoopLoggerProvider=void 0;var Na4=HO1();class DF0{getLogger(A,B,Q){return new Na4.NoopLogger}}sR2.NoopLoggerProvider=DF0;sR2.NOOP_LOGGER_PROVIDER=new DF0});

module.exports = oJ0;
