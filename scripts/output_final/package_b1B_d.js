// E_module package extracted with entry point: b1B

var _1B=E((sf5,y1B)=>{var CU6=J1("os"),k1B=J1("tty"),wE=ZB1(),{env:zI}=process,Fx;if(wE("no-color")||wE("no-colors")||wE("color=false")||wE("color=never"))Fx=0;else if(wE("color")||wE("colors")||wE("color=true")||wE("color=always"))Fx=1;if("FORCE_COLOR"in zI)if(zI.FORCE_COLOR==="true")Fx=1;else if(zI.FORCE_COLOR==="false")Fx=0;else Fx=zI.FORCE_COLOR.length===0?1:Math.min(parseInt(zI.FORCE_COLOR,10),3);function AX0(A){if(A===0)return!1;return{level:A,hasBasic:!0,has256:A>=2,has16m:A>=3}}function BX0(A,B){if(Fx===0)return 0;if(wE("color=16m")||wE("color=full")||wE("color=truecolor"))return 3;if(wE("color=256"))return 2;if(A&&!B&&Fx===void 0)return 0;let Q=Fx||0;if(zI.TERM==="dumb")return Q;if(process.platform==="win32"){let D=CU6.release().split(".");if(Number(D[0])>=10&&Number(D[2])>=10586)return Number(D[2])>=14931?3:2;return 1}if("CI"in zI){if(["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some((D)=>(D in zI))||zI.CI_NAME==="codeship")return 1;return Q}if("TEAMCITY_VERSION"in zI)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(zI.TEAMCITY_VERSION)?1:0;if(zI.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in zI){let D=parseInt((zI.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(zI.TERM_PROGRAM){case"iTerm.app":return D>=3?3:2;case"Apple_Terminal":return 2}}if(/-256(color)?$/i.test(zI.TERM))return 2;if(/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(zI.TERM))return 1;if("COLORTERM"in zI)return 1;return Q}function KU6(A){let B=BX0(A,A&&A.isTTY);return AX0(B)}y1B.exports={supportsColor:KU6,stdout:AX0(BX0(!0,k1B.isatty(1))),stderr:AX0(BX0(!0,k1B.isatty(2)))}});
var b1B=E((rf5,v1B)=>{var HU6=_1B(),Lt=ZB1();function x1B(A){if(/^\d{3,4}$/.test(A)){let Q=/(\d{1,2})(\d{2})/.exec(A);return{major:0,minor:parseInt(Q[1],10),patch:parseInt(Q[2],10)}}let B=(A||"").split(".").map((Q)=>parseInt(Q,10));return{major:B[0],minor:B[1],patch:B[2]}}function QX0(A){let{env:B}=process;if("FORCE_HYPERLINK"in B)return!(B.FORCE_HYPERLINK.length>0&&parseInt(B.FORCE_HYPERLINK,10)===0);if(Lt("no-hyperlink")||Lt("no-hyperlinks")||Lt("hyperlink=false")||Lt("hyperlink=never"))return!1;if(Lt("hyperlink=true")||Lt("hyperlink=always"))return!0;if("NETLIFY"in B)return!0;if(!HU6.supportsColor(A))return!1;if(A&&!A.isTTY)return!1;if(process.platform==="win32")return!1;if("CI"in B)return!1;if("TEAMCITY_VERSION"in B)return!1;if("TERM_PROGRAM"in B){let Q=x1B(B.TERM_PROGRAM_VERSION);switch(B.TERM_PROGRAM){case"iTerm.app":if(Q.major===3)return Q.minor>=1;return Q.major>3;case"WezTerm":return Q.major>=20200620;case"vscode":return Q.major>1||Q.major===1&&Q.minor>=72}}if("VTE_VERSION"in B){if(B.VTE_VERSION==="0.50.0")return!1;let Q=x1B(B.VTE_VERSION);return Q.major>0||Q.minor>=50}return!1}v1B.exports={supportsHyperlink:QX0,stdout:QX0(process.stdout),stderr:QX0(process.stderr)}});

module.exports = b1B;
