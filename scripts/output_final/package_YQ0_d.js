// E_module package extracted with entry point: YQ0

var A$1=E(($Y5,$dA)=>{var{defineProperty:ew1,getOwnPropertyDescriptor:HaQ,getOwnPropertyNames:zaQ}=Object,EaQ=Object.prototype.hasOwnProperty,UB0=(A,B)=>ew1(A,"name",{value:B,configurable:!0}),UaQ=(A,B)=>{for(var Q in B)ew1(A,Q,{get:B[Q],enumerable:!0})},waQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of zaQ(B))if(!EaQ.call(A,Z)&&Z!==Q)ew1(A,Z,{get:()=>B[Z],enumerable:!(D=HaQ(B,Z))||D.enumerable})}return A},$aQ=(A)=>waQ(ew1({},"__esModule",{value:!0}),A),EdA={};UaQ(EdA,{getLoggerPlugin:()=>qaQ,loggerMiddleware:()=>UdA,loggerMiddlewareOptions:()=>wdA});$dA.exports=$aQ(EdA);var UdA=UB0(()=>(A,B)=>async(Q)=>{try{let D=await A(Q),{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y,overrideOutputFilterSensitiveLog:W}=I,J=Y??B.inputFilterSensitiveLog,X=W??B.outputFilterSensitiveLog,{$metadata:V,...C}=D.output;return F?.info?.({clientName:Z,commandName:G,input:J(Q.input),output:X(C),metadata:V}),D}catch(D){let{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y}=I,W=Y??B.inputFilterSensitiveLog;throw F?.error?.({clientName:Z,commandName:G,input:W(Q.input),error:D,metadata:D.$metadata}),D}},"loggerMiddleware"),wdA={name:"loggerMiddleware",tags:["LOGGER"],step:"initialize",override:!0},qaQ=UB0((A)=>({applyToStack:UB0((B)=>{B.add(UdA(),wdA)},"applyToStack")}),"getLoggerPlugin")});
var A61=E((lW5,p$1)=>{var{defineProperty:iaA,getOwnPropertyDescriptor:bA4,getOwnPropertyNames:fA4}=Object,hA4=Object.prototype.hasOwnProperty,BQ0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of fA4(B))if(!hA4.call(A,Z)&&Z!==Q)iaA(A,Z,{get:()=>B[Z],enumerable:!(D=bA4(B,Z))||D.enumerable})}return A},naA=(A,B,Q)=>(BQ0(A,B,"default"),Q&&BQ0(Q,B,"default")),gA4=(A)=>BQ0(iaA({},"__esModule",{value:!0}),A),QQ0={};p$1.exports=gA4(QQ0);naA(QQ0,paA(),p$1.exports);naA(QQ0,AQ0(),p$1.exports)});
var AQ0=E((GL)=>{var RA4=GL&&GL.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),OA4=GL&&GL.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),TA4=GL&&GL.__importStar||function(){var A=function(B){return A=Object.getOwnPropertyNames||function(Q){var D=[];for(var Z in Q)if(Object.prototype.hasOwnProperty.call(Q,Z))D[D.length]=Z;return D},A(B)};return function(B){if(B&&B.__esModule)return B;var Q={};if(B!=null){for(var D=A(B),Z=0;Z<D.length;Z++)if(D[Z]!=="default")RA4(Q,B,D[Z])}return OA4(Q,B),Q}}();Object.defineProperty(GL,"__esModule",{value:!0});GL.fromWebToken=void 0;var PA4=(A)=>async(B)=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromWebToken");let{roleArn:Q,roleSessionName:D,webIdentityToken:Z,providerId:G,policyArns:F,policy:I,durationSeconds:Y}=A,{roleAssumerWithWebIdentity:W}=A;if(!W){let{getDefaultRoleAssumerWithWebIdentity:J}=await Promise.resolve().then(()=>TA4(T$1()));W=J({...A.clientConfig,credentialProviderLogger:A.logger,parentClientConfig:{...B?.callerClientConfig,...A.parentClientConfig}},A.clientPlugins)}return W({RoleArn:Q,RoleSessionName:D??`aws-sdk-js-session-${Date.now()}`,WebIdentityToken:Z,ProviderId:G,PolicyArns:F,Policy:I,DurationSeconds:Y})};GL.fromWebToken=PA4});
var AdA=E((tmA)=>{Object.defineProperty(tmA,"__esModule",{value:!0});tmA.fromHttp=void 0;var vnQ=ph(),bnQ=Mz(),fnQ=S3(),rmA=eB(),hnQ=vnQ.__importDefault(J1("fs/promises")),gnQ=NmA(),omA=imA(),unQ=smA(),mnQ="AWS_CONTAINER_CREDENTIALS_RELATIVE_URI",dnQ="http://*************",cnQ="AWS_CONTAINER_CREDENTIALS_FULL_URI",lnQ="AWS_CONTAINER_AUTHORIZATION_TOKEN_FILE",pnQ="AWS_CONTAINER_AUTHORIZATION_TOKEN",inQ=(A={})=>{A.logger?.debug("@aws-sdk/credential-provider-http - fromHttp");let B,Q=A.awsContainerCredentialsRelativeUri??process.env[mnQ],D=A.awsContainerCredentialsFullUri??process.env[cnQ],Z=A.awsContainerAuthorizationToken??process.env[pnQ],G=A.awsContainerAuthorizationTokenFile??process.env[lnQ],F=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console.warn:A.logger.warn;if(Q&&D)F("@aws-sdk/credential-provider-http: you have set both awsContainerCredentialsRelativeUri and awsContainerCredentialsFullUri."),F("awsContainerCredentialsFullUri will take precedence.");if(Z&&G)F("@aws-sdk/credential-provider-http: you have set both awsContainerAuthorizationToken and awsContainerAuthorizationTokenFile."),F("awsContainerAuthorizationToken will take precedence.");if(D)B=D;else if(Q)B=`${dnQ}${Q}`;else throw new rmA.CredentialsProviderError(`No HTTP credential provider host provided.
Set AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI.`,{logger:A.logger});let I=new URL(B);gnQ.checkUrl(I,A.logger);let Y=new fnQ.NodeHttpHandler({requestTimeout:A.timeout??1000,connectionTimeout:A.timeout??1000});return unQ.retryWrapper(async()=>{let W=omA.createGetRequest(I);if(Z)W.headers.Authorization=Z;else if(G)W.headers.Authorization=(await hnQ.default.readFile(G)).toString();try{let J=await Y.handle(W);return omA.getCredentials(J.response).then((X)=>bnQ.setCredentialFeature(X,"CREDENTIALS_HTTP","z"))}catch(J){throw new rmA.CredentialsProviderError(String(J),{logger:A.logger})}},A.maxRetries??3,A.timeout??1000)};tmA.fromHttp=inQ});
var AgA=E((thA)=>{Object.defineProperty(thA,"__esModule",{value:!0});thA.fromTokenFile=void 0;var EcQ=Lw(),UcQ=eB(),wcQ=J1("fs"),$cQ=c20(),ohA="AWS_WEB_IDENTITY_TOKEN_FILE",qcQ="AWS_ROLE_ARN",NcQ="AWS_ROLE_SESSION_NAME",LcQ=(A={})=>async()=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromTokenFile");let B=A?.webIdentityTokenFile??process.env[ohA],Q=A?.roleArn??process.env[qcQ],D=A?.roleSessionName??process.env[NcQ];if(!B||!Q)throw new UcQ.CredentialsProviderError("Web identity configuration not specified",{logger:A.logger});let Z=await $cQ.fromWebToken({...A,webIdentityToken:wcQ.readFileSync(B,{encoding:"ascii"}),roleArn:Q,roleSessionName:D})();if(B===process.env[ohA])EcQ.setCredentialFeature(Z,"CREDENTIALS_ENV_VARS_STS_WEB_ID_TOKEN","h");return Z};thA.fromTokenFile=LcQ});
var AhA=E((tfA)=>{Object.defineProperty(tfA,"__esModule",{value:!0});tfA.getRuntimeConfig=void 0;var kmQ=ih(),ymQ=kmQ.__importDefault(lA0()),F20=II(),sfA=q41(),jw1=V4(),_mQ=VB(),xmQ=jG(),rfA=v4(),eh=QD(),ofA=S3(),vmQ=kG(),bmQ=hZ(),fmQ=afA(),hmQ=W6(),gmQ=yG(),umQ=W6(),mmQ=(A)=>{umQ.emitWarningIfUnsupportedVersion(process.version);let B=gmQ.resolveDefaultsModeConfig(A),Q=()=>B().then(hmQ.loadConfigsForDefaultMode),D=fmQ.getRuntimeConfig(A);F20.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??eh.loadConfig(F20.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??vmQ.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??sfA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:ymQ.default.version}),httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(G)=>G.getIdentityProvider("aws.auth#sigv4")||(async(F)=>await A.credentialDefaultProvider(F?.__config||{})()),signer:new F20.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(G)=>G.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new _mQ.NoAuthSigner}],maxAttempts:A?.maxAttempts??eh.loadConfig(rfA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??eh.loadConfig(jw1.NODE_REGION_CONFIG_OPTIONS,{...jw1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:ofA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??eh.loadConfig({...rfA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||bmQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??xmQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??ofA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??eh.loadConfig(jw1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??eh.loadConfig(jw1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??eh.loadConfig(sfA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};tfA.getRuntimeConfig=mmQ});
var ApA=E((tlA)=>{Object.defineProperty(tlA,"__esModule",{value:!0});tlA.getRuntimeConfig=void 0;var joQ=XV(),koQ=VB(),yoQ=H8(),_oQ=BZ(),rlA=Dg(),olA=cB(),xoQ=sB0(),voQ=slA(),boQ=(A)=>{return{apiVersion:"2011-06-15",base64Decoder:A?.base64Decoder??rlA.fromBase64,base64Encoder:A?.base64Encoder??rlA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??voQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??xoQ.defaultSTSHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new joQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new koQ.NoAuthSigner}],logger:A?.logger??new yoQ.NoOpLogger,serviceId:A?.serviceId??"STS",urlParser:A?.urlParser??_oQ.parseUrl,utf8Decoder:A?.utf8Decoder??olA.fromUtf8,utf8Encoder:A?.utf8Encoder??olA.toUtf8}};tlA.getRuntimeConfig=boQ});
var AvA=E((txA)=>{Object.defineProperty(txA,"__esModule",{value:!0});txA.getRuntimeConfig=void 0;var ChQ=II(),KhQ=VB(),HhQ=W6(),zhQ=BZ(),rxA=Ry(),oxA=cB(),EhQ=vA0(),UhQ=sxA(),whQ=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??rxA.fromBase64,base64Encoder:A?.base64Encoder??rxA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??UhQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??EhQ.defaultSSOHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new ChQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new KhQ.NoAuthSigner}],logger:A?.logger??new HhQ.NoOpLogger,serviceId:A?.serviceId??"SSO",urlParser:A?.urlParser??zhQ.parseUrl,utf8Decoder:A?.utf8Decoder??oxA.fromUtf8,utf8Encoder:A?.utf8Encoder??oxA.toUtf8}};txA.getRuntimeConfig=whQ});
var B20=E((VfA)=>{Object.defineProperty(VfA,"__esModule",{value:!0});VfA.resolveHttpAuthSchemeConfig=VfA.resolveStsAuthConfig=VfA.defaultSTSHttpAuthSchemeProvider=VfA.defaultSTSHttpAuthSchemeParametersProvider=void 0;var ImQ=II(),A20=I5(),YmQ=j41(),WmQ=async(A,B,Q)=>{return{operation:A20.getSmithyContext(B).operation,region:await A20.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};VfA.defaultSTSHttpAuthSchemeParametersProvider=WmQ;function JmQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function XmQ(A){return{schemeId:"smithy.api#noAuth"}}var VmQ=(A)=>{let B=[];switch(A.operation){case"AssumeRoleWithWebIdentity":{B.push(XmQ(A));break}default:B.push(JmQ(A))}return B};VfA.defaultSTSHttpAuthSchemeProvider=VmQ;var CmQ=(A)=>Object.assign(A,{stsClientCtor:YmQ.STSClient});VfA.resolveStsAuthConfig=CmQ;var KmQ=(A)=>{let B=VfA.resolveStsAuthConfig(A),Q=ImQ.resolveAwsSdkSigV4Config(B);return Object.assign(Q,{authSchemePreference:A20.normalizeProvider(A.authSchemePreference??[])})};VfA.resolveHttpAuthSchemeConfig=KmQ});
var C41=E((PG5,KkA)=>{var{defineProperty:gU1,getOwnPropertyDescriptor:$_Q,getOwnPropertyNames:q_Q}=Object,N_Q=Object.prototype.hasOwnProperty,hU1=(A,B)=>gU1(A,"name",{value:B,configurable:!0}),L_Q=(A,B)=>{for(var Q in B)gU1(A,Q,{get:B[Q],enumerable:!0})},M_Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of q_Q(B))if(!N_Q.call(A,Z)&&Z!==Q)gU1(A,Z,{get:()=>B[Z],enumerable:!(D=$_Q(B,Z))||D.enumerable})}return A},R_Q=(A)=>M_Q(gU1({},"__esModule",{value:!0}),A),JkA={};L_Q(JkA,{getHostHeaderPlugin:()=>T_Q,hostHeaderMiddleware:()=>VkA,hostHeaderMiddlewareOptions:()=>CkA,resolveHostHeaderConfig:()=>XkA});KkA.exports=R_Q(JkA);var O_Q=JV();function XkA(A){return A}hU1(XkA,"resolveHostHeaderConfig");var VkA=hU1((A)=>(B)=>async(Q)=>{if(!O_Q.HttpRequest.isInstance(Q.request))return B(Q);let{request:D}=Q,{handlerProtocol:Z=""}=A.requestHandler.metadata||{};if(Z.indexOf("h2")>=0&&!D.headers[":authority"])delete D.headers.host,D.headers[":authority"]=D.hostname+(D.port?":"+D.port:"");else if(!D.headers.host){let G=D.hostname;if(D.port!=null)G+=`:${D.port}`;D.headers.host=G}return B(Q)},"hostHeaderMiddleware"),CkA={name:"hostHeaderMiddleware",step:"build",priority:"low",tags:["HOST"],override:!0},T_Q=hU1((A)=>({applyToStack:hU1((B)=>{B.add(VkA(A),CkA)},"applyToStack")}),"getHostHeaderPlugin")});
var D$1=E((qY5,MdA)=>{var{defineProperty:Q$1,getOwnPropertyDescriptor:NaQ,getOwnPropertyNames:LaQ}=Object,MaQ=Object.prototype.hasOwnProperty,B$1=(A,B)=>Q$1(A,"name",{value:B,configurable:!0}),RaQ=(A,B)=>{for(var Q in B)Q$1(A,Q,{get:B[Q],enumerable:!0})},OaQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of LaQ(B))if(!MaQ.call(A,Z)&&Z!==Q)Q$1(A,Z,{get:()=>B[Z],enumerable:!(D=NaQ(B,Z))||D.enumerable})}return A},TaQ=(A)=>OaQ(Q$1({},"__esModule",{value:!0}),A),qdA={};RaQ(qdA,{addRecursionDetectionMiddlewareOptions:()=>LdA,getRecursionDetectionPlugin:()=>kaQ,recursionDetectionMiddleware:()=>NdA});MdA.exports=TaQ(qdA);var PaQ=DK(),wB0="X-Amzn-Trace-Id",SaQ="AWS_LAMBDA_FUNCTION_NAME",jaQ="_X_AMZN_TRACE_ID",NdA=B$1((A)=>(B)=>async(Q)=>{let{request:D}=Q;if(!PaQ.HttpRequest.isInstance(D)||A.runtime!=="node")return B(Q);let Z=Object.keys(D.headers??{}).find((Y)=>Y.toLowerCase()===wB0.toLowerCase())??wB0;if(D.headers.hasOwnProperty(Z))return B(Q);let G=process.env[SaQ],F=process.env[jaQ],I=B$1((Y)=>typeof Y==="string"&&Y.length>0,"nonEmptyString");if(I(G)&&I(F))D.headers[wB0]=F;return B({...Q,request:D})},"recursionDetectionMiddleware"),LdA={step:"build",tags:["RECURSION_DETECTION"],name:"recursionDetectionMiddleware",override:!0,priority:"low"},kaQ=B$1((A)=>({applyToStack:B$1((B)=>{B.add(NdA(A),LdA)},"applyToStack")}),"getRecursionDetectionPlugin")});
var DK=E((DY5,bmA)=>{var{defineProperty:pw1,getOwnPropertyDescriptor:hiQ,getOwnPropertyNames:giQ}=Object,uiQ=Object.prototype.hasOwnProperty,yy=(A,B)=>pw1(A,"name",{value:B,configurable:!0}),miQ=(A,B)=>{for(var Q in B)pw1(A,Q,{get:B[Q],enumerable:!0})},diQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of giQ(B))if(!uiQ.call(A,Z)&&Z!==Q)pw1(A,Z,{get:()=>B[Z],enumerable:!(D=hiQ(B,Z))||D.enumerable})}return A},ciQ=(A)=>diQ(pw1({},"__esModule",{value:!0}),A),ymA={};miQ(ymA,{Field:()=>iiQ,Fields:()=>niQ,HttpRequest:()=>aiQ,HttpResponse:()=>siQ,IHttpRequest:()=>_mA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>liQ,isValidHostname:()=>vmA,resolveHttpHandlerRuntimeConfig:()=>piQ});bmA.exports=ciQ(ymA);var liQ=yy((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),piQ=yy((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),_mA=YB0(),iiQ=class{static{yy(this,"Field")}constructor({name:A,kind:B=_mA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},niQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{yy(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},aiQ=class A{static{yy(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=xmA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function xmA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}yy(xmA,"cloneQuery");var siQ=class{static{yy(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function vmA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}yy(vmA,"isValidHostname")});
var Da=E((zF5,s_A)=>{var{defineProperty:Jw1,getOwnPropertyDescriptor:hbQ,getOwnPropertyNames:gbQ}=Object,ubQ=Object.prototype.hasOwnProperty,ZT=(A,B)=>Jw1(A,"name",{value:B,configurable:!0}),mbQ=(A,B)=>{for(var Q in B)Jw1(A,Q,{get:B[Q],enumerable:!0})},dbQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of gbQ(B))if(!ubQ.call(A,Z)&&Z!==Q)Jw1(A,Z,{get:()=>B[Z],enumerable:!(D=hbQ(B,Z))||D.enumerable})}return A},cbQ=(A)=>dbQ(Jw1({},"__esModule",{value:!0}),A),m_A={};mbQ(m_A,{DEFAULT_UA_APP_ID:()=>d_A,getUserAgentMiddlewareOptions:()=>a_A,getUserAgentPlugin:()=>obQ,resolveUserAgentConfig:()=>l_A,userAgentMiddleware:()=>n_A});s_A.exports=cbQ(m_A);var lbQ=VB(),d_A=void 0;function c_A(A){if(A===void 0)return!0;return typeof A==="string"&&A.length<=50}ZT(c_A,"isValidUserAgentAppId");function l_A(A){let B=lbQ.normalizeProvider(A.userAgentAppId??d_A),{customUserAgent:Q}=A;return Object.assign(A,{customUserAgent:typeof Q==="string"?[[Q]]:Q,userAgentAppId:ZT(async()=>{let D=await B();if(!c_A(D)){let Z=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console:A.logger;if(typeof D!=="string")Z?.warn("userAgentAppId must be a string or undefined.");else if(D.length>50)Z?.warn("The provided userAgentAppId exceeds the maximum length of 50 characters.")}return D},"userAgentAppId")})}ZT(l_A,"resolveUserAgentConfig");var pbQ=on(),ibQ=JV(),rN=II(),nbQ=/\d{12}\.ddb/;async function p_A(A,B,Q){if(Q.request?.headers?.["smithy-protocol"]==="rpc-v2-cbor")rN.setFeature(A,"PROTOCOL_RPC_V2_CBOR","M");if(typeof B.retryStrategy==="function"){let G=await B.retryStrategy();if(typeof G.acquireInitialRetryToken==="function")if(G.constructor?.name?.includes("Adaptive"))rN.setFeature(A,"RETRY_MODE_ADAPTIVE","F");else rN.setFeature(A,"RETRY_MODE_STANDARD","E");else rN.setFeature(A,"RETRY_MODE_LEGACY","D")}if(typeof B.accountIdEndpointMode==="function"){let G=A.endpointV2;if(String(G?.url?.hostname).match(nbQ))rN.setFeature(A,"ACCOUNT_ID_ENDPOINT","O");switch(await B.accountIdEndpointMode?.()){case"disabled":rN.setFeature(A,"ACCOUNT_ID_MODE_DISABLED","Q");break;case"preferred":rN.setFeature(A,"ACCOUNT_ID_MODE_PREFERRED","P");break;case"required":rN.setFeature(A,"ACCOUNT_ID_MODE_REQUIRED","R");break}}let Z=A.__smithy_context?.selectedHttpAuthScheme?.identity;if(Z?.$source){let G=Z;if(G.accountId)rN.setFeature(A,"RESOLVED_ACCOUNT_ID","T");for(let[F,I]of Object.entries(G.$source??{}))rN.setFeature(A,F,I)}}ZT(p_A,"checkFeatures");var h_A="user-agent",OA0="x-amz-user-agent",g_A=" ",TA0="/",abQ=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g,sbQ=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g,u_A="-",rbQ=1024;function i_A(A){let B="";for(let Q in A){let D=A[Q];if(B.length+D.length+1<=rbQ){if(B.length)B+=","+D;else B+=D;continue}break}return B}ZT(i_A,"encodeFeatures");var n_A=ZT((A)=>(B,Q)=>async(D)=>{let{request:Z}=D;if(!ibQ.HttpRequest.isInstance(Z))return B(D);let{headers:G}=Z,F=Q?.userAgent?.map(Ww1)||[],I=(await A.defaultUserAgentProvider()).map(Ww1);await p_A(Q,A,D);let Y=Q;I.push(`m/${i_A(Object.assign({},Q.__smithy_context?.features,Y.__aws_sdk_context?.features))}`);let W=A?.customUserAgent?.map(Ww1)||[],J=await A.userAgentAppId();if(J)I.push(Ww1([`app/${J}`]));let X=pbQ.getUserAgentPrefix(),V=(X?[X]:[]).concat([...I,...F,...W]).join(g_A),C=[...I.filter((K)=>K.startsWith("aws-sdk-")),...W].join(g_A);if(A.runtime!=="browser"){if(C)G[OA0]=G[OA0]?`${G[h_A]} ${C}`:C;G[h_A]=V}else G[OA0]=V;return B({...D,request:Z})},"userAgentMiddleware"),Ww1=ZT((A)=>{let B=A[0].split(TA0).map((F)=>F.replace(abQ,u_A)).join(TA0),Q=A[1]?.replace(sbQ,u_A),D=B.indexOf(TA0),Z=B.substring(0,D),G=B.substring(D+1);if(Z==="api")G=G.toLowerCase();return[Z,G,Q].filter((F)=>F&&F.length>0).reduce((F,I,Y)=>{switch(Y){case 0:return I;case 1:return`${F}/${I}`;default:return`${F}#${I}`}},"")},"escapeUserAgent"),a_A={name:"getUserAgentMiddleware",step:"build",priority:"low",tags:["SET_USER_AGENT","USER_AGENT"],override:!0},obQ=ZT((A)=>({applyToStack:ZT((B)=>{B.add(n_A(A),a_A)},"applyToStack")}),"getUserAgentPlugin")});
var Dg=E((xY5,H$1)=>{var{defineProperty:vcA,getOwnPropertyDescriptor:rsQ,getOwnPropertyNames:osQ}=Object,tsQ=Object.prototype.hasOwnProperty,xB0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of osQ(B))if(!tsQ.call(A,Z)&&Z!==Q)vcA(A,Z,{get:()=>B[Z],enumerable:!(D=rsQ(B,Z))||D.enumerable})}return A},bcA=(A,B,Q)=>(xB0(A,B,"default"),Q&&xB0(Q,B,"default")),esQ=(A)=>xB0(vcA({},"__esModule",{value:!0}),A),vB0={};H$1.exports=esQ(vB0);bcA(vB0,kcA(),H$1.exports);bcA(vB0,xcA(),H$1.exports)});
var DhA=E((BhA)=>{Object.defineProperty(BhA,"__esModule",{value:!0});BhA.resolveHttpAuthRuntimeConfig=BhA.getHttpAuthExtensionConfiguration=void 0;var dmQ=(A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}};BhA.getHttpAuthExtensionConfiguration=dmQ;var cmQ=(A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}};BhA.resolveHttpAuthRuntimeConfig=cmQ});
var DsA=E((BsA)=>{Object.defineProperty(BsA,"__esModule",{value:!0});BsA.fromIni=void 0;var G24=GQ0(),F24=(A={})=>G24.fromIni({...A});BsA.fromIni=F24});
var EnA=E((MW5,znA)=>{var{defineProperty:y$1,getOwnPropertyDescriptor:F14,getOwnPropertyNames:I14}=Object,Y14=Object.prototype.hasOwnProperty,M6=(A,B)=>y$1(A,"name",{value:B,configurable:!0}),W14=(A,B)=>{for(var Q in B)y$1(A,Q,{get:B[Q],enumerable:!0})},J14=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of I14(B))if(!Y14.call(A,Z)&&Z!==Q)y$1(A,Z,{get:()=>B[Z],enumerable:!(D=F14(B,Z))||D.enumerable})}return A},X14=(A)=>J14(y$1({},"__esModule",{value:!0}),A),riA={};W14(riA,{GetRoleCredentialsCommand:()=>CnA,GetRoleCredentialsRequestFilterSensitiveLog:()=>BnA,GetRoleCredentialsResponseFilterSensitiveLog:()=>DnA,InvalidRequestException:()=>oiA,ListAccountRolesCommand:()=>d90,ListAccountRolesRequestFilterSensitiveLog:()=>ZnA,ListAccountsCommand:()=>c90,ListAccountsRequestFilterSensitiveLog:()=>GnA,LogoutCommand:()=>KnA,LogoutRequestFilterSensitiveLog:()=>FnA,ResourceNotFoundException:()=>tiA,RoleCredentialsFilterSensitiveLog:()=>QnA,SSO:()=>HnA,SSOClient:()=>x$1,SSOServiceException:()=>ma,TooManyRequestsException:()=>eiA,UnauthorizedException:()=>AnA,__Client:()=>OB.Client,paginateListAccountRoles:()=>f14,paginateListAccounts:()=>h14});znA.exports=X14(riA);var liA=tw1(),V14=A$1(),C14=D$1(),piA=l41(),K14=V4(),UT=VB(),H14=TG(),s41=q6(),iiA=v4(),niA=h90(),z14=M6((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"awsssoportal"})},"resolveClientEndpointParameters"),_$1={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},E14=ciA(),aiA=R$1(),siA=DK(),OB=H8(),U14=M6((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),w14=M6((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),$14=M6((A,B)=>{let Q=Object.assign(aiA.getAwsRegionExtensionConfiguration(A),OB.getDefaultExtensionConfiguration(A),siA.getHttpHandlerExtensionConfiguration(A),U14(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,aiA.resolveAwsRegionExtensionConfiguration(Q),OB.resolveDefaultRuntimeConfig(Q),siA.resolveHttpHandlerRuntimeConfig(Q),w14(Q))},"resolveRuntimeExtensions"),x$1=class extends OB.Client{static{M6(this,"SSOClient")}config;constructor(...[A]){let B=E14.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=z14(B),D=piA.resolveUserAgentConfig(Q),Z=iiA.resolveRetryConfig(D),G=K14.resolveRegionConfig(Z),F=liA.resolveHostHeaderConfig(G),I=s41.resolveEndpointConfig(F),Y=niA.resolveHttpAuthSchemeConfig(I),W=$14(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(piA.getUserAgentPlugin(this.config)),this.middlewareStack.use(iiA.getRetryPlugin(this.config)),this.middlewareStack.use(H14.getContentLengthPlugin(this.config)),this.middlewareStack.use(liA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(V14.getLoggerPlugin(this.config)),this.middlewareStack.use(C14.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(UT.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:niA.defaultSSOHttpAuthSchemeParametersProvider,identityProviderConfigProvider:M6(async(J)=>new UT.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(UT.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},v$1=T3(),ma=class A extends OB.ServiceException{static{M6(this,"SSOServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},oiA=class A extends ma{static{M6(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},tiA=class A extends ma{static{M6(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},eiA=class A extends ma{static{M6(this,"TooManyRequestsException")}name="TooManyRequestsException";$fault="client";constructor(B){super({name:"TooManyRequestsException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},AnA=class A extends ma{static{M6(this,"UnauthorizedException")}name="UnauthorizedException";$fault="client";constructor(B){super({name:"UnauthorizedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},BnA=M6((A)=>({...A,...A.accessToken&&{accessToken:OB.SENSITIVE_STRING}}),"GetRoleCredentialsRequestFilterSensitiveLog"),QnA=M6((A)=>({...A,...A.secretAccessKey&&{secretAccessKey:OB.SENSITIVE_STRING},...A.sessionToken&&{sessionToken:OB.SENSITIVE_STRING}}),"RoleCredentialsFilterSensitiveLog"),DnA=M6((A)=>({...A,...A.roleCredentials&&{roleCredentials:QnA(A.roleCredentials)}}),"GetRoleCredentialsResponseFilterSensitiveLog"),ZnA=M6((A)=>({...A,...A.accessToken&&{accessToken:OB.SENSITIVE_STRING}}),"ListAccountRolesRequestFilterSensitiveLog"),GnA=M6((A)=>({...A,...A.accessToken&&{accessToken:OB.SENSITIVE_STRING}}),"ListAccountsRequestFilterSensitiveLog"),FnA=M6((A)=>({...A,...A.accessToken&&{accessToken:OB.SENSITIVE_STRING}}),"LogoutRequestFilterSensitiveLog"),a41=XV(),q14=M6(async(A,B)=>{let Q=UT.requestBuilder(A,B),D=OB.map({},OB.isSerializableHeaderValue,{[h$1]:A[f$1]});Q.bp("/federation/credentials");let Z=OB.map({[v14]:[,OB.expectNonNull(A[x14],"roleName")],[YnA]:[,OB.expectNonNull(A[InA],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_GetRoleCredentialsCommand"),N14=M6(async(A,B)=>{let Q=UT.requestBuilder(A,B),D=OB.map({},OB.isSerializableHeaderValue,{[h$1]:A[f$1]});Q.bp("/assignment/roles");let Z=OB.map({[VnA]:[,A[XnA]],[JnA]:[()=>A.maxResults!==void 0,()=>A[WnA].toString()],[YnA]:[,OB.expectNonNull(A[InA],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountRolesCommand"),L14=M6(async(A,B)=>{let Q=UT.requestBuilder(A,B),D=OB.map({},OB.isSerializableHeaderValue,{[h$1]:A[f$1]});Q.bp("/assignment/accounts");let Z=OB.map({[VnA]:[,A[XnA]],[JnA]:[()=>A.maxResults!==void 0,()=>A[WnA].toString()]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountsCommand"),M14=M6(async(A,B)=>{let Q=UT.requestBuilder(A,B),D=OB.map({},OB.isSerializableHeaderValue,{[h$1]:A[f$1]});Q.bp("/logout");let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_LogoutCommand"),R14=M6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return b$1(A,B);let Q=OB.map({$metadata:fy(A)}),D=OB.expectNonNull(OB.expectObject(await a41.parseJsonBody(A.body,B)),"body"),Z=OB.take(D,{roleCredentials:OB._json});return Object.assign(Q,Z),Q},"de_GetRoleCredentialsCommand"),O14=M6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return b$1(A,B);let Q=OB.map({$metadata:fy(A)}),D=OB.expectNonNull(OB.expectObject(await a41.parseJsonBody(A.body,B)),"body"),Z=OB.take(D,{nextToken:OB.expectString,roleList:OB._json});return Object.assign(Q,Z),Q},"de_ListAccountRolesCommand"),T14=M6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return b$1(A,B);let Q=OB.map({$metadata:fy(A)}),D=OB.expectNonNull(OB.expectObject(await a41.parseJsonBody(A.body,B)),"body"),Z=OB.take(D,{accountList:OB._json,nextToken:OB.expectString});return Object.assign(Q,Z),Q},"de_ListAccountsCommand"),P14=M6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return b$1(A,B);let Q=OB.map({$metadata:fy(A)});return await OB.collectBody(A.body,B),Q},"de_LogoutCommand"),b$1=M6(async(A,B)=>{let Q={...A,body:await a41.parseJsonErrorBody(A.body,B)},D=a41.loadRestJsonErrorCode(A,Q.body);switch(D){case"InvalidRequestException":case"com.amazonaws.sso#InvalidRequestException":throw await j14(Q,B);case"ResourceNotFoundException":case"com.amazonaws.sso#ResourceNotFoundException":throw await k14(Q,B);case"TooManyRequestsException":case"com.amazonaws.sso#TooManyRequestsException":throw await y14(Q,B);case"UnauthorizedException":case"com.amazonaws.sso#UnauthorizedException":throw await _14(Q,B);default:let Z=Q.body;return S14({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),S14=OB.withBaseException(ma),j14=M6(async(A,B)=>{let Q=OB.map({}),D=A.body,Z=OB.take(D,{message:OB.expectString});Object.assign(Q,Z);let G=new oiA({$metadata:fy(A),...Q});return OB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),k14=M6(async(A,B)=>{let Q=OB.map({}),D=A.body,Z=OB.take(D,{message:OB.expectString});Object.assign(Q,Z);let G=new tiA({$metadata:fy(A),...Q});return OB.decorateServiceException(G,A.body)},"de_ResourceNotFoundExceptionRes"),y14=M6(async(A,B)=>{let Q=OB.map({}),D=A.body,Z=OB.take(D,{message:OB.expectString});Object.assign(Q,Z);let G=new eiA({$metadata:fy(A),...Q});return OB.decorateServiceException(G,A.body)},"de_TooManyRequestsExceptionRes"),_14=M6(async(A,B)=>{let Q=OB.map({}),D=A.body,Z=OB.take(D,{message:OB.expectString});Object.assign(Q,Z);let G=new AnA({$metadata:fy(A),...Q});return OB.decorateServiceException(G,A.body)},"de_UnauthorizedExceptionRes"),fy=M6((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),InA="accountId",f$1="accessToken",YnA="account_id",WnA="maxResults",JnA="max_result",XnA="nextToken",VnA="next_token",x14="roleName",v14="role_name",h$1="x-amz-sso_bearer_token",CnA=class extends OB.Command.classBuilder().ep(_$1).m(function(A,B,Q,D){return[v$1.getSerdePlugin(Q,this.serialize,this.deserialize),s41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","GetRoleCredentials",{}).n("SSOClient","GetRoleCredentialsCommand").f(BnA,DnA).ser(q14).de(R14).build(){static{M6(this,"GetRoleCredentialsCommand")}},d90=class extends OB.Command.classBuilder().ep(_$1).m(function(A,B,Q,D){return[v$1.getSerdePlugin(Q,this.serialize,this.deserialize),s41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccountRoles",{}).n("SSOClient","ListAccountRolesCommand").f(ZnA,void 0).ser(N14).de(O14).build(){static{M6(this,"ListAccountRolesCommand")}},c90=class extends OB.Command.classBuilder().ep(_$1).m(function(A,B,Q,D){return[v$1.getSerdePlugin(Q,this.serialize,this.deserialize),s41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccounts",{}).n("SSOClient","ListAccountsCommand").f(GnA,void 0).ser(L14).de(T14).build(){static{M6(this,"ListAccountsCommand")}},KnA=class extends OB.Command.classBuilder().ep(_$1).m(function(A,B,Q,D){return[v$1.getSerdePlugin(Q,this.serialize,this.deserialize),s41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","Logout",{}).n("SSOClient","LogoutCommand").f(FnA,void 0).ser(M14).de(P14).build(){static{M6(this,"LogoutCommand")}},b14={GetRoleCredentialsCommand:CnA,ListAccountRolesCommand:d90,ListAccountsCommand:c90,LogoutCommand:KnA},HnA=class extends x$1{static{M6(this,"SSO")}};OB.createAggregatedClient(b14,HnA);var f14=UT.createPaginator(x$1,d90,"nextToken","nextToken","maxResults"),h14=UT.createPaginator(x$1,c90,"nextToken","nextToken","maxResults")});
var FA0=E((uG5,G_A)=>{var{defineProperty:Zw1,getOwnPropertyDescriptor:ixQ,getOwnPropertyNames:nxQ}=Object,axQ=Object.prototype.hasOwnProperty,uZ=(A,B)=>Zw1(A,"name",{value:B,configurable:!0}),sxQ=(A,B)=>{for(var Q in B)Zw1(A,Q,{get:B[Q],enumerable:!0})},rxQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of nxQ(B))if(!axQ.call(A,Z)&&Z!==Q)Zw1(A,Z,{get:()=>B[Z],enumerable:!(D=ixQ(B,Z))||D.enumerable})}return A},oxQ=(A)=>rxQ(Zw1({},"__esModule",{value:!0}),A),A_A={};sxQ(A_A,{AWSSDKSigV4Signer:()=>BvQ,AwsSdkSigV4ASigner:()=>DvQ,AwsSdkSigV4Signer:()=>GA0,NODE_AUTH_SCHEME_PREFERENCE_OPTIONS:()=>ZvQ,NODE_SIGV4A_CONFIG_OPTIONS:()=>IvQ,getBearerTokenEnvKey:()=>B_A,resolveAWSSDKSigV4Config:()=>WvQ,resolveAwsSdkSigV4AConfig:()=>FvQ,resolveAwsSdkSigV4Config:()=>Q_A,validateSigningProperties:()=>ZA0});G_A.exports=oxQ(A_A);var txQ=JV(),exQ=JV(),nyA=uZ((A)=>exQ.HttpResponse.isInstance(A)?A.headers?.date??A.headers?.Date:void 0,"getDateHeader"),DA0=uZ((A)=>new Date(Date.now()+A),"getSkewCorrectedDate"),AvQ=uZ((A,B)=>Math.abs(DA0(B).getTime()-A)>=300000,"isClockSkewed"),ayA=uZ((A,B)=>{let Q=Date.parse(A);if(AvQ(Q,B))return Q-Date.now();return B},"getUpdatedSystemClockOffset"),z41=uZ((A,B)=>{if(!B)throw new Error(`Property \`${A}\` is not resolved for AWS SDK SigV4Auth`);return B},"throwSigningPropertyError"),ZA0=uZ(async(A)=>{let B=z41("context",A.context),Q=z41("config",A.config),D=B.endpointV2?.properties?.authSchemes?.[0],G=await z41("signer",Q.signer)(D),F=A?.signingRegion,I=A?.signingRegionSet,Y=A?.signingName;return{config:Q,signer:G,signingRegion:F,signingRegionSet:I,signingName:Y}},"validateSigningProperties"),GA0=class{static{uZ(this,"AwsSdkSigV4Signer")}async sign(A,B,Q){if(!txQ.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let D=await ZA0(Q),{config:Z,signer:G}=D,{signingRegion:F,signingName:I}=D,Y=Q.context;if(Y?.authSchemes?.length??0>1){let[J,X]=Y.authSchemes;if(J?.name==="sigv4a"&&X?.name==="sigv4")F=X?.signingRegion??F,I=X?.signingName??I}return await G.sign(A,{signingDate:DA0(Z.systemClockOffset),signingRegion:F,signingService:I})}errorHandler(A){return(B)=>{let Q=B.ServerTime??nyA(B.$response);if(Q){let D=z41("config",A.config),Z=D.systemClockOffset;if(D.systemClockOffset=ayA(Q,D.systemClockOffset),D.systemClockOffset!==Z&&B.$metadata)B.$metadata.clockSkewCorrected=!0}throw B}}successHandler(A,B){let Q=nyA(A);if(Q){let D=z41("config",B.config);D.systemClockOffset=ayA(Q,D.systemClockOffset)}}},BvQ=GA0,QvQ=JV(),DvQ=class extends GA0{static{uZ(this,"AwsSdkSigV4ASigner")}async sign(A,B,Q){if(!QvQ.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let{config:D,signer:Z,signingRegion:G,signingRegionSet:F,signingName:I}=await ZA0(Q),W=(await D.sigv4aSigningRegionSet?.()??F??[G]).join(",");return await Z.sign(A,{signingDate:DA0(D.systemClockOffset),signingRegion:W,signingService:I})}},syA=uZ((A)=>typeof A==="string"&&A.length>0?A.split(",").map((B)=>B.trim()):[],"getArrayForCommaSeparatedString"),B_A=uZ((A)=>`AWS_BEARER_TOKEN_${A.replace(/[\s-]/g,"_").toUpperCase()}`,"getBearerTokenEnvKey"),ryA="AWS_AUTH_SCHEME_PREFERENCE",oyA="auth_scheme_preference",ZvQ={environmentVariableSelector:uZ((A,B)=>{if(B?.signingName){if(B_A(B.signingName)in A)return["httpBearerAuth"]}if(!(ryA in A))return;return syA(A[ryA])},"environmentVariableSelector"),configFileSelector:uZ((A)=>{if(!(oyA in A))return;return syA(A[oyA])},"configFileSelector"),default:[]},GvQ=VB(),tyA=eB(),FvQ=uZ((A)=>{return A.sigv4aSigningRegionSet=GvQ.normalizeProvider(A.sigv4aSigningRegionSet),A},"resolveAwsSdkSigV4AConfig"),IvQ={environmentVariableSelector(A){if(A.AWS_SIGV4A_SIGNING_REGION_SET)return A.AWS_SIGV4A_SIGNING_REGION_SET.split(",").map((B)=>B.trim());throw new tyA.ProviderError("AWS_SIGV4A_SIGNING_REGION_SET not set in env.",{tryNextLink:!0})},configFileSelector(A){if(A.sigv4a_signing_region_set)return(A.sigv4a_signing_region_set??"").split(",").map((B)=>B.trim());throw new tyA.ProviderError("sigv4a_signing_region_set not set in profile.",{tryNextLink:!0})},default:void 0},YvQ=Lw(),nh=VB(),eyA=iyA(),Q_A=uZ((A)=>{let B=A.credentials,Q=!!A.credentials,D=void 0;Object.defineProperty(A,"credentials",{set(W){if(W&&W!==B&&W!==D)Q=!0;B=W;let J=D_A(A,{credentials:B,credentialDefaultProvider:A.credentialDefaultProvider}),X=Z_A(A,J);if(Q&&!X.attributed)D=uZ(async(V)=>X(V).then((C)=>YvQ.setCredentialFeature(C,"CREDENTIALS_CODE","e")),"resolvedCredentials"),D.memoized=X.memoized,D.configBound=X.configBound,D.attributed=!0;else D=X},get(){return D},enumerable:!0,configurable:!0}),A.credentials=B;let{signingEscapePath:Z=!0,systemClockOffset:G=A.systemClockOffset||0,sha256:F}=A,I;if(A.signer)I=nh.normalizeProvider(A.signer);else if(A.regionInfoProvider)I=uZ(()=>nh.normalizeProvider(A.region)().then(async(W)=>[await A.regionInfoProvider(W,{useFipsEndpoint:await A.useFipsEndpoint(),useDualstackEndpoint:await A.useDualstackEndpoint()})||{},W]).then(([W,J])=>{let{signingRegion:X,signingService:V}=W;A.signingRegion=A.signingRegion||X||J,A.signingName=A.signingName||V||A.serviceId;let C={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||eyA.SignatureV4)(C)}),"signer");else I=uZ(async(W)=>{W=Object.assign({},{name:"sigv4",signingName:A.signingName||A.defaultSigningName,signingRegion:await nh.normalizeProvider(A.region)(),properties:{}},W);let{signingRegion:J,signingName:X}=W;A.signingRegion=A.signingRegion||J,A.signingName=A.signingName||X||A.serviceId;let V={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||eyA.SignatureV4)(V)},"signer");return Object.assign(A,{systemClockOffset:G,signingEscapePath:Z,signer:I})},"resolveAwsSdkSigV4Config"),WvQ=Q_A;function D_A(A,{credentials:B,credentialDefaultProvider:Q}){let D;if(B)if(!B?.memoized)D=nh.memoizeIdentityProvider(B,nh.isIdentityExpired,nh.doesIdentityRequireRefresh);else D=B;else if(Q)D=nh.normalizeProvider(Q(Object.assign({},A,{parentClientConfig:A})));else D=uZ(async()=>{throw new Error("@aws-sdk/core::resolveAwsSdkSigV4Config - `credentials` not provided and no credentialDefaultProvider was configured.")},"credentialsProvider");return D.memoized=!0,D}uZ(D_A,"normalizeCredentialProvider");function Z_A(A,B){if(B.configBound)return B;let Q=uZ(async(D)=>B({...D,callerClientConfig:A}),"fn");return Q.memoized=B.memoized,Q.configBound=!0,Q}uZ(Z_A,"bindCallerConfig")});
var FB0=E((aI5,GmA)=>{var{defineProperty:gw1,getOwnPropertyDescriptor:BiQ,getOwnPropertyNames:suA}=Object,QiQ=Object.prototype.hasOwnProperty,QK=(A,B)=>gw1(A,"name",{value:B,configurable:!0}),DiQ=(A,B)=>function Q(){return A&&(B=A[suA(A)[0]](A=0)),B},ruA=(A,B)=>{for(var Q in B)gw1(A,Q,{get:B[Q],enumerable:!0})},ZiQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of suA(B))if(!QiQ.call(A,Z)&&Z!==Q)gw1(A,Z,{get:()=>B[Z],enumerable:!(D=BiQ(B,Z))||D.enumerable})}return A},GiQ=(A)=>ZiQ(gw1({},"__esModule",{value:!0}),A),DB0={};ruA(DB0,{CognitoIdentityClient:()=>hw1.CognitoIdentityClient,GetCredentialsForIdentityCommand:()=>hw1.GetCredentialsForIdentityCommand,GetIdCommand:()=>hw1.GetIdCommand});var hw1,ouA=DiQ({"src/loadCognitoIdentity.ts"(){hw1=auA()}}),tuA={};ruA(tuA,{fromCognitoIdentity:()=>GB0,fromCognitoIdentityPool:()=>DmA});GmA.exports=GiQ(tuA);var uw1=eB();function ZB0(A){return Promise.all(Object.keys(A).reduce((B,Q)=>{let D=A[Q];if(typeof D==="string")B.push([Q,D]);else B.push(D().then((Z)=>[Q,Z]));return B},[])).then((B)=>B.reduce((Q,[D,Z])=>{return Q[D]=Z,Q},{}))}QK(ZB0,"resolveLogins");function GB0(A){return async(B)=>{A.logger?.debug("@aws-sdk/credential-provider-cognito-identity - fromCognitoIdentity");let{GetCredentialsForIdentityCommand:Q,CognitoIdentityClient:D}=await Promise.resolve().then(()=>(ouA(),DB0)),Z=QK((W)=>A.clientConfig?.[W]??A.parentClientConfig?.[W]??B?.callerClientConfig?.[W],"fromConfigs"),{Credentials:{AccessKeyId:G=euA(A.logger),Expiration:F,SecretKey:I=BmA(A.logger),SessionToken:Y}=AmA(A.logger)}=await(A.client??new D(Object.assign({},A.clientConfig??{},{region:Z("region"),profile:Z("profile")}))).send(new Q({CustomRoleArn:A.customRoleArn,IdentityId:A.identityId,Logins:A.logins?await ZB0(A.logins):void 0}));return{identityId:A.identityId,accessKeyId:G,secretAccessKey:I,sessionToken:Y,expiration:F}}}QK(GB0,"fromCognitoIdentity");function euA(A){throw new uw1.CredentialsProviderError("Response from Amazon Cognito contained no access key ID",{logger:A})}QK(euA,"throwOnMissingAccessKeyId");function AmA(A){throw new uw1.CredentialsProviderError("Response from Amazon Cognito contained no credentials",{logger:A})}QK(AmA,"throwOnMissingCredentials");function BmA(A){throw new uw1.CredentialsProviderError("Response from Amazon Cognito contained no secret key",{logger:A})}QK(BmA,"throwOnMissingSecretKey");var QB0="IdentityIds",FiQ=class{constructor(A="aws:cognito-identity-ids"){this.dbName=A}static{QK(this,"IndexedDbStorage")}getItem(A){return this.withObjectStore("readonly",(B)=>{let Q=B.get(A);return new Promise((D)=>{Q.onerror=()=>D(null),Q.onsuccess=()=>D(Q.result?Q.result.value:null)})}).catch(()=>null)}removeItem(A){return this.withObjectStore("readwrite",(B)=>{let Q=B.delete(A);return new Promise((D,Z)=>{Q.onerror=()=>Z(Q.error),Q.onsuccess=()=>D()})})}setItem(A,B){return this.withObjectStore("readwrite",(Q)=>{let D=Q.put({id:A,value:B});return new Promise((Z,G)=>{D.onerror=()=>G(D.error),D.onsuccess=()=>Z()})})}getDb(){let A=self.indexedDB.open(this.dbName,1);return new Promise((B,Q)=>{A.onsuccess=()=>{B(A.result)},A.onerror=()=>{Q(A.error)},A.onblocked=()=>{Q(new Error("Unable to access DB"))},A.onupgradeneeded=()=>{let D=A.result;D.onerror=()=>{Q(new Error("Failed to create object store"))},D.createObjectStore(QB0,{keyPath:"id"})}})}withObjectStore(A,B){return this.getDb().then((Q)=>{let D=Q.transaction(QB0,A);return D.oncomplete=()=>Q.close(),new Promise((Z,G)=>{D.onerror=()=>G(D.error),Z(B(D.objectStore(QB0)))}).catch((Z)=>{throw Q.close(),Z})})}},IiQ=class{constructor(A={}){this.store=A}static{QK(this,"InMemoryStorage")}getItem(A){if(A in this.store)return this.store[A];return null}removeItem(A){delete this.store[A]}setItem(A,B){this.store[A]=B}},YiQ=new IiQ;function QmA(){if(typeof self==="object"&&self.indexedDB)return new FiQ;if(typeof window==="object"&&window.localStorage)return window.localStorage;return YiQ}QK(QmA,"localStorage");function DmA({accountId:A,cache:B=QmA(),client:Q,clientConfig:D,customRoleArn:Z,identityPoolId:G,logins:F,userIdentifier:I=!F||Object.keys(F).length===0?"ANONYMOUS":void 0,logger:Y,parentClientConfig:W}){Y?.debug("@aws-sdk/credential-provider-cognito-identity - fromCognitoIdentity");let J=I?`aws:cognito-identity-credentials:${G}:${I}`:void 0,X=QK(async(V)=>{let{GetIdCommand:C,CognitoIdentityClient:K}=await Promise.resolve().then(()=>(ouA(),DB0)),H=QK((L)=>D?.[L]??W?.[L]??V?.callerClientConfig?.[L],"fromConfigs"),z=Q??new K(Object.assign({},D??{},{region:H("region"),profile:H("profile")})),$=J&&await B.getItem(J);if(!$){let{IdentityId:L=ZmA(Y)}=await z.send(new C({AccountId:A,IdentityPoolId:G,Logins:F?await ZB0(F):void 0}));if($=L,J)Promise.resolve(B.setItem(J,$)).catch(()=>{})}return X=GB0({client:z,customRoleArn:Z,logins:F,identityId:$}),X(V)},"provider");return(V)=>X(V).catch(async(C)=>{if(J)Promise.resolve(B.removeItem(J)).catch(()=>{});throw C})}QK(DmA,"fromCognitoIdentityPool");function ZmA(A){throw new uw1.CredentialsProviderError("Response from Amazon Cognito contained no identity ID",{logger:A})}QK(ZmA,"throwOnMissingId")});
var FbA=E((ZbA)=>{Object.defineProperty(ZbA,"__esModule",{value:!0});ZbA.ruleSet=void 0;var AbA="required",qz="fn",Nz="argv",Ja="ref",lvA=!0,pvA="isSet",O41="booleanEquals",Ya="error",Wa="endpoint",IT="tree",pA0="PartitionResult",iA0="getAttr",ivA={[AbA]:!1,type:"String"},nvA={[AbA]:!0,default:!1,type:"Boolean"},avA={[Ja]:"Endpoint"},BbA={[qz]:O41,[Nz]:[{[Ja]:"UseFIPS"},!0]},QbA={[qz]:O41,[Nz]:[{[Ja]:"UseDualStack"},!0]},$z={},svA={[qz]:iA0,[Nz]:[{[Ja]:pA0},"supportsFIPS"]},DbA={[Ja]:pA0},rvA={[qz]:O41,[Nz]:[!0,{[qz]:iA0,[Nz]:[DbA,"supportsDualStack"]}]},ovA=[BbA],tvA=[QbA],evA=[{[Ja]:"Region"}],SgQ={version:"1.0",parameters:{Region:ivA,UseDualStack:nvA,UseFIPS:nvA,Endpoint:ivA},rules:[{conditions:[{[qz]:pvA,[Nz]:[avA]}],rules:[{conditions:ovA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:Ya},{conditions:tvA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:Ya},{endpoint:{url:avA,properties:$z,headers:$z},type:Wa}],type:IT},{conditions:[{[qz]:pvA,[Nz]:evA}],rules:[{conditions:[{[qz]:"aws.partition",[Nz]:evA,assign:pA0}],rules:[{conditions:[BbA,QbA],rules:[{conditions:[{[qz]:O41,[Nz]:[lvA,svA]},rvA],rules:[{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:$z,headers:$z},type:Wa}],type:IT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:Ya}],type:IT},{conditions:ovA,rules:[{conditions:[{[qz]:O41,[Nz]:[svA,lvA]}],rules:[{conditions:[{[qz]:"stringEquals",[Nz]:[{[qz]:iA0,[Nz]:[DbA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://oidc.{Region}.amazonaws.com",properties:$z,headers:$z},type:Wa},{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dnsSuffix}",properties:$z,headers:$z},type:Wa}],type:IT},{error:"FIPS is enabled but this partition does not support FIPS",type:Ya}],type:IT},{conditions:tvA,rules:[{conditions:[rvA],rules:[{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:$z,headers:$z},type:Wa}],type:IT},{error:"DualStack is enabled but this partition does not support DualStack",type:Ya}],type:IT},{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dnsSuffix}",properties:$z,headers:$z},type:Wa}],type:IT}],type:IT},{error:"Invalid Configuration: Missing Region",type:Ya}]};ZbA.ruleSet=SgQ});
var FpA=E((ZpA)=>{Object.defineProperty(ZpA,"__esModule",{value:!0});ZpA.getRuntimeConfig=void 0;var foQ=ph(),hoQ=foQ.__importDefault(rB0()),Q90=XV(),BpA=N$1(),L$1=V4(),goQ=VB(),uoQ=jG(),QpA=v4(),Fg=QD(),DpA=S3(),moQ=kG(),doQ=hZ(),coQ=ApA(),loQ=H8(),poQ=yG(),ioQ=H8(),noQ=(A)=>{ioQ.emitWarningIfUnsupportedVersion(process.version);let B=poQ.resolveDefaultsModeConfig(A),Q=()=>B().then(loQ.loadConfigsForDefaultMode),D=coQ.getRuntimeConfig(A);Q90.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??Fg.loadConfig(Q90.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??moQ.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??BpA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:hoQ.default.version}),httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(G)=>G.getIdentityProvider("aws.auth#sigv4")||(async(F)=>await A.credentialDefaultProvider(F?.__config||{})()),signer:new Q90.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(G)=>G.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new goQ.NoAuthSigner}],maxAttempts:A?.maxAttempts??Fg.loadConfig(QpA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??Fg.loadConfig(L$1.NODE_REGION_CONFIG_OPTIONS,{...L$1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:DpA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??Fg.loadConfig({...QpA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||doQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??uoQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??DpA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??Fg.loadConfig(L$1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??Fg.loadConfig(L$1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??Fg.loadConfig(BpA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};ZpA.getRuntimeConfig=noQ});
var FsA=E((ZsA)=>{Object.defineProperty(ZsA,"__esModule",{value:!0});ZsA.fromInstanceMetadata=void 0;var I24=Mz(),Y24=$F(),W24=(A)=>{return A?.logger?.debug("@smithy/credential-provider-imds","fromInstanceMetadata"),async()=>Y24.fromInstanceMetadata(A)().then((B)=>I24.setCredentialFeature(B,"CREDENTIALS_IMDS","0"))};ZsA.fromInstanceMetadata=W24});
var GQ0=E((pW5,AsA)=>{var{create:uA4,defineProperty:Q61,getOwnPropertyDescriptor:mA4,getOwnPropertyNames:dA4,getPrototypeOf:cA4}=Object,lA4=Object.prototype.hasOwnProperty,bG=(A,B)=>Q61(A,"name",{value:B,configurable:!0}),pA4=(A,B)=>{for(var Q in B)Q61(A,Q,{get:B[Q],enumerable:!0})},oaA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of dA4(B))if(!lA4.call(A,Z)&&Z!==Q)Q61(A,Z,{get:()=>B[Z],enumerable:!(D=mA4(B,Z))||D.enumerable})}return A},hy=(A,B,Q)=>(Q=A!=null?uA4(cA4(A)):{},oaA(B||!A||!A.__esModule?Q61(Q,"default",{value:A,enumerable:!0}):Q,A)),iA4=(A)=>oaA(Q61({},"__esModule",{value:!0}),A),taA={};pA4(taA,{fromIni:()=>Z24});AsA.exports=iA4(taA);var ZQ0=e5(),gy=Mz(),B61=eB(),nA4=bG((A,B,Q)=>{let D={EcsContainer:bG(async(Z)=>{let{fromHttp:G}=await Promise.resolve().then(()=>hy(nw1())),{fromContainerMetadata:F}=await Promise.resolve().then(()=>hy($F()));return Q?.debug("@aws-sdk/credential-provider-ini - credential_source is EcsContainer"),async()=>B61.chain(G(Z??{}),F(Z))().then(DQ0)},"EcsContainer"),Ec2InstanceMetadata:bG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Ec2InstanceMetadata");let{fromInstanceMetadata:G}=await Promise.resolve().then(()=>hy($F()));return async()=>G(Z)().then(DQ0)},"Ec2InstanceMetadata"),Environment:bG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Environment");let{fromEnv:G}=await Promise.resolve().then(()=>hy(sw1()));return async()=>G(Z)().then(DQ0)},"Environment")};if(A in D)return D[A];else throw new B61.CredentialsProviderError(`Unsupported credential source in profile ${B}. Got ${A}, expected EcsContainer or Ec2InstanceMetadata or Environment.`,{logger:Q})},"resolveCredentialSource"),DQ0=bG((A)=>gy.setCredentialFeature(A,"CREDENTIALS_PROFILE_NAMED_PROVIDER","p"),"setNamedProvider"),aA4=bG((A,{profile:B="default",logger:Q}={})=>{return Boolean(A)&&typeof A==="object"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1&&["undefined","string"].indexOf(typeof A.external_id)>-1&&["undefined","string"].indexOf(typeof A.mfa_serial)>-1&&(sA4(A,{profile:B,logger:Q})||rA4(A,{profile:B,logger:Q}))},"isAssumeRoleProfile"),sA4=bG((A,{profile:B,logger:Q})=>{let D=typeof A.source_profile==="string"&&typeof A.credential_source==="undefined";if(D)Q?.debug?.(`    ${B} isAssumeRoleWithSourceProfile source_profile=${A.source_profile}`);return D},"isAssumeRoleWithSourceProfile"),rA4=bG((A,{profile:B,logger:Q})=>{let D=typeof A.credential_source==="string"&&typeof A.source_profile==="undefined";if(D)Q?.debug?.(`    ${B} isCredentialSourceProfile credential_source=${A.credential_source}`);return D},"isCredentialSourceProfile"),oA4=bG(async(A,B,Q,D={})=>{Q.logger?.debug("@aws-sdk/credential-provider-ini - resolveAssumeRoleCredentials (STS)");let Z=B[A],{source_profile:G,region:F}=Z;if(!Q.roleAssumer){let{getDefaultRoleAssumer:Y}=await Promise.resolve().then(()=>hy(T$1()));Q.roleAssumer=Y({...Q.clientConfig,credentialProviderLogger:Q.logger,parentClientConfig:{...Q?.parentClientConfig,region:F??Q?.parentClientConfig?.region}},Q.clientPlugins)}if(G&&G in D)throw new B61.CredentialsProviderError(`Detected a cycle attempting to resolve credentials for profile ${ZQ0.getProfileName(Q)}. Profiles visited: `+Object.keys(D).join(", "),{logger:Q.logger});Q.logger?.debug(`@aws-sdk/credential-provider-ini - finding credential resolver using ${G?`source_profile=[${G}]`:`profile=[${A}]`}`);let I=G?eaA(G,B,Q,{...D,[G]:!0},aaA(B[G]??{})):(await nA4(Z.credential_source,A,Q.logger)(Q))();if(aaA(Z))return I.then((Y)=>gy.setCredentialFeature(Y,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"));else{let Y={RoleArn:Z.role_arn,RoleSessionName:Z.role_session_name||`aws-sdk-js-${Date.now()}`,ExternalId:Z.external_id,DurationSeconds:parseInt(Z.duration_seconds||"3600",10)},{mfa_serial:W}=Z;if(W){if(!Q.mfaCodeProvider)throw new B61.CredentialsProviderError(`Profile ${A} requires multi-factor authentication, but no MFA code callback was provided.`,{logger:Q.logger,tryNextLink:!1});Y.SerialNumber=W,Y.TokenCode=await Q.mfaCodeProvider(W)}let J=await I;return Q.roleAssumer(J,Y).then((X)=>gy.setCredentialFeature(X,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"))}},"resolveAssumeRoleCredentials"),aaA=bG((A)=>{return!A.role_arn&&!!A.credential_source},"isCredentialSourceWithoutRoleArn"),tA4=bG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.credential_process==="string","isProcessProfile"),eA4=bG(async(A,B)=>Promise.resolve().then(()=>hy(S$1())).then(({fromProcess:Q})=>Q({...A,profile:B})().then((D)=>gy.setCredentialFeature(D,"CREDENTIALS_PROFILE_PROCESS","v"))),"resolveProcessCredentials"),A24=bG(async(A,B,Q={})=>{let{fromSSO:D}=await Promise.resolve().then(()=>hy(l$1()));return D({profile:A,logger:Q.logger,parentClientConfig:Q.parentClientConfig,clientConfig:Q.clientConfig})().then((Z)=>{if(B.sso_session)return gy.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO","r");else return gy.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO_LEGACY","t")})},"resolveSsoCredentials"),B24=bG((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),saA=bG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.aws_access_key_id==="string"&&typeof A.aws_secret_access_key==="string"&&["undefined","string"].indexOf(typeof A.aws_session_token)>-1&&["undefined","string"].indexOf(typeof A.aws_account_id)>-1,"isStaticCredsProfile"),raA=bG(async(A,B)=>{B?.logger?.debug("@aws-sdk/credential-provider-ini - resolveStaticCredentials");let Q={accessKeyId:A.aws_access_key_id,secretAccessKey:A.aws_secret_access_key,sessionToken:A.aws_session_token,...A.aws_credential_scope&&{credentialScope:A.aws_credential_scope},...A.aws_account_id&&{accountId:A.aws_account_id}};return gy.setCredentialFeature(Q,"CREDENTIALS_PROFILE","n")},"resolveStaticCredentials"),Q24=bG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.web_identity_token_file==="string"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1,"isWebIdentityProfile"),D24=bG(async(A,B)=>Promise.resolve().then(()=>hy(A61())).then(({fromTokenFile:Q})=>Q({webIdentityTokenFile:A.web_identity_token_file,roleArn:A.role_arn,roleSessionName:A.role_session_name,roleAssumerWithWebIdentity:B.roleAssumerWithWebIdentity,logger:B.logger,parentClientConfig:B.parentClientConfig})().then((D)=>gy.setCredentialFeature(D,"CREDENTIALS_PROFILE_STS_WEB_ID_TOKEN","q"))),"resolveWebIdentityCredentials"),eaA=bG(async(A,B,Q,D={},Z=!1)=>{let G=B[A];if(Object.keys(D).length>0&&saA(G))return raA(G,Q);if(Z||aA4(G,{profile:A,logger:Q.logger}))return oA4(A,B,Q,D);if(saA(G))return raA(G,Q);if(Q24(G))return D24(G,Q);if(tA4(G))return eA4(Q,A);if(B24(G))return await A24(A,G,Q);throw new B61.CredentialsProviderError(`Could not resolve credentials using profile: [${A}] in configuration/credentials file(s).`,{logger:Q.logger})},"resolveProfileData"),Z24=bG((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/credential-provider-ini - fromIni");let D=await ZQ0.parseKnownFiles(Q);return eaA(ZQ0.getProfileName({profile:A.profile??B?.profile}),D,Q)},"fromIni")});
var H41=E((jG5,NkA)=>{var{defineProperty:dU1,getOwnPropertyDescriptor:v_Q,getOwnPropertyNames:b_Q}=Object,f_Q=Object.prototype.hasOwnProperty,mU1=(A,B)=>dU1(A,"name",{value:B,configurable:!0}),h_Q=(A,B)=>{for(var Q in B)dU1(A,Q,{get:B[Q],enumerable:!0})},g_Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of b_Q(B))if(!f_Q.call(A,Z)&&Z!==Q)dU1(A,Z,{get:()=>B[Z],enumerable:!(D=v_Q(B,Z))||D.enumerable})}return A},u_Q=(A)=>g_Q(dU1({},"__esModule",{value:!0}),A),wkA={};h_Q(wkA,{addRecursionDetectionMiddlewareOptions:()=>qkA,getRecursionDetectionPlugin:()=>l_Q,recursionDetectionMiddleware:()=>$kA});NkA.exports=u_Q(wkA);var m_Q=JV(),l00="X-Amzn-Trace-Id",d_Q="AWS_LAMBDA_FUNCTION_NAME",c_Q="_X_AMZN_TRACE_ID",$kA=mU1((A)=>(B)=>async(Q)=>{let{request:D}=Q;if(!m_Q.HttpRequest.isInstance(D)||A.runtime!=="node")return B(Q);let Z=Object.keys(D.headers??{}).find((Y)=>Y.toLowerCase()===l00.toLowerCase())??l00;if(D.headers.hasOwnProperty(Z))return B(Q);let G=process.env[d_Q],F=process.env[c_Q],I=mU1((Y)=>typeof Y==="string"&&Y.length>0,"nonEmptyString");if(I(G)&&I(F))D.headers[l00]=F;return B({...Q,request:D})},"recursionDetectionMiddleware"),qkA={step:"build",tags:["RECURSION_DETECTION"],name:"recursionDetectionMiddleware",override:!0,priority:"low"},l_Q=mU1((A)=>({applyToStack:mU1((B)=>{B.add($kA(A),qkA)},"applyToStack")}),"getRecursionDetectionPlugin")});
var H8=E((IY5,HB0)=>{var{defineProperty:iw1,getOwnPropertyDescriptor:riQ,getOwnPropertyNames:oiQ}=Object,tiQ=Object.prototype.hasOwnProperty,K8=(A,B)=>iw1(A,"name",{value:B,configurable:!0}),eiQ=(A,B)=>{for(var Q in B)iw1(A,Q,{get:B[Q],enumerable:!0})},JB0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of oiQ(B))if(!tiQ.call(A,Z)&&Z!==Q)iw1(A,Z,{get:()=>B[Z],enumerable:!(D=riQ(B,Z))||D.enumerable})}return A},AnQ=(A,B,Q)=>(JB0(A,B,"default"),Q&&JB0(Q,B,"default")),BnQ=(A)=>JB0(iw1({},"__esModule",{value:!0}),A),CB0={};eiQ(CB0,{Client:()=>QnQ,Command:()=>gmA,NoOpLogger:()=>wnQ,SENSITIVE_STRING:()=>ZnQ,ServiceException:()=>FnQ,_json:()=>VB0,collectBody:()=>WB0.collectBody,convertMap:()=>$nQ,createAggregatedClient:()=>GnQ,decorateServiceException:()=>umA,emitWarningIfUnsupportedVersion:()=>JnQ,extendedEncodeURIComponent:()=>WB0.extendedEncodeURIComponent,getArrayIfSingleItem:()=>EnQ,getDefaultClientConfiguration:()=>HnQ,getDefaultExtensionConfiguration:()=>dmA,getValueFromTextNode:()=>cmA,isSerializableHeaderValue:()=>UnQ,loadConfigsForDefaultMode:()=>WnQ,map:()=>KB0,resolveDefaultRuntimeConfig:()=>znQ,resolvedPath:()=>WB0.resolvedPath,serializeDateTime:()=>OnQ,serializeFloat:()=>RnQ,take:()=>qnQ,throwDefaultError:()=>mmA,withBaseException:()=>InQ});HB0.exports=BnQ(CB0);var hmA=Uw(),QnQ=class{constructor(A){this.config=A,this.middlewareStack=hmA.constructStack()}static{K8(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},WB0=$6(),XB0=YB0(),gmA=class{constructor(){this.middlewareStack=hmA.constructStack()}static{K8(this,"Command")}static classBuilder(){return new DnQ}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[XB0.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},DnQ=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{K8(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}sc(A){return this._operationSchema=A,this._smithyContext.operationSchema=A,this}build(){let A=this,B;return B=class extends gmA{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this),this.schema=A._operationSchema}static{K8(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},ZnQ="***SensitiveInformation***",GnQ=K8((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=K8(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),FnQ=class A extends Error{static{K8(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},umA=K8((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),mmA=K8(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=YnQ(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw umA(F,B)},"throwDefaultError"),InQ=K8((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{mmA({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),YnQ=K8((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),WnQ=K8((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),fmA=!1,JnQ=K8((A)=>{if(A&&!fmA&&parseInt(A.substring(1,A.indexOf(".")))<16)fmA=!0},"emitWarningIfUnsupportedVersion"),XnQ=K8((A)=>{let B=[];for(let Q in XB0.AlgorithmId){let D=XB0.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),VnQ=K8((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),CnQ=K8((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),KnQ=K8((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),dmA=K8((A)=>{return Object.assign(XnQ(A),CnQ(A))},"getDefaultExtensionConfiguration"),HnQ=dmA,znQ=K8((A)=>{return Object.assign(VnQ(A),KnQ(A))},"resolveDefaultRuntimeConfig"),EnQ=K8((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),cmA=K8((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=cmA(A[Q]);return A},"getValueFromTextNode"),UnQ=K8((A)=>{return A!=null},"isSerializableHeaderValue"),wnQ=class{static{K8(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function KB0(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,NnQ(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}lmA(D,null,G,F)}return D}K8(KB0,"map");var $nQ=K8((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),qnQ=K8((A,B)=>{let Q={};for(let D in B)lmA(Q,A,B,D);return Q},"take"),NnQ=K8((A,B,Q)=>{return KB0(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),lmA=K8((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=LnQ,Y=MnQ,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),LnQ=K8((A)=>A!=null,"nonNullish"),MnQ=K8((A)=>A,"pass"),RnQ=K8((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),OnQ=K8((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),VB0=K8((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(VB0);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=VB0(A[Q])}return B}return A},"_json");AnQ(CB0,Y6(),HB0.exports)});
var HcA=E((RY5,KcA)=>{var{defineProperty:C$1,getOwnPropertyDescriptor:ZsQ,getOwnPropertyNames:GsQ}=Object,FsQ=Object.prototype.hasOwnProperty,SY=(A,B)=>C$1(A,"name",{value:B,configurable:!0}),IsQ=(A,B)=>{for(var Q in B)C$1(A,Q,{get:B[Q],enumerable:!0})},YsQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of GsQ(B))if(!FsQ.call(A,Z)&&Z!==Q)C$1(A,Z,{get:()=>B[Z],enumerable:!(D=ZsQ(B,Z))||D.enumerable})}return A},WsQ=(A)=>YsQ(C$1({},"__esModule",{value:!0}),A),idA={};IsQ(idA,{ALGORITHM_IDENTIFIER:()=>I$1,ALGORITHM_IDENTIFIER_V4A:()=>CsQ,ALGORITHM_QUERY_PARAM:()=>ndA,ALWAYS_UNSIGNABLE_HEADERS:()=>BcA,AMZ_DATE_HEADER:()=>PB0,AMZ_DATE_QUERY_PARAM:()=>MB0,AUTH_HEADER:()=>TB0,CREDENTIAL_QUERY_PARAM:()=>adA,DATE_HEADER:()=>odA,EVENT_ALGORITHM_IDENTIFIER:()=>ZcA,EXPIRES_QUERY_PARAM:()=>rdA,GENERATED_HEADERS:()=>tdA,HOST_HEADER:()=>XsQ,KEY_TYPE_IDENTIFIER:()=>SB0,MAX_CACHE_SIZE:()=>FcA,MAX_PRESIGNED_TTL:()=>IcA,PROXY_HEADER_PATTERN:()=>QcA,REGION_SET_PARAM:()=>JsQ,SEC_HEADER_PATTERN:()=>DcA,SHA256_HEADER:()=>V$1,SIGNATURE_HEADER:()=>edA,SIGNATURE_QUERY_PARAM:()=>RB0,SIGNED_HEADERS_QUERY_PARAM:()=>sdA,SignatureV4:()=>MsQ,SignatureV4Base:()=>CcA,TOKEN_HEADER:()=>AcA,TOKEN_QUERY_PARAM:()=>OB0,UNSIGNABLE_PATTERNS:()=>VsQ,UNSIGNED_PAYLOAD:()=>GcA,clearCredentialCache:()=>HsQ,createScope:()=>W$1,getCanonicalHeaders:()=>qB0,getCanonicalQuery:()=>VcA,getPayloadHash:()=>J$1,getSigningKey:()=>YcA,hasHeader:()=>WcA,moveHeadersToQuery:()=>XcA,prepareRequest:()=>LB0,signatureV4aContainer:()=>RsQ});KcA.exports=WsQ(idA);var ddA=cB(),ndA="X-Amz-Algorithm",adA="X-Amz-Credential",MB0="X-Amz-Date",sdA="X-Amz-SignedHeaders",rdA="X-Amz-Expires",RB0="X-Amz-Signature",OB0="X-Amz-Security-Token",JsQ="X-Amz-Region-Set",TB0="authorization",PB0=MB0.toLowerCase(),odA="date",tdA=[TB0,PB0,odA],edA=RB0.toLowerCase(),V$1="x-amz-content-sha256",AcA=OB0.toLowerCase(),XsQ="host",BcA={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},QcA=/^proxy-/,DcA=/^sec-/,VsQ=[/^proxy-/i,/^sec-/i],I$1="AWS4-HMAC-SHA256",CsQ="AWS4-ECDSA-P256-SHA256",ZcA="AWS4-HMAC-SHA256-PAYLOAD",GcA="UNSIGNED-PAYLOAD",FcA=50,SB0="aws4_request",IcA=604800,_y=Zy(),KsQ=cB(),Ra={},Y$1=[],W$1=SY((A,B,Q)=>`${A}/${B}/${Q}/${SB0}`,"createScope"),YcA=SY(async(A,B,Q,D,Z)=>{let G=await cdA(A,B.secretAccessKey,B.accessKeyId),F=`${Q}:${D}:${Z}:${_y.toHex(G)}:${B.sessionToken}`;if(F in Ra)return Ra[F];Y$1.push(F);while(Y$1.length>FcA)delete Ra[Y$1.shift()];let I=`AWS4${B.secretAccessKey}`;for(let Y of[Q,D,Z,SB0])I=await cdA(A,I,Y);return Ra[F]=I},"getSigningKey"),HsQ=SY(()=>{Y$1.length=0,Object.keys(Ra).forEach((A)=>{delete Ra[A]})},"clearCredentialCache"),cdA=SY((A,B,Q)=>{let D=new A(B);return D.update(KsQ.toUint8Array(Q)),D.digest()},"hmac"),qB0=SY(({headers:A},B,Q)=>{let D={};for(let Z of Object.keys(A).sort()){if(A[Z]==null)continue;let G=Z.toLowerCase();if(G in BcA||B?.has(G)||QcA.test(G)||DcA.test(G)){if(!Q||Q&&!Q.has(G))continue}D[G]=A[Z].trim().replace(/\s+/g," ")}return D},"getCanonicalHeaders"),zsQ=fdA(),EsQ=cB(),J$1=SY(async({headers:A,body:B},Q)=>{for(let D of Object.keys(A))if(D.toLowerCase()===V$1)return A[D];if(B==null)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";else if(typeof B==="string"||ArrayBuffer.isView(B)||zsQ.isArrayBuffer(B)){let D=new Q;return D.update(EsQ.toUint8Array(B)),_y.toHex(await D.digest())}return GcA},"getPayloadHash"),ldA=cB(),UsQ=class{static{SY(this,"HeaderFormatter")}format(A){let B=[];for(let Z of Object.keys(A)){let G=ldA.fromUtf8(Z);B.push(Uint8Array.from([G.byteLength]),G,this.formatHeaderValue(A[Z]))}let Q=new Uint8Array(B.reduce((Z,G)=>Z+G.byteLength,0)),D=0;for(let Z of B)Q.set(Z,D),D+=Z.byteLength;return Q}formatHeaderValue(A){switch(A.type){case"boolean":return Uint8Array.from([A.value?0:1]);case"byte":return Uint8Array.from([2,A.value]);case"short":let B=new DataView(new ArrayBuffer(3));return B.setUint8(0,3),B.setInt16(1,A.value,!1),new Uint8Array(B.buffer);case"integer":let Q=new DataView(new ArrayBuffer(5));return Q.setUint8(0,4),Q.setInt32(1,A.value,!1),new Uint8Array(Q.buffer);case"long":let D=new Uint8Array(9);return D[0]=5,D.set(A.value.bytes,1),D;case"binary":let Z=new DataView(new ArrayBuffer(3+A.value.byteLength));Z.setUint8(0,6),Z.setUint16(1,A.value.byteLength,!1);let G=new Uint8Array(Z.buffer);return G.set(A.value,3),G;case"string":let F=ldA.fromUtf8(A.value),I=new DataView(new ArrayBuffer(3+F.byteLength));I.setUint8(0,7),I.setUint16(1,F.byteLength,!1);let Y=new Uint8Array(I.buffer);return Y.set(F,3),Y;case"timestamp":let W=new Uint8Array(9);return W[0]=8,W.set($sQ.fromNumber(A.value.valueOf()).bytes,1),W;case"uuid":if(!wsQ.test(A.value))throw new Error(`Invalid UUID received: ${A.value}`);let J=new Uint8Array(17);return J[0]=9,J.set(_y.fromHex(A.value.replace(/\-/g,"")),1),J}}},wsQ=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,$sQ=class A{constructor(B){if(this.bytes=B,B.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static{SY(this,"Int64")}static fromNumber(B){if(B>9223372036854776000||B<-9223372036854776000)throw new Error(`${B} is too large (or, if negative, too small) to represent as an Int64`);let Q=new Uint8Array(8);for(let D=7,Z=Math.abs(Math.round(B));D>-1&&Z>0;D--,Z/=256)Q[D]=Z;if(B<0)NB0(Q);return new A(Q)}valueOf(){let B=this.bytes.slice(0),Q=B[0]&128;if(Q)NB0(B);return parseInt(_y.toHex(B),16)*(Q?-1:1)}toString(){return String(this.valueOf())}};function NB0(A){for(let B=0;B<8;B++)A[B]^=255;for(let B=7;B>-1;B--)if(A[B]++,A[B]!==0)break}SY(NB0,"negate");var WcA=SY((A,B)=>{A=A.toLowerCase();for(let Q of Object.keys(B))if(A===Q.toLowerCase())return!0;return!1},"hasHeader"),JcA=DK(),XcA=SY((A,B={})=>{let{headers:Q,query:D={}}=JcA.HttpRequest.clone(A);for(let Z of Object.keys(Q)){let G=Z.toLowerCase();if(G.slice(0,6)==="x-amz-"&&!B.unhoistableHeaders?.has(G)||B.hoistableHeaders?.has(G))D[Z]=Q[Z],delete Q[Z]}return{...A,headers:Q,query:D}},"moveHeadersToQuery"),LB0=SY((A)=>{A=JcA.HttpRequest.clone(A);for(let B of Object.keys(A.headers))if(tdA.indexOf(B.toLowerCase())>-1)delete A.headers[B];return A},"prepareRequest"),pdA=I5(),qsQ=cB(),X$1=mdA(),VcA=SY(({query:A={}})=>{let B=[],Q={};for(let D of Object.keys(A)){if(D.toLowerCase()===edA)continue;let Z=X$1.escapeUri(D);B.push(Z);let G=A[D];if(typeof G==="string")Q[Z]=`${Z}=${X$1.escapeUri(G)}`;else if(Array.isArray(G))Q[Z]=G.slice(0).reduce((F,I)=>F.concat([`${Z}=${X$1.escapeUri(I)}`]),[]).sort().join("&")}return B.sort().map((D)=>Q[D]).filter((D)=>D).join("&")},"getCanonicalQuery"),NsQ=SY((A)=>LsQ(A).toISOString().replace(/\.\d{3}Z$/,"Z"),"iso8601"),LsQ=SY((A)=>{if(typeof A==="number")return new Date(A*1000);if(typeof A==="string"){if(Number(A))return new Date(Number(A)*1000);return new Date(A)}return A},"toDate"),CcA=class{static{SY(this,"SignatureV4Base")}constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){this.service=D,this.sha256=Z,this.uriEscapePath=G,this.applyChecksum=typeof A==="boolean"?A:!0,this.regionProvider=pdA.normalizeProvider(Q),this.credentialProvider=pdA.normalizeProvider(B)}createCanonicalRequest(A,B,Q){let D=Object.keys(B).sort();return`${A.method}
${this.getCanonicalPath(A)}
${VcA(A)}
${D.map((Z)=>`${Z}:${B[Z]}`).join(`
`)}

${D.join(";")}
${Q}`}async createStringToSign(A,B,Q,D){let Z=new this.sha256;Z.update(qsQ.toUint8Array(Q));let G=await Z.digest();return`${D}
${A}
${B}
${_y.toHex(G)}`}getCanonicalPath({path:A}){if(this.uriEscapePath){let B=[];for(let Z of A.split("/")){if(Z?.length===0)continue;if(Z===".")continue;if(Z==="..")B.pop();else B.push(Z)}let Q=`${A?.startsWith("/")?"/":""}${B.join("/")}${B.length>0&&A?.endsWith("/")?"/":""}`;return X$1.escapeUri(Q).replace(/%2F/g,"/")}return A}validateResolvedCredentials(A){if(typeof A!=="object"||typeof A.accessKeyId!=="string"||typeof A.secretAccessKey!=="string")throw new Error("Resolved credential object is not valid")}formatDate(A){let B=NsQ(A).replace(/[\-:]/g,"");return{longDate:B,shortDate:B.slice(0,8)}}getCanonicalHeaderList(A){return Object.keys(A).sort().join(";")}},MsQ=class extends CcA{constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){super({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G});this.headerFormatter=new UsQ}static{SY(this,"SignatureV4")}async presign(A,B={}){let{signingDate:Q=new Date,expiresIn:D=3600,unsignableHeaders:Z,unhoistableHeaders:G,signableHeaders:F,hoistableHeaders:I,signingRegion:Y,signingService:W}=B,J=await this.credentialProvider();this.validateResolvedCredentials(J);let X=Y??await this.regionProvider(),{longDate:V,shortDate:C}=this.formatDate(Q);if(D>IcA)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");let K=W$1(C,X,W??this.service),H=XcA(LB0(A),{unhoistableHeaders:G,hoistableHeaders:I});if(J.sessionToken)H.query[OB0]=J.sessionToken;H.query[ndA]=I$1,H.query[adA]=`${J.accessKeyId}/${K}`,H.query[MB0]=V,H.query[rdA]=D.toString(10);let z=qB0(H,Z,F);return H.query[sdA]=this.getCanonicalHeaderList(z),H.query[RB0]=await this.getSignature(V,K,this.getSigningKey(J,X,C,W),this.createCanonicalRequest(H,z,await J$1(A,this.sha256))),H}async sign(A,B){if(typeof A==="string")return this.signString(A,B);else if(A.headers&&A.payload)return this.signEvent(A,B);else if(A.message)return this.signMessage(A,B);else return this.signRequest(A,B)}async signEvent({headers:A,payload:B},{signingDate:Q=new Date,priorSignature:D,signingRegion:Z,signingService:G}){let F=Z??await this.regionProvider(),{shortDate:I,longDate:Y}=this.formatDate(Q),W=W$1(I,F,G??this.service),J=await J$1({headers:{},body:B},this.sha256),X=new this.sha256;X.update(A);let V=_y.toHex(await X.digest()),C=[ZcA,Y,W,D,V,J].join(`
`);return this.signString(C,{signingDate:Q,signingRegion:F,signingService:G})}async signMessage(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}){return this.signEvent({headers:this.headerFormatter.format(A.message.headers),payload:A.message.body},{signingDate:B,signingRegion:Q,signingService:D,priorSignature:A.priorSignature}).then((G)=>{return{message:A.message,signature:G}})}async signString(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}={}){let Z=await this.credentialProvider();this.validateResolvedCredentials(Z);let G=Q??await this.regionProvider(),{shortDate:F}=this.formatDate(B),I=new this.sha256(await this.getSigningKey(Z,G,F,D));return I.update(ddA.toUint8Array(A)),_y.toHex(await I.digest())}async signRequest(A,{signingDate:B=new Date,signableHeaders:Q,unsignableHeaders:D,signingRegion:Z,signingService:G}={}){let F=await this.credentialProvider();this.validateResolvedCredentials(F);let I=Z??await this.regionProvider(),Y=LB0(A),{longDate:W,shortDate:J}=this.formatDate(B),X=W$1(J,I,G??this.service);if(Y.headers[PB0]=W,F.sessionToken)Y.headers[AcA]=F.sessionToken;let V=await J$1(Y,this.sha256);if(!WcA(V$1,Y.headers)&&this.applyChecksum)Y.headers[V$1]=V;let C=qB0(Y,D,Q),K=await this.getSignature(W,X,this.getSigningKey(F,I,J,G),this.createCanonicalRequest(Y,C,V));return Y.headers[TB0]=`${I$1} Credential=${F.accessKeyId}/${X}, SignedHeaders=${this.getCanonicalHeaderList(C)}, Signature=${K}`,Y}async getSignature(A,B,Q,D){let Z=await this.createStringToSign(A,B,D,I$1),G=new this.sha256(await Q);return G.update(ddA.toUint8Array(Z)),_y.toHex(await G.digest())}getSigningKey(A,B,Q,D){return YcA(this.sha256,A,Q,B,D||this.service)}},RsQ={SignatureV4a:null}});
var HpA=E((CpA)=>{Object.defineProperty(CpA,"__esModule",{value:!0});CpA.resolveHttpAuthRuntimeConfig=CpA.getHttpAuthExtensionConfiguration=void 0;var GtQ=(A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}};CpA.getHttpAuthExtensionConfiguration=GtQ;var FtQ=(A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}};CpA.resolveHttpAuthRuntimeConfig=FtQ});
var II=E(($41)=>{Object.defineProperty($41,"__esModule",{value:!0});var RA0=ih();RA0.__exportStar(Lw(),$41);RA0.__exportStar(FA0(),$41);RA0.__exportStar(f_A(),$41)});
var IQ0=E((HsA)=>{Object.defineProperty(HsA,"__esModule",{value:!0});HsA.fromNodeProviderChain=void 0;var $24=KsA(),q24=(A={})=>$24.defaultProvider({...A});HsA.fromNodeProviderChain=q24});
var IvA=E((GvA)=>{Object.defineProperty(GvA,"__esModule",{value:!0});GvA.getRuntimeConfig=void 0;var $hQ=ih(),qhQ=$hQ.__importDefault(NxA()),BvA=II(),QvA=q41(),zw1=V4(),NhQ=jG(),DvA=v4(),rh=QD(),ZvA=S3(),LhQ=kG(),MhQ=hZ(),RhQ=AvA(),OhQ=W6(),ThQ=yG(),PhQ=W6(),ShQ=(A)=>{PhQ.emitWarningIfUnsupportedVersion(process.version);let B=ThQ.resolveDefaultsModeConfig(A),Q=()=>B().then(OhQ.loadConfigsForDefaultMode),D=RhQ.getRuntimeConfig(A);BvA.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??rh.loadConfig(BvA.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??LhQ.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??QvA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:qhQ.default.version}),maxAttempts:A?.maxAttempts??rh.loadConfig(DvA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??rh.loadConfig(zw1.NODE_REGION_CONFIG_OPTIONS,{...zw1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:ZvA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??rh.loadConfig({...DvA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||MhQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??NhQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??ZvA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??rh.loadConfig(zw1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??rh.loadConfig(zw1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??rh.loadConfig(QvA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};GvA.getRuntimeConfig=ShQ});
var JV=E((MG5,WkA)=>{var{defineProperty:fU1,getOwnPropertyDescriptor:Y_Q,getOwnPropertyNames:W_Q}=Object,J_Q=Object.prototype.hasOwnProperty,Ly=(A,B)=>fU1(A,"name",{value:B,configurable:!0}),X_Q=(A,B)=>{for(var Q in B)fU1(A,Q,{get:B[Q],enumerable:!0})},V_Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of W_Q(B))if(!J_Q.call(A,Z)&&Z!==Q)fU1(A,Z,{get:()=>B[Z],enumerable:!(D=Y_Q(B,Z))||D.enumerable})}return A},C_Q=(A)=>V_Q(fU1({},"__esModule",{value:!0}),A),GkA={};X_Q(GkA,{Field:()=>z_Q,Fields:()=>E_Q,HttpRequest:()=>U_Q,HttpResponse:()=>w_Q,IHttpRequest:()=>FkA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>K_Q,isValidHostname:()=>YkA,resolveHttpHandlerRuntimeConfig:()=>H_Q});WkA.exports=C_Q(GkA);var K_Q=Ly((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),H_Q=Ly((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),FkA=d00(),z_Q=class{static{Ly(this,"Field")}constructor({name:A,kind:B=FkA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},E_Q=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{Ly(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},U_Q=class A{static{Ly(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=IkA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function IkA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}Ly(IkA,"cloneQuery");var w_Q=class{static{Ly(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function YkA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}Ly(YkA,"isValidHostname")});
var JgA=E((zI5,WgA)=>{var{create:PcQ,defineProperty:_41,getOwnPropertyDescriptor:ScQ,getOwnPropertyNames:jcQ,getPrototypeOf:kcQ}=Object,ycQ=Object.prototype.hasOwnProperty,xG=(A,B)=>_41(A,"name",{value:B,configurable:!0}),_cQ=(A,B)=>{for(var Q in B)_41(A,Q,{get:B[Q],enumerable:!0})},FgA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of jcQ(B))if(!ycQ.call(A,Z)&&Z!==Q)_41(A,Z,{get:()=>B[Z],enumerable:!(D=ScQ(B,Z))||D.enumerable})}return A},jy=(A,B,Q)=>(Q=A!=null?PcQ(kcQ(A)):{},FgA(B||!A||!A.__esModule?_41(Q,"default",{value:A,enumerable:!0}):Q,A)),xcQ=(A)=>FgA(_41({},"__esModule",{value:!0}),A),IgA={};_cQ(IgA,{fromIni:()=>icQ});WgA.exports=xcQ(IgA);var a20=e5(),ky=Lw(),y41=eB(),vcQ=xG((A,B,Q)=>{let D={EcsContainer:xG(async(Z)=>{let{fromHttp:G}=await Promise.resolve().then(()=>jy(_A0())),{fromContainerMetadata:F}=await Promise.resolve().then(()=>jy($F()));return Q?.debug("@aws-sdk/credential-provider-ini - credential_source is EcsContainer"),async()=>y41.chain(G(Z??{}),F(Z))().then(n20)},"EcsContainer"),Ec2InstanceMetadata:xG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Ec2InstanceMetadata");let{fromInstanceMetadata:G}=await Promise.resolve().then(()=>jy($F()));return async()=>G(Z)().then(n20)},"Ec2InstanceMetadata"),Environment:xG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Environment");let{fromEnv:G}=await Promise.resolve().then(()=>jy(jA0()));return async()=>G(Z)().then(n20)},"Environment")};if(A in D)return D[A];else throw new y41.CredentialsProviderError(`Unsupported credential source in profile ${B}. Got ${A}, expected EcsContainer or Ec2InstanceMetadata or Environment.`,{logger:Q})},"resolveCredentialSource"),n20=xG((A)=>ky.setCredentialFeature(A,"CREDENTIALS_PROFILE_NAMED_PROVIDER","p"),"setNamedProvider"),bcQ=xG((A,{profile:B="default",logger:Q}={})=>{return Boolean(A)&&typeof A==="object"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1&&["undefined","string"].indexOf(typeof A.external_id)>-1&&["undefined","string"].indexOf(typeof A.mfa_serial)>-1&&(fcQ(A,{profile:B,logger:Q})||hcQ(A,{profile:B,logger:Q}))},"isAssumeRoleProfile"),fcQ=xG((A,{profile:B,logger:Q})=>{let D=typeof A.source_profile==="string"&&typeof A.credential_source==="undefined";if(D)Q?.debug?.(`    ${B} isAssumeRoleWithSourceProfile source_profile=${A.source_profile}`);return D},"isAssumeRoleWithSourceProfile"),hcQ=xG((A,{profile:B,logger:Q})=>{let D=typeof A.credential_source==="string"&&typeof A.source_profile==="undefined";if(D)Q?.debug?.(`    ${B} isCredentialSourceProfile credential_source=${A.credential_source}`);return D},"isCredentialSourceProfile"),gcQ=xG(async(A,B,Q,D={})=>{Q.logger?.debug("@aws-sdk/credential-provider-ini - resolveAssumeRoleCredentials (STS)");let Z=B[A],{source_profile:G,region:F}=Z;if(!Q.roleAssumer){let{getDefaultRoleAssumer:Y}=await Promise.resolve().then(()=>jy(g20()));Q.roleAssumer=Y({...Q.clientConfig,credentialProviderLogger:Q.logger,parentClientConfig:{...Q?.parentClientConfig,region:F??Q?.parentClientConfig?.region}},Q.clientPlugins)}if(G&&G in D)throw new y41.CredentialsProviderError(`Detected a cycle attempting to resolve credentials for profile ${a20.getProfileName(Q)}. Profiles visited: `+Object.keys(D).join(", "),{logger:Q.logger});Q.logger?.debug(`@aws-sdk/credential-provider-ini - finding credential resolver using ${G?`source_profile=[${G}]`:`profile=[${A}]`}`);let I=G?YgA(G,B,Q,{...D,[G]:!0},DgA(B[G]??{})):(await vcQ(Z.credential_source,A,Q.logger)(Q))();if(DgA(Z))return I.then((Y)=>ky.setCredentialFeature(Y,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"));else{let Y={RoleArn:Z.role_arn,RoleSessionName:Z.role_session_name||`aws-sdk-js-${Date.now()}`,ExternalId:Z.external_id,DurationSeconds:parseInt(Z.duration_seconds||"3600",10)},{mfa_serial:W}=Z;if(W){if(!Q.mfaCodeProvider)throw new y41.CredentialsProviderError(`Profile ${A} requires multi-factor authentication, but no MFA code callback was provided.`,{logger:Q.logger,tryNextLink:!1});Y.SerialNumber=W,Y.TokenCode=await Q.mfaCodeProvider(W)}let J=await I;return Q.roleAssumer(J,Y).then((X)=>ky.setCredentialFeature(X,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"))}},"resolveAssumeRoleCredentials"),DgA=xG((A)=>{return!A.role_arn&&!!A.credential_source},"isCredentialSourceWithoutRoleArn"),ucQ=xG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.credential_process==="string","isProcessProfile"),mcQ=xG(async(A,B)=>Promise.resolve().then(()=>jy(d20())).then(({fromProcess:Q})=>Q({...A,profile:B})().then((D)=>ky.setCredentialFeature(D,"CREDENTIALS_PROFILE_PROCESS","v"))),"resolveProcessCredentials"),dcQ=xG(async(A,B,Q={})=>{let{fromSSO:D}=await Promise.resolve().then(()=>jy(eA0()));return D({profile:A,logger:Q.logger,parentClientConfig:Q.parentClientConfig,clientConfig:Q.clientConfig})().then((Z)=>{if(B.sso_session)return ky.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO","r");else return ky.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO_LEGACY","t")})},"resolveSsoCredentials"),ccQ=xG((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),ZgA=xG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.aws_access_key_id==="string"&&typeof A.aws_secret_access_key==="string"&&["undefined","string"].indexOf(typeof A.aws_session_token)>-1&&["undefined","string"].indexOf(typeof A.aws_account_id)>-1,"isStaticCredsProfile"),GgA=xG(async(A,B)=>{B?.logger?.debug("@aws-sdk/credential-provider-ini - resolveStaticCredentials");let Q={accessKeyId:A.aws_access_key_id,secretAccessKey:A.aws_secret_access_key,sessionToken:A.aws_session_token,...A.aws_credential_scope&&{credentialScope:A.aws_credential_scope},...A.aws_account_id&&{accountId:A.aws_account_id}};return ky.setCredentialFeature(Q,"CREDENTIALS_PROFILE","n")},"resolveStaticCredentials"),lcQ=xG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.web_identity_token_file==="string"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1,"isWebIdentityProfile"),pcQ=xG(async(A,B)=>Promise.resolve().then(()=>jy(i20())).then(({fromTokenFile:Q})=>Q({webIdentityTokenFile:A.web_identity_token_file,roleArn:A.role_arn,roleSessionName:A.role_session_name,roleAssumerWithWebIdentity:B.roleAssumerWithWebIdentity,logger:B.logger,parentClientConfig:B.parentClientConfig})().then((D)=>ky.setCredentialFeature(D,"CREDENTIALS_PROFILE_STS_WEB_ID_TOKEN","q"))),"resolveWebIdentityCredentials"),YgA=xG(async(A,B,Q,D={},Z=!1)=>{let G=B[A];if(Object.keys(D).length>0&&ZgA(G))return GgA(G,Q);if(Z||bcQ(G,{profile:A,logger:Q.logger}))return gcQ(A,B,Q,D);if(ZgA(G))return GgA(G,Q);if(lcQ(G))return pcQ(G,Q);if(ucQ(G))return mcQ(Q,A);if(ccQ(G))return await dcQ(A,G,Q);throw new y41.CredentialsProviderError(`Could not resolve credentials using profile: [${A}] in configuration/credentials file(s).`,{logger:Q.logger})},"resolveProfileData"),icQ=xG((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/credential-provider-ini - fromIni");let D=await a20.parseKnownFiles(Q);return YgA(a20.getProfileName({profile:A.profile??B?.profile}),D,Q)},"fromIni")});
var JhA=E((YhA)=>{Object.defineProperty(YhA,"__esModule",{value:!0});YhA.resolveRuntimeExtensions=void 0;var ZhA=L41(),GhA=JV(),FhA=W6(),IhA=DhA(),pmQ=(A,B)=>{let Q=Object.assign(ZhA.getAwsRegionExtensionConfiguration(A),FhA.getDefaultExtensionConfiguration(A),GhA.getHttpHandlerExtensionConfiguration(A),IhA.getHttpAuthExtensionConfiguration(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,ZhA.resolveAwsRegionExtensionConfiguration(Q),FhA.resolveDefaultRuntimeConfig(Q),GhA.resolveHttpHandlerRuntimeConfig(Q),IhA.resolveHttpAuthRuntimeConfig(Q))};YhA.resolveRuntimeExtensions=pmQ});
var K41=E((SG5,UkA)=>{var{defineProperty:uU1,getOwnPropertyDescriptor:P_Q,getOwnPropertyNames:S_Q}=Object,j_Q=Object.prototype.hasOwnProperty,c00=(A,B)=>uU1(A,"name",{value:B,configurable:!0}),k_Q=(A,B)=>{for(var Q in B)uU1(A,Q,{get:B[Q],enumerable:!0})},y_Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of S_Q(B))if(!j_Q.call(A,Z)&&Z!==Q)uU1(A,Z,{get:()=>B[Z],enumerable:!(D=P_Q(B,Z))||D.enumerable})}return A},__Q=(A)=>y_Q(uU1({},"__esModule",{value:!0}),A),HkA={};k_Q(HkA,{getLoggerPlugin:()=>x_Q,loggerMiddleware:()=>zkA,loggerMiddlewareOptions:()=>EkA});UkA.exports=__Q(HkA);var zkA=c00(()=>(A,B)=>async(Q)=>{try{let D=await A(Q),{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y,overrideOutputFilterSensitiveLog:W}=I,J=Y??B.inputFilterSensitiveLog,X=W??B.outputFilterSensitiveLog,{$metadata:V,...C}=D.output;return F?.info?.({clientName:Z,commandName:G,input:J(Q.input),output:X(C),metadata:V}),D}catch(D){let{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y}=I,W=Y??B.inputFilterSensitiveLog;throw F?.error?.({clientName:Z,commandName:G,input:W(Q.input),error:D,metadata:D.$metadata}),D}},"loggerMiddleware"),EkA={name:"loggerMiddleware",tags:["LOGGER"],step:"initialize",override:!0},x_Q=c00((A)=>({applyToStack:c00((B)=>{B.add(zkA(),EkA)},"applyToStack")}),"getLoggerPlugin")});
var KbA=E((VbA)=>{Object.defineProperty(VbA,"__esModule",{value:!0});VbA.getRuntimeConfig=void 0;var xgQ=II(),vgQ=VB(),bgQ=W6(),fgQ=BZ(),JbA=Ry(),XbA=cB(),hgQ=cA0(),ggQ=WbA(),ugQ=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??JbA.fromBase64,base64Encoder:A?.base64Encoder??JbA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??ggQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??hgQ.defaultSSOOIDCHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new xgQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new vgQ.NoAuthSigner}],logger:A?.logger??new bgQ.NoOpLogger,serviceId:A?.serviceId??"SSO OIDC",urlParser:A?.urlParser??fgQ.parseUrl,utf8Decoder:A?.utf8Decoder??XbA.fromUtf8,utf8Encoder:A?.utf8Encoder??XbA.toUtf8}};VbA.getRuntimeConfig=ugQ});
var KmA=E((VmA)=>{Object.defineProperty(VmA,"__esModule",{value:!0});VmA.fromContainerMetadata=void 0;var CiQ=$F(),KiQ=(A)=>{return A?.logger?.debug("@smithy/credential-provider-imds","fromContainerMetadata"),CiQ.fromContainerMetadata(A)};VmA.fromContainerMetadata=KiQ});
var KsA=E((aW5,CsA)=>{var{create:J24,defineProperty:D61,getOwnPropertyDescriptor:X24,getOwnPropertyNames:V24,getPrototypeOf:C24}=Object,K24=Object.prototype.hasOwnProperty,i$1=(A,B)=>D61(A,"name",{value:B,configurable:!0}),H24=(A,B)=>{for(var Q in B)D61(A,Q,{get:B[Q],enumerable:!0})},WsA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of V24(B))if(!K24.call(A,Z)&&Z!==Q)D61(A,Z,{get:()=>B[Z],enumerable:!(D=X24(B,Z))||D.enumerable})}return A},ia=(A,B,Q)=>(Q=A!=null?J24(C24(A)):{},WsA(B||!A||!A.__esModule?D61(Q,"default",{value:A,enumerable:!0}):Q,A)),z24=(A)=>WsA(D61({},"__esModule",{value:!0}),A),JsA={};H24(JsA,{credentialsTreatedAsExpired:()=>VsA,credentialsWillNeedRefresh:()=>XsA,defaultProvider:()=>w24});CsA.exports=z24(JsA);var FQ0=sw1(),E24=e5(),Jg=eB(),IsA="AWS_EC2_METADATA_DISABLED",U24=i$1(async(A)=>{let{ENV_CMDS_FULL_URI:B,ENV_CMDS_RELATIVE_URI:Q,fromContainerMetadata:D,fromInstanceMetadata:Z}=await Promise.resolve().then(()=>ia($F()));if(process.env[Q]||process.env[B]){A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromHttp/fromContainerMetadata");let{fromHttp:G}=await Promise.resolve().then(()=>ia(nw1()));return Jg.chain(G(A),D(A))}if(process.env[IsA]&&process.env[IsA]!=="false")return async()=>{throw new Jg.CredentialsProviderError("EC2 Instance Metadata Service access disabled",{logger:A.logger})};return A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromInstanceMetadata"),Z(A)},"remoteProvider"),YsA=!1,w24=i$1((A={})=>Jg.memoize(Jg.chain(async()=>{if(A.profile??process.env[E24.ENV_PROFILE]){if(process.env[FQ0.ENV_KEY]&&process.env[FQ0.ENV_SECRET]){if(!YsA)(A.logger?.warn&&A.logger?.constructor?.name!=="NoOpLogger"?A.logger.warn:console.warn)(`@aws-sdk/credential-provider-node - defaultProvider::fromEnv WARNING:
    Multiple credential sources detected: 
    Both AWS_PROFILE and the pair AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY static credentials are set.
    This SDK will proceed with the AWS_PROFILE value.
    
    However, a future version may change this behavior to prefer the ENV static credentials.
    Please ensure that your environment only sets either the AWS_PROFILE or the
    AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY pair.
`),YsA=!0}throw new Jg.CredentialsProviderError("AWS_PROFILE is set, skipping fromEnv provider.",{logger:A.logger,tryNextLink:!0})}return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromEnv"),FQ0.fromEnv(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromSSO");let{ssoStartUrl:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoSession:G}=A;if(!B&&!Q&&!D&&!Z&&!G)throw new Jg.CredentialsProviderError("Skipping SSO provider in default chain (inputs do not include SSO fields).",{logger:A.logger});let{fromSSO:F}=await Promise.resolve().then(()=>ia(l$1()));return F(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromIni");let{fromIni:B}=await Promise.resolve().then(()=>ia(GQ0()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromProcess");let{fromProcess:B}=await Promise.resolve().then(()=>ia(S$1()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromTokenFile");let{fromTokenFile:B}=await Promise.resolve().then(()=>ia(A61()));return B(A)()},async()=>{return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::remoteProvider"),(await U24(A))()},async()=>{throw new Jg.CredentialsProviderError("Could not load credentials from any providers",{tryNextLink:!1,logger:A.logger})}),VsA,XsA),"defaultProvider"),XsA=i$1((A)=>A?.expiration!==void 0,"credentialsWillNeedRefresh"),VsA=i$1((A)=>A?.expiration!==void 0&&A.expiration.getTime()-Date.now()<300000,"credentialsTreatedAsExpired")});
var KxA=E((VxA)=>{Object.defineProperty(VxA,"__esModule",{value:!0});VxA.retryWrapper=void 0;var PfQ=(A,B,Q)=>{return async()=>{for(let D=0;D<B;++D)try{return await A()}catch(Z){await new Promise((G)=>setTimeout(G,Q))}return await A()}};VxA.retryWrapper=PfQ});
var KyA=E((xG5,CyA)=>{var{defineProperty:sU1,getOwnPropertyDescriptor:YxQ,getOwnPropertyNames:WxQ}=Object,JxQ=Object.prototype.hasOwnProperty,XxQ=(A,B)=>sU1(A,"name",{value:B,configurable:!0}),VxQ=(A,B)=>{for(var Q in B)sU1(A,Q,{get:B[Q],enumerable:!0})},CxQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of WxQ(B))if(!JxQ.call(A,Z)&&Z!==Q)sU1(A,Z,{get:()=>B[Z],enumerable:!(D=YxQ(B,Z))||D.enumerable})}return A},KxQ=(A)=>CxQ(sU1({},"__esModule",{value:!0}),A),VyA={};VxQ(VyA,{isArrayBuffer:()=>HxQ});CyA.exports=KxQ(VyA);var HxQ=XxQ((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var L41=E((yF5,CvA)=>{var{defineProperty:Ew1,getOwnPropertyDescriptor:jhQ,getOwnPropertyNames:khQ}=Object,yhQ=Object.prototype.hasOwnProperty,oN=(A,B)=>Ew1(A,"name",{value:B,configurable:!0}),_hQ=(A,B)=>{for(var Q in B)Ew1(A,Q,{get:B[Q],enumerable:!0})},xhQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of khQ(B))if(!yhQ.call(A,Z)&&Z!==Q)Ew1(A,Z,{get:()=>B[Z],enumerable:!(D=jhQ(B,Z))||D.enumerable})}return A},vhQ=(A)=>xhQ(Ew1({},"__esModule",{value:!0}),A),WvA={};_hQ(WvA,{NODE_REGION_CONFIG_FILE_OPTIONS:()=>ghQ,NODE_REGION_CONFIG_OPTIONS:()=>hhQ,REGION_ENV_NAME:()=>JvA,REGION_INI_NAME:()=>XvA,getAwsRegionExtensionConfiguration:()=>bhQ,resolveAwsRegionExtensionConfiguration:()=>fhQ,resolveRegionConfig:()=>uhQ});CvA.exports=vhQ(WvA);var bhQ=oN((A)=>{return{setRegion(B){A.region=B},region(){return A.region}}},"getAwsRegionExtensionConfiguration"),fhQ=oN((A)=>{return{region:A.region()}},"resolveAwsRegionExtensionConfiguration"),JvA="AWS_REGION",XvA="region",hhQ={environmentVariableSelector:oN((A)=>A[JvA],"environmentVariableSelector"),configFileSelector:oN((A)=>A[XvA],"configFileSelector"),default:oN(()=>{throw new Error("Region is missing")},"default")},ghQ={preferredFile:"credentials"},VvA=oN((A)=>typeof A==="string"&&(A.startsWith("fips-")||A.endsWith("-fips")),"isFipsRegion"),YvA=oN((A)=>VvA(A)?["fips-aws-global","aws-fips"].includes(A)?"us-east-1":A.replace(/fips-(dkr-|prod-)?|-fips/,""):A,"getRealRegion"),uhQ=oN((A)=>{let{region:B,useFipsEndpoint:Q}=A;if(!B)throw new Error("Region is missing");return Object.assign(A,{region:oN(async()=>{if(typeof B==="string")return YvA(B);let D=await B();return YvA(D)},"region"),useFipsEndpoint:oN(async()=>{let D=typeof B==="string"?B:await B();if(VvA(D))return!0;return typeof Q!=="function"?Promise.resolve(!!Q):Q()},"useFipsEndpoint")})},"resolveRegionConfig")});
var Lw=E((_G5,XyA)=>{var{defineProperty:nU1,getOwnPropertyDescriptor:BxQ,getOwnPropertyNames:QxQ}=Object,DxQ=Object.prototype.hasOwnProperty,aU1=(A,B)=>nU1(A,"name",{value:B,configurable:!0}),ZxQ=(A,B)=>{for(var Q in B)nU1(A,Q,{get:B[Q],enumerable:!0})},GxQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of QxQ(B))if(!DxQ.call(A,Z)&&Z!==Q)nU1(A,Z,{get:()=>B[Z],enumerable:!(D=BxQ(B,Z))||D.enumerable})}return A},FxQ=(A)=>GxQ(nU1({},"__esModule",{value:!0}),A),IyA={};ZxQ(IyA,{emitWarningIfUnsupportedVersion:()=>IxQ,setCredentialFeature:()=>YyA,setFeature:()=>WyA,setTokenFeature:()=>JyA,state:()=>i00});XyA.exports=FxQ(IyA);var i00={warningEmitted:!1},IxQ=aU1((A)=>{if(A&&!i00.warningEmitted&&parseInt(A.substring(1,A.indexOf(".")))<18)i00.warningEmitted=!0,process.emitWarning(`NodeDeprecationWarning: The AWS SDK for JavaScript (v3) will
no longer support Node.js 16.x on January 6, 2025.

To continue receiving updates to AWS services, bug fixes, and security
updates please upgrade to a supported Node.js LTS version.

More information can be found at: https://a.co/74kJMmI`)},"emitWarningIfUnsupportedVersion");function YyA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}aU1(YyA,"setCredentialFeature");function WyA(A,B,Q){if(!A.__aws_sdk_context)A.__aws_sdk_context={features:{}};else if(!A.__aws_sdk_context.features)A.__aws_sdk_context.features={};A.__aws_sdk_context.features[B]=Q}aU1(WyA,"setFeature");function JyA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}aU1(JyA,"setTokenFeature")});
var MsA=E((n$1)=>{Object.defineProperty(n$1,"__esModule",{value:!0});n$1.STSClient=n$1.AssumeRoleCommand=void 0;var LsA=T$1();Object.defineProperty(n$1,"AssumeRoleCommand",{enumerable:!0,get:function(){return LsA.AssumeRoleCommand}});Object.defineProperty(n$1,"STSClient",{enumerable:!0,get:function(){return LsA.STSClient}})});
var Mz=E((AY5,wmA)=>{var{defineProperty:mw1,getOwnPropertyDescriptor:HiQ,getOwnPropertyNames:ziQ}=Object,EiQ=Object.prototype.hasOwnProperty,dw1=(A,B)=>mw1(A,"name",{value:B,configurable:!0}),UiQ=(A,B)=>{for(var Q in B)mw1(A,Q,{get:B[Q],enumerable:!0})},wiQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ziQ(B))if(!EiQ.call(A,Z)&&Z!==Q)mw1(A,Z,{get:()=>B[Z],enumerable:!(D=HiQ(B,Z))||D.enumerable})}return A},$iQ=(A)=>wiQ(mw1({},"__esModule",{value:!0}),A),HmA={};UiQ(HmA,{emitWarningIfUnsupportedVersion:()=>qiQ,setCredentialFeature:()=>zmA,setFeature:()=>EmA,setTokenFeature:()=>UmA,state:()=>IB0});wmA.exports=$iQ(HmA);var IB0={warningEmitted:!1},qiQ=dw1((A)=>{if(A&&!IB0.warningEmitted&&parseInt(A.substring(1,A.indexOf(".")))<18)IB0.warningEmitted=!0,process.emitWarning(`NodeDeprecationWarning: The AWS SDK for JavaScript (v3) will
no longer support Node.js 16.x on January 6, 2025.

To continue receiving updates to AWS services, bug fixes, and security
updates please upgrade to a supported Node.js LTS version.

More information can be found at: https://a.co/74kJMmI`)},"emitWarningIfUnsupportedVersion");function zmA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}dw1(zmA,"setCredentialFeature");function EmA(A,B,Q){if(!A.__aws_sdk_context)A.__aws_sdk_context={features:{}};else if(!A.__aws_sdk_context.features)A.__aws_sdk_context.features={};A.__aws_sdk_context.features[B]=Q}dw1(EmA,"setFeature");function UmA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}dw1(UmA,"setTokenFeature")});
var N$1=E((DW5,NlA)=>{var{defineProperty:q$1,getOwnPropertyDescriptor:KoQ,getOwnPropertyNames:HoQ}=Object,zoQ=Object.prototype.hasOwnProperty,$$1=(A,B)=>q$1(A,"name",{value:B,configurable:!0}),EoQ=(A,B)=>{for(var Q in B)q$1(A,Q,{get:B[Q],enumerable:!0})},UoQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of HoQ(B))if(!zoQ.call(A,Z)&&Z!==Q)q$1(A,Z,{get:()=>B[Z],enumerable:!(D=KoQ(B,Z))||D.enumerable})}return A},woQ=(A)=>UoQ(q$1({},"__esModule",{value:!0}),A),ElA={};EoQ(ElA,{NODE_APP_ID_CONFIG_OPTIONS:()=>MoQ,UA_APP_ID_ENV_NAME:()=>$lA,UA_APP_ID_INI_NAME:()=>qlA,createDefaultUserAgentProvider:()=>wlA,crtAvailability:()=>UlA,defaultUserAgent:()=>qoQ});NlA.exports=woQ(ElA);var zlA=J1("os"),oB0=J1("process"),UlA={isCrtAvailable:!1},$oQ=$$1(()=>{if(UlA.isCrtAvailable)return["md/crt-avail"];return null},"isCrtAvailable"),wlA=$$1(({serviceId:A,clientVersion:B})=>{return async(Q)=>{let D=[["aws-sdk-js",B],["ua","2.1"],[`os/${zlA.platform()}`,zlA.release()],["lang/js"],["md/nodejs",`${oB0.versions.node}`]],Z=$oQ();if(Z)D.push(Z);if(A)D.push([`api/${A}`,B]);if(oB0.env.AWS_EXECUTION_ENV)D.push([`exec-env/${oB0.env.AWS_EXECUTION_ENV}`]);let G=await Q?.userAgentAppId?.();return G?[...D,[`app/${G}`]]:[...D]}},"createDefaultUserAgentProvider"),qoQ=wlA,NoQ=l41(),$lA="AWS_SDK_UA_APP_ID",qlA="sdk_ua_app_id",LoQ="sdk-ua-app-id",MoQ={environmentVariableSelector:$$1((A)=>A[$lA],"environmentVariableSelector"),configFileSelector:$$1((A)=>A[qlA]??A[LoQ],"configFileSelector"),default:NoQ.DEFAULT_UA_APP_ID}});
var NmA=E(($mA)=>{Object.defineProperty($mA,"__esModule",{value:!0});$mA.checkUrl=void 0;var NiQ=eB(),LiQ="*************",MiQ="*************3",RiQ="[fd00:ec2::23]",OiQ=(A,B)=>{if(A.protocol==="https:")return;if(A.hostname===LiQ||A.hostname===MiQ||A.hostname===RiQ)return;if(A.hostname.includes("[")){if(A.hostname==="[::1]"||A.hostname==="[0000:0000:0000:0000:0000:0000:0000:0001]")return}else{if(A.hostname==="localhost")return;let Q=A.hostname.split("."),D=(Z)=>{let G=parseInt(Z,10);return 0<=G&&G<=255};if(Q[0]==="127"&&D(Q[1])&&D(Q[2])&&D(Q[3])&&Q.length===4)return}throw new NiQ.CredentialsProviderError(`URL not accepted. It must either be HTTPS or match one of the following:
  - loopback CIDR *********/8 or [::1/128]
  - ECS container host *************
  - EKS container host *************3 or [fd00:ec2::23]`,{logger:B})};$mA.checkUrl=OiQ});
var NpA=E(($pA)=>{Object.defineProperty($pA,"__esModule",{value:!0});$pA.resolveRuntimeExtensions=void 0;var zpA=R$1(),EpA=DK(),UpA=H8(),wpA=HpA(),YtQ=(A,B)=>{let Q=Object.assign(zpA.getAwsRegionExtensionConfiguration(A),UpA.getDefaultExtensionConfiguration(A),EpA.getHttpHandlerExtensionConfiguration(A),wpA.getHttpAuthExtensionConfiguration(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,zpA.resolveAwsRegionExtensionConfiguration(Q),UpA.resolveDefaultRuntimeConfig(Q),EpA.resolveHttpHandlerRuntimeConfig(Q),wpA.resolveHttpAuthRuntimeConfig(Q))};$pA.resolveRuntimeExtensions=YtQ});
var NsA=E(($sA)=>{Object.defineProperty($sA,"__esModule",{value:!0});$sA.fromSSO=void 0;var M24=l$1(),R24=(A={})=>{return M24.fromSSO({...A})};$sA.fromSSO=R24});
var NxA=E((OF5,rfQ)=>{rfQ.exports={name:"@aws-sdk/client-sso",description:"AWS SDK for JavaScript Sso Client for Node.js, Browser and React Native",version:"3.840.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-sso","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo sso"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sso",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-sso"}}});
var PiA=E((OiA)=>{Object.defineProperty(OiA,"__esModule",{value:!0});OiA.ruleSet=void 0;var NiA="required",Oz="fn",Tz="argv",ua="ref",ViA=!0,CiA="isSet",n41="booleanEquals",ha="error",ga="endpoint",ET="tree",g90="PartitionResult",u90="getAttr",KiA={[NiA]:!1,type:"String"},HiA={[NiA]:!0,default:!1,type:"Boolean"},ziA={[ua]:"Endpoint"},LiA={[Oz]:n41,[Tz]:[{[ua]:"UseFIPS"},!0]},MiA={[Oz]:n41,[Tz]:[{[ua]:"UseDualStack"},!0]},Rz={},EiA={[Oz]:u90,[Tz]:[{[ua]:g90},"supportsFIPS"]},RiA={[ua]:g90},UiA={[Oz]:n41,[Tz]:[!0,{[Oz]:u90,[Tz]:[RiA,"supportsDualStack"]}]},wiA=[LiA],$iA=[MiA],qiA=[{[ua]:"Region"}],heQ={version:"1.0",parameters:{Region:KiA,UseDualStack:HiA,UseFIPS:HiA,Endpoint:KiA},rules:[{conditions:[{[Oz]:CiA,[Tz]:[ziA]}],rules:[{conditions:wiA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:ha},{conditions:$iA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:ha},{endpoint:{url:ziA,properties:Rz,headers:Rz},type:ga}],type:ET},{conditions:[{[Oz]:CiA,[Tz]:qiA}],rules:[{conditions:[{[Oz]:"aws.partition",[Tz]:qiA,assign:g90}],rules:[{conditions:[LiA,MiA],rules:[{conditions:[{[Oz]:n41,[Tz]:[ViA,EiA]},UiA],rules:[{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Rz,headers:Rz},type:ga}],type:ET},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:ha}],type:ET},{conditions:wiA,rules:[{conditions:[{[Oz]:n41,[Tz]:[EiA,ViA]}],rules:[{conditions:[{[Oz]:"stringEquals",[Tz]:[{[Oz]:u90,[Tz]:[RiA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://portal.sso.{Region}.amazonaws.com",properties:Rz,headers:Rz},type:ga},{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dnsSuffix}",properties:Rz,headers:Rz},type:ga}],type:ET},{error:"FIPS is enabled but this partition does not support FIPS",type:ha}],type:ET},{conditions:$iA,rules:[{conditions:[UiA],rules:[{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Rz,headers:Rz},type:ga}],type:ET},{error:"DualStack is enabled but this partition does not support DualStack",type:ha}],type:ET},{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dnsSuffix}",properties:Rz,headers:Rz},type:ga}],type:ET}],type:ET},{error:"Invalid Configuration: Missing Region",type:ha}]};OiA.ruleSet=heQ});
var QfA=E((rF5,BfA)=>{var{create:vuQ,defineProperty:P41,getOwnPropertyDescriptor:buQ,getOwnPropertyNames:fuQ,getPrototypeOf:huQ}=Object,guQ=Object.prototype.hasOwnProperty,YT=(A,B)=>P41(A,"name",{value:B,configurable:!0}),uuQ=(A,B)=>{for(var Q in B)P41(A,Q,{get:B[Q],enumerable:!0})},obA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of fuQ(B))if(!guQ.call(A,Z)&&Z!==Q)P41(A,Z,{get:()=>B[Z],enumerable:!(D=buQ(B,Z))||D.enumerable})}return A},tbA=(A,B,Q)=>(Q=A!=null?vuQ(huQ(A)):{},obA(B||!A||!A.__esModule?P41(Q,"default",{value:A,enumerable:!0}):Q,A)),muQ=(A)=>obA(P41({},"__esModule",{value:!0}),A),ebA={};uuQ(ebA,{fromEnvSigningName:()=>luQ,fromSso:()=>AfA,fromStatic:()=>ouQ,nodeProvider:()=>tuQ});BfA.exports=muQ(ebA);var duQ=Lw(),cuQ=FA0(),eC=eB(),luQ=YT(({logger:A,signingName:B}={})=>async()=>{if(A?.debug?.("@aws-sdk/token-providers - fromEnvSigningName"),!B)throw new eC.TokenProviderError("Please pass 'signingName' to compute environment variable key",{logger:A});let Q=cuQ.getBearerTokenEnvKey(B);if(!(Q in process.env))throw new eC.TokenProviderError(`Token not present in '${Q}' environment variable`,{logger:A});let D={token:process.env[Q]};return duQ.setTokenFeature(D,"BEARER_SERVICE_ENV_VARS","3"),D},"fromEnvSigningName"),puQ=300000,oA0="To refresh this SSO session run 'aws sso login' with the corresponding profile.",iuQ=YT(async(A,B={})=>{let{SSOOIDCClient:Q}=await Promise.resolve().then(()=>tbA(rA0()));return new Q(Object.assign({},B.clientConfig??{},{region:A??B.clientConfig?.region,logger:B.clientConfig?.logger??B.parentClientConfig?.logger}))},"getSsoOidcClient"),nuQ=YT(async(A,B,Q={})=>{let{CreateTokenCommand:D}=await Promise.resolve().then(()=>tbA(rA0()));return(await iuQ(B,Q)).send(new D({clientId:A.clientId,clientSecret:A.clientSecret,refreshToken:A.refreshToken,grantType:"refresh_token"}))},"getNewSsoOidcToken"),sbA=YT((A)=>{if(A.expiration&&A.expiration.getTime()<Date.now())throw new eC.TokenProviderError(`Token is expired. ${oA0}`,!1)},"validateTokenExpiry"),th=YT((A,B,Q=!1)=>{if(typeof B==="undefined")throw new eC.TokenProviderError(`Value not present for '${A}' in SSO Token${Q?". Cannot refresh":""}. ${oA0}`,!1)},"validateTokenKey"),T41=e5(),auQ=J1("fs"),{writeFile:suQ}=auQ.promises,ruQ=YT((A,B)=>{let Q=T41.getSSOTokenFilepath(A),D=JSON.stringify(B,null,2);return suQ(Q,D)},"writeSSOTokenToFile"),rbA=new Date(0),AfA=YT((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/token-providers - fromSso");let D=await T41.parseKnownFiles(Q),Z=T41.getProfileName({profile:Q.profile??B?.profile}),G=D[Z];if(!G)throw new eC.TokenProviderError(`Profile '${Z}' could not be found in shared credentials file.`,!1);else if(!G.sso_session)throw new eC.TokenProviderError(`Profile '${Z}' is missing required property 'sso_session'.`);let F=G.sso_session,Y=(await T41.loadSsoSessionData(Q))[F];if(!Y)throw new eC.TokenProviderError(`Sso session '${F}' could not be found in shared credentials file.`,!1);for(let H of["sso_start_url","sso_region"])if(!Y[H])throw new eC.TokenProviderError(`Sso session '${F}' is missing required property '${H}'.`,!1);let{sso_start_url:W,sso_region:J}=Y,X;try{X=await T41.getSSOTokenFromFile(F)}catch(H){throw new eC.TokenProviderError(`The SSO session token associated with profile=${Z} was not found or is invalid. ${oA0}`,!1)}th("accessToken",X.accessToken),th("expiresAt",X.expiresAt);let{accessToken:V,expiresAt:C}=X,K={token:V,expiration:new Date(C)};if(K.expiration.getTime()-Date.now()>puQ)return K;if(Date.now()-rbA.getTime()<30000)return sbA(K),K;th("clientId",X.clientId,!0),th("clientSecret",X.clientSecret,!0),th("refreshToken",X.refreshToken,!0);try{rbA.setTime(Date.now());let H=await nuQ(X,J,Q);th("accessToken",H.accessToken),th("expiresIn",H.expiresIn);let z=new Date(Date.now()+H.expiresIn*1000);try{await ruQ(F,{...X,accessToken:H.accessToken,expiresAt:z.toISOString(),refreshToken:H.refreshToken})}catch($){}return{token:H.accessToken,expiration:z}}catch(H){return sbA(K),K}},"fromSso"),ouQ=YT(({token:A,logger:B})=>async()=>{if(B?.debug("@aws-sdk/token-providers - fromStatic"),!A||!A.token)throw new eC.TokenProviderError("Please pass a valid token to fromStatic",!1);return A},"fromStatic"),tuQ=YT((A={})=>eC.memoize(eC.chain(AfA(A),async()=>{throw new eC.TokenProviderError("Could not load token from any providers",!1)}),(B)=>B.expiration!==void 0&&B.expiration.getTime()-Date.now()<300000,(B)=>B.expiration!==void 0),"nodeProvider")});
var R$1=E((YW5,VpA)=>{var{defineProperty:M$1,getOwnPropertyDescriptor:aoQ,getOwnPropertyNames:soQ}=Object,roQ=Object.prototype.hasOwnProperty,ZL=(A,B)=>M$1(A,"name",{value:B,configurable:!0}),ooQ=(A,B)=>{for(var Q in B)M$1(A,Q,{get:B[Q],enumerable:!0})},toQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of soQ(B))if(!roQ.call(A,Z)&&Z!==Q)M$1(A,Z,{get:()=>B[Z],enumerable:!(D=aoQ(B,Z))||D.enumerable})}return A},eoQ=(A)=>toQ(M$1({},"__esModule",{value:!0}),A),YpA={};ooQ(YpA,{NODE_REGION_CONFIG_FILE_OPTIONS:()=>DtQ,NODE_REGION_CONFIG_OPTIONS:()=>QtQ,REGION_ENV_NAME:()=>WpA,REGION_INI_NAME:()=>JpA,getAwsRegionExtensionConfiguration:()=>AtQ,resolveAwsRegionExtensionConfiguration:()=>BtQ,resolveRegionConfig:()=>ZtQ});VpA.exports=eoQ(YpA);var AtQ=ZL((A)=>{return{setRegion(B){A.region=B},region(){return A.region}}},"getAwsRegionExtensionConfiguration"),BtQ=ZL((A)=>{return{region:A.region()}},"resolveAwsRegionExtensionConfiguration"),WpA="AWS_REGION",JpA="region",QtQ={environmentVariableSelector:ZL((A)=>A[WpA],"environmentVariableSelector"),configFileSelector:ZL((A)=>A[JpA],"configFileSelector"),default:ZL(()=>{throw new Error("Region is missing")},"default")},DtQ={preferredFile:"credentials"},XpA=ZL((A)=>typeof A==="string"&&(A.startsWith("fips-")||A.endsWith("-fips")),"isFipsRegion"),IpA=ZL((A)=>XpA(A)?["fips-aws-global","aws-fips"].includes(A)?"us-east-1":A.replace(/fips-(dkr-|prod-)?|-fips/,""):A,"getRealRegion"),ZtQ=ZL((A)=>{let{region:B,useFipsEndpoint:Q}=A;if(!B)throw new Error("Region is missing");return Object.assign(A,{region:ZL(async()=>{if(typeof B==="string")return IpA(B);let D=await B();return IpA(D)},"region"),useFipsEndpoint:ZL(async()=>{let D=typeof B==="string"?B:await B();if(XpA(D))return!0;return typeof Q!=="function"?Promise.resolve(!!Q):Q()},"useFipsEndpoint")})},"resolveRegionConfig")});
var Ry=E((pG5,Gw1)=>{var{defineProperty:V_A,getOwnPropertyDescriptor:zvQ,getOwnPropertyNames:EvQ}=Object,UvQ=Object.prototype.hasOwnProperty,IA0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of EvQ(B))if(!UvQ.call(A,Z)&&Z!==Q)V_A(A,Z,{get:()=>B[Z],enumerable:!(D=zvQ(B,Z))||D.enumerable})}return A},C_A=(A,B,Q)=>(IA0(A,B,"default"),Q&&IA0(Q,B,"default")),wvQ=(A)=>IA0(V_A({},"__esModule",{value:!0}),A),YA0={};Gw1.exports=wvQ(YA0);C_A(YA0,Y_A(),Gw1.exports);C_A(YA0,X_A(),Gw1.exports)});
var S$1=E((EW5,YiA)=>{var{defineProperty:P$1,getOwnPropertyDescriptor:UeQ,getOwnPropertyNames:weQ}=Object,$eQ=Object.prototype.hasOwnProperty,b90=(A,B)=>P$1(A,"name",{value:B,configurable:!0}),qeQ=(A,B)=>{for(var Q in B)P$1(A,Q,{get:B[Q],enumerable:!0})},NeQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of weQ(B))if(!$eQ.call(A,Z)&&Z!==Q)P$1(A,Z,{get:()=>B[Z],enumerable:!(D=UeQ(B,Z))||D.enumerable})}return A},LeQ=(A)=>NeQ(P$1({},"__esModule",{value:!0}),A),IiA={};qeQ(IiA,{fromProcess:()=>SeQ});YiA.exports=LeQ(IiA);var FiA=e5(),v90=eB(),MeQ=J1("child_process"),ReQ=J1("util"),OeQ=Mz(),TeQ=b90((A,B,Q)=>{if(B.Version!==1)throw Error(`Profile ${A} credential_process did not return Version 1.`);if(B.AccessKeyId===void 0||B.SecretAccessKey===void 0)throw Error(`Profile ${A} credential_process returned invalid credentials.`);if(B.Expiration){let G=new Date;if(new Date(B.Expiration)<G)throw Error(`Profile ${A} credential_process returned expired credentials.`)}let D=B.AccountId;if(!D&&Q?.[A]?.aws_account_id)D=Q[A].aws_account_id;let Z={accessKeyId:B.AccessKeyId,secretAccessKey:B.SecretAccessKey,...B.SessionToken&&{sessionToken:B.SessionToken},...B.Expiration&&{expiration:new Date(B.Expiration)},...B.CredentialScope&&{credentialScope:B.CredentialScope},...D&&{accountId:D}};return OeQ.setCredentialFeature(Z,"CREDENTIALS_PROCESS","w"),Z},"getValidatedProcessCredentials"),PeQ=b90(async(A,B,Q)=>{let D=B[A];if(B[A]){let Z=D.credential_process;if(Z!==void 0){let G=ReQ.promisify(MeQ.exec);try{let{stdout:F}=await G(Z),I;try{I=JSON.parse(F.trim())}catch{throw Error(`Profile ${A} credential_process returned invalid JSON.`)}return TeQ(A,I,B)}catch(F){throw new v90.CredentialsProviderError(F.message,{logger:Q})}}else throw new v90.CredentialsProviderError(`Profile ${A} did not contain credential_process.`,{logger:Q})}else throw new v90.CredentialsProviderError(`Profile ${A} could not be found in shared credentials file.`,{logger:Q})},"resolveProcessCredentials"),SeQ=b90((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-process - fromProcess");let Q=await FiA.parseKnownFiles(A);return PeQ(FiA.getProfileName({profile:A.profile??B?.profile}),Q,A.logger)},"fromProcess")});
var SA0=E((r_A)=>{Object.defineProperty(r_A,"__esModule",{value:!0});r_A.resolveHttpAuthSchemeConfig=r_A.defaultCognitoIdentityHttpAuthSchemeProvider=r_A.defaultCognitoIdentityHttpAuthSchemeParametersProvider=void 0;var tbQ=II(),PA0=I5(),ebQ=async(A,B,Q)=>{return{operation:PA0.getSmithyContext(B).operation,region:await PA0.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};r_A.defaultCognitoIdentityHttpAuthSchemeParametersProvider=ebQ;function AfQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"cognito-identity",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function Xw1(A){return{schemeId:"smithy.api#noAuth"}}var BfQ=(A)=>{let B=[];switch(A.operation){case"GetCredentialsForIdentity":{B.push(Xw1(A));break}case"GetId":{B.push(Xw1(A));break}case"GetOpenIdToken":{B.push(Xw1(A));break}case"UnlinkIdentity":{B.push(Xw1(A));break}default:B.push(AfQ(A))}return B};r_A.defaultCognitoIdentityHttpAuthSchemeProvider=BfQ;var QfQ=(A)=>{let B=tbQ.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:PA0.normalizeProvider(A.authSchemePreference??[])})};r_A.resolveHttpAuthSchemeConfig=QfQ});
var T$1=E((CW5,x90)=>{var{defineProperty:O$1,getOwnPropertyDescriptor:EtQ,getOwnPropertyNames:UtQ}=Object,wtQ=Object.prototype.hasOwnProperty,Q9=(A,B)=>O$1(A,"name",{value:B,configurable:!0}),$tQ=(A,B)=>{for(var Q in B)O$1(A,Q,{get:B[Q],enumerable:!0})},T90=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of UtQ(B))if(!wtQ.call(A,Z)&&Z!==Q)O$1(A,Z,{get:()=>B[Z],enumerable:!(D=EtQ(B,Z))||D.enumerable})}return A},qtQ=(A,B,Q)=>(T90(A,B,"default"),Q&&T90(Q,B,"default")),NtQ=(A)=>T90(O$1({},"__esModule",{value:!0}),A),S90={};$tQ(S90,{AssumeRoleCommand:()=>y90,AssumeRoleResponseFilterSensitiveLog:()=>ypA,AssumeRoleWithWebIdentityCommand:()=>_90,AssumeRoleWithWebIdentityRequestFilterSensitiveLog:()=>gpA,AssumeRoleWithWebIdentityResponseFilterSensitiveLog:()=>upA,ClientInputEndpointParameters:()=>KeQ.ClientInputEndpointParameters,CredentialsFilterSensitiveLog:()=>j90,ExpiredTokenException:()=>_pA,IDPCommunicationErrorException:()=>mpA,IDPRejectedClaimException:()=>fpA,InvalidIdentityTokenException:()=>hpA,MalformedPolicyDocumentException:()=>xpA,PackedPolicyTooLargeException:()=>vpA,RegionDisabledException:()=>bpA,STS:()=>tpA,STSServiceException:()=>HT,decorateDefaultCredentialProvider:()=>EeQ,getDefaultRoleAssumer:()=>ZiA,getDefaultRoleAssumerWithWebIdentity:()=>GiA});x90.exports=NtQ(S90);qtQ(S90,p41(),x90.exports);var LtQ=H8(),MtQ=q6(),RtQ=T3(),OtQ=H8(),TtQ=i41(),kpA=H8(),PtQ=H8(),HT=class A extends PtQ.ServiceException{static{Q9(this,"STSServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},j90=Q9((A)=>({...A,...A.SecretAccessKey&&{SecretAccessKey:kpA.SENSITIVE_STRING}}),"CredentialsFilterSensitiveLog"),ypA=Q9((A)=>({...A,...A.Credentials&&{Credentials:j90(A.Credentials)}}),"AssumeRoleResponseFilterSensitiveLog"),_pA=class A extends HT{static{Q9(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},xpA=class A extends HT{static{Q9(this,"MalformedPolicyDocumentException")}name="MalformedPolicyDocumentException";$fault="client";constructor(B){super({name:"MalformedPolicyDocumentException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},vpA=class A extends HT{static{Q9(this,"PackedPolicyTooLargeException")}name="PackedPolicyTooLargeException";$fault="client";constructor(B){super({name:"PackedPolicyTooLargeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},bpA=class A extends HT{static{Q9(this,"RegionDisabledException")}name="RegionDisabledException";$fault="client";constructor(B){super({name:"RegionDisabledException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},fpA=class A extends HT{static{Q9(this,"IDPRejectedClaimException")}name="IDPRejectedClaimException";$fault="client";constructor(B){super({name:"IDPRejectedClaimException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},hpA=class A extends HT{static{Q9(this,"InvalidIdentityTokenException")}name="InvalidIdentityTokenException";$fault="client";constructor(B){super({name:"InvalidIdentityTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},gpA=Q9((A)=>({...A,...A.WebIdentityToken&&{WebIdentityToken:kpA.SENSITIVE_STRING}}),"AssumeRoleWithWebIdentityRequestFilterSensitiveLog"),upA=Q9((A)=>({...A,...A.Credentials&&{Credentials:j90(A.Credentials)}}),"AssumeRoleWithWebIdentityResponseFilterSensitiveLog"),mpA=class A extends HT{static{Q9(this,"IDPCommunicationErrorException")}name="IDPCommunicationErrorException";$fault="client";constructor(B){super({name:"IDPCommunicationErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},k90=XV(),StQ=DK(),J5=H8(),jtQ=Q9(async(A,B)=>{let Q=npA,D;return D=opA({...mtQ(A,B),[spA]:GeQ,[rpA]:apA}),ipA(B,Q,"/",void 0,D)},"se_AssumeRoleCommand"),ktQ=Q9(async(A,B)=>{let Q=npA,D;return D=opA({...dtQ(A,B),[spA]:FeQ,[rpA]:apA}),ipA(B,Q,"/",void 0,D)},"se_AssumeRoleWithWebIdentityCommand"),ytQ=Q9(async(A,B)=>{if(A.statusCode>=300)return dpA(A,B);let Q=await k90.parseXmlBody(A.body,B),D={};return D=stQ(Q.AssumeRoleResult,B),{$metadata:zT(A),...D}},"de_AssumeRoleCommand"),_tQ=Q9(async(A,B)=>{if(A.statusCode>=300)return dpA(A,B);let Q=await k90.parseXmlBody(A.body,B),D={};return D=rtQ(Q.AssumeRoleWithWebIdentityResult,B),{$metadata:zT(A),...D}},"de_AssumeRoleWithWebIdentityCommand"),dpA=Q9(async(A,B)=>{let Q={...A,body:await k90.parseXmlErrorBody(A.body,B)},D=IeQ(A,Q.body);switch(D){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await xtQ(Q,B);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await htQ(Q,B);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await gtQ(Q,B);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await utQ(Q,B);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await vtQ(Q,B);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await btQ(Q,B);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await ftQ(Q,B);default:let Z=Q.body;return ZeQ({output:A,parsedBody:Z.Error,errorCode:D})}},"de_CommandError"),xtQ=Q9(async(A,B)=>{let Q=A.body,D=otQ(Q.Error,B),Z=new _pA({$metadata:zT(A),...D});return J5.decorateServiceException(Z,Q)},"de_ExpiredTokenExceptionRes"),vtQ=Q9(async(A,B)=>{let Q=A.body,D=ttQ(Q.Error,B),Z=new mpA({$metadata:zT(A),...D});return J5.decorateServiceException(Z,Q)},"de_IDPCommunicationErrorExceptionRes"),btQ=Q9(async(A,B)=>{let Q=A.body,D=etQ(Q.Error,B),Z=new fpA({$metadata:zT(A),...D});return J5.decorateServiceException(Z,Q)},"de_IDPRejectedClaimExceptionRes"),ftQ=Q9(async(A,B)=>{let Q=A.body,D=AeQ(Q.Error,B),Z=new hpA({$metadata:zT(A),...D});return J5.decorateServiceException(Z,Q)},"de_InvalidIdentityTokenExceptionRes"),htQ=Q9(async(A,B)=>{let Q=A.body,D=BeQ(Q.Error,B),Z=new xpA({$metadata:zT(A),...D});return J5.decorateServiceException(Z,Q)},"de_MalformedPolicyDocumentExceptionRes"),gtQ=Q9(async(A,B)=>{let Q=A.body,D=QeQ(Q.Error,B),Z=new vpA({$metadata:zT(A),...D});return J5.decorateServiceException(Z,Q)},"de_PackedPolicyTooLargeExceptionRes"),utQ=Q9(async(A,B)=>{let Q=A.body,D=DeQ(Q.Error,B),Z=new bpA({$metadata:zT(A),...D});return J5.decorateServiceException(Z,Q)},"de_RegionDisabledExceptionRes"),mtQ=Q9((A,B)=>{let Q={};if(A[ba]!=null)Q[ba]=A[ba];if(A[fa]!=null)Q[fa]=A[fa];if(A[xa]!=null){let D=cpA(A[xa],B);if(A[xa]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[_a]!=null)Q[_a]=A[_a];if(A[ya]!=null)Q[ya]=A[ya];if(A[q90]!=null){let D=atQ(A[q90],B);if(A[q90]?.length===0)Q.Tags=[];Object.entries(D).forEach(([Z,G])=>{let F=`Tags.${Z}`;Q[F]=G})}if(A[L90]!=null){let D=ntQ(A[L90],B);if(A[L90]?.length===0)Q.TransitiveTagKeys=[];Object.entries(D).forEach(([Z,G])=>{let F=`TransitiveTagKeys.${Z}`;Q[F]=G})}if(A[X90]!=null)Q[X90]=A[X90];if(A[w90]!=null)Q[w90]=A[w90];if(A[N90]!=null)Q[N90]=A[N90];if(A[KT]!=null)Q[KT]=A[KT];if(A[K90]!=null){let D=ptQ(A[K90],B);if(A[K90]?.length===0)Q.ProvidedContexts=[];Object.entries(D).forEach(([Z,G])=>{let F=`ProvidedContexts.${Z}`;Q[F]=G})}return Q},"se_AssumeRoleRequest"),dtQ=Q9((A,B)=>{let Q={};if(A[ba]!=null)Q[ba]=A[ba];if(A[fa]!=null)Q[fa]=A[fa];if(A[R90]!=null)Q[R90]=A[R90];if(A[H90]!=null)Q[H90]=A[H90];if(A[xa]!=null){let D=cpA(A[xa],B);if(A[xa]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[_a]!=null)Q[_a]=A[_a];if(A[ya]!=null)Q[ya]=A[ya];return Q},"se_AssumeRoleWithWebIdentityRequest"),cpA=Q9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=ctQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_policyDescriptorListType"),ctQ=Q9((A,B)=>{let Q={};if(A[O90]!=null)Q[O90]=A[O90];return Q},"se_PolicyDescriptorType"),ltQ=Q9((A,B)=>{let Q={};if(A[C90]!=null)Q[C90]=A[C90];if(A[W90]!=null)Q[W90]=A[W90];return Q},"se_ProvidedContext"),ptQ=Q9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=ltQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_ProvidedContextsListType"),itQ=Q9((A,B)=>{let Q={};if(A[V90]!=null)Q[V90]=A[V90];if(A[M90]!=null)Q[M90]=A[M90];return Q},"se_Tag"),ntQ=Q9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;Q[`member.${D}`]=Z,D++}return Q},"se_tagKeyListType"),atQ=Q9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=itQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_tagListType"),lpA=Q9((A,B)=>{let Q={};if(A[F90]!=null)Q[F90]=J5.expectString(A[F90]);if(A[I90]!=null)Q[I90]=J5.expectString(A[I90]);return Q},"de_AssumedRoleUser"),stQ=Q9((A,B)=>{let Q={};if(A[ka]!=null)Q[ka]=ppA(A[ka],B);if(A[ja]!=null)Q[ja]=lpA(A[ja],B);if(A[va]!=null)Q[va]=J5.strictParseInt32(A[va]);if(A[KT]!=null)Q[KT]=J5.expectString(A[KT]);return Q},"de_AssumeRoleResponse"),rtQ=Q9((A,B)=>{let Q={};if(A[ka]!=null)Q[ka]=ppA(A[ka],B);if(A[U90]!=null)Q[U90]=J5.expectString(A[U90]);if(A[ja]!=null)Q[ja]=lpA(A[ja],B);if(A[va]!=null)Q[va]=J5.strictParseInt32(A[va]);if(A[z90]!=null)Q[z90]=J5.expectString(A[z90]);if(A[Y90]!=null)Q[Y90]=J5.expectString(A[Y90]);if(A[KT]!=null)Q[KT]=J5.expectString(A[KT]);return Q},"de_AssumeRoleWithWebIdentityResponse"),ppA=Q9((A,B)=>{let Q={};if(A[G90]!=null)Q[G90]=J5.expectString(A[G90]);if(A[E90]!=null)Q[E90]=J5.expectString(A[E90]);if(A[$90]!=null)Q[$90]=J5.expectString(A[$90]);if(A[J90]!=null)Q[J90]=J5.expectNonNull(J5.parseRfc3339DateTimeWithOffset(A[J90]));return Q},"de_Credentials"),otQ=Q9((A,B)=>{let Q={};if(A[lZ]!=null)Q[lZ]=J5.expectString(A[lZ]);return Q},"de_ExpiredTokenException"),ttQ=Q9((A,B)=>{let Q={};if(A[lZ]!=null)Q[lZ]=J5.expectString(A[lZ]);return Q},"de_IDPCommunicationErrorException"),etQ=Q9((A,B)=>{let Q={};if(A[lZ]!=null)Q[lZ]=J5.expectString(A[lZ]);return Q},"de_IDPRejectedClaimException"),AeQ=Q9((A,B)=>{let Q={};if(A[lZ]!=null)Q[lZ]=J5.expectString(A[lZ]);return Q},"de_InvalidIdentityTokenException"),BeQ=Q9((A,B)=>{let Q={};if(A[lZ]!=null)Q[lZ]=J5.expectString(A[lZ]);return Q},"de_MalformedPolicyDocumentException"),QeQ=Q9((A,B)=>{let Q={};if(A[lZ]!=null)Q[lZ]=J5.expectString(A[lZ]);return Q},"de_PackedPolicyTooLargeException"),DeQ=Q9((A,B)=>{let Q={};if(A[lZ]!=null)Q[lZ]=J5.expectString(A[lZ]);return Q},"de_RegionDisabledException"),zT=Q9((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),ZeQ=J5.withBaseException(HT),ipA=Q9(async(A,B,Q,D,Z)=>{let{hostname:G,protocol:F="https",port:I,path:Y}=await A.endpoint(),W={protocol:F,hostname:G,port:I,method:"POST",path:Y.endsWith("/")?Y.slice(0,-1)+Q:Y+Q,headers:B};if(D!==void 0)W.hostname=D;if(Z!==void 0)W.body=Z;return new StQ.HttpRequest(W)},"buildHttpRpcRequest"),npA={"content-type":"application/x-www-form-urlencoded"},apA="2011-06-15",spA="Action",G90="AccessKeyId",GeQ="AssumeRole",F90="AssumedRoleId",ja="AssumedRoleUser",FeQ="AssumeRoleWithWebIdentity",I90="Arn",Y90="Audience",ka="Credentials",W90="ContextAssertion",ya="DurationSeconds",J90="Expiration",X90="ExternalId",V90="Key",_a="Policy",xa="PolicyArns",C90="ProviderArn",K90="ProvidedContexts",H90="ProviderId",va="PackedPolicySize",z90="Provider",ba="RoleArn",fa="RoleSessionName",E90="SecretAccessKey",U90="SubjectFromWebIdentityToken",KT="SourceIdentity",w90="SerialNumber",$90="SessionToken",q90="Tags",N90="TokenCode",L90="TransitiveTagKeys",rpA="Version",M90="Value",R90="WebIdentityToken",O90="arn",lZ="message",opA=Q9((A)=>Object.entries(A).map(([B,Q])=>J5.extendedEncodeURIComponent(B)+"="+J5.extendedEncodeURIComponent(Q)).join("&"),"buildFormUrlencodedString"),IeQ=Q9((A,B)=>{if(B.Error?.Code!==void 0)return B.Error.Code;if(A.statusCode==404)return"NotFound"},"loadQueryErrorCode"),y90=class extends OtQ.Command.classBuilder().ep(TtQ.commonParams).m(function(A,B,Q,D){return[RtQ.getSerdePlugin(Q,this.serialize,this.deserialize),MtQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRole",{}).n("STSClient","AssumeRoleCommand").f(void 0,ypA).ser(jtQ).de(ytQ).build(){static{Q9(this,"AssumeRoleCommand")}},YeQ=q6(),WeQ=T3(),JeQ=H8(),XeQ=i41(),_90=class extends JeQ.Command.classBuilder().ep(XeQ.commonParams).m(function(A,B,Q,D){return[WeQ.getSerdePlugin(Q,this.serialize,this.deserialize),YeQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithWebIdentity",{}).n("STSClient","AssumeRoleWithWebIdentityCommand").f(gpA,upA).ser(ktQ).de(_tQ).build(){static{Q9(this,"AssumeRoleWithWebIdentityCommand")}},VeQ=p41(),CeQ={AssumeRoleCommand:y90,AssumeRoleWithWebIdentityCommand:_90},tpA=class extends VeQ.STSClient{static{Q9(this,"STS")}};LtQ.createAggregatedClient(CeQ,tpA);var KeQ=i41(),P90=Mz(),jpA="us-east-1",epA=Q9((A)=>{if(typeof A?.Arn==="string"){let B=A.Arn.split(":");if(B.length>4&&B[4]!=="")return B[4]}return},"getAccountIdFromAssumedRoleUser"),AiA=Q9(async(A,B,Q)=>{let D=typeof A==="function"?await A():A,Z=typeof B==="function"?await B():B;return Q?.debug?.("@aws-sdk/client-sts::resolveRegion","accepting first of:",`${D} (provider)`,`${Z} (parent client)`,`${jpA} (STS default)`),D??Z??jpA},"resolveRegion"),HeQ=Q9((A,B)=>{let Q,D;return async(Z,G)=>{if(D=Z,!Q){let{logger:J=A?.parentClientConfig?.logger,region:X,requestHandler:V=A?.parentClientConfig?.requestHandler,credentialProviderLogger:C}=A,K=await AiA(X,A?.parentClientConfig?.region,C),H=!BiA(V);Q=new B({profile:A?.parentClientConfig?.profile,credentialDefaultProvider:Q9(()=>async()=>D,"credentialDefaultProvider"),region:K,requestHandler:H?V:void 0,logger:J})}let{Credentials:F,AssumedRoleUser:I}=await Q.send(new y90(G));if(!F||!F.AccessKeyId||!F.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRole call with role ${G.RoleArn}`);let Y=epA(I),W={accessKeyId:F.AccessKeyId,secretAccessKey:F.SecretAccessKey,sessionToken:F.SessionToken,expiration:F.Expiration,...F.CredentialScope&&{credentialScope:F.CredentialScope},...Y&&{accountId:Y}};return P90.setCredentialFeature(W,"CREDENTIALS_STS_ASSUME_ROLE","i"),W}},"getDefaultRoleAssumer"),zeQ=Q9((A,B)=>{let Q;return async(D)=>{if(!Q){let{logger:Y=A?.parentClientConfig?.logger,region:W,requestHandler:J=A?.parentClientConfig?.requestHandler,credentialProviderLogger:X}=A,V=await AiA(W,A?.parentClientConfig?.region,X),C=!BiA(J);Q=new B({profile:A?.parentClientConfig?.profile,region:V,requestHandler:C?J:void 0,logger:Y})}let{Credentials:Z,AssumedRoleUser:G}=await Q.send(new _90(D));if(!Z||!Z.AccessKeyId||!Z.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${D.RoleArn}`);let F=epA(G),I={accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.SessionToken,expiration:Z.Expiration,...Z.CredentialScope&&{credentialScope:Z.CredentialScope},...F&&{accountId:F}};if(F)P90.setCredentialFeature(I,"RESOLVED_ACCOUNT_ID","T");return P90.setCredentialFeature(I,"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID","k"),I}},"getDefaultRoleAssumerWithWebIdentity"),BiA=Q9((A)=>{return A?.metadata?.handlerProtocol==="h2"},"isH2"),QiA=p41(),DiA=Q9((A,B)=>{if(!B)return A;else return class Q extends A{static{Q9(this,"CustomizableSTSClient")}constructor(D){super(D);for(let Z of B)this.middlewareStack.use(Z)}}},"getCustomizableStsClientCtor"),ZiA=Q9((A={},B)=>HeQ(A,DiA(QiA.STSClient,B)),"getDefaultRoleAssumer"),GiA=Q9((A={},B)=>zeQ(A,DiA(QiA.STSClient,B)),"getDefaultRoleAssumerWithWebIdentity"),EeQ=Q9((A)=>(B)=>A({roleAssumer:ZiA(B),roleAssumerWithWebIdentity:GiA(B),...B}),"decorateDefaultCredentialProvider")});
var TsA=E((FL)=>{var T24=FL&&FL.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),P24=FL&&FL.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),S24=FL&&FL.__importStar||function(){var A=function(B){return A=Object.getOwnPropertyNames||function(Q){var D=[];for(var Z in Q)if(Object.prototype.hasOwnProperty.call(Q,Z))D[D.length]=Z;return D},A(B)};return function(B){if(B&&B.__esModule)return B;var Q={};if(B!=null){for(var D=A(B),Z=0;Z<D.length;Z++)if(D[Z]!=="default")T24(Q,B,D[Z])}return P24(Q,B),Q}}();Object.defineProperty(FL,"__esModule",{value:!0});FL.fromTemporaryCredentials=void 0;var j24=VB(),RsA=eB(),k24="us-east-1",y24=(A,B,Q)=>{let D;return async(Z={})=>{let{callerClientConfig:G}=Z,F=A.clientConfig?.profile??G?.profile,I=A.logger??G?.logger;I?.debug("@aws-sdk/credential-providers - fromTemporaryCredentials (STS)");let Y={...A.params,RoleSessionName:A.params.RoleSessionName??"aws-sdk-js-"+Date.now()};if(Y?.SerialNumber){if(!A.mfaCodeProvider)throw new RsA.CredentialsProviderError("Temporary credential requires multi-factor authentication, but no MFA code callback was provided.",{tryNextLink:!1,logger:I});Y.TokenCode=await A.mfaCodeProvider(Y?.SerialNumber)}let{AssumeRoleCommand:W,STSClient:J}=await Promise.resolve().then(()=>S24(MsA()));if(!D){let V=typeof B==="function"?B():void 0,C=[A.masterCredentials,A.clientConfig?.credentials,void G?.credentials,G?.credentialDefaultProvider?.(),V],K="STS client default credentials";if(C[0])K="options.masterCredentials";else if(C[1])K="options.clientConfig.credentials";else if(C[2])throw K="caller client's credentials",new Error("fromTemporaryCredentials recursion in callerClientConfig.credentials");else if(C[3])K="caller client's credentialDefaultProvider";else if(C[4])K="AWS SDK default credentials";let H=[A.clientConfig?.region,G?.region,await Q?.({profile:F}),k24],z="default partition's default region";if(H[0])z="options.clientConfig.region";else if(H[1])z="caller client's region";else if(H[2])z="file or env region";let $=[OsA(A.clientConfig?.requestHandler),OsA(G?.requestHandler)],L="STS default requestHandler";if($[0])L="options.clientConfig.requestHandler";else if($[1])L="caller client's requestHandler";I?.debug?.(`@aws-sdk/credential-providers - fromTemporaryCredentials STS client init with ${z}=${await j24.normalizeProvider(a$1(H))()}, ${K}, ${L}.`),D=new J({...A.clientConfig,credentials:a$1(C),logger:I,profile:F,region:a$1(H),requestHandler:a$1($)})}if(A.clientPlugins)for(let V of A.clientPlugins)D.middlewareStack.use(V);let{Credentials:X}=await D.send(new W(Y));if(!X||!X.AccessKeyId||!X.SecretAccessKey)throw new RsA.CredentialsProviderError(`Invalid response from STS.assumeRole call with role ${Y.RoleArn}`,{logger:I});return{accessKeyId:X.AccessKeyId,secretAccessKey:X.SecretAccessKey,sessionToken:X.SessionToken,expiration:X.Expiration,credentialScope:X.CredentialScope}}};FL.fromTemporaryCredentials=y24;var OsA=(A)=>{return A?.metadata?.handlerProtocol==="h2"?void 0:A},a$1=(A)=>{for(let B of A)if(B!==void 0)return B}});
var UgA=E((EI5,EgA)=>{var{create:ncQ,defineProperty:x41,getOwnPropertyDescriptor:acQ,getOwnPropertyNames:scQ,getPrototypeOf:rcQ}=Object,ocQ=Object.prototype.hasOwnProperty,xw1=(A,B)=>x41(A,"name",{value:B,configurable:!0}),tcQ=(A,B)=>{for(var Q in B)x41(A,Q,{get:B[Q],enumerable:!0})},CgA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of scQ(B))if(!ocQ.call(A,Z)&&Z!==Q)x41(A,Z,{get:()=>B[Z],enumerable:!(D=acQ(B,Z))||D.enumerable})}return A},qa=(A,B,Q)=>(Q=A!=null?ncQ(rcQ(A)):{},CgA(B||!A||!A.__esModule?x41(Q,"default",{value:A,enumerable:!0}):Q,A)),ecQ=(A)=>CgA(x41({},"__esModule",{value:!0}),A),KgA={};tcQ(KgA,{credentialsTreatedAsExpired:()=>zgA,credentialsWillNeedRefresh:()=>HgA,defaultProvider:()=>QlQ});EgA.exports=ecQ(KgA);var s20=jA0(),AlQ=e5(),Ag=eB(),XgA="AWS_EC2_METADATA_DISABLED",BlQ=xw1(async(A)=>{let{ENV_CMDS_FULL_URI:B,ENV_CMDS_RELATIVE_URI:Q,fromContainerMetadata:D,fromInstanceMetadata:Z}=await Promise.resolve().then(()=>qa($F()));if(process.env[Q]||process.env[B]){A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromHttp/fromContainerMetadata");let{fromHttp:G}=await Promise.resolve().then(()=>qa(_A0()));return Ag.chain(G(A),D(A))}if(process.env[XgA]&&process.env[XgA]!=="false")return async()=>{throw new Ag.CredentialsProviderError("EC2 Instance Metadata Service access disabled",{logger:A.logger})};return A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromInstanceMetadata"),Z(A)},"remoteProvider"),VgA=!1,QlQ=xw1((A={})=>Ag.memoize(Ag.chain(async()=>{if(A.profile??process.env[AlQ.ENV_PROFILE]){if(process.env[s20.ENV_KEY]&&process.env[s20.ENV_SECRET]){if(!VgA)(A.logger?.warn&&A.logger?.constructor?.name!=="NoOpLogger"?A.logger.warn:console.warn)(`@aws-sdk/credential-provider-node - defaultProvider::fromEnv WARNING:
    Multiple credential sources detected: 
    Both AWS_PROFILE and the pair AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY static credentials are set.
    This SDK will proceed with the AWS_PROFILE value.
    
    However, a future version may change this behavior to prefer the ENV static credentials.
    Please ensure that your environment only sets either the AWS_PROFILE or the
    AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY pair.
`),VgA=!0}throw new Ag.CredentialsProviderError("AWS_PROFILE is set, skipping fromEnv provider.",{logger:A.logger,tryNextLink:!0})}return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromEnv"),s20.fromEnv(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromSSO");let{ssoStartUrl:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoSession:G}=A;if(!B&&!Q&&!D&&!Z&&!G)throw new Ag.CredentialsProviderError("Skipping SSO provider in default chain (inputs do not include SSO fields).",{logger:A.logger});let{fromSSO:F}=await Promise.resolve().then(()=>qa(eA0()));return F(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromIni");let{fromIni:B}=await Promise.resolve().then(()=>qa(JgA()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromProcess");let{fromProcess:B}=await Promise.resolve().then(()=>qa(d20()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromTokenFile");let{fromTokenFile:B}=await Promise.resolve().then(()=>qa(i20()));return B(A)()},async()=>{return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::remoteProvider"),(await BlQ(A))()},async()=>{throw new Ag.CredentialsProviderError("Could not load credentials from any providers",{tryNextLink:!1,logger:A.logger})}),zgA,HgA),"defaultProvider"),HgA=xw1((A)=>A?.expiration!==void 0,"credentialsWillNeedRefresh"),zgA=xw1((A)=>A?.expiration!==void 0&&A.expiration.getTime()-Date.now()<300000,"credentialsTreatedAsExpired")});
var UyA=E((vG5,EyA)=>{var{defineProperty:rU1,getOwnPropertyDescriptor:zxQ,getOwnPropertyNames:ExQ}=Object,UxQ=Object.prototype.hasOwnProperty,n00=(A,B)=>rU1(A,"name",{value:B,configurable:!0}),wxQ=(A,B)=>{for(var Q in B)rU1(A,Q,{get:B[Q],enumerable:!0})},$xQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ExQ(B))if(!UxQ.call(A,Z)&&Z!==Q)rU1(A,Z,{get:()=>B[Z],enumerable:!(D=zxQ(B,Z))||D.enumerable})}return A},qxQ=(A)=>$xQ(rU1({},"__esModule",{value:!0}),A),HyA={};wxQ(HyA,{escapeUri:()=>zyA,escapeUriPath:()=>LxQ});EyA.exports=qxQ(HyA);var zyA=n00((A)=>encodeURIComponent(A).replace(/[!'()*]/g,NxQ),"escapeUri"),NxQ=n00((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),LxQ=n00((A)=>A.split("/").map(zyA).join("/"),"escapeUriPath")});
var W6=E((iG5,HA0)=>{var{defineProperty:Fw1,getOwnPropertyDescriptor:$vQ,getOwnPropertyNames:qvQ}=Object,NvQ=Object.prototype.hasOwnProperty,C8=(A,B)=>Fw1(A,"name",{value:B,configurable:!0}),LvQ=(A,B)=>{for(var Q in B)Fw1(A,Q,{get:B[Q],enumerable:!0})},JA0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of qvQ(B))if(!NvQ.call(A,Z)&&Z!==Q)Fw1(A,Z,{get:()=>B[Z],enumerable:!(D=$vQ(B,Z))||D.enumerable})}return A},MvQ=(A,B,Q)=>(JA0(A,B,"default"),Q&&JA0(Q,B,"default")),RvQ=(A)=>JA0(Fw1({},"__esModule",{value:!0}),A),CA0={};LvQ(CA0,{Client:()=>OvQ,Command:()=>z_A,NoOpLogger:()=>cvQ,SENSITIVE_STRING:()=>PvQ,ServiceException:()=>jvQ,_json:()=>VA0,collectBody:()=>WA0.collectBody,convertMap:()=>lvQ,createAggregatedClient:()=>SvQ,decorateServiceException:()=>E_A,emitWarningIfUnsupportedVersion:()=>xvQ,extendedEncodeURIComponent:()=>WA0.extendedEncodeURIComponent,getArrayIfSingleItem:()=>mvQ,getDefaultClientConfiguration:()=>gvQ,getDefaultExtensionConfiguration:()=>w_A,getValueFromTextNode:()=>$_A,isSerializableHeaderValue:()=>dvQ,loadConfigsForDefaultMode:()=>_vQ,map:()=>KA0,resolveDefaultRuntimeConfig:()=>uvQ,resolvedPath:()=>WA0.resolvedPath,serializeDateTime:()=>rvQ,serializeFloat:()=>svQ,take:()=>pvQ,throwDefaultError:()=>U_A,withBaseException:()=>kvQ});HA0.exports=RvQ(CA0);var H_A=Uw(),OvQ=class{constructor(A){this.config=A,this.middlewareStack=H_A.constructStack()}static{C8(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},WA0=$6(),XA0=d00(),z_A=class{constructor(){this.middlewareStack=H_A.constructStack()}static{C8(this,"Command")}static classBuilder(){return new TvQ}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[XA0.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},TvQ=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{C8(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}sc(A){return this._operationSchema=A,this._smithyContext.operationSchema=A,this}build(){let A=this,B;return B=class extends z_A{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this),this.schema=A._operationSchema}static{C8(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},PvQ="***SensitiveInformation***",SvQ=C8((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=C8(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),jvQ=class A extends Error{static{C8(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},E_A=C8((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),U_A=C8(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=yvQ(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw E_A(F,B)},"throwDefaultError"),kvQ=C8((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{U_A({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),yvQ=C8((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),_vQ=C8((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),K_A=!1,xvQ=C8((A)=>{if(A&&!K_A&&parseInt(A.substring(1,A.indexOf(".")))<16)K_A=!0},"emitWarningIfUnsupportedVersion"),vvQ=C8((A)=>{let B=[];for(let Q in XA0.AlgorithmId){let D=XA0.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),bvQ=C8((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),fvQ=C8((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),hvQ=C8((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),w_A=C8((A)=>{return Object.assign(vvQ(A),fvQ(A))},"getDefaultExtensionConfiguration"),gvQ=w_A,uvQ=C8((A)=>{return Object.assign(bvQ(A),hvQ(A))},"resolveDefaultRuntimeConfig"),mvQ=C8((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),$_A=C8((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=$_A(A[Q]);return A},"getValueFromTextNode"),dvQ=C8((A)=>{return A!=null},"isSerializableHeaderValue"),cvQ=class{static{C8(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function KA0(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,ivQ(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}q_A(D,null,G,F)}return D}C8(KA0,"map");var lvQ=C8((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),pvQ=C8((A,B)=>{let Q={};for(let D in B)q_A(Q,A,B,D);return Q},"take"),ivQ=C8((A,B,Q)=>{return KA0(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),q_A=C8((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=nvQ,Y=avQ,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),nvQ=C8((A)=>A!=null,"nonNullish"),avQ=C8((A)=>A,"pass"),svQ=C8((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),rvQ=C8((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),VA0=C8((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(VA0);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=VA0(A[Q])}return B}return A},"_json");MvQ(CA0,Y6(),HA0.exports)});
var WbA=E((IbA)=>{Object.defineProperty(IbA,"__esModule",{value:!0});IbA.defaultEndpointResolver=void 0;var jgQ=on(),nA0=$7(),kgQ=FbA(),ygQ=new nA0.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),_gQ=(A,B={})=>{return ygQ.get(A,()=>nA0.resolveEndpoint(kgQ.ruleSet,{endpointParams:A,logger:B.logger}))};IbA.defaultEndpointResolver=_gQ;nA0.customEndpointFunctions.aws=jgQ.awsEndpointFunctions});
var WxA=E((IxA)=>{Object.defineProperty(IxA,"__esModule",{value:!0});IxA.checkUrl=void 0;var zfQ=eB(),EfQ="*************",UfQ="*************3",wfQ="[fd00:ec2::23]",$fQ=(A,B)=>{if(A.protocol==="https:")return;if(A.hostname===EfQ||A.hostname===UfQ||A.hostname===wfQ)return;if(A.hostname.includes("[")){if(A.hostname==="[::1]"||A.hostname==="[0000:0000:0000:0000:0000:0000:0000:0001]")return}else{if(A.hostname==="localhost")return;let Q=A.hostname.split("."),D=(Z)=>{let G=parseInt(Z,10);return 0<=G&&G<=255};if(Q[0]==="127"&&D(Q[1])&&D(Q[2])&&D(Q[3])&&Q.length===4)return}throw new zfQ.CredentialsProviderError(`URL not accepted. It must either be HTTPS or match one of the following:
  - loopback CIDR *********/8 or [::1/128]
  - ECS container host *************
  - EKS container host *************3 or [fd00:ec2::23]`,{logger:B})};IxA.checkUrl=$fQ});
var XV=E((c41)=>{Object.defineProperty(c41,"__esModule",{value:!0});var pB0=ph();pB0.__exportStar(Mz(),c41);pB0.__exportStar(_B0(),c41);pB0.__exportStar(tcA(),c41)});
var X_A=E((W_A)=>{Object.defineProperty(W_A,"__esModule",{value:!0});W_A.toBase64=void 0;var CvQ=AD(),KvQ=cB(),HvQ=(A)=>{let B;if(typeof A==="string")B=KvQ.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return CvQ.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};W_A.toBase64=HvQ});
var XdA=E((WdA)=>{Object.defineProperty(WdA,"__esModule",{value:!0});WdA.fromEnv=void 0;var GaQ=sw1(),FaQ=(A)=>GaQ.fromEnv(A);WdA.fromEnv=FaQ});
var XiA=E((wW5,feQ)=>{feQ.exports={name:"@aws-sdk/client-sso",description:"AWS SDK for JavaScript Sso Client for Node.js, Browser and React Native",version:"3.840.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-sso","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo sso"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sso",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-sso"}}});
var XmA=E((WmA)=>{Object.defineProperty(WmA,"__esModule",{value:!0});WmA.fromCognitoIdentityPool=void 0;var XiQ=FB0(),ViQ=(A)=>XiQ.fromCognitoIdentityPool({...A});WmA.fromCognitoIdentityPool=ViQ});
var XxA=E((JxA)=>{Object.defineProperty(JxA,"__esModule",{value:!0});JxA.createGetRequest=MfQ;JxA.getCredentials=RfQ;var kA0=eB(),qfQ=JV(),NfQ=W6(),LfQ=Fy();function MfQ(A){return new qfQ.HttpRequest({protocol:A.protocol,hostname:A.hostname,port:Number(A.port),path:A.pathname,query:Array.from(A.searchParams.entries()).reduce((B,[Q,D])=>{return B[Q]=D,B},{}),fragment:A.hash})}async function RfQ(A,B){let D=await LfQ.sdkStreamMixin(A.body).transformToString();if(A.statusCode===200){let Z=JSON.parse(D);if(typeof Z.AccessKeyId!=="string"||typeof Z.SecretAccessKey!=="string"||typeof Z.Token!=="string"||typeof Z.Expiration!=="string")throw new kA0.CredentialsProviderError("HTTP credential provider response not of the required format, an object matching: { AccessKeyId: string, SecretAccessKey: string, Token: string, Expiration: string(rfc3339) }",{logger:B});return{accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.Token,expiration:NfQ.parseRfc3339DateTime(Z.Expiration)}}if(A.statusCode>=400&&A.statusCode<500){let Z={};try{Z=JSON.parse(D)}catch(G){}throw Object.assign(new kA0.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B}),{Code:Z.Code,Message:Z.Message})}throw new kA0.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B})}});
var YB0=E((QY5,kmA)=>{var{defineProperty:cw1,getOwnPropertyDescriptor:TiQ,getOwnPropertyNames:PiQ}=Object,SiQ=Object.prototype.hasOwnProperty,lw1=(A,B)=>cw1(A,"name",{value:B,configurable:!0}),jiQ=(A,B)=>{for(var Q in B)cw1(A,Q,{get:B[Q],enumerable:!0})},kiQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of PiQ(B))if(!SiQ.call(A,Z)&&Z!==Q)cw1(A,Z,{get:()=>B[Z],enumerable:!(D=TiQ(B,Z))||D.enumerable})}return A},yiQ=(A)=>kiQ(cw1({},"__esModule",{value:!0}),A),LmA={};jiQ(LmA,{AlgorithmId:()=>TmA,EndpointURLScheme:()=>OmA,FieldPosition:()=>PmA,HttpApiKeyAuthLocation:()=>RmA,HttpAuthLocation:()=>MmA,IniSectionType:()=>SmA,RequestHandlerProtocol:()=>jmA,SMITHY_CONTEXT_KEY:()=>fiQ,getDefaultClientConfiguration:()=>viQ,resolveDefaultRuntimeConfig:()=>biQ});kmA.exports=yiQ(LmA);var MmA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(MmA||{}),RmA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(RmA||{}),OmA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(OmA||{}),TmA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(TmA||{}),_iQ=lw1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),xiQ=lw1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),viQ=lw1((A)=>{return _iQ(A)},"getDefaultClientConfiguration"),biQ=lw1((A)=>{return xiQ(A)},"resolveDefaultRuntimeConfig"),PmA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(PmA||{}),fiQ="__smithy_context",SmA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(SmA||{}),jmA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(jmA||{})});
var YQ0=E((uW)=>{Object.defineProperty(uW,"__esModule",{value:!0});uW.fromHttp=void 0;var IK=ph();IK.__exportStar(sjA(),uW);IK.__exportStar(YmA(),uW);IK.__exportStar(XmA(),uW);IK.__exportStar(KmA(),uW);var d24=nw1();Object.defineProperty(uW,"fromHttp",{enumerable:!0,get:function(){return d24.fromHttp}});IK.__exportStar(XdA(),uW);IK.__exportStar(DsA(),uW);IK.__exportStar(FsA(),uW);IK.__exportStar(IQ0(),uW);IK.__exportStar(wsA(),uW);IK.__exportStar(NsA(),uW);IK.__exportStar(jsA(),uW);IK.__exportStar(_sA(),uW);IK.__exportStar(bsA(),uW)});
var Y_A=E((F_A)=>{Object.defineProperty(F_A,"__esModule",{value:!0});F_A.fromBase64=void 0;var JvQ=AD(),XvQ=/^[A-Za-z0-9+/]*={0,2}$/,VvQ=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!XvQ.exec(A))throw new TypeError("Invalid base64 string.");let B=JvQ.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};F_A.fromBase64=VvQ});
var YmA=E((FmA)=>{Object.defineProperty(FmA,"__esModule",{value:!0});FmA.fromCognitoIdentity=void 0;var WiQ=FB0(),JiQ=(A)=>WiQ.fromCognitoIdentity({...A});FmA.fromCognitoIdentity=JiQ});
var _A0=E((yA0)=>{Object.defineProperty(yA0,"__esModule",{value:!0});yA0.fromHttp=void 0;var mfQ=wxA();Object.defineProperty(yA0,"fromHttp",{enumerable:!0,get:function(){return mfQ.fromHttp}})});
var _B0=E((SY5,PcA)=>{var{defineProperty:K$1,getOwnPropertyDescriptor:OsQ,getOwnPropertyNames:TsQ}=Object,PsQ=Object.prototype.hasOwnProperty,cZ=(A,B)=>K$1(A,"name",{value:B,configurable:!0}),SsQ=(A,B)=>{for(var Q in B)K$1(A,Q,{get:B[Q],enumerable:!0})},jsQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of TsQ(B))if(!PsQ.call(A,Z)&&Z!==Q)K$1(A,Z,{get:()=>B[Z],enumerable:!(D=OsQ(B,Z))||D.enumerable})}return A},ksQ=(A)=>jsQ(K$1({},"__esModule",{value:!0}),A),LcA={};SsQ(LcA,{AWSSDKSigV4Signer:()=>vsQ,AwsSdkSigV4ASigner:()=>fsQ,AwsSdkSigV4Signer:()=>yB0,NODE_AUTH_SCHEME_PREFERENCE_OPTIONS:()=>hsQ,NODE_SIGV4A_CONFIG_OPTIONS:()=>msQ,getBearerTokenEnvKey:()=>McA,resolveAWSSDKSigV4Config:()=>csQ,resolveAwsSdkSigV4AConfig:()=>usQ,resolveAwsSdkSigV4Config:()=>RcA,validateSigningProperties:()=>kB0});PcA.exports=ksQ(LcA);var ysQ=DK(),_sQ=DK(),zcA=cZ((A)=>_sQ.HttpResponse.isInstance(A)?A.headers?.date??A.headers?.Date:void 0,"getDateHeader"),jB0=cZ((A)=>new Date(Date.now()+A),"getSkewCorrectedDate"),xsQ=cZ((A,B)=>Math.abs(jB0(B).getTime()-A)>=300000,"isClockSkewed"),EcA=cZ((A,B)=>{let Q=Date.parse(A);if(xsQ(Q,B))return Q-Date.now();return B},"getUpdatedSystemClockOffset"),g41=cZ((A,B)=>{if(!B)throw new Error(`Property \`${A}\` is not resolved for AWS SDK SigV4Auth`);return B},"throwSigningPropertyError"),kB0=cZ(async(A)=>{let B=g41("context",A.context),Q=g41("config",A.config),D=B.endpointV2?.properties?.authSchemes?.[0],G=await g41("signer",Q.signer)(D),F=A?.signingRegion,I=A?.signingRegionSet,Y=A?.signingName;return{config:Q,signer:G,signingRegion:F,signingRegionSet:I,signingName:Y}},"validateSigningProperties"),yB0=class{static{cZ(this,"AwsSdkSigV4Signer")}async sign(A,B,Q){if(!ysQ.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let D=await kB0(Q),{config:Z,signer:G}=D,{signingRegion:F,signingName:I}=D,Y=Q.context;if(Y?.authSchemes?.length??0>1){let[J,X]=Y.authSchemes;if(J?.name==="sigv4a"&&X?.name==="sigv4")F=X?.signingRegion??F,I=X?.signingName??I}return await G.sign(A,{signingDate:jB0(Z.systemClockOffset),signingRegion:F,signingService:I})}errorHandler(A){return(B)=>{let Q=B.ServerTime??zcA(B.$response);if(Q){let D=g41("config",A.config),Z=D.systemClockOffset;if(D.systemClockOffset=EcA(Q,D.systemClockOffset),D.systemClockOffset!==Z&&B.$metadata)B.$metadata.clockSkewCorrected=!0}throw B}}successHandler(A,B){let Q=zcA(A);if(Q){let D=g41("config",B.config);D.systemClockOffset=EcA(Q,D.systemClockOffset)}}},vsQ=yB0,bsQ=DK(),fsQ=class extends yB0{static{cZ(this,"AwsSdkSigV4ASigner")}async sign(A,B,Q){if(!bsQ.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let{config:D,signer:Z,signingRegion:G,signingRegionSet:F,signingName:I}=await kB0(Q),W=(await D.sigv4aSigningRegionSet?.()??F??[G]).join(",");return await Z.sign(A,{signingDate:jB0(D.systemClockOffset),signingRegion:W,signingService:I})}},UcA=cZ((A)=>typeof A==="string"&&A.length>0?A.split(",").map((B)=>B.trim()):[],"getArrayForCommaSeparatedString"),McA=cZ((A)=>`AWS_BEARER_TOKEN_${A.replace(/[\s-]/g,"_").toUpperCase()}`,"getBearerTokenEnvKey"),wcA="AWS_AUTH_SCHEME_PREFERENCE",$cA="auth_scheme_preference",hsQ={environmentVariableSelector:cZ((A,B)=>{if(B?.signingName){if(McA(B.signingName)in A)return["httpBearerAuth"]}if(!(wcA in A))return;return UcA(A[wcA])},"environmentVariableSelector"),configFileSelector:cZ((A)=>{if(!($cA in A))return;return UcA(A[$cA])},"configFileSelector"),default:[]},gsQ=VB(),qcA=eB(),usQ=cZ((A)=>{return A.sigv4aSigningRegionSet=gsQ.normalizeProvider(A.sigv4aSigningRegionSet),A},"resolveAwsSdkSigV4AConfig"),msQ={environmentVariableSelector(A){if(A.AWS_SIGV4A_SIGNING_REGION_SET)return A.AWS_SIGV4A_SIGNING_REGION_SET.split(",").map((B)=>B.trim());throw new qcA.ProviderError("AWS_SIGV4A_SIGNING_REGION_SET not set in env.",{tryNextLink:!0})},configFileSelector(A){if(A.sigv4a_signing_region_set)return(A.sigv4a_signing_region_set??"").split(",").map((B)=>B.trim());throw new qcA.ProviderError("sigv4a_signing_region_set not set in profile.",{tryNextLink:!0})},default:void 0},dsQ=Mz(),Qg=VB(),NcA=HcA(),RcA=cZ((A)=>{let B=A.credentials,Q=!!A.credentials,D=void 0;Object.defineProperty(A,"credentials",{set(W){if(W&&W!==B&&W!==D)Q=!0;B=W;let J=OcA(A,{credentials:B,credentialDefaultProvider:A.credentialDefaultProvider}),X=TcA(A,J);if(Q&&!X.attributed)D=cZ(async(V)=>X(V).then((C)=>dsQ.setCredentialFeature(C,"CREDENTIALS_CODE","e")),"resolvedCredentials"),D.memoized=X.memoized,D.configBound=X.configBound,D.attributed=!0;else D=X},get(){return D},enumerable:!0,configurable:!0}),A.credentials=B;let{signingEscapePath:Z=!0,systemClockOffset:G=A.systemClockOffset||0,sha256:F}=A,I;if(A.signer)I=Qg.normalizeProvider(A.signer);else if(A.regionInfoProvider)I=cZ(()=>Qg.normalizeProvider(A.region)().then(async(W)=>[await A.regionInfoProvider(W,{useFipsEndpoint:await A.useFipsEndpoint(),useDualstackEndpoint:await A.useDualstackEndpoint()})||{},W]).then(([W,J])=>{let{signingRegion:X,signingService:V}=W;A.signingRegion=A.signingRegion||X||J,A.signingName=A.signingName||V||A.serviceId;let C={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||NcA.SignatureV4)(C)}),"signer");else I=cZ(async(W)=>{W=Object.assign({},{name:"sigv4",signingName:A.signingName||A.defaultSigningName,signingRegion:await Qg.normalizeProvider(A.region)(),properties:{}},W);let{signingRegion:J,signingName:X}=W;A.signingRegion=A.signingRegion||J,A.signingName=A.signingName||X||A.serviceId;let V={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||NcA.SignatureV4)(V)},"signer");return Object.assign(A,{systemClockOffset:G,signingEscapePath:Z,signer:I})},"resolveAwsSdkSigV4Config"),csQ=RcA;function OcA(A,{credentials:B,credentialDefaultProvider:Q}){let D;if(B)if(!B?.memoized)D=Qg.memoizeIdentityProvider(B,Qg.isIdentityExpired,Qg.doesIdentityRequireRefresh);else D=B;else if(Q)D=Qg.normalizeProvider(Q(Object.assign({},A,{parentClientConfig:A})));else D=cZ(async()=>{throw new Error("@aws-sdk/core::resolveAwsSdkSigV4Config - `credentials` not provided and no credentialDefaultProvider was configured.")},"credentialsProvider");return D.memoized=!0,D}cZ(OcA,"normalizeCredentialProvider");function TcA(A,B){if(B.configBound)return B;let Q=cZ(async(D)=>B({...D,callerClientConfig:A}),"fn");return Q.memoized=B.memoized,Q.configBound=!0,Q}cZ(TcA,"bindCallerConfig")});
var _sA=E((ksA)=>{Object.defineProperty(ksA,"__esModule",{value:!0});ksA.fromTokenFile=void 0;var h24=A61(),g24=(A={})=>h24.fromTokenFile({...A});ksA.fromTokenFile=g24});
var afA=E((ifA)=>{Object.defineProperty(ifA,"__esModule",{value:!0});ifA.getRuntimeConfig=void 0;var MmQ=II(),RmQ=VB(),OmQ=W6(),TmQ=BZ(),lfA=Ry(),pfA=cB(),PmQ=B20(),SmQ=cfA(),jmQ=(A)=>{return{apiVersion:"2011-06-15",base64Decoder:A?.base64Decoder??lfA.fromBase64,base64Encoder:A?.base64Encoder??lfA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??SmQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??PmQ.defaultSTSHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new MmQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new RmQ.NoAuthSigner}],logger:A?.logger??new OmQ.NoOpLogger,serviceId:A?.serviceId??"STS",urlParser:A?.urlParser??TmQ.parseUrl,utf8Decoder:A?.utf8Decoder??pfA.fromUtf8,utf8Encoder:A?.utf8Encoder??pfA.toUtf8}};ifA.getRuntimeConfig=jmQ});
var auA=E((NI5,nuA)=>{var{defineProperty:fw1,getOwnPropertyDescriptor:OlQ,getOwnPropertyNames:TlQ}=Object,PlQ=Object.prototype.hasOwnProperty,wA=(A,B)=>fw1(A,"name",{value:B,configurable:!0}),SlQ=(A,B)=>{for(var Q in B)fw1(A,Q,{get:B[Q],enumerable:!0})},jlQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of TlQ(B))if(!PlQ.call(A,Z)&&Z!==Q)fw1(A,Z,{get:()=>B[Z],enumerable:!(D=OlQ(B,Z))||D.enumerable})}return A},klQ=(A)=>jlQ(fw1({},"__esModule",{value:!0}),A),QuA={};SlQ(QuA,{AmbiguousRoleResolutionType:()=>mlQ,CognitoIdentity:()=>iuA,CognitoIdentityClient:()=>AB0,CognitoIdentityServiceException:()=>BK,ConcurrentModificationException:()=>CuA,CreateIdentityPoolCommand:()=>MuA,CredentialsFilterSensitiveLog:()=>HuA,DeleteIdentitiesCommand:()=>RuA,DeleteIdentityPoolCommand:()=>OuA,DescribeIdentityCommand:()=>TuA,DescribeIdentityPoolCommand:()=>PuA,DeveloperUserAlreadyRegisteredException:()=>VuA,ErrorCode:()=>dlQ,ExternalServiceException:()=>JuA,GetCredentialsForIdentityCommand:()=>SuA,GetCredentialsForIdentityInputFilterSensitiveLog:()=>KuA,GetCredentialsForIdentityResponseFilterSensitiveLog:()=>zuA,GetIdCommand:()=>juA,GetIdInputFilterSensitiveLog:()=>EuA,GetIdentityPoolRolesCommand:()=>kuA,GetOpenIdTokenCommand:()=>yuA,GetOpenIdTokenForDeveloperIdentityCommand:()=>_uA,GetOpenIdTokenForDeveloperIdentityInputFilterSensitiveLog:()=>$uA,GetOpenIdTokenForDeveloperIdentityResponseFilterSensitiveLog:()=>quA,GetOpenIdTokenInputFilterSensitiveLog:()=>UuA,GetOpenIdTokenResponseFilterSensitiveLog:()=>wuA,GetPrincipalTagAttributeMapCommand:()=>xuA,InternalErrorException:()=>DuA,InvalidIdentityPoolConfigurationException:()=>XuA,InvalidParameterException:()=>ZuA,LimitExceededException:()=>GuA,ListIdentitiesCommand:()=>vuA,ListIdentityPoolsCommand:()=>BB0,ListTagsForResourceCommand:()=>buA,LookupDeveloperIdentityCommand:()=>fuA,MappingRuleMatchType:()=>clQ,MergeDeveloperIdentitiesCommand:()=>huA,NotAuthorizedException:()=>FuA,ResourceConflictException:()=>IuA,ResourceNotFoundException:()=>WuA,RoleMappingType:()=>llQ,SetIdentityPoolRolesCommand:()=>guA,SetPrincipalTagAttributeMapCommand:()=>uuA,TagResourceCommand:()=>muA,TooManyRequestsException:()=>YuA,UnlinkDeveloperIdentityCommand:()=>duA,UnlinkIdentityCommand:()=>cuA,UnlinkIdentityInputFilterSensitiveLog:()=>NuA,UntagResourceCommand:()=>luA,UpdateIdentityPoolCommand:()=>puA,__Client:()=>zA.Client,paginateListIdentityPools:()=>AiQ});nuA.exports=klQ(QuA);var ogA=C41(),ylQ=K41(),_lQ=H41(),tgA=Da(),xlQ=V4(),bw1=VB(),vlQ=TG(),ID=q6(),egA=v4(),AuA=SA0(),blQ=wA((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"cognito-identity"})},"resolveClientEndpointParameters"),xD={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},flQ=rgA(),BuA=L41(),e20=JV(),zA=W6(),hlQ=wA((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),glQ=wA((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),ulQ=wA((A,B)=>{let Q=Object.assign(BuA.getAwsRegionExtensionConfiguration(A),zA.getDefaultExtensionConfiguration(A),e20.getHttpHandlerExtensionConfiguration(A),hlQ(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,BuA.resolveAwsRegionExtensionConfiguration(Q),zA.resolveDefaultRuntimeConfig(Q),e20.resolveHttpHandlerRuntimeConfig(Q),glQ(Q))},"resolveRuntimeExtensions"),AB0=class extends zA.Client{static{wA(this,"CognitoIdentityClient")}config;constructor(...[A]){let B=flQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=blQ(B),D=tgA.resolveUserAgentConfig(Q),Z=egA.resolveRetryConfig(D),G=xlQ.resolveRegionConfig(Z),F=ogA.resolveHostHeaderConfig(G),I=ID.resolveEndpointConfig(F),Y=AuA.resolveHttpAuthSchemeConfig(I),W=ulQ(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(tgA.getUserAgentPlugin(this.config)),this.middlewareStack.use(egA.getRetryPlugin(this.config)),this.middlewareStack.use(vlQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(ogA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(ylQ.getLoggerPlugin(this.config)),this.middlewareStack.use(_lQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(bw1.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:AuA.defaultCognitoIdentityHttpAuthSchemeParametersProvider,identityProviderConfigProvider:wA(async(J)=>new bw1.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(bw1.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},vD=T3(),dZ=II(),BK=class A extends zA.ServiceException{static{wA(this,"CognitoIdentityServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},mlQ={AUTHENTICATED_ROLE:"AuthenticatedRole",DENY:"Deny"},DuA=class A extends BK{static{wA(this,"InternalErrorException")}name="InternalErrorException";$fault="server";constructor(B){super({name:"InternalErrorException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype)}},ZuA=class A extends BK{static{wA(this,"InvalidParameterException")}name="InvalidParameterException";$fault="client";constructor(B){super({name:"InvalidParameterException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},GuA=class A extends BK{static{wA(this,"LimitExceededException")}name="LimitExceededException";$fault="client";constructor(B){super({name:"LimitExceededException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},FuA=class A extends BK{static{wA(this,"NotAuthorizedException")}name="NotAuthorizedException";$fault="client";constructor(B){super({name:"NotAuthorizedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},IuA=class A extends BK{static{wA(this,"ResourceConflictException")}name="ResourceConflictException";$fault="client";constructor(B){super({name:"ResourceConflictException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},YuA=class A extends BK{static{wA(this,"TooManyRequestsException")}name="TooManyRequestsException";$fault="client";constructor(B){super({name:"TooManyRequestsException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},dlQ={ACCESS_DENIED:"AccessDenied",INTERNAL_SERVER_ERROR:"InternalServerError"},WuA=class A extends BK{static{wA(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},JuA=class A extends BK{static{wA(this,"ExternalServiceException")}name="ExternalServiceException";$fault="client";constructor(B){super({name:"ExternalServiceException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},XuA=class A extends BK{static{wA(this,"InvalidIdentityPoolConfigurationException")}name="InvalidIdentityPoolConfigurationException";$fault="client";constructor(B){super({name:"InvalidIdentityPoolConfigurationException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},clQ={CONTAINS:"Contains",EQUALS:"Equals",NOT_EQUAL:"NotEqual",STARTS_WITH:"StartsWith"},llQ={RULES:"Rules",TOKEN:"Token"},VuA=class A extends BK{static{wA(this,"DeveloperUserAlreadyRegisteredException")}name="DeveloperUserAlreadyRegisteredException";$fault="client";constructor(B){super({name:"DeveloperUserAlreadyRegisteredException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},CuA=class A extends BK{static{wA(this,"ConcurrentModificationException")}name="ConcurrentModificationException";$fault="client";constructor(B){super({name:"ConcurrentModificationException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},KuA=wA((A)=>({...A,...A.Logins&&{Logins:zA.SENSITIVE_STRING}}),"GetCredentialsForIdentityInputFilterSensitiveLog"),HuA=wA((A)=>({...A,...A.SecretKey&&{SecretKey:zA.SENSITIVE_STRING}}),"CredentialsFilterSensitiveLog"),zuA=wA((A)=>({...A,...A.Credentials&&{Credentials:HuA(A.Credentials)}}),"GetCredentialsForIdentityResponseFilterSensitiveLog"),EuA=wA((A)=>({...A,...A.Logins&&{Logins:zA.SENSITIVE_STRING}}),"GetIdInputFilterSensitiveLog"),UuA=wA((A)=>({...A,...A.Logins&&{Logins:zA.SENSITIVE_STRING}}),"GetOpenIdTokenInputFilterSensitiveLog"),wuA=wA((A)=>({...A,...A.Token&&{Token:zA.SENSITIVE_STRING}}),"GetOpenIdTokenResponseFilterSensitiveLog"),$uA=wA((A)=>({...A,...A.Logins&&{Logins:zA.SENSITIVE_STRING}}),"GetOpenIdTokenForDeveloperIdentityInputFilterSensitiveLog"),quA=wA((A)=>({...A,...A.Token&&{Token:zA.SENSITIVE_STRING}}),"GetOpenIdTokenForDeveloperIdentityResponseFilterSensitiveLog"),NuA=wA((A)=>({...A,...A.Logins&&{Logins:zA.SENSITIVE_STRING}}),"UnlinkIdentityInputFilterSensitiveLog"),plQ=wA(async(A,B)=>{let Q=YD("CreateIdentityPool"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_CreateIdentityPoolCommand"),ilQ=wA(async(A,B)=>{let Q=YD("DeleteIdentities"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_DeleteIdentitiesCommand"),nlQ=wA(async(A,B)=>{let Q=YD("DeleteIdentityPool"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_DeleteIdentityPoolCommand"),alQ=wA(async(A,B)=>{let Q=YD("DescribeIdentity"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_DescribeIdentityCommand"),slQ=wA(async(A,B)=>{let Q=YD("DescribeIdentityPool"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_DescribeIdentityPoolCommand"),rlQ=wA(async(A,B)=>{let Q=YD("GetCredentialsForIdentity"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_GetCredentialsForIdentityCommand"),olQ=wA(async(A,B)=>{let Q=YD("GetId"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_GetIdCommand"),tlQ=wA(async(A,B)=>{let Q=YD("GetIdentityPoolRoles"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_GetIdentityPoolRolesCommand"),elQ=wA(async(A,B)=>{let Q=YD("GetOpenIdToken"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_GetOpenIdTokenCommand"),ApQ=wA(async(A,B)=>{let Q=YD("GetOpenIdTokenForDeveloperIdentity"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_GetOpenIdTokenForDeveloperIdentityCommand"),BpQ=wA(async(A,B)=>{let Q=YD("GetPrincipalTagAttributeMap"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_GetPrincipalTagAttributeMapCommand"),QpQ=wA(async(A,B)=>{let Q=YD("ListIdentities"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_ListIdentitiesCommand"),DpQ=wA(async(A,B)=>{let Q=YD("ListIdentityPools"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_ListIdentityPoolsCommand"),ZpQ=wA(async(A,B)=>{let Q=YD("ListTagsForResource"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_ListTagsForResourceCommand"),GpQ=wA(async(A,B)=>{let Q=YD("LookupDeveloperIdentity"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_LookupDeveloperIdentityCommand"),FpQ=wA(async(A,B)=>{let Q=YD("MergeDeveloperIdentities"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_MergeDeveloperIdentitiesCommand"),IpQ=wA(async(A,B)=>{let Q=YD("SetIdentityPoolRoles"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_SetIdentityPoolRolesCommand"),YpQ=wA(async(A,B)=>{let Q=YD("SetPrincipalTagAttributeMap"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_SetPrincipalTagAttributeMapCommand"),WpQ=wA(async(A,B)=>{let Q=YD("TagResource"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_TagResourceCommand"),JpQ=wA(async(A,B)=>{let Q=YD("UnlinkDeveloperIdentity"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_UnlinkDeveloperIdentityCommand"),XpQ=wA(async(A,B)=>{let Q=YD("UnlinkIdentity"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_UnlinkIdentityCommand"),VpQ=wA(async(A,B)=>{let Q=YD("UntagResource"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_UntagResourceCommand"),CpQ=wA(async(A,B)=>{let Q=YD("UpdateIdentityPool"),D;return D=JSON.stringify(zA._json(A)),fD(B,Q,"/",void 0,D)},"se_UpdateIdentityPoolCommand"),KpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_CreateIdentityPoolCommand"),HpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_DeleteIdentitiesCommand"),zpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);return await zA.collectBody(A.body,B),{$metadata:o6(A)}},"de_DeleteIdentityPoolCommand"),EpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=LuA(Q,B),{$metadata:o6(A),...D}},"de_DescribeIdentityCommand"),UpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_DescribeIdentityPoolCommand"),wpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=spQ(Q,B),{$metadata:o6(A),...D}},"de_GetCredentialsForIdentityCommand"),$pQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_GetIdCommand"),qpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_GetIdentityPoolRolesCommand"),NpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_GetOpenIdTokenCommand"),LpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_GetOpenIdTokenForDeveloperIdentityCommand"),MpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_GetPrincipalTagAttributeMapCommand"),RpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=opQ(Q,B),{$metadata:o6(A),...D}},"de_ListIdentitiesCommand"),OpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_ListIdentityPoolsCommand"),TpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_ListTagsForResourceCommand"),PpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_LookupDeveloperIdentityCommand"),SpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_MergeDeveloperIdentitiesCommand"),jpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);return await zA.collectBody(A.body,B),{$metadata:o6(A)}},"de_SetIdentityPoolRolesCommand"),kpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_SetPrincipalTagAttributeMapCommand"),ypQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_TagResourceCommand"),_pQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);return await zA.collectBody(A.body,B),{$metadata:o6(A)}},"de_UnlinkDeveloperIdentityCommand"),xpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);return await zA.collectBody(A.body,B),{$metadata:o6(A)}},"de_UnlinkIdentityCommand"),vpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_UntagResourceCommand"),bpQ=wA(async(A,B)=>{if(A.statusCode>=300)return bD(A,B);let Q=await dZ.parseJsonBody(A.body,B),D={};return D=zA._json(Q),{$metadata:o6(A),...D}},"de_UpdateIdentityPoolCommand"),bD=wA(async(A,B)=>{let Q={...A,body:await dZ.parseJsonErrorBody(A.body,B)},D=dZ.loadRestJsonErrorCode(A,Q.body);switch(D){case"InternalErrorException":case"com.amazonaws.cognitoidentity#InternalErrorException":throw await upQ(Q,B);case"InvalidParameterException":case"com.amazonaws.cognitoidentity#InvalidParameterException":throw await dpQ(Q,B);case"LimitExceededException":case"com.amazonaws.cognitoidentity#LimitExceededException":throw await cpQ(Q,B);case"NotAuthorizedException":case"com.amazonaws.cognitoidentity#NotAuthorizedException":throw await lpQ(Q,B);case"ResourceConflictException":case"com.amazonaws.cognitoidentity#ResourceConflictException":throw await ppQ(Q,B);case"TooManyRequestsException":case"com.amazonaws.cognitoidentity#TooManyRequestsException":throw await npQ(Q,B);case"ResourceNotFoundException":case"com.amazonaws.cognitoidentity#ResourceNotFoundException":throw await ipQ(Q,B);case"ExternalServiceException":case"com.amazonaws.cognitoidentity#ExternalServiceException":throw await gpQ(Q,B);case"InvalidIdentityPoolConfigurationException":case"com.amazonaws.cognitoidentity#InvalidIdentityPoolConfigurationException":throw await mpQ(Q,B);case"DeveloperUserAlreadyRegisteredException":case"com.amazonaws.cognitoidentity#DeveloperUserAlreadyRegisteredException":throw await hpQ(Q,B);case"ConcurrentModificationException":case"com.amazonaws.cognitoidentity#ConcurrentModificationException":throw await fpQ(Q,B);default:let Z=Q.body;return tpQ({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),fpQ=wA(async(A,B)=>{let Q=A.body,D=zA._json(Q),Z=new CuA({$metadata:o6(A),...D});return zA.decorateServiceException(Z,Q)},"de_ConcurrentModificationExceptionRes"),hpQ=wA(async(A,B)=>{let Q=A.body,D=zA._json(Q),Z=new VuA({$metadata:o6(A),...D});return zA.decorateServiceException(Z,Q)},"de_DeveloperUserAlreadyRegisteredExceptionRes"),gpQ=wA(async(A,B)=>{let Q=A.body,D=zA._json(Q),Z=new JuA({$metadata:o6(A),...D});return zA.decorateServiceException(Z,Q)},"de_ExternalServiceExceptionRes"),upQ=wA(async(A,B)=>{let Q=A.body,D=zA._json(Q),Z=new DuA({$metadata:o6(A),...D});return zA.decorateServiceException(Z,Q)},"de_InternalErrorExceptionRes"),mpQ=wA(async(A,B)=>{let Q=A.body,D=zA._json(Q),Z=new XuA({$metadata:o6(A),...D});return zA.decorateServiceException(Z,Q)},"de_InvalidIdentityPoolConfigurationExceptionRes"),dpQ=wA(async(A,B)=>{let Q=A.body,D=zA._json(Q),Z=new ZuA({$metadata:o6(A),...D});return zA.decorateServiceException(Z,Q)},"de_InvalidParameterExceptionRes"),cpQ=wA(async(A,B)=>{let Q=A.body,D=zA._json(Q),Z=new GuA({$metadata:o6(A),...D});return zA.decorateServiceException(Z,Q)},"de_LimitExceededExceptionRes"),lpQ=wA(async(A,B)=>{let Q=A.body,D=zA._json(Q),Z=new FuA({$metadata:o6(A),...D});return zA.decorateServiceException(Z,Q)},"de_NotAuthorizedExceptionRes"),ppQ=wA(async(A,B)=>{let Q=A.body,D=zA._json(Q),Z=new IuA({$metadata:o6(A),...D});return zA.decorateServiceException(Z,Q)},"de_ResourceConflictExceptionRes"),ipQ=wA(async(A,B)=>{let Q=A.body,D=zA._json(Q),Z=new WuA({$metadata:o6(A),...D});return zA.decorateServiceException(Z,Q)},"de_ResourceNotFoundExceptionRes"),npQ=wA(async(A,B)=>{let Q=A.body,D=zA._json(Q),Z=new YuA({$metadata:o6(A),...D});return zA.decorateServiceException(Z,Q)},"de_TooManyRequestsExceptionRes"),apQ=wA((A,B)=>{return zA.take(A,{AccessKeyId:zA.expectString,Expiration:wA((Q)=>zA.expectNonNull(zA.parseEpochTimestamp(zA.expectNumber(Q))),"Expiration"),SecretKey:zA.expectString,SessionToken:zA.expectString})},"de_Credentials"),spQ=wA((A,B)=>{return zA.take(A,{Credentials:wA((Q)=>apQ(Q,B),"Credentials"),IdentityId:zA.expectString})},"de_GetCredentialsForIdentityResponse"),rpQ=wA((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return LuA(D,B)})},"de_IdentitiesList"),LuA=wA((A,B)=>{return zA.take(A,{CreationDate:wA((Q)=>zA.expectNonNull(zA.parseEpochTimestamp(zA.expectNumber(Q))),"CreationDate"),IdentityId:zA.expectString,LastModifiedDate:wA((Q)=>zA.expectNonNull(zA.parseEpochTimestamp(zA.expectNumber(Q))),"LastModifiedDate"),Logins:zA._json})},"de_IdentityDescription"),opQ=wA((A,B)=>{return zA.take(A,{Identities:wA((Q)=>rpQ(Q,B),"Identities"),IdentityPoolId:zA.expectString,NextToken:zA.expectString})},"de_ListIdentitiesResponse"),o6=wA((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),tpQ=zA.withBaseException(BK),fD=wA(async(A,B,Q,D,Z)=>{let{hostname:G,protocol:F="https",port:I,path:Y}=await A.endpoint(),W={protocol:F,hostname:G,port:I,method:"POST",path:Y.endsWith("/")?Y.slice(0,-1)+Q:Y+Q,headers:B};if(D!==void 0)W.hostname=D;if(Z!==void 0)W.body=Z;return new e20.HttpRequest(W)},"buildHttpRpcRequest");function YD(A){return{"content-type":"application/x-amz-json-1.1","x-amz-target":`AWSCognitoIdentityService.${A}`}}wA(YD,"sharedHeaders");var MuA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","CreateIdentityPool",{}).n("CognitoIdentityClient","CreateIdentityPoolCommand").f(void 0,void 0).ser(plQ).de(KpQ).build(){static{wA(this,"CreateIdentityPoolCommand")}},RuA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","DeleteIdentities",{}).n("CognitoIdentityClient","DeleteIdentitiesCommand").f(void 0,void 0).ser(ilQ).de(HpQ).build(){static{wA(this,"DeleteIdentitiesCommand")}},OuA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","DeleteIdentityPool",{}).n("CognitoIdentityClient","DeleteIdentityPoolCommand").f(void 0,void 0).ser(nlQ).de(zpQ).build(){static{wA(this,"DeleteIdentityPoolCommand")}},TuA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","DescribeIdentity",{}).n("CognitoIdentityClient","DescribeIdentityCommand").f(void 0,void 0).ser(alQ).de(EpQ).build(){static{wA(this,"DescribeIdentityCommand")}},PuA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","DescribeIdentityPool",{}).n("CognitoIdentityClient","DescribeIdentityPoolCommand").f(void 0,void 0).ser(slQ).de(UpQ).build(){static{wA(this,"DescribeIdentityPoolCommand")}},SuA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetCredentialsForIdentity",{}).n("CognitoIdentityClient","GetCredentialsForIdentityCommand").f(KuA,zuA).ser(rlQ).de(wpQ).build(){static{wA(this,"GetCredentialsForIdentityCommand")}},juA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetId",{}).n("CognitoIdentityClient","GetIdCommand").f(EuA,void 0).ser(olQ).de($pQ).build(){static{wA(this,"GetIdCommand")}},kuA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetIdentityPoolRoles",{}).n("CognitoIdentityClient","GetIdentityPoolRolesCommand").f(void 0,void 0).ser(tlQ).de(qpQ).build(){static{wA(this,"GetIdentityPoolRolesCommand")}},yuA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetOpenIdToken",{}).n("CognitoIdentityClient","GetOpenIdTokenCommand").f(UuA,wuA).ser(elQ).de(NpQ).build(){static{wA(this,"GetOpenIdTokenCommand")}},_uA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetOpenIdTokenForDeveloperIdentity",{}).n("CognitoIdentityClient","GetOpenIdTokenForDeveloperIdentityCommand").f($uA,quA).ser(ApQ).de(LpQ).build(){static{wA(this,"GetOpenIdTokenForDeveloperIdentityCommand")}},xuA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetPrincipalTagAttributeMap",{}).n("CognitoIdentityClient","GetPrincipalTagAttributeMapCommand").f(void 0,void 0).ser(BpQ).de(MpQ).build(){static{wA(this,"GetPrincipalTagAttributeMapCommand")}},vuA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","ListIdentities",{}).n("CognitoIdentityClient","ListIdentitiesCommand").f(void 0,void 0).ser(QpQ).de(RpQ).build(){static{wA(this,"ListIdentitiesCommand")}},BB0=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","ListIdentityPools",{}).n("CognitoIdentityClient","ListIdentityPoolsCommand").f(void 0,void 0).ser(DpQ).de(OpQ).build(){static{wA(this,"ListIdentityPoolsCommand")}},buA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","ListTagsForResource",{}).n("CognitoIdentityClient","ListTagsForResourceCommand").f(void 0,void 0).ser(ZpQ).de(TpQ).build(){static{wA(this,"ListTagsForResourceCommand")}},fuA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","LookupDeveloperIdentity",{}).n("CognitoIdentityClient","LookupDeveloperIdentityCommand").f(void 0,void 0).ser(GpQ).de(PpQ).build(){static{wA(this,"LookupDeveloperIdentityCommand")}},huA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","MergeDeveloperIdentities",{}).n("CognitoIdentityClient","MergeDeveloperIdentitiesCommand").f(void 0,void 0).ser(FpQ).de(SpQ).build(){static{wA(this,"MergeDeveloperIdentitiesCommand")}},guA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","SetIdentityPoolRoles",{}).n("CognitoIdentityClient","SetIdentityPoolRolesCommand").f(void 0,void 0).ser(IpQ).de(jpQ).build(){static{wA(this,"SetIdentityPoolRolesCommand")}},uuA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","SetPrincipalTagAttributeMap",{}).n("CognitoIdentityClient","SetPrincipalTagAttributeMapCommand").f(void 0,void 0).ser(YpQ).de(kpQ).build(){static{wA(this,"SetPrincipalTagAttributeMapCommand")}},muA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","TagResource",{}).n("CognitoIdentityClient","TagResourceCommand").f(void 0,void 0).ser(WpQ).de(ypQ).build(){static{wA(this,"TagResourceCommand")}},duA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","UnlinkDeveloperIdentity",{}).n("CognitoIdentityClient","UnlinkDeveloperIdentityCommand").f(void 0,void 0).ser(JpQ).de(_pQ).build(){static{wA(this,"UnlinkDeveloperIdentityCommand")}},cuA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","UnlinkIdentity",{}).n("CognitoIdentityClient","UnlinkIdentityCommand").f(NuA,void 0).ser(XpQ).de(xpQ).build(){static{wA(this,"UnlinkIdentityCommand")}},luA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","UntagResource",{}).n("CognitoIdentityClient","UntagResourceCommand").f(void 0,void 0).ser(VpQ).de(vpQ).build(){static{wA(this,"UntagResourceCommand")}},puA=class extends zA.Command.classBuilder().ep(xD).m(function(A,B,Q,D){return[vD.getSerdePlugin(Q,this.serialize,this.deserialize),ID.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","UpdateIdentityPool",{}).n("CognitoIdentityClient","UpdateIdentityPoolCommand").f(void 0,void 0).ser(CpQ).de(bpQ).build(){static{wA(this,"UpdateIdentityPoolCommand")}},epQ={CreateIdentityPoolCommand:MuA,DeleteIdentitiesCommand:RuA,DeleteIdentityPoolCommand:OuA,DescribeIdentityCommand:TuA,DescribeIdentityPoolCommand:PuA,GetCredentialsForIdentityCommand:SuA,GetIdCommand:juA,GetIdentityPoolRolesCommand:kuA,GetOpenIdTokenCommand:yuA,GetOpenIdTokenForDeveloperIdentityCommand:_uA,GetPrincipalTagAttributeMapCommand:xuA,ListIdentitiesCommand:vuA,ListIdentityPoolsCommand:BB0,ListTagsForResourceCommand:buA,LookupDeveloperIdentityCommand:fuA,MergeDeveloperIdentitiesCommand:huA,SetIdentityPoolRolesCommand:guA,SetPrincipalTagAttributeMapCommand:uuA,TagResourceCommand:muA,UnlinkDeveloperIdentityCommand:duA,UnlinkIdentityCommand:cuA,UntagResourceCommand:luA,UpdateIdentityPoolCommand:puA},iuA=class extends AB0{static{wA(this,"CognitoIdentity")}};zA.createAggregatedClient(epQ,iuA);var AiQ=bw1.createPaginator(AB0,BB0,"NextToken","NextToken","MaxResults")});
var biA=E((xiA)=>{Object.defineProperty(xiA,"__esModule",{value:!0});xiA.getRuntimeConfig=void 0;var ceQ=XV(),leQ=VB(),peQ=H8(),ieQ=BZ(),yiA=Dg(),_iA=cB(),neQ=h90(),aeQ=kiA(),seQ=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??yiA.fromBase64,base64Encoder:A?.base64Encoder??yiA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??aeQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??neQ.defaultSSOHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new ceQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new leQ.NoAuthSigner}],logger:A?.logger??new peQ.NoOpLogger,serviceId:A?.serviceId??"SSO",urlParser:A?.urlParser??ieQ.parseUrl,utf8Decoder:A?.utf8Decoder??_iA.fromUtf8,utf8Encoder:A?.utf8Encoder??_iA.toUtf8}};xiA.getRuntimeConfig=seQ});
var bnA=E((xnA)=>{Object.defineProperty(xnA,"__esModule",{value:!0});xnA.ruleSet=void 0;var jnA="required",Sz="fn",jz="argv",la="ref",$nA=!0,qnA="isSet",r41="booleanEquals",da="error",ca="endpoint",wT="tree",i90="PartitionResult",n90="getAttr",NnA={[jnA]:!1,type:"String"},LnA={[jnA]:!0,default:!1,type:"Boolean"},MnA={[la]:"Endpoint"},knA={[Sz]:r41,[jz]:[{[la]:"UseFIPS"},!0]},ynA={[Sz]:r41,[jz]:[{[la]:"UseDualStack"},!0]},Pz={},RnA={[Sz]:n90,[jz]:[{[la]:i90},"supportsFIPS"]},_nA={[la]:i90},OnA={[Sz]:r41,[jz]:[!0,{[Sz]:n90,[jz]:[_nA,"supportsDualStack"]}]},TnA=[knA],PnA=[ynA],SnA=[{[la]:"Region"}],n14={version:"1.0",parameters:{Region:NnA,UseDualStack:LnA,UseFIPS:LnA,Endpoint:NnA},rules:[{conditions:[{[Sz]:qnA,[jz]:[MnA]}],rules:[{conditions:TnA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:da},{conditions:PnA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:da},{endpoint:{url:MnA,properties:Pz,headers:Pz},type:ca}],type:wT},{conditions:[{[Sz]:qnA,[jz]:SnA}],rules:[{conditions:[{[Sz]:"aws.partition",[jz]:SnA,assign:i90}],rules:[{conditions:[knA,ynA],rules:[{conditions:[{[Sz]:r41,[jz]:[$nA,RnA]},OnA],rules:[{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Pz,headers:Pz},type:ca}],type:wT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:da}],type:wT},{conditions:TnA,rules:[{conditions:[{[Sz]:r41,[jz]:[RnA,$nA]}],rules:[{conditions:[{[Sz]:"stringEquals",[jz]:[{[Sz]:n90,[jz]:[_nA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://oidc.{Region}.amazonaws.com",properties:Pz,headers:Pz},type:ca},{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dnsSuffix}",properties:Pz,headers:Pz},type:ca}],type:wT},{error:"FIPS is enabled but this partition does not support FIPS",type:da}],type:wT},{conditions:PnA,rules:[{conditions:[OnA],rules:[{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Pz,headers:Pz},type:ca}],type:wT},{error:"DualStack is enabled but this partition does not support DualStack",type:da}],type:wT},{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dnsSuffix}",properties:Pz,headers:Pz},type:ca}],type:wT}],type:wT},{error:"Invalid Configuration: Missing Region",type:da}]};xnA.ruleSet=n14});
var bsA=E((xsA)=>{Object.defineProperty(xsA,"__esModule",{value:!0});xsA.fromWebToken=void 0;var u24=A61(),m24=(A)=>u24.fromWebToken({...A});xsA.fromWebToken=m24});
var c20=E((eN)=>{var CcQ=eN&&eN.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),KcQ=eN&&eN.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),HcQ=eN&&eN.__importStar||function(){var A=function(B){return A=Object.getOwnPropertyNames||function(Q){var D=[];for(var Z in Q)if(Object.prototype.hasOwnProperty.call(Q,Z))D[D.length]=Z;return D},A(B)};return function(B){if(B&&B.__esModule)return B;var Q={};if(B!=null){for(var D=A(B),Z=0;Z<D.length;Z++)if(D[Z]!=="default")CcQ(Q,B,D[Z])}return KcQ(Q,B),Q}}();Object.defineProperty(eN,"__esModule",{value:!0});eN.fromWebToken=void 0;var zcQ=(A)=>async(B)=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromWebToken");let{roleArn:Q,roleSessionName:D,webIdentityToken:Z,providerId:G,policyArns:F,policy:I,durationSeconds:Y}=A,{roleAssumerWithWebIdentity:W}=A;if(!W){let{getDefaultRoleAssumerWithWebIdentity:J}=await Promise.resolve().then(()=>HcQ(g20()));W=J({...A.clientConfig,credentialProviderLogger:A.logger,parentClientConfig:{...B?.callerClientConfig,...A.parentClientConfig}},A.clientPlugins)}return W({RoleArn:Q,RoleSessionName:D??`aws-sdk-js-session-${Date.now()}`,WebIdentityToken:Z,ProviderId:G,PolicyArns:F,Policy:I,DurationSeconds:Y})};eN.fromWebToken=zcQ});
var cA0=E((dvA)=>{Object.defineProperty(dvA,"__esModule",{value:!0});dvA.resolveHttpAuthSchemeConfig=dvA.defaultSSOOIDCHttpAuthSchemeProvider=dvA.defaultSSOOIDCHttpAuthSchemeParametersProvider=void 0;var $gQ=II(),dA0=I5(),qgQ=async(A,B,Q)=>{return{operation:dA0.getSmithyContext(B).operation,region:await dA0.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};dvA.defaultSSOOIDCHttpAuthSchemeParametersProvider=qgQ;function NgQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sso-oauth",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function LgQ(A){return{schemeId:"smithy.api#noAuth"}}var MgQ=(A)=>{let B=[];switch(A.operation){case"CreateToken":{B.push(LgQ(A));break}default:B.push(NgQ(A))}return B};dvA.defaultSSOOIDCHttpAuthSchemeProvider=MgQ;var RgQ=(A)=>{let B=$gQ.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:dA0.normalizeProvider(A.authSchemePreference??[])})};dvA.resolveHttpAuthSchemeConfig=RgQ});
var cfA=E((mfA)=>{Object.defineProperty(mfA,"__esModule",{value:!0});mfA.defaultEndpointResolver=void 0;var $mQ=on(),G20=$7(),qmQ=ufA(),NmQ=new G20.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),LmQ=(A,B={})=>{return NmQ.get(A,()=>G20.resolveEndpoint(qmQ.ruleSet,{endpointParams:A,logger:B.logger}))};mfA.defaultEndpointResolver=LmQ;G20.customEndpointFunctions.aws=$mQ.awsEndpointFunctions});
var cgA=E((mgA)=>{Object.defineProperty(mgA,"__esModule",{value:!0});mgA.getRuntimeConfig=void 0;var YlQ=II(),WlQ=VB(),JlQ=W6(),XlQ=BZ(),ggA=Ry(),ugA=cB(),VlQ=SA0(),ClQ=hgA(),KlQ=(A)=>{return{apiVersion:"2014-06-30",base64Decoder:A?.base64Decoder??ggA.fromBase64,base64Encoder:A?.base64Encoder??ggA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??ClQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??VlQ.defaultCognitoIdentityHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new YlQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new WlQ.NoAuthSigner}],logger:A?.logger??new JlQ.NoOpLogger,serviceId:A?.serviceId??"Cognito Identity",urlParser:A?.urlParser??XlQ.parseUrl,utf8Decoder:A?.utf8Decoder??ugA.fromUtf8,utf8Encoder:A?.utf8Encoder??ugA.toUtf8}};mgA.getRuntimeConfig=KlQ});
var ciA=E((miA)=>{Object.defineProperty(miA,"__esModule",{value:!0});miA.getRuntimeConfig=void 0;var reQ=ph(),oeQ=reQ.__importDefault(XiA()),fiA=XV(),hiA=N$1(),k$1=V4(),teQ=jG(),giA=v4(),Ig=QD(),uiA=S3(),eeQ=kG(),A14=hZ(),B14=biA(),Q14=H8(),D14=yG(),Z14=H8(),G14=(A)=>{Z14.emitWarningIfUnsupportedVersion(process.version);let B=D14.resolveDefaultsModeConfig(A),Q=()=>B().then(Q14.loadConfigsForDefaultMode),D=B14.getRuntimeConfig(A);fiA.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??Ig.loadConfig(fiA.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??eeQ.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??hiA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:oeQ.default.version}),maxAttempts:A?.maxAttempts??Ig.loadConfig(giA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??Ig.loadConfig(k$1.NODE_REGION_CONFIG_OPTIONS,{...k$1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:uiA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??Ig.loadConfig({...giA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||A14.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??teQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??uiA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??Ig.loadConfig(k$1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??Ig.loadConfig(k$1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??Ig.loadConfig(hiA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};miA.getRuntimeConfig=G14});
var d00=E((LG5,ZkA)=>{var{defineProperty:vU1,getOwnPropertyDescriptor:oyQ,getOwnPropertyNames:tyQ}=Object,eyQ=Object.prototype.hasOwnProperty,bU1=(A,B)=>vU1(A,"name",{value:B,configurable:!0}),A_Q=(A,B)=>{for(var Q in B)vU1(A,Q,{get:B[Q],enumerable:!0})},B_Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of tyQ(B))if(!eyQ.call(A,Z)&&Z!==Q)vU1(A,Z,{get:()=>B[Z],enumerable:!(D=oyQ(B,Z))||D.enumerable})}return A},Q_Q=(A)=>B_Q(vU1({},"__esModule",{value:!0}),A),rjA={};A_Q(rjA,{AlgorithmId:()=>AkA,EndpointURLScheme:()=>ejA,FieldPosition:()=>BkA,HttpApiKeyAuthLocation:()=>tjA,HttpAuthLocation:()=>ojA,IniSectionType:()=>QkA,RequestHandlerProtocol:()=>DkA,SMITHY_CONTEXT_KEY:()=>I_Q,getDefaultClientConfiguration:()=>G_Q,resolveDefaultRuntimeConfig:()=>F_Q});ZkA.exports=Q_Q(rjA);var ojA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(ojA||{}),tjA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(tjA||{}),ejA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(ejA||{}),AkA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(AkA||{}),D_Q=bU1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),Z_Q=bU1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),G_Q=bU1((A)=>{return D_Q(A)},"getDefaultClientConfiguration"),F_Q=bU1((A)=>{return Z_Q(A)},"resolveDefaultRuntimeConfig"),BkA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(BkA||{}),I_Q="__smithy_context",QkA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(QkA||{}),DkA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(DkA||{})});
var d20=E((VI5,rhA)=>{var{defineProperty:yw1,getOwnPropertyDescriptor:BcQ,getOwnPropertyNames:QcQ}=Object,DcQ=Object.prototype.hasOwnProperty,m20=(A,B)=>yw1(A,"name",{value:B,configurable:!0}),ZcQ=(A,B)=>{for(var Q in B)yw1(A,Q,{get:B[Q],enumerable:!0})},GcQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of QcQ(B))if(!DcQ.call(A,Z)&&Z!==Q)yw1(A,Z,{get:()=>B[Z],enumerable:!(D=BcQ(B,Z))||D.enumerable})}return A},FcQ=(A)=>GcQ(yw1({},"__esModule",{value:!0}),A),shA={};ZcQ(shA,{fromProcess:()=>VcQ});rhA.exports=FcQ(shA);var ahA=e5(),u20=eB(),IcQ=J1("child_process"),YcQ=J1("util"),WcQ=Lw(),JcQ=m20((A,B,Q)=>{if(B.Version!==1)throw Error(`Profile ${A} credential_process did not return Version 1.`);if(B.AccessKeyId===void 0||B.SecretAccessKey===void 0)throw Error(`Profile ${A} credential_process returned invalid credentials.`);if(B.Expiration){let G=new Date;if(new Date(B.Expiration)<G)throw Error(`Profile ${A} credential_process returned expired credentials.`)}let D=B.AccountId;if(!D&&Q?.[A]?.aws_account_id)D=Q[A].aws_account_id;let Z={accessKeyId:B.AccessKeyId,secretAccessKey:B.SecretAccessKey,...B.SessionToken&&{sessionToken:B.SessionToken},...B.Expiration&&{expiration:new Date(B.Expiration)},...B.CredentialScope&&{credentialScope:B.CredentialScope},...D&&{accountId:D}};return WcQ.setCredentialFeature(Z,"CREDENTIALS_PROCESS","w"),Z},"getValidatedProcessCredentials"),XcQ=m20(async(A,B,Q)=>{let D=B[A];if(B[A]){let Z=D.credential_process;if(Z!==void 0){let G=YcQ.promisify(IcQ.exec);try{let{stdout:F}=await G(Z),I;try{I=JSON.parse(F.trim())}catch{throw Error(`Profile ${A} credential_process returned invalid JSON.`)}return JcQ(A,I,B)}catch(F){throw new u20.CredentialsProviderError(F.message,{logger:Q})}}else throw new u20.CredentialsProviderError(`Profile ${A} did not contain credential_process.`,{logger:Q})}else throw new u20.CredentialsProviderError(`Profile ${A} could not be found in shared credentials file.`,{logger:Q})},"resolveProcessCredentials"),VcQ=m20((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-process - fromProcess");let Q=await ahA.parseKnownFiles(A);return XcQ(ahA.getProfileName({profile:A.profile??B?.profile}),Q,A.logger)},"fromProcess")});
var eA0=E((oF5,XfA)=>{var{defineProperty:Pw1,getOwnPropertyDescriptor:euQ,getOwnPropertyNames:GfA}=Object,AmQ=Object.prototype.hasOwnProperty,Sw1=(A,B)=>Pw1(A,"name",{value:B,configurable:!0}),BmQ=(A,B)=>function Q(){return A&&(B=A[GfA(A)[0]](A=0)),B},FfA=(A,B)=>{for(var Q in B)Pw1(A,Q,{get:B[Q],enumerable:!0})},QmQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of GfA(B))if(!AmQ.call(A,Z)&&Z!==Q)Pw1(A,Z,{get:()=>B[Z],enumerable:!(D=euQ(B,Z))||D.enumerable})}return A},DmQ=(A)=>QmQ(Pw1({},"__esModule",{value:!0}),A),IfA={};FfA(IfA,{GetRoleCredentialsCommand:()=>tA0.GetRoleCredentialsCommand,SSOClient:()=>tA0.SSOClient});var tA0,ZmQ=BmQ({"src/loadSso.ts"(){tA0=mvA()}}),YfA={};FfA(YfA,{fromSSO:()=>FmQ,isSsoProfile:()=>WfA,validateSsoProfile:()=>JfA});XfA.exports=DmQ(YfA);var WfA=Sw1((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),DfA=Lw(),GmQ=QfA(),Mw=eB(),Tw1=e5(),S41=!1,ZfA=Sw1(async({ssoStartUrl:A,ssoSession:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoClient:G,clientConfig:F,parentClientConfig:I,profile:Y,logger:W})=>{let J,X="To refresh this SSO session run aws sso login with the corresponding profile.";if(B)try{let f=await GmQ.fromSso({profile:Y})();J={accessToken:f.token,expiresAt:new Date(f.expiration).toISOString()}}catch(f){throw new Mw.CredentialsProviderError(f.message,{tryNextLink:S41,logger:W})}else try{J=await Tw1.getSSOTokenFromFile(A)}catch(f){throw new Mw.CredentialsProviderError("The SSO session associated with this profile is invalid. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:S41,logger:W})}if(new Date(J.expiresAt).getTime()-Date.now()<=0)throw new Mw.CredentialsProviderError("The SSO session associated with this profile has expired. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:S41,logger:W});let{accessToken:V}=J,{SSOClient:C,GetRoleCredentialsCommand:K}=await Promise.resolve().then(()=>(ZmQ(),IfA)),H=G||new C(Object.assign({},F??{},{logger:F?.logger??I?.logger,region:F?.region??D})),z;try{z=await H.send(new K({accountId:Q,roleName:Z,accessToken:V}))}catch(f){throw new Mw.CredentialsProviderError(f,{tryNextLink:S41,logger:W})}let{roleCredentials:{accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:O,credentialScope:R,accountId:T}={}}=z;if(!$||!L||!N||!O)throw new Mw.CredentialsProviderError("SSO returns an invalid temporary credential.",{tryNextLink:S41,logger:W});let j={accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:new Date(O),...R&&{credentialScope:R},...T&&{accountId:T}};if(B)DfA.setCredentialFeature(j,"CREDENTIALS_SSO","s");else DfA.setCredentialFeature(j,"CREDENTIALS_SSO_LEGACY","u");return j},"resolveSSOCredentials"),JfA=Sw1((A,B)=>{let{sso_start_url:Q,sso_account_id:D,sso_region:Z,sso_role_name:G}=A;if(!Q||!D||!Z||!G)throw new Mw.CredentialsProviderError(`Profile is configured with invalid SSO credentials. Required parameters "sso_account_id", "sso_region", "sso_role_name", "sso_start_url". Got ${Object.keys(A).join(", ")}
Reference: https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.html`,{tryNextLink:!1,logger:B});return A},"validateSsoProfile"),FmQ=Sw1((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-sso - fromSSO");let{ssoStartUrl:Q,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoSession:F}=A,{ssoClient:I}=A,Y=Tw1.getProfileName({profile:A.profile??B?.profile});if(!Q&&!D&&!Z&&!G&&!F){let J=(await Tw1.parseKnownFiles(A))[Y];if(!J)throw new Mw.CredentialsProviderError(`Profile ${Y} was not found.`,{logger:A.logger});if(!WfA(J))throw new Mw.CredentialsProviderError(`Profile ${Y} is not configured with SSO credentials.`,{logger:A.logger});if(J?.sso_session){let $=(await Tw1.loadSsoSessionData(A))[J.sso_session],L=` configurations in profile ${Y} and sso-session ${J.sso_session}`;if(Z&&Z!==$.sso_region)throw new Mw.CredentialsProviderError("Conflicting SSO region"+L,{tryNextLink:!1,logger:A.logger});if(Q&&Q!==$.sso_start_url)throw new Mw.CredentialsProviderError("Conflicting SSO start_url"+L,{tryNextLink:!1,logger:A.logger});J.sso_region=$.sso_region,J.sso_start_url=$.sso_start_url}let{sso_start_url:X,sso_account_id:V,sso_region:C,sso_role_name:K,sso_session:H}=JfA(J,A.logger);return ZfA({ssoStartUrl:X,ssoSession:H,ssoAccountId:V,ssoRegion:C,ssoRoleName:K,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})}else if(!Q||!D||!Z||!G)throw new Mw.CredentialsProviderError('Incomplete configuration. The fromSSO() argument hash must include "ssoStartUrl", "ssoAccountId", "ssoRegion", "ssoRoleName"',{tryNextLink:!1,logger:A.logger});else return ZfA({ssoStartUrl:Q,ssoSession:F,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})},"fromSSO")});
var f_A=E((tG5,b_A)=>{var{defineProperty:Yw1,getOwnPropertyDescriptor:ovQ,getOwnPropertyNames:tvQ}=Object,evQ=Object.prototype.hasOwnProperty,r6=(A,B)=>Yw1(A,"name",{value:B,configurable:!0}),AbQ=(A,B)=>{for(var Q in B)Yw1(A,Q,{get:B[Q],enumerable:!0})},BbQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of tvQ(B))if(!evQ.call(A,Z)&&Z!==Q)Yw1(A,Z,{get:()=>B[Z],enumerable:!(D=ovQ(B,Z))||D.enumerable})}return A},QbQ=(A)=>BbQ(Yw1({},"__esModule",{value:!0}),A),O_A={};AbQ(O_A,{AwsEc2QueryProtocol:()=>kbQ,AwsJson1_0Protocol:()=>zbQ,AwsJson1_1Protocol:()=>EbQ,AwsJsonRpcProtocol:()=>LA0,AwsQueryProtocol:()=>k_A,AwsRestJsonProtocol:()=>wbQ,AwsRestXmlProtocol:()=>fbQ,JsonCodec:()=>NA0,JsonShapeDeserializer:()=>S_A,JsonShapeSerializer:()=>j_A,XmlCodec:()=>v_A,XmlShapeDeserializer:()=>MA0,XmlShapeSerializer:()=>x_A,_toBool:()=>ZbQ,_toNum:()=>GbQ,_toStr:()=>DbQ,awsExpectUnion:()=>qbQ,loadRestJsonErrorCode:()=>qA0,loadRestXmlErrorCode:()=>__A,parseJsonBody:()=>$A0,parseJsonErrorBody:()=>XbQ,parseXmlBody:()=>y_A,parseXmlErrorBody:()=>vbQ});b_A.exports=QbQ(O_A);var DbQ=r6((A)=>{if(A==null)return A;if(typeof A==="number"||typeof A==="bigint"){let B=new Error(`Received number ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}if(typeof A==="boolean"){let B=new Error(`Received boolean ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}return A},"_toStr"),ZbQ=r6((A)=>{if(A==null)return A;if(typeof A==="string"){let B=A.toLowerCase();if(A!==""&&B!=="false"&&B!=="true"){let Q=new Error(`Received string "${A}" where a boolean was expected.`);Q.name="Warning",console.warn(Q)}return A!==""&&B!=="false"}return A},"_toBool"),GbQ=r6((A)=>{if(A==null)return A;if(typeof A==="string"){let B=Number(A);if(B.toString()!==A){let Q=new Error(`Received string "${A}" where a number was expected.`);return Q.name="Warning",console.warn(Q),A}return B}return A},"_toNum"),FbQ=$6(),Aa=jQ(),IbQ=wY(),sh=class{static{r6(this,"SerdeContextConfig")}serdeContext;setSerdeContext(A){this.serdeContext=A}},E41=jQ(),Ba=Y6(),YbQ=Ry(),WbQ=Y6();function T_A(A,B,Q){if(Q?.source){let D=Q.source;if(typeof B==="number"){if(B>Number.MAX_SAFE_INTEGER||B<Number.MIN_SAFE_INTEGER||D!==String(B))if(D.includes("."))return new WbQ.NumericValue(D,"bigDecimal");else return BigInt(D)}}return B}r6(T_A,"jsonReviver");var JbQ=W6(),P_A=r6((A,B)=>JbQ.collectBody(A,B).then((Q)=>B.utf8Encoder(Q)),"collectBodyString"),$A0=r6((A,B)=>P_A(A,B).then((Q)=>{if(Q.length)try{return JSON.parse(Q)}catch(D){if(D?.name==="SyntaxError")Object.defineProperty(D,"$responseBodyText",{value:Q});throw D}return{}}),"parseJsonBody"),XbQ=r6(async(A,B)=>{let Q=await $A0(A,B);return Q.message=Q.message??Q.Message,Q},"parseJsonErrorBody"),qA0=r6((A,B)=>{let Q=r6((G,F)=>Object.keys(G).find((I)=>I.toLowerCase()===F.toLowerCase()),"findKey"),D=r6((G)=>{let F=G;if(typeof F==="number")F=F.toString();if(F.indexOf(",")>=0)F=F.split(",")[0];if(F.indexOf(":")>=0)F=F.split(":")[0];if(F.indexOf("#")>=0)F=F.split("#")[1];return F},"sanitizeErrorCode"),Z=Q(A.headers,"x-amzn-errortype");if(Z!==void 0)return D(A.headers[Z]);if(B&&typeof B==="object"){let G=Q(B,"code");if(G&&B[G]!==void 0)return D(B[G]);if(B.__type!==void 0)return D(B.__type)}},"loadRestJsonErrorCode"),S_A=class extends sh{constructor(A){super();this.settings=A}static{r6(this,"JsonShapeDeserializer")}async read(A,B){return this._read(A,typeof B==="string"?JSON.parse(B,T_A):await $A0(B,this.serdeContext))}readObject(A,B){return this._read(A,B)}_read(A,B){let Q=B!==null&&typeof B==="object",D=E41.NormalizedSchema.of(A);if(D.isListSchema()&&Array.isArray(B)){let G=D.getValueSchema(),F=[],I=!!D.getMergedTraits().sparse;for(let Y of B)if(I||Y!=null)F.push(this._read(G,Y));return F}else if(D.isMapSchema()&&Q){let G=D.getValueSchema(),F={},I=!!D.getMergedTraits().sparse;for(let[Y,W]of Object.entries(B))if(I||W!=null)F[Y]=this._read(G,W);return F}else if(D.isStructSchema()&&Q){let G={};for(let[F,I]of D.structIterator()){let Y=this.settings.jsonName?I.getMergedTraits().jsonName??F:F,W=this._read(I,B[Y]);if(W!=null)G[F]=W}return G}if(D.isBlobSchema()&&typeof B==="string")return YbQ.fromBase64(B);let Z=D.getMergedTraits().mediaType;if(D.isStringSchema()&&typeof B==="string"&&Z){if(Z==="application/json"||Z.endsWith("+json"))return Ba.LazyJsonString.from(B)}if(D.isTimestampSchema()){let G=this.settings.timestampFormat;switch(G.useTrait?D.getSchema()===E41.SCHEMA.TIMESTAMP_DEFAULT?G.default:D.getSchema()??G.default:G.default){case E41.SCHEMA.TIMESTAMP_DATE_TIME:return Ba.parseRfc3339DateTimeWithOffset(B);case E41.SCHEMA.TIMESTAMP_HTTP_DATE:return Ba.parseRfc7231DateTime(B);case E41.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return Ba.parseEpochTimestamp(B);default:return console.warn("Missing timestamp format, parsing value with Date constructor:",B),new Date(B)}}if(D.isBigIntegerSchema()&&(typeof B==="number"||typeof B==="string"))return BigInt(B);if(D.isBigDecimalSchema()&&B!=null){if(B instanceof Ba.NumericValue)return B;return new Ba.NumericValue(String(B),"bigDecimal")}if(D.isNumericSchema()&&typeof B==="string")switch(B){case"Infinity":return 1/0;case"-Infinity":return-1/0;case"NaN":return NaN}return B}},Qa=jQ(),VbQ=Y6(),CbQ=Y6(),KbQ=Y6(),N_A=String.fromCharCode(925),HbQ=class{static{r6(this,"JsonReplacer")}values=new Map;counter=0;stage=0;createReplacer(){if(this.stage===1)throw new Error("@aws-sdk/core/protocols - JsonReplacer already created.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");return this.stage=1,(A,B)=>{if(B instanceof KbQ.NumericValue){let Q=`${N_A+NaN+this.counter++}_`+B.string;return this.values.set(`"${Q}"`,B.string),Q}if(typeof B==="bigint"){let Q=B.toString(),D=`${N_A+"b"+this.counter++}_`+Q;return this.values.set(`"${D}"`,Q),D}return B}}replaceInJson(A){if(this.stage===0)throw new Error("@aws-sdk/core/protocols - JsonReplacer not created yet.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");if(this.stage=2,this.counter===0)return A;for(let[B,Q]of this.values)A=A.replace(B,Q);return A}},j_A=class extends sh{constructor(A){super();this.settings=A}static{r6(this,"JsonShapeSerializer")}buffer;rootSchema;write(A,B){this.rootSchema=Qa.NormalizedSchema.of(A),this.buffer=this._write(this.rootSchema,B)}flush(){if(this.rootSchema?.isStructSchema()||this.rootSchema?.isDocumentSchema()){let A=new HbQ;return A.replaceInJson(JSON.stringify(this.buffer,A.createReplacer(),0))}return this.buffer}_write(A,B,Q){let D=B!==null&&typeof B==="object",Z=Qa.NormalizedSchema.of(A);if(Z.isListSchema()&&Array.isArray(B)){let F=Z.getValueSchema(),I=[],Y=!!Z.getMergedTraits().sparse;for(let W of B)if(Y||W!=null)I.push(this._write(F,W));return I}else if(Z.isMapSchema()&&D){let F=Z.getValueSchema(),I={},Y=!!Z.getMergedTraits().sparse;for(let[W,J]of Object.entries(B))if(Y||J!=null)I[W]=this._write(F,J);return I}else if(Z.isStructSchema()&&D){let F={};for(let[I,Y]of Z.structIterator()){let W=this.settings.jsonName?Y.getMergedTraits().jsonName??I:I,J=this._write(Y,B[I],Z);if(J!==void 0)F[W]=J}return F}if(B===null&&Q?.isStructSchema())return;if(Z.isBlobSchema()&&(B instanceof Uint8Array||typeof B==="string")){if(Z===this.rootSchema)return B;if(!this.serdeContext?.base64Encoder)throw new Error("Missing base64Encoder in serdeContext");return this.serdeContext?.base64Encoder(B)}if(Z.isTimestampSchema()&&B instanceof Date){let F=this.settings.timestampFormat;switch(F.useTrait?Z.getSchema()===Qa.SCHEMA.TIMESTAMP_DEFAULT?F.default:Z.getSchema()??F.default:F.default){case Qa.SCHEMA.TIMESTAMP_DATE_TIME:return B.toISOString().replace(".000Z","Z");case Qa.SCHEMA.TIMESTAMP_HTTP_DATE:return VbQ.dateToUtcString(B);case Qa.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return B.getTime()/1000;default:return console.warn("Missing timestamp format, using epoch seconds",B),B.getTime()/1000}}if(Z.isNumericSchema()&&typeof B==="number"){if(Math.abs(B)===1/0||isNaN(B))return String(B)}let G=Z.getMergedTraits().mediaType;if(Z.isStringSchema()&&typeof B==="string"&&G){if(G==="application/json"||G.endsWith("+json"))return CbQ.LazyJsonString.from(B)}return B}},NA0=class extends sh{constructor(A){super();this.settings=A}static{r6(this,"JsonCodec")}createSerializer(){let A=new j_A(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new S_A(this.settings);return A.setSerdeContext(this.serdeContext),A}},LA0=class extends FbQ.RpcProtocol{static{r6(this,"AwsJsonRpcProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});this.codec=new NA0({timestampFormat:{useTrait:!0,default:Aa.SCHEMA.TIMESTAMP_EPOCH_SECONDS},jsonName:!1}),this.serializer=this.codec.createSerializer(),this.deserializer=this.codec.createDeserializer()}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":`application/x-amz-json-${this.getJsonRpcVersion()}`,"x-amz-target":(this.getJsonRpcVersion()==="1.0"?"JsonRpc10.":"JsonProtocol.")+Aa.NormalizedSchema.of(A).getName()}),Aa.deref(A.input)==="unit"||!D.body)D.body="{}";try{D.headers["content-length"]=String(IbQ.calculateBodyLength(D.body))}catch(Z){}return D}getPayloadCodec(){return this.codec}async handleError(A,B,Q,D,Z){let G=qA0(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=Aa.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=Aa.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=Aa.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},zbQ=class extends LA0{static{r6(this,"AwsJson1_0Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_0"}getJsonRpcVersion(){return"1.0"}},EbQ=class extends LA0{static{r6(this,"AwsJson1_1Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_1"}getJsonRpcVersion(){return"1.1"}},zA0=$6(),U41=jQ(),UbQ=wY(),wbQ=class extends zA0.HttpBindingProtocol{static{r6(this,"AwsRestJsonProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});let B={timestampFormat:{useTrait:!0,default:U41.SCHEMA.TIMESTAMP_EPOCH_SECONDS},httpBindings:!0,jsonName:!0};this.codec=new NA0(B),this.serializer=new zA0.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new zA0.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getShapeId(){return"aws.protocols#restJson1"}getPayloadCodec(){return this.codec}setSerdeContext(A){this.codec.setSerdeContext(A),super.setSerdeContext(A)}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=U41.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/json"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/json"}}if(D.headers["content-type"]&&!D.body)D.body="{}";if(D.body)try{D.headers["content-length"]=String(UbQ.calculateBodyLength(D.body))}catch(F){}return D}async handleError(A,B,Q,D,Z){let G=qA0(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=U41.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=U41.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=U41.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},$bQ=W6(),qbQ=r6((A)=>{if(A==null)return;if(typeof A==="object"&&"__type"in A)delete A.__type;return $bQ.expectUnion(A)},"awsExpectUnion"),EA0=$6(),Oy=jQ(),NbQ=wY(),LbQ=$6(),L_A=jQ(),MbQ=W6(),RbQ=cB(),ObQ=gN(),MA0=class extends sh{constructor(A){super();this.settings=A,this.stringDeserializer=new LbQ.FromStringShapeDeserializer(A)}static{r6(this,"XmlShapeDeserializer")}stringDeserializer;setSerdeContext(A){this.serdeContext=A,this.stringDeserializer.setSerdeContext(A)}read(A,B,Q){let D=L_A.NormalizedSchema.of(A),Z=D.getMemberSchemas();if(D.isStructSchema()&&D.isMemberSchema()&&!!Object.values(Z).find((Y)=>{return!!Y.getMemberTraits().eventPayload})){let Y={},W=Object.keys(Z)[0];if(Z[W].isBlobSchema())Y[W]=B;else Y[W]=this.read(Z[W],B);return Y}let F=(this.serdeContext?.utf8Encoder??RbQ.toUtf8)(B),I=this.parseXml(F);return this.readSchema(A,Q?I[Q]:I)}readSchema(A,B){let Q=L_A.NormalizedSchema.of(A),D=Q.getMergedTraits(),Z=Q.getSchema();if(Q.isListSchema()&&!Array.isArray(B))return this.readSchema(Z,[B]);if(B==null)return B;if(typeof B==="object"){let G=!!D.sparse,F=!!D.xmlFlattened;if(Q.isListSchema()){let Y=Q.getValueSchema(),W=[],J=Y.getMergedTraits().xmlName??"member",X=F?B:(B[0]??B)[J],V=Array.isArray(X)?X:[X];for(let C of V)if(C!=null||G)W.push(this.readSchema(Y,C));return W}let I={};if(Q.isMapSchema()){let Y=Q.getKeySchema(),W=Q.getValueSchema(),J;if(F)J=Array.isArray(B)?B:[B];else J=Array.isArray(B.entry)?B.entry:[B.entry];let X=Y.getMergedTraits().xmlName??"key",V=W.getMergedTraits().xmlName??"value";for(let C of J){let K=C[X],H=C[V];if(H!=null||G)I[K]=this.readSchema(W,H)}return I}if(Q.isStructSchema()){for(let[Y,W]of Q.structIterator()){let J=W.getMergedTraits(),X=!J.httpPayload?W.getMemberTraits().xmlName??Y:J.xmlName??W.getName();if(B[X]!=null)I[Y]=this.readSchema(W,B[X])}return I}if(Q.isDocumentSchema())return B;throw new Error(`@aws-sdk/core/protocols - xml deserializer unhandled schema type for ${Q.getName(!0)}`)}else{if(Q.isListSchema())return[];else if(Q.isMapSchema()||Q.isStructSchema())return{};return this.stringDeserializer.read(Q,B)}}parseXml(A){if(A.length){let B=new ObQ.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:r6((F,I)=>I.trim()===""&&I.includes(`
`)?"":void 0,"tagValueProcessor")});B.addEntity("#xD","\r"),B.addEntity("#10",`
`);let Q;try{Q=B.parse(A,!0)}catch(F){if(F&&typeof F==="object")Object.defineProperty(F,"$responseBodyText",{value:A});throw F}let D="#text",Z=Object.keys(Q)[0],G=Q[Z];if(G[D])G[Z]=G[D],delete G[D];return MbQ.getValueFromTextNode(G)}return{}}},UA0=$6(),Iw1=jQ(),TbQ=Y6(),PbQ=W6(),SbQ=Ry(),jbQ=class extends sh{constructor(A){super();this.settings=A}static{r6(this,"QueryShapeSerializer")}buffer;write(A,B,Q=""){if(this.buffer===void 0)this.buffer="";let D=Iw1.NormalizedSchema.of(A);if(Q&&!Q.endsWith("."))Q+=".";if(D.isBlobSchema()){if(typeof B==="string"||B instanceof Uint8Array)this.writeKey(Q),this.writeValue((this.serdeContext?.base64Encoder??SbQ.toBase64)(B))}else if(D.isBooleanSchema()||D.isNumericSchema()||D.isStringSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigIntegerSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigDecimalSchema()){if(B!=null)this.writeKey(Q),this.writeValue(B instanceof TbQ.NumericValue?B.string:String(B))}else if(D.isTimestampSchema()){if(B instanceof Date)switch(this.writeKey(Q),UA0.determineTimestampFormat(D,this.settings)){case Iw1.SCHEMA.TIMESTAMP_DATE_TIME:this.writeValue(B.toISOString().replace(".000Z","Z"));break;case Iw1.SCHEMA.TIMESTAMP_HTTP_DATE:this.writeValue(PbQ.dateToUtcString(B));break;case Iw1.SCHEMA.TIMESTAMP_EPOCH_SECONDS:this.writeValue(String(B.getTime()/1000));break}}else if(D.isDocumentSchema())throw new Error(`@aws-sdk/core/protocols - QuerySerializer unsupported document type ${D.getName(!0)}`);else if(D.isListSchema()){if(Array.isArray(B))if(B.length===0){if(this.settings.serializeEmptyLists)this.writeKey(Q),this.writeValue("")}else{let Z=D.getValueSchema(),G=this.settings.flattenLists||D.getMergedTraits().xmlFlattened,F=1;for(let I of B){if(I==null)continue;let Y=this.getKey("member",Z.getMergedTraits().xmlName),W=G?`${Q}${F}`:`${Q}${Y}.${F}`;this.write(Z,I,W),++F}}}else if(D.isMapSchema()){if(B&&typeof B==="object"){let Z=D.getKeySchema(),G=D.getValueSchema(),F=D.getMergedTraits().xmlFlattened,I=1;for(let[Y,W]of Object.entries(B)){if(W==null)continue;let J=this.getKey("key",Z.getMergedTraits().xmlName),X=F?`${Q}${I}.${J}`:`${Q}entry.${I}.${J}`,V=this.getKey("value",G.getMergedTraits().xmlName),C=F?`${Q}${I}.${V}`:`${Q}entry.${I}.${V}`;this.write(Z,Y,X),this.write(G,W,C),++I}}}else if(D.isStructSchema()){if(B&&typeof B==="object")for(let[Z,G]of D.structIterator()){if(B[Z]==null)continue;let F=this.getKey(Z,G.getMergedTraits().xmlName),I=`${Q}${F}`;this.write(G,B[Z],I)}}else if(D.isUnitSchema());else throw new Error(`@aws-sdk/core/protocols - QuerySerializer unrecognized schema type ${D.getName(!0)}`)}flush(){if(this.buffer===void 0)throw new Error("@aws-sdk/core/protocols - QuerySerializer cannot flush with nothing written to buffer.");let A=this.buffer;return delete this.buffer,A}getKey(A,B){let Q=B??A;if(this.settings.capitalizeKeys)return Q[0].toUpperCase()+Q.slice(1);return Q}writeKey(A){if(A.endsWith("."))A=A.slice(0,A.length-1);this.buffer+=`&${UA0.extendedEncodeURIComponent(A)}=`}writeValue(A){this.buffer+=UA0.extendedEncodeURIComponent(A)}},k_A=class extends EA0.RpcProtocol{constructor(A){super({defaultNamespace:A.defaultNamespace});this.options=A;let B={timestampFormat:{useTrait:!0,default:Oy.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!1,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace,serializeEmptyLists:!0};this.serializer=new jbQ(B),this.deserializer=new MA0(B)}static{r6(this,"AwsQueryProtocol")}serializer;deserializer;getShapeId(){return"aws.protocols#awsQuery"}setSerdeContext(A){this.serializer.setSerdeContext(A),this.deserializer.setSerdeContext(A)}getPayloadCodec(){throw new Error("AWSQuery protocol has no payload codec.")}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":"application/x-www-form-urlencoded"}),Oy.deref(A.input)==="unit"||!D.body)D.body="";if(D.body=`Action=${A.name.split("#")[1]}&Version=${this.options.version}`+D.body,D.body.endsWith("&"))D.body=D.body.slice(-1);try{D.headers["content-length"]=String(NbQ.calculateBodyLength(D.body))}catch(Z){}return D}async deserializeResponse(A,B,Q){let D=this.deserializer,Z=Oy.NormalizedSchema.of(A.output),G={};if(Q.statusCode>=300){let W=await EA0.collectBody(Q.body,B);if(W.byteLength>0)Object.assign(G,await D.read(Oy.SCHEMA.DOCUMENT,W));await this.handleError(A,B,Q,G,this.deserializeMetadata(Q))}for(let W in Q.headers){let J=Q.headers[W];delete Q.headers[W],Q.headers[W.toLowerCase()]=J}let F=Z.isStructSchema()&&this.useNestedResult()?A.name.split("#")[1]+"Result":void 0,I=await EA0.collectBody(Q.body,B);if(I.byteLength>0)Object.assign(G,await D.read(Z,I,F));return{$metadata:this.deserializeMetadata(Q),...G}}useNestedResult(){return!0}async handleError(A,B,Q,D,Z){let G=this.loadQueryErrorCode(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=this.loadQueryError(D),W=Oy.TypeRegistry.for(F),J;try{if(J=W.find((H)=>Oy.NormalizedSchema.of(H).getMergedTraits().awsQueryError?.[0]===I),!J)J=W.getSchema(G)}catch(H){let z=Oy.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(z){let $=z.ctor;throw Object.assign(new $(I),Y)}throw new Error(I)}let X=Oy.NormalizedSchema.of(J),V=this.loadQueryErrorMessage(D),C=new J.ctor(V),K={};for(let[H,z]of X.structIterator()){let $=z.getMergedTraits().xmlName??H,L=Y[$]??D[$];K[H]=this.deserializer.readSchema(z,L)}throw Object.assign(C,{$metadata:Z,$response:Q,$fault:X.getMergedTraits().error,message:V,...K}),C}loadQueryErrorCode(A,B){let Q=(B.Errors?.[0]?.Error??B.Errors?.Error??B.Error)?.Code;if(Q!==void 0)return Q;if(A.statusCode==404)return"NotFound"}loadQueryError(A){return A.Errors?.[0]?.Error??A.Errors?.Error??A.Error}loadQueryErrorMessage(A){let B=this.loadQueryError(A);return B?.message??B?.Message??A.message??A.Message??"Unknown"}},kbQ=class extends k_A{constructor(A){super(A);this.options=A;let B={capitalizeKeys:!0,flattenLists:!0,serializeEmptyLists:!1};Object.assign(this.serializer.settings,B)}static{r6(this,"AwsEc2QueryProtocol")}useNestedResult(){return!1}},wA0=$6(),w41=jQ(),ybQ=wY(),_bQ=W6(),xbQ=gN(),y_A=r6((A,B)=>P_A(A,B).then((Q)=>{if(Q.length){let D=new xbQ.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:r6((Y,W)=>W.trim()===""&&W.includes(`
`)?"":void 0,"tagValueProcessor")});D.addEntity("#xD","\r"),D.addEntity("#10",`
`);let Z;try{Z=D.parse(Q,!0)}catch(Y){if(Y&&typeof Y==="object")Object.defineProperty(Y,"$responseBodyText",{value:Q});throw Y}let G="#text",F=Object.keys(Z)[0],I=Z[F];if(I[G])I[F]=I[G],delete I[G];return _bQ.getValueFromTextNode(I)}return{}}),"parseXmlBody"),vbQ=r6(async(A,B)=>{let Q=await y_A(A,B);if(Q.Error)Q.Error.message=Q.Error.message??Q.Error.Message;return Q},"parseXmlErrorBody"),__A=r6((A,B)=>{if(B?.Error?.Code!==void 0)return B.Error.Code;if(B?.Code!==void 0)return B.Code;if(A.statusCode==404)return"NotFound"},"loadRestXmlErrorCode"),sN=gQ1(),ah=jQ(),bbQ=Y6(),M_A=W6(),R_A=Ry(),x_A=class extends sh{constructor(A){super();this.settings=A}static{r6(this,"XmlShapeSerializer")}stringBuffer;byteBuffer;buffer;write(A,B){let Q=ah.NormalizedSchema.of(A);if(Q.isStringSchema()&&typeof B==="string")this.stringBuffer=B;else if(Q.isBlobSchema())this.byteBuffer="byteLength"in B?B:(this.serdeContext?.base64Decoder??R_A.fromBase64)(B);else{this.buffer=this.writeStruct(Q,B,void 0);let D=Q.getMergedTraits();if(D.httpPayload&&!D.xmlName)this.buffer.withName(Q.getName())}}flush(){if(this.byteBuffer!==void 0){let B=this.byteBuffer;return delete this.byteBuffer,B}if(this.stringBuffer!==void 0){let B=this.stringBuffer;return delete this.stringBuffer,B}let A=this.buffer;if(this.settings.xmlNamespace){if(!A?.attributes?.xmlns)A.addAttribute("xmlns",this.settings.xmlNamespace)}return delete this.buffer,A.toString()}writeStruct(A,B,Q){let D=A.getMergedTraits(),Z=A.isMemberSchema()&&!D.httpPayload?A.getMemberTraits().xmlName??A.getMemberName():D.xmlName??A.getName();if(!Z||!A.isStructSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write struct with empty name or non-struct, schema=${A.getName(!0)}.`);let G=sN.XmlNode.of(Z),[F,I]=this.getXmlnsAttribute(A,Q);if(I)G.addAttribute(F,I);for(let[Y,W]of A.structIterator()){let J=B[Y];if(J!=null){if(W.getMergedTraits().xmlAttribute){G.addAttribute(W.getMergedTraits().xmlName??Y,this.writeSimple(W,J));continue}if(W.isListSchema())this.writeList(W,J,G,I);else if(W.isMapSchema())this.writeMap(W,J,G,I);else if(W.isStructSchema())G.addChildNode(this.writeStruct(W,J,I));else{let X=sN.XmlNode.of(W.getMergedTraits().xmlName??W.getMemberName());this.writeSimpleInto(W,J,X,I),G.addChildNode(X)}}}return G}writeList(A,B,Q,D){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member list: ${A.getName(!0)}`);let Z=A.getMergedTraits(),G=A.getValueSchema(),F=G.getMergedTraits(),I=!!F.sparse,Y=!!Z.xmlFlattened,[W,J]=this.getXmlnsAttribute(A,D),X=r6((V,C)=>{if(G.isListSchema())this.writeList(G,Array.isArray(C)?C:[C],V,J);else if(G.isMapSchema())this.writeMap(G,C,V,J);else if(G.isStructSchema()){let K=this.writeStruct(G,C,J);V.addChildNode(K.withName(Y?Z.xmlName??A.getMemberName():F.xmlName??"member"))}else{let K=sN.XmlNode.of(Y?Z.xmlName??A.getMemberName():F.xmlName??"member");this.writeSimpleInto(G,C,K,J),V.addChildNode(K)}},"writeItem");if(Y){for(let V of B)if(I||V!=null)X(Q,V)}else{let V=sN.XmlNode.of(Z.xmlName??A.getMemberName());if(J)V.addAttribute(W,J);for(let C of B)if(I||C!=null)X(V,C);Q.addChildNode(V)}}writeMap(A,B,Q,D,Z=!1){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member map: ${A.getName(!0)}`);let G=A.getMergedTraits(),F=A.getKeySchema(),Y=F.getMergedTraits().xmlName??"key",W=A.getValueSchema(),J=W.getMergedTraits(),X=J.xmlName??"value",V=!!J.sparse,C=!!G.xmlFlattened,[K,H]=this.getXmlnsAttribute(A,D),z=r6(($,L,N)=>{let O=sN.XmlNode.of(Y,L),[R,T]=this.getXmlnsAttribute(F,H);if(T)O.addAttribute(R,T);$.addChildNode(O);let j=sN.XmlNode.of(X);if(W.isListSchema())this.writeList(W,N,j,H);else if(W.isMapSchema())this.writeMap(W,N,j,H,!0);else if(W.isStructSchema())j=this.writeStruct(W,N,H);else this.writeSimpleInto(W,N,j,H);$.addChildNode(j)},"addKeyValue");if(C){for(let[$,L]of Object.entries(B))if(V||L!=null){let N=sN.XmlNode.of(G.xmlName??A.getMemberName());z(N,$,L),Q.addChildNode(N)}}else{let $;if(!Z){if($=sN.XmlNode.of(G.xmlName??A.getMemberName()),H)$.addAttribute(K,H);Q.addChildNode($)}for(let[L,N]of Object.entries(B))if(V||N!=null){let O=sN.XmlNode.of("entry");z(O,L,N),(Z?Q:$).addChildNode(O)}}}writeSimple(A,B){if(B===null)throw new Error("@aws-sdk/core/protocols - (XML serializer) cannot write null value.");let Q=ah.NormalizedSchema.of(A),D=null;if(B&&typeof B==="object")if(Q.isBlobSchema())D=(this.serdeContext?.base64Encoder??R_A.toBase64)(B);else if(Q.isTimestampSchema()&&B instanceof Date){let Z=this.settings.timestampFormat;switch(Z.useTrait?Q.getSchema()===ah.SCHEMA.TIMESTAMP_DEFAULT?Z.default:Q.getSchema()??Z.default:Z.default){case ah.SCHEMA.TIMESTAMP_DATE_TIME:D=B.toISOString().replace(".000Z","Z");break;case ah.SCHEMA.TIMESTAMP_HTTP_DATE:D=M_A.dateToUtcString(B);break;case ah.SCHEMA.TIMESTAMP_EPOCH_SECONDS:D=String(B.getTime()/1000);break;default:console.warn("Missing timestamp format, using http date",B),D=M_A.dateToUtcString(B);break}}else if(Q.isBigDecimalSchema()&&B){if(B instanceof bbQ.NumericValue)return B.string;return String(B)}else if(Q.isMapSchema()||Q.isListSchema())throw new Error("@aws-sdk/core/protocols - xml serializer, cannot call _write() on List/Map schema, call writeList or writeMap() instead.");else throw new Error(`@aws-sdk/core/protocols - xml serializer, unhandled schema type for object value and schema: ${Q.getName(!0)}`);if(Q.isStringSchema()||Q.isBooleanSchema()||Q.isNumericSchema()||Q.isBigIntegerSchema()||Q.isBigDecimalSchema())D=String(B);if(D===null)throw new Error(`Unhandled schema-value pair ${Q.getName(!0)}=${B}`);return D}writeSimpleInto(A,B,Q,D){let Z=this.writeSimple(A,B),G=ah.NormalizedSchema.of(A),F=new sN.XmlText(Z),[I,Y]=this.getXmlnsAttribute(G,D);if(Y)Q.addAttribute(I,Y);Q.addChildNode(F)}getXmlnsAttribute(A,B){let Q=A.getMergedTraits(),[D,Z]=Q.xmlNamespace??[];if(Z&&Z!==B)return[D?`xmlns:${D}`:"xmlns",Z];return[void 0,void 0]}},v_A=class extends sh{constructor(A){super();this.settings=A}static{r6(this,"XmlCodec")}createSerializer(){let A=new x_A(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new MA0(this.settings);return A.setSerdeContext(this.serdeContext),A}},fbQ=class extends wA0.HttpBindingProtocol{static{r6(this,"AwsRestXmlProtocol")}codec;serializer;deserializer;constructor(A){super(A);let B={timestampFormat:{useTrait:!0,default:w41.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!0,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace};this.codec=new v_A(B),this.serializer=new wA0.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new wA0.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getPayloadCodec(){return this.codec}getShapeId(){return"aws.protocols#restXml"}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=w41.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(D.path=String(D.path).split("/").filter((F)=>{return F!=="{Bucket}"}).join("/")||"/",!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/xml"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/xml"}}if(D.headers["content-type"]==="application/xml"){if(typeof D.body==="string")D.body='<?xml version="1.0" encoding="UTF-8"?>'+D.body}if(D.body)try{D.headers["content-length"]=String(ybQ.calculateBodyLength(D.body))}catch(F){}return D}async deserializeResponse(A,B,Q){return super.deserializeResponse(A,B,Q)}async handleError(A,B,Q,D,Z){let G=__A(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=w41.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=w41.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=w41.NormalizedSchema.of(W),X=D.Error?.message??D.Error?.Message??D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().xmlName??K,$=D.Error?.[z]??D[z];C[K]=this.codec.createDeserializer().readSchema(H,$)}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}}});
var fdA=E((LY5,bdA)=>{var{defineProperty:G$1,getOwnPropertyDescriptor:daQ,getOwnPropertyNames:caQ}=Object,laQ=Object.prototype.hasOwnProperty,paQ=(A,B)=>G$1(A,"name",{value:B,configurable:!0}),iaQ=(A,B)=>{for(var Q in B)G$1(A,Q,{get:B[Q],enumerable:!0})},naQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of caQ(B))if(!laQ.call(A,Z)&&Z!==Q)G$1(A,Z,{get:()=>B[Z],enumerable:!(D=daQ(B,Z))||D.enumerable})}return A},aaQ=(A)=>naQ(G$1({},"__esModule",{value:!0}),A),vdA={};iaQ(vdA,{isArrayBuffer:()=>saQ});bdA.exports=aaQ(vdA);var saQ=paQ((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var g20=E((YI5,h20)=>{var{defineProperty:kw1,getOwnPropertyDescriptor:AdQ,getOwnPropertyNames:BdQ}=Object,QdQ=Object.prototype.hasOwnProperty,B9=(A,B)=>kw1(A,"name",{value:B,configurable:!0}),DdQ=(A,B)=>{for(var Q in B)kw1(A,Q,{get:B[Q],enumerable:!0})},k20=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of BdQ(B))if(!QdQ.call(A,Z)&&Z!==Q)kw1(A,Z,{get:()=>B[Z],enumerable:!(D=AdQ(B,Z))||D.enumerable})}return A},ZdQ=(A,B,Q)=>(k20(A,B,"default"),Q&&k20(Q,B,"default")),GdQ=(A)=>k20(kw1({},"__esModule",{value:!0}),A),_20={};DdQ(_20,{AssumeRoleCommand:()=>b20,AssumeRoleResponseFilterSensitiveLog:()=>$hA,AssumeRoleWithWebIdentityCommand:()=>f20,AssumeRoleWithWebIdentityRequestFilterSensitiveLog:()=>ThA,AssumeRoleWithWebIdentityResponseFilterSensitiveLog:()=>PhA,ClientInputEndpointParameters:()=>odQ.ClientInputEndpointParameters,CredentialsFilterSensitiveLog:()=>x20,ExpiredTokenException:()=>qhA,IDPCommunicationErrorException:()=>ShA,IDPRejectedClaimException:()=>RhA,InvalidIdentityTokenException:()=>OhA,MalformedPolicyDocumentException:()=>NhA,PackedPolicyTooLargeException:()=>LhA,RegionDisabledException:()=>MhA,STS:()=>uhA,STSServiceException:()=>JT,decorateDefaultCredentialProvider:()=>AcQ,getDefaultRoleAssumer:()=>ihA,getDefaultRoleAssumerWithWebIdentity:()=>nhA});h20.exports=GdQ(_20);ZdQ(_20,j41(),h20.exports);var FdQ=W6(),IdQ=q6(),YdQ=T3(),WdQ=W6(),JdQ=k41(),whA=W6(),XdQ=W6(),JT=class A extends XdQ.ServiceException{static{B9(this,"STSServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},x20=B9((A)=>({...A,...A.SecretAccessKey&&{SecretAccessKey:whA.SENSITIVE_STRING}}),"CredentialsFilterSensitiveLog"),$hA=B9((A)=>({...A,...A.Credentials&&{Credentials:x20(A.Credentials)}}),"AssumeRoleResponseFilterSensitiveLog"),qhA=class A extends JT{static{B9(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},NhA=class A extends JT{static{B9(this,"MalformedPolicyDocumentException")}name="MalformedPolicyDocumentException";$fault="client";constructor(B){super({name:"MalformedPolicyDocumentException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},LhA=class A extends JT{static{B9(this,"PackedPolicyTooLargeException")}name="PackedPolicyTooLargeException";$fault="client";constructor(B){super({name:"PackedPolicyTooLargeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},MhA=class A extends JT{static{B9(this,"RegionDisabledException")}name="RegionDisabledException";$fault="client";constructor(B){super({name:"RegionDisabledException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},RhA=class A extends JT{static{B9(this,"IDPRejectedClaimException")}name="IDPRejectedClaimException";$fault="client";constructor(B){super({name:"IDPRejectedClaimException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},OhA=class A extends JT{static{B9(this,"InvalidIdentityTokenException")}name="InvalidIdentityTokenException";$fault="client";constructor(B){super({name:"InvalidIdentityTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},ThA=B9((A)=>({...A,...A.WebIdentityToken&&{WebIdentityToken:whA.SENSITIVE_STRING}}),"AssumeRoleWithWebIdentityRequestFilterSensitiveLog"),PhA=B9((A)=>({...A,...A.Credentials&&{Credentials:x20(A.Credentials)}}),"AssumeRoleWithWebIdentityResponseFilterSensitiveLog"),ShA=class A extends JT{static{B9(this,"IDPCommunicationErrorException")}name="IDPCommunicationErrorException";$fault="client";constructor(B){super({name:"IDPCommunicationErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},v20=II(),VdQ=JV(),W5=W6(),CdQ=B9(async(A,B)=>{let Q=vhA,D;return D=ghA({...MdQ(A,B),[fhA]:ddQ,[hhA]:bhA}),xhA(B,Q,"/",void 0,D)},"se_AssumeRoleCommand"),KdQ=B9(async(A,B)=>{let Q=vhA,D;return D=ghA({...RdQ(A,B),[fhA]:cdQ,[hhA]:bhA}),xhA(B,Q,"/",void 0,D)},"se_AssumeRoleWithWebIdentityCommand"),HdQ=B9(async(A,B)=>{if(A.statusCode>=300)return jhA(A,B);let Q=await v20.parseXmlBody(A.body,B),D={};return D=ydQ(Q.AssumeRoleResult,B),{$metadata:XT(A),...D}},"de_AssumeRoleCommand"),zdQ=B9(async(A,B)=>{if(A.statusCode>=300)return jhA(A,B);let Q=await v20.parseXmlBody(A.body,B),D={};return D=_dQ(Q.AssumeRoleWithWebIdentityResult,B),{$metadata:XT(A),...D}},"de_AssumeRoleWithWebIdentityCommand"),jhA=B9(async(A,B)=>{let Q={...A,body:await v20.parseXmlErrorBody(A.body,B)},D=ldQ(A,Q.body);switch(D){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await EdQ(Q,B);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await qdQ(Q,B);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await NdQ(Q,B);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await LdQ(Q,B);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await UdQ(Q,B);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await wdQ(Q,B);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await $dQ(Q,B);default:let Z=Q.body;return mdQ({output:A,parsedBody:Z.Error,errorCode:D})}},"de_CommandError"),EdQ=B9(async(A,B)=>{let Q=A.body,D=xdQ(Q.Error,B),Z=new qhA({$metadata:XT(A),...D});return W5.decorateServiceException(Z,Q)},"de_ExpiredTokenExceptionRes"),UdQ=B9(async(A,B)=>{let Q=A.body,D=vdQ(Q.Error,B),Z=new ShA({$metadata:XT(A),...D});return W5.decorateServiceException(Z,Q)},"de_IDPCommunicationErrorExceptionRes"),wdQ=B9(async(A,B)=>{let Q=A.body,D=bdQ(Q.Error,B),Z=new RhA({$metadata:XT(A),...D});return W5.decorateServiceException(Z,Q)},"de_IDPRejectedClaimExceptionRes"),$dQ=B9(async(A,B)=>{let Q=A.body,D=fdQ(Q.Error,B),Z=new OhA({$metadata:XT(A),...D});return W5.decorateServiceException(Z,Q)},"de_InvalidIdentityTokenExceptionRes"),qdQ=B9(async(A,B)=>{let Q=A.body,D=hdQ(Q.Error,B),Z=new NhA({$metadata:XT(A),...D});return W5.decorateServiceException(Z,Q)},"de_MalformedPolicyDocumentExceptionRes"),NdQ=B9(async(A,B)=>{let Q=A.body,D=gdQ(Q.Error,B),Z=new LhA({$metadata:XT(A),...D});return W5.decorateServiceException(Z,Q)},"de_PackedPolicyTooLargeExceptionRes"),LdQ=B9(async(A,B)=>{let Q=A.body,D=udQ(Q.Error,B),Z=new MhA({$metadata:XT(A),...D});return W5.decorateServiceException(Z,Q)},"de_RegionDisabledExceptionRes"),MdQ=B9((A,B)=>{let Q={};if(A[wa]!=null)Q[wa]=A[wa];if(A[$a]!=null)Q[$a]=A[$a];if(A[Ea]!=null){let D=khA(A[Ea],B);if(A[Ea]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[za]!=null)Q[za]=A[za];if(A[Ha]!=null)Q[Ha]=A[Ha];if(A[R20]!=null){let D=kdQ(A[R20],B);if(A[R20]?.length===0)Q.Tags=[];Object.entries(D).forEach(([Z,G])=>{let F=`Tags.${Z}`;Q[F]=G})}if(A[T20]!=null){let D=jdQ(A[T20],B);if(A[T20]?.length===0)Q.TransitiveTagKeys=[];Object.entries(D).forEach(([Z,G])=>{let F=`TransitiveTagKeys.${Z}`;Q[F]=G})}if(A[H20]!=null)Q[H20]=A[H20];if(A[L20]!=null)Q[L20]=A[L20];if(A[O20]!=null)Q[O20]=A[O20];if(A[WT]!=null)Q[WT]=A[WT];if(A[U20]!=null){let D=PdQ(A[U20],B);if(A[U20]?.length===0)Q.ProvidedContexts=[];Object.entries(D).forEach(([Z,G])=>{let F=`ProvidedContexts.${Z}`;Q[F]=G})}return Q},"se_AssumeRoleRequest"),RdQ=B9((A,B)=>{let Q={};if(A[wa]!=null)Q[wa]=A[wa];if(A[$a]!=null)Q[$a]=A[$a];if(A[S20]!=null)Q[S20]=A[S20];if(A[w20]!=null)Q[w20]=A[w20];if(A[Ea]!=null){let D=khA(A[Ea],B);if(A[Ea]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[za]!=null)Q[za]=A[za];if(A[Ha]!=null)Q[Ha]=A[Ha];return Q},"se_AssumeRoleWithWebIdentityRequest"),khA=B9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=OdQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_policyDescriptorListType"),OdQ=B9((A,B)=>{let Q={};if(A[j20]!=null)Q[j20]=A[j20];return Q},"se_PolicyDescriptorType"),TdQ=B9((A,B)=>{let Q={};if(A[E20]!=null)Q[E20]=A[E20];if(A[C20]!=null)Q[C20]=A[C20];return Q},"se_ProvidedContext"),PdQ=B9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=TdQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_ProvidedContextsListType"),SdQ=B9((A,B)=>{let Q={};if(A[z20]!=null)Q[z20]=A[z20];if(A[P20]!=null)Q[P20]=A[P20];return Q},"se_Tag"),jdQ=B9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;Q[`member.${D}`]=Z,D++}return Q},"se_tagKeyListType"),kdQ=B9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=SdQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_tagListType"),yhA=B9((A,B)=>{let Q={};if(A[J20]!=null)Q[J20]=W5.expectString(A[J20]);if(A[X20]!=null)Q[X20]=W5.expectString(A[X20]);return Q},"de_AssumedRoleUser"),ydQ=B9((A,B)=>{let Q={};if(A[Ka]!=null)Q[Ka]=_hA(A[Ka],B);if(A[Ca]!=null)Q[Ca]=yhA(A[Ca],B);if(A[Ua]!=null)Q[Ua]=W5.strictParseInt32(A[Ua]);if(A[WT]!=null)Q[WT]=W5.expectString(A[WT]);return Q},"de_AssumeRoleResponse"),_dQ=B9((A,B)=>{let Q={};if(A[Ka]!=null)Q[Ka]=_hA(A[Ka],B);if(A[N20]!=null)Q[N20]=W5.expectString(A[N20]);if(A[Ca]!=null)Q[Ca]=yhA(A[Ca],B);if(A[Ua]!=null)Q[Ua]=W5.strictParseInt32(A[Ua]);if(A[$20]!=null)Q[$20]=W5.expectString(A[$20]);if(A[V20]!=null)Q[V20]=W5.expectString(A[V20]);if(A[WT]!=null)Q[WT]=W5.expectString(A[WT]);return Q},"de_AssumeRoleWithWebIdentityResponse"),_hA=B9((A,B)=>{let Q={};if(A[W20]!=null)Q[W20]=W5.expectString(A[W20]);if(A[q20]!=null)Q[q20]=W5.expectString(A[q20]);if(A[M20]!=null)Q[M20]=W5.expectString(A[M20]);if(A[K20]!=null)Q[K20]=W5.expectNonNull(W5.parseRfc3339DateTimeWithOffset(A[K20]));return Q},"de_Credentials"),xdQ=B9((A,B)=>{let Q={};if(A[mZ]!=null)Q[mZ]=W5.expectString(A[mZ]);return Q},"de_ExpiredTokenException"),vdQ=B9((A,B)=>{let Q={};if(A[mZ]!=null)Q[mZ]=W5.expectString(A[mZ]);return Q},"de_IDPCommunicationErrorException"),bdQ=B9((A,B)=>{let Q={};if(A[mZ]!=null)Q[mZ]=W5.expectString(A[mZ]);return Q},"de_IDPRejectedClaimException"),fdQ=B9((A,B)=>{let Q={};if(A[mZ]!=null)Q[mZ]=W5.expectString(A[mZ]);return Q},"de_InvalidIdentityTokenException"),hdQ=B9((A,B)=>{let Q={};if(A[mZ]!=null)Q[mZ]=W5.expectString(A[mZ]);return Q},"de_MalformedPolicyDocumentException"),gdQ=B9((A,B)=>{let Q={};if(A[mZ]!=null)Q[mZ]=W5.expectString(A[mZ]);return Q},"de_PackedPolicyTooLargeException"),udQ=B9((A,B)=>{let Q={};if(A[mZ]!=null)Q[mZ]=W5.expectString(A[mZ]);return Q},"de_RegionDisabledException"),XT=B9((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),mdQ=W5.withBaseException(JT),xhA=B9(async(A,B,Q,D,Z)=>{let{hostname:G,protocol:F="https",port:I,path:Y}=await A.endpoint(),W={protocol:F,hostname:G,port:I,method:"POST",path:Y.endsWith("/")?Y.slice(0,-1)+Q:Y+Q,headers:B};if(D!==void 0)W.hostname=D;if(Z!==void 0)W.body=Z;return new VdQ.HttpRequest(W)},"buildHttpRpcRequest"),vhA={"content-type":"application/x-www-form-urlencoded"},bhA="2011-06-15",fhA="Action",W20="AccessKeyId",ddQ="AssumeRole",J20="AssumedRoleId",Ca="AssumedRoleUser",cdQ="AssumeRoleWithWebIdentity",X20="Arn",V20="Audience",Ka="Credentials",C20="ContextAssertion",Ha="DurationSeconds",K20="Expiration",H20="ExternalId",z20="Key",za="Policy",Ea="PolicyArns",E20="ProviderArn",U20="ProvidedContexts",w20="ProviderId",Ua="PackedPolicySize",$20="Provider",wa="RoleArn",$a="RoleSessionName",q20="SecretAccessKey",N20="SubjectFromWebIdentityToken",WT="SourceIdentity",L20="SerialNumber",M20="SessionToken",R20="Tags",O20="TokenCode",T20="TransitiveTagKeys",hhA="Version",P20="Value",S20="WebIdentityToken",j20="arn",mZ="message",ghA=B9((A)=>Object.entries(A).map(([B,Q])=>W5.extendedEncodeURIComponent(B)+"="+W5.extendedEncodeURIComponent(Q)).join("&"),"buildFormUrlencodedString"),ldQ=B9((A,B)=>{if(B.Error?.Code!==void 0)return B.Error.Code;if(A.statusCode==404)return"NotFound"},"loadQueryErrorCode"),b20=class extends WdQ.Command.classBuilder().ep(JdQ.commonParams).m(function(A,B,Q,D){return[YdQ.getSerdePlugin(Q,this.serialize,this.deserialize),IdQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRole",{}).n("STSClient","AssumeRoleCommand").f(void 0,$hA).ser(CdQ).de(HdQ).build(){static{B9(this,"AssumeRoleCommand")}},pdQ=q6(),idQ=T3(),ndQ=W6(),adQ=k41(),f20=class extends ndQ.Command.classBuilder().ep(adQ.commonParams).m(function(A,B,Q,D){return[idQ.getSerdePlugin(Q,this.serialize,this.deserialize),pdQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithWebIdentity",{}).n("STSClient","AssumeRoleWithWebIdentityCommand").f(ThA,PhA).ser(KdQ).de(zdQ).build(){static{B9(this,"AssumeRoleWithWebIdentityCommand")}},sdQ=j41(),rdQ={AssumeRoleCommand:b20,AssumeRoleWithWebIdentityCommand:f20},uhA=class extends sdQ.STSClient{static{B9(this,"STS")}};FdQ.createAggregatedClient(rdQ,uhA);var odQ=k41(),y20=Lw(),UhA="us-east-1",mhA=B9((A)=>{if(typeof A?.Arn==="string"){let B=A.Arn.split(":");if(B.length>4&&B[4]!=="")return B[4]}return},"getAccountIdFromAssumedRoleUser"),dhA=B9(async(A,B,Q)=>{let D=typeof A==="function"?await A():A,Z=typeof B==="function"?await B():B;return Q?.debug?.("@aws-sdk/client-sts::resolveRegion","accepting first of:",`${D} (provider)`,`${Z} (parent client)`,`${UhA} (STS default)`),D??Z??UhA},"resolveRegion"),tdQ=B9((A,B)=>{let Q,D;return async(Z,G)=>{if(D=Z,!Q){let{logger:J=A?.parentClientConfig?.logger,region:X,requestHandler:V=A?.parentClientConfig?.requestHandler,credentialProviderLogger:C}=A,K=await dhA(X,A?.parentClientConfig?.region,C),H=!chA(V);Q=new B({profile:A?.parentClientConfig?.profile,credentialDefaultProvider:B9(()=>async()=>D,"credentialDefaultProvider"),region:K,requestHandler:H?V:void 0,logger:J})}let{Credentials:F,AssumedRoleUser:I}=await Q.send(new b20(G));if(!F||!F.AccessKeyId||!F.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRole call with role ${G.RoleArn}`);let Y=mhA(I),W={accessKeyId:F.AccessKeyId,secretAccessKey:F.SecretAccessKey,sessionToken:F.SessionToken,expiration:F.Expiration,...F.CredentialScope&&{credentialScope:F.CredentialScope},...Y&&{accountId:Y}};return y20.setCredentialFeature(W,"CREDENTIALS_STS_ASSUME_ROLE","i"),W}},"getDefaultRoleAssumer"),edQ=B9((A,B)=>{let Q;return async(D)=>{if(!Q){let{logger:Y=A?.parentClientConfig?.logger,region:W,requestHandler:J=A?.parentClientConfig?.requestHandler,credentialProviderLogger:X}=A,V=await dhA(W,A?.parentClientConfig?.region,X),C=!chA(J);Q=new B({profile:A?.parentClientConfig?.profile,region:V,requestHandler:C?J:void 0,logger:Y})}let{Credentials:Z,AssumedRoleUser:G}=await Q.send(new f20(D));if(!Z||!Z.AccessKeyId||!Z.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${D.RoleArn}`);let F=mhA(G),I={accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.SessionToken,expiration:Z.Expiration,...Z.CredentialScope&&{credentialScope:Z.CredentialScope},...F&&{accountId:F}};if(F)y20.setCredentialFeature(I,"RESOLVED_ACCOUNT_ID","T");return y20.setCredentialFeature(I,"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID","k"),I}},"getDefaultRoleAssumerWithWebIdentity"),chA=B9((A)=>{return A?.metadata?.handlerProtocol==="h2"},"isH2"),lhA=j41(),phA=B9((A,B)=>{if(!B)return A;else return class Q extends A{static{B9(this,"CustomizableSTSClient")}constructor(D){super(D);for(let Z of B)this.middlewareStack.use(Z)}}},"getCustomizableStsClientCtor"),ihA=B9((A={},B)=>tdQ(A,phA(lhA.STSClient,B)),"getDefaultRoleAssumer"),nhA=B9((A={},B)=>edQ(A,phA(lhA.STSClient,B)),"getDefaultRoleAssumerWithWebIdentity"),AcQ=B9((A)=>(B)=>A({roleAssumer:ihA(B),roleAssumerWithWebIdentity:nhA(B),...B}),"decorateDefaultCredentialProvider")});
var gnA=E((fnA)=>{Object.defineProperty(fnA,"__esModule",{value:!0});fnA.defaultEndpointResolver=void 0;var a14=h41(),a90=$7(),s14=bnA(),r14=new a90.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),o14=(A,B={})=>{return r14.get(A,()=>a90.resolveEndpoint(s14.ruleSet,{endpointParams:A,logger:B.logger}))};fnA.defaultEndpointResolver=o14;a90.customEndpointFunctions.aws=a14.awsEndpointFunctions});
var h41=E((NY5,xdA)=>{var{defineProperty:Z$1,getOwnPropertyDescriptor:yaQ,getOwnPropertyNames:_aQ}=Object,xaQ=Object.prototype.hasOwnProperty,Ma=(A,B)=>Z$1(A,"name",{value:B,configurable:!0}),vaQ=(A,B)=>{for(var Q in B)Z$1(A,Q,{get:B[Q],enumerable:!0})},baQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of _aQ(B))if(!xaQ.call(A,Z)&&Z!==Q)Z$1(A,Z,{get:()=>B[Z],enumerable:!(D=yaQ(B,Z))||D.enumerable})}return A},faQ=(A)=>baQ(Z$1({},"__esModule",{value:!0}),A),OdA={};vaQ(OdA,{ConditionObject:()=>e3.ConditionObject,DeprecatedObject:()=>e3.DeprecatedObject,EndpointError:()=>e3.EndpointError,EndpointObject:()=>e3.EndpointObject,EndpointObjectHeaders:()=>e3.EndpointObjectHeaders,EndpointObjectProperties:()=>e3.EndpointObjectProperties,EndpointParams:()=>e3.EndpointParams,EndpointResolverOptions:()=>e3.EndpointResolverOptions,EndpointRuleObject:()=>e3.EndpointRuleObject,ErrorRuleObject:()=>e3.ErrorRuleObject,EvaluateOptions:()=>e3.EvaluateOptions,Expression:()=>e3.Expression,FunctionArgv:()=>e3.FunctionArgv,FunctionObject:()=>e3.FunctionObject,FunctionReturn:()=>e3.FunctionReturn,ParameterObject:()=>e3.ParameterObject,ReferenceObject:()=>e3.ReferenceObject,ReferenceRecord:()=>e3.ReferenceRecord,RuleSetObject:()=>e3.RuleSetObject,RuleSetRules:()=>e3.RuleSetRules,TreeRuleObject:()=>e3.TreeRuleObject,awsEndpointFunctions:()=>_dA,getUserAgentPrefix:()=>maQ,isIpAddress:()=>e3.isIpAddress,partition:()=>kdA,resolveEndpoint:()=>e3.resolveEndpoint,setPartitionInfo:()=>ydA,useDefaultPartitionInfo:()=>uaQ});xdA.exports=faQ(OdA);var e3=$7(),TdA=Ma((A,B=!1)=>{if(B){for(let Q of A.split("."))if(!TdA(Q))return!1;return!0}if(!e3.isValidHostLabel(A))return!1;if(A.length<3||A.length>63)return!1;if(A!==A.toLowerCase())return!1;if(e3.isIpAddress(A))return!1;return!0},"isVirtualHostableS3Bucket"),RdA=":",haQ="/",gaQ=Ma((A)=>{let B=A.split(RdA);if(B.length<6)return null;let[Q,D,Z,G,F,...I]=B;if(Q!=="arn"||D===""||Z===""||I.join(RdA)==="")return null;let Y=I.map((W)=>W.split(haQ)).flat();return{partition:D,service:Z,region:G,accountId:F,resourceId:Y}},"parseArn"),PdA={partitions:[{id:"aws",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-east-1",name:"aws",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^(us|eu|ap|sa|ca|me|af|il|mx)\\-\\w+\\-\\d+$",regions:{"af-south-1":{description:"Africa (Cape Town)"},"ap-east-1":{description:"Asia Pacific (Hong Kong)"},"ap-east-2":{description:"Asia Pacific (Taipei)"},"ap-northeast-1":{description:"Asia Pacific (Tokyo)"},"ap-northeast-2":{description:"Asia Pacific (Seoul)"},"ap-northeast-3":{description:"Asia Pacific (Osaka)"},"ap-south-1":{description:"Asia Pacific (Mumbai)"},"ap-south-2":{description:"Asia Pacific (Hyderabad)"},"ap-southeast-1":{description:"Asia Pacific (Singapore)"},"ap-southeast-2":{description:"Asia Pacific (Sydney)"},"ap-southeast-3":{description:"Asia Pacific (Jakarta)"},"ap-southeast-4":{description:"Asia Pacific (Melbourne)"},"ap-southeast-5":{description:"Asia Pacific (Malaysia)"},"ap-southeast-7":{description:"Asia Pacific (Thailand)"},"aws-global":{description:"AWS Standard global region"},"ca-central-1":{description:"Canada (Central)"},"ca-west-1":{description:"Canada West (Calgary)"},"eu-central-1":{description:"Europe (Frankfurt)"},"eu-central-2":{description:"Europe (Zurich)"},"eu-north-1":{description:"Europe (Stockholm)"},"eu-south-1":{description:"Europe (Milan)"},"eu-south-2":{description:"Europe (Spain)"},"eu-west-1":{description:"Europe (Ireland)"},"eu-west-2":{description:"Europe (London)"},"eu-west-3":{description:"Europe (Paris)"},"il-central-1":{description:"Israel (Tel Aviv)"},"me-central-1":{description:"Middle East (UAE)"},"me-south-1":{description:"Middle East (Bahrain)"},"mx-central-1":{description:"Mexico (Central)"},"sa-east-1":{description:"South America (Sao Paulo)"},"us-east-1":{description:"US East (N. Virginia)"},"us-east-2":{description:"US East (Ohio)"},"us-west-1":{description:"US West (N. California)"},"us-west-2":{description:"US West (Oregon)"}}},{id:"aws-cn",outputs:{dnsSuffix:"amazonaws.com.cn",dualStackDnsSuffix:"api.amazonwebservices.com.cn",implicitGlobalRegion:"cn-northwest-1",name:"aws-cn",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^cn\\-\\w+\\-\\d+$",regions:{"aws-cn-global":{description:"AWS China global region"},"cn-north-1":{description:"China (Beijing)"},"cn-northwest-1":{description:"China (Ningxia)"}}},{id:"aws-us-gov",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-gov-west-1",name:"aws-us-gov",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^us\\-gov\\-\\w+\\-\\d+$",regions:{"aws-us-gov-global":{description:"AWS GovCloud (US) global region"},"us-gov-east-1":{description:"AWS GovCloud (US-East)"},"us-gov-west-1":{description:"AWS GovCloud (US-West)"}}},{id:"aws-iso",outputs:{dnsSuffix:"c2s.ic.gov",dualStackDnsSuffix:"c2s.ic.gov",implicitGlobalRegion:"us-iso-east-1",name:"aws-iso",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-iso\\-\\w+\\-\\d+$",regions:{"aws-iso-global":{description:"AWS ISO (US) global region"},"us-iso-east-1":{description:"US ISO East"},"us-iso-west-1":{description:"US ISO WEST"}}},{id:"aws-iso-b",outputs:{dnsSuffix:"sc2s.sgov.gov",dualStackDnsSuffix:"sc2s.sgov.gov",implicitGlobalRegion:"us-isob-east-1",name:"aws-iso-b",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isob\\-\\w+\\-\\d+$",regions:{"aws-iso-b-global":{description:"AWS ISOB (US) global region"},"us-isob-east-1":{description:"US ISOB East (Ohio)"}}},{id:"aws-iso-e",outputs:{dnsSuffix:"cloud.adc-e.uk",dualStackDnsSuffix:"cloud.adc-e.uk",implicitGlobalRegion:"eu-isoe-west-1",name:"aws-iso-e",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eu\\-isoe\\-\\w+\\-\\d+$",regions:{"aws-iso-e-global":{description:"AWS ISOE (Europe) global region"},"eu-isoe-west-1":{description:"EU ISOE West"}}},{id:"aws-iso-f",outputs:{dnsSuffix:"csp.hci.ic.gov",dualStackDnsSuffix:"csp.hci.ic.gov",implicitGlobalRegion:"us-isof-south-1",name:"aws-iso-f",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isof\\-\\w+\\-\\d+$",regions:{"aws-iso-f-global":{description:"AWS ISOF global region"},"us-isof-east-1":{description:"US ISOF EAST"},"us-isof-south-1":{description:"US ISOF SOUTH"}}},{id:"aws-eusc",outputs:{dnsSuffix:"amazonaws.eu",dualStackDnsSuffix:"amazonaws.eu",implicitGlobalRegion:"eusc-de-east-1",name:"aws-eusc",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eusc\\-(de)\\-\\w+\\-\\d+$",regions:{"eusc-de-east-1":{description:"EU (Germany)"}}}],version:"1.1"},SdA=PdA,jdA="",kdA=Ma((A)=>{let{partitions:B}=SdA;for(let D of B){let{regions:Z,outputs:G}=D;for(let[F,I]of Object.entries(Z))if(F===A)return{...G,...I}}for(let D of B){let{regionRegex:Z,outputs:G}=D;if(new RegExp(Z).test(A))return{...G}}let Q=B.find((D)=>D.id==="aws");if(!Q)throw new Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");return{...Q.outputs}},"partition"),ydA=Ma((A,B="")=>{SdA=A,jdA=B},"setPartitionInfo"),uaQ=Ma(()=>{ydA(PdA,"")},"useDefaultPartitionInfo"),maQ=Ma(()=>jdA,"getUserAgentPrefix"),_dA={isVirtualHostableS3Bucket:TdA,parseArn:gaQ,partition:kdA};e3.customEndpointFunctions.aws=_dA});
var h90=E((WiA)=>{Object.defineProperty(WiA,"__esModule",{value:!0});WiA.resolveHttpAuthSchemeConfig=WiA.defaultSSOHttpAuthSchemeProvider=WiA.defaultSSOHttpAuthSchemeParametersProvider=void 0;var jeQ=XV(),f90=I5(),keQ=async(A,B,Q)=>{return{operation:f90.getSmithyContext(B).operation,region:await f90.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};WiA.defaultSSOHttpAuthSchemeParametersProvider=keQ;function yeQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"awsssoportal",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function j$1(A){return{schemeId:"smithy.api#noAuth"}}var _eQ=(A)=>{let B=[];switch(A.operation){case"GetRoleCredentials":{B.push(j$1(A));break}case"ListAccountRoles":{B.push(j$1(A));break}case"ListAccounts":{B.push(j$1(A));break}case"Logout":{B.push(j$1(A));break}default:B.push(yeQ(A))}return B};WiA.defaultSSOHttpAuthSchemeProvider=_eQ;var xeQ=(A)=>{let B=jeQ.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:f90.normalizeProvider(A.authSchemePreference??[])})};WiA.resolveHttpAuthSchemeConfig=xeQ});
var hgA=E((bgA)=>{Object.defineProperty(bgA,"__esModule",{value:!0});bgA.defaultEndpointResolver=void 0;var ZlQ=on(),t20=$7(),GlQ=vgA(),FlQ=new t20.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),IlQ=(A,B={})=>{return FlQ.get(A,()=>t20.resolveEndpoint(GlQ.ruleSet,{endpointParams:A,logger:B.logger}))};bgA.defaultEndpointResolver=IlQ;t20.customEndpointFunctions.aws=ZlQ.awsEndpointFunctions});
var i20=E((HI5,_w1)=>{var{defineProperty:BgA,getOwnPropertyDescriptor:McQ,getOwnPropertyNames:RcQ}=Object,OcQ=Object.prototype.hasOwnProperty,l20=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of RcQ(B))if(!OcQ.call(A,Z)&&Z!==Q)BgA(A,Z,{get:()=>B[Z],enumerable:!(D=McQ(B,Z))||D.enumerable})}return A},QgA=(A,B,Q)=>(l20(A,B,"default"),Q&&l20(Q,B,"default")),TcQ=(A)=>l20(BgA({},"__esModule",{value:!0}),A),p20={};_w1.exports=TcQ(p20);QgA(p20,AgA(),_w1.exports);QgA(p20,c20(),_w1.exports)});
var i41=E((KlA)=>{Object.defineProperty(KlA,"__esModule",{value:!0});KlA.commonParams=KlA.resolveClientEndpointParameters=void 0;var XoQ=(A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,useGlobalEndpoint:A.useGlobalEndpoint??!1,defaultSigningName:"sts"})};KlA.resolveClientEndpointParameters=XoQ;KlA.commonParams={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}});
var ih=E((yG5,iU1)=>{var _kA,xkA,vkA,bkA,fkA,hkA,gkA,ukA,mkA,dkA,ckA,lkA,pkA,lU1,p00,ikA,nkA,akA,tn,skA,rkA,okA,tkA,ekA,AyA,ByA,QyA,DyA,pU1,ZyA,GyA,FyA;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof iU1==="object"&&typeof yG5==="object")A(Q(B,Q(yG5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};_kA=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},xkA=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},vkA=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},bkA=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},fkA=function(G,F){return function(I,Y){F(I,Y,G)}},hkA=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},gkA=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},ukA=function(G){return typeof G==="symbol"?G:"".concat(G)},mkA=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},dkA=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},ckA=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},lkA=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},pkA=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))pU1(F,G,I)},pU1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},lU1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},p00=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},ikA=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(p00(arguments[F]));return G},nkA=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},akA=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},tn=function(G){return this instanceof tn?(this.v=G,this):new tn(G)},skA=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof tn?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},rkA=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:tn(G[W](X)),done:!1}:J?J(X):X}:J}},okA=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof lU1==="function"?lU1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},tkA=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};ekA=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")pU1(F,G,I[Y])}return Q(F,G),F},AyA=function(G){return G&&G.__esModule?G:{default:G}},ByA=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},QyA=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},DyA=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},ZyA=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};GyA=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},FyA=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",_kA),A("__assign",xkA),A("__rest",vkA),A("__decorate",bkA),A("__param",fkA),A("__esDecorate",hkA),A("__runInitializers",gkA),A("__propKey",ukA),A("__setFunctionName",mkA),A("__metadata",dkA),A("__awaiter",ckA),A("__generator",lkA),A("__exportStar",pkA),A("__createBinding",pU1),A("__values",lU1),A("__read",p00),A("__spread",ikA),A("__spreadArrays",nkA),A("__spreadArray",akA),A("__await",tn),A("__asyncGenerator",skA),A("__asyncDelegator",rkA),A("__asyncValues",okA),A("__makeTemplateObject",tkA),A("__importStar",ekA),A("__importDefault",AyA),A("__classPrivateFieldGet",ByA),A("__classPrivateFieldSet",QyA),A("__classPrivateFieldIn",DyA),A("__addDisposableResource",ZyA),A("__disposeResources",GyA),A("__rewriteRelativeImportExtension",FyA)})});
var ilA=E((llA)=>{Object.defineProperty(llA,"__esModule",{value:!0});llA.ruleSet=void 0;var xlA="required",z4="type",g8="fn",u8="argv",by="ref",LlA=!1,tB0=!0,vy="booleanEquals",jY="stringEquals",vlA="sigv4",blA="sts",flA="us-east-1",WD="endpoint",MlA="https://sts.{Region}.{PartitionResult#dnsSuffix}",DL="tree",Sa="error",A90="getAttr",RlA={[xlA]:!1,[z4]:"String"},eB0={[xlA]:!0,default:!1,[z4]:"Boolean"},hlA={[by]:"Endpoint"},OlA={[g8]:"isSet",[u8]:[{[by]:"Region"}]},kY={[by]:"Region"},TlA={[g8]:"aws.partition",[u8]:[kY],assign:"PartitionResult"},glA={[by]:"UseFIPS"},ulA={[by]:"UseDualStack"},gW={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:vlA,signingName:blA,signingRegion:flA}]},headers:{}},ZK={},PlA={conditions:[{[g8]:jY,[u8]:[kY,"aws-global"]}],[WD]:gW,[z4]:WD},mlA={[g8]:vy,[u8]:[glA,!0]},dlA={[g8]:vy,[u8]:[ulA,!0]},SlA={[g8]:A90,[u8]:[{[by]:"PartitionResult"},"supportsFIPS"]},clA={[by]:"PartitionResult"},jlA={[g8]:vy,[u8]:[!0,{[g8]:A90,[u8]:[clA,"supportsDualStack"]}]},klA=[{[g8]:"isSet",[u8]:[hlA]}],ylA=[mlA],_lA=[dlA],RoQ={version:"1.0",parameters:{Region:RlA,UseDualStack:eB0,UseFIPS:eB0,Endpoint:RlA,UseGlobalEndpoint:eB0},rules:[{conditions:[{[g8]:vy,[u8]:[{[by]:"UseGlobalEndpoint"},tB0]},{[g8]:"not",[u8]:klA},OlA,TlA,{[g8]:vy,[u8]:[glA,LlA]},{[g8]:vy,[u8]:[ulA,LlA]}],rules:[{conditions:[{[g8]:jY,[u8]:[kY,"ap-northeast-1"]}],endpoint:gW,[z4]:WD},{conditions:[{[g8]:jY,[u8]:[kY,"ap-south-1"]}],endpoint:gW,[z4]:WD},{conditions:[{[g8]:jY,[u8]:[kY,"ap-southeast-1"]}],endpoint:gW,[z4]:WD},{conditions:[{[g8]:jY,[u8]:[kY,"ap-southeast-2"]}],endpoint:gW,[z4]:WD},PlA,{conditions:[{[g8]:jY,[u8]:[kY,"ca-central-1"]}],endpoint:gW,[z4]:WD},{conditions:[{[g8]:jY,[u8]:[kY,"eu-central-1"]}],endpoint:gW,[z4]:WD},{conditions:[{[g8]:jY,[u8]:[kY,"eu-north-1"]}],endpoint:gW,[z4]:WD},{conditions:[{[g8]:jY,[u8]:[kY,"eu-west-1"]}],endpoint:gW,[z4]:WD},{conditions:[{[g8]:jY,[u8]:[kY,"eu-west-2"]}],endpoint:gW,[z4]:WD},{conditions:[{[g8]:jY,[u8]:[kY,"eu-west-3"]}],endpoint:gW,[z4]:WD},{conditions:[{[g8]:jY,[u8]:[kY,"sa-east-1"]}],endpoint:gW,[z4]:WD},{conditions:[{[g8]:jY,[u8]:[kY,flA]}],endpoint:gW,[z4]:WD},{conditions:[{[g8]:jY,[u8]:[kY,"us-east-2"]}],endpoint:gW,[z4]:WD},{conditions:[{[g8]:jY,[u8]:[kY,"us-west-1"]}],endpoint:gW,[z4]:WD},{conditions:[{[g8]:jY,[u8]:[kY,"us-west-2"]}],endpoint:gW,[z4]:WD},{endpoint:{url:MlA,properties:{authSchemes:[{name:vlA,signingName:blA,signingRegion:"{Region}"}]},headers:ZK},[z4]:WD}],[z4]:DL},{conditions:klA,rules:[{conditions:ylA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[z4]:Sa},{conditions:_lA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[z4]:Sa},{endpoint:{url:hlA,properties:ZK,headers:ZK},[z4]:WD}],[z4]:DL},{conditions:[OlA],rules:[{conditions:[TlA],rules:[{conditions:[mlA,dlA],rules:[{conditions:[{[g8]:vy,[u8]:[tB0,SlA]},jlA],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:ZK,headers:ZK},[z4]:WD}],[z4]:DL},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[z4]:Sa}],[z4]:DL},{conditions:ylA,rules:[{conditions:[{[g8]:vy,[u8]:[SlA,tB0]}],rules:[{conditions:[{[g8]:jY,[u8]:[{[g8]:A90,[u8]:[clA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:ZK,headers:ZK},[z4]:WD},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:ZK,headers:ZK},[z4]:WD}],[z4]:DL},{error:"FIPS is enabled but this partition does not support FIPS",[z4]:Sa}],[z4]:DL},{conditions:_lA,rules:[{conditions:[jlA],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:ZK,headers:ZK},[z4]:WD}],[z4]:DL},{error:"DualStack is enabled but this partition does not support DualStack",[z4]:Sa}],[z4]:DL},PlA,{endpoint:{url:MlA,properties:ZK,headers:ZK},[z4]:WD}],[z4]:DL}],[z4]:DL},{error:"Invalid Configuration: Missing Region",[z4]:Sa}]};llA.ruleSet=RoQ});
var imA=E((pmA)=>{Object.defineProperty(pmA,"__esModule",{value:!0});pmA.createGetRequest=jnQ;pmA.getCredentials=knQ;var zB0=eB(),TnQ=DK(),PnQ=H8(),SnQ=Fy();function jnQ(A){return new TnQ.HttpRequest({protocol:A.protocol,hostname:A.hostname,port:Number(A.port),path:A.pathname,query:Array.from(A.searchParams.entries()).reduce((B,[Q,D])=>{return B[Q]=D,B},{}),fragment:A.hash})}async function knQ(A,B){let D=await SnQ.sdkStreamMixin(A.body).transformToString();if(A.statusCode===200){let Z=JSON.parse(D);if(typeof Z.AccessKeyId!=="string"||typeof Z.SecretAccessKey!=="string"||typeof Z.Token!=="string"||typeof Z.Expiration!=="string")throw new zB0.CredentialsProviderError("HTTP credential provider response not of the required format, an object matching: { AccessKeyId: string, SecretAccessKey: string, Token: string, Expiration: string(rfc3339) }",{logger:B});return{accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.Token,expiration:PnQ.parseRfc3339DateTime(Z.Expiration)}}if(A.statusCode>=400&&A.statusCode<500){let Z={};try{Z=JSON.parse(D)}catch(G){}throw Object.assign(new zB0.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B}),{Code:Z.Code,Message:Z.Message})}throw new zB0.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B})}});
var ixA=E((lxA)=>{Object.defineProperty(lxA,"__esModule",{value:!0});lxA.ruleSet=void 0;var uxA="required",Uz="fn",wz="argv",Fa="ref",jxA=!0,kxA="isSet",N41="booleanEquals",Za="error",Ga="endpoint",GT="tree",fA0="PartitionResult",hA0="getAttr",yxA={[uxA]:!1,type:"String"},_xA={[uxA]:!0,default:!1,type:"Boolean"},xxA={[Fa]:"Endpoint"},mxA={[Uz]:N41,[wz]:[{[Fa]:"UseFIPS"},!0]},dxA={[Uz]:N41,[wz]:[{[Fa]:"UseDualStack"},!0]},Ez={},vxA={[Uz]:hA0,[wz]:[{[Fa]:fA0},"supportsFIPS"]},cxA={[Fa]:fA0},bxA={[Uz]:N41,[wz]:[!0,{[Uz]:hA0,[wz]:[cxA,"supportsDualStack"]}]},fxA=[mxA],hxA=[dxA],gxA=[{[Fa]:"Region"}],YhQ={version:"1.0",parameters:{Region:yxA,UseDualStack:_xA,UseFIPS:_xA,Endpoint:yxA},rules:[{conditions:[{[Uz]:kxA,[wz]:[xxA]}],rules:[{conditions:fxA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:Za},{conditions:hxA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:Za},{endpoint:{url:xxA,properties:Ez,headers:Ez},type:Ga}],type:GT},{conditions:[{[Uz]:kxA,[wz]:gxA}],rules:[{conditions:[{[Uz]:"aws.partition",[wz]:gxA,assign:fA0}],rules:[{conditions:[mxA,dxA],rules:[{conditions:[{[Uz]:N41,[wz]:[jxA,vxA]},bxA],rules:[{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Ez,headers:Ez},type:Ga}],type:GT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:Za}],type:GT},{conditions:fxA,rules:[{conditions:[{[Uz]:N41,[wz]:[vxA,jxA]}],rules:[{conditions:[{[Uz]:"stringEquals",[wz]:[{[Uz]:hA0,[wz]:[cxA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://portal.sso.{Region}.amazonaws.com",properties:Ez,headers:Ez},type:Ga},{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dnsSuffix}",properties:Ez,headers:Ez},type:Ga}],type:GT},{error:"FIPS is enabled but this partition does not support FIPS",type:Za}],type:GT},{conditions:hxA,rules:[{conditions:[bxA],rules:[{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Ez,headers:Ez},type:Ga}],type:GT},{error:"DualStack is enabled but this partition does not support DualStack",type:Za}],type:GT},{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dnsSuffix}",properties:Ez,headers:Ez},type:Ga}],type:GT}],type:GT},{error:"Invalid Configuration: Missing Region",type:Za}]};lxA.ruleSet=YhQ});
var iyA=E((bG5,pyA)=>{var{defineProperty:Dw1,getOwnPropertyDescriptor:MxQ,getOwnPropertyNames:RxQ}=Object,OxQ=Object.prototype.hasOwnProperty,OY=(A,B)=>Dw1(A,"name",{value:B,configurable:!0}),TxQ=(A,B)=>{for(var Q in B)Dw1(A,Q,{get:B[Q],enumerable:!0})},PxQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of RxQ(B))if(!OxQ.call(A,Z)&&Z!==Q)Dw1(A,Z,{get:()=>B[Z],enumerable:!(D=MxQ(B,Z))||D.enumerable})}return A},SxQ=(A)=>PxQ(Dw1({},"__esModule",{value:!0}),A),LyA={};TxQ(LyA,{ALGORITHM_IDENTIFIER:()=>oU1,ALGORITHM_IDENTIFIER_V4A:()=>_xQ,ALGORITHM_QUERY_PARAM:()=>MyA,ALWAYS_UNSIGNABLE_HEADERS:()=>yyA,AMZ_DATE_HEADER:()=>BA0,AMZ_DATE_QUERY_PARAM:()=>o00,AUTH_HEADER:()=>AA0,CREDENTIAL_QUERY_PARAM:()=>RyA,DATE_HEADER:()=>PyA,EVENT_ALGORITHM_IDENTIFIER:()=>vyA,EXPIRES_QUERY_PARAM:()=>TyA,GENERATED_HEADERS:()=>SyA,HOST_HEADER:()=>kxQ,KEY_TYPE_IDENTIFIER:()=>QA0,MAX_CACHE_SIZE:()=>fyA,MAX_PRESIGNED_TTL:()=>hyA,PROXY_HEADER_PATTERN:()=>_yA,REGION_SET_PARAM:()=>jxQ,SEC_HEADER_PATTERN:()=>xyA,SHA256_HEADER:()=>Qw1,SIGNATURE_HEADER:()=>jyA,SIGNATURE_QUERY_PARAM:()=>t00,SIGNED_HEADERS_QUERY_PARAM:()=>OyA,SignatureV4:()=>lxQ,SignatureV4Base:()=>lyA,TOKEN_HEADER:()=>kyA,TOKEN_QUERY_PARAM:()=>e00,UNSIGNABLE_PATTERNS:()=>yxQ,UNSIGNED_PAYLOAD:()=>byA,clearCredentialCache:()=>vxQ,createScope:()=>eU1,getCanonicalHeaders:()=>a00,getCanonicalQuery:()=>cyA,getPayloadHash:()=>Aw1,getSigningKey:()=>gyA,hasHeader:()=>uyA,moveHeadersToQuery:()=>dyA,prepareRequest:()=>r00,signatureV4aContainer:()=>pxQ});pyA.exports=SxQ(LyA);var wyA=cB(),MyA="X-Amz-Algorithm",RyA="X-Amz-Credential",o00="X-Amz-Date",OyA="X-Amz-SignedHeaders",TyA="X-Amz-Expires",t00="X-Amz-Signature",e00="X-Amz-Security-Token",jxQ="X-Amz-Region-Set",AA0="authorization",BA0=o00.toLowerCase(),PyA="date",SyA=[AA0,BA0,PyA],jyA=t00.toLowerCase(),Qw1="x-amz-content-sha256",kyA=e00.toLowerCase(),kxQ="host",yyA={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},_yA=/^proxy-/,xyA=/^sec-/,yxQ=[/^proxy-/i,/^sec-/i],oU1="AWS4-HMAC-SHA256",_xQ="AWS4-ECDSA-P256-SHA256",vyA="AWS4-HMAC-SHA256-PAYLOAD",byA="UNSIGNED-PAYLOAD",fyA=50,QA0="aws4_request",hyA=604800,My=Zy(),xxQ=cB(),en={},tU1=[],eU1=OY((A,B,Q)=>`${A}/${B}/${Q}/${QA0}`,"createScope"),gyA=OY(async(A,B,Q,D,Z)=>{let G=await $yA(A,B.secretAccessKey,B.accessKeyId),F=`${Q}:${D}:${Z}:${My.toHex(G)}:${B.sessionToken}`;if(F in en)return en[F];tU1.push(F);while(tU1.length>fyA)delete en[tU1.shift()];let I=`AWS4${B.secretAccessKey}`;for(let Y of[Q,D,Z,QA0])I=await $yA(A,I,Y);return en[F]=I},"getSigningKey"),vxQ=OY(()=>{tU1.length=0,Object.keys(en).forEach((A)=>{delete en[A]})},"clearCredentialCache"),$yA=OY((A,B,Q)=>{let D=new A(B);return D.update(xxQ.toUint8Array(Q)),D.digest()},"hmac"),a00=OY(({headers:A},B,Q)=>{let D={};for(let Z of Object.keys(A).sort()){if(A[Z]==null)continue;let G=Z.toLowerCase();if(G in yyA||B?.has(G)||_yA.test(G)||xyA.test(G)){if(!Q||Q&&!Q.has(G))continue}D[G]=A[Z].trim().replace(/\s+/g," ")}return D},"getCanonicalHeaders"),bxQ=KyA(),fxQ=cB(),Aw1=OY(async({headers:A,body:B},Q)=>{for(let D of Object.keys(A))if(D.toLowerCase()===Qw1)return A[D];if(B==null)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";else if(typeof B==="string"||ArrayBuffer.isView(B)||bxQ.isArrayBuffer(B)){let D=new Q;return D.update(fxQ.toUint8Array(B)),My.toHex(await D.digest())}return byA},"getPayloadHash"),qyA=cB(),hxQ=class{static{OY(this,"HeaderFormatter")}format(A){let B=[];for(let Z of Object.keys(A)){let G=qyA.fromUtf8(Z);B.push(Uint8Array.from([G.byteLength]),G,this.formatHeaderValue(A[Z]))}let Q=new Uint8Array(B.reduce((Z,G)=>Z+G.byteLength,0)),D=0;for(let Z of B)Q.set(Z,D),D+=Z.byteLength;return Q}formatHeaderValue(A){switch(A.type){case"boolean":return Uint8Array.from([A.value?0:1]);case"byte":return Uint8Array.from([2,A.value]);case"short":let B=new DataView(new ArrayBuffer(3));return B.setUint8(0,3),B.setInt16(1,A.value,!1),new Uint8Array(B.buffer);case"integer":let Q=new DataView(new ArrayBuffer(5));return Q.setUint8(0,4),Q.setInt32(1,A.value,!1),new Uint8Array(Q.buffer);case"long":let D=new Uint8Array(9);return D[0]=5,D.set(A.value.bytes,1),D;case"binary":let Z=new DataView(new ArrayBuffer(3+A.value.byteLength));Z.setUint8(0,6),Z.setUint16(1,A.value.byteLength,!1);let G=new Uint8Array(Z.buffer);return G.set(A.value,3),G;case"string":let F=qyA.fromUtf8(A.value),I=new DataView(new ArrayBuffer(3+F.byteLength));I.setUint8(0,7),I.setUint16(1,F.byteLength,!1);let Y=new Uint8Array(I.buffer);return Y.set(F,3),Y;case"timestamp":let W=new Uint8Array(9);return W[0]=8,W.set(uxQ.fromNumber(A.value.valueOf()).bytes,1),W;case"uuid":if(!gxQ.test(A.value))throw new Error(`Invalid UUID received: ${A.value}`);let J=new Uint8Array(17);return J[0]=9,J.set(My.fromHex(A.value.replace(/\-/g,"")),1),J}}},gxQ=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,uxQ=class A{constructor(B){if(this.bytes=B,B.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static{OY(this,"Int64")}static fromNumber(B){if(B>9223372036854776000||B<-9223372036854776000)throw new Error(`${B} is too large (or, if negative, too small) to represent as an Int64`);let Q=new Uint8Array(8);for(let D=7,Z=Math.abs(Math.round(B));D>-1&&Z>0;D--,Z/=256)Q[D]=Z;if(B<0)s00(Q);return new A(Q)}valueOf(){let B=this.bytes.slice(0),Q=B[0]&128;if(Q)s00(B);return parseInt(My.toHex(B),16)*(Q?-1:1)}toString(){return String(this.valueOf())}};function s00(A){for(let B=0;B<8;B++)A[B]^=255;for(let B=7;B>-1;B--)if(A[B]++,A[B]!==0)break}OY(s00,"negate");var uyA=OY((A,B)=>{A=A.toLowerCase();for(let Q of Object.keys(B))if(A===Q.toLowerCase())return!0;return!1},"hasHeader"),myA=JV(),dyA=OY((A,B={})=>{let{headers:Q,query:D={}}=myA.HttpRequest.clone(A);for(let Z of Object.keys(Q)){let G=Z.toLowerCase();if(G.slice(0,6)==="x-amz-"&&!B.unhoistableHeaders?.has(G)||B.hoistableHeaders?.has(G))D[Z]=Q[Z],delete Q[Z]}return{...A,headers:Q,query:D}},"moveHeadersToQuery"),r00=OY((A)=>{A=myA.HttpRequest.clone(A);for(let B of Object.keys(A.headers))if(SyA.indexOf(B.toLowerCase())>-1)delete A.headers[B];return A},"prepareRequest"),NyA=I5(),mxQ=cB(),Bw1=UyA(),cyA=OY(({query:A={}})=>{let B=[],Q={};for(let D of Object.keys(A)){if(D.toLowerCase()===jyA)continue;let Z=Bw1.escapeUri(D);B.push(Z);let G=A[D];if(typeof G==="string")Q[Z]=`${Z}=${Bw1.escapeUri(G)}`;else if(Array.isArray(G))Q[Z]=G.slice(0).reduce((F,I)=>F.concat([`${Z}=${Bw1.escapeUri(I)}`]),[]).sort().join("&")}return B.sort().map((D)=>Q[D]).filter((D)=>D).join("&")},"getCanonicalQuery"),dxQ=OY((A)=>cxQ(A).toISOString().replace(/\.\d{3}Z$/,"Z"),"iso8601"),cxQ=OY((A)=>{if(typeof A==="number")return new Date(A*1000);if(typeof A==="string"){if(Number(A))return new Date(Number(A)*1000);return new Date(A)}return A},"toDate"),lyA=class{static{OY(this,"SignatureV4Base")}constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){this.service=D,this.sha256=Z,this.uriEscapePath=G,this.applyChecksum=typeof A==="boolean"?A:!0,this.regionProvider=NyA.normalizeProvider(Q),this.credentialProvider=NyA.normalizeProvider(B)}createCanonicalRequest(A,B,Q){let D=Object.keys(B).sort();return`${A.method}
${this.getCanonicalPath(A)}
${cyA(A)}
${D.map((Z)=>`${Z}:${B[Z]}`).join(`
`)}

${D.join(";")}
${Q}`}async createStringToSign(A,B,Q,D){let Z=new this.sha256;Z.update(mxQ.toUint8Array(Q));let G=await Z.digest();return`${D}
${A}
${B}
${My.toHex(G)}`}getCanonicalPath({path:A}){if(this.uriEscapePath){let B=[];for(let Z of A.split("/")){if(Z?.length===0)continue;if(Z===".")continue;if(Z==="..")B.pop();else B.push(Z)}let Q=`${A?.startsWith("/")?"/":""}${B.join("/")}${B.length>0&&A?.endsWith("/")?"/":""}`;return Bw1.escapeUri(Q).replace(/%2F/g,"/")}return A}validateResolvedCredentials(A){if(typeof A!=="object"||typeof A.accessKeyId!=="string"||typeof A.secretAccessKey!=="string")throw new Error("Resolved credential object is not valid")}formatDate(A){let B=dxQ(A).replace(/[\-:]/g,"");return{longDate:B,shortDate:B.slice(0,8)}}getCanonicalHeaderList(A){return Object.keys(A).sort().join(";")}},lxQ=class extends lyA{constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){super({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G});this.headerFormatter=new hxQ}static{OY(this,"SignatureV4")}async presign(A,B={}){let{signingDate:Q=new Date,expiresIn:D=3600,unsignableHeaders:Z,unhoistableHeaders:G,signableHeaders:F,hoistableHeaders:I,signingRegion:Y,signingService:W}=B,J=await this.credentialProvider();this.validateResolvedCredentials(J);let X=Y??await this.regionProvider(),{longDate:V,shortDate:C}=this.formatDate(Q);if(D>hyA)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");let K=eU1(C,X,W??this.service),H=dyA(r00(A),{unhoistableHeaders:G,hoistableHeaders:I});if(J.sessionToken)H.query[e00]=J.sessionToken;H.query[MyA]=oU1,H.query[RyA]=`${J.accessKeyId}/${K}`,H.query[o00]=V,H.query[TyA]=D.toString(10);let z=a00(H,Z,F);return H.query[OyA]=this.getCanonicalHeaderList(z),H.query[t00]=await this.getSignature(V,K,this.getSigningKey(J,X,C,W),this.createCanonicalRequest(H,z,await Aw1(A,this.sha256))),H}async sign(A,B){if(typeof A==="string")return this.signString(A,B);else if(A.headers&&A.payload)return this.signEvent(A,B);else if(A.message)return this.signMessage(A,B);else return this.signRequest(A,B)}async signEvent({headers:A,payload:B},{signingDate:Q=new Date,priorSignature:D,signingRegion:Z,signingService:G}){let F=Z??await this.regionProvider(),{shortDate:I,longDate:Y}=this.formatDate(Q),W=eU1(I,F,G??this.service),J=await Aw1({headers:{},body:B},this.sha256),X=new this.sha256;X.update(A);let V=My.toHex(await X.digest()),C=[vyA,Y,W,D,V,J].join(`
`);return this.signString(C,{signingDate:Q,signingRegion:F,signingService:G})}async signMessage(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}){return this.signEvent({headers:this.headerFormatter.format(A.message.headers),payload:A.message.body},{signingDate:B,signingRegion:Q,signingService:D,priorSignature:A.priorSignature}).then((G)=>{return{message:A.message,signature:G}})}async signString(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}={}){let Z=await this.credentialProvider();this.validateResolvedCredentials(Z);let G=Q??await this.regionProvider(),{shortDate:F}=this.formatDate(B),I=new this.sha256(await this.getSigningKey(Z,G,F,D));return I.update(wyA.toUint8Array(A)),My.toHex(await I.digest())}async signRequest(A,{signingDate:B=new Date,signableHeaders:Q,unsignableHeaders:D,signingRegion:Z,signingService:G}={}){let F=await this.credentialProvider();this.validateResolvedCredentials(F);let I=Z??await this.regionProvider(),Y=r00(A),{longDate:W,shortDate:J}=this.formatDate(B),X=eU1(J,I,G??this.service);if(Y.headers[BA0]=W,F.sessionToken)Y.headers[kyA]=F.sessionToken;let V=await Aw1(Y,this.sha256);if(!uyA(Qw1,Y.headers)&&this.applyChecksum)Y.headers[Qw1]=V;let C=a00(Y,D,Q),K=await this.getSignature(W,X,this.getSigningKey(F,I,J,G),this.createCanonicalRequest(Y,C,V));return Y.headers[AA0]=`${oU1} Credential=${F.accessKeyId}/${X}, SignedHeaders=${this.getCanonicalHeaderList(C)}, Signature=${K}`,Y}async getSignature(A,B,Q,D){let Z=await this.createStringToSign(A,B,D,oU1),G=new this.sha256(await Q);return G.update(wyA.toUint8Array(Z)),My.toHex(await G.digest())}getSigningKey(A,B,Q,D){return gyA(this.sha256,A,Q,B,D||this.service)}},pxQ={SignatureV4a:null}});
var j41=E((Y20)=>{Object.defineProperty(Y20,"__esModule",{value:!0});Y20.STSClient=Y20.__Client=void 0;var XhA=C41(),imQ=K41(),nmQ=H41(),VhA=Da(),amQ=V4(),I20=VB(),smQ=TG(),rmQ=q6(),ChA=v4(),HhA=W6();Object.defineProperty(Y20,"__Client",{enumerable:!0,get:function(){return HhA.Client}});var KhA=B20(),omQ=k41(),tmQ=AhA(),emQ=JhA();class zhA extends HhA.Client{config;constructor(...[A]){let B=tmQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=omQ.resolveClientEndpointParameters(B),D=VhA.resolveUserAgentConfig(Q),Z=ChA.resolveRetryConfig(D),G=amQ.resolveRegionConfig(Z),F=XhA.resolveHostHeaderConfig(G),I=rmQ.resolveEndpointConfig(F),Y=KhA.resolveHttpAuthSchemeConfig(I),W=emQ.resolveRuntimeExtensions(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(VhA.getUserAgentPlugin(this.config)),this.middlewareStack.use(ChA.getRetryPlugin(this.config)),this.middlewareStack.use(smQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(XhA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(imQ.getLoggerPlugin(this.config)),this.middlewareStack.use(nmQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(I20.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:KhA.defaultSTSHttpAuthSchemeParametersProvider,identityProviderConfigProvider:async(J)=>new I20.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials})})),this.middlewareStack.use(I20.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}}Y20.STSClient=zhA});
var jA0=E((wF5,FxA)=>{var{defineProperty:Vw1,getOwnPropertyDescriptor:FfQ,getOwnPropertyNames:IfQ}=Object,YfQ=Object.prototype.hasOwnProperty,WfQ=(A,B)=>Vw1(A,"name",{value:B,configurable:!0}),JfQ=(A,B)=>{for(var Q in B)Vw1(A,Q,{get:B[Q],enumerable:!0})},XfQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of IfQ(B))if(!YfQ.call(A,Z)&&Z!==Q)Vw1(A,Z,{get:()=>B[Z],enumerable:!(D=FfQ(B,Z))||D.enumerable})}return A},VfQ=(A)=>XfQ(Vw1({},"__esModule",{value:!0}),A),e_A={};JfQ(e_A,{ENV_ACCOUNT_ID:()=>GxA,ENV_CREDENTIAL_SCOPE:()=>ZxA,ENV_EXPIRATION:()=>DxA,ENV_KEY:()=>AxA,ENV_SECRET:()=>BxA,ENV_SESSION:()=>QxA,fromEnv:()=>HfQ});FxA.exports=VfQ(e_A);var CfQ=Lw(),KfQ=eB(),AxA="AWS_ACCESS_KEY_ID",BxA="AWS_SECRET_ACCESS_KEY",QxA="AWS_SESSION_TOKEN",DxA="AWS_CREDENTIAL_EXPIRATION",ZxA="AWS_CREDENTIAL_SCOPE",GxA="AWS_ACCOUNT_ID",HfQ=WfQ((A)=>async()=>{A?.logger?.debug("@aws-sdk/credential-provider-env - fromEnv");let B=process.env[AxA],Q=process.env[BxA],D=process.env[QxA],Z=process.env[DxA],G=process.env[ZxA],F=process.env[GxA];if(B&&Q){let I={accessKeyId:B,secretAccessKey:Q,...D&&{sessionToken:D},...Z&&{expiration:new Date(Z)},...G&&{credentialScope:G},...F&&{accountId:F}};return CfQ.setCredentialFeature(I,"CREDENTIALS_ENV_VARS","g"),I}throw new KfQ.CredentialsProviderError("Unable to find environment variable credentials.",{logger:A?.logger})},"fromEnv")});
var jsA=E((PsA)=>{Object.defineProperty(PsA,"__esModule",{value:!0});PsA.fromTemporaryCredentials=void 0;var _24=V4(),x24=QD(),v24=IQ0(),b24=TsA(),f24=(A)=>{return b24.fromTemporaryCredentials(A,v24.fromNodeProviderChain,async({profile:B=process.env.AWS_PROFILE})=>x24.loadConfig({environmentVariableSelector:(Q)=>Q.AWS_REGION,configFileSelector:(Q)=>{return Q.region},default:()=>{return}},{..._24.NODE_REGION_CONFIG_FILE_OPTIONS,profile:B})())};PsA.fromTemporaryCredentials=f24});
var k41=E((HfA)=>{Object.defineProperty(HfA,"__esModule",{value:!0});HfA.commonParams=HfA.resolveClientEndpointParameters=void 0;var EmQ=(A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,useGlobalEndpoint:A.useGlobalEndpoint??!1,defaultSigningName:"sts"})};HfA.resolveClientEndpointParameters=EmQ;HfA.commonParams={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}});
var kcA=E((ScA)=>{Object.defineProperty(ScA,"__esModule",{value:!0});ScA.fromBase64=void 0;var lsQ=AD(),psQ=/^[A-Za-z0-9+/]*={0,2}$/,isQ=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!psQ.exec(A))throw new TypeError("Invalid base64 string.");let B=lsQ.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};ScA.fromBase64=isQ});
var kiA=E((SiA)=>{Object.defineProperty(SiA,"__esModule",{value:!0});SiA.defaultEndpointResolver=void 0;var geQ=h41(),m90=$7(),ueQ=PiA(),meQ=new m90.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),deQ=(A,B={})=>{return meQ.get(A,()=>m90.resolveEndpoint(ueQ.ruleSet,{endpointParams:A,logger:B.logger}))};SiA.defaultEndpointResolver=deQ;m90.customEndpointFunctions.aws=geQ.awsEndpointFunctions});
var l$1=E((mW5,maA)=>{var{defineProperty:d$1,getOwnPropertyDescriptor:EA4,getOwnPropertyNames:vaA}=Object,UA4=Object.prototype.hasOwnProperty,c$1=(A,B)=>d$1(A,"name",{value:B,configurable:!0}),wA4=(A,B)=>function Q(){return A&&(B=A[vaA(A)[0]](A=0)),B},baA=(A,B)=>{for(var Q in B)d$1(A,Q,{get:B[Q],enumerable:!0})},$A4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of vaA(B))if(!UA4.call(A,Z)&&Z!==Q)d$1(A,Z,{get:()=>B[Z],enumerable:!(D=EA4(B,Z))||D.enumerable})}return A},qA4=(A)=>$A4(d$1({},"__esModule",{value:!0}),A),faA={};baA(faA,{GetRoleCredentialsCommand:()=>e90.GetRoleCredentialsCommand,SSOClient:()=>e90.SSOClient});var e90,NA4=wA4({"src/loadSso.ts"(){e90=EnA()}}),haA={};baA(haA,{fromSSO:()=>MA4,isSsoProfile:()=>gaA,validateSsoProfile:()=>uaA});maA.exports=qA4(haA);var gaA=c$1((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),_aA=Mz(),LA4=yaA(),Rw=eB(),m$1=e5(),e41=!1,xaA=c$1(async({ssoStartUrl:A,ssoSession:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoClient:G,clientConfig:F,parentClientConfig:I,profile:Y,logger:W})=>{let J,X="To refresh this SSO session run aws sso login with the corresponding profile.";if(B)try{let f=await LA4.fromSso({profile:Y})();J={accessToken:f.token,expiresAt:new Date(f.expiration).toISOString()}}catch(f){throw new Rw.CredentialsProviderError(f.message,{tryNextLink:e41,logger:W})}else try{J=await m$1.getSSOTokenFromFile(A)}catch(f){throw new Rw.CredentialsProviderError("The SSO session associated with this profile is invalid. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:e41,logger:W})}if(new Date(J.expiresAt).getTime()-Date.now()<=0)throw new Rw.CredentialsProviderError("The SSO session associated with this profile has expired. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:e41,logger:W});let{accessToken:V}=J,{SSOClient:C,GetRoleCredentialsCommand:K}=await Promise.resolve().then(()=>(NA4(),faA)),H=G||new C(Object.assign({},F??{},{logger:F?.logger??I?.logger,region:F?.region??D})),z;try{z=await H.send(new K({accountId:Q,roleName:Z,accessToken:V}))}catch(f){throw new Rw.CredentialsProviderError(f,{tryNextLink:e41,logger:W})}let{roleCredentials:{accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:O,credentialScope:R,accountId:T}={}}=z;if(!$||!L||!N||!O)throw new Rw.CredentialsProviderError("SSO returns an invalid temporary credential.",{tryNextLink:e41,logger:W});let j={accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:new Date(O),...R&&{credentialScope:R},...T&&{accountId:T}};if(B)_aA.setCredentialFeature(j,"CREDENTIALS_SSO","s");else _aA.setCredentialFeature(j,"CREDENTIALS_SSO_LEGACY","u");return j},"resolveSSOCredentials"),uaA=c$1((A,B)=>{let{sso_start_url:Q,sso_account_id:D,sso_region:Z,sso_role_name:G}=A;if(!Q||!D||!Z||!G)throw new Rw.CredentialsProviderError(`Profile is configured with invalid SSO credentials. Required parameters "sso_account_id", "sso_region", "sso_role_name", "sso_start_url". Got ${Object.keys(A).join(", ")}
Reference: https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.html`,{tryNextLink:!1,logger:B});return A},"validateSsoProfile"),MA4=c$1((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-sso - fromSSO");let{ssoStartUrl:Q,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoSession:F}=A,{ssoClient:I}=A,Y=m$1.getProfileName({profile:A.profile??B?.profile});if(!Q&&!D&&!Z&&!G&&!F){let J=(await m$1.parseKnownFiles(A))[Y];if(!J)throw new Rw.CredentialsProviderError(`Profile ${Y} was not found.`,{logger:A.logger});if(!gaA(J))throw new Rw.CredentialsProviderError(`Profile ${Y} is not configured with SSO credentials.`,{logger:A.logger});if(J?.sso_session){let $=(await m$1.loadSsoSessionData(A))[J.sso_session],L=` configurations in profile ${Y} and sso-session ${J.sso_session}`;if(Z&&Z!==$.sso_region)throw new Rw.CredentialsProviderError("Conflicting SSO region"+L,{tryNextLink:!1,logger:A.logger});if(Q&&Q!==$.sso_start_url)throw new Rw.CredentialsProviderError("Conflicting SSO start_url"+L,{tryNextLink:!1,logger:A.logger});J.sso_region=$.sso_region,J.sso_start_url=$.sso_start_url}let{sso_start_url:X,sso_account_id:V,sso_region:C,sso_role_name:K,sso_session:H}=uaA(J,A.logger);return xaA({ssoStartUrl:X,ssoSession:H,ssoAccountId:V,ssoRegion:C,ssoRoleName:K,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})}else if(!Q||!D||!Z||!G)throw new Rw.CredentialsProviderError('Incomplete configuration. The fromSSO() argument hash must include "ssoStartUrl", "ssoAccountId", "ssoRegion", "ssoRoleName"',{tryNextLink:!1,logger:A.logger});else return xaA({ssoStartUrl:Q,ssoSession:F,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})},"fromSSO")});
var l41=E((eY5,JlA)=>{var{defineProperty:w$1,getOwnPropertyDescriptor:mrQ,getOwnPropertyNames:drQ}=Object,crQ=Object.prototype.hasOwnProperty,CT=(A,B)=>w$1(A,"name",{value:B,configurable:!0}),lrQ=(A,B)=>{for(var Q in B)w$1(A,Q,{get:B[Q],enumerable:!0})},prQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of drQ(B))if(!crQ.call(A,Z)&&Z!==Q)w$1(A,Z,{get:()=>B[Z],enumerable:!(D=mrQ(B,Z))||D.enumerable})}return A},irQ=(A)=>prQ(w$1({},"__esModule",{value:!0}),A),QlA={};lrQ(QlA,{DEFAULT_UA_APP_ID:()=>DlA,getUserAgentMiddlewareOptions:()=>WlA,getUserAgentPlugin:()=>AoQ,resolveUserAgentConfig:()=>GlA,userAgentMiddleware:()=>YlA});JlA.exports=irQ(QlA);var nrQ=VB(),DlA=void 0;function ZlA(A){if(A===void 0)return!0;return typeof A==="string"&&A.length<=50}CT(ZlA,"isValidUserAgentAppId");function GlA(A){let B=nrQ.normalizeProvider(A.userAgentAppId??DlA),{customUserAgent:Q}=A;return Object.assign(A,{customUserAgent:typeof Q==="string"?[[Q]]:Q,userAgentAppId:CT(async()=>{let D=await B();if(!ZlA(D)){let Z=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console:A.logger;if(typeof D!=="string")Z?.warn("userAgentAppId must be a string or undefined.");else if(D.length>50)Z?.warn("The provided userAgentAppId exceeds the maximum length of 50 characters.")}return D},"userAgentAppId")})}CT(GlA,"resolveUserAgentConfig");var arQ=h41(),srQ=DK(),QL=XV(),rrQ=/\d{12}\.ddb/;async function FlA(A,B,Q){if(Q.request?.headers?.["smithy-protocol"]==="rpc-v2-cbor")QL.setFeature(A,"PROTOCOL_RPC_V2_CBOR","M");if(typeof B.retryStrategy==="function"){let G=await B.retryStrategy();if(typeof G.acquireInitialRetryToken==="function")if(G.constructor?.name?.includes("Adaptive"))QL.setFeature(A,"RETRY_MODE_ADAPTIVE","F");else QL.setFeature(A,"RETRY_MODE_STANDARD","E");else QL.setFeature(A,"RETRY_MODE_LEGACY","D")}if(typeof B.accountIdEndpointMode==="function"){let G=A.endpointV2;if(String(G?.url?.hostname).match(rrQ))QL.setFeature(A,"ACCOUNT_ID_ENDPOINT","O");switch(await B.accountIdEndpointMode?.()){case"disabled":QL.setFeature(A,"ACCOUNT_ID_MODE_DISABLED","Q");break;case"preferred":QL.setFeature(A,"ACCOUNT_ID_MODE_PREFERRED","P");break;case"required":QL.setFeature(A,"ACCOUNT_ID_MODE_REQUIRED","R");break}}let Z=A.__smithy_context?.selectedHttpAuthScheme?.identity;if(Z?.$source){let G=Z;if(G.accountId)QL.setFeature(A,"RESOLVED_ACCOUNT_ID","T");for(let[F,I]of Object.entries(G.$source??{}))QL.setFeature(A,F,I)}}CT(FlA,"checkFeatures");var ecA="user-agent",iB0="x-amz-user-agent",AlA=" ",nB0="/",orQ=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g,trQ=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g,BlA="-",erQ=1024;function IlA(A){let B="";for(let Q in A){let D=A[Q];if(B.length+D.length+1<=erQ){if(B.length)B+=","+D;else B+=D;continue}break}return B}CT(IlA,"encodeFeatures");var YlA=CT((A)=>(B,Q)=>async(D)=>{let{request:Z}=D;if(!srQ.HttpRequest.isInstance(Z))return B(D);let{headers:G}=Z,F=Q?.userAgent?.map(U$1)||[],I=(await A.defaultUserAgentProvider()).map(U$1);await FlA(Q,A,D);let Y=Q;I.push(`m/${IlA(Object.assign({},Q.__smithy_context?.features,Y.__aws_sdk_context?.features))}`);let W=A?.customUserAgent?.map(U$1)||[],J=await A.userAgentAppId();if(J)I.push(U$1([`app/${J}`]));let X=arQ.getUserAgentPrefix(),V=(X?[X]:[]).concat([...I,...F,...W]).join(AlA),C=[...I.filter((K)=>K.startsWith("aws-sdk-")),...W].join(AlA);if(A.runtime!=="browser"){if(C)G[iB0]=G[iB0]?`${G[ecA]} ${C}`:C;G[ecA]=V}else G[iB0]=V;return B({...D,request:Z})},"userAgentMiddleware"),U$1=CT((A)=>{let B=A[0].split(nB0).map((F)=>F.replace(orQ,BlA)).join(nB0),Q=A[1]?.replace(trQ,BlA),D=B.indexOf(nB0),Z=B.substring(0,D),G=B.substring(D+1);if(Z==="api")G=G.toLowerCase();return[Z,G,Q].filter((F)=>F&&F.length>0).reduce((F,I,Y)=>{switch(Y){case 0:return I;case 1:return`${F}/${I}`;default:return`${F}#${I}`}},"")},"escapeUserAgent"),WlA={name:"getUserAgentMiddleware",step:"build",priority:"low",tags:["SET_USER_AGENT","USER_AGENT"],override:!0},AoQ=CT((A)=>({applyToStack:CT((B)=>{B.add(YlA(A),WlA)},"applyToStack")}),"getUserAgentPlugin")});
var lA0=E((mF5,PgQ)=>{PgQ.exports={name:"@aws-sdk/nested-clients",version:"3.840.0",description:"Nested clients for AWS SDK packages.",main:"./dist-cjs/index.js",module:"./dist-es/index.js",types:"./dist-types/index.d.ts",scripts:{build:"yarn lint && concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline nested-clients","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo",lint:"node ../../scripts/validation/submodules-linter.js --pkg nested-clients",test:"yarn g:vitest run","test:watch":"yarn g:vitest watch"},engines:{node:">=18.0.0"},author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["./sso-oidc.d.ts","./sso-oidc.js","./sts.d.ts","./sts.js","dist-*/**"],browser:{"./dist-es/submodules/sso-oidc/runtimeConfig":"./dist-es/submodules/sso-oidc/runtimeConfig.browser","./dist-es/submodules/sts/runtimeConfig":"./dist-es/submodules/sts/runtimeConfig.browser"},"react-native":{},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/packages/nested-clients",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"packages/nested-clients"},exports:{"./sso-oidc":{types:"./dist-types/submodules/sso-oidc/index.d.ts",module:"./dist-es/submodules/sso-oidc/index.js",node:"./dist-cjs/submodules/sso-oidc/index.js",import:"./dist-es/submodules/sso-oidc/index.js",require:"./dist-cjs/submodules/sso-oidc/index.js"},"./sts":{types:"./dist-types/submodules/sts/index.d.ts",module:"./dist-es/submodules/sts/index.js",node:"./dist-cjs/submodules/sts/index.js",import:"./dist-es/submodules/sts/index.js",require:"./dist-cjs/submodules/sts/index.js"}}}});
var lnA=E((dnA)=>{Object.defineProperty(dnA,"__esModule",{value:!0});dnA.getRuntimeConfig=void 0;var t14=XV(),e14=VB(),A04=H8(),B04=BZ(),unA=Dg(),mnA=cB(),Q04=p90(),D04=gnA(),Z04=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??unA.fromBase64,base64Encoder:A?.base64Encoder??unA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??D04.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??Q04.defaultSSOOIDCHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new t14.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new e14.NoAuthSigner}],logger:A?.logger??new A04.NoOpLogger,serviceId:A?.serviceId??"SSO OIDC",urlParser:A?.urlParser??B04.parseUrl,utf8Decoder:A?.utf8Decoder??mnA.fromUtf8,utf8Encoder:A?.utf8Encoder??mnA.toUtf8}};dnA.getRuntimeConfig=Z04});
var mdA=E((MY5,udA)=>{var{defineProperty:F$1,getOwnPropertyDescriptor:raQ,getOwnPropertyNames:oaQ}=Object,taQ=Object.prototype.hasOwnProperty,$B0=(A,B)=>F$1(A,"name",{value:B,configurable:!0}),eaQ=(A,B)=>{for(var Q in B)F$1(A,Q,{get:B[Q],enumerable:!0})},AsQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of oaQ(B))if(!taQ.call(A,Z)&&Z!==Q)F$1(A,Z,{get:()=>B[Z],enumerable:!(D=raQ(B,Z))||D.enumerable})}return A},BsQ=(A)=>AsQ(F$1({},"__esModule",{value:!0}),A),hdA={};eaQ(hdA,{escapeUri:()=>gdA,escapeUriPath:()=>DsQ});udA.exports=BsQ(hdA);var gdA=$B0((A)=>encodeURIComponent(A).replace(/[!'()*]/g,QsQ),"escapeUri"),QsQ=$B0((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),DsQ=$B0((A)=>A.split("/").map(gdA).join("/"),"escapeUriPath")});
var mvA=E((_F5,uvA)=>{var{defineProperty:Uw1,getOwnPropertyDescriptor:mhQ,getOwnPropertyNames:dhQ}=Object,chQ=Object.prototype.hasOwnProperty,L6=(A,B)=>Uw1(A,"name",{value:B,configurable:!0}),lhQ=(A,B)=>{for(var Q in B)Uw1(A,Q,{get:B[Q],enumerable:!0})},phQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of dhQ(B))if(!chQ.call(A,Z)&&Z!==Q)Uw1(A,Z,{get:()=>B[Z],enumerable:!(D=mhQ(B,Z))||D.enumerable})}return A},ihQ=(A)=>phQ(Uw1({},"__esModule",{value:!0}),A),$vA={};lhQ($vA,{GetRoleCredentialsCommand:()=>fvA,GetRoleCredentialsRequestFilterSensitiveLog:()=>RvA,GetRoleCredentialsResponseFilterSensitiveLog:()=>TvA,InvalidRequestException:()=>qvA,ListAccountRolesCommand:()=>uA0,ListAccountRolesRequestFilterSensitiveLog:()=>PvA,ListAccountsCommand:()=>mA0,ListAccountsRequestFilterSensitiveLog:()=>SvA,LogoutCommand:()=>hvA,LogoutRequestFilterSensitiveLog:()=>jvA,ResourceNotFoundException:()=>NvA,RoleCredentialsFilterSensitiveLog:()=>OvA,SSO:()=>gvA,SSOClient:()=>$w1,SSOServiceException:()=>Ia,TooManyRequestsException:()=>LvA,UnauthorizedException:()=>MvA,__Client:()=>RB.Client,paginateListAccountRoles:()=>UgQ,paginateListAccounts:()=>wgQ});uvA.exports=ihQ($vA);var KvA=C41(),nhQ=K41(),ahQ=H41(),HvA=Da(),shQ=V4(),FT=VB(),rhQ=TG(),R41=q6(),zvA=v4(),EvA=vA0(),ohQ=L6((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"awsssoportal"})},"resolveClientEndpointParameters"),ww1={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},thQ=IvA(),UvA=L41(),wvA=JV(),RB=W6(),ehQ=L6((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),AgQ=L6((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),BgQ=L6((A,B)=>{let Q=Object.assign(UvA.getAwsRegionExtensionConfiguration(A),RB.getDefaultExtensionConfiguration(A),wvA.getHttpHandlerExtensionConfiguration(A),ehQ(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,UvA.resolveAwsRegionExtensionConfiguration(Q),RB.resolveDefaultRuntimeConfig(Q),wvA.resolveHttpHandlerRuntimeConfig(Q),AgQ(Q))},"resolveRuntimeExtensions"),$w1=class extends RB.Client{static{L6(this,"SSOClient")}config;constructor(...[A]){let B=thQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=ohQ(B),D=HvA.resolveUserAgentConfig(Q),Z=zvA.resolveRetryConfig(D),G=shQ.resolveRegionConfig(Z),F=KvA.resolveHostHeaderConfig(G),I=R41.resolveEndpointConfig(F),Y=EvA.resolveHttpAuthSchemeConfig(I),W=BgQ(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(HvA.getUserAgentPlugin(this.config)),this.middlewareStack.use(zvA.getRetryPlugin(this.config)),this.middlewareStack.use(rhQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(KvA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(nhQ.getLoggerPlugin(this.config)),this.middlewareStack.use(ahQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(FT.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:EvA.defaultSSOHttpAuthSchemeParametersProvider,identityProviderConfigProvider:L6(async(J)=>new FT.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(FT.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},qw1=T3(),Ia=class A extends RB.ServiceException{static{L6(this,"SSOServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},qvA=class A extends Ia{static{L6(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},NvA=class A extends Ia{static{L6(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},LvA=class A extends Ia{static{L6(this,"TooManyRequestsException")}name="TooManyRequestsException";$fault="client";constructor(B){super({name:"TooManyRequestsException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},MvA=class A extends Ia{static{L6(this,"UnauthorizedException")}name="UnauthorizedException";$fault="client";constructor(B){super({name:"UnauthorizedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},RvA=L6((A)=>({...A,...A.accessToken&&{accessToken:RB.SENSITIVE_STRING}}),"GetRoleCredentialsRequestFilterSensitiveLog"),OvA=L6((A)=>({...A,...A.secretAccessKey&&{secretAccessKey:RB.SENSITIVE_STRING},...A.sessionToken&&{sessionToken:RB.SENSITIVE_STRING}}),"RoleCredentialsFilterSensitiveLog"),TvA=L6((A)=>({...A,...A.roleCredentials&&{roleCredentials:OvA(A.roleCredentials)}}),"GetRoleCredentialsResponseFilterSensitiveLog"),PvA=L6((A)=>({...A,...A.accessToken&&{accessToken:RB.SENSITIVE_STRING}}),"ListAccountRolesRequestFilterSensitiveLog"),SvA=L6((A)=>({...A,...A.accessToken&&{accessToken:RB.SENSITIVE_STRING}}),"ListAccountsRequestFilterSensitiveLog"),jvA=L6((A)=>({...A,...A.accessToken&&{accessToken:RB.SENSITIVE_STRING}}),"LogoutRequestFilterSensitiveLog"),M41=II(),QgQ=L6(async(A,B)=>{let Q=FT.requestBuilder(A,B),D=RB.map({},RB.isSerializableHeaderValue,{[Mw1]:A[Lw1]});Q.bp("/federation/credentials");let Z=RB.map({[zgQ]:[,RB.expectNonNull(A[HgQ],"roleName")],[yvA]:[,RB.expectNonNull(A[kvA],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_GetRoleCredentialsCommand"),DgQ=L6(async(A,B)=>{let Q=FT.requestBuilder(A,B),D=RB.map({},RB.isSerializableHeaderValue,{[Mw1]:A[Lw1]});Q.bp("/assignment/roles");let Z=RB.map({[bvA]:[,A[vvA]],[xvA]:[()=>A.maxResults!==void 0,()=>A[_vA].toString()],[yvA]:[,RB.expectNonNull(A[kvA],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountRolesCommand"),ZgQ=L6(async(A,B)=>{let Q=FT.requestBuilder(A,B),D=RB.map({},RB.isSerializableHeaderValue,{[Mw1]:A[Lw1]});Q.bp("/assignment/accounts");let Z=RB.map({[bvA]:[,A[vvA]],[xvA]:[()=>A.maxResults!==void 0,()=>A[_vA].toString()]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountsCommand"),GgQ=L6(async(A,B)=>{let Q=FT.requestBuilder(A,B),D=RB.map({},RB.isSerializableHeaderValue,{[Mw1]:A[Lw1]});Q.bp("/logout");let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_LogoutCommand"),FgQ=L6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return Nw1(A,B);let Q=RB.map({$metadata:Ty(A)}),D=RB.expectNonNull(RB.expectObject(await M41.parseJsonBody(A.body,B)),"body"),Z=RB.take(D,{roleCredentials:RB._json});return Object.assign(Q,Z),Q},"de_GetRoleCredentialsCommand"),IgQ=L6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return Nw1(A,B);let Q=RB.map({$metadata:Ty(A)}),D=RB.expectNonNull(RB.expectObject(await M41.parseJsonBody(A.body,B)),"body"),Z=RB.take(D,{nextToken:RB.expectString,roleList:RB._json});return Object.assign(Q,Z),Q},"de_ListAccountRolesCommand"),YgQ=L6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return Nw1(A,B);let Q=RB.map({$metadata:Ty(A)}),D=RB.expectNonNull(RB.expectObject(await M41.parseJsonBody(A.body,B)),"body"),Z=RB.take(D,{accountList:RB._json,nextToken:RB.expectString});return Object.assign(Q,Z),Q},"de_ListAccountsCommand"),WgQ=L6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return Nw1(A,B);let Q=RB.map({$metadata:Ty(A)});return await RB.collectBody(A.body,B),Q},"de_LogoutCommand"),Nw1=L6(async(A,B)=>{let Q={...A,body:await M41.parseJsonErrorBody(A.body,B)},D=M41.loadRestJsonErrorCode(A,Q.body);switch(D){case"InvalidRequestException":case"com.amazonaws.sso#InvalidRequestException":throw await XgQ(Q,B);case"ResourceNotFoundException":case"com.amazonaws.sso#ResourceNotFoundException":throw await VgQ(Q,B);case"TooManyRequestsException":case"com.amazonaws.sso#TooManyRequestsException":throw await CgQ(Q,B);case"UnauthorizedException":case"com.amazonaws.sso#UnauthorizedException":throw await KgQ(Q,B);default:let Z=Q.body;return JgQ({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),JgQ=RB.withBaseException(Ia),XgQ=L6(async(A,B)=>{let Q=RB.map({}),D=A.body,Z=RB.take(D,{message:RB.expectString});Object.assign(Q,Z);let G=new qvA({$metadata:Ty(A),...Q});return RB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),VgQ=L6(async(A,B)=>{let Q=RB.map({}),D=A.body,Z=RB.take(D,{message:RB.expectString});Object.assign(Q,Z);let G=new NvA({$metadata:Ty(A),...Q});return RB.decorateServiceException(G,A.body)},"de_ResourceNotFoundExceptionRes"),CgQ=L6(async(A,B)=>{let Q=RB.map({}),D=A.body,Z=RB.take(D,{message:RB.expectString});Object.assign(Q,Z);let G=new LvA({$metadata:Ty(A),...Q});return RB.decorateServiceException(G,A.body)},"de_TooManyRequestsExceptionRes"),KgQ=L6(async(A,B)=>{let Q=RB.map({}),D=A.body,Z=RB.take(D,{message:RB.expectString});Object.assign(Q,Z);let G=new MvA({$metadata:Ty(A),...Q});return RB.decorateServiceException(G,A.body)},"de_UnauthorizedExceptionRes"),Ty=L6((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),kvA="accountId",Lw1="accessToken",yvA="account_id",_vA="maxResults",xvA="max_result",vvA="nextToken",bvA="next_token",HgQ="roleName",zgQ="role_name",Mw1="x-amz-sso_bearer_token",fvA=class extends RB.Command.classBuilder().ep(ww1).m(function(A,B,Q,D){return[qw1.getSerdePlugin(Q,this.serialize,this.deserialize),R41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","GetRoleCredentials",{}).n("SSOClient","GetRoleCredentialsCommand").f(RvA,TvA).ser(QgQ).de(FgQ).build(){static{L6(this,"GetRoleCredentialsCommand")}},uA0=class extends RB.Command.classBuilder().ep(ww1).m(function(A,B,Q,D){return[qw1.getSerdePlugin(Q,this.serialize,this.deserialize),R41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccountRoles",{}).n("SSOClient","ListAccountRolesCommand").f(PvA,void 0).ser(DgQ).de(IgQ).build(){static{L6(this,"ListAccountRolesCommand")}},mA0=class extends RB.Command.classBuilder().ep(ww1).m(function(A,B,Q,D){return[qw1.getSerdePlugin(Q,this.serialize,this.deserialize),R41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccounts",{}).n("SSOClient","ListAccountsCommand").f(SvA,void 0).ser(ZgQ).de(YgQ).build(){static{L6(this,"ListAccountsCommand")}},hvA=class extends RB.Command.classBuilder().ep(ww1).m(function(A,B,Q,D){return[qw1.getSerdePlugin(Q,this.serialize,this.deserialize),R41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","Logout",{}).n("SSOClient","LogoutCommand").f(jvA,void 0).ser(GgQ).de(WgQ).build(){static{L6(this,"LogoutCommand")}},EgQ={GetRoleCredentialsCommand:fvA,ListAccountRolesCommand:uA0,ListAccountsCommand:mA0,LogoutCommand:hvA},gvA=class extends $w1{static{L6(this,"SSO")}};RB.createAggregatedClient(EgQ,gvA);var UgQ=FT.createPaginator($w1,uA0,"nextToken","nextToken","maxResults"),wgQ=FT.createPaginator($w1,mA0,"nextToken","nextToken","maxResults")});
var nw1=E((EB0)=>{Object.defineProperty(EB0,"__esModule",{value:!0});EB0.fromHttp=void 0;var nnQ=AdA();Object.defineProperty(EB0,"fromHttp",{enumerable:!0,get:function(){return nnQ.fromHttp}})});
var o90=E((bW5,MaA)=>{var{defineProperty:u$1,getOwnPropertyDescriptor:H04,getOwnPropertyNames:z04}=Object,E04=Object.prototype.hasOwnProperty,i4=(A,B)=>u$1(A,"name",{value:B,configurable:!0}),U04=(A,B)=>{for(var Q in B)u$1(A,Q,{get:B[Q],enumerable:!0})},w04=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of z04(B))if(!E04.call(A,Z)&&Z!==Q)u$1(A,Z,{get:()=>B[Z],enumerable:!(D=H04(B,Z))||D.enumerable})}return A},$04=(A)=>w04(u$1({},"__esModule",{value:!0}),A),GaA={};U04(GaA,{$Command:()=>YaA.Command,AccessDeniedException:()=>WaA,AuthorizationPendingException:()=>JaA,CreateTokenCommand:()=>NaA,CreateTokenRequestFilterSensitiveLog:()=>XaA,CreateTokenResponseFilterSensitiveLog:()=>VaA,ExpiredTokenException:()=>CaA,InternalServerException:()=>KaA,InvalidClientException:()=>HaA,InvalidGrantException:()=>zaA,InvalidRequestException:()=>EaA,InvalidScopeException:()=>UaA,SSOOIDC:()=>LaA,SSOOIDCClient:()=>IaA,SSOOIDCServiceException:()=>GK,SlowDownException:()=>waA,UnauthorizedClientException:()=>$aA,UnsupportedGrantTypeException:()=>qaA,__Client:()=>FaA.Client});MaA.exports=$04(GaA);var tnA=tw1(),q04=A$1(),N04=D$1(),enA=l41(),L04=V4(),s90=VB(),M04=TG(),R04=q6(),AaA=v4(),FaA=H8(),BaA=p90(),O04=i4((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"sso-oauth"})},"resolveClientEndpointParameters"),T04={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},P04=onA(),QaA=R$1(),DaA=DK(),ZaA=H8(),S04=i4((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),j04=i4((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),k04=i4((A,B)=>{let Q=Object.assign(QaA.getAwsRegionExtensionConfiguration(A),ZaA.getDefaultExtensionConfiguration(A),DaA.getHttpHandlerExtensionConfiguration(A),S04(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,QaA.resolveAwsRegionExtensionConfiguration(Q),ZaA.resolveDefaultRuntimeConfig(Q),DaA.resolveHttpHandlerRuntimeConfig(Q),j04(Q))},"resolveRuntimeExtensions"),IaA=class extends FaA.Client{static{i4(this,"SSOOIDCClient")}config;constructor(...[A]){let B=P04.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=O04(B),D=enA.resolveUserAgentConfig(Q),Z=AaA.resolveRetryConfig(D),G=L04.resolveRegionConfig(Z),F=tnA.resolveHostHeaderConfig(G),I=R04.resolveEndpointConfig(F),Y=BaA.resolveHttpAuthSchemeConfig(I),W=k04(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(enA.getUserAgentPlugin(this.config)),this.middlewareStack.use(AaA.getRetryPlugin(this.config)),this.middlewareStack.use(M04.getContentLengthPlugin(this.config)),this.middlewareStack.use(tnA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(q04.getLoggerPlugin(this.config)),this.middlewareStack.use(N04.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(s90.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:BaA.defaultSSOOIDCHttpAuthSchemeParametersProvider,identityProviderConfigProvider:i4(async(J)=>new s90.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(s90.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},y04=H8(),_04=q6(),x04=T3(),YaA=H8(),pa=H8(),v04=H8(),GK=class A extends v04.ServiceException{static{i4(this,"SSOOIDCServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},WaA=class A extends GK{static{i4(this,"AccessDeniedException")}name="AccessDeniedException";$fault="client";error;error_description;constructor(B){super({name:"AccessDeniedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},JaA=class A extends GK{static{i4(this,"AuthorizationPendingException")}name="AuthorizationPendingException";$fault="client";error;error_description;constructor(B){super({name:"AuthorizationPendingException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},XaA=i4((A)=>({...A,...A.clientSecret&&{clientSecret:pa.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:pa.SENSITIVE_STRING},...A.codeVerifier&&{codeVerifier:pa.SENSITIVE_STRING}}),"CreateTokenRequestFilterSensitiveLog"),VaA=i4((A)=>({...A,...A.accessToken&&{accessToken:pa.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:pa.SENSITIVE_STRING},...A.idToken&&{idToken:pa.SENSITIVE_STRING}}),"CreateTokenResponseFilterSensitiveLog"),CaA=class A extends GK{static{i4(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";error;error_description;constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},KaA=class A extends GK{static{i4(this,"InternalServerException")}name="InternalServerException";$fault="server";error;error_description;constructor(B){super({name:"InternalServerException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},HaA=class A extends GK{static{i4(this,"InvalidClientException")}name="InvalidClientException";$fault="client";error;error_description;constructor(B){super({name:"InvalidClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},zaA=class A extends GK{static{i4(this,"InvalidGrantException")}name="InvalidGrantException";$fault="client";error;error_description;constructor(B){super({name:"InvalidGrantException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},EaA=class A extends GK{static{i4(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";error;error_description;constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},UaA=class A extends GK{static{i4(this,"InvalidScopeException")}name="InvalidScopeException";$fault="client";error;error_description;constructor(B){super({name:"InvalidScopeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},waA=class A extends GK{static{i4(this,"SlowDownException")}name="SlowDownException";$fault="client";error;error_description;constructor(B){super({name:"SlowDownException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},$aA=class A extends GK{static{i4(this,"UnauthorizedClientException")}name="UnauthorizedClientException";$fault="client";error;error_description;constructor(B){super({name:"UnauthorizedClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},qaA=class A extends GK{static{i4(this,"UnsupportedGrantTypeException")}name="UnsupportedGrantTypeException";$fault="client";error;error_description;constructor(B){super({name:"UnsupportedGrantTypeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},r90=XV(),b04=VB(),UB=H8(),f04=i4(async(A,B)=>{let Q=b04.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/token");let Z;return Z=JSON.stringify(UB.take(A,{clientId:[],clientSecret:[],code:[],codeVerifier:[],deviceCode:[],grantType:[],redirectUri:[],refreshToken:[],scope:i4((G)=>UB._json(G),"scope")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateTokenCommand"),h04=i4(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return g04(A,B);let Q=UB.map({$metadata:kz(A)}),D=UB.expectNonNull(UB.expectObject(await r90.parseJsonBody(A.body,B)),"body"),Z=UB.take(D,{accessToken:UB.expectString,expiresIn:UB.expectInt32,idToken:UB.expectString,refreshToken:UB.expectString,tokenType:UB.expectString});return Object.assign(Q,Z),Q},"de_CreateTokenCommand"),g04=i4(async(A,B)=>{let Q={...A,body:await r90.parseJsonErrorBody(A.body,B)},D=r90.loadRestJsonErrorCode(A,Q.body);switch(D){case"AccessDeniedException":case"com.amazonaws.ssooidc#AccessDeniedException":throw await m04(Q,B);case"AuthorizationPendingException":case"com.amazonaws.ssooidc#AuthorizationPendingException":throw await d04(Q,B);case"ExpiredTokenException":case"com.amazonaws.ssooidc#ExpiredTokenException":throw await c04(Q,B);case"InternalServerException":case"com.amazonaws.ssooidc#InternalServerException":throw await l04(Q,B);case"InvalidClientException":case"com.amazonaws.ssooidc#InvalidClientException":throw await p04(Q,B);case"InvalidGrantException":case"com.amazonaws.ssooidc#InvalidGrantException":throw await i04(Q,B);case"InvalidRequestException":case"com.amazonaws.ssooidc#InvalidRequestException":throw await n04(Q,B);case"InvalidScopeException":case"com.amazonaws.ssooidc#InvalidScopeException":throw await a04(Q,B);case"SlowDownException":case"com.amazonaws.ssooidc#SlowDownException":throw await s04(Q,B);case"UnauthorizedClientException":case"com.amazonaws.ssooidc#UnauthorizedClientException":throw await r04(Q,B);case"UnsupportedGrantTypeException":case"com.amazonaws.ssooidc#UnsupportedGrantTypeException":throw await o04(Q,B);default:let Z=Q.body;return u04({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),u04=UB.withBaseException(GK),m04=i4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new WaA({$metadata:kz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_AccessDeniedExceptionRes"),d04=i4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new JaA({$metadata:kz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_AuthorizationPendingExceptionRes"),c04=i4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new CaA({$metadata:kz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_ExpiredTokenExceptionRes"),l04=i4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new KaA({$metadata:kz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_InternalServerExceptionRes"),p04=i4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new HaA({$metadata:kz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_InvalidClientExceptionRes"),i04=i4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new zaA({$metadata:kz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_InvalidGrantExceptionRes"),n04=i4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new EaA({$metadata:kz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),a04=i4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new UaA({$metadata:kz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_InvalidScopeExceptionRes"),s04=i4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new waA({$metadata:kz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_SlowDownExceptionRes"),r04=i4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new $aA({$metadata:kz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_UnauthorizedClientExceptionRes"),o04=i4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new qaA({$metadata:kz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_UnsupportedGrantTypeExceptionRes"),kz=i4((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),NaA=class extends YaA.Command.classBuilder().ep(T04).m(function(A,B,Q,D){return[x04.getSerdePlugin(Q,this.serialize,this.deserialize),_04.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSSOOIDCService","CreateToken",{}).n("SSOOIDCClient","CreateTokenCommand").f(XaA,VaA).ser(f04).de(h04).build(){static{i4(this,"CreateTokenCommand")}},t04={CreateTokenCommand:NaA},LaA=class extends IaA{static{i4(this,"SSOOIDC")}};y04.createAggregatedClient(t04,LaA)});
var on=E((kG5,ykA)=>{var{defineProperty:cU1,getOwnPropertyDescriptor:p_Q,getOwnPropertyNames:i_Q}=Object,n_Q=Object.prototype.hasOwnProperty,rn=(A,B)=>cU1(A,"name",{value:B,configurable:!0}),a_Q=(A,B)=>{for(var Q in B)cU1(A,Q,{get:B[Q],enumerable:!0})},s_Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of i_Q(B))if(!n_Q.call(A,Z)&&Z!==Q)cU1(A,Z,{get:()=>B[Z],enumerable:!(D=p_Q(B,Z))||D.enumerable})}return A},r_Q=(A)=>s_Q(cU1({},"__esModule",{value:!0}),A),MkA={};a_Q(MkA,{ConditionObject:()=>t3.ConditionObject,DeprecatedObject:()=>t3.DeprecatedObject,EndpointError:()=>t3.EndpointError,EndpointObject:()=>t3.EndpointObject,EndpointObjectHeaders:()=>t3.EndpointObjectHeaders,EndpointObjectProperties:()=>t3.EndpointObjectProperties,EndpointParams:()=>t3.EndpointParams,EndpointResolverOptions:()=>t3.EndpointResolverOptions,EndpointRuleObject:()=>t3.EndpointRuleObject,ErrorRuleObject:()=>t3.ErrorRuleObject,EvaluateOptions:()=>t3.EvaluateOptions,Expression:()=>t3.Expression,FunctionArgv:()=>t3.FunctionArgv,FunctionObject:()=>t3.FunctionObject,FunctionReturn:()=>t3.FunctionReturn,ParameterObject:()=>t3.ParameterObject,ReferenceObject:()=>t3.ReferenceObject,ReferenceRecord:()=>t3.ReferenceRecord,RuleSetObject:()=>t3.RuleSetObject,RuleSetRules:()=>t3.RuleSetRules,TreeRuleObject:()=>t3.TreeRuleObject,awsEndpointFunctions:()=>kkA,getUserAgentPrefix:()=>AxQ,isIpAddress:()=>t3.isIpAddress,partition:()=>SkA,resolveEndpoint:()=>t3.resolveEndpoint,setPartitionInfo:()=>jkA,useDefaultPartitionInfo:()=>e_Q});ykA.exports=r_Q(MkA);var t3=$7(),RkA=rn((A,B=!1)=>{if(B){for(let Q of A.split("."))if(!RkA(Q))return!1;return!0}if(!t3.isValidHostLabel(A))return!1;if(A.length<3||A.length>63)return!1;if(A!==A.toLowerCase())return!1;if(t3.isIpAddress(A))return!1;return!0},"isVirtualHostableS3Bucket"),LkA=":",o_Q="/",t_Q=rn((A)=>{let B=A.split(LkA);if(B.length<6)return null;let[Q,D,Z,G,F,...I]=B;if(Q!=="arn"||D===""||Z===""||I.join(LkA)==="")return null;let Y=I.map((W)=>W.split(o_Q)).flat();return{partition:D,service:Z,region:G,accountId:F,resourceId:Y}},"parseArn"),OkA={partitions:[{id:"aws",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-east-1",name:"aws",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^(us|eu|ap|sa|ca|me|af|il|mx)\\-\\w+\\-\\d+$",regions:{"af-south-1":{description:"Africa (Cape Town)"},"ap-east-1":{description:"Asia Pacific (Hong Kong)"},"ap-east-2":{description:"Asia Pacific (Taipei)"},"ap-northeast-1":{description:"Asia Pacific (Tokyo)"},"ap-northeast-2":{description:"Asia Pacific (Seoul)"},"ap-northeast-3":{description:"Asia Pacific (Osaka)"},"ap-south-1":{description:"Asia Pacific (Mumbai)"},"ap-south-2":{description:"Asia Pacific (Hyderabad)"},"ap-southeast-1":{description:"Asia Pacific (Singapore)"},"ap-southeast-2":{description:"Asia Pacific (Sydney)"},"ap-southeast-3":{description:"Asia Pacific (Jakarta)"},"ap-southeast-4":{description:"Asia Pacific (Melbourne)"},"ap-southeast-5":{description:"Asia Pacific (Malaysia)"},"ap-southeast-7":{description:"Asia Pacific (Thailand)"},"aws-global":{description:"AWS Standard global region"},"ca-central-1":{description:"Canada (Central)"},"ca-west-1":{description:"Canada West (Calgary)"},"eu-central-1":{description:"Europe (Frankfurt)"},"eu-central-2":{description:"Europe (Zurich)"},"eu-north-1":{description:"Europe (Stockholm)"},"eu-south-1":{description:"Europe (Milan)"},"eu-south-2":{description:"Europe (Spain)"},"eu-west-1":{description:"Europe (Ireland)"},"eu-west-2":{description:"Europe (London)"},"eu-west-3":{description:"Europe (Paris)"},"il-central-1":{description:"Israel (Tel Aviv)"},"me-central-1":{description:"Middle East (UAE)"},"me-south-1":{description:"Middle East (Bahrain)"},"mx-central-1":{description:"Mexico (Central)"},"sa-east-1":{description:"South America (Sao Paulo)"},"us-east-1":{description:"US East (N. Virginia)"},"us-east-2":{description:"US East (Ohio)"},"us-west-1":{description:"US West (N. California)"},"us-west-2":{description:"US West (Oregon)"}}},{id:"aws-cn",outputs:{dnsSuffix:"amazonaws.com.cn",dualStackDnsSuffix:"api.amazonwebservices.com.cn",implicitGlobalRegion:"cn-northwest-1",name:"aws-cn",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^cn\\-\\w+\\-\\d+$",regions:{"aws-cn-global":{description:"AWS China global region"},"cn-north-1":{description:"China (Beijing)"},"cn-northwest-1":{description:"China (Ningxia)"}}},{id:"aws-us-gov",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-gov-west-1",name:"aws-us-gov",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^us\\-gov\\-\\w+\\-\\d+$",regions:{"aws-us-gov-global":{description:"AWS GovCloud (US) global region"},"us-gov-east-1":{description:"AWS GovCloud (US-East)"},"us-gov-west-1":{description:"AWS GovCloud (US-West)"}}},{id:"aws-iso",outputs:{dnsSuffix:"c2s.ic.gov",dualStackDnsSuffix:"c2s.ic.gov",implicitGlobalRegion:"us-iso-east-1",name:"aws-iso",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-iso\\-\\w+\\-\\d+$",regions:{"aws-iso-global":{description:"AWS ISO (US) global region"},"us-iso-east-1":{description:"US ISO East"},"us-iso-west-1":{description:"US ISO WEST"}}},{id:"aws-iso-b",outputs:{dnsSuffix:"sc2s.sgov.gov",dualStackDnsSuffix:"sc2s.sgov.gov",implicitGlobalRegion:"us-isob-east-1",name:"aws-iso-b",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isob\\-\\w+\\-\\d+$",regions:{"aws-iso-b-global":{description:"AWS ISOB (US) global region"},"us-isob-east-1":{description:"US ISOB East (Ohio)"}}},{id:"aws-iso-e",outputs:{dnsSuffix:"cloud.adc-e.uk",dualStackDnsSuffix:"cloud.adc-e.uk",implicitGlobalRegion:"eu-isoe-west-1",name:"aws-iso-e",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eu\\-isoe\\-\\w+\\-\\d+$",regions:{"aws-iso-e-global":{description:"AWS ISOE (Europe) global region"},"eu-isoe-west-1":{description:"EU ISOE West"}}},{id:"aws-iso-f",outputs:{dnsSuffix:"csp.hci.ic.gov",dualStackDnsSuffix:"csp.hci.ic.gov",implicitGlobalRegion:"us-isof-south-1",name:"aws-iso-f",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isof\\-\\w+\\-\\d+$",regions:{"aws-iso-f-global":{description:"AWS ISOF global region"},"us-isof-east-1":{description:"US ISOF EAST"},"us-isof-south-1":{description:"US ISOF SOUTH"}}},{id:"aws-eusc",outputs:{dnsSuffix:"amazonaws.eu",dualStackDnsSuffix:"amazonaws.eu",implicitGlobalRegion:"eusc-de-east-1",name:"aws-eusc",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eusc\\-(de)\\-\\w+\\-\\d+$",regions:{"eusc-de-east-1":{description:"EU (Germany)"}}}],version:"1.1"},TkA=OkA,PkA="",SkA=rn((A)=>{let{partitions:B}=TkA;for(let D of B){let{regions:Z,outputs:G}=D;for(let[F,I]of Object.entries(Z))if(F===A)return{...G,...I}}for(let D of B){let{regionRegex:Z,outputs:G}=D;if(new RegExp(Z).test(A))return{...G}}let Q=B.find((D)=>D.id==="aws");if(!Q)throw new Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");return{...Q.outputs}},"partition"),jkA=rn((A,B="")=>{TkA=A,PkA=B},"setPartitionInfo"),e_Q=rn(()=>{jkA(OkA,"")},"useDefaultPartitionInfo"),AxQ=rn(()=>PkA,"getUserAgentPrefix"),kkA={isVirtualHostableS3Bucket:RkA,parseArn:t_Q,partition:SkA};t3.customEndpointFunctions.aws=kkA});
var onA=E((snA)=>{Object.defineProperty(snA,"__esModule",{value:!0});snA.getRuntimeConfig=void 0;var G04=ph(),F04=G04.__importDefault(rB0()),pnA=XV(),inA=N$1(),g$1=V4(),I04=jG(),nnA=v4(),Yg=QD(),anA=S3(),Y04=kG(),W04=hZ(),J04=lnA(),X04=H8(),V04=yG(),C04=H8(),K04=(A)=>{C04.emitWarningIfUnsupportedVersion(process.version);let B=V04.resolveDefaultsModeConfig(A),Q=()=>B().then(X04.loadConfigsForDefaultMode),D=J04.getRuntimeConfig(A);pnA.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??Yg.loadConfig(pnA.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??Y04.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??inA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:F04.default.version}),maxAttempts:A?.maxAttempts??Yg.loadConfig(nnA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??Yg.loadConfig(g$1.NODE_REGION_CONFIG_OPTIONS,{...g$1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:anA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??Yg.loadConfig({...nnA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||W04.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??I04.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??anA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??Yg.loadConfig(g$1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??Yg.loadConfig(g$1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??Yg.loadConfig(inA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};snA.getRuntimeConfig=K04});
var p41=E((Z90)=>{Object.defineProperty(Z90,"__esModule",{value:!0});Z90.STSClient=Z90.__Client=void 0;var LpA=tw1(),WtQ=A$1(),JtQ=D$1(),MpA=l41(),XtQ=V4(),D90=VB(),VtQ=TG(),CtQ=q6(),RpA=v4(),TpA=H8();Object.defineProperty(Z90,"__Client",{enumerable:!0,get:function(){return TpA.Client}});var OpA=sB0(),KtQ=i41(),HtQ=FpA(),ztQ=NpA();class PpA extends TpA.Client{config;constructor(...[A]){let B=HtQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=KtQ.resolveClientEndpointParameters(B),D=MpA.resolveUserAgentConfig(Q),Z=RpA.resolveRetryConfig(D),G=XtQ.resolveRegionConfig(Z),F=LpA.resolveHostHeaderConfig(G),I=CtQ.resolveEndpointConfig(F),Y=OpA.resolveHttpAuthSchemeConfig(I),W=ztQ.resolveRuntimeExtensions(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(MpA.getUserAgentPlugin(this.config)),this.middlewareStack.use(RpA.getRetryPlugin(this.config)),this.middlewareStack.use(VtQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(LpA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(WtQ.getLoggerPlugin(this.config)),this.middlewareStack.use(JtQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(D90.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:OpA.defaultSTSHttpAuthSchemeParametersProvider,identityProviderConfigProvider:async(J)=>new D90.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials})})),this.middlewareStack.use(D90.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}}Z90.STSClient=PpA});
var p90=E((UnA)=>{Object.defineProperty(UnA,"__esModule",{value:!0});UnA.resolveHttpAuthSchemeConfig=UnA.defaultSSOOIDCHttpAuthSchemeProvider=UnA.defaultSSOOIDCHttpAuthSchemeParametersProvider=void 0;var g14=XV(),l90=I5(),u14=async(A,B,Q)=>{return{operation:l90.getSmithyContext(B).operation,region:await l90.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};UnA.defaultSSOOIDCHttpAuthSchemeParametersProvider=u14;function m14(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sso-oauth",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function d14(A){return{schemeId:"smithy.api#noAuth"}}var c14=(A)=>{let B=[];switch(A.operation){case"CreateToken":{B.push(d14(A));break}default:B.push(m14(A))}return B};UnA.defaultSSOOIDCHttpAuthSchemeProvider=c14;var l14=(A)=>{let B=g14.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:l90.normalizeProvider(A.authSchemePreference??[])})};UnA.resolveHttpAuthSchemeConfig=l14});
var paA=E((caA)=>{Object.defineProperty(caA,"__esModule",{value:!0});caA.fromTokenFile=void 0;var SA4=Mz(),jA4=eB(),kA4=J1("fs"),yA4=AQ0(),daA="AWS_WEB_IDENTITY_TOKEN_FILE",_A4="AWS_ROLE_ARN",xA4="AWS_ROLE_SESSION_NAME",vA4=(A={})=>async()=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromTokenFile");let B=A?.webIdentityTokenFile??process.env[daA],Q=A?.roleArn??process.env[_A4],D=A?.roleSessionName??process.env[xA4];if(!B||!Q)throw new jA4.CredentialsProviderError("Web identity configuration not specified",{logger:A.logger});let Z=await yA4.fromWebToken({...A,webIdentityToken:kA4.readFileSync(B,{encoding:"ascii"}),roleArn:Q,roleSessionName:D})();if(B===process.env[daA])SA4.setCredentialFeature(Z,"CREDENTIALS_ENV_VARS_STS_WEB_ID_TOKEN","h");return Z};caA.fromTokenFile=vA4});
var ph=E((qG5,xU1)=>{var $jA,qjA,NjA,LjA,MjA,RjA,OjA,TjA,PjA,SjA,jjA,kjA,yjA,yU1,u00,_jA,xjA,vjA,sn,bjA,fjA,hjA,gjA,ujA,mjA,djA,cjA,ljA,_U1,pjA,ijA,njA;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof xU1==="object"&&typeof qG5==="object")A(Q(B,Q(qG5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};$jA=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},qjA=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},NjA=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},LjA=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},MjA=function(G,F){return function(I,Y){F(I,Y,G)}},RjA=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},OjA=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},TjA=function(G){return typeof G==="symbol"?G:"".concat(G)},PjA=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},SjA=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},jjA=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},kjA=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},yjA=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))_U1(F,G,I)},_U1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},yU1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},u00=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},_jA=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(u00(arguments[F]));return G},xjA=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},vjA=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},sn=function(G){return this instanceof sn?(this.v=G,this):new sn(G)},bjA=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof sn?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},fjA=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:sn(G[W](X)),done:!1}:J?J(X):X}:J}},hjA=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof yU1==="function"?yU1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},gjA=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};ujA=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")_U1(F,G,I[Y])}return Q(F,G),F},mjA=function(G){return G&&G.__esModule?G:{default:G}},djA=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},cjA=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},ljA=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},pjA=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};ijA=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},njA=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",$jA),A("__assign",qjA),A("__rest",NjA),A("__decorate",LjA),A("__param",MjA),A("__esDecorate",RjA),A("__runInitializers",OjA),A("__propKey",TjA),A("__setFunctionName",PjA),A("__metadata",SjA),A("__awaiter",jjA),A("__generator",kjA),A("__exportStar",yjA),A("__createBinding",_U1),A("__values",yU1),A("__read",u00),A("__spread",_jA),A("__spreadArrays",xjA),A("__spreadArray",vjA),A("__await",sn),A("__asyncGenerator",bjA),A("__asyncDelegator",fjA),A("__asyncValues",hjA),A("__makeTemplateObject",gjA),A("__importStar",ujA),A("__importDefault",mjA),A("__classPrivateFieldGet",djA),A("__classPrivateFieldSet",cjA),A("__classPrivateFieldIn",ljA),A("__addDisposableResource",pjA),A("__disposeResources",ijA),A("__rewriteRelativeImportExtension",njA)})});
var q41=E((TF5,SxA)=>{var{defineProperty:Hw1,getOwnPropertyDescriptor:ofQ,getOwnPropertyNames:tfQ}=Object,efQ=Object.prototype.hasOwnProperty,Kw1=(A,B)=>Hw1(A,"name",{value:B,configurable:!0}),AhQ=(A,B)=>{for(var Q in B)Hw1(A,Q,{get:B[Q],enumerable:!0})},BhQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of tfQ(B))if(!efQ.call(A,Z)&&Z!==Q)Hw1(A,Z,{get:()=>B[Z],enumerable:!(D=ofQ(B,Z))||D.enumerable})}return A},QhQ=(A)=>BhQ(Hw1({},"__esModule",{value:!0}),A),MxA={};AhQ(MxA,{NODE_APP_ID_CONFIG_OPTIONS:()=>IhQ,UA_APP_ID_ENV_NAME:()=>TxA,UA_APP_ID_INI_NAME:()=>PxA,createDefaultUserAgentProvider:()=>OxA,crtAvailability:()=>RxA,defaultUserAgent:()=>ZhQ});SxA.exports=QhQ(MxA);var LxA=J1("os"),bA0=J1("process"),RxA={isCrtAvailable:!1},DhQ=Kw1(()=>{if(RxA.isCrtAvailable)return["md/crt-avail"];return null},"isCrtAvailable"),OxA=Kw1(({serviceId:A,clientVersion:B})=>{return async(Q)=>{let D=[["aws-sdk-js",B],["ua","2.1"],[`os/${LxA.platform()}`,LxA.release()],["lang/js"],["md/nodejs",`${bA0.versions.node}`]],Z=DhQ();if(Z)D.push(Z);if(A)D.push([`api/${A}`,B]);if(bA0.env.AWS_EXECUTION_ENV)D.push([`exec-env/${bA0.env.AWS_EXECUTION_ENV}`]);let G=await Q?.userAgentAppId?.();return G?[...D,[`app/${G}`]]:[...D]}},"createDefaultUserAgentProvider"),ZhQ=OxA,GhQ=Da(),TxA="AWS_SDK_UA_APP_ID",PxA="sdk_ua_app_id",FhQ="sdk-ua-app-id",IhQ={environmentVariableSelector:Kw1((A)=>A[TxA],"environmentVariableSelector"),configFileSelector:Kw1((A)=>A[PxA]??A[FhQ],"configFileSelector"),default:GhQ.DEFAULT_UA_APP_ID}});
var qbA=E((wbA)=>{Object.defineProperty(wbA,"__esModule",{value:!0});wbA.getRuntimeConfig=void 0;var mgQ=ih(),dgQ=mgQ.__importDefault(lA0()),HbA=II(),zbA=q41(),Rw1=V4(),cgQ=jG(),EbA=v4(),oh=QD(),UbA=S3(),lgQ=kG(),pgQ=hZ(),igQ=KbA(),ngQ=W6(),agQ=yG(),sgQ=W6(),rgQ=(A)=>{sgQ.emitWarningIfUnsupportedVersion(process.version);let B=agQ.resolveDefaultsModeConfig(A),Q=()=>B().then(ngQ.loadConfigsForDefaultMode),D=igQ.getRuntimeConfig(A);HbA.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??oh.loadConfig(HbA.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??lgQ.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??zbA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:dgQ.default.version}),maxAttempts:A?.maxAttempts??oh.loadConfig(EbA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??oh.loadConfig(Rw1.NODE_REGION_CONFIG_OPTIONS,{...Rw1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:UbA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??oh.loadConfig({...EbA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||pgQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??cgQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??UbA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??oh.loadConfig(Rw1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??oh.loadConfig(Rw1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??oh.loadConfig(zbA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};wbA.getRuntimeConfig=rgQ});
var rA0=E((iF5,abA)=>{var{defineProperty:Ow1,getOwnPropertyDescriptor:ogQ,getOwnPropertyNames:tgQ}=Object,egQ=Object.prototype.hasOwnProperty,p4=(A,B)=>Ow1(A,"name",{value:B,configurable:!0}),AuQ=(A,B)=>{for(var Q in B)Ow1(A,Q,{get:B[Q],enumerable:!0})},BuQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of tgQ(B))if(!egQ.call(A,Z)&&Z!==Q)Ow1(A,Z,{get:()=>B[Z],enumerable:!(D=ogQ(B,Z))||D.enumerable})}return A},QuQ=(A)=>BuQ(Ow1({},"__esModule",{value:!0}),A),SbA={};AuQ(SbA,{$Command:()=>ybA.Command,AccessDeniedException:()=>_bA,AuthorizationPendingException:()=>xbA,CreateTokenCommand:()=>ibA,CreateTokenRequestFilterSensitiveLog:()=>vbA,CreateTokenResponseFilterSensitiveLog:()=>bbA,ExpiredTokenException:()=>fbA,InternalServerException:()=>hbA,InvalidClientException:()=>gbA,InvalidGrantException:()=>ubA,InvalidRequestException:()=>mbA,InvalidScopeException:()=>dbA,SSOOIDC:()=>nbA,SSOOIDCClient:()=>kbA,SSOOIDCServiceException:()=>tC,SlowDownException:()=>cbA,UnauthorizedClientException:()=>lbA,UnsupportedGrantTypeException:()=>pbA,__Client:()=>jbA.Client});abA.exports=QuQ(SbA);var NbA=C41(),DuQ=K41(),ZuQ=H41(),LbA=Da(),GuQ=V4(),aA0=VB(),FuQ=TG(),IuQ=q6(),MbA=v4(),jbA=W6(),RbA=cA0(),YuQ=p4((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"sso-oauth"})},"resolveClientEndpointParameters"),WuQ={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},JuQ=qbA(),ObA=L41(),TbA=JV(),PbA=W6(),XuQ=p4((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),VuQ=p4((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),CuQ=p4((A,B)=>{let Q=Object.assign(ObA.getAwsRegionExtensionConfiguration(A),PbA.getDefaultExtensionConfiguration(A),TbA.getHttpHandlerExtensionConfiguration(A),XuQ(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,ObA.resolveAwsRegionExtensionConfiguration(Q),PbA.resolveDefaultRuntimeConfig(Q),TbA.resolveHttpHandlerRuntimeConfig(Q),VuQ(Q))},"resolveRuntimeExtensions"),kbA=class extends jbA.Client{static{p4(this,"SSOOIDCClient")}config;constructor(...[A]){let B=JuQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=YuQ(B),D=LbA.resolveUserAgentConfig(Q),Z=MbA.resolveRetryConfig(D),G=GuQ.resolveRegionConfig(Z),F=NbA.resolveHostHeaderConfig(G),I=IuQ.resolveEndpointConfig(F),Y=RbA.resolveHttpAuthSchemeConfig(I),W=CuQ(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(LbA.getUserAgentPlugin(this.config)),this.middlewareStack.use(MbA.getRetryPlugin(this.config)),this.middlewareStack.use(FuQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(NbA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(DuQ.getLoggerPlugin(this.config)),this.middlewareStack.use(ZuQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(aA0.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:RbA.defaultSSOOIDCHttpAuthSchemeParametersProvider,identityProviderConfigProvider:p4(async(J)=>new aA0.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(aA0.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},KuQ=W6(),HuQ=q6(),zuQ=T3(),ybA=W6(),Xa=W6(),EuQ=W6(),tC=class A extends EuQ.ServiceException{static{p4(this,"SSOOIDCServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},_bA=class A extends tC{static{p4(this,"AccessDeniedException")}name="AccessDeniedException";$fault="client";error;error_description;constructor(B){super({name:"AccessDeniedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},xbA=class A extends tC{static{p4(this,"AuthorizationPendingException")}name="AuthorizationPendingException";$fault="client";error;error_description;constructor(B){super({name:"AuthorizationPendingException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},vbA=p4((A)=>({...A,...A.clientSecret&&{clientSecret:Xa.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:Xa.SENSITIVE_STRING},...A.codeVerifier&&{codeVerifier:Xa.SENSITIVE_STRING}}),"CreateTokenRequestFilterSensitiveLog"),bbA=p4((A)=>({...A,...A.accessToken&&{accessToken:Xa.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:Xa.SENSITIVE_STRING},...A.idToken&&{idToken:Xa.SENSITIVE_STRING}}),"CreateTokenResponseFilterSensitiveLog"),fbA=class A extends tC{static{p4(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";error;error_description;constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},hbA=class A extends tC{static{p4(this,"InternalServerException")}name="InternalServerException";$fault="server";error;error_description;constructor(B){super({name:"InternalServerException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},gbA=class A extends tC{static{p4(this,"InvalidClientException")}name="InvalidClientException";$fault="client";error;error_description;constructor(B){super({name:"InvalidClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},ubA=class A extends tC{static{p4(this,"InvalidGrantException")}name="InvalidGrantException";$fault="client";error;error_description;constructor(B){super({name:"InvalidGrantException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},mbA=class A extends tC{static{p4(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";error;error_description;constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},dbA=class A extends tC{static{p4(this,"InvalidScopeException")}name="InvalidScopeException";$fault="client";error;error_description;constructor(B){super({name:"InvalidScopeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},cbA=class A extends tC{static{p4(this,"SlowDownException")}name="SlowDownException";$fault="client";error;error_description;constructor(B){super({name:"SlowDownException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},lbA=class A extends tC{static{p4(this,"UnauthorizedClientException")}name="UnauthorizedClientException";$fault="client";error;error_description;constructor(B){super({name:"UnauthorizedClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},pbA=class A extends tC{static{p4(this,"UnsupportedGrantTypeException")}name="UnsupportedGrantTypeException";$fault="client";error;error_description;constructor(B){super({name:"UnsupportedGrantTypeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},sA0=II(),UuQ=VB(),EB=W6(),wuQ=p4(async(A,B)=>{let Q=UuQ.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/token");let Z;return Z=JSON.stringify(EB.take(A,{clientId:[],clientSecret:[],code:[],codeVerifier:[],deviceCode:[],grantType:[],redirectUri:[],refreshToken:[],scope:p4((G)=>EB._json(G),"scope")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateTokenCommand"),$uQ=p4(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return quQ(A,B);let Q=EB.map({$metadata:Lz(A)}),D=EB.expectNonNull(EB.expectObject(await sA0.parseJsonBody(A.body,B)),"body"),Z=EB.take(D,{accessToken:EB.expectString,expiresIn:EB.expectInt32,idToken:EB.expectString,refreshToken:EB.expectString,tokenType:EB.expectString});return Object.assign(Q,Z),Q},"de_CreateTokenCommand"),quQ=p4(async(A,B)=>{let Q={...A,body:await sA0.parseJsonErrorBody(A.body,B)},D=sA0.loadRestJsonErrorCode(A,Q.body);switch(D){case"AccessDeniedException":case"com.amazonaws.ssooidc#AccessDeniedException":throw await LuQ(Q,B);case"AuthorizationPendingException":case"com.amazonaws.ssooidc#AuthorizationPendingException":throw await MuQ(Q,B);case"ExpiredTokenException":case"com.amazonaws.ssooidc#ExpiredTokenException":throw await RuQ(Q,B);case"InternalServerException":case"com.amazonaws.ssooidc#InternalServerException":throw await OuQ(Q,B);case"InvalidClientException":case"com.amazonaws.ssooidc#InvalidClientException":throw await TuQ(Q,B);case"InvalidGrantException":case"com.amazonaws.ssooidc#InvalidGrantException":throw await PuQ(Q,B);case"InvalidRequestException":case"com.amazonaws.ssooidc#InvalidRequestException":throw await SuQ(Q,B);case"InvalidScopeException":case"com.amazonaws.ssooidc#InvalidScopeException":throw await juQ(Q,B);case"SlowDownException":case"com.amazonaws.ssooidc#SlowDownException":throw await kuQ(Q,B);case"UnauthorizedClientException":case"com.amazonaws.ssooidc#UnauthorizedClientException":throw await yuQ(Q,B);case"UnsupportedGrantTypeException":case"com.amazonaws.ssooidc#UnsupportedGrantTypeException":throw await _uQ(Q,B);default:let Z=Q.body;return NuQ({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),NuQ=EB.withBaseException(tC),LuQ=p4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new _bA({$metadata:Lz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_AccessDeniedExceptionRes"),MuQ=p4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new xbA({$metadata:Lz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_AuthorizationPendingExceptionRes"),RuQ=p4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new fbA({$metadata:Lz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_ExpiredTokenExceptionRes"),OuQ=p4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new hbA({$metadata:Lz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_InternalServerExceptionRes"),TuQ=p4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new gbA({$metadata:Lz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_InvalidClientExceptionRes"),PuQ=p4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new ubA({$metadata:Lz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_InvalidGrantExceptionRes"),SuQ=p4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new mbA({$metadata:Lz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),juQ=p4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new dbA({$metadata:Lz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_InvalidScopeExceptionRes"),kuQ=p4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new cbA({$metadata:Lz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_SlowDownExceptionRes"),yuQ=p4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new lbA({$metadata:Lz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_UnauthorizedClientExceptionRes"),_uQ=p4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new pbA({$metadata:Lz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_UnsupportedGrantTypeExceptionRes"),Lz=p4((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),ibA=class extends ybA.Command.classBuilder().ep(WuQ).m(function(A,B,Q,D){return[zuQ.getSerdePlugin(Q,this.serialize,this.deserialize),HuQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSSOOIDCService","CreateToken",{}).n("SSOOIDCClient","CreateTokenCommand").f(vbA,bbA).ser(wuQ).de($uQ).build(){static{p4(this,"CreateTokenCommand")}},xuQ={CreateTokenCommand:ibA},nbA=class extends kbA{static{p4(this,"SSOOIDC")}};KuQ.createAggregatedClient(xuQ,nbA)});
var rB0=E((QW5,CoQ)=>{CoQ.exports={name:"@aws-sdk/nested-clients",version:"3.840.0",description:"Nested clients for AWS SDK packages.",main:"./dist-cjs/index.js",module:"./dist-es/index.js",types:"./dist-types/index.d.ts",scripts:{build:"yarn lint && concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline nested-clients","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo",lint:"node ../../scripts/validation/submodules-linter.js --pkg nested-clients",test:"yarn g:vitest run","test:watch":"yarn g:vitest watch"},engines:{node:">=18.0.0"},author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["./sso-oidc.d.ts","./sso-oidc.js","./sts.d.ts","./sts.js","dist-*/**"],browser:{"./dist-es/submodules/sso-oidc/runtimeConfig":"./dist-es/submodules/sso-oidc/runtimeConfig.browser","./dist-es/submodules/sts/runtimeConfig":"./dist-es/submodules/sts/runtimeConfig.browser"},"react-native":{},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/packages/nested-clients",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"packages/nested-clients"},exports:{"./sso-oidc":{types:"./dist-types/submodules/sso-oidc/index.d.ts",module:"./dist-es/submodules/sso-oidc/index.js",node:"./dist-cjs/submodules/sso-oidc/index.js",import:"./dist-es/submodules/sso-oidc/index.js",require:"./dist-cjs/submodules/sso-oidc/index.js"},"./sts":{types:"./dist-types/submodules/sts/index.d.ts",module:"./dist-es/submodules/sts/index.js",node:"./dist-cjs/submodules/sts/index.js",import:"./dist-es/submodules/sts/index.js",require:"./dist-cjs/submodules/sts/index.js"}}}});
var rgA=E((agA)=>{Object.defineProperty(agA,"__esModule",{value:!0});agA.getRuntimeConfig=void 0;var HlQ=ih(),zlQ=HlQ.__importDefault(t_A()),lgA=II(),ElQ=UgA(),pgA=q41(),vw1=V4(),UlQ=jG(),igA=v4(),Bg=QD(),ngA=S3(),wlQ=kG(),$lQ=hZ(),qlQ=cgA(),NlQ=W6(),LlQ=yG(),MlQ=W6(),RlQ=(A)=>{MlQ.emitWarningIfUnsupportedVersion(process.version);let B=LlQ.resolveDefaultsModeConfig(A),Q=()=>B().then(NlQ.loadConfigsForDefaultMode),D=qlQ.getRuntimeConfig(A);lgA.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??Bg.loadConfig(lgA.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??wlQ.calculateBodyLength,credentialDefaultProvider:A?.credentialDefaultProvider??ElQ.defaultProvider,defaultUserAgentProvider:A?.defaultUserAgentProvider??pgA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:zlQ.default.version}),maxAttempts:A?.maxAttempts??Bg.loadConfig(igA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??Bg.loadConfig(vw1.NODE_REGION_CONFIG_OPTIONS,{...vw1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:ngA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??Bg.loadConfig({...igA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||$lQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??UlQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??ngA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??Bg.loadConfig(vw1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??Bg.loadConfig(vw1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??Bg.loadConfig(pgA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};agA.getRuntimeConfig=RlQ});
var sB0=E((XlA)=>{Object.defineProperty(XlA,"__esModule",{value:!0});XlA.resolveHttpAuthSchemeConfig=XlA.resolveStsAuthConfig=XlA.defaultSTSHttpAuthSchemeProvider=XlA.defaultSTSHttpAuthSchemeParametersProvider=void 0;var BoQ=XV(),aB0=I5(),QoQ=p41(),DoQ=async(A,B,Q)=>{return{operation:aB0.getSmithyContext(B).operation,region:await aB0.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};XlA.defaultSTSHttpAuthSchemeParametersProvider=DoQ;function ZoQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function GoQ(A){return{schemeId:"smithy.api#noAuth"}}var FoQ=(A)=>{let B=[];switch(A.operation){case"AssumeRoleWithWebIdentity":{B.push(GoQ(A));break}default:B.push(ZoQ(A))}return B};XlA.defaultSTSHttpAuthSchemeProvider=FoQ;var IoQ=(A)=>Object.assign(A,{stsClientCtor:QoQ.STSClient});XlA.resolveStsAuthConfig=IoQ;var YoQ=(A)=>{let B=XlA.resolveStsAuthConfig(A),Q=BoQ.resolveAwsSdkSigV4Config(B);return Object.assign(Q,{authSchemePreference:aB0.normalizeProvider(A.authSchemePreference??[])})};XlA.resolveHttpAuthSchemeConfig=YoQ});
var sjA=E((ajA)=>{Object.defineProperty(ajA,"__esModule",{value:!0});ajA.propertyProviderChain=ajA.createCredentialChain=void 0;var nyQ=eB(),ayQ=(...A)=>{let B=-1,D=Object.assign(async(Z)=>{let G=await ajA.propertyProviderChain(...A)(Z);if(!G.expiration&&B!==-1)G.expiration=new Date(Date.now()+B);return G},{expireAfter(Z){if(Z<300000)throw new Error("@aws-sdk/credential-providers - createCredentialChain(...).expireAfter(ms) may not be called with a duration lower than five minutes.");return B=Z,D}});return D};ajA.createCredentialChain=ayQ;var syQ=(...A)=>async(B)=>{if(A.length===0)throw new nyQ.ProviderError("No providers in chain");let Q;for(let D of A)try{return await D(B)}catch(Z){if(Q=Z,Z?.tryNextLink)continue;throw Z}throw Q};ajA.propertyProviderChain=syQ});
var slA=E((nlA)=>{Object.defineProperty(nlA,"__esModule",{value:!0});nlA.defaultEndpointResolver=void 0;var OoQ=h41(),B90=$7(),ToQ=ilA(),PoQ=new B90.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),SoQ=(A,B={})=>{return PoQ.get(A,()=>B90.resolveEndpoint(ToQ.ruleSet,{endpointParams:A,logger:B.logger}))};nlA.defaultEndpointResolver=SoQ;B90.customEndpointFunctions.aws=OoQ.awsEndpointFunctions});
var smA=E((nmA)=>{Object.defineProperty(nmA,"__esModule",{value:!0});nmA.retryWrapper=void 0;var xnQ=(A,B,Q)=>{return async()=>{for(let D=0;D<B;++D)try{return await A()}catch(Z){await new Promise((G)=>setTimeout(G,Q))}return await A()}};nmA.retryWrapper=xnQ});
var sw1=E((EY5,YdA)=>{var{defineProperty:aw1,getOwnPropertyDescriptor:snQ,getOwnPropertyNames:rnQ}=Object,onQ=Object.prototype.hasOwnProperty,tnQ=(A,B)=>aw1(A,"name",{value:B,configurable:!0}),enQ=(A,B)=>{for(var Q in B)aw1(A,Q,{get:B[Q],enumerable:!0})},AaQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of rnQ(B))if(!onQ.call(A,Z)&&Z!==Q)aw1(A,Z,{get:()=>B[Z],enumerable:!(D=snQ(B,Z))||D.enumerable})}return A},BaQ=(A)=>AaQ(aw1({},"__esModule",{value:!0}),A),BdA={};enQ(BdA,{ENV_ACCOUNT_ID:()=>IdA,ENV_CREDENTIAL_SCOPE:()=>FdA,ENV_EXPIRATION:()=>GdA,ENV_KEY:()=>QdA,ENV_SECRET:()=>DdA,ENV_SESSION:()=>ZdA,fromEnv:()=>ZaQ});YdA.exports=BaQ(BdA);var QaQ=Mz(),DaQ=eB(),QdA="AWS_ACCESS_KEY_ID",DdA="AWS_SECRET_ACCESS_KEY",ZdA="AWS_SESSION_TOKEN",GdA="AWS_CREDENTIAL_EXPIRATION",FdA="AWS_CREDENTIAL_SCOPE",IdA="AWS_ACCOUNT_ID",ZaQ=tnQ((A)=>async()=>{A?.logger?.debug("@aws-sdk/credential-provider-env - fromEnv");let B=process.env[QdA],Q=process.env[DdA],D=process.env[ZdA],Z=process.env[GdA],G=process.env[FdA],F=process.env[IdA];if(B&&Q){let I={accessKeyId:B,secretAccessKey:Q,...D&&{sessionToken:D},...Z&&{expiration:new Date(Z)},...G&&{credentialScope:G},...F&&{accountId:F}};return QaQ.setCredentialFeature(I,"CREDENTIALS_ENV_VARS","g"),I}throw new DaQ.CredentialsProviderError("Unable to find environment variable credentials.",{logger:A?.logger})},"fromEnv")});
var sxA=E((nxA)=>{Object.defineProperty(nxA,"__esModule",{value:!0});nxA.defaultEndpointResolver=void 0;var WhQ=on(),gA0=$7(),JhQ=ixA(),XhQ=new gA0.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),VhQ=(A,B={})=>{return XhQ.get(A,()=>gA0.resolveEndpoint(JhQ.ruleSet,{endpointParams:A,logger:B.logger}))};nxA.defaultEndpointResolver=VhQ;gA0.customEndpointFunctions.aws=WhQ.awsEndpointFunctions});
var t_A=E((UF5,GfQ)=>{GfQ.exports={name:"@aws-sdk/client-cognito-identity",description:"AWS SDK for JavaScript Cognito Identity Client for Node.js, Browser and React Native",version:"3.840.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-cognito-identity","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo cognito-identity","test:e2e":"yarn g:vitest run -c vitest.config.e2e.ts --mode development","test:e2e:watch":"yarn g:vitest watch -c vitest.config.e2e.ts"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/credential-provider-node":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{"@aws-sdk/client-iam":"3.840.0","@tsconfig/node18":"18.2.4","@types/chai":"^4.2.11","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-cognito-identity",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-cognito-identity"}}});
var tcA=E((vY5,ocA)=>{var{defineProperty:E$1,getOwnPropertyDescriptor:ArQ,getOwnPropertyNames:BrQ}=Object,QrQ=Object.prototype.hasOwnProperty,t6=(A,B)=>E$1(A,"name",{value:B,configurable:!0}),DrQ=(A,B)=>{for(var Q in B)E$1(A,Q,{get:B[Q],enumerable:!0})},ZrQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of BrQ(B))if(!QrQ.call(A,Z)&&Z!==Q)E$1(A,Z,{get:()=>B[Z],enumerable:!(D=ArQ(B,Z))||D.enumerable})}return A},GrQ=(A)=>ZrQ(E$1({},"__esModule",{value:!0}),A),mcA={};DrQ(mcA,{AwsEc2QueryProtocol:()=>xrQ,AwsJson1_0Protocol:()=>wrQ,AwsJson1_1Protocol:()=>$rQ,AwsJsonRpcProtocol:()=>cB0,AwsQueryProtocol:()=>icA,AwsRestJsonProtocol:()=>NrQ,AwsRestXmlProtocol:()=>urQ,JsonCodec:()=>dB0,JsonShapeDeserializer:()=>lcA,JsonShapeSerializer:()=>pcA,XmlCodec:()=>rcA,XmlShapeDeserializer:()=>lB0,XmlShapeSerializer:()=>scA,_toBool:()=>IrQ,_toNum:()=>YrQ,_toStr:()=>FrQ,awsExpectUnion:()=>MrQ,loadRestJsonErrorCode:()=>mB0,loadRestXmlErrorCode:()=>acA,parseJsonBody:()=>uB0,parseJsonErrorBody:()=>KrQ,parseXmlBody:()=>ncA,parseXmlErrorBody:()=>hrQ});ocA.exports=GrQ(mcA);var FrQ=t6((A)=>{if(A==null)return A;if(typeof A==="number"||typeof A==="bigint"){let B=new Error(`Received number ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}if(typeof A==="boolean"){let B=new Error(`Received boolean ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}return A},"_toStr"),IrQ=t6((A)=>{if(A==null)return A;if(typeof A==="string"){let B=A.toLowerCase();if(A!==""&&B!=="false"&&B!=="true"){let Q=new Error(`Received string "${A}" where a boolean was expected.`);Q.name="Warning",console.warn(Q)}return A!==""&&B!=="false"}return A},"_toBool"),YrQ=t6((A)=>{if(A==null)return A;if(typeof A==="string"){let B=Number(A);if(B.toString()!==A){let Q=new Error(`Received string "${A}" where a number was expected.`);return Q.name="Warning",console.warn(Q),A}return B}return A},"_toNum"),WrQ=$6(),Oa=jQ(),JrQ=wY(),Gg=class{static{t6(this,"SerdeContextConfig")}serdeContext;setSerdeContext(A){this.serdeContext=A}},u41=jQ(),Ta=Y6(),XrQ=Dg(),VrQ=Y6();function dcA(A,B,Q){if(Q?.source){let D=Q.source;if(typeof B==="number"){if(B>Number.MAX_SAFE_INTEGER||B<Number.MIN_SAFE_INTEGER||D!==String(B))if(D.includes("."))return new VrQ.NumericValue(D,"bigDecimal");else return BigInt(D)}}return B}t6(dcA,"jsonReviver");var CrQ=H8(),ccA=t6((A,B)=>CrQ.collectBody(A,B).then((Q)=>B.utf8Encoder(Q)),"collectBodyString"),uB0=t6((A,B)=>ccA(A,B).then((Q)=>{if(Q.length)try{return JSON.parse(Q)}catch(D){if(D?.name==="SyntaxError")Object.defineProperty(D,"$responseBodyText",{value:Q});throw D}return{}}),"parseJsonBody"),KrQ=t6(async(A,B)=>{let Q=await uB0(A,B);return Q.message=Q.message??Q.Message,Q},"parseJsonErrorBody"),mB0=t6((A,B)=>{let Q=t6((G,F)=>Object.keys(G).find((I)=>I.toLowerCase()===F.toLowerCase()),"findKey"),D=t6((G)=>{let F=G;if(typeof F==="number")F=F.toString();if(F.indexOf(",")>=0)F=F.split(",")[0];if(F.indexOf(":")>=0)F=F.split(":")[0];if(F.indexOf("#")>=0)F=F.split("#")[1];return F},"sanitizeErrorCode"),Z=Q(A.headers,"x-amzn-errortype");if(Z!==void 0)return D(A.headers[Z]);if(B&&typeof B==="object"){let G=Q(B,"code");if(G&&B[G]!==void 0)return D(B[G]);if(B.__type!==void 0)return D(B.__type)}},"loadRestJsonErrorCode"),lcA=class extends Gg{constructor(A){super();this.settings=A}static{t6(this,"JsonShapeDeserializer")}async read(A,B){return this._read(A,typeof B==="string"?JSON.parse(B,dcA):await uB0(B,this.serdeContext))}readObject(A,B){return this._read(A,B)}_read(A,B){let Q=B!==null&&typeof B==="object",D=u41.NormalizedSchema.of(A);if(D.isListSchema()&&Array.isArray(B)){let G=D.getValueSchema(),F=[],I=!!D.getMergedTraits().sparse;for(let Y of B)if(I||Y!=null)F.push(this._read(G,Y));return F}else if(D.isMapSchema()&&Q){let G=D.getValueSchema(),F={},I=!!D.getMergedTraits().sparse;for(let[Y,W]of Object.entries(B))if(I||W!=null)F[Y]=this._read(G,W);return F}else if(D.isStructSchema()&&Q){let G={};for(let[F,I]of D.structIterator()){let Y=this.settings.jsonName?I.getMergedTraits().jsonName??F:F,W=this._read(I,B[Y]);if(W!=null)G[F]=W}return G}if(D.isBlobSchema()&&typeof B==="string")return XrQ.fromBase64(B);let Z=D.getMergedTraits().mediaType;if(D.isStringSchema()&&typeof B==="string"&&Z){if(Z==="application/json"||Z.endsWith("+json"))return Ta.LazyJsonString.from(B)}if(D.isTimestampSchema()){let G=this.settings.timestampFormat;switch(G.useTrait?D.getSchema()===u41.SCHEMA.TIMESTAMP_DEFAULT?G.default:D.getSchema()??G.default:G.default){case u41.SCHEMA.TIMESTAMP_DATE_TIME:return Ta.parseRfc3339DateTimeWithOffset(B);case u41.SCHEMA.TIMESTAMP_HTTP_DATE:return Ta.parseRfc7231DateTime(B);case u41.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return Ta.parseEpochTimestamp(B);default:return console.warn("Missing timestamp format, parsing value with Date constructor:",B),new Date(B)}}if(D.isBigIntegerSchema()&&(typeof B==="number"||typeof B==="string"))return BigInt(B);if(D.isBigDecimalSchema()&&B!=null){if(B instanceof Ta.NumericValue)return B;return new Ta.NumericValue(String(B),"bigDecimal")}if(D.isNumericSchema()&&typeof B==="string")switch(B){case"Infinity":return 1/0;case"-Infinity":return-1/0;case"NaN":return NaN}return B}},Pa=jQ(),HrQ=Y6(),zrQ=Y6(),ErQ=Y6(),fcA=String.fromCharCode(925),UrQ=class{static{t6(this,"JsonReplacer")}values=new Map;counter=0;stage=0;createReplacer(){if(this.stage===1)throw new Error("@aws-sdk/core/protocols - JsonReplacer already created.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");return this.stage=1,(A,B)=>{if(B instanceof ErQ.NumericValue){let Q=`${fcA+NaN+this.counter++}_`+B.string;return this.values.set(`"${Q}"`,B.string),Q}if(typeof B==="bigint"){let Q=B.toString(),D=`${fcA+"b"+this.counter++}_`+Q;return this.values.set(`"${D}"`,Q),D}return B}}replaceInJson(A){if(this.stage===0)throw new Error("@aws-sdk/core/protocols - JsonReplacer not created yet.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");if(this.stage=2,this.counter===0)return A;for(let[B,Q]of this.values)A=A.replace(B,Q);return A}},pcA=class extends Gg{constructor(A){super();this.settings=A}static{t6(this,"JsonShapeSerializer")}buffer;rootSchema;write(A,B){this.rootSchema=Pa.NormalizedSchema.of(A),this.buffer=this._write(this.rootSchema,B)}flush(){if(this.rootSchema?.isStructSchema()||this.rootSchema?.isDocumentSchema()){let A=new UrQ;return A.replaceInJson(JSON.stringify(this.buffer,A.createReplacer(),0))}return this.buffer}_write(A,B,Q){let D=B!==null&&typeof B==="object",Z=Pa.NormalizedSchema.of(A);if(Z.isListSchema()&&Array.isArray(B)){let F=Z.getValueSchema(),I=[],Y=!!Z.getMergedTraits().sparse;for(let W of B)if(Y||W!=null)I.push(this._write(F,W));return I}else if(Z.isMapSchema()&&D){let F=Z.getValueSchema(),I={},Y=!!Z.getMergedTraits().sparse;for(let[W,J]of Object.entries(B))if(Y||J!=null)I[W]=this._write(F,J);return I}else if(Z.isStructSchema()&&D){let F={};for(let[I,Y]of Z.structIterator()){let W=this.settings.jsonName?Y.getMergedTraits().jsonName??I:I,J=this._write(Y,B[I],Z);if(J!==void 0)F[W]=J}return F}if(B===null&&Q?.isStructSchema())return;if(Z.isBlobSchema()&&(B instanceof Uint8Array||typeof B==="string")){if(Z===this.rootSchema)return B;if(!this.serdeContext?.base64Encoder)throw new Error("Missing base64Encoder in serdeContext");return this.serdeContext?.base64Encoder(B)}if(Z.isTimestampSchema()&&B instanceof Date){let F=this.settings.timestampFormat;switch(F.useTrait?Z.getSchema()===Pa.SCHEMA.TIMESTAMP_DEFAULT?F.default:Z.getSchema()??F.default:F.default){case Pa.SCHEMA.TIMESTAMP_DATE_TIME:return B.toISOString().replace(".000Z","Z");case Pa.SCHEMA.TIMESTAMP_HTTP_DATE:return HrQ.dateToUtcString(B);case Pa.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return B.getTime()/1000;default:return console.warn("Missing timestamp format, using epoch seconds",B),B.getTime()/1000}}if(Z.isNumericSchema()&&typeof B==="number"){if(Math.abs(B)===1/0||isNaN(B))return String(B)}let G=Z.getMergedTraits().mediaType;if(Z.isStringSchema()&&typeof B==="string"&&G){if(G==="application/json"||G.endsWith("+json"))return zrQ.LazyJsonString.from(B)}return B}},dB0=class extends Gg{constructor(A){super();this.settings=A}static{t6(this,"JsonCodec")}createSerializer(){let A=new pcA(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new lcA(this.settings);return A.setSerdeContext(this.serdeContext),A}},cB0=class extends WrQ.RpcProtocol{static{t6(this,"AwsJsonRpcProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});this.codec=new dB0({timestampFormat:{useTrait:!0,default:Oa.SCHEMA.TIMESTAMP_EPOCH_SECONDS},jsonName:!1}),this.serializer=this.codec.createSerializer(),this.deserializer=this.codec.createDeserializer()}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":`application/x-amz-json-${this.getJsonRpcVersion()}`,"x-amz-target":(this.getJsonRpcVersion()==="1.0"?"JsonRpc10.":"JsonProtocol.")+Oa.NormalizedSchema.of(A).getName()}),Oa.deref(A.input)==="unit"||!D.body)D.body="{}";try{D.headers["content-length"]=String(JrQ.calculateBodyLength(D.body))}catch(Z){}return D}getPayloadCodec(){return this.codec}async handleError(A,B,Q,D,Z){let G=mB0(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=Oa.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=Oa.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=Oa.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},wrQ=class extends cB0{static{t6(this,"AwsJson1_0Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_0"}getJsonRpcVersion(){return"1.0"}},$rQ=class extends cB0{static{t6(this,"AwsJson1_1Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_1"}getJsonRpcVersion(){return"1.1"}},bB0=$6(),m41=jQ(),qrQ=wY(),NrQ=class extends bB0.HttpBindingProtocol{static{t6(this,"AwsRestJsonProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});let B={timestampFormat:{useTrait:!0,default:m41.SCHEMA.TIMESTAMP_EPOCH_SECONDS},httpBindings:!0,jsonName:!0};this.codec=new dB0(B),this.serializer=new bB0.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new bB0.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getShapeId(){return"aws.protocols#restJson1"}getPayloadCodec(){return this.codec}setSerdeContext(A){this.codec.setSerdeContext(A),super.setSerdeContext(A)}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=m41.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/json"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/json"}}if(D.headers["content-type"]&&!D.body)D.body="{}";if(D.body)try{D.headers["content-length"]=String(qrQ.calculateBodyLength(D.body))}catch(F){}return D}async handleError(A,B,Q,D,Z){let G=mB0(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=m41.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=m41.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=m41.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},LrQ=H8(),MrQ=t6((A)=>{if(A==null)return;if(typeof A==="object"&&"__type"in A)delete A.__type;return LrQ.expectUnion(A)},"awsExpectUnion"),fB0=$6(),xy=jQ(),RrQ=wY(),OrQ=$6(),hcA=jQ(),TrQ=H8(),PrQ=cB(),SrQ=gN(),lB0=class extends Gg{constructor(A){super();this.settings=A,this.stringDeserializer=new OrQ.FromStringShapeDeserializer(A)}static{t6(this,"XmlShapeDeserializer")}stringDeserializer;setSerdeContext(A){this.serdeContext=A,this.stringDeserializer.setSerdeContext(A)}read(A,B,Q){let D=hcA.NormalizedSchema.of(A),Z=D.getMemberSchemas();if(D.isStructSchema()&&D.isMemberSchema()&&!!Object.values(Z).find((Y)=>{return!!Y.getMemberTraits().eventPayload})){let Y={},W=Object.keys(Z)[0];if(Z[W].isBlobSchema())Y[W]=B;else Y[W]=this.read(Z[W],B);return Y}let F=(this.serdeContext?.utf8Encoder??PrQ.toUtf8)(B),I=this.parseXml(F);return this.readSchema(A,Q?I[Q]:I)}readSchema(A,B){let Q=hcA.NormalizedSchema.of(A),D=Q.getMergedTraits(),Z=Q.getSchema();if(Q.isListSchema()&&!Array.isArray(B))return this.readSchema(Z,[B]);if(B==null)return B;if(typeof B==="object"){let G=!!D.sparse,F=!!D.xmlFlattened;if(Q.isListSchema()){let Y=Q.getValueSchema(),W=[],J=Y.getMergedTraits().xmlName??"member",X=F?B:(B[0]??B)[J],V=Array.isArray(X)?X:[X];for(let C of V)if(C!=null||G)W.push(this.readSchema(Y,C));return W}let I={};if(Q.isMapSchema()){let Y=Q.getKeySchema(),W=Q.getValueSchema(),J;if(F)J=Array.isArray(B)?B:[B];else J=Array.isArray(B.entry)?B.entry:[B.entry];let X=Y.getMergedTraits().xmlName??"key",V=W.getMergedTraits().xmlName??"value";for(let C of J){let K=C[X],H=C[V];if(H!=null||G)I[K]=this.readSchema(W,H)}return I}if(Q.isStructSchema()){for(let[Y,W]of Q.structIterator()){let J=W.getMergedTraits(),X=!J.httpPayload?W.getMemberTraits().xmlName??Y:J.xmlName??W.getName();if(B[X]!=null)I[Y]=this.readSchema(W,B[X])}return I}if(Q.isDocumentSchema())return B;throw new Error(`@aws-sdk/core/protocols - xml deserializer unhandled schema type for ${Q.getName(!0)}`)}else{if(Q.isListSchema())return[];else if(Q.isMapSchema()||Q.isStructSchema())return{};return this.stringDeserializer.read(Q,B)}}parseXml(A){if(A.length){let B=new SrQ.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:t6((F,I)=>I.trim()===""&&I.includes(`
`)?"":void 0,"tagValueProcessor")});B.addEntity("#xD","\r"),B.addEntity("#10",`
`);let Q;try{Q=B.parse(A,!0)}catch(F){if(F&&typeof F==="object")Object.defineProperty(F,"$responseBodyText",{value:A});throw F}let D="#text",Z=Object.keys(Q)[0],G=Q[Z];if(G[D])G[Z]=G[D],delete G[D];return TrQ.getValueFromTextNode(G)}return{}}},hB0=$6(),z$1=jQ(),jrQ=Y6(),krQ=H8(),yrQ=Dg(),_rQ=class extends Gg{constructor(A){super();this.settings=A}static{t6(this,"QueryShapeSerializer")}buffer;write(A,B,Q=""){if(this.buffer===void 0)this.buffer="";let D=z$1.NormalizedSchema.of(A);if(Q&&!Q.endsWith("."))Q+=".";if(D.isBlobSchema()){if(typeof B==="string"||B instanceof Uint8Array)this.writeKey(Q),this.writeValue((this.serdeContext?.base64Encoder??yrQ.toBase64)(B))}else if(D.isBooleanSchema()||D.isNumericSchema()||D.isStringSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigIntegerSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigDecimalSchema()){if(B!=null)this.writeKey(Q),this.writeValue(B instanceof jrQ.NumericValue?B.string:String(B))}else if(D.isTimestampSchema()){if(B instanceof Date)switch(this.writeKey(Q),hB0.determineTimestampFormat(D,this.settings)){case z$1.SCHEMA.TIMESTAMP_DATE_TIME:this.writeValue(B.toISOString().replace(".000Z","Z"));break;case z$1.SCHEMA.TIMESTAMP_HTTP_DATE:this.writeValue(krQ.dateToUtcString(B));break;case z$1.SCHEMA.TIMESTAMP_EPOCH_SECONDS:this.writeValue(String(B.getTime()/1000));break}}else if(D.isDocumentSchema())throw new Error(`@aws-sdk/core/protocols - QuerySerializer unsupported document type ${D.getName(!0)}`);else if(D.isListSchema()){if(Array.isArray(B))if(B.length===0){if(this.settings.serializeEmptyLists)this.writeKey(Q),this.writeValue("")}else{let Z=D.getValueSchema(),G=this.settings.flattenLists||D.getMergedTraits().xmlFlattened,F=1;for(let I of B){if(I==null)continue;let Y=this.getKey("member",Z.getMergedTraits().xmlName),W=G?`${Q}${F}`:`${Q}${Y}.${F}`;this.write(Z,I,W),++F}}}else if(D.isMapSchema()){if(B&&typeof B==="object"){let Z=D.getKeySchema(),G=D.getValueSchema(),F=D.getMergedTraits().xmlFlattened,I=1;for(let[Y,W]of Object.entries(B)){if(W==null)continue;let J=this.getKey("key",Z.getMergedTraits().xmlName),X=F?`${Q}${I}.${J}`:`${Q}entry.${I}.${J}`,V=this.getKey("value",G.getMergedTraits().xmlName),C=F?`${Q}${I}.${V}`:`${Q}entry.${I}.${V}`;this.write(Z,Y,X),this.write(G,W,C),++I}}}else if(D.isStructSchema()){if(B&&typeof B==="object")for(let[Z,G]of D.structIterator()){if(B[Z]==null)continue;let F=this.getKey(Z,G.getMergedTraits().xmlName),I=`${Q}${F}`;this.write(G,B[Z],I)}}else if(D.isUnitSchema());else throw new Error(`@aws-sdk/core/protocols - QuerySerializer unrecognized schema type ${D.getName(!0)}`)}flush(){if(this.buffer===void 0)throw new Error("@aws-sdk/core/protocols - QuerySerializer cannot flush with nothing written to buffer.");let A=this.buffer;return delete this.buffer,A}getKey(A,B){let Q=B??A;if(this.settings.capitalizeKeys)return Q[0].toUpperCase()+Q.slice(1);return Q}writeKey(A){if(A.endsWith("."))A=A.slice(0,A.length-1);this.buffer+=`&${hB0.extendedEncodeURIComponent(A)}=`}writeValue(A){this.buffer+=hB0.extendedEncodeURIComponent(A)}},icA=class extends fB0.RpcProtocol{constructor(A){super({defaultNamespace:A.defaultNamespace});this.options=A;let B={timestampFormat:{useTrait:!0,default:xy.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!1,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace,serializeEmptyLists:!0};this.serializer=new _rQ(B),this.deserializer=new lB0(B)}static{t6(this,"AwsQueryProtocol")}serializer;deserializer;getShapeId(){return"aws.protocols#awsQuery"}setSerdeContext(A){this.serializer.setSerdeContext(A),this.deserializer.setSerdeContext(A)}getPayloadCodec(){throw new Error("AWSQuery protocol has no payload codec.")}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":"application/x-www-form-urlencoded"}),xy.deref(A.input)==="unit"||!D.body)D.body="";if(D.body=`Action=${A.name.split("#")[1]}&Version=${this.options.version}`+D.body,D.body.endsWith("&"))D.body=D.body.slice(-1);try{D.headers["content-length"]=String(RrQ.calculateBodyLength(D.body))}catch(Z){}return D}async deserializeResponse(A,B,Q){let D=this.deserializer,Z=xy.NormalizedSchema.of(A.output),G={};if(Q.statusCode>=300){let W=await fB0.collectBody(Q.body,B);if(W.byteLength>0)Object.assign(G,await D.read(xy.SCHEMA.DOCUMENT,W));await this.handleError(A,B,Q,G,this.deserializeMetadata(Q))}for(let W in Q.headers){let J=Q.headers[W];delete Q.headers[W],Q.headers[W.toLowerCase()]=J}let F=Z.isStructSchema()&&this.useNestedResult()?A.name.split("#")[1]+"Result":void 0,I=await fB0.collectBody(Q.body,B);if(I.byteLength>0)Object.assign(G,await D.read(Z,I,F));return{$metadata:this.deserializeMetadata(Q),...G}}useNestedResult(){return!0}async handleError(A,B,Q,D,Z){let G=this.loadQueryErrorCode(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=this.loadQueryError(D),W=xy.TypeRegistry.for(F),J;try{if(J=W.find((H)=>xy.NormalizedSchema.of(H).getMergedTraits().awsQueryError?.[0]===I),!J)J=W.getSchema(G)}catch(H){let z=xy.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(z){let $=z.ctor;throw Object.assign(new $(I),Y)}throw new Error(I)}let X=xy.NormalizedSchema.of(J),V=this.loadQueryErrorMessage(D),C=new J.ctor(V),K={};for(let[H,z]of X.structIterator()){let $=z.getMergedTraits().xmlName??H,L=Y[$]??D[$];K[H]=this.deserializer.readSchema(z,L)}throw Object.assign(C,{$metadata:Z,$response:Q,$fault:X.getMergedTraits().error,message:V,...K}),C}loadQueryErrorCode(A,B){let Q=(B.Errors?.[0]?.Error??B.Errors?.Error??B.Error)?.Code;if(Q!==void 0)return Q;if(A.statusCode==404)return"NotFound"}loadQueryError(A){return A.Errors?.[0]?.Error??A.Errors?.Error??A.Error}loadQueryErrorMessage(A){let B=this.loadQueryError(A);return B?.message??B?.Message??A.message??A.Message??"Unknown"}},xrQ=class extends icA{constructor(A){super(A);this.options=A;let B={capitalizeKeys:!0,flattenLists:!0,serializeEmptyLists:!1};Object.assign(this.serializer.settings,B)}static{t6(this,"AwsEc2QueryProtocol")}useNestedResult(){return!1}},gB0=$6(),d41=jQ(),vrQ=wY(),brQ=H8(),frQ=gN(),ncA=t6((A,B)=>ccA(A,B).then((Q)=>{if(Q.length){let D=new frQ.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:t6((Y,W)=>W.trim()===""&&W.includes(`
`)?"":void 0,"tagValueProcessor")});D.addEntity("#xD","\r"),D.addEntity("#10",`
`);let Z;try{Z=D.parse(Q,!0)}catch(Y){if(Y&&typeof Y==="object")Object.defineProperty(Y,"$responseBodyText",{value:Q});throw Y}let G="#text",F=Object.keys(Z)[0],I=Z[F];if(I[G])I[F]=I[G],delete I[G];return brQ.getValueFromTextNode(I)}return{}}),"parseXmlBody"),hrQ=t6(async(A,B)=>{let Q=await ncA(A,B);if(Q.Error)Q.Error.message=Q.Error.message??Q.Error.Message;return Q},"parseXmlErrorBody"),acA=t6((A,B)=>{if(B?.Error?.Code!==void 0)return B.Error.Code;if(B?.Code!==void 0)return B.Code;if(A.statusCode==404)return"NotFound"},"loadRestXmlErrorCode"),BL=gQ1(),Zg=jQ(),grQ=Y6(),gcA=H8(),ucA=Dg(),scA=class extends Gg{constructor(A){super();this.settings=A}static{t6(this,"XmlShapeSerializer")}stringBuffer;byteBuffer;buffer;write(A,B){let Q=Zg.NormalizedSchema.of(A);if(Q.isStringSchema()&&typeof B==="string")this.stringBuffer=B;else if(Q.isBlobSchema())this.byteBuffer="byteLength"in B?B:(this.serdeContext?.base64Decoder??ucA.fromBase64)(B);else{this.buffer=this.writeStruct(Q,B,void 0);let D=Q.getMergedTraits();if(D.httpPayload&&!D.xmlName)this.buffer.withName(Q.getName())}}flush(){if(this.byteBuffer!==void 0){let B=this.byteBuffer;return delete this.byteBuffer,B}if(this.stringBuffer!==void 0){let B=this.stringBuffer;return delete this.stringBuffer,B}let A=this.buffer;if(this.settings.xmlNamespace){if(!A?.attributes?.xmlns)A.addAttribute("xmlns",this.settings.xmlNamespace)}return delete this.buffer,A.toString()}writeStruct(A,B,Q){let D=A.getMergedTraits(),Z=A.isMemberSchema()&&!D.httpPayload?A.getMemberTraits().xmlName??A.getMemberName():D.xmlName??A.getName();if(!Z||!A.isStructSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write struct with empty name or non-struct, schema=${A.getName(!0)}.`);let G=BL.XmlNode.of(Z),[F,I]=this.getXmlnsAttribute(A,Q);if(I)G.addAttribute(F,I);for(let[Y,W]of A.structIterator()){let J=B[Y];if(J!=null){if(W.getMergedTraits().xmlAttribute){G.addAttribute(W.getMergedTraits().xmlName??Y,this.writeSimple(W,J));continue}if(W.isListSchema())this.writeList(W,J,G,I);else if(W.isMapSchema())this.writeMap(W,J,G,I);else if(W.isStructSchema())G.addChildNode(this.writeStruct(W,J,I));else{let X=BL.XmlNode.of(W.getMergedTraits().xmlName??W.getMemberName());this.writeSimpleInto(W,J,X,I),G.addChildNode(X)}}}return G}writeList(A,B,Q,D){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member list: ${A.getName(!0)}`);let Z=A.getMergedTraits(),G=A.getValueSchema(),F=G.getMergedTraits(),I=!!F.sparse,Y=!!Z.xmlFlattened,[W,J]=this.getXmlnsAttribute(A,D),X=t6((V,C)=>{if(G.isListSchema())this.writeList(G,Array.isArray(C)?C:[C],V,J);else if(G.isMapSchema())this.writeMap(G,C,V,J);else if(G.isStructSchema()){let K=this.writeStruct(G,C,J);V.addChildNode(K.withName(Y?Z.xmlName??A.getMemberName():F.xmlName??"member"))}else{let K=BL.XmlNode.of(Y?Z.xmlName??A.getMemberName():F.xmlName??"member");this.writeSimpleInto(G,C,K,J),V.addChildNode(K)}},"writeItem");if(Y){for(let V of B)if(I||V!=null)X(Q,V)}else{let V=BL.XmlNode.of(Z.xmlName??A.getMemberName());if(J)V.addAttribute(W,J);for(let C of B)if(I||C!=null)X(V,C);Q.addChildNode(V)}}writeMap(A,B,Q,D,Z=!1){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member map: ${A.getName(!0)}`);let G=A.getMergedTraits(),F=A.getKeySchema(),Y=F.getMergedTraits().xmlName??"key",W=A.getValueSchema(),J=W.getMergedTraits(),X=J.xmlName??"value",V=!!J.sparse,C=!!G.xmlFlattened,[K,H]=this.getXmlnsAttribute(A,D),z=t6(($,L,N)=>{let O=BL.XmlNode.of(Y,L),[R,T]=this.getXmlnsAttribute(F,H);if(T)O.addAttribute(R,T);$.addChildNode(O);let j=BL.XmlNode.of(X);if(W.isListSchema())this.writeList(W,N,j,H);else if(W.isMapSchema())this.writeMap(W,N,j,H,!0);else if(W.isStructSchema())j=this.writeStruct(W,N,H);else this.writeSimpleInto(W,N,j,H);$.addChildNode(j)},"addKeyValue");if(C){for(let[$,L]of Object.entries(B))if(V||L!=null){let N=BL.XmlNode.of(G.xmlName??A.getMemberName());z(N,$,L),Q.addChildNode(N)}}else{let $;if(!Z){if($=BL.XmlNode.of(G.xmlName??A.getMemberName()),H)$.addAttribute(K,H);Q.addChildNode($)}for(let[L,N]of Object.entries(B))if(V||N!=null){let O=BL.XmlNode.of("entry");z(O,L,N),(Z?Q:$).addChildNode(O)}}}writeSimple(A,B){if(B===null)throw new Error("@aws-sdk/core/protocols - (XML serializer) cannot write null value.");let Q=Zg.NormalizedSchema.of(A),D=null;if(B&&typeof B==="object")if(Q.isBlobSchema())D=(this.serdeContext?.base64Encoder??ucA.toBase64)(B);else if(Q.isTimestampSchema()&&B instanceof Date){let Z=this.settings.timestampFormat;switch(Z.useTrait?Q.getSchema()===Zg.SCHEMA.TIMESTAMP_DEFAULT?Z.default:Q.getSchema()??Z.default:Z.default){case Zg.SCHEMA.TIMESTAMP_DATE_TIME:D=B.toISOString().replace(".000Z","Z");break;case Zg.SCHEMA.TIMESTAMP_HTTP_DATE:D=gcA.dateToUtcString(B);break;case Zg.SCHEMA.TIMESTAMP_EPOCH_SECONDS:D=String(B.getTime()/1000);break;default:console.warn("Missing timestamp format, using http date",B),D=gcA.dateToUtcString(B);break}}else if(Q.isBigDecimalSchema()&&B){if(B instanceof grQ.NumericValue)return B.string;return String(B)}else if(Q.isMapSchema()||Q.isListSchema())throw new Error("@aws-sdk/core/protocols - xml serializer, cannot call _write() on List/Map schema, call writeList or writeMap() instead.");else throw new Error(`@aws-sdk/core/protocols - xml serializer, unhandled schema type for object value and schema: ${Q.getName(!0)}`);if(Q.isStringSchema()||Q.isBooleanSchema()||Q.isNumericSchema()||Q.isBigIntegerSchema()||Q.isBigDecimalSchema())D=String(B);if(D===null)throw new Error(`Unhandled schema-value pair ${Q.getName(!0)}=${B}`);return D}writeSimpleInto(A,B,Q,D){let Z=this.writeSimple(A,B),G=Zg.NormalizedSchema.of(A),F=new BL.XmlText(Z),[I,Y]=this.getXmlnsAttribute(G,D);if(Y)Q.addAttribute(I,Y);Q.addChildNode(F)}getXmlnsAttribute(A,B){let Q=A.getMergedTraits(),[D,Z]=Q.xmlNamespace??[];if(Z&&Z!==B)return[D?`xmlns:${D}`:"xmlns",Z];return[void 0,void 0]}},rcA=class extends Gg{constructor(A){super();this.settings=A}static{t6(this,"XmlCodec")}createSerializer(){let A=new scA(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new lB0(this.settings);return A.setSerdeContext(this.serdeContext),A}},urQ=class extends gB0.HttpBindingProtocol{static{t6(this,"AwsRestXmlProtocol")}codec;serializer;deserializer;constructor(A){super(A);let B={timestampFormat:{useTrait:!0,default:d41.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!0,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace};this.codec=new rcA(B),this.serializer=new gB0.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new gB0.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getPayloadCodec(){return this.codec}getShapeId(){return"aws.protocols#restXml"}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=d41.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(D.path=String(D.path).split("/").filter((F)=>{return F!=="{Bucket}"}).join("/")||"/",!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/xml"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/xml"}}if(D.headers["content-type"]==="application/xml"){if(typeof D.body==="string")D.body='<?xml version="1.0" encoding="UTF-8"?>'+D.body}if(D.body)try{D.headers["content-length"]=String(vrQ.calculateBodyLength(D.body))}catch(F){}return D}async deserializeResponse(A,B,Q){return super.deserializeResponse(A,B,Q)}async handleError(A,B,Q,D,Z){let G=acA(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=d41.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=d41.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=d41.NormalizedSchema.of(W),X=D.Error?.message??D.Error?.Message??D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().xmlName??K,$=D.Error?.[z]??D[z];C[K]=this.codec.createDeserializer().readSchema(H,$)}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}}});
var tw1=E((wY5,zdA)=>{var{defineProperty:ow1,getOwnPropertyDescriptor:IaQ,getOwnPropertyNames:YaQ}=Object,WaQ=Object.prototype.hasOwnProperty,rw1=(A,B)=>ow1(A,"name",{value:B,configurable:!0}),JaQ=(A,B)=>{for(var Q in B)ow1(A,Q,{get:B[Q],enumerable:!0})},XaQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of YaQ(B))if(!WaQ.call(A,Z)&&Z!==Q)ow1(A,Z,{get:()=>B[Z],enumerable:!(D=IaQ(B,Z))||D.enumerable})}return A},VaQ=(A)=>XaQ(ow1({},"__esModule",{value:!0}),A),VdA={};JaQ(VdA,{getHostHeaderPlugin:()=>KaQ,hostHeaderMiddleware:()=>KdA,hostHeaderMiddlewareOptions:()=>HdA,resolveHostHeaderConfig:()=>CdA});zdA.exports=VaQ(VdA);var CaQ=DK();function CdA(A){return A}rw1(CdA,"resolveHostHeaderConfig");var KdA=rw1((A)=>(B)=>async(Q)=>{if(!CaQ.HttpRequest.isInstance(Q.request))return B(Q);let{request:D}=Q,{handlerProtocol:Z=""}=A.requestHandler.metadata||{};if(Z.indexOf("h2")>=0&&!D.headers[":authority"])delete D.headers.host,D.headers[":authority"]=D.hostname+(D.port?":"+D.port:"");else if(!D.headers.host){let G=D.hostname;if(D.port!=null)G+=`:${D.port}`;D.headers.host=G}return B(Q)},"hostHeaderMiddleware"),HdA={name:"hostHeaderMiddleware",step:"build",priority:"low",tags:["HOST"],override:!0},KaQ=rw1((A)=>({applyToStack:rw1((B)=>{B.add(KdA(A),HdA)},"applyToStack")}),"getHostHeaderPlugin")});
var ufA=E((hfA)=>{Object.defineProperty(hfA,"__esModule",{value:!0});hfA.ruleSet=void 0;var PfA="required",H4="type",f8="fn",h8="argv",Sy="ref",EfA=!1,Q20=!0,Py="booleanEquals",TY="stringEquals",SfA="sigv4",jfA="sts",kfA="us-east-1",FD="endpoint",UfA="https://sts.{Region}.{PartitionResult#dnsSuffix}",tN="tree",Va="error",Z20="getAttr",wfA={[PfA]:!1,[H4]:"String"},D20={[PfA]:!0,default:!1,[H4]:"Boolean"},yfA={[Sy]:"Endpoint"},$fA={[f8]:"isSet",[h8]:[{[Sy]:"Region"}]},PY={[Sy]:"Region"},qfA={[f8]:"aws.partition",[h8]:[PY],assign:"PartitionResult"},_fA={[Sy]:"UseFIPS"},xfA={[Sy]:"UseDualStack"},bW={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:SfA,signingName:jfA,signingRegion:kfA}]},headers:{}},AK={},NfA={conditions:[{[f8]:TY,[h8]:[PY,"aws-global"]}],[FD]:bW,[H4]:FD},vfA={[f8]:Py,[h8]:[_fA,!0]},bfA={[f8]:Py,[h8]:[xfA,!0]},LfA={[f8]:Z20,[h8]:[{[Sy]:"PartitionResult"},"supportsFIPS"]},ffA={[Sy]:"PartitionResult"},MfA={[f8]:Py,[h8]:[!0,{[f8]:Z20,[h8]:[ffA,"supportsDualStack"]}]},RfA=[{[f8]:"isSet",[h8]:[yfA]}],OfA=[vfA],TfA=[bfA],wmQ={version:"1.0",parameters:{Region:wfA,UseDualStack:D20,UseFIPS:D20,Endpoint:wfA,UseGlobalEndpoint:D20},rules:[{conditions:[{[f8]:Py,[h8]:[{[Sy]:"UseGlobalEndpoint"},Q20]},{[f8]:"not",[h8]:RfA},$fA,qfA,{[f8]:Py,[h8]:[_fA,EfA]},{[f8]:Py,[h8]:[xfA,EfA]}],rules:[{conditions:[{[f8]:TY,[h8]:[PY,"ap-northeast-1"]}],endpoint:bW,[H4]:FD},{conditions:[{[f8]:TY,[h8]:[PY,"ap-south-1"]}],endpoint:bW,[H4]:FD},{conditions:[{[f8]:TY,[h8]:[PY,"ap-southeast-1"]}],endpoint:bW,[H4]:FD},{conditions:[{[f8]:TY,[h8]:[PY,"ap-southeast-2"]}],endpoint:bW,[H4]:FD},NfA,{conditions:[{[f8]:TY,[h8]:[PY,"ca-central-1"]}],endpoint:bW,[H4]:FD},{conditions:[{[f8]:TY,[h8]:[PY,"eu-central-1"]}],endpoint:bW,[H4]:FD},{conditions:[{[f8]:TY,[h8]:[PY,"eu-north-1"]}],endpoint:bW,[H4]:FD},{conditions:[{[f8]:TY,[h8]:[PY,"eu-west-1"]}],endpoint:bW,[H4]:FD},{conditions:[{[f8]:TY,[h8]:[PY,"eu-west-2"]}],endpoint:bW,[H4]:FD},{conditions:[{[f8]:TY,[h8]:[PY,"eu-west-3"]}],endpoint:bW,[H4]:FD},{conditions:[{[f8]:TY,[h8]:[PY,"sa-east-1"]}],endpoint:bW,[H4]:FD},{conditions:[{[f8]:TY,[h8]:[PY,kfA]}],endpoint:bW,[H4]:FD},{conditions:[{[f8]:TY,[h8]:[PY,"us-east-2"]}],endpoint:bW,[H4]:FD},{conditions:[{[f8]:TY,[h8]:[PY,"us-west-1"]}],endpoint:bW,[H4]:FD},{conditions:[{[f8]:TY,[h8]:[PY,"us-west-2"]}],endpoint:bW,[H4]:FD},{endpoint:{url:UfA,properties:{authSchemes:[{name:SfA,signingName:jfA,signingRegion:"{Region}"}]},headers:AK},[H4]:FD}],[H4]:tN},{conditions:RfA,rules:[{conditions:OfA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[H4]:Va},{conditions:TfA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[H4]:Va},{endpoint:{url:yfA,properties:AK,headers:AK},[H4]:FD}],[H4]:tN},{conditions:[$fA],rules:[{conditions:[qfA],rules:[{conditions:[vfA,bfA],rules:[{conditions:[{[f8]:Py,[h8]:[Q20,LfA]},MfA],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:AK,headers:AK},[H4]:FD}],[H4]:tN},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[H4]:Va}],[H4]:tN},{conditions:OfA,rules:[{conditions:[{[f8]:Py,[h8]:[LfA,Q20]}],rules:[{conditions:[{[f8]:TY,[h8]:[{[f8]:Z20,[h8]:[ffA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:AK,headers:AK},[H4]:FD},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:AK,headers:AK},[H4]:FD}],[H4]:tN},{error:"FIPS is enabled but this partition does not support FIPS",[H4]:Va}],[H4]:tN},{conditions:TfA,rules:[{conditions:[MfA],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:AK,headers:AK},[H4]:FD}],[H4]:tN},{error:"DualStack is enabled but this partition does not support DualStack",[H4]:Va}],[H4]:tN},NfA,{endpoint:{url:UfA,properties:AK,headers:AK},[H4]:FD}],[H4]:tN}],[H4]:tN},{error:"Invalid Configuration: Missing Region",[H4]:Va}]};hfA.ruleSet=wmQ});
var vA0=E(($xA)=>{Object.defineProperty($xA,"__esModule",{value:!0});$xA.resolveHttpAuthSchemeConfig=$xA.defaultSSOHttpAuthSchemeProvider=$xA.defaultSSOHttpAuthSchemeParametersProvider=void 0;var cfQ=II(),xA0=I5(),lfQ=async(A,B,Q)=>{return{operation:xA0.getSmithyContext(B).operation,region:await xA0.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};$xA.defaultSSOHttpAuthSchemeParametersProvider=lfQ;function pfQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"awsssoportal",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function Cw1(A){return{schemeId:"smithy.api#noAuth"}}var ifQ=(A)=>{let B=[];switch(A.operation){case"GetRoleCredentials":{B.push(Cw1(A));break}case"ListAccountRoles":{B.push(Cw1(A));break}case"ListAccounts":{B.push(Cw1(A));break}case"Logout":{B.push(Cw1(A));break}default:B.push(pfQ(A))}return B};$xA.defaultSSOHttpAuthSchemeProvider=ifQ;var nfQ=(A)=>{let B=cfQ.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:xA0.normalizeProvider(A.authSchemePreference??[])})};$xA.resolveHttpAuthSchemeConfig=nfQ});
var vgA=E((_gA)=>{Object.defineProperty(_gA,"__esModule",{value:!0});_gA.ruleSet=void 0;var SgA="required",fW="fn",hW="argv",La="ref",wgA=!0,$gA="isSet",f41="booleanEquals",Na="error",AL="endpoint",VT="tree",r20="PartitionResult",o20="getAttr",v41="stringEquals",qgA={[SgA]:!1,type:"String"},NgA={[SgA]:!0,default:!1,type:"Boolean"},LgA={[La]:"Endpoint"},jgA={[fW]:f41,[hW]:[{[La]:"UseFIPS"},!0]},kgA={[fW]:f41,[hW]:[{[La]:"UseDualStack"},!0]},vG={},b41={[La]:"Region"},MgA={[fW]:o20,[hW]:[{[La]:r20},"supportsFIPS"]},ygA={[La]:r20},RgA={[fW]:f41,[hW]:[!0,{[fW]:o20,[hW]:[ygA,"supportsDualStack"]}]},OgA=[jgA],TgA=[kgA],PgA=[b41],DlQ={version:"1.0",parameters:{Region:qgA,UseDualStack:NgA,UseFIPS:NgA,Endpoint:qgA},rules:[{conditions:[{[fW]:$gA,[hW]:[LgA]}],rules:[{conditions:OgA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:Na},{conditions:TgA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:Na},{endpoint:{url:LgA,properties:vG,headers:vG},type:AL}],type:VT},{conditions:[{[fW]:$gA,[hW]:PgA}],rules:[{conditions:[{[fW]:"aws.partition",[hW]:PgA,assign:r20}],rules:[{conditions:[jgA,kgA],rules:[{conditions:[{[fW]:f41,[hW]:[wgA,MgA]},RgA],rules:[{conditions:[{[fW]:v41,[hW]:[b41,"us-east-1"]}],endpoint:{url:"https://cognito-identity-fips.us-east-1.amazonaws.com",properties:vG,headers:vG},type:AL},{conditions:[{[fW]:v41,[hW]:[b41,"us-east-2"]}],endpoint:{url:"https://cognito-identity-fips.us-east-2.amazonaws.com",properties:vG,headers:vG},type:AL},{conditions:[{[fW]:v41,[hW]:[b41,"us-west-1"]}],endpoint:{url:"https://cognito-identity-fips.us-west-1.amazonaws.com",properties:vG,headers:vG},type:AL},{conditions:[{[fW]:v41,[hW]:[b41,"us-west-2"]}],endpoint:{url:"https://cognito-identity-fips.us-west-2.amazonaws.com",properties:vG,headers:vG},type:AL},{endpoint:{url:"https://cognito-identity-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:vG,headers:vG},type:AL}],type:VT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:Na}],type:VT},{conditions:OgA,rules:[{conditions:[{[fW]:f41,[hW]:[MgA,wgA]}],rules:[{endpoint:{url:"https://cognito-identity-fips.{Region}.{PartitionResult#dnsSuffix}",properties:vG,headers:vG},type:AL}],type:VT},{error:"FIPS is enabled but this partition does not support FIPS",type:Na}],type:VT},{conditions:TgA,rules:[{conditions:[RgA],rules:[{conditions:[{[fW]:v41,[hW]:["aws",{[fW]:o20,[hW]:[ygA,"name"]}]}],endpoint:{url:"https://cognito-identity.{Region}.amazonaws.com",properties:vG,headers:vG},type:AL},{endpoint:{url:"https://cognito-identity.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:vG,headers:vG},type:AL}],type:VT},{error:"DualStack is enabled but this partition does not support DualStack",type:Na}],type:VT},{endpoint:{url:"https://cognito-identity.{Region}.{PartitionResult#dnsSuffix}",properties:vG,headers:vG},type:AL}],type:VT}],type:VT},{error:"Invalid Configuration: Missing Region",type:Na}]};_gA.ruleSet=DlQ});
var wsA=E((EsA)=>{Object.defineProperty(EsA,"__esModule",{value:!0});EsA.fromProcess=void 0;var N24=S$1(),L24=(A)=>N24.fromProcess(A);EsA.fromProcess=L24});
var wxA=E((ExA)=>{Object.defineProperty(ExA,"__esModule",{value:!0});ExA.fromHttp=void 0;var SfQ=ih(),jfQ=Lw(),kfQ=S3(),HxA=eB(),yfQ=SfQ.__importDefault(J1("fs/promises")),_fQ=WxA(),zxA=XxA(),xfQ=KxA(),vfQ="AWS_CONTAINER_CREDENTIALS_RELATIVE_URI",bfQ="http://*************",ffQ="AWS_CONTAINER_CREDENTIALS_FULL_URI",hfQ="AWS_CONTAINER_AUTHORIZATION_TOKEN_FILE",gfQ="AWS_CONTAINER_AUTHORIZATION_TOKEN",ufQ=(A={})=>{A.logger?.debug("@aws-sdk/credential-provider-http - fromHttp");let B,Q=A.awsContainerCredentialsRelativeUri??process.env[vfQ],D=A.awsContainerCredentialsFullUri??process.env[ffQ],Z=A.awsContainerAuthorizationToken??process.env[gfQ],G=A.awsContainerAuthorizationTokenFile??process.env[hfQ],F=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console.warn:A.logger.warn;if(Q&&D)F("@aws-sdk/credential-provider-http: you have set both awsContainerCredentialsRelativeUri and awsContainerCredentialsFullUri."),F("awsContainerCredentialsFullUri will take precedence.");if(Z&&G)F("@aws-sdk/credential-provider-http: you have set both awsContainerAuthorizationToken and awsContainerAuthorizationTokenFile."),F("awsContainerAuthorizationToken will take precedence.");if(D)B=D;else if(Q)B=`${bfQ}${Q}`;else throw new HxA.CredentialsProviderError(`No HTTP credential provider host provided.
Set AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI.`,{logger:A.logger});let I=new URL(B);_fQ.checkUrl(I,A.logger);let Y=new kfQ.NodeHttpHandler({requestTimeout:A.timeout??1000,connectionTimeout:A.timeout??1000});return xfQ.retryWrapper(async()=>{let W=zxA.createGetRequest(I);if(Z)W.headers.Authorization=Z;else if(G)W.headers.Authorization=(await yfQ.default.readFile(G)).toString();try{let J=await Y.handle(W);return zxA.getCredentials(J.response).then((X)=>jfQ.setCredentialFeature(X,"CREDENTIALS_HTTP","z"))}catch(J){throw new HxA.CredentialsProviderError(String(J),{logger:A.logger})}},A.maxRetries??3,A.timeout??1000)};ExA.fromHttp=ufQ});
var xcA=E((ycA)=>{Object.defineProperty(ycA,"__esModule",{value:!0});ycA.toBase64=void 0;var nsQ=AD(),asQ=cB(),ssQ=(A)=>{let B;if(typeof A==="string")B=asQ.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return nsQ.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};ycA.toBase64=ssQ});
var yaA=E((uW5,kaA)=>{var{create:e04,defineProperty:t41,getOwnPropertyDescriptor:AA4,getOwnPropertyNames:BA4,getPrototypeOf:QA4}=Object,DA4=Object.prototype.hasOwnProperty,$T=(A,B)=>t41(A,"name",{value:B,configurable:!0}),ZA4=(A,B)=>{for(var Q in B)t41(A,Q,{get:B[Q],enumerable:!0})},TaA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of BA4(B))if(!DA4.call(A,Z)&&Z!==Q)t41(A,Z,{get:()=>B[Z],enumerable:!(D=AA4(B,Z))||D.enumerable})}return A},PaA=(A,B,Q)=>(Q=A!=null?e04(QA4(A)):{},TaA(B||!A||!A.__esModule?t41(Q,"default",{value:A,enumerable:!0}):Q,A)),GA4=(A)=>TaA(t41({},"__esModule",{value:!0}),A),SaA={};ZA4(SaA,{fromEnvSigningName:()=>YA4,fromSso:()=>jaA,fromStatic:()=>HA4,nodeProvider:()=>zA4});kaA.exports=GA4(SaA);var FA4=Mz(),IA4=_B0(),FK=eB(),YA4=$T(({logger:A,signingName:B}={})=>async()=>{if(A?.debug?.("@aws-sdk/token-providers - fromEnvSigningName"),!B)throw new FK.TokenProviderError("Please pass 'signingName' to compute environment variable key",{logger:A});let Q=IA4.getBearerTokenEnvKey(B);if(!(Q in process.env))throw new FK.TokenProviderError(`Token not present in '${Q}' environment variable`,{logger:A});let D={token:process.env[Q]};return FA4.setTokenFeature(D,"BEARER_SERVICE_ENV_VARS","3"),D},"fromEnvSigningName"),WA4=300000,t90="To refresh this SSO session run 'aws sso login' with the corresponding profile.",JA4=$T(async(A,B={})=>{let{SSOOIDCClient:Q}=await Promise.resolve().then(()=>PaA(o90()));return new Q(Object.assign({},B.clientConfig??{},{region:A??B.clientConfig?.region,logger:B.clientConfig?.logger??B.parentClientConfig?.logger}))},"getSsoOidcClient"),XA4=$T(async(A,B,Q={})=>{let{CreateTokenCommand:D}=await Promise.resolve().then(()=>PaA(o90()));return(await JA4(B,Q)).send(new D({clientId:A.clientId,clientSecret:A.clientSecret,refreshToken:A.refreshToken,grantType:"refresh_token"}))},"getNewSsoOidcToken"),RaA=$T((A)=>{if(A.expiration&&A.expiration.getTime()<Date.now())throw new FK.TokenProviderError(`Token is expired. ${t90}`,!1)},"validateTokenExpiry"),Wg=$T((A,B,Q=!1)=>{if(typeof B==="undefined")throw new FK.TokenProviderError(`Value not present for '${A}' in SSO Token${Q?". Cannot refresh":""}. ${t90}`,!1)},"validateTokenKey"),o41=e5(),VA4=J1("fs"),{writeFile:CA4}=VA4.promises,KA4=$T((A,B)=>{let Q=o41.getSSOTokenFilepath(A),D=JSON.stringify(B,null,2);return CA4(Q,D)},"writeSSOTokenToFile"),OaA=new Date(0),jaA=$T((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/token-providers - fromSso");let D=await o41.parseKnownFiles(Q),Z=o41.getProfileName({profile:Q.profile??B?.profile}),G=D[Z];if(!G)throw new FK.TokenProviderError(`Profile '${Z}' could not be found in shared credentials file.`,!1);else if(!G.sso_session)throw new FK.TokenProviderError(`Profile '${Z}' is missing required property 'sso_session'.`);let F=G.sso_session,Y=(await o41.loadSsoSessionData(Q))[F];if(!Y)throw new FK.TokenProviderError(`Sso session '${F}' could not be found in shared credentials file.`,!1);for(let H of["sso_start_url","sso_region"])if(!Y[H])throw new FK.TokenProviderError(`Sso session '${F}' is missing required property '${H}'.`,!1);let{sso_start_url:W,sso_region:J}=Y,X;try{X=await o41.getSSOTokenFromFile(F)}catch(H){throw new FK.TokenProviderError(`The SSO session token associated with profile=${Z} was not found or is invalid. ${t90}`,!1)}Wg("accessToken",X.accessToken),Wg("expiresAt",X.expiresAt);let{accessToken:V,expiresAt:C}=X,K={token:V,expiration:new Date(C)};if(K.expiration.getTime()-Date.now()>WA4)return K;if(Date.now()-OaA.getTime()<30000)return RaA(K),K;Wg("clientId",X.clientId,!0),Wg("clientSecret",X.clientSecret,!0),Wg("refreshToken",X.refreshToken,!0);try{OaA.setTime(Date.now());let H=await XA4(X,J,Q);Wg("accessToken",H.accessToken),Wg("expiresIn",H.expiresIn);let z=new Date(Date.now()+H.expiresIn*1000);try{await KA4(F,{...X,accessToken:H.accessToken,expiresAt:z.toISOString(),refreshToken:H.refreshToken})}catch($){}return{token:H.accessToken,expiration:z}}catch(H){return RaA(K),K}},"fromSso"),HA4=$T(({token:A,logger:B})=>async()=>{if(B?.debug("@aws-sdk/token-providers - fromStatic"),!A||!A.token)throw new FK.TokenProviderError("Please pass a valid token to fromStatic",!1);return A},"fromStatic"),zA4=$T((A={})=>FK.memoize(FK.chain(jaA(A),async()=>{throw new FK.TokenProviderError("Could not load token from any providers",!1)}),(B)=>B.expiration!==void 0&&B.expiration.getTime()-Date.now()<300000,(B)=>B.expiration!==void 0),"nodeProvider")});

module.exports = YQ0;
