// dependency_chain package extracted with entry point: fR1

var BV1=E((ti0)=>{Object.defineProperty(ti0,"__esModule",{value:!0});ti0.SDKType=void 0;var oi0={},el;ti0.SDKType={_get:(A)=>{var B;return((B=oi0[A])!==null&&B!==void 0?B:"js-mono")+(el!==null&&el!==void 0?el:"")},_setClientType(A,B){oi0[A]=B},_setBindingType(A){if(!el||el==="-react")el="-"+A}}});
var DV1=E((Pn0)=>{Object.defineProperty(Pn0,"__esModule",{value:!0});Pn0.StatsigSession=Pn0.SessionID=void 0;var qT9=zB1(),NT9=jW(),Ln0=VO(),Mn0=sX1(),Rn0=1800000,On0=14400000,QV1={};Pn0.SessionID={get:(A)=>{return Pn0.StatsigSession.get(A).data.sessionID}};Pn0.StatsigSession={get:(A)=>{if(QV1[A]==null)QV1[A]=LT9(A);let B=QV1[A];return RT9(B)},overrideInitialSessionID:(A,B)=>{QV1[B]=MT9(A,B)}};function LT9(A){let B=ST9(A),Q=Date.now();if(!B)B={sessionID:Mn0.getUUID(),startTime:Q,lastUpdate:Q};return{data:B,sdkKey:A}}function MT9(A,B){let Q=Date.now();return{data:{sessionID:A,startTime:Q,lastUpdate:Q},sdkKey:B}}function RT9(A){let B=Date.now(),Q=A.data;if(OT9(Q)||TT9(Q))Q.sessionID=Mn0.getUUID(),Q.startTime=B;Q.lastUpdate=B,PT9(Q,A.sdkKey),clearTimeout(A.idleTimeoutID),clearTimeout(A.ageTimeoutID);let D=B-Q.startTime,Z=A.sdkKey;return A.idleTimeoutID=Nn0(Z,Rn0),A.ageTimeoutID=Nn0(Z,On0-D),A}function Nn0(A,B){return setTimeout(()=>{let Q=__STATSIG__===null||__STATSIG__===void 0?void 0:__STATSIG__.instance(A);if(Q)Q.$emt({name:"session_expired"})},B)}function OT9({lastUpdate:A}){return Date.now()-A>Rn0}function TT9({startTime:A}){return Date.now()-A>On0}function Tn0(A){return`statsig.session_id.${qT9._getStorageKey(A)}`}function PT9(A,B){let Q=Tn0(B);try{Ln0._setObjectInStorage(Q,A)}catch(D){NT9.Log.warn("Failed to save SessionID")}}function ST9(A){let B=Tn0(A);return Ln0._getObjectFromStorage(B)}});
var EB1=E((Yi0)=>{Object.defineProperty(Yi0,"__esModule",{value:!0});Yi0.NetworkParam=Yi0.NetworkDefault=Yi0.Endpoint=void 0;Yi0.Endpoint={_initialize:"initialize",_rgstr:"rgstr",_download_config_specs:"download_config_specs"};Yi0.NetworkDefault={[Yi0.Endpoint._rgstr]:"https://prodregistryv2.org/v1",[Yi0.Endpoint._initialize]:"https://featureassets.org/v1",[Yi0.Endpoint._download_config_specs]:"https://api.statsigcdn.com/v1"};Yi0.NetworkParam={EventCount:"ec",SdkKey:"k",SdkType:"st",SdkVersion:"sv",Time:"t",SessionID:"sid",StatsigEncoded:"se",IsGzipped:"gz"}});
var Fn0=E((Gn0)=>{Object.defineProperty(Gn0,"__esModule",{value:!0})});
var Ha0=E((Ca0)=>{Object.defineProperty(Ca0,"__esModule",{value:!0});Ca0._resolveDeltasResponse=void 0;var Va0=sj(),LP9=2;function MP9(A,B){let Q=Va0._typedJsonParse(B,"checksum","DeltasEvaluationResponse");if(!Q)return{hadBadDeltaChecksum:!0};let D=RP9(A,Q),Z=OP9(D),G=Va0._DJB2Object({feature_gates:Z.feature_gates,dynamic_configs:Z.dynamic_configs,layer_configs:Z.layer_configs},LP9);if(G!==Q.checksumV2)return{hadBadDeltaChecksum:!0,badChecksum:G,badMergedConfigs:Z,badFullResponse:Q.deltas_full_response};return JSON.stringify(Z)}Ca0._resolveDeltasResponse=MP9;function RP9(A,B){return Object.assign(Object.assign(Object.assign({},A),B),{feature_gates:Object.assign(Object.assign({},A.feature_gates),B.feature_gates),layer_configs:Object.assign(Object.assign({},A.layer_configs),B.layer_configs),dynamic_configs:Object.assign(Object.assign({},A.dynamic_configs),B.dynamic_configs)})}function OP9(A){let B=A;return il1(A.deleted_gates,B.feature_gates),delete B.deleted_gates,il1(A.deleted_configs,B.dynamic_configs),delete B.deleted_configs,il1(A.deleted_layers,B.layer_configs),delete B.deleted_layers,B}function il1(A,B){A===null||A===void 0||A.forEach((Q)=>{delete B[Q]})}});
var Ia0=E((Ga0)=>{Object.defineProperty(Ga0,"__esModule",{value:!0});Ga0.UPDATE_DETAIL_ERROR_MESSAGES=Ga0.createUpdateDetails=void 0;var HP9=(A,B,Q,D,Z,G)=>{return{duration:Q,source:B,success:A,error:D,sourceUrl:Z,warnings:G}};Ga0.createUpdateDetails=HP9;Ga0.UPDATE_DETAIL_ERROR_MESSAGES={NO_NETWORK_DATA:"No data was returned from the network. This may be due to a network timeout if a timeout value was specified in the options or ad blocker error."}});
var La0=E((Zp)=>{var xP9=Zp&&Zp.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(Zp,"__esModule",{value:!0});Zp.StatsigEvaluationsDataAdapter=void 0;var hf=sj(),vP9=nl1();class Na0 extends hf.DataAdapterCore{constructor(){super("EvaluationsDataAdapter","evaluations");this._network=null,this._options=null}attach(A,B){super.attach(A,B),this._network=new vP9.default(B!==null&&B!==void 0?B:{})}getDataAsync(A,B,Q){return this._getDataAsyncImpl(A,hf._normalizeUser(B,this._options),Q)}prefetchData(A,B){return this._prefetchDataImpl(A,B)}setData(A){let B=hf._typedJsonParse(A,"has_updates","data");if(B&&"user"in B)super.setData(A,B.user);else hf.Log.error("StatsigUser not found. You may be using an older server SDK version. Please upgrade your SDK or use setDataLegacy.")}setDataLegacy(A,B){super.setData(A,B)}_fetchFromNetwork(A,B,Q,D){var Z;return xP9(this,void 0,void 0,function*(){let G=yield(Z=this._network)===null||Z===void 0?void 0:Z.fetchEvaluations(this._getSdkKey(),A,Q===null||Q===void 0?void 0:Q.priority,B,D);return G!==null&&G!==void 0?G:null})}_getCacheKey(A){var B;let Q=hf._getStorageKey(this._getSdkKey(),A,(B=this._options)===null||B===void 0?void 0:B.customUserCacheKeyFunc);return`${hf.DataAdapterCachePrefix}.${this._cacheSuffix}.${Q}`}_isCachedResultValidFor204(A,B){return A.fullUserHash!=null&&A.fullUserHash===hf._getFullUserHash(B)}}Zp.StatsigEvaluationsDataAdapter=Na0});
var Ll1=E((Hi0)=>{Object.defineProperty(Hi0,"__esModule",{value:!0});Hi0._createLayerParameterExposure=Hi0._createConfigExposure=Hi0._mapExposures=Hi0._createGateExposure=Hi0._isExposureEvent=void 0;var Vi0="statsig::config_exposure",Ci0="statsig::gate_exposure",Ki0="statsig::layer_exposure",Nl1=(A,B,Q,D,Z)=>{if(Q.bootstrapMetadata)D.bootstrapMetadata=Q.bootstrapMetadata;return{eventName:A,user:B,value:null,metadata:VO9(Q,D),secondaryExposures:Z,time:Date.now()}},YO9=({eventName:A})=>{return A===Ci0||A===Vi0||A===Ki0};Hi0._isExposureEvent=YO9;var WO9=(A,B,Q)=>{var D,Z,G;let F={gate:B.name,gateValue:String(B.value),ruleID:B.ruleID};if(((D=B.__evaluation)===null||D===void 0?void 0:D.version)!=null)F.configVersion=B.__evaluation.version;return Nl1(Ci0,A,B.details,F,mX1((G=(Z=B.__evaluation)===null||Z===void 0?void 0:Z.secondary_exposures)!==null&&G!==void 0?G:[],Q))};Hi0._createGateExposure=WO9;function mX1(A,B){return A.map((Q)=>{if(typeof Q==="string")return(B!==null&&B!==void 0?B:{})[Q];return Q}).filter((Q)=>Q!=null)}Hi0._mapExposures=mX1;var JO9=(A,B,Q)=>{var D,Z,G,F;let I={config:B.name,ruleID:B.ruleID};if(((D=B.__evaluation)===null||D===void 0?void 0:D.version)!=null)I.configVersion=B.__evaluation.version;if(((Z=B.__evaluation)===null||Z===void 0?void 0:Z.passed)!=null)I.rulePassed=String(B.__evaluation.passed);return Nl1(Vi0,A,B.details,I,mX1((F=(G=B.__evaluation)===null||G===void 0?void 0:G.secondary_exposures)!==null&&F!==void 0?F:[],Q))};Hi0._createConfigExposure=JO9;var XO9=(A,B,Q,D)=>{var Z,G,F,I;let Y=B.__evaluation,W=((Z=Y===null||Y===void 0?void 0:Y.explicit_parameters)===null||Z===void 0?void 0:Z.includes(Q))===!0,J="",X=(G=Y===null||Y===void 0?void 0:Y.undelegated_secondary_exposures)!==null&&G!==void 0?G:[];if(W)J=(F=Y.allocated_experiment_name)!==null&&F!==void 0?F:"",X=Y.secondary_exposures;let V={config:B.name,parameterName:Q,ruleID:B.ruleID,allocatedExperiment:J,isExplicitParameter:String(W)};if(((I=B.__evaluation)===null||I===void 0?void 0:I.version)!=null)V.configVersion=B.__evaluation.version;return Nl1(Ki0,A,B.details,V,mX1(X,D))};Hi0._createLayerParameterExposure=XO9;var VO9=(A,B)=>{if(B.reason=A.reason,A.lcut)B.lcut=String(A.lcut);if(A.receivedAt)B.receivedAt=String(A.receivedAt);return B}});
var Ol1=E(($i0)=>{Object.defineProperty($i0,"__esModule",{value:!0});$i0.UrlConfiguration=void 0;var lX1=EB1(),LO9={[lX1.Endpoint._initialize]:"i",[lX1.Endpoint._rgstr]:"e",[lX1.Endpoint._download_config_specs]:"d"};class wi0{constructor(A,B,Q,D){if(this.customUrl=null,this.fallbackUrls=null,this.endpoint=A,this.endpointDnsKey=LO9[A],B)this.customUrl=B;if(!B&&Q)this.customUrl=Q.endsWith("/")?`${Q}${A}`:`${Q}/${A}`;if(D)this.fallbackUrls=D;let Z=lX1.NetworkDefault[A];this.defaultUrl=`${Z}/${A}`}getUrl(){var A;return(A=this.customUrl)!==null&&A!==void 0?A:this.defaultUrl}}$i0.UrlConfiguration=wi0});
var Qa0=E((Aa0)=>{Object.defineProperty(Aa0,"__esModule",{value:!0});Aa0._makeTypedGet=Aa0._mergeOverride=Aa0._makeLayer=Aa0._makeExperiment=Aa0._makeDynamicConfig=Aa0._makeFeatureGate=void 0;var DP9=jW(),ZP9=fX1(),GP9="default";function pl1(A,B,Q,D){var Z;return{name:A,details:B,ruleID:(Z=Q===null||Q===void 0?void 0:Q.rule_id)!==null&&Z!==void 0?Z:GP9,__evaluation:Q,value:D}}function FP9(A,B,Q){return pl1(A,B,Q,(Q===null||Q===void 0?void 0:Q.value)===!0)}Aa0._makeFeatureGate=FP9;function en0(A,B,Q){var D;let Z=(D=Q===null||Q===void 0?void 0:Q.value)!==null&&D!==void 0?D:{};return Object.assign(Object.assign({},pl1(A,B,Q,Z)),{get:GV1(A,Q===null||Q===void 0?void 0:Q.value)})}Aa0._makeDynamicConfig=en0;function IP9(A,B,Q){var D;let Z=en0(A,B,Q);return Object.assign(Object.assign({},Z),{groupName:(D=Q===null||Q===void 0?void 0:Q.group_name)!==null&&D!==void 0?D:null})}Aa0._makeExperiment=IP9;function YP9(A,B,Q,D){var Z,G;return Object.assign(Object.assign({},pl1(A,B,Q,void 0)),{get:GV1(A,Q===null||Q===void 0?void 0:Q.value,D),groupName:(Z=Q===null||Q===void 0?void 0:Q.group_name)!==null&&Z!==void 0?Z:null,__value:(G=Q===null||Q===void 0?void 0:Q.value)!==null&&G!==void 0?G:{}})}Aa0._makeLayer=YP9;function WP9(A,B,Q,D){return Object.assign(Object.assign(Object.assign({},A),B),{get:GV1(A.name,Q,D)})}Aa0._mergeOverride=WP9;function GV1(A,B,Q){return(D,Z)=>{var G;let F=(G=B===null||B===void 0?void 0:B[D])!==null&&G!==void 0?G:null;if(F==null)return Z!==null&&Z!==void 0?Z:null;if(Z!=null&&!ZP9._isTypeMatch(F,Z))return DP9.Log.warn(`Parameter type mismatch. '${A}.${D}' was found to be type '${typeof F}' but fallback/return type is '${typeof Z}'. See https://docs.statsig.com/client/javascript-sdk/#typed-getters`),Z!==null&&Z!==void 0?Z:null;return Q===null||Q===void 0||Q(D),F}}Aa0._makeTypedGet=GV1});
var Ra0=E((LB1)=>{var sl1=LB1&&LB1.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(LB1,"__esModule",{value:!0});var d4=sj(),bP9=Xa0(),fP9=nl1(),Ma0=qa0(),hP9=La0();class WV1 extends d4.StatsigClientBase{static instance(A){let B=d4._getStatsigGlobal().instance(A);if(B instanceof WV1)return B;return d4.Log.warn(d4._isServerEnv()?"StatsigClient.instance is not supported in server environments":"Unable to find StatsigClient instance"),new WV1(A!==null&&A!==void 0?A:"",{})}constructor(A,B,Q=null){var D,Z;d4.SDKType._setClientType(A,"javascript-client");let G=new fP9.default(Q,(I)=>{this.$emt(I)});super(A,(D=Q===null||Q===void 0?void 0:Q.dataAdapter)!==null&&D!==void 0?D:new hP9.StatsigEvaluationsDataAdapter,G,Q);this.getFeatureGate=this._memoize(d4.MemoPrefix._gate,this._getFeatureGateImpl.bind(this)),this.getDynamicConfig=this._memoize(d4.MemoPrefix._dynamicConfig,this._getDynamicConfigImpl.bind(this)),this.getExperiment=this._memoize(d4.MemoPrefix._experiment,this._getExperimentImpl.bind(this)),this.getLayer=this._memoize(d4.MemoPrefix._layer,this._getLayerImpl.bind(this)),this.getParameterStore=this._memoize(d4.MemoPrefix._paramStore,this._getParameterStoreImpl.bind(this)),this._store=new bP9.default(A),this._network=G,this._user=this._configureUser(B,Q);let F=(Z=Q===null||Q===void 0?void 0:Q.plugins)!==null&&Z!==void 0?Z:[];for(let I of F)I.bind(this)}initializeSync(A){var B;if(this.loadingStatus!=="Uninitialized")return d4.createUpdateDetails(!0,this._store.getSource(),-1,null,null,["MultipleInitializations",...(B=this._store.getWarnings())!==null&&B!==void 0?B:[]]);return this._logger.start(),this.updateUserSync(this._user,A)}initializeAsync(A){return sl1(this,void 0,void 0,function*(){if(this._initializePromise)return this._initializePromise;return this._initializePromise=this._initializeAsyncImpl(A),this._initializePromise})}updateUserSync(A,B){var Q;let D=performance.now(),Z=[...(Q=this._store.getWarnings())!==null&&Q!==void 0?Q:[]];this._resetForUser(A);let G=this.dataAdapter.getDataSync(this._user);if(G==null)Z.push("NoCachedValues");this._store.setValues(G,this._user),this._finalizeUpdate(G);let F=B===null||B===void 0?void 0:B.disableBackgroundCacheRefresh;if(F===!0||F==null&&(G===null||G===void 0?void 0:G.source)==="Bootstrap")return d4.createUpdateDetails(!0,this._store.getSource(),performance.now()-D,this._errorBoundary.getLastSeenErrorAndReset(),this._network.getLastUsedInitUrlAndReset(),Z);return this._runPostUpdate(G!==null&&G!==void 0?G:null,this._user),d4.createUpdateDetails(!0,this._store.getSource(),performance.now()-D,this._errorBoundary.getLastSeenErrorAndReset(),this._network.getLastUsedInitUrlAndReset(),Z)}updateUserAsync(A,B){return sl1(this,void 0,void 0,function*(){this._resetForUser(A);let Q=this._user;d4.Diagnostics._markInitOverallStart(this._sdkKey);let D=this.dataAdapter.getDataSync(Q);if(this._store.setValues(D,this._user),this._setStatus("Loading",D),D=yield this.dataAdapter.getDataAsync(D,Q,B),Q!==this._user)return d4.createUpdateDetails(!1,this._store.getSource(),-1,new Error("User changed during update"),this._network.getLastUsedInitUrlAndReset());let Z=!1;if(D!=null)d4.Diagnostics._markInitProcessStart(this._sdkKey),Z=this._store.setValues(D,this._user),d4.Diagnostics._markInitProcessEnd(this._sdkKey,{success:Z});if(this._finalizeUpdate(D),!Z)this._errorBoundary.attachErrorIfNoneExists(d4.UPDATE_DETAIL_ERROR_MESSAGES.NO_NETWORK_DATA),this.$emt({name:"initialization_failure"});d4.Diagnostics._markInitOverallEnd(this._sdkKey,Z,this._store.getCurrentSourceDetails());let G=d4.Diagnostics._enqueueDiagnosticsEvent(this._user,this._logger,this._sdkKey,this._options);return d4.createUpdateDetails(Z,this._store.getSource(),G,this._errorBoundary.getLastSeenErrorAndReset(),this._network.getLastUsedInitUrlAndReset(),this._store.getWarnings())})}getContext(){return{sdkKey:this._sdkKey,options:this._options,values:this._store.getValues(),user:JSON.parse(JSON.stringify(this._user)),errorBoundary:this._errorBoundary,session:d4.StatsigSession.get(this._sdkKey),stableID:d4.StableID.get(this._sdkKey)}}checkGate(A,B){return this.getFeatureGate(A,B).value}logEvent(A,B,Q){let D=typeof A==="string"?{eventName:A,value:B,metadata:Q}:A;this._logger.enqueue(Object.assign(Object.assign({},D),{user:this._user,time:Date.now()}))}_primeReadyRipcord(){this.$on("error",()=>{this.loadingStatus==="Loading"&&this._finalizeUpdate(null)})}_initializeAsyncImpl(A){return sl1(this,void 0,void 0,function*(){if(!d4.Storage.isReady())yield d4.Storage.isReadyResolver();return this._logger.start(),this.updateUserAsync(this._user,A)})}_finalizeUpdate(A){this._store.finalize(),this._setStatus("Ready",A)}_runPostUpdate(A,B){this.dataAdapter.getDataAsync(A,B,{priority:"low"}).catch((Q)=>{d4.Log.error("An error occurred after update.",Q)})}_resetForUser(A){this._logger.reset(),this._store.reset(),this._user=this._configureUser(A,this._options)}_configureUser(A,B){var Q;let D=d4._normalizeUser(A,B),Z=(Q=D.customIDs)===null||Q===void 0?void 0:Q.stableID;if(Z)d4.StableID.setOverride(Z,this._sdkKey);return D}_getFeatureGateImpl(A,B){var Q,D;let{result:Z,details:G}=this._store.getGate(A),F=d4._makeFeatureGate(A,G,Z),I=(D=(Q=this.overrideAdapter)===null||Q===void 0?void 0:Q.getGateOverride)===null||D===void 0?void 0:D.call(Q,F,this._user,B),Y=I!==null&&I!==void 0?I:F;return this._enqueueExposure(A,d4._createGateExposure(this._user,Y,this._store.getExposureMapping()),B),this.$emt({name:"gate_evaluation",gate:Y}),Y}_getDynamicConfigImpl(A,B){var Q,D;let{result:Z,details:G}=this._store.getConfig(A),F=d4._makeDynamicConfig(A,G,Z),I=(D=(Q=this.overrideAdapter)===null||Q===void 0?void 0:Q.getDynamicConfigOverride)===null||D===void 0?void 0:D.call(Q,F,this._user,B),Y=I!==null&&I!==void 0?I:F;return this._enqueueExposure(A,d4._createConfigExposure(this._user,Y,this._store.getExposureMapping()),B),this.$emt({name:"dynamic_config_evaluation",dynamicConfig:Y}),Y}_getExperimentImpl(A,B){var Q,D,Z,G;let{result:F,details:I}=this._store.getConfig(A),Y=d4._makeExperiment(A,I,F);if(Y.__evaluation!=null)Y.__evaluation.secondary_exposures=d4._mapExposures((D=(Q=Y.__evaluation)===null||Q===void 0?void 0:Q.secondary_exposures)!==null&&D!==void 0?D:[],this._store.getExposureMapping());let W=(G=(Z=this.overrideAdapter)===null||Z===void 0?void 0:Z.getExperimentOverride)===null||G===void 0?void 0:G.call(Z,Y,this._user,B),J=W!==null&&W!==void 0?W:Y;return this._enqueueExposure(A,d4._createConfigExposure(this._user,J,this._store.getExposureMapping()),B),this.$emt({name:"experiment_evaluation",experiment:J}),J}_getLayerImpl(A,B){var Q,D,Z;let{result:G,details:F}=this._store.getLayer(A),I=d4._makeLayer(A,F,G),Y=(D=(Q=this.overrideAdapter)===null||Q===void 0?void 0:Q.getLayerOverride)===null||D===void 0?void 0:D.call(Q,I,this._user,B);if(B===null||B===void 0?void 0:B.disableExposureLog)this._logger.incrementNonExposureCount(A);let W=d4._mergeOverride(I,Y,(Z=Y===null||Y===void 0?void 0:Y.__value)!==null&&Z!==void 0?Z:I.__value,(J)=>{if(B===null||B===void 0?void 0:B.disableExposureLog)return;this._enqueueExposure(A,d4._createLayerParameterExposure(this._user,W,J,this._store.getExposureMapping()),B)});return this.$emt({name:"layer_evaluation",layer:W}),W}_getParameterStoreImpl(A,B){var Q,D;let{result:Z,details:G}=this._store.getParamStore(A);this._logger.incrementNonExposureCount(A);let F={name:A,details:G,__configuration:Z,get:Ma0._makeParamStoreGetter(this,Z,B)},I=(D=(Q=this.overrideAdapter)===null||Q===void 0?void 0:Q.getParamStoreOverride)===null||D===void 0?void 0:D.call(Q,F,B);if(I!=null)F.__configuration=I.config,F.details=I.details,F.get=Ma0._makeParamStoreGetter(this,I.config,B);return F}}LB1.default=WV1});
var Ta0=E((GN)=>{var gP9=GN&&GN.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),uP9=GN&&GN.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))gP9(B,A,Q)};Object.defineProperty(GN,"__esModule",{value:!0});GN.StatsigClient=void 0;var Oa0=Ra0();GN.StatsigClient=Oa0.default;uP9(sj(),GN);__STATSIG__=Object.assign(Object.assign({},__STATSIG__!==null&&__STATSIG__!==void 0?__STATSIG__:{}),{StatsigClient:Oa0.default});GN.default=__STATSIG__});
var Un0=E((aj)=>{var Vn0=aj&&aj.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(aj,"__esModule",{value:!0});aj._isDomainFailure=aj.NetworkFallbackResolver=void 0;var zT9=Xn0(),ET9=al(),UT9=jW(),gl1=VO(),Cn0=604800000,wT9=14400000;class Hn0{constructor(A){var B;this._fallbackInfo=null,this._errorBoundary=null,this._dnsQueryCooldowns={},this._networkOverrideFunc=(B=A.networkConfig)===null||B===void 0?void 0:B.networkOverrideFunc}setErrorBoundary(A){this._errorBoundary=A}tryBumpExpiryTime(A,B){var Q;let D=(Q=this._fallbackInfo)===null||Q===void 0?void 0:Q[B.endpoint];if(!D)return;D.expiryTime=Date.now()+Cn0,hl1(A,Object.assign(Object.assign({},this._fallbackInfo),{[B.endpoint]:D}))}getActiveFallbackUrl(A,B){var Q,D;let Z=this._fallbackInfo;if(Z==null)Z=(Q=$T9(A))!==null&&Q!==void 0?Q:{},this._fallbackInfo=Z;let G=Z[B.endpoint];if(!G||Date.now()>((D=G.expiryTime)!==null&&D!==void 0?D:0))return delete Z[B.endpoint],this._fallbackInfo=Z,hl1(A,this._fallbackInfo),null;if(G.url)return G.url;return null}getFallbackFromProvided(A){let B=Kn0(A);if(B)return A.replace(B,"");return null}tryFetchUpdatedFallbackInfo(A,B,Q,D){var Z,G;return Vn0(this,void 0,void 0,function*(){try{if(!zn0(Q,D))return!1;let I=B.customUrl==null&&B.fallbackUrls==null?yield this._tryFetchFallbackUrlsFromNetwork(B):B.fallbackUrls,Y=this._pickNewFallbackUrl((Z=this._fallbackInfo)===null||Z===void 0?void 0:Z[B.endpoint],I);if(!Y)return!1;return this._updateFallbackInfoWithNewUrl(A,B.endpoint,Y),!0}catch(F){return(G=this._errorBoundary)===null||G===void 0||G.logError("tryFetchUpdatedFallbackInfo",F),!1}})}_updateFallbackInfoWithNewUrl(A,B,Q){var D,Z,G;let F={url:Q,expiryTime:Date.now()+Cn0,previous:[]},I=(D=this._fallbackInfo)===null||D===void 0?void 0:D[B];if(I)F.previous.push(...I.previous);if(F.previous.length>10)F.previous=[];let Y=(G=(Z=this._fallbackInfo)===null||Z===void 0?void 0:Z[B])===null||G===void 0?void 0:G.url;if(Y!=null)F.previous.push(Y);this._fallbackInfo=Object.assign(Object.assign({},this._fallbackInfo),{[B]:F}),hl1(A,this._fallbackInfo)}_tryFetchFallbackUrlsFromNetwork(A){var B;return Vn0(this,void 0,void 0,function*(){let Q=this._dnsQueryCooldowns[A.endpoint];if(Q&&Date.now()<Q)return null;this._dnsQueryCooldowns[A.endpoint]=Date.now()+wT9;let D=[],Z=yield zT9._fetchTxtRecords((B=this._networkOverrideFunc)!==null&&B!==void 0?B:fetch),G=Kn0(A.defaultUrl);for(let F of Z){if(!F.startsWith(A.endpointDnsKey+"="))continue;let I=F.split("=");if(I.length>1){let Y=I[1];if(Y.endsWith("/"))Y=Y.slice(0,-1);D.push(`https://${Y}${G}`)}}return D})}_pickNewFallbackUrl(A,B){var Q;if(B==null)return null;let D=new Set((Q=A===null||A===void 0?void 0:A.previous)!==null&&Q!==void 0?Q:[]),Z=A===null||A===void 0?void 0:A.url,G=null;for(let F of B){let I=F.endsWith("/")?F.slice(0,-1):F;if(!D.has(F)&&I!==Z){G=I;break}}return G}}aj.NetworkFallbackResolver=Hn0;function zn0(A,B){var Q;let D=(Q=A===null||A===void 0?void 0:A.toLowerCase())!==null&&Q!==void 0?Q:"";return B||D.includes("uncaught exception")||D.includes("failed to fetch")||D.includes("networkerror when attempting to fetch resource")}aj._isDomainFailure=zn0;function En0(A){return`statsig.network_fallback.${ET9._DJB2(A)}`}function hl1(A,B){let Q=En0(A);if(!B||Object.keys(B).length===0){gl1.Storage.removeItem(Q);return}gl1.Storage.setItem(Q,JSON.stringify(B))}function $T9(A){let B=En0(A),Q=gl1.Storage.getItem(B);if(!Q)return null;try{return JSON.parse(Q)}catch(D){return UT9.Log.error("Failed to parse FallbackInfo"),null}}function Kn0(A){try{return new URL(A).pathname}catch(B){return null}}});
var VO=E((Ei0)=>{Object.defineProperty(Ei0,"__esModule",{value:!0});Ei0._setObjectInStorage=Ei0._getObjectFromStorage=Ei0.Storage=void 0;var EO9=jW(),UO9=xf(),UB1={},Rl1={isReady:()=>!0,isReadyResolver:()=>null,getProviderName:()=>"InMemory",getItem:(A)=>UB1[A]?UB1[A]:null,setItem:(A,B)=>{UB1[A]=B},removeItem:(A)=>{delete UB1[A]},getAllKeys:()=>Object.keys(UB1)},dX1=null;try{let A=UO9._getWindowSafe();if(A&&A.localStorage&&typeof A.localStorage.getItem==="function")dX1={isReady:()=>!0,isReadyResolver:()=>null,getProviderName:()=>"LocalStorage",getItem:(B)=>A.localStorage.getItem(B),setItem:(B,Q)=>A.localStorage.setItem(B,Q),removeItem:(B)=>A.localStorage.removeItem(B),getAllKeys:()=>Object.keys(A.localStorage)}}catch(A){EO9.Log.warn("Failed to setup localStorageProvider.")}var Ml1=dX1!==null&&dX1!==void 0?dX1:Rl1,ZN=Ml1;function wO9(A){try{return A()}catch(B){if(B instanceof Error&&B.name==="SecurityError")return Ei0.Storage._setProvider(Rl1),null;throw B}}Ei0.Storage={isReady:()=>ZN.isReady(),isReadyResolver:()=>ZN.isReadyResolver(),getProviderName:()=>ZN.getProviderName(),getItem:(A)=>wO9(()=>ZN.getItem(A)),setItem:(A,B)=>ZN.setItem(A,B),removeItem:(A)=>ZN.removeItem(A),getAllKeys:()=>ZN.getAllKeys(),_setProvider:(A)=>{Ml1=A,ZN=A},_setDisabled:(A)=>{if(A)ZN=Rl1;else ZN=Ml1}};function $O9(A){let B=Ei0.Storage.getItem(A);return JSON.parse(B!==null&&B!==void 0?B:"null")}Ei0._getObjectFromStorage=$O9;function qO9(A,B){Ei0.Storage.setItem(A,JSON.stringify(B))}Ei0._setObjectInStorage=qO9});
var Xa0=E((Ja0)=>{Object.defineProperty(Ja0,"__esModule",{value:!0});var ff=sj();class Wa0{constructor(A){this._sdkKey=A,this._rawValues=null,this._values=null,this._source="Uninitialized",this._lcut=0,this._receivedAt=0,this._bootstrapMetadata=null,this._warnings=new Set}reset(){this._values=null,this._rawValues=null,this._source="Loading",this._lcut=0,this._receivedAt=0,this._bootstrapMetadata=null}finalize(){if(this._values)return;this._source="NoValues"}getValues(){return this._rawValues?ff._typedJsonParse(this._rawValues,"has_updates","EvaluationStoreValues"):null}setValues(A,B){var Q;if(!A)return!1;let D=ff._typedJsonParse(A.data,"has_updates","EvaluationResponse");if(D==null)return!1;if(this._source=A.source,(D===null||D===void 0?void 0:D.has_updates)!==!0)return!0;if(this._rawValues=A.data,this._lcut=D.time,this._receivedAt=A.receivedAt,this._values=D,this._bootstrapMetadata=this._extractBootstrapMetadata(A.source,D),A.source&&D.user)this._setWarningState(B,D);return ff.SDKFlags.setFlags(this._sdkKey,(Q=D.sdk_flags)!==null&&Q!==void 0?Q:{}),!0}getWarnings(){if(this._warnings.size===0)return;return Array.from(this._warnings)}getGate(A){var B;return this._getDetailedStoreResult((B=this._values)===null||B===void 0?void 0:B.feature_gates,A)}getConfig(A){var B;return this._getDetailedStoreResult((B=this._values)===null||B===void 0?void 0:B.dynamic_configs,A)}getLayer(A){var B;return this._getDetailedStoreResult((B=this._values)===null||B===void 0?void 0:B.layer_configs,A)}getParamStore(A){var B;return this._getDetailedStoreResult((B=this._values)===null||B===void 0?void 0:B.param_stores,A)}getSource(){return this._source}getExposureMapping(){var A;return(A=this._values)===null||A===void 0?void 0:A.exposures}_extractBootstrapMetadata(A,B){if(A!=="Bootstrap")return null;let Q={};if(B.user)Q.user=B.user;if(B.sdkInfo)Q.generatorSDKInfo=B.sdkInfo;return Q.lcut=B.time,Q}_getDetailedStoreResult(A,B){let Q=null;if(A)Q=A[B]?A[B]:A[ff._DJB2(B)];return{result:Q,details:this._getDetails(Q==null)}}_setWarningState(A,B){var Q;let D=ff.StableID.get(this._sdkKey);if(((Q=A.customIDs)===null||Q===void 0?void 0:Q.stableID)!==D){this._warnings.add("StableIDMismatch");return}if("user"in B){let Z=B.user;if(ff._getFullUserHash(A)!==ff._getFullUserHash(Z))this._warnings.add("PartialUserMatch")}}getCurrentSourceDetails(){if(this._source==="Uninitialized"||this._source==="NoValues")return{reason:this._source};let A={reason:this._source,lcut:this._lcut,receivedAt:this._receivedAt};if(this._warnings.size>0)A.warnings=Array.from(this._warnings);return A}_getDetails(A){var B,Q;let D=this.getCurrentSourceDetails(),Z=D.reason,G=(B=D.warnings)!==null&&B!==void 0?B:[];if(this._source==="Bootstrap"&&G.length>0)Z=Z+G[0];if(Z!=="Uninitialized"&&Z!=="NoValues")Z=`${Z}:${A?"Unrecognized":"Recognized"}`;let F=this._source==="Bootstrap"?(Q=this._bootstrapMetadata)!==null&&Q!==void 0?Q:void 0:void 0;if(F)D.bootstrapMetadata=F;return Object.assign(Object.assign({},D),{reason:Z})}}Ja0.default=Wa0});
var Xn0=E((Ap)=>{var WT9=Ap&&Ap.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(Ap,"__esModule",{value:!0});Ap._fetchTxtRecords=void 0;var JT9=new Uint8Array([0,0,1,0,0,1,0,0,0,0,0,0,13,102,101,97,116,117,114,101,97,115,115,101,116,115,3,111,114,103,0,0,16,0,1]),XT9="https://cloudflare-dns.com/dns-query",VT9=["i","e","d"],CT9=200;function KT9(A){return WT9(this,void 0,void 0,function*(){let B=yield A(XT9,{method:"POST",headers:{"Content-Type":"application/dns-message",Accept:"application/dns-message"},body:JT9});if(!B.ok){let Z=new Error("Failed to fetch TXT records from DNS");throw Z.name="DnsTxtFetchError",Z}let Q=yield B.arrayBuffer(),D=new Uint8Array(Q);return HT9(D)})}Ap._fetchTxtRecords=KT9;function HT9(A){let B=A.findIndex((D,Z)=>Z<CT9&&String.fromCharCode(D)==="="&&VT9.includes(String.fromCharCode(A[Z-1])));if(B===-1){let D=new Error("Failed to parse TXT records from DNS");throw D.name="DnsTxtParseError",D}let Q="";for(let D=B-1;D<A.length;D++)Q+=String.fromCharCode(A[D]);return Q.split(",")}});
var Yn0=E((In0)=>{Object.defineProperty(In0,"__esModule",{value:!0})});
var Za0=E((Da0)=>{Object.defineProperty(Da0,"__esModule",{value:!0})});
var Zn0=E((Dn0)=>{Object.defineProperty(Dn0,"__esModule",{value:!0})});
var _f=E((tp0)=>{var Cl1,Kl1,Hl1;Object.defineProperty(tp0,"__esModule",{value:!0});tp0._getInstance=tp0._getStatsigGlobalFlag=tp0._getStatsigGlobal=void 0;var vR9=jW(),bR9=()=>{return __STATSIG__?__STATSIG__:xX1};tp0._getStatsigGlobal=bR9;var fR9=(A)=>{return tp0._getStatsigGlobal()[A]};tp0._getStatsigGlobalFlag=fR9;var hR9=(A)=>{let B=tp0._getStatsigGlobal();if(!A){if(B.instances&&Object.keys(B.instances).length>1)vR9.Log.warn("Call made to Statsig global instance without an SDK key but there is more than one client instance. If you are using mulitple clients, please specify the SDK key.");return B.firstInstance}return B.instances&&B.instances[A]};tp0._getInstance=hR9;var pl="__STATSIG__",sp0=typeof window!=="undefined"?window:{},rp0=typeof global!=="undefined"?global:{},op0=typeof globalThis!=="undefined"?globalThis:{},xX1=(Hl1=(Kl1=(Cl1=sp0[pl])!==null&&Cl1!==void 0?Cl1:rp0[pl])!==null&&Kl1!==void 0?Kl1:op0[pl])!==null&&Hl1!==void 0?Hl1:{instance:tp0._getInstance};sp0[pl]=xX1;rp0[pl]=xX1;op0[pl]=xX1});
var _l1=E((gi0)=>{Object.defineProperty(gi0,"__esModule",{value:!0});gi0._getFullUserHash=gi0._normalizeUser=void 0;var pO9=al(),iO9=jW();function nO9(A,B,Q){try{let D=JSON.parse(JSON.stringify(A));if(B!=null&&B.environment!=null)D.statsigEnvironment=B.environment;else if(Q!=null)D.statsigEnvironment={tier:Q};return D}catch(D){return iO9.Log.error("Failed to JSON.stringify user"),{statsigEnvironment:void 0}}}gi0._normalizeUser=nO9;function aO9(A){return A?pO9._DJB2Object(A):null}gi0._getFullUserHash=aO9});
var ai0=E((nj)=>{var vl1=nj&&nj.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(nj,"__esModule",{value:!0});nj._makeDataAdapterResult=nj.DataAdapterCore=void 0;var tX1=jW(),tO9=oX1(),eX1=_l1(),ij=VO(),ci0=xl1(),li0=10;class pi0{constructor(A,B){this._adapterName=A,this._cacheSuffix=B,this._options=null,this._sdkKey=null,this._lastModifiedStoreKey=`statsig.last_modified_time.${B}`,this._inMemoryCache=new ii0}attach(A,B){this._sdkKey=A,this._options=B}getDataSync(A){let B=A&&eX1._normalizeUser(A,this._options),Q=this._getCacheKey(B),D=this._inMemoryCache.get(Q,B);if(D)return D;let Z=this._loadFromCache(Q);if(Z)return this._inMemoryCache.add(Q,Z),this._inMemoryCache.get(Q,B);return null}setData(A,B){let Q=B&&eX1._normalizeUser(B,this._options),D=this._getCacheKey(Q);this._inMemoryCache.add(D,AV1("Bootstrap",A,null,Q))}_getDataAsyncImpl(A,B,Q){return vl1(this,void 0,void 0,function*(){if(!ij.Storage.isReady())yield ij.Storage.isReadyResolver();let D=A!==null&&A!==void 0?A:this.getDataSync(B),Z=[this._fetchAndPrepFromNetwork(D,B,Q)];if(Q===null||Q===void 0?void 0:Q.timeoutMs)Z.push(new Promise((G)=>setTimeout(G,Q.timeoutMs)).then(()=>{return tX1.Log.debug("Fetching latest value timed out"),null}));return yield Promise.race(Z)})}_prefetchDataImpl(A,B){return vl1(this,void 0,void 0,function*(){let Q=A&&eX1._normalizeUser(A,this._options),D=this._getCacheKey(Q),Z=yield this._getDataAsyncImpl(null,Q,B);if(Z)this._inMemoryCache.add(D,Object.assign(Object.assign({},Z),{source:"Prefetch"}))})}_fetchAndPrepFromNetwork(A,B,Q){var D;return vl1(this,void 0,void 0,function*(){let Z=(D=A===null||A===void 0?void 0:A.data)!==null&&D!==void 0?D:null,G=A!=null&&this._isCachedResultValidFor204(A,B),F=yield this._fetchFromNetwork(Z,B,Q,G);if(!F)return tX1.Log.debug("No response returned for latest value"),null;let I=ci0._typedJsonParse(F,"has_updates","Response"),Y=this._getSdkKey(),W=tO9.StableID.get(Y),J=null;if((I===null||I===void 0?void 0:I.has_updates)===!0)J=AV1("Network",F,W,B);else if(Z&&(I===null||I===void 0?void 0:I.has_updates)===!1)J=AV1("NetworkNotModified",Z,W,B);else return null;let X=this._getCacheKey(B);return this._inMemoryCache.add(X,J),this._writeToCache(X,J),J})}_getSdkKey(){if(this._sdkKey!=null)return this._sdkKey;return tX1.Log.error(`${this._adapterName} is not attached to a Client`),""}_loadFromCache(A){var B;let Q=(B=ij.Storage.getItem)===null||B===void 0?void 0:B.call(ij.Storage,A);if(Q==null)return null;let D=ci0._typedJsonParse(Q,"source","Cached Result");return D?Object.assign(Object.assign({},D),{source:"Cache"}):null}_writeToCache(A,B){ij.Storage.setItem(A,JSON.stringify(B)),this._runLocalStorageCacheEviction(A)}_runLocalStorageCacheEviction(A){var B;let Q=(B=ij._getObjectFromStorage(this._lastModifiedStoreKey))!==null&&B!==void 0?B:{};Q[A]=Date.now();let D=ni0(Q,li0);if(D)delete Q[D],ij.Storage.removeItem(D);ij._setObjectInStorage(this._lastModifiedStoreKey,Q)}}nj.DataAdapterCore=pi0;function AV1(A,B,Q,D){return{source:A,data:B,receivedAt:Date.now(),stableID:Q,fullUserHash:eX1._getFullUserHash(D)}}nj._makeDataAdapterResult=AV1;class ii0{constructor(){this._data={}}get(A,B){var Q;let D=this._data[A],Z=D===null||D===void 0?void 0:D.stableID,G=(Q=B===null||B===void 0?void 0:B.customIDs)===null||Q===void 0?void 0:Q.stableID;if(G&&Z&&G!==Z)return tX1.Log.warn("'StatsigUser.customIDs.stableID' mismatch"),null;return D}add(A,B){let Q=ni0(this._data,li0-1);if(Q)delete this._data[Q];this._data[A]=B}merge(A){this._data=Object.assign(Object.assign({},this._data),A)}}function ni0(A,B){let Q=Object.keys(A);if(Q.length<=B)return null;return Q.reduce((D,Z)=>{let G=A[D],F=A[Z];if(typeof G==="object"&&typeof F==="object")return F.receivedAt<G.receivedAt?Z:D;return F<G?Z:D})}});
var al=E((Qi0)=>{Object.defineProperty(Qi0,"__esModule",{value:!0});Qi0._getSortedObject=Qi0._DJB2Object=Qi0._DJB2=void 0;var pR9=fX1(),iR9=(A)=>{let B=0;for(let Q=0;Q<A.length;Q++){let D=A.charCodeAt(Q);B=(B<<5)-B+D,B=B&B}return String(B>>>0)};Qi0._DJB2=iR9;var nR9=(A,B)=>{return Qi0._DJB2(JSON.stringify(Qi0._getSortedObject(A,B)))};Qi0._DJB2Object=nR9;var aR9=(A,B)=>{if(A==null)return null;let Q=Object.keys(A).sort(),D={};return Q.forEach((Z)=>{let G=A[Z];if(B===0||pR9._typeOf(G)!=="object"){D[Z]=G;return}D[Z]=Qi0._getSortedObject(G,B!=null?B-1:B)}),D};Qi0._getSortedObject=aR9});
var an0=E((in0)=>{Object.defineProperty(in0,"__esModule",{value:!0});in0.DataAdapterCachePrefix=void 0;in0.DataAdapterCachePrefix="statsig.cached"});
var bX1=E((ep0)=>{Object.defineProperty(ep0,"__esModule",{value:!0});ep0.Diagnostics=void 0;var vX1=new Map,Ul1="start",wl1="end",uR9="statsig::diagnostics";ep0.Diagnostics={_getMarkers:(A)=>{return vX1.get(A)},_markInitOverallStart:(A)=>{nl(A,il({},Ul1,"overall"))},_markInitOverallEnd:(A,B,Q)=>{nl(A,il({success:B,error:B?void 0:{name:"InitializeError",message:"Failed to initialize"},evaluationDetails:Q},wl1,"overall"))},_markInitNetworkReqStart:(A,B)=>{nl(A,il(B,Ul1,"initialize","network_request"))},_markInitNetworkReqEnd:(A,B)=>{nl(A,il(B,wl1,"initialize","network_request"))},_markInitProcessStart:(A)=>{nl(A,il({},Ul1,"initialize","process"))},_markInitProcessEnd:(A,B)=>{nl(A,il(B,wl1,"initialize","process"))},_clearMarkers:(A)=>{vX1.delete(A)},_formatError(A){if(!(A&&typeof A==="object"))return;return{code:$l1(A,"code"),name:$l1(A,"name"),message:$l1(A,"message")}},_getDiagnosticsData(A,B,Q,D){var Z;return{success:(A===null||A===void 0?void 0:A.ok)===!0,statusCode:A===null||A===void 0?void 0:A.status,sdkRegion:(Z=A===null||A===void 0?void 0:A.headers)===null||Z===void 0?void 0:Z.get("x-statsig-region"),isDelta:Q.includes('"is_delta":true')===!0?!0:void 0,attempt:B,error:ep0.Diagnostics._formatError(D)}},_enqueueDiagnosticsEvent(A,B,Q,D){let Z=ep0.Diagnostics._getMarkers(Q);if(Z==null||Z.length<=0)return-1;let G=Z[Z.length-1].timestamp-Z[0].timestamp;ep0.Diagnostics._clearMarkers(Q);let F=mR9(A,{context:"initialize",markers:Z.slice(),statsigOptions:D});return B.enqueue(F),G}};function il(A,B,Q,D){return Object.assign({key:Q,action:B,step:D,timestamp:Date.now()},A)}function mR9(A,B){return{eventName:uR9,user:A,value:null,metadata:B,time:Date.now()}}function nl(A,B){var Q;let D=(Q=vX1.get(A))!==null&&Q!==void 0?Q:[];D.push(B),vX1.set(A,D)}function $l1(A,B){if(B in A)return A[B];return}});
var bl1=E((CO)=>{var eO9=CO&&CO.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(CO,"__esModule",{value:!0});CO.ErrorBoundary=CO.EXCEPTION_ENDPOINT=void 0;var AT9=jW(),BT9=BV1(),QT9=qB1();CO.EXCEPTION_ENDPOINT="https://statsigapi.net/v1/sdk_exception";var Bn0="[Statsig] UnknownError";class Qn0{constructor(A,B,Q,D){this._sdkKey=A,this._options=B,this._emitter=Q,this._lastSeenError=D,this._seen=new Set}wrap(A){try{let B=A;ZT9(B).forEach((Q)=>{let D=B[Q];if("$EB"in D)return;B[Q]=(...Z)=>{return this._capture(Q,()=>D.apply(A,Z))},B[Q].$EB=!0})}catch(B){this._onError("eb:wrap",B)}}logError(A,B){this._onError(A,B)}getLastSeenErrorAndReset(){let A=this._lastSeenError;return this._lastSeenError=void 0,A!==null&&A!==void 0?A:null}attachErrorIfNoneExists(A){if(this._lastSeenError)return;this._lastSeenError=An0(A)}_capture(A,B){try{let Q=B();if(Q&&Q instanceof Promise)return Q.catch((D)=>this._onError(A,D));return Q}catch(Q){return this._onError(A,Q),null}}_onError(A,B){try{AT9.Log.warn(`Caught error in ${A}`,{error:B}),(()=>eO9(this,void 0,void 0,function*(){var D,Z,G,F,I,Y,W;let J=B?B:Error(Bn0),X=J instanceof Error,V=X?J.name:"No Name",C=An0(J);if(this._lastSeenError=C,this._seen.has(V))return;if(this._seen.add(V),(Z=(D=this._options)===null||D===void 0?void 0:D.networkConfig)===null||Z===void 0?void 0:Z.preventAllNetworkTraffic){(G=this._emitter)===null||G===void 0||G.call(this,{name:"error",error:B,tag:A});return}let K=BT9.SDKType._get(this._sdkKey),H=QT9.StatsigMetadataProvider.get(),z=X?J.stack:DT9(J),$=JSON.stringify(Object.assign({tag:A,exception:V,info:z},Object.assign(Object.assign({},H),{sdkType:K})));yield((Y=(I=(F=this._options)===null||F===void 0?void 0:F.networkConfig)===null||I===void 0?void 0:I.networkOverrideFunc)!==null&&Y!==void 0?Y:fetch)(CO.EXCEPTION_ENDPOINT,{method:"POST",headers:{"STATSIG-API-KEY":this._sdkKey,"STATSIG-SDK-TYPE":String(K),"STATSIG-SDK-VERSION":String(H.sdkVersion),"Content-Type":"application/json"},body:$}),(W=this._emitter)===null||W===void 0||W.call(this,{name:"error",error:B,tag:A})}))().then(()=>{}).catch(()=>{})}catch(Q){}}}CO.ErrorBoundary=Qn0;function An0(A){if(A instanceof Error)return A;else if(typeof A==="string")return new Error(A);else return new Error("An unknown error occurred.")}function DT9(A){try{return JSON.stringify(A)}catch(B){return Bn0}}function ZT9(A){let B=new Set,Q=Object.getPrototypeOf(A);while(Q&&Q!==Object.prototype)Object.getOwnPropertyNames(Q).filter((D)=>typeof(Q===null||Q===void 0?void 0:Q[D])==="function").forEach((D)=>B.add(D)),Q=Object.getPrototypeOf(Q);return Array.from(B)}});
var cn0=E((dn0)=>{Object.defineProperty(dn0,"__esModule",{value:!0})});
var dl1=E((Sn0)=>{Object.defineProperty(Sn0,"__esModule",{value:!0});Sn0.ErrorTag=void 0;Sn0.ErrorTag={NetworkError:"NetworkError"}});
var fX1=E((Ai0)=>{Object.defineProperty(Ai0,"__esModule",{value:!0});Ai0._isTypeMatch=Ai0._typeOf=void 0;function dR9(A){return Array.isArray(A)?"array":typeof A}Ai0._typeOf=dR9;function cR9(A,B){let Q=(D)=>Array.isArray(D)?"array":typeof D;return Q(A)===Q(B)}Ai0._isTypeMatch=cR9});
var fl1=E((Wn0)=>{Object.defineProperty(Wn0,"__esModule",{value:!0});Wn0.createMemoKey=Wn0.MemoPrefix=void 0;Wn0.MemoPrefix={_gate:"g",_dynamicConfig:"c",_experiment:"e",_layer:"l",_paramStore:"p"};var GT9=new Set([]),FT9=new Set(["userPersistedValues"]);function IT9(A,B,Q){let D=`${A}|${B}`;if(!Q)return D;for(let Z of Object.keys(Q)){if(FT9.has(Z))return;if(GT9.has(Z))D+=`|${Z}=true`;else D+=`|${Z}=${Q[Z]}`}return D}Wn0.createMemoKey=IT9});
var gn0=E((Qp)=>{var Bp=Qp&&Qp.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(Qp,"__esModule",{value:!0});Qp.NetworkCore=void 0;_f();var kn0=_f(),cl1=bX1(),bf=jW(),tU=EB1(),kT9=Un0(),yT9=ul1(),xn0=BV1(),_T9=xf(),vn0=DV1(),xT9=oX1(),vT9=dl1(),bn0=qB1(),bT9=nX1(),fT9=1e4,hT9=500,gT9=30000,uT9=1000,fn0=50,mT9=fn0/uT9,dT9=new Set([408,500,502,503,504,522,524,599]);class hn0{constructor(A,B){if(this._emitter=B,this._errorBoundary=null,this._timeout=fT9,this._netConfig={},this._options={},this._leakyBucket={},this._lastUsedInitUrl=null,A)this._options=A;if(this._options.networkConfig)this._netConfig=this._options.networkConfig;if(this._netConfig.networkTimeoutMs)this._timeout=this._netConfig.networkTimeoutMs;this._fallbackResolver=new kT9.NetworkFallbackResolver(this._options)}setErrorBoundary(A){this._errorBoundary=A,this._errorBoundary.wrap(this),this._errorBoundary.wrap(this._fallbackResolver),this._fallbackResolver.setErrorBoundary(A)}isBeaconSupported(){return typeof navigator!=="undefined"&&typeof navigator.sendBeacon==="function"}getLastUsedInitUrlAndReset(){let A=this._lastUsedInitUrl;return this._lastUsedInitUrl=null,A}beacon(A){return Bp(this,void 0,void 0,function*(){if(!yn0(A))return!1;let B=this._getInternalRequestArgs("POST",A);yield this._tryToCompressBody(B);let Q=yield this._getPopulatedURL(B),D=navigator;return D.sendBeacon.bind(D)(Q,B.body)})}post(A){return Bp(this,void 0,void 0,function*(){let B=this._getInternalRequestArgs("POST",A);return this._tryEncodeBody(B),yield this._tryToCompressBody(B),this._sendRequest(B)})}get(A){let B=this._getInternalRequestArgs("GET",A);return this._sendRequest(B)}_sendRequest(A){var B,Q,D,Z;return Bp(this,void 0,void 0,function*(){if(!yn0(A))return null;if(this._netConfig.preventAllNetworkTraffic)return null;let{method:G,body:F,retries:I,attempt:Y}=A,W=A.urlConfig.endpoint;if(this._isRateLimited(W))return bf.Log.warn(`Request to ${W} was blocked because you are making requests too frequently.`),null;let J=Y!==null&&Y!==void 0?Y:1,X=typeof AbortController!=="undefined"?new AbortController:null,V=setTimeout(()=>{X===null||X===void 0||X.abort(`Timeout of ${this._timeout}ms expired.`)},this._timeout),C=yield this._getPopulatedURL(A),K=null,H=bT9._isUnloading();try{let z={method:G,body:F,headers:Object.assign({},A.headers),signal:X===null||X===void 0?void 0:X.signal,priority:A.priority,keepalive:H};iT9(A,J);let $=this._leakyBucket[W];if($)$.lastRequestTime=Date.now(),this._leakyBucket[W]=$;if(K=yield((B=this._netConfig.networkOverrideFunc)!==null&&B!==void 0?B:fetch)(C,z),clearTimeout(V),!K.ok){let O=yield K.text().catch(()=>"No Text"),R=new Error(`NetworkError: ${C} ${O}`);throw R.name="NetworkError",R}let N=yield K.text();return _n0(A,K,J,N),this._fallbackResolver.tryBumpExpiryTime(A.sdkKey,A.urlConfig),{body:N,code:K.status}}catch(z){let $=lT9(X,z),L=pT9(X);if(_n0(A,K,J,"",z),yield this._fallbackResolver.tryFetchUpdatedFallbackInfo(A.sdkKey,A.urlConfig,$,L))A.fallbackUrl=this._fallbackResolver.getActiveFallbackUrl(A.sdkKey,A.urlConfig);if(!I||J>I||!dT9.has((Q=K===null||K===void 0?void 0:K.status)!==null&&Q!==void 0?Q:500)){(D=this._emitter)===null||D===void 0||D.call(this,{name:"error",error:z,tag:vT9.ErrorTag.NetworkError,requestArgs:A});let O=`A networking error occurred during ${G} request to ${C}.`;return bf.Log.error(O,$,z),(Z=this._errorBoundary)===null||Z===void 0||Z.attachErrorIfNoneExists(O),null}return yield nT9(J),this._sendRequest(Object.assign(Object.assign({},A),{retries:I,attempt:J+1}))}})}_isRateLimited(A){var B;let Q=Date.now(),D=(B=this._leakyBucket[A])!==null&&B!==void 0?B:{count:0,lastRequestTime:Q},Z=Q-D.lastRequestTime,G=Math.floor(Z*mT9);if(D.count=Math.max(0,D.count-G),D.count>=fn0)return!0;return D.count+=1,D.lastRequestTime=Q,this._leakyBucket[A]=D,!1}_getPopulatedURL(A){var B;return Bp(this,void 0,void 0,function*(){let Q=(B=A.fallbackUrl)!==null&&B!==void 0?B:A.urlConfig.getUrl();if(A.urlConfig.endpoint===tU.Endpoint._initialize||A.urlConfig.endpoint===tU.Endpoint._download_config_specs)this._lastUsedInitUrl=Q;let D=Object.assign({[tU.NetworkParam.SdkKey]:A.sdkKey,[tU.NetworkParam.SdkType]:xn0.SDKType._get(A.sdkKey),[tU.NetworkParam.SdkVersion]:bn0.SDK_VERSION,[tU.NetworkParam.Time]:String(Date.now()),[tU.NetworkParam.SessionID]:vn0.SessionID.get(A.sdkKey)},A.params),Z=Object.keys(D).map((G)=>{return`${encodeURIComponent(G)}=${encodeURIComponent(D[G])}`}).join("&");return`${Q}${Z?`?${Z}`:""}`})}_tryEncodeBody(A){var B;let Q=_T9._getWindowSafe(),D=A.body;if(!A.isStatsigEncodable||this._options.disableStatsigEncoding||typeof D!=="string"||kn0._getStatsigGlobalFlag("no-encode")!=null||!(Q===null||Q===void 0?void 0:Q.btoa))return;try{A.body=Q.btoa(D).split("").reverse().join(""),A.params=Object.assign(Object.assign({},(B=A.params)!==null&&B!==void 0?B:{}),{[tU.NetworkParam.StatsigEncoded]:"1"})}catch(Z){bf.Log.warn(`Request encoding failed for ${A.urlConfig.getUrl()}`,Z)}}_tryToCompressBody(A){var B;return Bp(this,void 0,void 0,function*(){let Q=A.body;if(!A.isCompressable||this._options.disableCompression||typeof Q!=="string"||yT9.SDKFlags.get(A.sdkKey,"enable_log_event_compression")!==!0||kn0._getStatsigGlobalFlag("no-compress")!=null||typeof CompressionStream==="undefined"||typeof TextEncoder==="undefined")return;try{let D=new TextEncoder().encode(Q),Z=new CompressionStream("gzip"),G=Z.writable.getWriter();G.write(D).catch(bf.Log.error),G.close().catch(bf.Log.error);let F=Z.readable.getReader(),I=[],Y;while(!(Y=yield F.read()).done)I.push(Y.value);let W=I.reduce((V,C)=>V+C.length,0),J=new Uint8Array(W),X=0;for(let V of I)J.set(V,X),X+=V.length;A.body=J,A.params=Object.assign(Object.assign({},(B=A.params)!==null&&B!==void 0?B:{}),{[tU.NetworkParam.IsGzipped]:"1"})}catch(D){bf.Log.warn(`Request compression failed for ${A.urlConfig.getUrl()}`,D)}})}_getInternalRequestArgs(A,B){let Q=this._fallbackResolver.getActiveFallbackUrl(B.sdkKey,B.urlConfig),D=Object.assign(Object.assign({},B),{method:A,fallbackUrl:Q});if("data"in B)cT9(D,B.data);return D}}Qp.NetworkCore=hn0;var yn0=(A)=>{if(!A.sdkKey)return bf.Log.warn("Unable to make request without an SDK key"),!1;return!0},cT9=(A,B)=>{let{sdkKey:Q,fallbackUrl:D}=A,Z=xT9.StableID.get(Q),G=vn0.SessionID.get(Q),F=xn0.SDKType._get(Q);A.body=JSON.stringify(Object.assign(Object.assign({},B),{statsigMetadata:Object.assign(Object.assign({},bn0.StatsigMetadataProvider.get()),{stableID:Z,sessionID:G,sdkType:F,fallbackUrl:D})}))};function lT9(A,B){if((A===null||A===void 0?void 0:A.signal.aborted)&&typeof A.signal.reason==="string")return A.signal.reason;if(typeof B==="string")return B;if(B instanceof Error)return`${B.name}: ${B.message}`;return"Unknown Error"}function pT9(A){return(A===null||A===void 0?void 0:A.signal.aborted)&&typeof A.signal.reason==="string"&&A.signal.reason.includes("Timeout")||!1}function iT9(A,B){if(A.urlConfig.endpoint!==tU.Endpoint._initialize)return;cl1.Diagnostics._markInitNetworkReqStart(A.sdkKey,{attempt:B})}function _n0(A,B,Q,D,Z){if(A.urlConfig.endpoint!==tU.Endpoint._initialize)return;cl1.Diagnostics._markInitNetworkReqEnd(A.sdkKey,cl1.Diagnostics._getDiagnosticsData(B,Q,D,Z))}function nT9(A){return Bp(this,void 0,void 0,function*(){yield new Promise((B)=>setTimeout(B,Math.min(hT9*(A*A),gT9)))})}});
var jW=E((np0)=>{Object.defineProperty(np0,"__esModule",{value:!0});np0.Log=np0.LogLevel=void 0;var kR9=" DEBUG ",yR9="  INFO ",_R9="  WARN ",xR9=" ERROR ";function _X1(A){return A.unshift("[Statsig]"),A}np0.LogLevel={None:0,Error:1,Warn:2,Info:3,Debug:4};class yf{static info(...A){if(yf.level>=np0.LogLevel.Info)console.info(yR9,..._X1(A))}static debug(...A){if(yf.level>=np0.LogLevel.Debug)console.debug(kR9,..._X1(A))}static warn(...A){if(yf.level>=np0.LogLevel.Warn)console.warn(_R9,..._X1(A))}static error(...A){if(yf.level>=np0.LogLevel.Error)console.error(xR9,..._X1(A))}}np0.Log=yf;yf.level=np0.LogLevel.Warn});
var ki0=E((ji0)=>{Object.defineProperty(ji0,"__esModule",{value:!0})});
var kl1=E((tl)=>{var rl=tl&&tl.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(tl,"__esModule",{value:!0});tl.EventLogger=void 0;var kO9=zB1(),yO9=al(),wB1=jW(),Mi0=EB1(),jl1=xf(),_O9=Ll1(),ol=VO(),xO9=Ol1(),Ri0=nX1(),vO9=100,bO9=1e4,fO9=1000,hO9=600000,gO9=500,Oi0=200,$B1={},aX1={Startup:"startup",GainedFocus:"gained_focus"};class vf{static _safeFlushAndForget(A){var B;(B=$B1[A])===null||B===void 0||B.flush().catch(()=>{})}static _safeRetryFailedLogs(A){var B;(B=$B1[A])===null||B===void 0||B._retryFailedLogs(aX1.GainedFocus)}constructor(A,B,Q,D){var Z;this._sdkKey=A,this._emitter=B,this._network=Q,this._options=D,this._queue=[],this._lastExposureTimeMap={},this._nonExposedChecks={},this._hasRunQuickFlush=!1,this._creationTime=Date.now(),this._isLoggingDisabled=(D===null||D===void 0?void 0:D.disableLogging)===!0,this._maxQueueSize=(Z=D===null||D===void 0?void 0:D.loggingBufferMaxSize)!==null&&Z!==void 0?Z:vO9;let G=D===null||D===void 0?void 0:D.networkConfig;this._logEventUrlConfig=new xO9.UrlConfiguration(Mi0.Endpoint._rgstr,G===null||G===void 0?void 0:G.logEventUrl,G===null||G===void 0?void 0:G.api,G===null||G===void 0?void 0:G.logEventFallbackUrls)}setLoggingDisabled(A){this._isLoggingDisabled=A}enqueue(A){if(!this._shouldLogEvent(A))return;if(this._normalizeAndAppendEvent(A),this._quickFlushIfNeeded(),this._queue.length>this._maxQueueSize)vf._safeFlushAndForget(this._sdkKey)}incrementNonExposureCount(A){var B;let Q=(B=this._nonExposedChecks[A])!==null&&B!==void 0?B:0;this._nonExposedChecks[A]=Q+1}reset(){this._lastExposureTimeMap={}}start(){if(jl1._isServerEnv())return;$B1[this._sdkKey]=this,Ri0._subscribeToVisiblityChanged((A)=>{if(A==="background")vf._safeFlushAndForget(this._sdkKey);else if(A==="foreground")vf._safeRetryFailedLogs(this._sdkKey)}),this._retryFailedLogs(aX1.Startup),this._startBackgroundFlushInterval()}stop(){return rl(this,void 0,void 0,function*(){if(this._flushIntervalId)clearInterval(this._flushIntervalId),this._flushIntervalId=null;delete $B1[this._sdkKey],yield this.flush()})}flush(){return rl(this,void 0,void 0,function*(){if(this._appendAndResetNonExposedChecks(),this._queue.length===0)return;let A=this._queue;this._queue=[],yield this._sendEvents(A)})}_quickFlushIfNeeded(){if(this._hasRunQuickFlush)return;if(this._hasRunQuickFlush=!0,Date.now()-this._creationTime>Oi0)return;setTimeout(()=>vf._safeFlushAndForget(this._sdkKey),Oi0)}_shouldLogEvent(A){if(jl1._isServerEnv())return!1;if(!_O9._isExposureEvent(A))return!0;let B=A.user?A.user:{statsigEnvironment:void 0},Q=kO9._getUserStorageKey(this._sdkKey,B),D=A.metadata?A.metadata:{},Z=[A.eventName,Q,D.gate,D.config,D.ruleID,D.allocatedExperiment,D.parameterName,String(D.isExplicitParameter),D.reason].join("|"),G=this._lastExposureTimeMap[Z],F=Date.now();if(G&&F-G<hO9)return!1;if(Object.keys(this._lastExposureTimeMap).length>fO9)this._lastExposureTimeMap={};return this._lastExposureTimeMap[Z]=F,!0}_sendEvents(A){var B,Q;return rl(this,void 0,void 0,function*(){if(this._isLoggingDisabled)return this._saveFailedLogsToStorage(A),!1;try{let Z=Ri0._isUnloading()&&this._network.isBeaconSupported()&&((Q=(B=this._options)===null||B===void 0?void 0:B.networkConfig)===null||Q===void 0?void 0:Q.networkOverrideFunc)==null;if(this._emitter({name:"pre_logs_flushed",events:A}),(Z?yield this._sendEventsViaBeacon(A):yield this._sendEventsViaPost(A)).success)return this._emitter({name:"logs_flushed",events:A}),!0;else return wB1.Log.warn("Failed to flush events."),this._saveFailedLogsToStorage(A),!1}catch(D){return wB1.Log.warn("Failed to flush events."),!1}})}_sendEventsViaPost(A){var B;return rl(this,void 0,void 0,function*(){let Q=yield this._network.post(this._getRequestData(A)),D=(B=Q===null||Q===void 0?void 0:Q.code)!==null&&B!==void 0?B:-1;return{success:D>=200&&D<300}})}_sendEventsViaBeacon(A){return rl(this,void 0,void 0,function*(){return{success:yield this._network.beacon(this._getRequestData(A))}})}_getRequestData(A){return{sdkKey:this._sdkKey,data:{events:A},urlConfig:this._logEventUrlConfig,retries:3,isCompressable:!0,params:{[Mi0.NetworkParam.EventCount]:String(A.length)}}}_saveFailedLogsToStorage(A){while(A.length>gO9)A.shift();let B=this._getStorageKey();try{ol._setObjectInStorage(B,A)}catch(Q){wB1.Log.warn("Unable to save failed logs to storage")}}_retryFailedLogs(A){let B=this._getStorageKey();(()=>rl(this,void 0,void 0,function*(){if(!ol.Storage.isReady())yield ol.Storage.isReadyResolver();let Q=ol._getObjectFromStorage(B);if(!Q)return;if(A===aX1.Startup)ol.Storage.removeItem(B);if((yield this._sendEvents(Q))&&A===aX1.GainedFocus)ol.Storage.removeItem(B)}))().catch(()=>{wB1.Log.warn("Failed to flush stored logs")})}_getStorageKey(){return`statsig.failed_logs.${yO9._DJB2(this._sdkKey)}`}_normalizeAndAppendEvent(A){if(A.user)A.user=Object.assign({},A.user),delete A.user.privateAttributes;let B={},Q=this._getCurrentPageUrl();if(Q)B.statsigMetadata={currentPage:Q};let D=Object.assign(Object.assign({},A),B);wB1.Log.debug("Enqueued Event:",D),this._queue.push(D)}_appendAndResetNonExposedChecks(){if(Object.keys(this._nonExposedChecks).length===0)return;this._normalizeAndAppendEvent({eventName:"statsig::non_exposed_checks",user:null,time:Date.now(),metadata:{checks:Object.assign({},this._nonExposedChecks)}}),this._nonExposedChecks={}}_getCurrentPageUrl(){var A;if(((A=this._options)===null||A===void 0?void 0:A.includeCurrentPageUrlWithEvents)===!1)return;return jl1._getCurrentPageUrlSafe()}_startBackgroundFlushInterval(){var A,B;let Q=(B=(A=this._options)===null||A===void 0?void 0:A.loggingIntervalMs)!==null&&B!==void 0?B:bO9,D=setInterval(()=>{let Z=$B1[this._sdkKey];if(!Z||Z._flushIntervalId!==D)clearInterval(D);else vf._safeFlushAndForget(this._sdkKey)},Q);this._flushIntervalId=D}}tl.EventLogger=vf});
var mn0=E((un0)=>{Object.defineProperty(un0,"__esModule",{value:!0})});
var nX1=E((Li0)=>{Object.defineProperty(Li0,"__esModule",{value:!0});Li0._notifyVisibilityChanged=Li0._subscribeToVisiblityChanged=Li0._isUnloading=Li0._isCurrentlyVisible=void 0;var pX1=xf(),iX1="foreground",Pl1="background",Ni0=[],Tl1=iX1,Sl1=!1,MO9=()=>{return Tl1===iX1};Li0._isCurrentlyVisible=MO9;var RO9=()=>Sl1;Li0._isUnloading=RO9;var OO9=(A)=>{Ni0.unshift(A)};Li0._subscribeToVisiblityChanged=OO9;var TO9=(A)=>{if(A===Tl1)return;Tl1=A,Ni0.forEach((B)=>B(A))};Li0._notifyVisibilityChanged=TO9;pX1._addWindowEventListenerSafe("focus",()=>{Sl1=!1,Li0._notifyVisibilityChanged(iX1)});pX1._addWindowEventListenerSafe("blur",()=>Li0._notifyVisibilityChanged(Pl1));pX1._addWindowEventListenerSafe("beforeunload",()=>{Sl1=!0,Li0._notifyVisibilityChanged(Pl1)});pX1._addDocumentEventListenerSafe("visibilitychange",()=>{Li0._notifyVisibilityChanged(document.visibilityState==="visible"?iX1:Pl1)})});
var nl1=E((NB1)=>{var za0=NB1&&NB1.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(NB1,"__esModule",{value:!0});var FV1=sj(),TP9=Ha0();class Ea0 extends FV1.NetworkCore{constructor(A,B){super(A,B);let Q=A===null||A===void 0?void 0:A.networkConfig;this._initializeUrlConfig=new FV1.UrlConfiguration(FV1.Endpoint._initialize,Q===null||Q===void 0?void 0:Q.initializeUrl,Q===null||Q===void 0?void 0:Q.api,Q===null||Q===void 0?void 0:Q.initializeFallbackUrls)}fetchEvaluations(A,B,Q,D,Z){return za0(this,void 0,void 0,function*(){let G=B?FV1._typedJsonParse(B,"has_updates","InitializeResponse"):null,F={user:D,hash:"djb2",deltasResponseRequested:!1,full_checksum:null};if(G===null||G===void 0?void 0:G.has_updates)F=Object.assign(Object.assign({},F),{sinceTime:Z?G.time:0,previousDerivedFields:"derived_fields"in G&&Z?G.derived_fields:{},deltasResponseRequested:!0,full_checksum:G.full_checksum});return this._fetchEvaluations(A,G,F,Q)})}_fetchEvaluations(A,B,Q,D){var Z,G;return za0(this,void 0,void 0,function*(){let F=yield this.post({sdkKey:A,urlConfig:this._initializeUrlConfig,data:Q,retries:2,isStatsigEncodable:!0,priority:D});if((F===null||F===void 0?void 0:F.code)===204)return'{"has_updates": false}';if((F===null||F===void 0?void 0:F.code)!==200)return(Z=F===null||F===void 0?void 0:F.body)!==null&&Z!==void 0?Z:null;if((B===null||B===void 0?void 0:B.has_updates)!==!0||((G=F.body)===null||G===void 0?void 0:G.includes('"is_delta":true'))!==!0||Q.deltasResponseRequested!==!0)return F.body;let I=TP9._resolveDeltasResponse(B,F.body);if(typeof I==="string")return I;return this._fetchEvaluations(A,B,Object.assign(Object.assign(Object.assign({},Q),I),{deltasResponseRequested:!1}),D)})}}NB1.default=Ea0});
var oX1=E((fi0)=>{Object.defineProperty(fi0,"__esModule",{value:!0});fi0.StableID=void 0;var mO9=zB1(),dO9=jW(),vi0=VO(),cO9=sX1(),rX1={};fi0.StableID={get:(A)=>{if(rX1[A]==null){let B=lO9(A);if(B==null)B=cO9.getUUID(),xi0(B,A);rX1[A]=B}return rX1[A]},setOverride:(A,B)=>{rX1[B]=A,xi0(A,B)}};function bi0(A){return`statsig.stable_id.${mO9._getStorageKey(A)}`}function xi0(A,B){let Q=bi0(B);try{vi0._setObjectInStorage(Q,A)}catch(D){dO9.Log.warn("Failed to save StableID")}}function lO9(A){let B=bi0(A);return vi0._getObjectFromStorage(B)}});
var pn0=E((Dp)=>{var aT9=Dp&&Dp.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(Dp,"__esModule",{value:!0});Dp.StatsigClientBase=void 0;_f();var sT9=_f(),rT9=bl1(),oT9=kl1(),ll1=jW(),tT9=fl1(),eT9=xf(),AP9=DV1(),ZV1=VO(),BP9=3000;class ln0{constructor(A,B,Q,D){var Z;this.loadingStatus="Uninitialized",this._initializePromise=null,this._listeners={};let G=this.$emt.bind(this);(D===null||D===void 0?void 0:D.logLevel)!=null&&(ll1.Log.level=D.logLevel),(D===null||D===void 0?void 0:D.disableStorage)&&ZV1.Storage._setDisabled(!0),(D===null||D===void 0?void 0:D.initialSessionID)&&AP9.StatsigSession.overrideInitialSessionID(D.initialSessionID,A),(D===null||D===void 0?void 0:D.storageProvider)&&ZV1.Storage._setProvider(D.storageProvider),this._sdkKey=A,this._options=D!==null&&D!==void 0?D:{},this._memoCache={},this.overrideAdapter=(Z=D===null||D===void 0?void 0:D.overrideAdapter)!==null&&Z!==void 0?Z:null,this._logger=new oT9.EventLogger(A,G,Q,D),this._errorBoundary=new rT9.ErrorBoundary(A,D,G),this._errorBoundary.wrap(this),this._errorBoundary.wrap(B),this._errorBoundary.wrap(this._logger),Q.setErrorBoundary(this._errorBoundary),this.dataAdapter=B,this.dataAdapter.attach(A,D),this.storageProvider=ZV1.Storage,this._primeReadyRipcord(),QP9(A,this)}updateRuntimeOptions(A){if(A.disableLogging!=null)this._options.disableLogging=A.disableLogging,this._logger.setLoggingDisabled(A.disableLogging);if(A.disableStorage!=null)this._options.disableStorage=A.disableStorage,ZV1.Storage._setDisabled(A.disableStorage)}flush(){return this._logger.flush()}shutdown(){return aT9(this,void 0,void 0,function*(){this.$emt({name:"pre_shutdown"}),this._setStatus("Uninitialized",null),this._initializePromise=null,yield this._logger.stop()})}on(A,B){if(!this._listeners[A])this._listeners[A]=[];this._listeners[A].push(B)}off(A,B){if(this._listeners[A]){let Q=this._listeners[A].indexOf(B);if(Q!==-1)this._listeners[A].splice(Q,1)}}$on(A,B){B.__isInternal=!0,this.on(A,B)}$emt(A){var B;let Q=(D)=>{try{D(A)}catch(Z){if(D.__isInternal===!0){this._errorBoundary.logError(`__emit:${A.name}`,Z);return}ll1.Log.error("An error occurred in a StatsigClientEvent listener. This is not an issue with Statsig.",A)}};if(this._listeners[A.name])this._listeners[A.name].forEach((D)=>Q(D));(B=this._listeners["*"])===null||B===void 0||B.forEach(Q)}_setStatus(A,B){this.loadingStatus=A,this._memoCache={},this.$emt({name:"values_updated",status:A,values:B})}_enqueueExposure(A,B,Q){if((Q===null||Q===void 0?void 0:Q.disableExposureLog)===!0){this._logger.incrementNonExposureCount(A);return}this._logger.enqueue(B)}_memoize(A,B){return(Q,D)=>{if(this._options.disableEvaluationMemoization)return B(Q,D);let Z=tT9.createMemoKey(A,Q,D);if(!Z)return B(Q,D);if(!(Z in this._memoCache)){if(Object.keys(this._memoCache).length>=BP9)this._memoCache={};this._memoCache[Z]=B(Q,D)}return this._memoCache[Z]}}}Dp.StatsigClientBase=ln0;function QP9(A,B){var Q;if(eT9._isServerEnv())return;let D=sT9._getStatsigGlobal(),Z=(Q=D.instances)!==null&&Q!==void 0?Q:{},G=B;if(Z[A]!=null)ll1.Log.warn("Creating multiple Statsig clients with the same SDK key can lead to unexpected behavior. Multi-instance support requires different SDK keys.");if(Z[A]=G,!D.firstInstance)D.firstInstance=G;D.instances=Z,__STATSIG__=D}});
var qB1=E((Ti0)=>{Object.defineProperty(Ti0,"__esModule",{value:!0});Ti0.StatsigMetadataProvider=Ti0.SDK_VERSION=void 0;Ti0.SDK_VERSION="3.12.1";var yl1={sdkVersion:Ti0.SDK_VERSION,sdkType:"js-mono"};Ti0.StatsigMetadataProvider={get:()=>yl1,add:(A)=>{yl1=Object.assign(Object.assign({},yl1),A)}}});
var qa0=E((wa0)=>{Object.defineProperty(wa0,"__esModule",{value:!0});wa0._makeParamStoreGetter=void 0;var Ua0=sj(),IV1={disableExposureLog:!0};function YV1(A){return A==null||A.disableExposureLog===!1}function al1(A,B){return B!=null&&!Ua0._isTypeMatch(A,B)}function PP9(A,B){return A.value}function SP9(A,B,Q){if(A.getFeatureGate(B.gate_name,YV1(Q)?void 0:IV1).value)return B.pass_value;return B.fail_value}function jP9(A,B,Q,D){let G=A.getDynamicConfig(B.config_name,IV1).get(B.param_name);if(al1(G,Q))return Q;if(YV1(D))A.getDynamicConfig(B.config_name);return G}function kP9(A,B,Q,D){let G=A.getExperiment(B.experiment_name,IV1).get(B.param_name);if(al1(G,Q))return Q;if(YV1(D))A.getExperiment(B.experiment_name);return G}function yP9(A,B,Q,D){let G=A.getLayer(B.layer_name,IV1).get(B.param_name);if(al1(G,Q))return Q;if(YV1(D))A.getLayer(B.layer_name).get(B.param_name);return G}function _P9(A,B,Q){return(D,Z)=>{if(B==null)return Z;let G=B[D];if(G==null||Z!=null&&Ua0._typeOf(Z)!==G.param_type)return Z;switch(G.ref_type){case"static":return PP9(G,Q);case"gate":return SP9(A,G,Q);case"dynamic_config":return jP9(A,G,Z,Q);case"experiment":return kP9(A,G,Z,Q);case"layer":return yP9(A,G,Z,Q);default:return Z}}}wa0._makeParamStoreGetter=_P9});
var ri0=E((si0)=>{Object.defineProperty(si0,"__esModule",{value:!0})});
var rn0=E((sn0)=>{Object.defineProperty(sn0,"__esModule",{value:!0})});
var sX1=E((yi0)=>{Object.defineProperty(yi0,"__esModule",{value:!0});yi0.getUUID=void 0;function uO9(){if(typeof crypto!=="undefined"&&typeof crypto.randomUUID==="function")return crypto.randomUUID();let A=new Date().getTime(),B=typeof performance!=="undefined"&&performance.now&&performance.now()*1000||0;return`xxxxxxxx-xxxx-4xxx-${"89ab"[Math.floor(Math.random()*4)]}xxx-xxxxxxxxxxxx`.replace(/[xy]/g,(D)=>{let Z=Math.random()*16;if(A>0)Z=(A+Z)%16|0,A=Math.floor(A/16);else Z=(B+Z)%16|0,B=Math.floor(B/16);return(D==="x"?Z:Z&7|8).toString(16)})}yi0.getUUID=uO9});
var sj=E((d9)=>{var EP9=d9&&d9.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),k4=d9&&d9.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))EP9(B,A,Q)};Object.defineProperty(d9,"__esModule",{value:!0});d9.Storage=d9.Log=d9.EventLogger=d9.Diagnostics=void 0;_f();var UP9=bX1();Object.defineProperty(d9,"Diagnostics",{enumerable:!0,get:function(){return UP9.Diagnostics}});var wP9=kl1();Object.defineProperty(d9,"EventLogger",{enumerable:!0,get:function(){return wP9.EventLogger}});var Ya0=jW();Object.defineProperty(d9,"Log",{enumerable:!0,get:function(){return Ya0.Log}});var $P9=qB1(),qP9=VO();Object.defineProperty(d9,"Storage",{enumerable:!0,get:function(){return qP9.Storage}});k4(_f(),d9);k4(zB1(),d9);k4(ki0(),d9);k4(ai0(),d9);k4(bX1(),d9);k4(ri0(),d9);k4(bl1(),d9);k4(Zn0(),d9);k4(Fn0(),d9);k4(al(),d9);k4(Yn0(),d9);k4(jW(),d9);k4(fl1(),d9);k4(EB1(),d9);k4(gn0(),d9);k4(mn0(),d9);k4(cn0(),d9);k4(xf(),d9);k4(BV1(),d9);k4(DV1(),d9);k4(oX1(),d9);k4(pn0(),d9);k4(dl1(),d9);k4(an0(),d9);k4(Ll1(),d9);k4(qB1(),d9);k4(rn0(),d9);k4(tn0(),d9);k4(Qa0(),d9);k4(Za0(),d9);k4(_l1(),d9);k4(VO(),d9);k4(xl1(),d9);k4(fX1(),d9);k4(Ol1(),d9);k4(sX1(),d9);k4(nX1(),d9);k4(Ia0(),d9);k4(ul1(),d9);__STATSIG__=Object.assign(Object.assign({},__STATSIG__!==null&&__STATSIG__!==void 0?__STATSIG__:{}),{Log:Ya0.Log,SDK_VERSION:$P9.SDK_VERSION})});
var tn0=E((on0)=>{Object.defineProperty(on0,"__esModule",{value:!0})});
var ul1=E(($n0)=>{Object.defineProperty($n0,"__esModule",{value:!0});$n0.SDKFlags=void 0;var wn0={};$n0.SDKFlags={setFlags:(A,B)=>{wn0[A]=B},get:(A,B)=>{var Q,D;return(D=(Q=wn0[A])===null||Q===void 0?void 0:Q[B])!==null&&D!==void 0?D:!1}}});
var xf=E((Ji0)=>{Object.defineProperty(Ji0,"__esModule",{value:!0});Ji0._getCurrentPageUrlSafe=Ji0._addDocumentEventListenerSafe=Ji0._addWindowEventListenerSafe=Ji0._isServerEnv=Ji0._getDocumentSafe=Ji0._getWindowSafe=void 0;var eR9=()=>{return typeof window!=="undefined"?window:null};Ji0._getWindowSafe=eR9;var AO9=()=>{var A;let B=Ji0._getWindowSafe();return(A=B===null||B===void 0?void 0:B.document)!==null&&A!==void 0?A:null};Ji0._getDocumentSafe=AO9;var BO9=()=>{if(Ji0._getDocumentSafe()!==null)return!1;let A=typeof process!=="undefined"&&process.versions!=null&&process.versions.node!=null;return typeof EdgeRuntime==="string"||A};Ji0._isServerEnv=BO9;var QO9=(A,B)=>{let Q=Ji0._getWindowSafe();if(typeof(Q===null||Q===void 0?void 0:Q.addEventListener)==="function")Q.addEventListener(A,B)};Ji0._addWindowEventListenerSafe=QO9;var DO9=(A,B)=>{let Q=Ji0._getDocumentSafe();if(typeof(Q===null||Q===void 0?void 0:Q.addEventListener)==="function")Q.addEventListener(A,B)};Ji0._addDocumentEventListenerSafe=DO9;var ZO9=()=>{var A;try{return(A=Ji0._getWindowSafe())===null||A===void 0?void 0:A.location.href.split(/[?#]/)[0]}catch(B){return}};Ji0._getCurrentPageUrlSafe=ZO9});
var xl1=E((mi0)=>{Object.defineProperty(mi0,"__esModule",{value:!0});mi0._typedJsonParse=void 0;var rO9=jW();function oO9(A,B,Q){try{let D=JSON.parse(A);if(D&&typeof D==="object"&&B in D)return D}catch(D){}return rO9.Log.error(`Failed to parse ${Q}`),null}mi0._typedJsonParse=oO9});
var zB1=E((Fi0)=>{Object.defineProperty(Fi0,"__esModule",{value:!0});Fi0._getStorageKey=Fi0._getUserStorageKey=void 0;var Zi0=al();function Gi0(A,B,Q){var D;if(Q)return Q(A,B);let Z=B&&B.customIDs?B.customIDs:{},G=[`uid:${(D=B===null||B===void 0?void 0:B.userID)!==null&&D!==void 0?D:""}`,`cids:${Object.keys(Z).sort((F,I)=>F.localeCompare(I)).map((F)=>`${F}-${Z[F]}`).join(",")}`,`k:${A}`];return Zi0._DJB2(G.join("|"))}Fi0._getUserStorageKey=Gi0;function rR9(A,B,Q){if(B)return Gi0(A,B,Q);return Zi0._DJB2(`k:${A}`)}Fi0._getStorageKey=rR9});

module.exports = fR1;
