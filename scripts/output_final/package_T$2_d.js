// dependency_chain package extracted with entry point: T$2

var ACA=E((d55,eVA)=>{var{defineProperty:Tz1,getOwnPropertyDescriptor:_GQ,getOwnPropertyNames:xGQ}=Object,vGQ=Object.prototype.hasOwnProperty,Pz1=(A,B)=>Tz1(A,"name",{value:B,configurable:!0}),bGQ=(A,B)=>{for(var Q in B)Tz1(A,Q,{get:B[Q],enumerable:!0})},fGQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of xGQ(B))if(!vGQ.call(A,Z)&&Z!==Q)Tz1(A,Z,{get:()=>B[Z],enumerable:!(D=_GQ(B,Z))||D.enumerable})}return A},hGQ=(A)=>fGQ(Tz1({},"__esModule",{value:!0}),A),pVA={};bGQ(pVA,{AlgorithmId:()=>sVA,EndpointURLScheme:()=>aVA,FieldPosition:()=>rVA,HttpApiKeyAuthLocation:()=>nVA,HttpAuthLocation:()=>iVA,IniSectionType:()=>oVA,RequestHandlerProtocol:()=>tVA,SMITHY_CONTEXT_KEY:()=>cGQ,getDefaultClientConfiguration:()=>mGQ,resolveDefaultRuntimeConfig:()=>dGQ});eVA.exports=hGQ(pVA);var iVA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(iVA||{}),nVA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(nVA||{}),aVA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(aVA||{}),sVA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(sVA||{}),gGQ=Pz1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),uGQ=Pz1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),mGQ=Pz1((A)=>{return gGQ(A)},"getDefaultClientConfiguration"),dGQ=Pz1((A)=>{return uGQ(A)},"resolveDefaultRuntimeConfig"),rVA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(rVA||{}),cGQ="__smithy_context",oVA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(oVA||{}),tVA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(tVA||{})});
var FCA=E((c55,GCA)=>{var{defineProperty:Sz1,getOwnPropertyDescriptor:lGQ,getOwnPropertyNames:pGQ}=Object,iGQ=Object.prototype.hasOwnProperty,Qy=(A,B)=>Sz1(A,"name",{value:B,configurable:!0}),nGQ=(A,B)=>{for(var Q in B)Sz1(A,Q,{get:B[Q],enumerable:!0})},aGQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of pGQ(B))if(!iGQ.call(A,Z)&&Z!==Q)Sz1(A,Z,{get:()=>B[Z],enumerable:!(D=lGQ(B,Z))||D.enumerable})}return A},sGQ=(A)=>aGQ(Sz1({},"__esModule",{value:!0}),A),BCA={};nGQ(BCA,{Field:()=>tGQ,Fields:()=>eGQ,HttpRequest:()=>AFQ,HttpResponse:()=>BFQ,IHttpRequest:()=>QCA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>rGQ,isValidHostname:()=>ZCA,resolveHttpHandlerRuntimeConfig:()=>oGQ});GCA.exports=sGQ(BCA);var rGQ=Qy((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),oGQ=Qy((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),QCA=ACA(),tGQ=class{static{Qy(this,"Field")}constructor({name:A,kind:B=QCA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},eGQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{Qy(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},AFQ=class A{static{Qy(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=DCA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function DCA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}Qy(DCA,"cloneQuery");var BFQ=class{static{Qy(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function ZCA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}Qy(ZCA,"isValidHostname")});
var JCA=E((n55,WCA)=>{var{defineProperty:jz1,getOwnPropertyDescriptor:QFQ,getOwnPropertyNames:DFQ}=Object,ZFQ=Object.prototype.hasOwnProperty,Vo1=(A,B)=>jz1(A,"name",{value:B,configurable:!0}),GFQ=(A,B)=>{for(var Q in B)jz1(A,Q,{get:B[Q],enumerable:!0})},FFQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of DFQ(B))if(!ZFQ.call(A,Z)&&Z!==Q)jz1(A,Z,{get:()=>B[Z],enumerable:!(D=QFQ(B,Z))||D.enumerable})}return A},IFQ=(A)=>FFQ(jz1({},"__esModule",{value:!0}),A),ICA={};GFQ(ICA,{escapeUri:()=>YCA,escapeUriPath:()=>WFQ});WCA.exports=IFQ(ICA);var YCA=Vo1((A)=>encodeURIComponent(A).replace(/[!'()*]/g,YFQ),"escapeUri"),YFQ=Vo1((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),WFQ=Vo1((A)=>A.split("/").map(YCA).join("/"),"escapeUriPath")});
var KCA=E((a55,CCA)=>{var{defineProperty:kz1,getOwnPropertyDescriptor:JFQ,getOwnPropertyNames:XFQ}=Object,VFQ=Object.prototype.hasOwnProperty,CFQ=(A,B)=>kz1(A,"name",{value:B,configurable:!0}),KFQ=(A,B)=>{for(var Q in B)kz1(A,Q,{get:B[Q],enumerable:!0})},HFQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of XFQ(B))if(!VFQ.call(A,Z)&&Z!==Q)kz1(A,Z,{get:()=>B[Z],enumerable:!(D=JFQ(B,Z))||D.enumerable})}return A},zFQ=(A)=>HFQ(kz1({},"__esModule",{value:!0}),A),XCA={};KFQ(XCA,{buildQueryString:()=>VCA});CCA.exports=zFQ(XCA);var Co1=JCA();function VCA(A){let B=[];for(let Q of Object.keys(A).sort()){let D=A[Q];if(Q=Co1.escapeUri(Q),Array.isArray(D))for(let Z=0,G=D.length;Z<G;Z++)B.push(`${Q}=${Co1.escapeUri(D[Z])}`);else{let Z=Q;if(D||typeof D==="string")Z+=`=${Co1.escapeUri(D)}`;B.push(Z)}}return B.join("&")}CFQ(VCA,"buildQueryString")});
var S3=E((s55,PCA)=>{var{create:EFQ,defineProperty:RQ1,getOwnPropertyDescriptor:UFQ,getOwnPropertyNames:wFQ,getPrototypeOf:$FQ}=Object,qFQ=Object.prototype.hasOwnProperty,P3=(A,B)=>RQ1(A,"name",{value:B,configurable:!0}),NFQ=(A,B)=>{for(var Q in B)RQ1(A,Q,{get:B[Q],enumerable:!0})},wCA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of wFQ(B))if(!qFQ.call(A,Z)&&Z!==Q)RQ1(A,Z,{get:()=>B[Z],enumerable:!(D=UFQ(B,Z))||D.enumerable})}return A},LFQ=(A,B,Q)=>(Q=A!=null?EFQ($FQ(A)):{},wCA(B||!A||!A.__esModule?RQ1(Q,"default",{value:A,enumerable:!0}):Q,A)),MFQ=(A)=>wCA(RQ1({},"__esModule",{value:!0}),A),$CA={};NFQ($CA,{DEFAULT_REQUEST_TIMEOUT:()=>OCA,NodeHttp2Handler:()=>xFQ,NodeHttpHandler:()=>jFQ,streamCollector:()=>bFQ});PCA.exports=MFQ($CA);var qCA=FCA(),NCA=KCA(),Ko1=J1("http"),Ho1=J1("https"),RFQ=["ECONNRESET","EPIPE","ETIMEDOUT"],LCA=P3((A)=>{let B={};for(let Q of Object.keys(A)){let D=A[Q];B[Q]=Array.isArray(D)?D.join(","):D}return B},"getTransformedHeaders"),YV={setTimeout:(A,B)=>setTimeout(A,B),clearTimeout:(A)=>clearTimeout(A)},HCA=1000,OFQ=P3((A,B,Q=0)=>{if(!Q)return-1;let D=P3((Z)=>{let G=YV.setTimeout(()=>{A.destroy(),B(Object.assign(new Error(`Socket timed out without establishing a connection within ${Q} ms`),{name:"TimeoutError"}))},Q-Z),F=P3((I)=>{if(I?.connecting)I.on("connect",()=>{YV.clearTimeout(G)});else YV.clearTimeout(G)},"doWithSocket");if(A.socket)F(A.socket);else A.on("socket",F)},"registerTimeout");if(Q<2000)return D(0),0;return YV.setTimeout(D.bind(null,HCA),HCA)},"setConnectionTimeout"),TFQ=3000,PFQ=P3((A,{keepAlive:B,keepAliveMsecs:Q},D=TFQ)=>{if(B!==!0)return-1;let Z=P3(()=>{if(A.socket)A.socket.setKeepAlive(B,Q||0);else A.on("socket",(G)=>{G.setKeepAlive(B,Q||0)})},"registerListener");if(D===0)return Z(),0;return YV.setTimeout(Z,D)},"setSocketKeepAlive"),zCA=3000,SFQ=P3((A,B,Q=OCA)=>{let D=P3((Z)=>{let G=Q-Z,F=P3(()=>{A.destroy(),B(Object.assign(new Error(`Connection timed out after ${Q} ms`),{name:"TimeoutError"}))},"onTimeout");if(A.socket)A.socket.setTimeout(G,F),A.on("close",()=>A.socket?.removeListener("timeout",F));else A.setTimeout(G,F)},"registerTimeout");if(0<Q&&Q<6000)return D(0),0;return YV.setTimeout(D.bind(null,Q===0?0:zCA),zCA)},"setSocketTimeout"),MCA=J1("stream"),ECA=6000;async function zo1(A,B,Q=ECA){let D=B.headers??{},Z=D.Expect||D.expect,G=-1,F=!0;if(Z==="100-continue")F=await Promise.race([new Promise((I)=>{G=Number(YV.setTimeout(()=>I(!0),Math.max(ECA,Q)))}),new Promise((I)=>{A.on("continue",()=>{YV.clearTimeout(G),I(!0)}),A.on("response",()=>{YV.clearTimeout(G),I(!1)}),A.on("error",()=>{YV.clearTimeout(G),I(!1)})})]);if(F)RCA(A,B.body)}P3(zo1,"writeRequestBody");function RCA(A,B){if(B instanceof MCA.Readable){B.pipe(A);return}if(B){if(Buffer.isBuffer(B)||typeof B==="string"){A.end(B);return}let Q=B;if(typeof Q==="object"&&Q.buffer&&typeof Q.byteOffset==="number"&&typeof Q.byteLength==="number"){A.end(Buffer.from(Q.buffer,Q.byteOffset,Q.byteLength));return}A.end(Buffer.from(B));return}A.end()}P3(RCA,"writeBody");var OCA=0,jFQ=class A{constructor(B){this.socketWarningTimestamp=0,this.metadata={handlerProtocol:"http/1.1"},this.configProvider=new Promise((Q,D)=>{if(typeof B==="function")B().then((Z)=>{Q(this.resolveDefaultConfig(Z))}).catch(D);else Q(this.resolveDefaultConfig(B))})}static{P3(this,"NodeHttpHandler")}static create(B){if(typeof B?.handle==="function")return B;return new A(B)}static checkSocketUsage(B,Q,D=console){let{sockets:Z,requests:G,maxSockets:F}=B;if(typeof F!=="number"||F===1/0)return Q;let I=15000;if(Date.now()-I<Q)return Q;if(Z&&G)for(let Y in Z){let W=Z[Y]?.length??0,J=G[Y]?.length??0;if(W>=F&&J>=2*F)return D?.warn?.(`@smithy/node-http-handler:WARN - socket usage at capacity=${W} and ${J} additional requests are enqueued.
See https://docs.aws.amazon.com/sdk-for-javascript/v3/developer-guide/node-configuring-maxsockets.html
or increase socketAcquisitionWarningTimeout=(millis) in the NodeHttpHandler config.`),Date.now()}return Q}resolveDefaultConfig(B){let{requestTimeout:Q,connectionTimeout:D,socketTimeout:Z,socketAcquisitionWarningTimeout:G,httpAgent:F,httpsAgent:I}=B||{},Y=!0,W=50;return{connectionTimeout:D,requestTimeout:Q??Z,socketAcquisitionWarningTimeout:G,httpAgent:(()=>{if(F instanceof Ko1.Agent||typeof F?.destroy==="function")return F;return new Ko1.Agent({keepAlive:!0,maxSockets:50,...F})})(),httpsAgent:(()=>{if(I instanceof Ho1.Agent||typeof I?.destroy==="function")return I;return new Ho1.Agent({keepAlive:!0,maxSockets:50,...I})})(),logger:console}}destroy(){this.config?.httpAgent?.destroy(),this.config?.httpsAgent?.destroy()}async handle(B,{abortSignal:Q}={}){if(!this.config)this.config=await this.configProvider;return new Promise((D,Z)=>{let G=void 0,F=[],I=P3(async(N)=>{await G,F.forEach(YV.clearTimeout),D(N)},"resolve"),Y=P3(async(N)=>{await G,F.forEach(YV.clearTimeout),Z(N)},"reject");if(!this.config)throw new Error("Node HTTP request handler config is not resolved");if(Q?.aborted){let N=new Error("Request aborted");N.name="AbortError",Y(N);return}let W=B.protocol==="https:",J=W?this.config.httpsAgent:this.config.httpAgent;F.push(YV.setTimeout(()=>{this.socketWarningTimestamp=A.checkSocketUsage(J,this.socketWarningTimestamp,this.config.logger)},this.config.socketAcquisitionWarningTimeout??(this.config.requestTimeout??2000)+(this.config.connectionTimeout??1000)));let X=NCA.buildQueryString(B.query||{}),V=void 0;if(B.username!=null||B.password!=null){let N=B.username??"",O=B.password??"";V=`${N}:${O}`}let C=B.path;if(X)C+=`?${X}`;if(B.fragment)C+=`#${B.fragment}`;let K=B.hostname??"";if(K[0]==="["&&K.endsWith("]"))K=B.hostname.slice(1,-1);else K=B.hostname;let H={headers:B.headers,host:K,method:B.method,path:C,port:B.port,agent:J,auth:V},$=(W?Ho1.request:Ko1.request)(H,(N)=>{let O=new qCA.HttpResponse({statusCode:N.statusCode||-1,reason:N.statusMessage,headers:LCA(N.headers),body:N});I({response:O})});if($.on("error",(N)=>{if(RFQ.includes(N.code))Y(Object.assign(N,{name:"TimeoutError"}));else Y(N)}),Q){let N=P3(()=>{$.destroy();let O=new Error("Request aborted");O.name="AbortError",Y(O)},"onAbort");if(typeof Q.addEventListener==="function"){let O=Q;O.addEventListener("abort",N,{once:!0}),$.once("close",()=>O.removeEventListener("abort",N))}else Q.onabort=N}F.push(OFQ($,Y,this.config.connectionTimeout)),F.push(SFQ($,Y,this.config.requestTimeout));let L=H.agent;if(typeof L==="object"&&"keepAlive"in L)F.push(PFQ($,{keepAlive:L.keepAlive,keepAliveMsecs:L.keepAliveMsecs}));G=zo1($,B,this.config.requestTimeout).catch((N)=>{return F.forEach(YV.clearTimeout),Z(N)})})}updateHttpClientConfig(B,Q){this.config=void 0,this.configProvider=this.configProvider.then((D)=>{return{...D,[B]:Q}})}httpHandlerConfigs(){return this.config??{}}},UCA=J1("http2"),kFQ=LFQ(J1("http2")),yFQ=class{constructor(A){this.sessions=[],this.sessions=A??[]}static{P3(this,"NodeHttp2ConnectionPool")}poll(){if(this.sessions.length>0)return this.sessions.shift()}offerLast(A){this.sessions.push(A)}contains(A){return this.sessions.includes(A)}remove(A){this.sessions=this.sessions.filter((B)=>B!==A)}[Symbol.iterator](){return this.sessions[Symbol.iterator]()}destroy(A){for(let B of this.sessions)if(B===A){if(!B.destroyed)B.destroy()}}},_FQ=class{constructor(A){if(this.sessionCache=new Map,this.config=A,this.config.maxConcurrency&&this.config.maxConcurrency<=0)throw new RangeError("maxConcurrency must be greater than zero.")}static{P3(this,"NodeHttp2ConnectionManager")}lease(A,B){let Q=this.getUrlString(A),D=this.sessionCache.get(Q);if(D){let I=D.poll();if(I&&!this.config.disableConcurrency)return I}let Z=kFQ.default.connect(Q);if(this.config.maxConcurrency)Z.settings({maxConcurrentStreams:this.config.maxConcurrency},(I)=>{if(I)throw new Error("Fail to set maxConcurrentStreams to "+this.config.maxConcurrency+"when creating new session for "+A.destination.toString())});Z.unref();let G=P3(()=>{Z.destroy(),this.deleteSession(Q,Z)},"destroySessionCb");if(Z.on("goaway",G),Z.on("error",G),Z.on("frameError",G),Z.on("close",()=>this.deleteSession(Q,Z)),B.requestTimeout)Z.setTimeout(B.requestTimeout,G);let F=this.sessionCache.get(Q)||new yFQ;return F.offerLast(Z),this.sessionCache.set(Q,F),Z}deleteSession(A,B){let Q=this.sessionCache.get(A);if(!Q)return;if(!Q.contains(B))return;Q.remove(B),this.sessionCache.set(A,Q)}release(A,B){let Q=this.getUrlString(A);this.sessionCache.get(Q)?.offerLast(B)}destroy(){for(let[A,B]of this.sessionCache){for(let Q of B){if(!Q.destroyed)Q.destroy();B.remove(Q)}this.sessionCache.delete(A)}}setMaxConcurrentStreams(A){if(A&&A<=0)throw new RangeError("maxConcurrentStreams must be greater than zero.");this.config.maxConcurrency=A}setDisableConcurrentStreams(A){this.config.disableConcurrency=A}getUrlString(A){return A.destination.toString()}},xFQ=class A{constructor(B){this.metadata={handlerProtocol:"h2"},this.connectionManager=new _FQ({}),this.configProvider=new Promise((Q,D)=>{if(typeof B==="function")B().then((Z)=>{Q(Z||{})}).catch(D);else Q(B||{})})}static{P3(this,"NodeHttp2Handler")}static create(B){if(typeof B?.handle==="function")return B;return new A(B)}destroy(){this.connectionManager.destroy()}async handle(B,{abortSignal:Q}={}){if(!this.config){if(this.config=await this.configProvider,this.connectionManager.setDisableConcurrentStreams(this.config.disableConcurrentStreams||!1),this.config.maxConcurrentStreams)this.connectionManager.setMaxConcurrentStreams(this.config.maxConcurrentStreams)}let{requestTimeout:D,disableConcurrentStreams:Z}=this.config;return new Promise((G,F)=>{let I=!1,Y=void 0,W=P3(async(f)=>{await Y,G(f)},"resolve"),J=P3(async(f)=>{await Y,F(f)},"reject");if(Q?.aborted){I=!0;let f=new Error("Request aborted");f.name="AbortError",J(f);return}let{hostname:X,method:V,port:C,protocol:K,query:H}=B,z="";if(B.username!=null||B.password!=null){let f=B.username??"",k=B.password??"";z=`${f}:${k}@`}let $=`${K}//${z}${X}${C?`:${C}`:""}`,L={destination:new URL($)},N=this.connectionManager.lease(L,{requestTimeout:this.config?.sessionTimeout,disableConcurrentStreams:Z||!1}),O=P3((f)=>{if(Z)this.destroySession(N);I=!0,J(f)},"rejectWithDestroy"),R=NCA.buildQueryString(H||{}),T=B.path;if(R)T+=`?${R}`;if(B.fragment)T+=`#${B.fragment}`;let j=N.request({...B.headers,[UCA.constants.HTTP2_HEADER_PATH]:T,[UCA.constants.HTTP2_HEADER_METHOD]:V});if(N.ref(),j.on("response",(f)=>{let k=new qCA.HttpResponse({statusCode:f[":status"]||-1,headers:LCA(f),body:j});if(I=!0,W({response:k}),Z)N.close(),this.connectionManager.deleteSession($,N)}),D)j.setTimeout(D,()=>{j.close();let f=new Error(`Stream timed out because of no activity for ${D} ms`);f.name="TimeoutError",O(f)});if(Q){let f=P3(()=>{j.close();let k=new Error("Request aborted");k.name="AbortError",O(k)},"onAbort");if(typeof Q.addEventListener==="function"){let k=Q;k.addEventListener("abort",f,{once:!0}),j.once("close",()=>k.removeEventListener("abort",f))}else Q.onabort=f}j.on("frameError",(f,k,c)=>{O(new Error(`Frame type id ${f} in stream id ${c} has failed with code ${k}.`))}),j.on("error",O),j.on("aborted",()=>{O(new Error(`HTTP/2 stream is abnormally aborted in mid-communication with result code ${j.rstCode}.`))}),j.on("close",()=>{if(N.unref(),Z)N.destroy();if(!I)O(new Error("Unexpected error: http2 request did not get a response"))}),Y=zo1(j,B,D)})}updateHttpClientConfig(B,Q){this.config=void 0,this.configProvider=this.configProvider.then((D)=>{return{...D,[B]:Q}})}httpHandlerConfigs(){return this.config??{}}destroySession(B){if(!B.destroyed)B.destroy()}},vFQ=class extends MCA.Writable{constructor(){super(...arguments);this.bufferedBytes=[]}static{P3(this,"Collector")}_write(A,B,Q){this.bufferedBytes.push(A),Q()}},bFQ=P3((A)=>{if(fFQ(A))return TCA(A);return new Promise((B,Q)=>{let D=new vFQ;A.pipe(D),A.on("error",(Z)=>{D.end(),Q(Z)}),D.on("error",Q),D.on("finish",function(){let Z=new Uint8Array(Buffer.concat(this.bufferedBytes));B(Z)})})},"streamCollector"),fFQ=P3((A)=>typeof ReadableStream==="function"&&A instanceof ReadableStream,"isReadableStreamInstance");async function TCA(A){let B=[],Q=A.getReader(),D=!1,Z=0;while(!D){let{done:I,value:Y}=await Q.read();if(Y)B.push(Y),Z+=Y.length;D=I}let G=new Uint8Array(Z),F=0;for(let I of B)G.set(I,F),F+=I.length;return G}P3(TCA,"collectReadableStream")});

module.exports = T$2;
