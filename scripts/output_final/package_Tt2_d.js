// E_module package extracted with entry point: Tt2

var AY0=E((G_5,VG6)=>{VG6.exports={name:"@grpc/grpc-js",version:"1.13.1",description:"gRPC Library for Node - pure JS implementation",homepage:"https://grpc.io/",repository:"https://github.com/grpc/grpc-node/tree/master/packages/grpc-js",main:"build/src/index.js",engines:{node:">=12.10.0"},keywords:[],author:{name:"Google Inc."},types:"build/src/index.d.ts",license:"Apache-2.0",devDependencies:{"@grpc/proto-loader":"file:../proto-loader","@types/gulp":"^4.0.17","@types/gulp-mocha":"0.0.37","@types/lodash":"^4.14.202","@types/mocha":"^10.0.6","@types/ncp":"^2.0.8","@types/node":">=20.11.20","@types/pify":"^5.0.4","@types/semver":"^7.5.8","@typescript-eslint/eslint-plugin":"^7.1.0","@typescript-eslint/parser":"^7.1.0","@typescript-eslint/typescript-estree":"^7.1.0","clang-format":"^1.8.0",eslint:"^8.42.0","eslint-config-prettier":"^8.8.0","eslint-plugin-node":"^11.1.0","eslint-plugin-prettier":"^4.2.1",execa:"^2.0.3",gulp:"^4.0.2","gulp-mocha":"^6.0.0",lodash:"^4.17.21",madge:"^5.0.1","mocha-jenkins-reporter":"^0.4.1",ncp:"^2.0.0",pify:"^4.0.1",prettier:"^2.8.8",rimraf:"^3.0.2",semver:"^7.6.0","ts-node":"^10.9.2",typescript:"^5.3.3"},contributors:[{name:"Google Inc."}],scripts:{build:"npm run compile",clean:"rimraf ./build",compile:"tsc -p .",format:'clang-format -i -style="{Language: JavaScript, BasedOnStyle: Google, ColumnLimit: 80}" src/*.ts test/*.ts',lint:"eslint src/*.ts test/*.ts",prepare:"npm run generate-types && npm run compile",test:"gulp test",check:"npm run lint",fix:"eslint --fix src/*.ts test/*.ts",pretest:"npm run generate-types && npm run generate-test-types && npm run compile",posttest:"npm run check && madge -c ./build/src","generate-types":"proto-loader-gen-types --keepCase --longs String --enums String --defaults --oneofs --includeComments --includeDirs proto/ --include-dirs test/fixtures/ -O src/generated/ --grpcLib ../index channelz.proto","generate-test-types":"proto-loader-gen-types --keepCase --longs String --enums String --defaults --oneofs --includeComments --include-dirs test/fixtures/ -O test/generated/ --grpcLib ../../src/index test_service.proto"},dependencies:{"@grpc/proto-loader":"^0.7.13","@js-sdsl/ordered-map":"^4.4.2"},files:["src/**/*.ts","build/src/**/*.{js,d.ts,js.map}","proto/*.proto","LICENSE","deps/envoy-api/envoy/api/v2/**/*.proto","deps/envoy-api/envoy/config/**/*.proto","deps/envoy-api/envoy/service/**/*.proto","deps/envoy-api/envoy/type/**/*.proto","deps/udpa/udpa/**/*.proto","deps/googleapis/google/api/*.proto","deps/googleapis/google/rpc/*.proto","deps/protoc-gen-validate/validate/**/*.proto"]}});
var An2=E((__5,ei2)=>{ei2.exports=_Y0;function _Y0(A,B){if(typeof A==="string")B=A,A=void 0;var Q=[];function D(G){if(typeof G!=="string"){var F=Z();if(_Y0.verbose)console.log("codegen: "+F);if(F="return "+F,G){var I=Object.keys(G),Y=new Array(I.length+1),W=new Array(I.length),J=0;while(J<I.length)Y[J]=I[J],W[J]=G[I[J++]];return Y[J]=F,Function.apply(null,Y).apply(null,W)}return Function(F)()}var X=new Array(arguments.length-1),V=0;while(V<X.length)X[V]=arguments[++V];if(V=0,G=G.replace(/%([%dfijs])/g,function C(K,H){var z=X[V++];switch(H){case"d":case"f":return String(Number(z));case"i":return String(Math.floor(z));case"j":return JSON.stringify(z);case"s":return String(z)}return"%"}),V!==X.length)throw Error("parameter count mismatch");return Q.push(G),D}function Z(G){return"function "+(G||B||"")+"("+(A&&A.join(",")||"")+`){
  `+Q.join(`
  `)+`
}`}return D.toString=Z,D}_Y0.verbose=!1});
var At=E((g_5,zn2)=>{zn2.exports=p8;var hY0=vu();((p8.prototype=Object.create(hY0.prototype)).constructor=p8).className="Namespace";var Cn2=n_(),kT1=KI(),IW6=oo(),to,O31,eo;p8.fromJSON=function A(B,Q){return new p8(B,Q.options).addJSON(Q.nested)};function Kn2(A,B){if(!(A&&A.length))return;var Q={};for(var D=0;D<A.length;++D)Q[A[D].name]=A[D].toJSON(B);return Q}p8.arrayToJSON=Kn2;p8.isReservedId=function A(B,Q){if(B){for(var D=0;D<B.length;++D)if(typeof B[D]!=="string"&&B[D][0]<=Q&&B[D][1]>Q)return!0}return!1};p8.isReservedName=function A(B,Q){if(B){for(var D=0;D<B.length;++D)if(B[D]===Q)return!0}return!1};function p8(A,B){hY0.call(this,A,B),this.nested=void 0,this._nestedArray=null}function Hn2(A){return A._nestedArray=null,A}Object.defineProperty(p8.prototype,"nestedArray",{get:function(){return this._nestedArray||(this._nestedArray=kT1.toArray(this.nested))}});p8.prototype.toJSON=function A(B){return kT1.toObject(["options",this.options,"nested",Kn2(this.nestedArray,B)])};p8.prototype.addJSON=function A(B){var Q=this;if(B)for(var D=Object.keys(B),Z=0,G;Z<D.length;++Z)G=B[D[Z]],Q.add((G.fields!==void 0?to.fromJSON:G.values!==void 0?eo.fromJSON:G.methods!==void 0?O31.fromJSON:G.id!==void 0?Cn2.fromJSON:p8.fromJSON)(D[Z],G));return this};p8.prototype.get=function A(B){return this.nested&&this.nested[B]||null};p8.prototype.getEnum=function A(B){if(this.nested&&this.nested[B]instanceof eo)return this.nested[B].values;throw Error("no such enum: "+B)};p8.prototype.add=function A(B){if(!(B instanceof Cn2&&B.extend!==void 0||B instanceof to||B instanceof IW6||B instanceof eo||B instanceof O31||B instanceof p8))throw TypeError("object must be a valid nested object");if(!this.nested)this.nested={};else{var Q=this.get(B.name);if(Q)if(Q instanceof p8&&B instanceof p8&&!(Q instanceof to||Q instanceof O31)){var D=Q.nestedArray;for(var Z=0;Z<D.length;++Z)B.add(D[Z]);if(this.remove(Q),!this.nested)this.nested={};B.setOptions(Q.options,!0)}else throw Error("duplicate name '"+B.name+"' in "+this)}return this.nested[B.name]=B,B.onAdd(this),Hn2(this)};p8.prototype.remove=function A(B){if(!(B instanceof hY0))throw TypeError("object must be a ReflectionObject");if(B.parent!==this)throw Error(B+" is not a member of "+this);if(delete this.nested[B.name],!Object.keys(this.nested).length)this.nested=void 0;return B.onRemove(this),Hn2(this)};p8.prototype.define=function A(B,Q){if(kT1.isString(B))B=B.split(".");else if(!Array.isArray(B))throw TypeError("illegal path");if(B&&B.length&&B[0]==="")throw Error("path must be relative");var D=this;while(B.length>0){var Z=B.shift();if(D.nested&&D.nested[Z]){if(D=D.nested[Z],!(D instanceof p8))throw Error("path conflicts with non-namespace objects")}else D.add(D=new p8(Z))}if(Q)D.addJSON(Q);return D};p8.prototype.resolveAll=function A(){var B=this.nestedArray,Q=0;while(Q<B.length)if(B[Q]instanceof p8)B[Q++].resolveAll();else B[Q++].resolve();return this.resolve()};p8.prototype.lookup=function A(B,Q,D){if(typeof Q==="boolean")D=Q,Q=void 0;else if(Q&&!Array.isArray(Q))Q=[Q];if(kT1.isString(B)&&B.length){if(B===".")return this.root;B=B.split(".")}else if(!B.length)return this;if(B[0]==="")return this.root.lookup(B.slice(1),Q);var Z=this.get(B[0]);if(Z){if(B.length===1){if(!Q||Q.indexOf(Z.constructor)>-1)return Z}else if(Z instanceof p8&&(Z=Z.lookup(B.slice(1),Q,!0)))return Z}else for(var G=0;G<this.nestedArray.length;++G)if(this._nestedArray[G]instanceof p8&&(Z=this._nestedArray[G].lookup(B,Q,!0)))return Z;if(this.parent===null||D)return null;return this.parent.lookup(B,Q)};p8.prototype.lookupType=function A(B){var Q=this.lookup(B,[to]);if(!Q)throw Error("no such type: "+B);return Q};p8.prototype.lookupEnum=function A(B){var Q=this.lookup(B,[eo]);if(!Q)throw Error("no such Enum '"+B+"' in "+this);return Q};p8.prototype.lookupTypeOrEnum=function A(B){var Q=this.lookup(B,[to,eo]);if(!Q)throw Error("no such Type or Enum '"+B+"' in "+this);return Q};p8.prototype.lookupService=function A(B){var Q=this.lookup(B,[O31]);if(!Q)throw Error("no such Service '"+B+"' in "+this);return Q};p8._configure=function(A,B,Q){to=A,O31=B,eo=Q}});
var At2=E((to2)=>{Object.defineProperty(to2,"__esModule",{value:!0});to2.VERSION=void 0;to2.VERSION="0.200.0"});
var C31=E((Vp2)=>{Object.defineProperty(Vp2,"__esModule",{value:!0});Vp2.ChannelCredentials=void 0;Vp2.createCertificateProviderChannelCredentials=CF6;var V31=J1("tls"),zT1=VT1(),HY0=XY0(),CT1=SV(),Wp2=nL(),XF6=D7(),VF6=S6();function KY0(A,B){if(A&&!(A instanceof Buffer))throw new TypeError(`${B}, if provided, must be a Buffer.`)}class po{compose(A){return new HT1(this,A)}static createSsl(A,B,Q,D){var Z;if(KY0(A,"Root certificate"),KY0(B,"Private key"),KY0(Q,"Certificate chain"),B&&!Q)throw new Error("Private key must be given with accompanying certificate chain");if(!B&&Q)throw new Error("Certificate chain must be given with accompanying private key");let G=V31.createSecureContext({ca:(Z=A!==null&&A!==void 0?A:HY0.getDefaultRootsData())!==null&&Z!==void 0?Z:void 0,key:B!==null&&B!==void 0?B:void 0,cert:Q!==null&&Q!==void 0?Q:void 0,ciphers:HY0.CIPHER_SUITES});return new KT1(G,D!==null&&D!==void 0?D:{})}static createFromSecureContext(A,B){return new KT1(A,B!==null&&B!==void 0?B:{})}static createInsecure(){return new zY0}}Vp2.ChannelCredentials=po;class zY0 extends po{constructor(){super()}compose(A){throw new Error("Cannot compose insecure credentials")}_isSecure(){return!1}_equals(A){return A instanceof zY0}_createSecureConnector(A,B,Q){return{connect(D){return Promise.resolve({socket:D,secure:!1})},waitForReady:()=>{return Promise.resolve()},getCallCredentials:()=>{return Q!==null&&Q!==void 0?Q:zT1.CallCredentials.createEmpty()},destroy(){}}}}function Jp2(A,B,Q,D){var Z,G,F,I;let Y={secureContext:A};if(B.checkServerIdentity)Y.checkServerIdentity=B.checkServerIdentity;if(B.rejectUnauthorized!==void 0)Y.rejectUnauthorized=B.rejectUnauthorized;if(Y.ALPNProtocols=["h2"],D["grpc.ssl_target_name_override"]){let C=D["grpc.ssl_target_name_override"],K=(Z=Y.checkServerIdentity)!==null&&Z!==void 0?Z:V31.checkServerIdentity;Y.checkServerIdentity=(H,z)=>{return K(C,z)},Y.servername=C}else if("grpc.http_connect_target"in D){let C=Wp2.getDefaultAuthority((G=CT1.parseUri(D["grpc.http_connect_target"]))!==null&&G!==void 0?G:{path:"localhost"}),K=CT1.splitHostPort(C);Y.servername=(F=K===null||K===void 0?void 0:K.host)!==null&&F!==void 0?F:C}if(D["grpc-node.tls_enable_trace"])Y.enableTrace=!0;let W=Q;if("grpc.http_connect_target"in D){let C=CT1.parseUri(D["grpc.http_connect_target"]);if(C)W=C}let J=Wp2.getDefaultAuthority(W),X=CT1.splitHostPort(J),V=(I=X===null||X===void 0?void 0:X.host)!==null&&I!==void 0?I:J;return Y.host=V,Y.servername=V,Y}class Xp2{constructor(A,B){this.connectionOptions=A,this.callCredentials=B}connect(A){let B=Object.assign({socket:A},this.connectionOptions);return new Promise((Q,D)=>{let Z=V31.connect(B,()=>{var G;if(((G=this.connectionOptions.rejectUnauthorized)!==null&&G!==void 0?G:!0)&&!Z.authorized){D(Z.authorizationError);return}Q({socket:Z,secure:!0})});Z.on("error",(G)=>{D(G)})})}waitForReady(){return Promise.resolve()}getCallCredentials(){return this.callCredentials}destroy(){}}class KT1 extends po{constructor(A,B){super();this.secureContext=A,this.verifyOptions=B}_isSecure(){return!0}_equals(A){if(this===A)return!0;if(A instanceof KT1)return this.secureContext===A.secureContext&&this.verifyOptions.checkServerIdentity===A.verifyOptions.checkServerIdentity;else return!1}_createSecureConnector(A,B,Q){let D=Jp2(this.secureContext,this.verifyOptions,A,B);return new Xp2(D,Q!==null&&Q!==void 0?Q:zT1.CallCredentials.createEmpty())}}class X31 extends po{constructor(A,B,Q){super();this.caCertificateProvider=A,this.identityCertificateProvider=B,this.verifyOptions=Q,this.refcount=0,this.latestCaUpdate=void 0,this.latestIdentityUpdate=void 0,this.caCertificateUpdateListener=this.handleCaCertificateUpdate.bind(this),this.identityCertificateUpdateListener=this.handleIdentityCertitificateUpdate.bind(this),this.secureContextWatchers=[]}_isSecure(){return!0}_equals(A){var B,Q;if(this===A)return!0;if(A instanceof X31)return this.caCertificateProvider===A.caCertificateProvider&&this.identityCertificateProvider===A.identityCertificateProvider&&((B=this.verifyOptions)===null||B===void 0?void 0:B.checkServerIdentity)===((Q=A.verifyOptions)===null||Q===void 0?void 0:Q.checkServerIdentity);else return!1}ref(){var A;if(this.refcount===0)this.caCertificateProvider.addCaCertificateListener(this.caCertificateUpdateListener),(A=this.identityCertificateProvider)===null||A===void 0||A.addIdentityCertificateListener(this.identityCertificateUpdateListener);this.refcount+=1}unref(){var A;if(this.refcount-=1,this.refcount===0)this.caCertificateProvider.removeCaCertificateListener(this.caCertificateUpdateListener),(A=this.identityCertificateProvider)===null||A===void 0||A.removeIdentityCertificateListener(this.identityCertificateUpdateListener)}_createSecureConnector(A,B,Q){return this.ref(),new X31.SecureConnectorImpl(this,A,B,Q!==null&&Q!==void 0?Q:zT1.CallCredentials.createEmpty())}maybeUpdateWatchers(){if(this.hasReceivedUpdates()){for(let A of this.secureContextWatchers)A(this.getLatestSecureContext());this.secureContextWatchers=[]}}handleCaCertificateUpdate(A){this.latestCaUpdate=A,this.maybeUpdateWatchers()}handleIdentityCertitificateUpdate(A){this.latestIdentityUpdate=A,this.maybeUpdateWatchers()}hasReceivedUpdates(){if(this.latestCaUpdate===void 0)return!1;if(this.identityCertificateProvider&&this.latestIdentityUpdate===void 0)return!1;return!0}getSecureContext(){if(this.hasReceivedUpdates())return Promise.resolve(this.getLatestSecureContext());else return new Promise((A)=>{this.secureContextWatchers.push(A)})}getLatestSecureContext(){var A,B;if(!this.latestCaUpdate)return null;if(this.identityCertificateProvider!==null&&!this.latestIdentityUpdate)return null;try{return V31.createSecureContext({ca:this.latestCaUpdate.caCertificate,key:(A=this.latestIdentityUpdate)===null||A===void 0?void 0:A.privateKey,cert:(B=this.latestIdentityUpdate)===null||B===void 0?void 0:B.certificate,ciphers:HY0.CIPHER_SUITES})}catch(Q){return XF6.log(VF6.LogVerbosity.ERROR,"Failed to createSecureContext with error "+Q.message),null}}}X31.SecureConnectorImpl=class{constructor(A,B,Q,D){this.parent=A,this.channelTarget=B,this.options=Q,this.callCredentials=D}connect(A){return new Promise((B,Q)=>{let D=this.parent.getLatestSecureContext();if(!D){Q(new Error("Failed to load credentials"));return}if(A.closed)Q(new Error("Socket closed while loading credentials"));let Z=Jp2(D,this.parent.verifyOptions,this.channelTarget,this.options),G=Object.assign({socket:A},Z),F=()=>{Q(new Error("Socket closed"))},I=(W)=>{Q(W)},Y=V31.connect(G,()=>{var W;if(Y.removeListener("close",F),Y.removeListener("error",I),((W=this.parent.verifyOptions.rejectUnauthorized)!==null&&W!==void 0?W:!0)&&!Y.authorized){Q(Y.authorizationError);return}B({socket:Y,secure:!0})});Y.once("close",F),Y.once("error",I)})}async waitForReady(){await this.parent.getSecureContext()}getCallCredentials(){return this.callCredentials}destroy(){this.parent.unref()}};function CF6(A,B,Q){return new X31(A,B,Q!==null&&Q!==void 0?Q:{})}class HT1 extends po{constructor(A,B){super();if(this.channelCredentials=A,this.callCredentials=B,!A._isSecure())throw new Error("Cannot compose insecure credentials")}compose(A){let B=this.callCredentials.compose(A);return new HT1(this.channelCredentials,B)}_isSecure(){return!0}_equals(A){if(this===A)return!0;if(A instanceof HT1)return this.channelCredentials._equals(A.channelCredentials)&&this.callCredentials._equals(A.callCredentials);else return!1}_createSecureConnector(A,B,Q){let D=this.callCredentials.compose(Q!==null&&Q!==void 0?Q:zT1.CallCredentials.createEmpty());return this.channelCredentials._createSecureConnector(A,B,D)}}});
var CE=E((cp2)=>{Object.defineProperty(cp2,"__esModule",{value:!0});cp2.EndpointMap=void 0;cp2.isTcpSubchannelAddress=U31;cp2.subchannelAddressEqual=NT1;cp2.subchannelAddressToString=up2;cp2.stringToSubchannelAddress=LI6;cp2.endpointEqual=MI6;cp2.endpointToString=RI6;cp2.endpointHasAddress=mp2;var gp2=J1("net");function U31(A){return"port"in A}function NT1(A,B){if(!A&&!B)return!0;if(!A||!B)return!1;if(U31(A))return U31(B)&&A.host===B.host&&A.port===B.port;else return!U31(B)&&A.path===B.path}function up2(A){if(U31(A))if(gp2.isIPv6(A.host))return"["+A.host+"]:"+A.port;else return A.host+":"+A.port;else return A.path}var NI6=443;function LI6(A,B){if(gp2.isIP(A))return{host:A,port:B!==null&&B!==void 0?B:NI6};else return{path:A}}function MI6(A,B){if(A.addresses.length!==B.addresses.length)return!1;for(let Q=0;Q<A.addresses.length;Q++)if(!NT1(A.addresses[Q],B.addresses[Q]))return!1;return!0}function RI6(A){return"["+A.addresses.map(up2).join(", ")+"]"}function mp2(A,B){for(let Q of A.addresses)if(NT1(Q,B))return!0;return!1}function E31(A,B){if(A.addresses.length!==B.addresses.length)return!1;for(let Q of A.addresses){let D=!1;for(let Z of B.addresses)if(NT1(Q,Z)){D=!0;break}if(!D)return!1}return!0}class dp2{constructor(){this.map=new Set}get size(){return this.map.size}getForSubchannelAddress(A){for(let B of this.map)if(mp2(B.key,A))return B.value;return}deleteMissing(A){let B=[];for(let Q of this.map){let D=!1;for(let Z of A)if(E31(Z,Q.key))D=!0;if(!D)B.push(Q.value),this.map.delete(Q)}return B}get(A){for(let B of this.map)if(E31(A,B.key))return B.value;return}set(A,B){for(let Q of this.map)if(E31(A,Q.key)){Q.value=B;return}this.map.add({key:A,value:B})}delete(A){for(let B of this.map)if(E31(A,B.key)){this.map.delete(B);return}}has(A){for(let B of this.map)if(E31(A,B.key))return!0;return!1}clear(){this.map.clear()}*keys(){for(let A of this.map)yield A.key}*values(){for(let A of this.map)yield A.value}*entries(){for(let A of this.map)yield[A.key,A.value]}}cp2.EndpointMap=dp2});
var Co2=E((Xo2)=>{Object.defineProperty(Xo2,"__esModule",{value:!0});Xo2.FileWatcherCertificateProvider=void 0;var OK6=J1("fs"),TK6=D7(),PK6=S6(),SK6=J1("util"),jK6="certificate_provider";function wP1(A){TK6.trace(PK6.LogVerbosity.DEBUG,jK6,A)}var WJ0=SK6.promisify(OK6.readFile);class Jo2{constructor(A){if(this.config=A,this.refreshTimer=null,this.fileResultPromise=null,this.latestCaUpdate=void 0,this.caListeners=new Set,this.latestIdentityUpdate=void 0,this.identityListeners=new Set,this.lastUpdateTime=null,A.certificateFile===void 0!==(A.privateKeyFile===void 0))throw new Error("certificateFile and privateKeyFile must be set or unset together");if(A.certificateFile===void 0&&A.caCertificateFile===void 0)throw new Error("At least one of certificateFile and caCertificateFile must be set");wP1("File watcher constructed with config "+JSON.stringify(A))}updateCertificates(){if(this.fileResultPromise)return;this.fileResultPromise=Promise.allSettled([this.config.certificateFile?WJ0(this.config.certificateFile):Promise.reject(),this.config.privateKeyFile?WJ0(this.config.privateKeyFile):Promise.reject(),this.config.caCertificateFile?WJ0(this.config.caCertificateFile):Promise.reject()]),this.fileResultPromise.then(([A,B,Q])=>{if(!this.refreshTimer)return;if(wP1("File watcher read certificates certificate "+A.status+", privateKey "+B.status+", CA certificate "+Q.status),this.lastUpdateTime=new Date,this.fileResultPromise=null,A.status==="fulfilled"&&B.status==="fulfilled")this.latestIdentityUpdate={certificate:A.value,privateKey:B.value};else this.latestIdentityUpdate=null;if(Q.status==="fulfilled")this.latestCaUpdate={caCertificate:Q.value};else this.latestCaUpdate=null;for(let D of this.identityListeners)D(this.latestIdentityUpdate);for(let D of this.caListeners)D(this.latestCaUpdate)}),wP1("File watcher initiated certificate update")}maybeStartWatchingFiles(){if(!this.refreshTimer){let A=this.lastUpdateTime?new Date().getTime()-this.lastUpdateTime.getTime():1/0;if(A>this.config.refreshIntervalMs)this.updateCertificates();if(A>this.config.refreshIntervalMs*2)this.latestCaUpdate=void 0,this.latestIdentityUpdate=void 0;this.refreshTimer=setInterval(()=>this.updateCertificates(),this.config.refreshIntervalMs),wP1("File watcher started watching")}}maybeStopWatchingFiles(){if(this.caListeners.size===0&&this.identityListeners.size===0){if(this.fileResultPromise=null,this.refreshTimer)clearInterval(this.refreshTimer),this.refreshTimer=null}}addCaCertificateListener(A){if(this.caListeners.add(A),this.maybeStartWatchingFiles(),this.latestCaUpdate!==void 0)process.nextTick(A,this.latestCaUpdate)}removeCaCertificateListener(A){this.caListeners.delete(A),this.maybeStopWatchingFiles()}addIdentityCertificateListener(A){if(this.identityListeners.add(A),this.maybeStartWatchingFiles(),this.latestIdentityUpdate!==void 0)process.nextTick(A,this.latestIdentityUpdate)}removeIdentityCertificateListener(A){this.identityListeners.delete(A),this.maybeStopWatchingFiles()}}Xo2.FileWatcherCertificateProvider=Jo2});
var Cs2=E((Xs2)=>{Object.defineProperty(Xs2,"__esModule",{value:!0});Xs2.Http2SubchannelCall=void 0;var KP=J1("http2"),nX6=J1("os"),Q3=S6(),HP=tW(),aX6=PW0(),sX6=D7(),rX6=S6(),oX6="subchannel_call";function tX6(A){for(let[B,Q]of Object.entries(nX6.constants.errno))if(Q===A)return B;return"Unknown system error "+A}function SW0(A){let B=`Received HTTP status code ${A}`,Q;switch(A){case 400:Q=Q3.Status.INTERNAL;break;case 401:Q=Q3.Status.UNAUTHENTICATED;break;case 403:Q=Q3.Status.PERMISSION_DENIED;break;case 404:Q=Q3.Status.UNIMPLEMENTED;break;case 429:case 502:case 503:case 504:Q=Q3.Status.UNAVAILABLE;break;default:Q=Q3.Status.UNKNOWN}return{code:Q,details:B,metadata:new HP.Metadata}}class Js2{constructor(A,B,Q,D,Z){var G;this.http2Stream=A,this.callEventTracker=B,this.listener=Q,this.transport=D,this.callId=Z,this.isReadFilterPending=!1,this.isPushPending=!1,this.canPush=!1,this.readsClosed=!1,this.statusOutput=!1,this.unpushedReadMessages=[],this.finalStatus=null,this.internalError=null,this.serverEndedCall=!1,this.connectionDropped=!1;let F=(G=D.getOptions()["grpc.max_receive_message_length"])!==null&&G!==void 0?G:Q3.DEFAULT_MAX_RECEIVE_MESSAGE_LENGTH;this.decoder=new aX6.StreamDecoder(F),A.on("response",(I,Y)=>{let W="";for(let J of Object.keys(I))W+="\t\t"+J+": "+I[J]+`
`;if(this.trace(`Received server headers:
`+W),this.httpStatusCode=I[":status"],Y&KP.constants.NGHTTP2_FLAG_END_STREAM)this.handleTrailers(I);else{let J;try{J=HP.Metadata.fromHttp2Headers(I)}catch(X){this.endCall({code:Q3.Status.UNKNOWN,details:X.message,metadata:new HP.Metadata});return}this.listener.onReceiveMetadata(J)}}),A.on("trailers",(I)=>{this.handleTrailers(I)}),A.on("data",(I)=>{if(this.statusOutput)return;this.trace("receive HTTP/2 data frame of length "+I.length);let Y;try{Y=this.decoder.write(I)}catch(W){if(this.httpStatusCode!==void 0&&this.httpStatusCode!==200){let J=SW0(this.httpStatusCode);this.cancelWithStatus(J.code,J.details)}else this.cancelWithStatus(Q3.Status.RESOURCE_EXHAUSTED,W.message);return}for(let W of Y)this.trace("parsed message of length "+W.length),this.callEventTracker.addMessageReceived(),this.tryPush(W)}),A.on("end",()=>{this.readsClosed=!0,this.maybeOutputStatus()}),A.on("close",()=>{this.serverEndedCall=!0,process.nextTick(()=>{var I;if(this.trace("HTTP/2 stream closed with code "+A.rstCode),((I=this.finalStatus)===null||I===void 0?void 0:I.code)===Q3.Status.OK)return;let Y,W="";switch(A.rstCode){case KP.constants.NGHTTP2_NO_ERROR:if(this.finalStatus!==null)return;if(this.httpStatusCode&&this.httpStatusCode!==200){let J=SW0(this.httpStatusCode);Y=J.code,W=J.details}else Y=Q3.Status.INTERNAL,W=`Received RST_STREAM with code ${A.rstCode} (Call ended without gRPC status)`;break;case KP.constants.NGHTTP2_REFUSED_STREAM:Y=Q3.Status.UNAVAILABLE,W="Stream refused by server";break;case KP.constants.NGHTTP2_CANCEL:if(this.connectionDropped)Y=Q3.Status.UNAVAILABLE,W="Connection dropped";else Y=Q3.Status.CANCELLED,W="Call cancelled";break;case KP.constants.NGHTTP2_ENHANCE_YOUR_CALM:Y=Q3.Status.RESOURCE_EXHAUSTED,W="Bandwidth exhausted or memory limit exceeded";break;case KP.constants.NGHTTP2_INADEQUATE_SECURITY:Y=Q3.Status.PERMISSION_DENIED,W="Protocol not secure enough";break;case KP.constants.NGHTTP2_INTERNAL_ERROR:if(Y=Q3.Status.INTERNAL,this.internalError===null)W=`Received RST_STREAM with code ${A.rstCode} (Internal server error)`;else if(this.internalError.code==="ECONNRESET"||this.internalError.code==="ETIMEDOUT")Y=Q3.Status.UNAVAILABLE,W=this.internalError.message;else W=`Received RST_STREAM with code ${A.rstCode} triggered by internal client error: ${this.internalError.message}`;break;default:Y=Q3.Status.INTERNAL,W=`Received RST_STREAM with code ${A.rstCode}`}this.endCall({code:Y,details:W,metadata:new HP.Metadata,rstCode:A.rstCode})})}),A.on("error",(I)=>{if(I.code!=="ERR_HTTP2_STREAM_ERROR")this.trace("Node error event: message="+I.message+" code="+I.code+" errno="+tX6(I.errno)+" syscall="+I.syscall),this.internalError=I;this.callEventTracker.onStreamEnd(!1)})}getDeadlineInfo(){return[`remote_addr=${this.getPeer()}`]}onDisconnect(){this.connectionDropped=!0,setImmediate(()=>{this.endCall({code:Q3.Status.UNAVAILABLE,details:"Connection dropped",metadata:new HP.Metadata})})}outputStatus(){if(!this.statusOutput)this.statusOutput=!0,this.trace("ended with status: code="+this.finalStatus.code+' details="'+this.finalStatus.details+'"'),this.callEventTracker.onCallEnd(this.finalStatus),process.nextTick(()=>{this.listener.onReceiveStatus(this.finalStatus)}),this.http2Stream.resume()}trace(A){sX6.trace(rX6.LogVerbosity.DEBUG,oX6,"["+this.callId+"] "+A)}endCall(A){if(this.finalStatus===null||this.finalStatus.code===Q3.Status.OK)this.finalStatus=A,this.maybeOutputStatus();this.destroyHttp2Stream()}maybeOutputStatus(){if(this.finalStatus!==null){if(this.finalStatus.code!==Q3.Status.OK||this.readsClosed&&this.unpushedReadMessages.length===0&&!this.isReadFilterPending&&!this.isPushPending)this.outputStatus()}}push(A){this.trace("pushing to reader message of length "+(A instanceof Buffer?A.length:null)),this.canPush=!1,this.isPushPending=!0,process.nextTick(()=>{if(this.isPushPending=!1,this.statusOutput)return;this.listener.onReceiveMessage(A),this.maybeOutputStatus()})}tryPush(A){if(this.canPush)this.http2Stream.pause(),this.push(A);else this.trace("unpushedReadMessages.push message of length "+A.length),this.unpushedReadMessages.push(A)}handleTrailers(A){this.serverEndedCall=!0,this.callEventTracker.onStreamEnd(!0);let B="";for(let G of Object.keys(A))B+="\t\t"+G+": "+A[G]+`
`;this.trace(`Received server trailers:
`+B);let Q;try{Q=HP.Metadata.fromHttp2Headers(A)}catch(G){Q=new HP.Metadata}let D=Q.getMap(),Z;if(typeof D["grpc-status"]==="string"){let G=Number(D["grpc-status"]);this.trace("received status code "+G+" from server"),Q.remove("grpc-status");let F="";if(typeof D["grpc-message"]==="string"){try{F=decodeURI(D["grpc-message"])}catch(I){F=D["grpc-message"]}Q.remove("grpc-message"),this.trace('received status details string "'+F+'" from server')}Z={code:G,details:F,metadata:Q}}else if(this.httpStatusCode)Z=SW0(this.httpStatusCode),Z.metadata=Q;else Z={code:Q3.Status.UNKNOWN,details:"No status information received",metadata:Q};this.endCall(Z)}destroyHttp2Stream(){var A;if(this.http2Stream.destroyed)return;if(this.serverEndedCall)this.http2Stream.end();else{let B;if(((A=this.finalStatus)===null||A===void 0?void 0:A.code)===Q3.Status.OK)B=KP.constants.NGHTTP2_NO_ERROR;else B=KP.constants.NGHTTP2_CANCEL;this.trace("close http2 stream with code "+B),this.http2Stream.close(B)}}cancelWithStatus(A,B){this.trace("cancelWithStatus code: "+A+' details: "'+B+'"'),this.endCall({code:A,details:B,metadata:new HP.Metadata})}getStatus(){return this.finalStatus}getPeer(){return this.transport.getPeerName()}getCallNumber(){return this.callId}startRead(){if(this.finalStatus!==null&&this.finalStatus.code!==Q3.Status.OK){this.readsClosed=!0,this.maybeOutputStatus();return}if(this.canPush=!0,this.unpushedReadMessages.length>0){let A=this.unpushedReadMessages.shift();this.push(A);return}this.http2Stream.resume()}sendMessageWithContext(A,B){this.trace("write() called with message of length "+B.length);let Q=(D)=>{process.nextTick(()=>{var Z;let G=Q3.Status.UNAVAILABLE;if((D===null||D===void 0?void 0:D.code)==="ERR_STREAM_WRITE_AFTER_END")G=Q3.Status.INTERNAL;if(D)this.cancelWithStatus(G,`Write error: ${D.message}`);(Z=A.callback)===null||Z===void 0||Z.call(A)})};this.trace("sending data chunk of length "+B.length),this.callEventTracker.addMessageSent();try{this.http2Stream.write(B,Q)}catch(D){this.endCall({code:Q3.Status.UNAVAILABLE,details:`Write failed with error ${D.message}`,metadata:new HP.Metadata})}}halfClose(){this.trace("end() called"),this.trace("calling end() on HTTP/2 stream"),this.http2Stream.end()}}Xs2.Http2SubchannelCall=Js2});
var D7=E((sl2)=>{var BY0,QY0,DY0,ZY0;Object.defineProperty(sl2,"__esModule",{value:!0});sl2.log=sl2.setLoggerVerbosity=sl2.setLogger=sl2.getLogger=void 0;sl2.trace=LG6;sl2.isTracerEnabled=al2;var c_=S6(),CG6=J1("process"),KG6=AY0().version,HG6={error:(A,...B)=>{console.error("E "+A,...B)},info:(A,...B)=>{console.error("I "+A,...B)},debug:(A,...B)=>{console.error("D "+A,...B)}},ku=HG6,mo=c_.LogVerbosity.ERROR,zG6=(QY0=(BY0=process.env.GRPC_NODE_VERBOSITY)!==null&&BY0!==void 0?BY0:process.env.GRPC_VERBOSITY)!==null&&QY0!==void 0?QY0:"";switch(zG6.toUpperCase()){case"DEBUG":mo=c_.LogVerbosity.DEBUG;break;case"INFO":mo=c_.LogVerbosity.INFO;break;case"ERROR":mo=c_.LogVerbosity.ERROR;break;case"NONE":mo=c_.LogVerbosity.NONE;break;default:}var EG6=()=>{return ku};sl2.getLogger=EG6;var UG6=(A)=>{ku=A};sl2.setLogger=UG6;var wG6=(A)=>{mo=A};sl2.setLoggerVerbosity=wG6;var $G6=(A,...B)=>{let Q;if(A>=mo){switch(A){case c_.LogVerbosity.DEBUG:Q=ku.debug;break;case c_.LogVerbosity.INFO:Q=ku.info;break;case c_.LogVerbosity.ERROR:Q=ku.error;break}if(!Q)Q=ku.error;if(Q)Q.bind(ku)(...B)}};sl2.log=$G6;var qG6=(ZY0=(DY0=process.env.GRPC_NODE_TRACE)!==null&&DY0!==void 0?DY0:process.env.GRPC_TRACE)!==null&&ZY0!==void 0?ZY0:"",GY0=new Set,nl2=new Set;for(let A of qG6.split(","))if(A.startsWith("-"))nl2.add(A.substring(1));else GY0.add(A);var NG6=GY0.has("all");function LG6(A,B,Q){if(al2(B))sl2.log(A,new Date().toISOString()+" | v"+KG6+" "+CG6.pid+" | "+B+" | "+Q)}function al2(A){return!nl2.has(A)&&(NG6||GY0.has(A))}});
var DJ0=E((mr2)=>{Object.defineProperty(mr2,"__esModule",{value:!0});mr2.BaseServerInterceptingCall=mr2.ServerInterceptingCall=mr2.ResponderBuilder=mr2.ServerListenerBuilder=void 0;mr2.isInterceptingServerListener=bC6;mr2.getServerInterceptingCall=mC6;var AJ0=tW(),kV=S6(),Xt=J1("http2"),Sr2=YT1(),jr2=J1("zlib"),vC6=PW0(),xr2=D7(),vr2="server_call";function mu(A){xr2.trace(kV.LogVerbosity.DEBUG,vr2,A)}class br2{constructor(){this.metadata=void 0,this.message=void 0,this.halfClose=void 0,this.cancel=void 0}withOnReceiveMetadata(A){return this.metadata=A,this}withOnReceiveMessage(A){return this.message=A,this}withOnReceiveHalfClose(A){return this.halfClose=A,this}withOnCancel(A){return this.cancel=A,this}build(){return{onReceiveMetadata:this.metadata,onReceiveMessage:this.message,onReceiveHalfClose:this.halfClose,onCancel:this.cancel}}}mr2.ServerListenerBuilder=br2;function bC6(A){return A.onReceiveMetadata!==void 0&&A.onReceiveMetadata.length===1}class fr2{constructor(A,B){this.listener=A,this.nextListener=B,this.cancelled=!1,this.processingMetadata=!1,this.hasPendingMessage=!1,this.pendingMessage=null,this.processingMessage=!1,this.hasPendingHalfClose=!1}processPendingMessage(){if(this.hasPendingMessage)this.nextListener.onReceiveMessage(this.pendingMessage),this.pendingMessage=null,this.hasPendingMessage=!1}processPendingHalfClose(){if(this.hasPendingHalfClose)this.nextListener.onReceiveHalfClose(),this.hasPendingHalfClose=!1}onReceiveMetadata(A){if(this.cancelled)return;this.processingMetadata=!0,this.listener.onReceiveMetadata(A,(B)=>{if(this.processingMetadata=!1,this.cancelled)return;this.nextListener.onReceiveMetadata(B),this.processPendingMessage(),this.processPendingHalfClose()})}onReceiveMessage(A){if(this.cancelled)return;this.processingMessage=!0,this.listener.onReceiveMessage(A,(B)=>{if(this.processingMessage=!1,this.cancelled)return;if(this.processingMetadata)this.pendingMessage=B,this.hasPendingMessage=!0;else this.nextListener.onReceiveMessage(B),this.processPendingHalfClose()})}onReceiveHalfClose(){if(this.cancelled)return;this.listener.onReceiveHalfClose(()=>{if(this.cancelled)return;if(this.processingMetadata||this.processingMessage)this.hasPendingHalfClose=!0;else this.nextListener.onReceiveHalfClose()})}onCancel(){this.cancelled=!0,this.listener.onCancel(),this.nextListener.onCancel()}}class hr2{constructor(){this.start=void 0,this.metadata=void 0,this.message=void 0,this.status=void 0}withStart(A){return this.start=A,this}withSendMetadata(A){return this.metadata=A,this}withSendMessage(A){return this.message=A,this}withSendStatus(A){return this.status=A,this}build(){return{start:this.start,sendMetadata:this.metadata,sendMessage:this.message,sendStatus:this.status}}}mr2.ResponderBuilder=hr2;var HP1={onReceiveMetadata:(A,B)=>{B(A)},onReceiveMessage:(A,B)=>{B(A)},onReceiveHalfClose:(A)=>{A()},onCancel:()=>{}},zP1={start:(A)=>{A()},sendMetadata:(A,B)=>{B(A)},sendMessage:(A,B)=>{B(A)},sendStatus:(A,B)=>{B(A)}};class gr2{constructor(A,B){var Q,D,Z,G;this.nextCall=A,this.processingMetadata=!1,this.sentMetadata=!1,this.processingMessage=!1,this.pendingMessage=null,this.pendingMessageCallback=null,this.pendingStatus=null,this.responder={start:(Q=B===null||B===void 0?void 0:B.start)!==null&&Q!==void 0?Q:zP1.start,sendMetadata:(D=B===null||B===void 0?void 0:B.sendMetadata)!==null&&D!==void 0?D:zP1.sendMetadata,sendMessage:(Z=B===null||B===void 0?void 0:B.sendMessage)!==null&&Z!==void 0?Z:zP1.sendMessage,sendStatus:(G=B===null||B===void 0?void 0:B.sendStatus)!==null&&G!==void 0?G:zP1.sendStatus}}processPendingMessage(){if(this.pendingMessageCallback)this.nextCall.sendMessage(this.pendingMessage,this.pendingMessageCallback),this.pendingMessage=null,this.pendingMessageCallback=null}processPendingStatus(){if(this.pendingStatus)this.nextCall.sendStatus(this.pendingStatus),this.pendingStatus=null}start(A){this.responder.start((B)=>{var Q,D,Z,G;let F={onReceiveMetadata:(Q=B===null||B===void 0?void 0:B.onReceiveMetadata)!==null&&Q!==void 0?Q:HP1.onReceiveMetadata,onReceiveMessage:(D=B===null||B===void 0?void 0:B.onReceiveMessage)!==null&&D!==void 0?D:HP1.onReceiveMessage,onReceiveHalfClose:(Z=B===null||B===void 0?void 0:B.onReceiveHalfClose)!==null&&Z!==void 0?Z:HP1.onReceiveHalfClose,onCancel:(G=B===null||B===void 0?void 0:B.onCancel)!==null&&G!==void 0?G:HP1.onCancel},I=new fr2(F,A);this.nextCall.start(I)})}sendMetadata(A){this.processingMetadata=!0,this.sentMetadata=!0,this.responder.sendMetadata(A,(B)=>{this.processingMetadata=!1,this.nextCall.sendMetadata(B),this.processPendingMessage(),this.processPendingStatus()})}sendMessage(A,B){if(this.processingMessage=!0,!this.sentMetadata)this.sendMetadata(new AJ0.Metadata);this.responder.sendMessage(A,(Q)=>{if(this.processingMessage=!1,this.processingMetadata)this.pendingMessage=Q,this.pendingMessageCallback=B;else this.nextCall.sendMessage(Q,B)})}sendStatus(A){this.responder.sendStatus(A,(B)=>{if(this.processingMetadata||this.processingMessage)this.pendingStatus=B;else this.nextCall.sendStatus(B)})}startRead(){this.nextCall.startRead()}getPeer(){return this.nextCall.getPeer()}getDeadline(){return this.nextCall.getDeadline()}getHost(){return this.nextCall.getHost()}}mr2.ServerInterceptingCall=gr2;var ur2="grpc-accept-encoding",BJ0="grpc-encoding",kr2="grpc-message",yr2="grpc-status",eW0="grpc-timeout",fC6=/(\d{1,8})\s*([HMSmun])/,hC6={H:3600000,M:60000,S:1000,m:1,u:0.001,n:0.000001},gC6={[ur2]:"identity,deflate,gzip",[BJ0]:"identity"},_r2={[Xt.constants.HTTP2_HEADER_STATUS]:Xt.constants.HTTP_STATUS_OK,[Xt.constants.HTTP2_HEADER_CONTENT_TYPE]:"application/grpc+proto"},uC6={waitForTrailers:!0};class QJ0{constructor(A,B,Q,D,Z){var G;if(this.stream=A,this.callEventTracker=Q,this.handler=D,this.listener=null,this.deadlineTimer=null,this.deadline=1/0,this.maxSendMessageSize=kV.DEFAULT_MAX_SEND_MESSAGE_LENGTH,this.maxReceiveMessageSize=kV.DEFAULT_MAX_RECEIVE_MESSAGE_LENGTH,this.cancelled=!1,this.metadataSent=!1,this.wantTrailers=!1,this.cancelNotified=!1,this.incomingEncoding="identity",this.readQueue=[],this.isReadPending=!1,this.receivedHalfClose=!1,this.streamEnded=!1,this.stream.once("error",(W)=>{}),this.stream.once("close",()=>{var W;if(mu("Request to method "+((W=this.handler)===null||W===void 0?void 0:W.path)+" stream closed with rstCode "+this.stream.rstCode),this.callEventTracker&&!this.streamEnded)this.streamEnded=!0,this.callEventTracker.onStreamEnd(!1),this.callEventTracker.onCallEnd({code:kV.Status.CANCELLED,details:"Stream closed before sending status",metadata:null});this.notifyOnCancel()}),this.stream.on("data",(W)=>{this.handleDataFrame(W)}),this.stream.pause(),this.stream.on("end",()=>{this.handleEndEvent()}),"grpc.max_send_message_length"in Z)this.maxSendMessageSize=Z["grpc.max_send_message_length"];if("grpc.max_receive_message_length"in Z)this.maxReceiveMessageSize=Z["grpc.max_receive_message_length"];this.host=(G=B[":authority"])!==null&&G!==void 0?G:B.host,this.decoder=new vC6.StreamDecoder(this.maxReceiveMessageSize);let F=AJ0.Metadata.fromHttp2Headers(B);if(xr2.isTracerEnabled(vr2))mu("Request to "+this.handler.path+" received headers "+JSON.stringify(F.toJSON()));let I=F.get(eW0);if(I.length>0)this.handleTimeoutHeader(I[0]);let Y=F.get(BJ0);if(Y.length>0)this.incomingEncoding=Y[0];F.remove(eW0),F.remove(BJ0),F.remove(ur2),F.remove(Xt.constants.HTTP2_HEADER_ACCEPT_ENCODING),F.remove(Xt.constants.HTTP2_HEADER_TE),F.remove(Xt.constants.HTTP2_HEADER_CONTENT_TYPE),this.metadata=F}handleTimeoutHeader(A){let B=A.toString().match(fC6);if(B===null){let Z={code:kV.Status.INTERNAL,details:`Invalid ${eW0} value "${A}"`,metadata:null};process.nextTick(()=>{this.sendStatus(Z)});return}let Q=+B[1]*hC6[B[2]]|0,D=new Date;this.deadline=D.setMilliseconds(D.getMilliseconds()+Q),this.deadlineTimer=setTimeout(()=>{let Z={code:kV.Status.DEADLINE_EXCEEDED,details:"Deadline exceeded",metadata:null};this.sendStatus(Z)},Q)}checkCancelled(){if(!this.cancelled&&(this.stream.destroyed||this.stream.closed))this.notifyOnCancel(),this.cancelled=!0;return this.cancelled}notifyOnCancel(){if(this.cancelNotified)return;if(this.cancelNotified=!0,this.cancelled=!0,process.nextTick(()=>{var A;(A=this.listener)===null||A===void 0||A.onCancel()}),this.deadlineTimer)clearTimeout(this.deadlineTimer);this.stream.resume()}maybeSendMetadata(){if(!this.metadataSent)this.sendMetadata(new AJ0.Metadata)}serializeMessage(A){let B=this.handler.serialize(A),Q=B.byteLength,D=Buffer.allocUnsafe(Q+5);return D.writeUInt8(0,0),D.writeUInt32BE(Q,1),B.copy(D,5),D}decompressMessage(A,B){let Q=A.subarray(5);if(B==="identity")return Q;else if(B==="deflate"||B==="gzip"){let D;if(B==="deflate")D=jr2.createInflate();else D=jr2.createGunzip();return new Promise((Z,G)=>{let F=0,I=[];D.on("data",(Y)=>{if(I.push(Y),F+=Y.byteLength,this.maxReceiveMessageSize!==-1&&F>this.maxReceiveMessageSize)D.destroy(),G({code:kV.Status.RESOURCE_EXHAUSTED,details:`Received message that decompresses to a size larger than ${this.maxReceiveMessageSize}`})}),D.on("end",()=>{Z(Buffer.concat(I))}),D.write(Q),D.end()})}else return Promise.reject({code:kV.Status.UNIMPLEMENTED,details:`Received message compressed with unsupported encoding "${B}"`})}async decompressAndMaybePush(A){if(A.type!=="COMPRESSED")throw new Error(`Invalid queue entry type: ${A.type}`);let Q=A.compressedMessage.readUInt8(0)===1?this.incomingEncoding:"identity",D;try{D=await this.decompressMessage(A.compressedMessage,Q)}catch(Z){this.sendStatus(Z);return}try{A.parsedMessage=this.handler.deserialize(D)}catch(Z){this.sendStatus({code:kV.Status.INTERNAL,details:`Error deserializing request: ${Z.message}`});return}A.type="READABLE",this.maybePushNextMessage()}maybePushNextMessage(){if(this.listener&&this.isReadPending&&this.readQueue.length>0&&this.readQueue[0].type!=="COMPRESSED"){this.isReadPending=!1;let A=this.readQueue.shift();if(A.type==="READABLE")this.listener.onReceiveMessage(A.parsedMessage);else this.listener.onReceiveHalfClose()}}handleDataFrame(A){var B;if(this.checkCancelled())return;mu("Request to "+this.handler.path+" received data frame of size "+A.length);let Q;try{Q=this.decoder.write(A)}catch(D){this.sendStatus({code:kV.Status.RESOURCE_EXHAUSTED,details:D.message});return}for(let D of Q){this.stream.pause();let Z={type:"COMPRESSED",compressedMessage:D,parsedMessage:null};this.readQueue.push(Z),this.decompressAndMaybePush(Z),(B=this.callEventTracker)===null||B===void 0||B.addMessageReceived()}}handleEndEvent(){this.readQueue.push({type:"HALF_CLOSE",compressedMessage:null,parsedMessage:null}),this.receivedHalfClose=!0,this.maybePushNextMessage()}start(A){if(mu("Request to "+this.handler.path+" start called"),this.checkCancelled())return;this.listener=A,A.onReceiveMetadata(this.metadata)}sendMetadata(A){if(this.checkCancelled())return;if(this.metadataSent)return;this.metadataSent=!0;let B=A?A.toHttp2Headers():null,Q=Object.assign(Object.assign(Object.assign({},_r2),gC6),B);this.stream.respond(Q,uC6)}sendMessage(A,B){if(this.checkCancelled())return;let Q;try{Q=this.serializeMessage(A)}catch(D){this.sendStatus({code:kV.Status.INTERNAL,details:`Error serializing response: ${Sr2.getErrorMessage(D)}`,metadata:null});return}if(this.maxSendMessageSize!==-1&&Q.length-5>this.maxSendMessageSize){this.sendStatus({code:kV.Status.RESOURCE_EXHAUSTED,details:`Sent message larger than max (${Q.length} vs. ${this.maxSendMessageSize})`,metadata:null});return}this.maybeSendMetadata(),mu("Request to "+this.handler.path+" sent data frame of size "+Q.length),this.stream.write(Q,(D)=>{var Z;if(D){this.sendStatus({code:kV.Status.INTERNAL,details:`Error writing message: ${Sr2.getErrorMessage(D)}`,metadata:null});return}(Z=this.callEventTracker)===null||Z===void 0||Z.addMessageSent(),B()})}sendStatus(A){var B,Q;if(this.checkCancelled())return;if(mu("Request to method "+((B=this.handler)===null||B===void 0?void 0:B.path)+" ended with status code: "+kV.Status[A.code]+" details: "+A.details),this.metadataSent)if(!this.wantTrailers)this.wantTrailers=!0,this.stream.once("wantTrailers",()=>{var D;if(this.callEventTracker&&!this.streamEnded)this.streamEnded=!0,this.callEventTracker.onStreamEnd(!0),this.callEventTracker.onCallEnd(A);let Z=Object.assign({[yr2]:A.code,[kr2]:encodeURI(A.details)},(D=A.metadata)===null||D===void 0?void 0:D.toHttp2Headers());this.stream.sendTrailers(Z),this.notifyOnCancel()}),this.stream.end();else this.notifyOnCancel();else{if(this.callEventTracker&&!this.streamEnded)this.streamEnded=!0,this.callEventTracker.onStreamEnd(!0),this.callEventTracker.onCallEnd(A);let D=Object.assign(Object.assign({[yr2]:A.code,[kr2]:encodeURI(A.details)},_r2),(Q=A.metadata)===null||Q===void 0?void 0:Q.toHttp2Headers());this.stream.respond(D,{endStream:!0}),this.notifyOnCancel()}}startRead(){if(mu("Request to "+this.handler.path+" startRead called"),this.checkCancelled())return;if(this.isReadPending=!0,this.readQueue.length===0){if(!this.receivedHalfClose)this.stream.resume()}else this.maybePushNextMessage()}getPeer(){var A;let B=(A=this.stream.session)===null||A===void 0?void 0:A.socket;if(B===null||B===void 0?void 0:B.remoteAddress)if(B.remotePort)return`${B.remoteAddress}:${B.remotePort}`;else return B.remoteAddress;else return"unknown"}getDeadline(){return this.deadline}getHost(){return this.host}}mr2.BaseServerInterceptingCall=QJ0;function mC6(A,B,Q,D,Z,G){let F={path:Z.path,requestStream:Z.type==="clientStream"||Z.type==="bidi",responseStream:Z.type==="serverStream"||Z.type==="bidi",requestDeserialize:Z.deserialize,responseSerialize:Z.serialize},I=new QJ0(B,Q,D,Z,G);return A.reduce((Y,W)=>{return W(F,Y)},I)}});
var EY0=E((Up2)=>{Object.defineProperty(Up2,"__esModule",{value:!0});Up2.validateRetryThrottling=zp2;Up2.validateServiceConfig=Ep2;Up2.extractAndSelectServiceConfig=mF6;var yF6=J1("os"),ET1=S6(),UT1=/^\d+(\.\d{1,9})?s$/,_F6="node";function xF6(A){if("service"in A&&A.service!==""){if(typeof A.service!=="string")throw new Error(`Invalid method config name: invalid service: expected type string, got ${typeof A.service}`);if("method"in A&&A.method!==""){if(typeof A.method!=="string")throw new Error(`Invalid method config name: invalid method: expected type string, got ${typeof A.service}`);return{service:A.service,method:A.method}}else return{service:A.service}}else{if("method"in A&&A.method!==void 0)throw new Error("Invalid method config name: method set with empty or unset service");return{}}}function vF6(A){if(!("maxAttempts"in A)||!Number.isInteger(A.maxAttempts)||A.maxAttempts<2)throw new Error("Invalid method config retry policy: maxAttempts must be an integer at least 2");if(!("initialBackoff"in A)||typeof A.initialBackoff!=="string"||!UT1.test(A.initialBackoff))throw new Error("Invalid method config retry policy: initialBackoff must be a string consisting of a positive integer or decimal followed by s");if(!("maxBackoff"in A)||typeof A.maxBackoff!=="string"||!UT1.test(A.maxBackoff))throw new Error("Invalid method config retry policy: maxBackoff must be a string consisting of a positive integer or decimal followed by s");if(!("backoffMultiplier"in A)||typeof A.backoffMultiplier!=="number"||A.backoffMultiplier<=0)throw new Error("Invalid method config retry policy: backoffMultiplier must be a number greater than 0");if(!(("retryableStatusCodes"in A)&&Array.isArray(A.retryableStatusCodes)))throw new Error("Invalid method config retry policy: retryableStatusCodes is required");if(A.retryableStatusCodes.length===0)throw new Error("Invalid method config retry policy: retryableStatusCodes must be non-empty");for(let B of A.retryableStatusCodes)if(typeof B==="number"){if(!Object.values(ET1.Status).includes(B))throw new Error("Invalid method config retry policy: retryableStatusCodes value not in status code range")}else if(typeof B==="string"){if(!Object.values(ET1.Status).includes(B.toUpperCase()))throw new Error("Invalid method config retry policy: retryableStatusCodes value not a status code name")}else throw new Error("Invalid method config retry policy: retryableStatusCodes value must be a string or number");return{maxAttempts:A.maxAttempts,initialBackoff:A.initialBackoff,maxBackoff:A.maxBackoff,backoffMultiplier:A.backoffMultiplier,retryableStatusCodes:A.retryableStatusCodes}}function bF6(A){if(!("maxAttempts"in A)||!Number.isInteger(A.maxAttempts)||A.maxAttempts<2)throw new Error("Invalid method config hedging policy: maxAttempts must be an integer at least 2");if("hedgingDelay"in A&&(typeof A.hedgingDelay!=="string"||!UT1.test(A.hedgingDelay)))throw new Error("Invalid method config hedging policy: hedgingDelay must be a string consisting of a positive integer followed by s");if("nonFatalStatusCodes"in A&&Array.isArray(A.nonFatalStatusCodes))for(let Q of A.nonFatalStatusCodes)if(typeof Q==="number"){if(!Object.values(ET1.Status).includes(Q))throw new Error("Invalid method config hedging policy: nonFatalStatusCodes value not in status code range")}else if(typeof Q==="string"){if(!Object.values(ET1.Status).includes(Q.toUpperCase()))throw new Error("Invalid method config hedging policy: nonFatalStatusCodes value not a status code name")}else throw new Error("Invalid method config hedging policy: nonFatalStatusCodes value must be a string or number");let B={maxAttempts:A.maxAttempts};if(A.hedgingDelay)B.hedgingDelay=A.hedgingDelay;if(A.nonFatalStatusCodes)B.nonFatalStatusCodes=A.nonFatalStatusCodes;return B}function fF6(A){var B;let Q={name:[]};if(!("name"in A)||!Array.isArray(A.name))throw new Error("Invalid method config: invalid name array");for(let D of A.name)Q.name.push(xF6(D));if("waitForReady"in A){if(typeof A.waitForReady!=="boolean")throw new Error("Invalid method config: invalid waitForReady");Q.waitForReady=A.waitForReady}if("timeout"in A)if(typeof A.timeout==="object"){if(!("seconds"in A.timeout)||typeof A.timeout.seconds!=="number")throw new Error("Invalid method config: invalid timeout.seconds");if(!("nanos"in A.timeout)||typeof A.timeout.nanos!=="number")throw new Error("Invalid method config: invalid timeout.nanos");Q.timeout=A.timeout}else if(typeof A.timeout==="string"&&UT1.test(A.timeout)){let D=A.timeout.substring(0,A.timeout.length-1).split(".");Q.timeout={seconds:D[0]|0,nanos:((B=D[1])!==null&&B!==void 0?B:0)|0}}else throw new Error("Invalid method config: invalid timeout");if("maxRequestBytes"in A){if(typeof A.maxRequestBytes!=="number")throw new Error("Invalid method config: invalid maxRequestBytes");Q.maxRequestBytes=A.maxRequestBytes}if("maxResponseBytes"in A){if(typeof A.maxResponseBytes!=="number")throw new Error("Invalid method config: invalid maxRequestBytes");Q.maxResponseBytes=A.maxResponseBytes}if("retryPolicy"in A)if("hedgingPolicy"in A)throw new Error("Invalid method config: retryPolicy and hedgingPolicy cannot both be specified");else Q.retryPolicy=vF6(A.retryPolicy);else if("hedgingPolicy"in A)Q.hedgingPolicy=bF6(A.hedgingPolicy);return Q}function zp2(A){if(!("maxTokens"in A)||typeof A.maxTokens!=="number"||A.maxTokens<=0||A.maxTokens>1000)throw new Error("Invalid retryThrottling: maxTokens must be a number in (0, 1000]");if(!("tokenRatio"in A)||typeof A.tokenRatio!=="number"||A.tokenRatio<=0)throw new Error("Invalid retryThrottling: tokenRatio must be a number greater than 0");return{maxTokens:+A.maxTokens.toFixed(3),tokenRatio:+A.tokenRatio.toFixed(3)}}function hF6(A){if(!(typeof A==="object"&&A!==null))throw new Error(`Invalid loadBalancingConfig: unexpected type ${typeof A}`);let B=Object.keys(A);if(B.length>1)throw new Error(`Invalid loadBalancingConfig: unexpected multiple keys ${B}`);if(B.length===0)throw new Error("Invalid loadBalancingConfig: load balancing policy name required");return{[B[0]]:A[B[0]]}}function Ep2(A){let B={loadBalancingConfig:[],methodConfig:[]};if("loadBalancingPolicy"in A)if(typeof A.loadBalancingPolicy==="string")B.loadBalancingPolicy=A.loadBalancingPolicy;else throw new Error("Invalid service config: invalid loadBalancingPolicy");if("loadBalancingConfig"in A)if(Array.isArray(A.loadBalancingConfig))for(let D of A.loadBalancingConfig)B.loadBalancingConfig.push(hF6(D));else throw new Error("Invalid service config: invalid loadBalancingConfig");if("methodConfig"in A){if(Array.isArray(A.methodConfig))for(let D of A.methodConfig)B.methodConfig.push(fF6(D))}if("retryThrottling"in A)B.retryThrottling=zp2(A.retryThrottling);let Q=[];for(let D of B.methodConfig)for(let Z of D.name){for(let G of Q)if(Z.service===G.service&&Z.method===G.method)throw new Error(`Invalid service config: duplicate name ${Z.service}/${Z.method}`);Q.push(Z)}return B}function gF6(A){if(!("serviceConfig"in A))throw new Error("Invalid service config choice: missing service config");let B={serviceConfig:Ep2(A.serviceConfig)};if("clientLanguage"in A)if(Array.isArray(A.clientLanguage)){B.clientLanguage=[];for(let D of A.clientLanguage)if(typeof D==="string")B.clientLanguage.push(D);else throw new Error("Invalid service config choice: invalid clientLanguage")}else throw new Error("Invalid service config choice: invalid clientLanguage");if("clientHostname"in A)if(Array.isArray(A.clientHostname)){B.clientHostname=[];for(let D of A.clientHostname)if(typeof D==="string")B.clientHostname.push(D);else throw new Error("Invalid service config choice: invalid clientHostname")}else throw new Error("Invalid service config choice: invalid clientHostname");if("percentage"in A)if(typeof A.percentage==="number"&&0<=A.percentage&&A.percentage<=100)B.percentage=A.percentage;else throw new Error("Invalid service config choice: invalid percentage");let Q=["clientLanguage","percentage","clientHostname","serviceConfig"];for(let D in A)if(!Q.includes(D))throw new Error(`Invalid service config choice: unexpected field ${D}`);return B}function uF6(A,B){if(!Array.isArray(A))throw new Error("Invalid service config list");for(let Q of A){let D=gF6(Q);if(typeof D.percentage==="number"&&B>D.percentage)continue;if(Array.isArray(D.clientHostname)){let Z=!1;for(let G of D.clientHostname)if(G===yF6.hostname())Z=!0;if(!Z)continue}if(Array.isArray(D.clientLanguage)){let Z=!1;for(let G of D.clientLanguage)if(G===_F6)Z=!0;if(!Z)continue}return D.serviceConfig}throw new Error("No matching service config found")}function mF6(A,B){for(let Q of A)if(Q.length>0&&Q[0].startsWith("grpc_config=")){let D=Q.join("").substring(12),Z=JSON.parse(D);return uF6(Z,B)}return null}});
var Ea2=E((Ha2)=>{Object.defineProperty(Ha2,"__esModule",{value:!0});Ha2.addCommonProtos=Ha2.loadProtosWithOptionsSync=Ha2.loadProtosWithOptions=void 0;var Va2=J1("fs"),Ca2=J1("path"),Zt=pT1();function Ka2(A,B){let Q=A.resolvePath;A.resolvePath=(D,Z)=>{if(Ca2.isAbsolute(Z))return Z;for(let G of B){let F=Ca2.join(G,Z);try{return Va2.accessSync(F,Va2.constants.R_OK),F}catch(I){continue}}return process.emitWarning(`${Z} not found in any of the include paths ${B}`),Q(D,Z)}}async function UJ6(A,B){let Q=new Zt.Root;if(B=B||{},B.includeDirs){if(!Array.isArray(B.includeDirs))return Promise.reject(new Error("The includeDirs option must be an array"));Ka2(Q,B.includeDirs)}let D=await Q.load(A,B);return D.resolveAll(),D}Ha2.loadProtosWithOptions=UJ6;function wJ6(A,B){let Q=new Zt.Root;if(B=B||{},B.includeDirs){if(!Array.isArray(B.includeDirs))throw new Error("The includeDirs option must be an array");Ka2(Q,B.includeDirs)}let D=Q.loadSync(A,B);return D.resolveAll(),D}Ha2.loadProtosWithOptionsSync=wJ6;function $J6(){let A=Wa2(),B=JW0(),Q=Ja2(),D=Xa2();Zt.common("api",A.nested.google.nested.protobuf.nested),Zt.common("descriptor",B.nested.google.nested.protobuf.nested),Zt.common("source_context",Q.nested.google.nested.protobuf.nested),Zt.common("type",D.nested.google.nested.protobuf.nested)}Ha2.addCommonProtos=$J6});
var FP1=E((ms2)=>{Object.defineProperty(ms2,"__esModule",{value:!0});ms2.restrictControlPlaneStatusCode=lV6;var GM=S6(),cV6=[GM.Status.OK,GM.Status.INVALID_ARGUMENT,GM.Status.NOT_FOUND,GM.Status.ALREADY_EXISTS,GM.Status.FAILED_PRECONDITION,GM.Status.ABORTED,GM.Status.OUT_OF_RANGE,GM.Status.DATA_LOSS];function lV6(A,B){if(cV6.includes(A))return{code:GM.Status.INTERNAL,details:`Invalid status from control plane: ${A} ${GM.Status[A]} ${B}`};else return{code:A,details:B}}});
var Gn2=E((Zn2)=>{var bY0=Zn2,Dn2=bY0.isAbsolute=function A(B){return/^(?:\/|\w+:)/.test(B)},vY0=bY0.normalize=function A(B){B=B.replace(/\\/g,"/").replace(/\/{2,}/g,"/");var Q=B.split("/"),D=Dn2(B),Z="";if(D)Z=Q.shift()+"/";for(var G=0;G<Q.length;)if(Q[G]==="..")if(G>0&&Q[G-1]!=="..")Q.splice(--G,2);else if(D)Q.splice(G,1);else++G;else if(Q[G]===".")Q.splice(G,1);else++G;return Z+Q.join("/")};bY0.resolve=function A(B,Q,D){if(!D)Q=vY0(Q);if(Dn2(Q))return Q;if(!D)B=vY0(B);return(B=B.replace(/(?:\/|^)[^/]+$/,"")).length?vY0(B+"/"+Q):Q}});
var H31=E((Op2)=>{Object.defineProperty(Op2,"__esModule",{value:!0});Op2.BackoffTimeout=void 0;var sF6=S6(),rF6=D7(),oF6="backoff",tF6=1000,eF6=1.6,AI6=120000,BI6=0.2;function QI6(A,B){return Math.random()*(B-A)+A}class $T1{constructor(A,B){if(this.callback=A,this.initialDelay=tF6,this.multiplier=eF6,this.maxDelay=AI6,this.jitter=BI6,this.running=!1,this.hasRef=!0,this.startTime=new Date,this.endTime=new Date,this.id=$T1.getNextId(),B){if(B.initialDelay)this.initialDelay=B.initialDelay;if(B.multiplier)this.multiplier=B.multiplier;if(B.jitter)this.jitter=B.jitter;if(B.maxDelay)this.maxDelay=B.maxDelay}this.trace("constructed initialDelay="+this.initialDelay+" multiplier="+this.multiplier+" jitter="+this.jitter+" maxDelay="+this.maxDelay),this.nextDelay=this.initialDelay,this.timerId=setTimeout(()=>{},0),clearTimeout(this.timerId)}static getNextId(){return this.nextId++}trace(A){rF6.trace(sF6.LogVerbosity.DEBUG,oF6,"{"+this.id+"} "+A)}runTimer(A){var B,Q;if(this.trace("runTimer(delay="+A+")"),this.endTime=this.startTime,this.endTime.setMilliseconds(this.endTime.getMilliseconds()+A),clearTimeout(this.timerId),this.timerId=setTimeout(()=>{this.trace("timer fired"),this.running=!1,this.callback()},A),!this.hasRef)(Q=(B=this.timerId).unref)===null||Q===void 0||Q.call(B)}runOnce(){this.trace("runOnce()"),this.running=!0,this.startTime=new Date,this.runTimer(this.nextDelay);let A=Math.min(this.nextDelay*this.multiplier,this.maxDelay),B=A*this.jitter;this.nextDelay=A+QI6(-B,B)}stop(){this.trace("stop()"),clearTimeout(this.timerId),this.running=!1}reset(){if(this.trace("reset() running="+this.running),this.nextDelay=this.initialDelay,this.running){let A=new Date,B=this.startTime;if(B.setMilliseconds(B.getMilliseconds()+this.nextDelay),clearTimeout(this.timerId),A<B)this.runTimer(B.getTime()-A.getTime());else this.running=!1}}isRunning(){return this.running}ref(){var A,B;this.hasRef=!0,(B=(A=this.timerId).ref)===null||B===void 0||B.call(A)}unref(){var A,B;this.hasRef=!1,(B=(A=this.timerId).unref)===null||B===void 0||B.call(A)}getEndTime(){return this.endTime}}Op2.BackoffTimeout=$T1;$T1.nextId=0});
var IJ0=E((Ao2)=>{Object.defineProperty(Ao2,"__esModule",{value:!0});Ao2.msToDuration=FK6;Ao2.durationToMs=IK6;Ao2.isDuration=YK6;Ao2.parseDuration=JK6;function FK6(A){return{seconds:A/1000|0,nanos:A%1000*1e6|0}}function IK6(A){return A.seconds*1000+A.nanos/1e6|0}function YK6(A){return typeof A.seconds==="number"&&typeof A.nanos==="number"}var WK6=/^(\d+)(?:\.(\d+))?s$/;function JK6(A){let B=A.match(WK6);if(!B)return null;return{seconds:Number.parseInt(B[1],10),nanos:B[2]?Number.parseInt(B[2].padEnd(9,"0"),10):0}}});
var IW0=E((Bx5,nn2)=>{nn2.exports=in2;var FW0=/[\s{}=;:[\],'"()<>]/g,bW6=/(?:"([^"\\]*(?:\\.[^"\\]*)*)")/g,fW6=/(?:'([^'\\]*(?:\\.[^'\\]*)*)')/g,hW6=/^ *[*/]+ */,gW6=/^\s*\*?\/*/,uW6=/\n/g,mW6=/\s/,dW6=/\\(.?)/g,cW6={"0":"\x00",r:"\r",n:`
`,t:"\t"};function pn2(A){return A.replace(dW6,function(B,Q){switch(Q){case"\\":case"":return Q;default:return cW6[Q]||""}})}in2.unescape=pn2;function in2(A,B){A=A.toString();var Q=0,D=A.length,Z=1,G=0,F={},I=[],Y=null;function W(O){return Error("illegal "+O+" (line "+Z+")")}function J(){var O=Y==="'"?fW6:bW6;O.lastIndex=Q-1;var R=O.exec(A);if(!R)throw W("string");return Q=O.lastIndex,z(Y),Y=null,pn2(R[1])}function X(O){return A.charAt(O)}function V(O,R,T){var j={type:A.charAt(O++),lineEmpty:!1,leading:T},f;if(B)f=2;else f=3;var k=O-f,c;do if(--k<0||(c=A.charAt(k))===`
`){j.lineEmpty=!0;break}while(c===" "||c==="\t");var h=A.substring(O,R).split(uW6);for(var n=0;n<h.length;++n)h[n]=h[n].replace(B?gW6:hW6,"").trim();j.text=h.join(`
`).trim(),F[Z]=j,G=Z}function C(O){var R=K(O),T=A.substring(O,R),j=/^\s*\/\//.test(T);return j}function K(O){var R=O;while(R<D&&X(R)!==`
`)R++;return R}function H(){if(I.length>0)return I.shift();if(Y)return J();var O,R,T,j,f,k=Q===0;do{if(Q===D)return null;O=!1;while(mW6.test(T=X(Q))){if(T===`
`)k=!0,++Z;if(++Q===D)return null}if(X(Q)==="/"){if(++Q===D)throw W("comment");if(X(Q)==="/")if(!B){f=X(j=Q+1)==="/";while(X(++Q)!==`
`)if(Q===D)return null;if(++Q,f)V(j,Q-1,k),k=!0;++Z,O=!0}else{if(j=Q,f=!1,C(Q-1)){f=!0;do{if(Q=K(Q),Q===D)break;if(Q++,!k)break}while(C(Q))}else Q=Math.min(D,K(Q)+1);if(f)V(j,Q,k),k=!0;Z++,O=!0}else if((T=X(Q))==="*"){j=Q+1,f=B||X(j)==="*";do{if(T===`
`)++Z;if(++Q===D)throw W("comment");R=T,T=X(Q)}while(R!=="*"||T!=="/");if(++Q,f)V(j,Q-2,k),k=!0;O=!0}else return"/"}}while(O);var c=Q;FW0.lastIndex=0;var h=FW0.test(X(c++));if(!h)while(c<D&&!FW0.test(X(c)))++c;var n=A.substring(Q,Q=c);if(n==='"'||n==="'")Y=n;return n}function z(O){I.push(O)}function $(){if(!I.length){var O=H();if(O===null)return null;z(O)}return I[0]}function L(O,R){var T=$(),j=T===O;if(j)return H(),!0;if(!R)throw W("token '"+T+"', '"+O+"' expected");return!1}function N(O){var R=null,T;if(O===void 0){if(T=F[Z-1],delete F[Z-1],T&&(B||T.type==="*"||T.lineEmpty))R=T.leading?T.text:null}else{if(G<O)$();if(T=F[O],delete F[O],T&&!T.lineEmpty&&(B||T.type==="/"))R=T.leading?null:T.text}return R}return Object.defineProperty({next:H,peek:$,push:z,skip:L,cmnt:N},"line",{get:function(){return Z}})}});
var It2=E((Gt2)=>{Object.defineProperty(Gt2,"__esModule",{value:!0});Gt2.getOtlpGrpcDefaultConfiguration=Gt2.mergeOtlpGrpcConfigurationWithDefaults=Gt2.validateAndNormalizeUrl=void 0;var Dt2=Tu(),s31=a31(),Gz6=At2(),Fz6=J1("url"),Bt2=ZQ();function Zt2(A){if(A=A.trim(),!A.match(/^([\w]{1,8}):\/\//))A=`https://${A}`;let Q=new Fz6.URL(A);if(Q.protocol==="unix:")return A;if(Q.pathname&&Q.pathname!=="/")Bt2.diag.warn("URL path should not be set when using grpc, the path part of the URL will be ignored.");if(Q.protocol!==""&&!Q.protocol?.match(/^(http)s?:$/))Bt2.diag.warn("URL protocol should be http(s)://. Using http://.");return Q.host}Gt2.validateAndNormalizeUrl=Zt2;function Qt2(A,B){for(let[Q,D]of Object.entries(B.getMap()))if(A.get(Q).length<1)A.set(Q,D)}function Iz6(A,B,Q){let D=A.url??B.url??Q.url;return{...Dt2.mergeOtlpSharedConfigurationWithDefaults(A,B,Q),metadata:()=>{let Z=Q.metadata();return Qt2(Z,A.metadata?.().clone()??s31.createEmptyMetadata()),Qt2(Z,B.metadata?.()??s31.createEmptyMetadata()),Z},url:Zt2(D),credentials:A.credentials??B.credentials?.(D)??Q.credentials(D)}}Gt2.mergeOtlpGrpcConfigurationWithDefaults=Iz6;function Yz6(){return{...Dt2.getSharedConfigurationDefaults(),metadata:()=>{let A=s31.createEmptyMetadata();return A.set("User-Agent",`OTel-OTLP-Exporter-JavaScript/${Gz6.VERSION}`),A},url:"http://localhost:4317",credentials:(A)=>{if(A.startsWith("http://"))return()=>s31.createInsecureCredentials();else return()=>s31.createSslCredentials()}}}Gt2.getOtlpGrpcDefaultConfiguration=Yz6});
var JP1=E((Fr2)=>{Object.defineProperty(Fr2,"__esModule",{value:!0});Fr2.BaseSubchannelWrapper=void 0;class Gr2{constructor(A){this.child=A,this.healthy=!0,this.healthListeners=new Set,A.addHealthStateWatcher((B)=>{if(this.healthy)this.updateHealthListeners()})}updateHealthListeners(){for(let A of this.healthListeners)A(this.isHealthy())}getConnectivityState(){return this.child.getConnectivityState()}addConnectivityStateListener(A){this.child.addConnectivityStateListener(A)}removeConnectivityStateListener(A){this.child.removeConnectivityStateListener(A)}startConnecting(){this.child.startConnecting()}getAddress(){return this.child.getAddress()}throttleKeepalive(A){this.child.throttleKeepalive(A)}ref(){this.child.ref()}unref(){this.child.unref()}getChannelzRef(){return this.child.getChannelzRef()}isHealthy(){return this.healthy&&this.child.isHealthy()}addHealthStateWatcher(A){this.healthListeners.add(A)}removeHealthStateWatcher(A){this.healthListeners.delete(A)}setHealthy(A){if(A!==this.healthy){if(this.healthy=A,this.child.isHealthy())this.updateHealthListeners()}}getRealSubchannel(){return this.child.getRealSubchannel()}realSubchannelEquals(A){return this.getRealSubchannel()===A.getRealSubchannel()}getCallCredentials(){return this.child.getCallCredentials()}}Fr2.BaseSubchannelWrapper=Gr2});
var JW0=E((Gx5,ZJ6)=>{ZJ6.exports={nested:{google:{nested:{protobuf:{nested:{FileDescriptorSet:{fields:{file:{rule:"repeated",type:"FileDescriptorProto",id:1}}},FileDescriptorProto:{fields:{name:{type:"string",id:1},package:{type:"string",id:2},dependency:{rule:"repeated",type:"string",id:3},publicDependency:{rule:"repeated",type:"int32",id:10,options:{packed:!1}},weakDependency:{rule:"repeated",type:"int32",id:11,options:{packed:!1}},messageType:{rule:"repeated",type:"DescriptorProto",id:4},enumType:{rule:"repeated",type:"EnumDescriptorProto",id:5},service:{rule:"repeated",type:"ServiceDescriptorProto",id:6},extension:{rule:"repeated",type:"FieldDescriptorProto",id:7},options:{type:"FileOptions",id:8},sourceCodeInfo:{type:"SourceCodeInfo",id:9},syntax:{type:"string",id:12}}},DescriptorProto:{fields:{name:{type:"string",id:1},field:{rule:"repeated",type:"FieldDescriptorProto",id:2},extension:{rule:"repeated",type:"FieldDescriptorProto",id:6},nestedType:{rule:"repeated",type:"DescriptorProto",id:3},enumType:{rule:"repeated",type:"EnumDescriptorProto",id:4},extensionRange:{rule:"repeated",type:"ExtensionRange",id:5},oneofDecl:{rule:"repeated",type:"OneofDescriptorProto",id:8},options:{type:"MessageOptions",id:7},reservedRange:{rule:"repeated",type:"ReservedRange",id:9},reservedName:{rule:"repeated",type:"string",id:10}},nested:{ExtensionRange:{fields:{start:{type:"int32",id:1},end:{type:"int32",id:2}}},ReservedRange:{fields:{start:{type:"int32",id:1},end:{type:"int32",id:2}}}}},FieldDescriptorProto:{fields:{name:{type:"string",id:1},number:{type:"int32",id:3},label:{type:"Label",id:4},type:{type:"Type",id:5},typeName:{type:"string",id:6},extendee:{type:"string",id:2},defaultValue:{type:"string",id:7},oneofIndex:{type:"int32",id:9},jsonName:{type:"string",id:10},options:{type:"FieldOptions",id:8}},nested:{Type:{values:{TYPE_DOUBLE:1,TYPE_FLOAT:2,TYPE_INT64:3,TYPE_UINT64:4,TYPE_INT32:5,TYPE_FIXED64:6,TYPE_FIXED32:7,TYPE_BOOL:8,TYPE_STRING:9,TYPE_GROUP:10,TYPE_MESSAGE:11,TYPE_BYTES:12,TYPE_UINT32:13,TYPE_ENUM:14,TYPE_SFIXED32:15,TYPE_SFIXED64:16,TYPE_SINT32:17,TYPE_SINT64:18}},Label:{values:{LABEL_OPTIONAL:1,LABEL_REQUIRED:2,LABEL_REPEATED:3}}}},OneofDescriptorProto:{fields:{name:{type:"string",id:1},options:{type:"OneofOptions",id:2}}},EnumDescriptorProto:{fields:{name:{type:"string",id:1},value:{rule:"repeated",type:"EnumValueDescriptorProto",id:2},options:{type:"EnumOptions",id:3}}},EnumValueDescriptorProto:{fields:{name:{type:"string",id:1},number:{type:"int32",id:2},options:{type:"EnumValueOptions",id:3}}},ServiceDescriptorProto:{fields:{name:{type:"string",id:1},method:{rule:"repeated",type:"MethodDescriptorProto",id:2},options:{type:"ServiceOptions",id:3}}},MethodDescriptorProto:{fields:{name:{type:"string",id:1},inputType:{type:"string",id:2},outputType:{type:"string",id:3},options:{type:"MethodOptions",id:4},clientStreaming:{type:"bool",id:5},serverStreaming:{type:"bool",id:6}}},FileOptions:{fields:{javaPackage:{type:"string",id:1},javaOuterClassname:{type:"string",id:8},javaMultipleFiles:{type:"bool",id:10},javaGenerateEqualsAndHash:{type:"bool",id:20,options:{deprecated:!0}},javaStringCheckUtf8:{type:"bool",id:27},optimizeFor:{type:"OptimizeMode",id:9,options:{default:"SPEED"}},goPackage:{type:"string",id:11},ccGenericServices:{type:"bool",id:16},javaGenericServices:{type:"bool",id:17},pyGenericServices:{type:"bool",id:18},deprecated:{type:"bool",id:23},ccEnableArenas:{type:"bool",id:31},objcClassPrefix:{type:"string",id:36},csharpNamespace:{type:"string",id:37},uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]],reserved:[[38,38]],nested:{OptimizeMode:{values:{SPEED:1,CODE_SIZE:2,LITE_RUNTIME:3}}}},MessageOptions:{fields:{messageSetWireFormat:{type:"bool",id:1},noStandardDescriptorAccessor:{type:"bool",id:2},deprecated:{type:"bool",id:3},mapEntry:{type:"bool",id:7},uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]],reserved:[[8,8]]},FieldOptions:{fields:{ctype:{type:"CType",id:1,options:{default:"STRING"}},packed:{type:"bool",id:2},jstype:{type:"JSType",id:6,options:{default:"JS_NORMAL"}},lazy:{type:"bool",id:5},deprecated:{type:"bool",id:3},weak:{type:"bool",id:10},uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]],reserved:[[4,4]],nested:{CType:{values:{STRING:0,CORD:1,STRING_PIECE:2}},JSType:{values:{JS_NORMAL:0,JS_STRING:1,JS_NUMBER:2}}}},OneofOptions:{fields:{uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]]},EnumOptions:{fields:{allowAlias:{type:"bool",id:2},deprecated:{type:"bool",id:3},uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]]},EnumValueOptions:{fields:{deprecated:{type:"bool",id:1},uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]]},ServiceOptions:{fields:{deprecated:{type:"bool",id:33},uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]]},MethodOptions:{fields:{deprecated:{type:"bool",id:33},uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]]},UninterpretedOption:{fields:{name:{rule:"repeated",type:"NamePart",id:2},identifierValue:{type:"string",id:3},positiveIntValue:{type:"uint64",id:4},negativeIntValue:{type:"int64",id:5},doubleValue:{type:"double",id:6},stringValue:{type:"bytes",id:7},aggregateValue:{type:"string",id:8}},nested:{NamePart:{fields:{namePart:{rule:"required",type:"string",id:1},isExtension:{rule:"required",type:"bool",id:2}}}}},SourceCodeInfo:{fields:{location:{rule:"repeated",type:"Location",id:1}},nested:{Location:{fields:{path:{rule:"repeated",type:"int32",id:1},span:{rule:"repeated",type:"int32",id:2},leadingComments:{type:"string",id:3},trailingComments:{type:"string",id:4},leadingDetachedComments:{rule:"repeated",type:"string",id:6}}}}},GeneratedCodeInfo:{fields:{annotation:{rule:"repeated",type:"Annotation",id:1}},nested:{Annotation:{fields:{path:{rule:"repeated",type:"int32",id:1},sourceFile:{type:"string",id:2},begin:{type:"int32",id:3},end:{type:"int32",id:4}}}}}}}}}}}});
var Ja2=E((Ix5,zJ6)=>{zJ6.exports={nested:{google:{nested:{protobuf:{nested:{SourceContext:{fields:{fileName:{type:"string",id:1}}}}}}}}}});
var KI=E((r_5,bn2)=>{var eZ=bn2.exports=pL(),vn2=_I0(),QW0,DW0;eZ.codegen=An2();eZ.fetch=Qn2();eZ.path=Gn2();eZ.fs=eZ.inquire("fs");eZ.toArray=function A(B){if(B){var Q=Object.keys(B),D=new Array(Q.length),Z=0;while(Z<Q.length)D[Z]=B[Q[Z++]];return D}return[]};eZ.toObject=function A(B){var Q={},D=0;while(D<B.length){var Z=B[D++],G=B[D++];if(G!==void 0)Q[Z]=G}return Q};var PW6=/\\/g,SW6=/"/g;eZ.isReserved=function A(B){return/^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(B)};eZ.safeProp=function A(B){if(!/^[$\w_]+$/.test(B)||eZ.isReserved(B))return'["'+B.replace(PW6,"\\\\").replace(SW6,"\\\"")+'"]';return"."+B};eZ.ucFirst=function A(B){return B.charAt(0).toUpperCase()+B.substring(1)};var jW6=/_([a-z])/g;eZ.camelCase=function A(B){return B.substring(0,1)+B.substring(1).replace(jW6,function(Q,D){return D.toUpperCase()})};eZ.compareFieldsById=function A(B,Q){return B.id-Q.id};eZ.decorateType=function A(B,Q){if(B.$type){if(Q&&B.$type.name!==Q)eZ.decorateRoot.remove(B.$type),B.$type.name=Q,eZ.decorateRoot.add(B.$type);return B.$type}if(!QW0)QW0=hT1();var D=new QW0(Q||B.name);return eZ.decorateRoot.add(D),D.ctor=B,Object.defineProperty(B,"$type",{value:D,enumerable:!1}),Object.defineProperty(B.prototype,"$type",{value:D,enumerable:!1}),D};var kW6=0;eZ.decorateEnum=function A(B){if(B.$type)return B.$type;if(!DW0)DW0=sw();var Q=new DW0("Enum"+kW6++,B);return eZ.decorateRoot.add(Q),Object.defineProperty(B,"$type",{value:Q,enumerable:!1}),Q};eZ.setProperty=function A(B,Q,D){function Z(G,F,I){var Y=F.shift();if(Y==="__proto__"||Y==="prototype")return G;if(F.length>0)G[Y]=Z(G[Y]||{},F,I);else{var W=G[Y];if(W)I=[].concat(W).concat(I);G[Y]=I}return G}if(typeof B!=="object")throw TypeError("dst must be an object");if(!Q)throw TypeError("path must be specified");return Q=Q.split("."),Z(B,Q,D)};Object.defineProperty(eZ,"decorateRoot",{get:function(){return vn2.decorated||(vn2.decorated=new(mT1()))}})});
var KP1=E((Tr2)=>{Object.defineProperty(Tr2,"__esModule",{value:!0});Tr2.ServerCredentials=void 0;Tr2.createCertificateProviderServerCredentials=kC6;Tr2.createServerCredentialsWithInterceptors=yC6;var aW0=XY0();class Jt{constructor(A,B){this.serverConstructorOptions=A,this.watchers=new Set,this.latestContextOptions=null,this.latestContextOptions=B!==null&&B!==void 0?B:null}_addWatcher(A){this.watchers.add(A)}_removeWatcher(A){this.watchers.delete(A)}getWatcherCount(){return this.watchers.size}updateSecureContextOptions(A){this.latestContextOptions=A;for(let B of this.watchers)B(this.latestContextOptions)}_isSecure(){return this.serverConstructorOptions!==null}_getSecureContextOptions(){return this.latestContextOptions}_getConstructorOptions(){return this.serverConstructorOptions}_getInterceptors(){return[]}static createInsecure(){return new sW0}static createSsl(A,B,Q=!1){var D;if(A!==null&&!Buffer.isBuffer(A))throw new TypeError("rootCerts must be null or a Buffer");if(!Array.isArray(B))throw new TypeError("keyCertPairs must be an array");if(typeof Q!=="boolean")throw new TypeError("checkClientCertificate must be a boolean");let Z=[],G=[];for(let F=0;F<B.length;F++){let I=B[F];if(I===null||typeof I!=="object")throw new TypeError(`keyCertPair[${F}] must be an object`);if(!Buffer.isBuffer(I.private_key))throw new TypeError(`keyCertPair[${F}].private_key must be a Buffer`);if(!Buffer.isBuffer(I.cert_chain))throw new TypeError(`keyCertPair[${F}].cert_chain must be a Buffer`);Z.push(I.cert_chain),G.push(I.private_key)}return new rW0({requestCert:Q,ciphers:aW0.CIPHER_SUITES},{ca:(D=A!==null&&A!==void 0?A:aW0.getDefaultRootsData())!==null&&D!==void 0?D:void 0,cert:Z,key:G})}}Tr2.ServerCredentials=Jt;class sW0 extends Jt{constructor(){super(null)}_getSettings(){return null}_equals(A){return A instanceof sW0}}class rW0 extends Jt{constructor(A,B){super(A,B);this.options=Object.assign(Object.assign({},A),B)}_equals(A){if(this===A)return!0;if(!(A instanceof rW0))return!1;if(Buffer.isBuffer(this.options.ca)&&Buffer.isBuffer(A.options.ca)){if(!this.options.ca.equals(A.options.ca))return!1}else if(this.options.ca!==A.options.ca)return!1;if(Array.isArray(this.options.cert)&&Array.isArray(A.options.cert)){if(this.options.cert.length!==A.options.cert.length)return!1;for(let B=0;B<this.options.cert.length;B++){let Q=this.options.cert[B],D=A.options.cert[B];if(Buffer.isBuffer(Q)&&Buffer.isBuffer(D)){if(!Q.equals(D))return!1}else if(Q!==D)return!1}}else if(this.options.cert!==A.options.cert)return!1;if(Array.isArray(this.options.key)&&Array.isArray(A.options.key)){if(this.options.key.length!==A.options.key.length)return!1;for(let B=0;B<this.options.key.length;B++){let Q=this.options.key[B],D=A.options.key[B];if(Buffer.isBuffer(Q)&&Buffer.isBuffer(D)){if(!Q.equals(D))return!1}else if(Q!==D)return!1}}else if(this.options.key!==A.options.key)return!1;if(this.options.requestCert!==A.options.requestCert)return!1;return!0}}class oW0 extends Jt{constructor(A,B,Q){super({requestCert:B!==null,rejectUnauthorized:Q,ciphers:aW0.CIPHER_SUITES});this.identityCertificateProvider=A,this.caCertificateProvider=B,this.requireClientCertificate=Q,this.latestCaUpdate=null,this.latestIdentityUpdate=null,this.caCertificateUpdateListener=this.handleCaCertificateUpdate.bind(this),this.identityCertificateUpdateListener=this.handleIdentityCertitificateUpdate.bind(this)}_addWatcher(A){var B;if(this.getWatcherCount()===0)(B=this.caCertificateProvider)===null||B===void 0||B.addCaCertificateListener(this.caCertificateUpdateListener),this.identityCertificateProvider.addIdentityCertificateListener(this.identityCertificateUpdateListener);super._addWatcher(A)}_removeWatcher(A){var B;if(super._removeWatcher(A),this.getWatcherCount()===0)(B=this.caCertificateProvider)===null||B===void 0||B.removeCaCertificateListener(this.caCertificateUpdateListener),this.identityCertificateProvider.removeIdentityCertificateListener(this.identityCertificateUpdateListener)}_equals(A){if(this===A)return!0;if(!(A instanceof oW0))return!1;return this.caCertificateProvider===A.caCertificateProvider&&this.identityCertificateProvider===A.identityCertificateProvider&&this.requireClientCertificate===A.requireClientCertificate}calculateSecureContextOptions(){var A;if(this.latestIdentityUpdate===null)return null;if(this.caCertificateProvider!==null&&this.latestCaUpdate===null)return null;return{ca:(A=this.latestCaUpdate)===null||A===void 0?void 0:A.caCertificate,cert:[this.latestIdentityUpdate.certificate],key:[this.latestIdentityUpdate.privateKey]}}finalizeUpdate(){let A=this.calculateSecureContextOptions();this.updateSecureContextOptions(A)}handleCaCertificateUpdate(A){this.latestCaUpdate=A,this.finalizeUpdate()}handleIdentityCertitificateUpdate(A){this.latestIdentityUpdate=A,this.finalizeUpdate()}}function kC6(A,B,Q){return new oW0(A,B,Q)}class tW0 extends Jt{constructor(A,B){super({});this.childCredentials=A,this.interceptors=B}_isSecure(){return this.childCredentials._isSecure()}_equals(A){if(!(A instanceof tW0))return!1;if(!this.childCredentials._equals(A.childCredentials))return!1;if(this.interceptors.length!==A.interceptors.length)return!1;for(let B=0;B<this.interceptors.length;B++)if(this.interceptors[B]!==A.interceptors[B])return!1;return!0}_getInterceptors(){return this.interceptors}_addWatcher(A){this.childCredentials._addWatcher(A)}_removeWatcher(A){this.childCredentials._removeWatcher(A)}_getConstructorOptions(){return this.childCredentials._getConstructorOptions()}_getSecureContextOptions(){return this.childCredentials._getSecureContextOptions()}}function yC6(A,B){return new tW0(A,B)}});
var Kt2=E((Vt2)=>{Object.defineProperty(Vt2,"__esModule",{value:!0});Vt2.getOtlpGrpcConfigurationFromEnv=void 0;var Yt2=y3(),r31=a31(),Xz6=uo(),Vz6=J1("fs"),Cz6=J1("path"),Jt2=ZQ();function SJ0(A,B){if(A!=null&&A!=="")return A;if(B!=null&&B!=="")return B;return}function Kz6(A){let B=process.env[`OTEL_EXPORTER_OTLP_${A}_HEADERS`]?.trim(),Q=process.env.OTEL_EXPORTER_OTLP_HEADERS?.trim(),D=Yt2.parseKeyPairsIntoRecord(B),Z=Yt2.parseKeyPairsIntoRecord(Q);if(Object.keys(D).length===0&&Object.keys(Z).length===0)return;let G=Object.assign({},Z,D),F=r31.createEmptyMetadata();for(let[I,Y]of Object.entries(G))F.set(I,Y);return F}function Hz6(A){let B=Kz6(A);if(B==null)return;return()=>B}function zz6(A){let B=process.env[`OTEL_EXPORTER_OTLP_${A}_ENDPOINT`]?.trim(),Q=process.env.OTEL_EXPORTER_OTLP_ENDPOINT?.trim();return SJ0(B,Q)}function Ez6(A){let B=process.env[`OTEL_EXPORTER_OTLP_${A}_INSECURE`]?.toLowerCase().trim(),Q=process.env.OTEL_EXPORTER_OTLP_INSECURE?.toLowerCase().trim();return SJ0(B,Q)==="true"}function jJ0(A,B,Q){let D=process.env[A]?.trim(),Z=process.env[B]?.trim(),G=SJ0(D,Z);if(G!=null)try{return Vz6.readFileSync(Cz6.resolve(process.cwd(),G))}catch{Jt2.diag.warn(Q);return}else return}function Uz6(A){return jJ0(`OTEL_EXPORTER_OTLP_${A}_CLIENT_CERTIFICATE`,"OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE","Failed to read client certificate chain file")}function wz6(A){return jJ0(`OTEL_EXPORTER_OTLP_${A}_CLIENT_KEY`,"OTEL_EXPORTER_OTLP_CLIENT_KEY","Failed to read client certificate private key file")}function Wt2(A){return jJ0(`OTEL_EXPORTER_OTLP_${A}_CERTIFICATE`,"OTEL_EXPORTER_OTLP_CERTIFICATE","Failed to read root certificate file")}function Xt2(A){let B=wz6(A),Q=Uz6(A),D=Wt2(A),Z=B!=null&&Q!=null;if(D!=null&&!Z)return Jt2.diag.warn("Client key and certificate must both be provided, but one was missing - attempting to create credentials from just the root certificate"),r31.createSslCredentials(Wt2(A));return r31.createSslCredentials(D,B,Q)}function $z6(A){if(Ez6(A))return r31.createInsecureCredentials();return Xt2(A)}function qz6(A){return{...Xz6.getSharedConfigurationFromEnvironment(A),metadata:Hz6(A),url:zz6(A),credentials:(B)=>{if(B.startsWith("http://"))return()=>{return r31.createInsecureCredentials()};else if(B.startsWith("https://"))return()=>{return Xt2(A)};return()=>{return $z6(A)}}}}Vt2.getOtlpGrpcConfigurationFromEnv=qz6});
var LT1=E((Bi2)=>{Object.defineProperty(Bi2,"__esModule",{value:!0});Bi2.registerAdminService=xI6;Bi2.addAdminServicesToServer=vI6;var Ai2=[];function xI6(A,B){Ai2.push({getServiceDefinition:A,getHandlers:B})}function vI6(A){for(let{getServiceDefinition:B,getHandlers:Q}of Ai2)A.addService(B(),Q())}});
var LY0=E((qi2)=>{Object.defineProperty(qi2,"__esModule",{value:!0});qi2.InterceptingCall=qi2.RequesterBuilder=qi2.ListenerBuilder=qi2.InterceptorConfigurationError=void 0;qi2.getInterceptingCall=sI6;var iI6=tW(),Ci2=Vi2(),Ki2=S6(),Hi2=YT1();class q31 extends Error{constructor(A){super(A);this.name="InterceptorConfigurationError",Error.captureStackTrace(this,q31)}}qi2.InterceptorConfigurationError=q31;class zi2{constructor(){this.metadata=void 0,this.message=void 0,this.status=void 0}withOnReceiveMetadata(A){return this.metadata=A,this}withOnReceiveMessage(A){return this.message=A,this}withOnReceiveStatus(A){return this.status=A,this}build(){return{onReceiveMetadata:this.metadata,onReceiveMessage:this.message,onReceiveStatus:this.status}}}qi2.ListenerBuilder=zi2;class Ei2{constructor(){this.start=void 0,this.message=void 0,this.halfClose=void 0,this.cancel=void 0}withStart(A){return this.start=A,this}withSendMessage(A){return this.message=A,this}withHalfClose(A){return this.halfClose=A,this}withCancel(A){return this.cancel=A,this}build(){return{start:this.start,sendMessage:this.message,halfClose:this.halfClose,cancel:this.cancel}}}qi2.RequesterBuilder=Ei2;var qY0={onReceiveMetadata:(A,B)=>{B(A)},onReceiveMessage:(A,B)=>{B(A)},onReceiveStatus:(A,B)=>{B(A)}},$31={start:(A,B,Q)=>{Q(A,B)},sendMessage:(A,B)=>{B(A)},halfClose:(A)=>{A()},cancel:(A)=>{A()}};class Ui2{constructor(A,B){var Q,D,Z,G;if(this.nextCall=A,this.processingMetadata=!1,this.pendingMessageContext=null,this.processingMessage=!1,this.pendingHalfClose=!1,B)this.requester={start:(Q=B.start)!==null&&Q!==void 0?Q:$31.start,sendMessage:(D=B.sendMessage)!==null&&D!==void 0?D:$31.sendMessage,halfClose:(Z=B.halfClose)!==null&&Z!==void 0?Z:$31.halfClose,cancel:(G=B.cancel)!==null&&G!==void 0?G:$31.cancel};else this.requester=$31}cancelWithStatus(A,B){this.requester.cancel(()=>{this.nextCall.cancelWithStatus(A,B)})}getPeer(){return this.nextCall.getPeer()}processPendingMessage(){if(this.pendingMessageContext)this.nextCall.sendMessageWithContext(this.pendingMessageContext,this.pendingMessage),this.pendingMessageContext=null,this.pendingMessage=null}processPendingHalfClose(){if(this.pendingHalfClose)this.nextCall.halfClose()}start(A,B){var Q,D,Z,G,F,I;let Y={onReceiveMetadata:(D=(Q=B===null||B===void 0?void 0:B.onReceiveMetadata)===null||Q===void 0?void 0:Q.bind(B))!==null&&D!==void 0?D:(W)=>{},onReceiveMessage:(G=(Z=B===null||B===void 0?void 0:B.onReceiveMessage)===null||Z===void 0?void 0:Z.bind(B))!==null&&G!==void 0?G:(W)=>{},onReceiveStatus:(I=(F=B===null||B===void 0?void 0:B.onReceiveStatus)===null||F===void 0?void 0:F.bind(B))!==null&&I!==void 0?I:(W)=>{}};this.processingMetadata=!0,this.requester.start(A,Y,(W,J)=>{var X,V,C;this.processingMetadata=!1;let K;if(Ci2.isInterceptingListener(J))K=J;else{let H={onReceiveMetadata:(X=J.onReceiveMetadata)!==null&&X!==void 0?X:qY0.onReceiveMetadata,onReceiveMessage:(V=J.onReceiveMessage)!==null&&V!==void 0?V:qY0.onReceiveMessage,onReceiveStatus:(C=J.onReceiveStatus)!==null&&C!==void 0?C:qY0.onReceiveStatus};K=new Ci2.InterceptingListenerImpl(H,Y)}this.nextCall.start(W,K),this.processPendingMessage(),this.processPendingHalfClose()})}sendMessageWithContext(A,B){this.processingMessage=!0,this.requester.sendMessage(B,(Q)=>{if(this.processingMessage=!1,this.processingMetadata)this.pendingMessageContext=A,this.pendingMessage=B;else this.nextCall.sendMessageWithContext(A,Q),this.processPendingHalfClose()})}sendMessage(A){this.sendMessageWithContext({},A)}startRead(){this.nextCall.startRead()}halfClose(){this.requester.halfClose(()=>{if(this.processingMetadata||this.processingMessage)this.pendingHalfClose=!0;else this.nextCall.halfClose()})}}qi2.InterceptingCall=Ui2;function nI6(A,B,Q){var D,Z;let G=(D=Q.deadline)!==null&&D!==void 0?D:1/0,F=Q.host,I=(Z=Q.parent)!==null&&Z!==void 0?Z:null,Y=Q.propagate_flags,W=Q.credentials,J=A.createCall(B,G,F,I,Y);if(W)J.setCredentials(W);return J}class NY0{constructor(A,B){this.call=A,this.methodDefinition=B}cancelWithStatus(A,B){this.call.cancelWithStatus(A,B)}getPeer(){return this.call.getPeer()}sendMessageWithContext(A,B){let Q;try{Q=this.methodDefinition.requestSerialize(B)}catch(D){this.call.cancelWithStatus(Ki2.Status.INTERNAL,`Request message serialization failure: ${Hi2.getErrorMessage(D)}`);return}this.call.sendMessageWithContext(A,Q)}sendMessage(A){this.sendMessageWithContext({},A)}start(A,B){let Q=null;this.call.start(A,{onReceiveMetadata:(D)=>{var Z;(Z=B===null||B===void 0?void 0:B.onReceiveMetadata)===null||Z===void 0||Z.call(B,D)},onReceiveMessage:(D)=>{var Z;let G;try{G=this.methodDefinition.responseDeserialize(D)}catch(F){Q={code:Ki2.Status.INTERNAL,details:`Response message parsing error: ${Hi2.getErrorMessage(F)}`,metadata:new iI6.Metadata},this.call.cancelWithStatus(Q.code,Q.details);return}(Z=B===null||B===void 0?void 0:B.onReceiveMessage)===null||Z===void 0||Z.call(B,G)},onReceiveStatus:(D)=>{var Z,G;if(Q)(Z=B===null||B===void 0?void 0:B.onReceiveStatus)===null||Z===void 0||Z.call(B,Q);else(G=B===null||B===void 0?void 0:B.onReceiveStatus)===null||G===void 0||G.call(B,D)}})}startRead(){this.call.startRead()}halfClose(){this.call.halfClose()}}class wi2 extends NY0{constructor(A,B){super(A,B)}start(A,B){var Q,D;let Z=!1,G={onReceiveMetadata:(D=(Q=B===null||B===void 0?void 0:B.onReceiveMetadata)===null||Q===void 0?void 0:Q.bind(B))!==null&&D!==void 0?D:(F)=>{},onReceiveMessage:(F)=>{var I;Z=!0,(I=B===null||B===void 0?void 0:B.onReceiveMessage)===null||I===void 0||I.call(B,F)},onReceiveStatus:(F)=>{var I,Y;if(!Z)(I=B===null||B===void 0?void 0:B.onReceiveMessage)===null||I===void 0||I.call(B,null);(Y=B===null||B===void 0?void 0:B.onReceiveStatus)===null||Y===void 0||Y.call(B,F)}};super.start(A,G),this.call.startRead()}}class $i2 extends NY0{}function aI6(A,B,Q){let D=nI6(A,Q.path,B);if(Q.responseStream)return new $i2(D,Q);else return new wi2(D,Q)}function sI6(A,B,Q,D){if(A.clientInterceptors.length>0&&A.clientInterceptorProviders.length>0)throw new q31("Both interceptors and interceptor_providers were passed as options to the client constructor. Only one of these is allowed.");if(A.callInterceptors.length>0&&A.callInterceptorProviders.length>0)throw new q31("Both interceptors and interceptor_providers were passed as call options. Only one of these is allowed.");let Z=[];if(A.callInterceptors.length>0||A.callInterceptorProviders.length>0)Z=[].concat(A.callInterceptors,A.callInterceptorProviders.map((I)=>I(B))).filter((I)=>I);else Z=[].concat(A.clientInterceptors,A.clientInterceptorProviders.map((I)=>I(B))).filter((I)=>I);let G=Object.assign({},Q,{method_definition:B});return Z.reduceRight((I,Y)=>{return(W)=>Y(W,I)},(I)=>aI6(D,I,B))(G)}});
var Ns2=E(($s2)=>{Object.defineProperty($s2,"__esModule",{value:!0});$s2.SubchannelPool=void 0;$s2.getSubchannelPool=MV6;var EV6=hp2(),UV6=na2(),wV6=CE(),$V6=SV(),qV6=ws2(),NV6=1e4;class ZP1{constructor(){this.pool=Object.create(null),this.cleanupTimer=null}unrefUnusedSubchannels(){let A=!0;for(let B in this.pool){let D=this.pool[B].filter((Z)=>!Z.subchannel.unrefIfOneRef());if(D.length>0)A=!1;this.pool[B]=D}if(A&&this.cleanupTimer!==null)clearInterval(this.cleanupTimer),this.cleanupTimer=null}ensureCleanupTask(){var A,B;if(this.cleanupTimer===null)this.cleanupTimer=setInterval(()=>{this.unrefUnusedSubchannels()},NV6),(B=(A=this.cleanupTimer).unref)===null||B===void 0||B.call(A)}getOrCreateSubchannel(A,B,Q,D){this.ensureCleanupTask();let Z=$V6.uriToString(A);if(Z in this.pool){let F=this.pool[Z];for(let I of F)if(wV6.subchannelAddressEqual(B,I.subchannelAddress)&&EV6.channelOptionsEqual(Q,I.channelArguments)&&D._equals(I.channelCredentials))return I.subchannel}let G=new UV6.Subchannel(A,B,Q,D,new qV6.Http2SubchannelConnector(A));if(!(Z in this.pool))this.pool[Z]=[];return this.pool[Z].push({subchannelAddress:B,channelArguments:Q,channelCredentials:D,subchannel:G}),G.ref(),G}}$s2.SubchannelPool=ZP1;var LV6=new ZP1;function MV6(A){if(A)return LV6;else return new ZP1}});
var OW0=E((Bs2)=>{Object.defineProperty(Bs2,"__esModule",{value:!0});Bs2.DEFAULT_PORT=void 0;Bs2.setup=kX6;var oa2=nL(),LW0=J1("dns"),RX6=EY0(),MW0=S6(),RW0=tW(),OX6=D7(),TX6=S6(),e_=SV(),ta2=J1("net"),PX6=H31(),ea2=ra2(),SX6="dns_resolver";function DM(A){OX6.trace(TX6.LogVerbosity.DEBUG,SX6,A)}Bs2.DEFAULT_PORT=443;var jX6=30000;class As2{constructor(A,B,Q){var D,Z,G;if(this.target=A,this.listener=B,this.pendingLookupPromise=null,this.pendingTxtPromise=null,this.latestLookupResult=null,this.latestServiceConfig=null,this.latestServiceConfigError=null,this.continueResolving=!1,this.isNextResolutionTimerRunning=!1,this.isServiceConfigEnabled=!0,this.returnedIpResult=!1,this.alternativeResolver=new LW0.promises.Resolver,DM("Resolver constructed for target "+e_.uriToString(A)),A.authority)this.alternativeResolver.setServers([A.authority]);let F=e_.splitHostPort(A.path);if(F===null)this.ipResult=null,this.dnsHostname=null,this.port=null;else if(ta2.isIPv4(F.host)||ta2.isIPv6(F.host))this.ipResult=[{addresses:[{host:F.host,port:(D=F.port)!==null&&D!==void 0?D:Bs2.DEFAULT_PORT}]}],this.dnsHostname=null,this.port=null;else this.ipResult=null,this.dnsHostname=F.host,this.port=(Z=F.port)!==null&&Z!==void 0?Z:Bs2.DEFAULT_PORT;if(this.percentage=Math.random()*100,Q["grpc.service_config_disable_resolution"]===1)this.isServiceConfigEnabled=!1;this.defaultResolutionError={code:MW0.Status.UNAVAILABLE,details:`Name resolution failed for target ${e_.uriToString(this.target)}`,metadata:new RW0.Metadata};let I={initialDelay:Q["grpc.initial_reconnect_backoff_ms"],maxDelay:Q["grpc.max_reconnect_backoff_ms"]};this.backoff=new PX6.BackoffTimeout(()=>{if(this.continueResolving)this.startResolutionWithBackoff()},I),this.backoff.unref(),this.minTimeBetweenResolutionsMs=(G=Q["grpc.dns_min_time_between_resolutions_ms"])!==null&&G!==void 0?G:jX6,this.nextResolutionTimer=setTimeout(()=>{},0),clearTimeout(this.nextResolutionTimer)}startResolution(){if(this.ipResult!==null){if(!this.returnedIpResult)DM("Returning IP address for target "+e_.uriToString(this.target)),setImmediate(()=>{this.listener.onSuccessfulResolution(this.ipResult,null,null,null,{})}),this.returnedIpResult=!0;this.backoff.stop(),this.backoff.reset(),this.stopNextResolutionTimer();return}if(this.dnsHostname===null)DM("Failed to parse DNS address "+e_.uriToString(this.target)),setImmediate(()=>{this.listener.onError({code:MW0.Status.UNAVAILABLE,details:`Failed to parse DNS address ${e_.uriToString(this.target)}`,metadata:new RW0.Metadata})}),this.stopNextResolutionTimer();else{if(this.pendingLookupPromise!==null)return;DM("Looking up DNS hostname "+this.dnsHostname),this.latestLookupResult=null;let A=this.dnsHostname;if(this.pendingLookupPromise=this.lookup(A),this.pendingLookupPromise.then((B)=>{if(this.pendingLookupPromise===null)return;this.pendingLookupPromise=null,this.backoff.reset(),this.backoff.stop(),this.latestLookupResult=B.map((D)=>({addresses:[D]}));let Q="["+B.map((D)=>D.host+":"+D.port).join(",")+"]";if(DM("Resolved addresses for target "+e_.uriToString(this.target)+": "+Q),this.latestLookupResult.length===0){this.listener.onError(this.defaultResolutionError);return}this.listener.onSuccessfulResolution(this.latestLookupResult,this.latestServiceConfig,this.latestServiceConfigError,null,{})},(B)=>{if(this.pendingLookupPromise===null)return;DM("Resolution error for target "+e_.uriToString(this.target)+": "+B.message),this.pendingLookupPromise=null,this.stopNextResolutionTimer(),this.listener.onError(this.defaultResolutionError)}),this.isServiceConfigEnabled&&this.pendingTxtPromise===null)this.pendingTxtPromise=this.resolveTxt(A),this.pendingTxtPromise.then((B)=>{if(this.pendingTxtPromise===null)return;this.pendingTxtPromise=null;try{this.latestServiceConfig=RX6.extractAndSelectServiceConfig(B,this.percentage)}catch(Q){this.latestServiceConfigError={code:MW0.Status.UNAVAILABLE,details:`Parsing service config failed with error ${Q.message}`,metadata:new RW0.Metadata}}if(this.latestLookupResult!==null)this.listener.onSuccessfulResolution(this.latestLookupResult,this.latestServiceConfig,this.latestServiceConfigError,null,{})},(B)=>{})}}async lookup(A){if(ea2.GRPC_NODE_USE_ALTERNATIVE_RESOLVER){DM("Using alternative DNS resolver.");let Q=await Promise.allSettled([this.alternativeResolver.resolve4(A),this.alternativeResolver.resolve6(A)]);if(Q.every((D)=>D.status==="rejected"))throw new Error(Q[0].reason);return Q.reduce((D,Z)=>{return Z.status==="fulfilled"?[...D,...Z.value]:D},[]).map((D)=>({host:D,port:+this.port}))}return(await LW0.promises.lookup(A,{all:!0})).map((Q)=>({host:Q.address,port:+this.port}))}async resolveTxt(A){if(ea2.GRPC_NODE_USE_ALTERNATIVE_RESOLVER)return DM("Using alternative DNS resolver."),this.alternativeResolver.resolveTxt(A);return LW0.promises.resolveTxt(A)}startNextResolutionTimer(){var A,B;clearTimeout(this.nextResolutionTimer),this.nextResolutionTimer=setTimeout(()=>{if(this.stopNextResolutionTimer(),this.continueResolving)this.startResolutionWithBackoff()},this.minTimeBetweenResolutionsMs),(B=(A=this.nextResolutionTimer).unref)===null||B===void 0||B.call(A),this.isNextResolutionTimerRunning=!0}stopNextResolutionTimer(){clearTimeout(this.nextResolutionTimer),this.isNextResolutionTimerRunning=!1}startResolutionWithBackoff(){if(this.pendingLookupPromise===null)this.continueResolving=!1,this.backoff.runOnce(),this.startNextResolutionTimer(),this.startResolution()}updateResolution(){if(this.pendingLookupPromise===null)if(this.isNextResolutionTimerRunning||this.backoff.isRunning()){if(this.isNextResolutionTimerRunning)DM('resolution update delayed by "min time between resolutions" rate limit');else DM("resolution update delayed by backoff timer until "+this.backoff.getEndTime().toISOString());this.continueResolving=!0}else this.startResolutionWithBackoff()}destroy(){this.continueResolving=!1,this.backoff.reset(),this.backoff.stop(),this.stopNextResolutionTimer(),this.pendingLookupPromise=null,this.pendingTxtPromise=null,this.latestLookupResult=null,this.latestServiceConfig=null,this.latestServiceConfigError=null,this.returnedIpResult=!1}static getDefaultAuthority(A){return A.path}}function kX6(){oa2.registerResolver("dns",As2),oa2.registerDefaultScheme("dns")}});
var OY0=E((Er2)=>{Object.defineProperty(Er2,"__esModule",{value:!0});Er2.ChannelImplementation=void 0;var MC6=C31(),RC6=lW0();class zr2{constructor(A,B,Q){if(typeof A!=="string")throw new TypeError("Channel target must be a string");if(!(B instanceof MC6.ChannelCredentials))throw new TypeError("Channel credentials must be a ChannelCredentials object");if(Q){if(typeof Q!=="object")throw new TypeError("Channel options must be an object")}this.internalChannel=new RC6.InternalChannel(A,B,Q)}close(){this.internalChannel.close()}getTarget(){return this.internalChannel.getTarget()}getConnectivityState(A){return this.internalChannel.getConnectivityState(A)}watchConnectivityState(A,B,Q){this.internalChannel.watchConnectivityState(A,B,Q)}getChannelzRef(){return this.internalChannel.getChannelzRef()}createCall(A,B,Q,D,Z){if(typeof A!=="string")throw new TypeError("Channel#createCall: method must be a string");if(!(typeof B==="number"||B instanceof Date))throw new TypeError("Channel#createCall: deadline must be a number or Date");return this.internalChannel.createCall(A,B,Q,D,Z)}}Er2.ChannelImplementation=zr2});
var Or2=E((Mr2)=>{Object.defineProperty(Mr2,"__esModule",{value:!0});Mr2.ServerDuplexStreamImpl=Mr2.ServerWritableStreamImpl=Mr2.ServerReadableStreamImpl=Mr2.ServerUnaryCallImpl=void 0;Mr2.serverErrorToStatus=nW0;var OC6=J1("events"),pW0=J1("stream"),iW0=S6(),wr2=tW();function nW0(A,B){var Q;let D={code:iW0.Status.UNKNOWN,details:"message"in A?A.message:"Unknown Error",metadata:(Q=B!==null&&B!==void 0?B:A.metadata)!==null&&Q!==void 0?Q:null};if("code"in A&&typeof A.code==="number"&&Number.isInteger(A.code)){if(D.code=A.code,"details"in A&&typeof A.details==="string")D.details=A.details}return D}class $r2 extends OC6.EventEmitter{constructor(A,B,Q,D){super();this.path=A,this.call=B,this.metadata=Q,this.request=D,this.cancelled=!1}getPeer(){return this.call.getPeer()}sendMetadata(A){this.call.sendMetadata(A)}getDeadline(){return this.call.getDeadline()}getPath(){return this.path}getHost(){return this.call.getHost()}}Mr2.ServerUnaryCallImpl=$r2;class qr2 extends pW0.Readable{constructor(A,B,Q){super({objectMode:!0});this.path=A,this.call=B,this.metadata=Q,this.cancelled=!1}_read(A){this.call.startRead()}getPeer(){return this.call.getPeer()}sendMetadata(A){this.call.sendMetadata(A)}getDeadline(){return this.call.getDeadline()}getPath(){return this.path}getHost(){return this.call.getHost()}}Mr2.ServerReadableStreamImpl=qr2;class Nr2 extends pW0.Writable{constructor(A,B,Q,D){super({objectMode:!0});this.path=A,this.call=B,this.metadata=Q,this.request=D,this.pendingStatus={code:iW0.Status.OK,details:"OK"},this.cancelled=!1,this.trailingMetadata=new wr2.Metadata,this.on("error",(Z)=>{this.pendingStatus=nW0(Z),this.end()})}getPeer(){return this.call.getPeer()}sendMetadata(A){this.call.sendMetadata(A)}getDeadline(){return this.call.getDeadline()}getPath(){return this.path}getHost(){return this.call.getHost()}_write(A,B,Q){this.call.sendMessage(A,Q)}_final(A){var B;A(null),this.call.sendStatus(Object.assign(Object.assign({},this.pendingStatus),{metadata:(B=this.pendingStatus.metadata)!==null&&B!==void 0?B:this.trailingMetadata}))}end(A){if(A)this.trailingMetadata=A;return super.end()}}Mr2.ServerWritableStreamImpl=Nr2;class Lr2 extends pW0.Duplex{constructor(A,B,Q){super({objectMode:!0});this.path=A,this.call=B,this.metadata=Q,this.pendingStatus={code:iW0.Status.OK,details:"OK"},this.cancelled=!1,this.trailingMetadata=new wr2.Metadata,this.on("error",(D)=>{this.pendingStatus=nW0(D),this.end()})}getPeer(){return this.call.getPeer()}sendMetadata(A){this.call.sendMetadata(A)}getDeadline(){return this.call.getDeadline()}getPath(){return this.path}getHost(){return this.call.getHost()}_read(A){this.call.startRead()}_write(A,B,Q){this.call.sendMessage(A,Q)}_final(A){var B;A(null),this.call.sendStatus(Object.assign(Object.assign({},this.pendingStatus),{metadata:(B=this.pendingStatus.metadata)!==null&&B!==void 0?B:this.trailingMetadata}))}end(A){if(A)this.trailingMetadata=A;return super.end()}}Mr2.ServerDuplexStreamImpl=Lr2});
var Ot2=E((Mt2)=>{Object.defineProperty(Mt2,"__esModule",{value:!0});Mt2.OTLPMetricExporter=void 0;var yz6=IT1(),Nt2=kJ0(),_z6=ju();class Lt2 extends yz6.OTLPMetricExporterBase{constructor(A){super(Nt2.createOtlpGrpcExportDelegate(Nt2.convertLegacyOtlpGrpcOptions(A??{},"METRICS"),_z6.ProtobufMetricsSerializer,"MetricsExportService","/opentelemetry.proto.collector.metrics.v1.MetricsService/Export"),A)}}Mt2.OTLPMetricExporter=Lt2});
var PW0=E((Ys2)=>{Object.defineProperty(Ys2,"__esModule",{value:!0});Ys2.StreamDecoder=void 0;var ZM;(function(A){A[A.NO_DATA=0]="NO_DATA",A[A.READING_SIZE=1]="READING_SIZE",A[A.READING_MESSAGE=2]="READING_MESSAGE"})(ZM||(ZM={}));class Is2{constructor(A){this.maxReadMessageLength=A,this.readState=ZM.NO_DATA,this.readCompressFlag=Buffer.alloc(1),this.readPartialSize=Buffer.alloc(4),this.readSizeRemaining=4,this.readMessageSize=0,this.readPartialMessage=[],this.readMessageRemaining=0}write(A){let B=0,Q,D=[];while(B<A.length)switch(this.readState){case ZM.NO_DATA:this.readCompressFlag=A.slice(B,B+1),B+=1,this.readState=ZM.READING_SIZE,this.readPartialSize.fill(0),this.readSizeRemaining=4,this.readMessageSize=0,this.readMessageRemaining=0,this.readPartialMessage=[];break;case ZM.READING_SIZE:if(Q=Math.min(A.length-B,this.readSizeRemaining),A.copy(this.readPartialSize,4-this.readSizeRemaining,B,B+Q),this.readSizeRemaining-=Q,B+=Q,this.readSizeRemaining===0){if(this.readMessageSize=this.readPartialSize.readUInt32BE(0),this.maxReadMessageLength!==-1&&this.readMessageSize>this.maxReadMessageLength)throw new Error(`Received message larger than max (${this.readMessageSize} vs ${this.maxReadMessageLength})`);if(this.readMessageRemaining=this.readMessageSize,this.readMessageRemaining>0)this.readState=ZM.READING_MESSAGE;else{let Z=Buffer.concat([this.readCompressFlag,this.readPartialSize],5);this.readState=ZM.NO_DATA,D.push(Z)}}break;case ZM.READING_MESSAGE:if(Q=Math.min(A.length-B,this.readMessageRemaining),this.readPartialMessage.push(A.slice(B,B+Q)),this.readMessageRemaining-=Q,B+=Q,this.readMessageRemaining===0){let Z=[this.readCompressFlag,this.readPartialSize].concat(this.readPartialMessage),G=Buffer.concat(Z,this.readMessageSize+5);this.readState=ZM.NO_DATA,D.push(G)}break;default:throw new Error("Unexpected read state")}return D}}Ys2.StreamDecoder=Is2});
var PY0=E((Ti2)=>{Object.defineProperty(Ti2,"__esModule",{value:!0});Ti2.makeClientConstructor=Oi2;Ti2.loadPackageDefinition=GY6;var N31=RY0(),QY6={unary:N31.Client.prototype.makeUnaryRequest,server_stream:N31.Client.prototype.makeServerStreamRequest,client_stream:N31.Client.prototype.makeClientStreamRequest,bidi:N31.Client.prototype.makeBidiStreamRequest};function TY0(A){return["__proto__","prototype","constructor"].includes(A)}function Oi2(A,B,Q){if(!Q)Q={};class D extends N31.Client{}return Object.keys(A).forEach((Z)=>{if(TY0(Z))return;let G=A[Z],F;if(typeof Z==="string"&&Z.charAt(0)==="$")throw new Error("Method names cannot start with $");if(G.requestStream)if(G.responseStream)F="bidi";else F="client_stream";else if(G.responseStream)F="server_stream";else F="unary";let{requestSerialize:I,responseDeserialize:Y}=G,W=DY6(QY6[F],G.path,I,Y);if(D.prototype[Z]=W,Object.assign(D.prototype[Z],G),G.originalName&&!TY0(G.originalName))D.prototype[G.originalName]=D.prototype[Z]}),D.service=A,D.serviceName=B,D}function DY6(A,B,Q,D){return function(...Z){return A.call(this,B,Q,D,...Z)}}function ZY6(A){return"format"in A}function GY6(A){let B={};for(let Q in A)if(Object.prototype.hasOwnProperty.call(A,Q)){let D=A[Q],Z=Q.split(".");if(Z.some((I)=>TY0(I)))continue;let G=Z[Z.length-1],F=B;for(let I of Z.slice(0,-1)){if(!F[I])F[I]={};F=F[I]}if(ZY6(D))F[G]=D;else F[G]=Oi2(D,G,{})}return B}});
var Pa2=E((Oa2)=>{Object.defineProperty(Oa2,"__esModule",{value:!0});Oa2.loadFileDescriptorSetFromObject=Oa2.loadFileDescriptorSetFromBuffer=Oa2.fromJSON=Oa2.loadSync=Oa2.load=Oa2.IdempotencyLevel=Oa2.isAnyExtension=Oa2.Long=void 0;var LJ6=ti2(),AM=pT1(),KW0=Ya2(),HW0=Ea2(),MJ6=Ua2();Oa2.Long=MJ6;function RJ6(A){return"@type"in A&&typeof A["@type"]==="string"}Oa2.isAnyExtension=RJ6;var Na2;(function(A){A.IDEMPOTENCY_UNKNOWN="IDEMPOTENCY_UNKNOWN",A.NO_SIDE_EFFECTS="NO_SIDE_EFFECTS",A.IDEMPOTENT="IDEMPOTENT"})(Na2=Oa2.IdempotencyLevel||(Oa2.IdempotencyLevel={}));var La2={longs:String,enums:String,bytes:String,defaults:!0,oneofs:!0,json:!0};function OJ6(A,B){if(A==="")return B;else return A+"."+B}function TJ6(A){return A instanceof AM.Service||A instanceof AM.Type||A instanceof AM.Enum}function PJ6(A){return A instanceof AM.Namespace||A instanceof AM.Root}function Ma2(A,B){let Q=OJ6(B,A.name);if(TJ6(A))return[[Q,A]];else if(PJ6(A)&&typeof A.nested!=="undefined")return Object.keys(A.nested).map((D)=>{return Ma2(A.nested[D],Q)}).reduce((D,Z)=>D.concat(Z),[]);return[]}function wa2(A,B){return function Q(D){return A.toObject(A.decode(D),B)}}function $a2(A){return function B(Q){if(Array.isArray(Q))throw new Error(`Failed to serialize message: expected object with ${A.name} structure, got array instead`);let D=A.fromObject(Q);return A.encode(D).finish()}}function SJ6(A){return(A||[]).reduce((B,Q)=>{for(let[D,Z]of Object.entries(Q))switch(D){case"uninterpreted_option":B.uninterpreted_option.push(Q.uninterpreted_option);break;default:B[D]=Z}return B},{deprecated:!1,idempotency_level:Na2.IDEMPOTENCY_UNKNOWN,uninterpreted_option:[]})}function jJ6(A,B,Q,D){let{resolvedRequestType:Z,resolvedResponseType:G}=A;return{path:"/"+B+"/"+A.name,requestStream:!!A.requestStream,responseStream:!!A.responseStream,requestSerialize:$a2(Z),requestDeserialize:wa2(Z,Q),responseSerialize:$a2(G),responseDeserialize:wa2(G,Q),originalName:LJ6(A.name),requestType:CW0(Z,D),responseType:CW0(G,D),options:SJ6(A.parsedOptions)}}function kJ6(A,B,Q,D){let Z={};for(let G of A.methodsArray)Z[G.name]=jJ6(G,B,Q,D);return Z}function CW0(A,B){let Q=A.toDescriptor("proto3");return{format:"Protocol Buffer 3 DescriptorProto",type:Q.$type.toObject(Q,La2),fileDescriptorProtos:B}}function yJ6(A,B){let Q=A.toDescriptor("proto3");return{format:"Protocol Buffer 3 EnumDescriptorProto",type:Q.$type.toObject(Q,La2),fileDescriptorProtos:B}}function _J6(A,B,Q,D){if(A instanceof AM.Service)return kJ6(A,B,Q,D);else if(A instanceof AM.Type)return CW0(A,D);else if(A instanceof AM.Enum)return yJ6(A,D);else throw new Error("Type mismatch in reflection object handling")}function aT1(A,B){let Q={};A.resolveAll();let Z=A.toDescriptor("proto3").file.map((G)=>Buffer.from(KW0.FileDescriptorProto.encode(G).finish()));for(let[G,F]of Ma2(A,""))Q[G]=_J6(F,G,B,Z);return Q}function Ra2(A,B){B=B||{};let Q=AM.Root.fromDescriptor(A);return Q.resolveAll(),aT1(Q,B)}function xJ6(A,B){return HW0.loadProtosWithOptions(A,B).then((Q)=>{return aT1(Q,B)})}Oa2.load=xJ6;function vJ6(A,B){let Q=HW0.loadProtosWithOptionsSync(A,B);return aT1(Q,B)}Oa2.loadSync=vJ6;function bJ6(A,B){B=B||{};let Q=AM.Root.fromJSON(A);return Q.resolveAll(),aT1(Q,B)}Oa2.fromJSON=bJ6;function fJ6(A,B){let Q=KW0.FileDescriptorSet.decode(A);return Ra2(Q,B)}Oa2.loadFileDescriptorSetFromBuffer=fJ6;function hJ6(A,B){let Q=KW0.FileDescriptorSet.fromObject(A);return Ra2(Q,B)}Oa2.loadFileDescriptorSetFromObject=hJ6;HW0.addCommonProtos()});
var Qa2=E((Dx5,Ba2)=>{Ba2.exports=ow;var DJ6=/\/|\./;function ow(A,B){if(!DJ6.test(A))A="google/protobuf/"+A+".proto",B={nested:{google:{nested:{protobuf:{nested:B}}}}};ow[A]=B}ow("any",{Any:{fields:{type_url:{type:"string",id:1},value:{type:"bytes",id:2}}}});var Aa2;ow("duration",{Duration:Aa2={fields:{seconds:{type:"int64",id:1},nanos:{type:"int32",id:2}}}});ow("timestamp",{Timestamp:Aa2});ow("empty",{Empty:{fields:{}}});ow("struct",{Struct:{fields:{fields:{keyType:"string",type:"Value",id:1}}},Value:{oneofs:{kind:{oneof:["nullValue","numberValue","stringValue","boolValue","structValue","listValue"]}},fields:{nullValue:{type:"NullValue",id:1},numberValue:{type:"double",id:2},stringValue:{type:"string",id:3},boolValue:{type:"bool",id:4},structValue:{type:"Struct",id:5},listValue:{type:"ListValue",id:6}}},NullValue:{values:{NULL_VALUE:0}},ListValue:{fields:{values:{rule:"repeated",type:"Value",id:1}}}});ow("wrappers",{DoubleValue:{fields:{value:{type:"double",id:1}}},FloatValue:{fields:{value:{type:"float",id:1}}},Int64Value:{fields:{value:{type:"int64",id:1}}},UInt64Value:{fields:{value:{type:"uint64",id:1}}},Int32Value:{fields:{value:{type:"int32",id:1}}},UInt32Value:{fields:{value:{type:"uint32",id:1}}},BoolValue:{fields:{value:{type:"bool",id:1}}},StringValue:{fields:{value:{type:"string",id:1}}},BytesValue:{fields:{value:{type:"bytes",id:1}}}});ow("field_mask",{FieldMask:{fields:{paths:{rule:"repeated",type:"string",id:1}}}});ow.get=function A(B){return ow[B]||null}});
var Qn2=E((x_5,Bn2)=>{Bn2.exports=L31;var QW6=UI0(),DW6=$I0(),xY0=DW6("fs");function L31(A,B,Q){if(typeof B==="function")Q=B,B={};else if(!B)B={};if(!Q)return QW6(L31,this,A,B);if(!B.xhr&&xY0&&xY0.readFile)return xY0.readFile(A,function D(Z,G){return Z&&typeof XMLHttpRequest!=="undefined"?L31.xhr(A,B,Q):Z?Q(Z):Q(null,B.binary?G:G.toString("utf8"))});return L31.xhr(A,B,Q)}L31.xhr=function A(B,Q,D){var Z=new XMLHttpRequest;if(Z.onreadystatechange=function G(){if(Z.readyState!==4)return;if(Z.status!==0&&Z.status!==200)return D(Error("status "+Z.status));if(Q.binary){var F=Z.response;if(!F){F=[];for(var I=0;I<Z.responseText.length;++I)F.push(Z.responseText.charCodeAt(I)&255)}return D(null,typeof Uint8Array!=="undefined"?new Uint8Array(F):F)}return D(null,Z.responseText)},Q.binary){if("overrideMimeType"in Z)Z.overrideMimeType("text/plain; charset=x-user-defined");Z.responseType="arraybuffer"}Z.open("GET",B),Z.send()}});
var RY0=E((Mi2)=>{Object.defineProperty(Mi2,"__esModule",{value:!0});Mi2.Client=void 0;var aL=Yi2(),AY6=OY0(),BY6=VE(),i_=S6(),io=tW(),MT1=LY0(),aw=Symbol(),no=Symbol(),ao=Symbol(),YP=Symbol();function MY0(A){return typeof A==="function"}function so(A){var B;return((B=A.stack)===null||B===void 0?void 0:B.split(`
`).slice(1).join(`
`))||"no stack trace available"}class Li2{constructor(A,B,Q={}){var D,Z;if(Q=Object.assign({},Q),this[no]=(D=Q.interceptors)!==null&&D!==void 0?D:[],delete Q.interceptors,this[ao]=(Z=Q.interceptor_providers)!==null&&Z!==void 0?Z:[],delete Q.interceptor_providers,this[no].length>0&&this[ao].length>0)throw new Error("Both interceptors and interceptor_providers were passed as options to the client constructor. Only one of these is allowed.");if(this[YP]=Q.callInvocationTransformer,delete Q.callInvocationTransformer,Q.channelOverride)this[aw]=Q.channelOverride;else if(Q.channelFactoryOverride){let G=Q.channelFactoryOverride;delete Q.channelFactoryOverride,this[aw]=G(A,B,Q)}else this[aw]=new AY6.ChannelImplementation(A,B,Q)}close(){this[aw].close()}getChannel(){return this[aw]}waitForReady(A,B){let Q=(D)=>{if(D){B(new Error("Failed to connect before the deadline"));return}let Z;try{Z=this[aw].getConnectivityState(!0)}catch(G){B(new Error("The channel has been closed"));return}if(Z===BY6.ConnectivityState.READY)B();else try{this[aw].watchConnectivityState(Z,A,Q)}catch(G){B(new Error("The channel has been closed"))}};setImmediate(Q)}checkOptionalUnaryResponseArguments(A,B,Q){if(MY0(A))return{metadata:new io.Metadata,options:{},callback:A};else if(MY0(B))if(A instanceof io.Metadata)return{metadata:A,options:{},callback:B};else return{metadata:new io.Metadata,options:A,callback:B};else{if(!(A instanceof io.Metadata&&B instanceof Object&&MY0(Q)))throw new Error("Incorrect arguments passed");return{metadata:A,options:B,callback:Q}}}makeUnaryRequest(A,B,Q,D,Z,G,F){var I,Y;let W=this.checkOptionalUnaryResponseArguments(Z,G,F),J={path:A,requestStream:!1,responseStream:!1,requestSerialize:B,responseDeserialize:Q},X={argument:D,metadata:W.metadata,call:new aL.ClientUnaryCallImpl,channel:this[aw],methodDefinition:J,callOptions:W.options,callback:W.callback};if(this[YP])X=this[YP](X);let V=X.call,C={clientInterceptors:this[no],clientInterceptorProviders:this[ao],callInterceptors:(I=X.callOptions.interceptors)!==null&&I!==void 0?I:[],callInterceptorProviders:(Y=X.callOptions.interceptor_providers)!==null&&Y!==void 0?Y:[]},K=MT1.getInterceptingCall(C,X.methodDefinition,X.callOptions,X.channel);V.call=K;let H=null,z=!1,$=new Error;return K.start(X.metadata,{onReceiveMetadata:(L)=>{V.emit("metadata",L)},onReceiveMessage(L){if(H!==null)K.cancelWithStatus(i_.Status.UNIMPLEMENTED,"Too many responses received");H=L},onReceiveStatus(L){if(z)return;if(z=!0,L.code===i_.Status.OK)if(H===null){let N=so($);X.callback(aL.callErrorFromStatus({code:i_.Status.UNIMPLEMENTED,details:"No message received",metadata:L.metadata},N))}else X.callback(null,H);else{let N=so($);X.callback(aL.callErrorFromStatus(L,N))}$=null,V.emit("status",L)}}),K.sendMessage(D),K.halfClose(),V}makeClientStreamRequest(A,B,Q,D,Z,G){var F,I;let Y=this.checkOptionalUnaryResponseArguments(D,Z,G),W={path:A,requestStream:!0,responseStream:!1,requestSerialize:B,responseDeserialize:Q},J={metadata:Y.metadata,call:new aL.ClientWritableStreamImpl(B),channel:this[aw],methodDefinition:W,callOptions:Y.options,callback:Y.callback};if(this[YP])J=this[YP](J);let X=J.call,V={clientInterceptors:this[no],clientInterceptorProviders:this[ao],callInterceptors:(F=J.callOptions.interceptors)!==null&&F!==void 0?F:[],callInterceptorProviders:(I=J.callOptions.interceptor_providers)!==null&&I!==void 0?I:[]},C=MT1.getInterceptingCall(V,J.methodDefinition,J.callOptions,J.channel);X.call=C;let K=null,H=!1,z=new Error;return C.start(J.metadata,{onReceiveMetadata:($)=>{X.emit("metadata",$)},onReceiveMessage($){if(K!==null)C.cancelWithStatus(i_.Status.UNIMPLEMENTED,"Too many responses received");K=$,C.startRead()},onReceiveStatus($){if(H)return;if(H=!0,$.code===i_.Status.OK)if(K===null){let L=so(z);J.callback(aL.callErrorFromStatus({code:i_.Status.UNIMPLEMENTED,details:"No message received",metadata:$.metadata},L))}else J.callback(null,K);else{let L=so(z);J.callback(aL.callErrorFromStatus($,L))}z=null,X.emit("status",$)}}),X}checkMetadataAndOptions(A,B){let Q,D;if(A instanceof io.Metadata)if(Q=A,B)D=B;else D={};else{if(A)D=A;else D={};Q=new io.Metadata}return{metadata:Q,options:D}}makeServerStreamRequest(A,B,Q,D,Z,G){var F,I;let Y=this.checkMetadataAndOptions(Z,G),W={path:A,requestStream:!1,responseStream:!0,requestSerialize:B,responseDeserialize:Q},J={argument:D,metadata:Y.metadata,call:new aL.ClientReadableStreamImpl(Q),channel:this[aw],methodDefinition:W,callOptions:Y.options};if(this[YP])J=this[YP](J);let X=J.call,V={clientInterceptors:this[no],clientInterceptorProviders:this[ao],callInterceptors:(F=J.callOptions.interceptors)!==null&&F!==void 0?F:[],callInterceptorProviders:(I=J.callOptions.interceptor_providers)!==null&&I!==void 0?I:[]},C=MT1.getInterceptingCall(V,J.methodDefinition,J.callOptions,J.channel);X.call=C;let K=!1,H=new Error;return C.start(J.metadata,{onReceiveMetadata(z){X.emit("metadata",z)},onReceiveMessage(z){X.push(z)},onReceiveStatus(z){if(K)return;if(K=!0,X.push(null),z.code!==i_.Status.OK){let $=so(H);X.emit("error",aL.callErrorFromStatus(z,$))}H=null,X.emit("status",z)}}),C.sendMessage(D),C.halfClose(),X}makeBidiStreamRequest(A,B,Q,D,Z){var G,F;let I=this.checkMetadataAndOptions(D,Z),Y={path:A,requestStream:!0,responseStream:!0,requestSerialize:B,responseDeserialize:Q},W={metadata:I.metadata,call:new aL.ClientDuplexStreamImpl(B,Q),channel:this[aw],methodDefinition:Y,callOptions:I.options};if(this[YP])W=this[YP](W);let J=W.call,X={clientInterceptors:this[no],clientInterceptorProviders:this[ao],callInterceptors:(G=W.callOptions.interceptors)!==null&&G!==void 0?G:[],callInterceptorProviders:(F=W.callOptions.interceptor_providers)!==null&&F!==void 0?F:[]},V=MT1.getInterceptingCall(X,W.methodDefinition,W.callOptions,W.channel);J.call=V;let C=!1,K=new Error;return V.start(W.metadata,{onReceiveMetadata(H){J.emit("metadata",H)},onReceiveMessage(H){J.push(H)},onReceiveStatus(H){if(C)return;if(C=!0,J.push(null),H.code!==i_.Status.OK){let z=so(K);J.emit("error",aL.callErrorFromStatus(H,z))}K=null,J.emit("status",H)}}),J}}Mi2.Client=Li2});
var S6=E((pl2)=>{Object.defineProperty(pl2,"__esModule",{value:!0});pl2.DEFAULT_MAX_RECEIVE_MESSAGE_LENGTH=pl2.DEFAULT_MAX_SEND_MESSAGE_LENGTH=pl2.Propagate=pl2.LogVerbosity=pl2.Status=void 0;var dl2;(function(A){A[A.OK=0]="OK",A[A.CANCELLED=1]="CANCELLED",A[A.UNKNOWN=2]="UNKNOWN",A[A.INVALID_ARGUMENT=3]="INVALID_ARGUMENT",A[A.DEADLINE_EXCEEDED=4]="DEADLINE_EXCEEDED",A[A.NOT_FOUND=5]="NOT_FOUND",A[A.ALREADY_EXISTS=6]="ALREADY_EXISTS",A[A.PERMISSION_DENIED=7]="PERMISSION_DENIED",A[A.RESOURCE_EXHAUSTED=8]="RESOURCE_EXHAUSTED",A[A.FAILED_PRECONDITION=9]="FAILED_PRECONDITION",A[A.ABORTED=10]="ABORTED",A[A.OUT_OF_RANGE=11]="OUT_OF_RANGE",A[A.UNIMPLEMENTED=12]="UNIMPLEMENTED",A[A.INTERNAL=13]="INTERNAL",A[A.UNAVAILABLE=14]="UNAVAILABLE",A[A.DATA_LOSS=15]="DATA_LOSS",A[A.UNAUTHENTICATED=16]="UNAUTHENTICATED"})(dl2||(pl2.Status=dl2={}));var cl2;(function(A){A[A.DEBUG=0]="DEBUG",A[A.INFO=1]="INFO",A[A.ERROR=2]="ERROR",A[A.NONE=3]="NONE"})(cl2||(pl2.LogVerbosity=cl2={}));var ll2;(function(A){A[A.DEADLINE=1]="DEADLINE",A[A.CENSUS_STATS_CONTEXT=2]="CENSUS_STATS_CONTEXT",A[A.CENSUS_TRACING_CONTEXT=4]="CENSUS_TRACING_CONTEXT",A[A.CANCELLATION=8]="CANCELLATION",A[A.DEFAULTS=65535]="DEFAULTS"})(ll2||(pl2.Propagate=ll2={}));pl2.DEFAULT_MAX_SEND_MESSAGE_LENGTH=-1;pl2.DEFAULT_MAX_RECEIVE_MESSAGE_LENGTH=4194304});
var SV=E((Ip2)=>{Object.defineProperty(Ip2,"__esModule",{value:!0});Ip2.parseUri=nG6;Ip2.splitHostPort=aG6;Ip2.combineHostPort=sG6;Ip2.uriToString=rG6;var iG6=/^(?:([A-Za-z0-9+.-]+):)?(?:\/\/([^/]*)\/)?(.+)$/;function nG6(A){let B=iG6.exec(A);if(B===null)return null;return{scheme:B[1],authority:B[2],path:B[3]}}var Fp2=/^\d+$/;function aG6(A){if(A.startsWith("[")){let B=A.indexOf("]");if(B===-1)return null;let Q=A.substring(1,B);if(Q.indexOf(":")===-1)return null;if(A.length>B+1)if(A[B+1]===":"){let D=A.substring(B+2);if(Fp2.test(D))return{host:Q,port:+D};else return null}else return null;else return{host:Q}}else{let B=A.split(":");if(B.length===2)if(Fp2.test(B[1]))return{host:B[0],port:+B[1]};else return null;else return{host:A}}}function sG6(A){if(A.port===void 0)return A.host;else if(A.host.includes(":"))return`[${A.host}]:${A.port}`;else return`${A.host}:${A.port}`}function rG6(A){let B="";if(A.scheme!==void 0)B+=A.scheme+":";if(A.authority!==void 0)B+="//"+A.authority+"/";return B+=A.path,B}});
var TW0=E((Fs2)=>{Object.defineProperty(Fs2,"__esModule",{value:!0});Fs2.parseCIDR=Zs2;Fs2.mapProxyName=dX6;Fs2.getProxiedConnection=cX6;var b31=D7(),Gt=S6(),Ds2=J1("net"),_X6=J1("http"),xX6=D7(),Qs2=CE(),f31=SV(),vX6=J1("url"),bX6=OW0(),fX6="proxy";function Ft(A){xX6.trace(Gt.LogVerbosity.DEBUG,fX6,A)}function hX6(){let A="",B="";if(process.env.grpc_proxy)B="grpc_proxy",A=process.env.grpc_proxy;else if(process.env.https_proxy)B="https_proxy",A=process.env.https_proxy;else if(process.env.http_proxy)B="http_proxy",A=process.env.http_proxy;else return{};let Q;try{Q=new vX6.URL(A)}catch(I){return b31.log(Gt.LogVerbosity.ERROR,`cannot parse value of "${B}" env var`),{}}if(Q.protocol!=="http:")return b31.log(Gt.LogVerbosity.ERROR,`"${Q.protocol}" scheme not supported in proxy URI`),{};let D=null;if(Q.username)if(Q.password)b31.log(Gt.LogVerbosity.INFO,"userinfo found in proxy URI"),D=decodeURIComponent(`${Q.username}:${Q.password}`);else D=Q.username;let{hostname:Z,port:G}=Q;if(G==="")G="80";let F={address:`${Z}:${G}`};if(D)F.creds=D;return Ft("Proxy server "+F.address+" set by environment variable "+B),F}function gX6(){let A=process.env.no_grpc_proxy,B="no_grpc_proxy";if(!A)A=process.env.no_proxy,B="no_proxy";if(A)return Ft("No proxy server list set by environment variable "+B),A.split(",");else return[]}function Zs2(A){let B=A.split("/");if(B.length!==2)return null;let Q=parseInt(B[1],10);if(!Ds2.isIPv4(B[0])||Number.isNaN(Q)||Q<0||Q>32)return null;return{ip:Gs2(B[0]),prefixLength:Q}}function Gs2(A){return A.split(".").reduce((B,Q)=>(B<<8)+parseInt(Q,10),0)}function uX6(A,B){let Q=A.ip,D=-1<<32-A.prefixLength;return(Gs2(B)&D)===(Q&D)}function mX6(A){for(let B of gX6()){let Q=Zs2(B);if(Ds2.isIPv4(A)&&Q&&uX6(Q,A))return!0;else if(A.endsWith(B))return!0}return!1}function dX6(A,B){var Q;let D={target:A,extraOptions:{}};if(((Q=B["grpc.enable_http_proxy"])!==null&&Q!==void 0?Q:1)===0)return D;if(A.scheme==="unix")return D;let Z=hX6();if(!Z.address)return D;let G=f31.splitHostPort(A.path);if(!G)return D;let F=G.host;if(mX6(F))return Ft("Not using proxy for target in no_proxy list: "+f31.uriToString(A)),D;let I={"grpc.http_connect_target":f31.uriToString(A)};if(Z.creds)I["grpc.http_connect_creds"]=Z.creds;return{target:{scheme:"dns",path:Z.address},extraOptions:I}}function cX6(A,B){var Q;if(!("grpc.http_connect_target"in B))return Promise.resolve(null);let D=B["grpc.http_connect_target"],Z=f31.parseUri(D);if(Z===null)return Promise.resolve(null);let G=f31.splitHostPort(Z.path);if(G===null)return Promise.resolve(null);let F=`${G.host}:${(Q=G.port)!==null&&Q!==void 0?Q:bX6.DEFAULT_PORT}`,I={method:"CONNECT",path:F},Y={Host:F};if(Qs2.isTcpSubchannelAddress(A))I.host=A.host,I.port=A.port;else I.socketPath=A.path;if("grpc.http_connect_creds"in B)Y["Proxy-Authorization"]="Basic "+Buffer.from(B["grpc.http_connect_creds"]).toString("base64");I.headers=Y;let W=Qs2.subchannelAddressToString(A);return Ft("Using proxy "+W+" to connect to "+I.path),new Promise((J,X)=>{let V=_X6.request(I);V.once("connect",(C,K,H)=>{if(V.removeAllListeners(),K.removeAllListeners(),C.statusCode===200){if(Ft("Successfully connected to "+I.path+" through proxy "+W),H.length>0)K.unshift(H);Ft("Successfully established a plaintext connection to "+I.path+" through proxy "+W),J(K)}else b31.log(Gt.LogVerbosity.ERROR,"Failed to connect to "+I.path+" through proxy "+W+" with status "+C.statusCode),X()}),V.once("error",(C)=>{V.removeAllListeners(),b31.log(Gt.LogVerbosity.ERROR,"Failed to connect to proxy "+W+" with error "+C.message),X()}),V.end()})}});
var To2=E((Oo2)=>{Object.defineProperty(Oo2,"__esModule",{value:!0});Oo2.setup=aK6;var No2=J1("net"),qP1=S6(),VJ0=tW(),Lo2=nL(),Mo2=SV(),pK6=D7(),iK6="ip_resolver";function Ro2(A){pK6.trace(qP1.LogVerbosity.DEBUG,iK6,A)}var CJ0="ipv4",KJ0="ipv6",nK6=443;class HJ0{constructor(A,B,Q){var D;this.listener=B,this.endpoints=[],this.error=null,this.hasReturnedResult=!1,Ro2("Resolver constructed for target "+Mo2.uriToString(A));let Z=[];if(!(A.scheme===CJ0||A.scheme===KJ0)){this.error={code:qP1.Status.UNAVAILABLE,details:`Unrecognized scheme ${A.scheme} in IP resolver`,metadata:new VJ0.Metadata};return}let G=A.path.split(",");for(let F of G){let I=Mo2.splitHostPort(F);if(I===null){this.error={code:qP1.Status.UNAVAILABLE,details:`Failed to parse ${A.scheme} address ${F}`,metadata:new VJ0.Metadata};return}if(A.scheme===CJ0&&!No2.isIPv4(I.host)||A.scheme===KJ0&&!No2.isIPv6(I.host)){this.error={code:qP1.Status.UNAVAILABLE,details:`Failed to parse ${A.scheme} address ${F}`,metadata:new VJ0.Metadata};return}Z.push({host:I.host,port:(D=I.port)!==null&&D!==void 0?D:nK6})}this.endpoints=Z.map((F)=>({addresses:[F]})),Ro2("Parsed "+A.scheme+" address list "+Z)}updateResolution(){if(!this.hasReturnedResult)this.hasReturnedResult=!0,process.nextTick(()=>{if(this.error)this.listener.onError(this.error);else this.listener.onSuccessfulResolution(this.endpoints,null,null,null,{})})}destroy(){this.hasReturnedResult=!1}static getDefaultAuthority(A){return A.path.split(",")[0]}}function aK6(){Lo2.registerResolver(CJ0,HJ0),Lo2.registerResolver(KJ0,HJ0)}});
var Tt2=E((yJ0)=>{Object.defineProperty(yJ0,"__esModule",{value:!0});yJ0.OTLPMetricExporter=void 0;var xz6=Ot2();Object.defineProperty(yJ0,"OTLPMetricExporter",{enumerable:!0,get:function(){return xz6.OTLPMetricExporter}})});
var UP1=E((Yo2)=>{Object.defineProperty(Yo2,"__esModule",{value:!0});Yo2.LeafLoadBalancer=Yo2.PickFirstLoadBalancer=Yo2.PickFirstLoadBalancingConfig=void 0;Yo2.shuffled=Go2;Yo2.setup=qK6;var YJ0=yu(),cG=VE(),du=p_(),Bo2=CE(),HK6=D7(),zK6=S6(),Qo2=CE(),Do2=J1("net"),EK6="pick_first";function c31(A){HK6.trace(zK6.LogVerbosity.DEBUG,EK6,A)}var l31="pick_first",UK6=250;class Ht{constructor(A){this.shuffleAddressList=A}getLoadBalancerName(){return l31}toJsonObject(){return{[l31]:{shuffleAddressList:this.shuffleAddressList}}}getShuffleAddressList(){return this.shuffleAddressList}static createFromJson(A){if("shuffleAddressList"in A&&typeof A.shuffleAddressList!=="boolean")throw new Error("pick_first config field shuffleAddressList must be a boolean if provided");return new Ht(A.shuffleAddressList===!0)}}Yo2.PickFirstLoadBalancingConfig=Ht;class Zo2{constructor(A){this.subchannel=A}pick(A){return{pickResultType:du.PickResultType.COMPLETE,subchannel:this.subchannel,status:null,onCallStarted:null,onCallEnded:null}}}function Go2(A){let B=A.slice();for(let Q=B.length-1;Q>1;Q--){let D=Math.floor(Math.random()*(Q+1)),Z=B[Q];B[Q]=B[D],B[D]=Z}return B}function wK6(A){if(A.length===0)return[];let B=[],Q=[],D=[],Z=Qo2.isTcpSubchannelAddress(A[0])&&Do2.isIPv6(A[0].host);for(let I of A)if(Qo2.isTcpSubchannelAddress(I)&&Do2.isIPv6(I.host))Q.push(I);else D.push(I);let G=Z?Q:D,F=Z?D:Q;for(let I=0;I<Math.max(G.length,F.length);I++){if(I<G.length)B.push(G[I]);if(I<F.length)B.push(F[I])}return B}var Fo2="grpc-node.internal.pick-first.report_health_status";class EP1{constructor(A){this.channelControlHelper=A,this.children=[],this.currentState=cG.ConnectivityState.IDLE,this.currentSubchannelIndex=0,this.currentPick=null,this.subchannelStateListener=(B,Q,D,Z,G)=>{this.onSubchannelStateUpdate(B,Q,D,G)},this.pickedSubchannelHealthListener=()=>this.calculateAndReportNewState(),this.stickyTransientFailureMode=!1,this.reportHealthStatus=!1,this.lastError=null,this.latestAddressList=null,this.latestOptions={},this.connectionDelayTimeout=setTimeout(()=>{},0),clearTimeout(this.connectionDelayTimeout)}allChildrenHaveReportedTF(){return this.children.every((A)=>A.hasReportedTransientFailure)}resetChildrenReportedTF(){this.children.every((A)=>A.hasReportedTransientFailure=!1)}calculateAndReportNewState(){var A;if(this.currentPick)if(this.reportHealthStatus&&!this.currentPick.isHealthy()){let B=`Picked subchannel ${this.currentPick.getAddress()} is unhealthy`;this.updateState(cG.ConnectivityState.TRANSIENT_FAILURE,new du.UnavailablePicker({details:B}),B)}else this.updateState(cG.ConnectivityState.READY,new Zo2(this.currentPick),null);else if(((A=this.latestAddressList)===null||A===void 0?void 0:A.length)===0){let B=`No connection established. Last error: ${this.lastError}`;this.updateState(cG.ConnectivityState.TRANSIENT_FAILURE,new du.UnavailablePicker({details:B}),B)}else if(this.children.length===0)this.updateState(cG.ConnectivityState.IDLE,new du.QueuePicker(this),null);else if(this.stickyTransientFailureMode){let B=`No connection established. Last error: ${this.lastError}`;this.updateState(cG.ConnectivityState.TRANSIENT_FAILURE,new du.UnavailablePicker({details:B}),B)}else this.updateState(cG.ConnectivityState.CONNECTING,new du.QueuePicker(this),null)}requestReresolution(){this.channelControlHelper.requestReresolution()}maybeEnterStickyTransientFailureMode(){if(!this.allChildrenHaveReportedTF())return;if(this.requestReresolution(),this.resetChildrenReportedTF(),this.stickyTransientFailureMode){this.calculateAndReportNewState();return}this.stickyTransientFailureMode=!0;for(let{subchannel:A}of this.children)A.startConnecting();this.calculateAndReportNewState()}removeCurrentPick(){if(this.currentPick!==null)this.currentPick.removeConnectivityStateListener(this.subchannelStateListener),this.channelControlHelper.removeChannelzChild(this.currentPick.getChannelzRef()),this.currentPick.removeHealthStateWatcher(this.pickedSubchannelHealthListener),this.currentPick.unref(),this.currentPick=null}onSubchannelStateUpdate(A,B,Q,D){var Z;if((Z=this.currentPick)===null||Z===void 0?void 0:Z.realSubchannelEquals(A)){if(Q!==cG.ConnectivityState.READY)this.removeCurrentPick(),this.calculateAndReportNewState();return}for(let[G,F]of this.children.entries())if(A.realSubchannelEquals(F.subchannel)){if(Q===cG.ConnectivityState.READY)this.pickSubchannel(F.subchannel);if(Q===cG.ConnectivityState.TRANSIENT_FAILURE){if(F.hasReportedTransientFailure=!0,D)this.lastError=D;if(this.maybeEnterStickyTransientFailureMode(),G===this.currentSubchannelIndex)this.startNextSubchannelConnecting(G+1)}F.subchannel.startConnecting();return}}startNextSubchannelConnecting(A){clearTimeout(this.connectionDelayTimeout);for(let[B,Q]of this.children.entries())if(B>=A){let D=Q.subchannel.getConnectivityState();if(D===cG.ConnectivityState.IDLE||D===cG.ConnectivityState.CONNECTING){this.startConnecting(B);return}}this.maybeEnterStickyTransientFailureMode()}startConnecting(A){var B,Q;if(clearTimeout(this.connectionDelayTimeout),this.currentSubchannelIndex=A,this.children[A].subchannel.getConnectivityState()===cG.ConnectivityState.IDLE)c31("Start connecting to subchannel with address "+this.children[A].subchannel.getAddress()),process.nextTick(()=>{var D;(D=this.children[A])===null||D===void 0||D.subchannel.startConnecting()});this.connectionDelayTimeout=setTimeout(()=>{this.startNextSubchannelConnecting(A+1)},UK6),(Q=(B=this.connectionDelayTimeout).unref)===null||Q===void 0||Q.call(B)}pickSubchannel(A){c31("Pick subchannel with address "+A.getAddress()),this.stickyTransientFailureMode=!1,A.ref(),this.channelControlHelper.addChannelzChild(A.getChannelzRef()),this.removeCurrentPick(),this.resetSubchannelList(),A.addConnectivityStateListener(this.subchannelStateListener),A.addHealthStateWatcher(this.pickedSubchannelHealthListener),this.currentPick=A,clearTimeout(this.connectionDelayTimeout),this.calculateAndReportNewState()}updateState(A,B,Q){c31(cG.ConnectivityState[this.currentState]+" -> "+cG.ConnectivityState[A]),this.currentState=A,this.channelControlHelper.updateState(A,B,Q)}resetSubchannelList(){for(let A of this.children)A.subchannel.removeConnectivityStateListener(this.subchannelStateListener),A.subchannel.unref(),this.channelControlHelper.removeChannelzChild(A.subchannel.getChannelzRef());this.currentSubchannelIndex=0,this.children=[]}connectToAddressList(A,B){c31("connectToAddressList(["+A.map((D)=>Bo2.subchannelAddressToString(D))+"])");let Q=A.map((D)=>({subchannel:this.channelControlHelper.createSubchannel(D,B),hasReportedTransientFailure:!1}));for(let{subchannel:D}of Q)if(D.getConnectivityState()===cG.ConnectivityState.READY){this.pickSubchannel(D);return}for(let{subchannel:D}of Q)D.ref(),this.channelControlHelper.addChannelzChild(D.getChannelzRef());this.resetSubchannelList(),this.children=Q;for(let{subchannel:D}of this.children)D.addConnectivityStateListener(this.subchannelStateListener);for(let D of this.children)if(D.subchannel.getConnectivityState()===cG.ConnectivityState.TRANSIENT_FAILURE)D.hasReportedTransientFailure=!0;this.startNextSubchannelConnecting(0),this.calculateAndReportNewState()}updateAddressList(A,B,Q){if(!(B instanceof Ht))return;if(this.reportHealthStatus=Q[Fo2],B.getShuffleAddressList())A=Go2(A);let D=[].concat(...A.map((G)=>G.addresses));if(c31("updateAddressList(["+D.map((G)=>Bo2.subchannelAddressToString(G))+"])"),D.length===0)this.lastError="No addresses resolved";let Z=wK6(D);this.latestAddressList=Z,this.latestOptions=Q,this.connectToAddressList(Z,Q)}exitIdle(){if(this.currentState===cG.ConnectivityState.IDLE&&this.latestAddressList)this.connectToAddressList(this.latestAddressList,this.latestOptions)}resetBackoff(){}destroy(){this.resetSubchannelList(),this.removeCurrentPick()}getTypeName(){return l31}}Yo2.PickFirstLoadBalancer=EP1;var $K6=new Ht(!1);class Io2{constructor(A,B,Q){this.endpoint=A,this.options=Q,this.latestState=cG.ConnectivityState.IDLE;let D=YJ0.createChildChannelControlHelper(B,{updateState:(Z,G,F)=>{this.latestState=Z,this.latestPicker=G,B.updateState(Z,G,F)}});this.pickFirstBalancer=new EP1(D),this.latestPicker=new du.QueuePicker(this.pickFirstBalancer)}startConnecting(){this.pickFirstBalancer.updateAddressList([this.endpoint],$K6,Object.assign(Object.assign({},this.options),{[Fo2]:!0}))}updateEndpoint(A,B){if(this.options=B,this.endpoint=A,this.latestState!==cG.ConnectivityState.IDLE)this.startConnecting()}getConnectivityState(){return this.latestState}getPicker(){return this.latestPicker}getEndpoint(){return this.endpoint}exitIdle(){this.pickFirstBalancer.exitIdle()}destroy(){this.pickFirstBalancer.destroy()}}Yo2.LeafLoadBalancer=Io2;function qK6(){YJ0.registerLoadBalancerType(l31,EP1,Ht),YJ0.registerDefaultLoadBalancerType(l31)}});
var Ua2=E((_31,VW0)=>{(function(A,B){function Q(D){return"default"in D?D.default:D}if(typeof define==="function"&&define.amd)define([],function(){var D={};return B(D),Q(D)});else if(typeof _31==="object"){if(B(_31),typeof VW0==="object")VW0.exports=Q(_31)}else(function(){var D={};B(D),A.Long=Q(D)})()})(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:_31,function(A){Object.defineProperty(A,"__esModule",{value:!0}),A.default=void 0;var B=null;try{B=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch{}function Q(a,x,e){this.low=a|0,this.high=x|0,this.unsigned=!!e}Q.prototype.__isLong__,Object.defineProperty(Q.prototype,"__isLong__",{value:!0});function D(a){return(a&&a.__isLong__)===!0}function Z(a){var x=Math.clz32(a&-a);return a?31-x:x}Q.isLong=D;var G={},F={};function I(a,x){var e,W1,U1;if(x){if(a>>>=0,U1=0<=a&&a<256){if(W1=F[a],W1)return W1}if(e=W(a,0,!0),U1)F[a]=e;return e}else{if(a|=0,U1=-128<=a&&a<128){if(W1=G[a],W1)return W1}if(e=W(a,a<0?-1:0,!1),U1)G[a]=e;return e}}Q.fromInt=I;function Y(a,x){if(isNaN(a))return x?O:N;if(x){if(a<0)return O;if(a>=z)return k}else{if(a<=-$)return c;if(a+1>=$)return f}if(a<0)return Y(-a,x).neg();return W(a%H|0,a/H|0,x)}Q.fromNumber=Y;function W(a,x,e){return new Q(a,x,e)}Q.fromBits=W;var J=Math.pow;function X(a,x,e){if(a.length===0)throw Error("empty string");if(typeof x==="number")e=x,x=!1;else x=!!x;if(a==="NaN"||a==="Infinity"||a==="+Infinity"||a==="-Infinity")return x?O:N;if(e=e||10,e<2||36<e)throw RangeError("radix");var W1;if((W1=a.indexOf("-"))>0)throw Error("interior hyphen");else if(W1===0)return X(a.substring(1),x,e).neg();var U1=Y(J(e,8)),y1=N;for(var W0=0;W0<a.length;W0+=8){var F0=Math.min(8,a.length-W0),g1=parseInt(a.substring(W0,W0+F0),e);if(F0<8){var K1=Y(J(e,F0));y1=y1.mul(K1).add(Y(g1))}else y1=y1.mul(U1),y1=y1.add(Y(g1))}return y1.unsigned=x,y1}Q.fromString=X;function V(a,x){if(typeof a==="number")return Y(a,x);if(typeof a==="string")return X(a,x);return W(a.low,a.high,typeof x==="boolean"?x:a.unsigned)}Q.fromValue=V;var C=65536,K=16777216,H=C*C,z=H*H,$=z/2,L=I(K),N=I(0);Q.ZERO=N;var O=I(0,!0);Q.UZERO=O;var R=I(1);Q.ONE=R;var T=I(1,!0);Q.UONE=T;var j=I(-1);Q.NEG_ONE=j;var f=W(-1,2147483647,!1);Q.MAX_VALUE=f;var k=W(-1,-1,!0);Q.MAX_UNSIGNED_VALUE=k;var c=W(0,-2147483648,!1);Q.MIN_VALUE=c;var h=Q.prototype;if(h.toInt=function a(){return this.unsigned?this.low>>>0:this.low},h.toNumber=function a(){if(this.unsigned)return(this.high>>>0)*H+(this.low>>>0);return this.high*H+(this.low>>>0)},h.toString=function a(x){if(x=x||10,x<2||36<x)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative())if(this.eq(c)){var e=Y(x),W1=this.div(e),U1=W1.mul(e).sub(this);return W1.toString(x)+U1.toInt().toString(x)}else return"-"+this.neg().toString(x);var y1=Y(J(x,6),this.unsigned),W0=this,F0="";while(!0){var g1=W0.div(y1),K1=W0.sub(g1.mul(y1)).toInt()>>>0,G1=K1.toString(x);if(W0=g1,W0.isZero())return G1+F0;else{while(G1.length<6)G1="0"+G1;F0=""+G1+F0}}},h.getHighBits=function a(){return this.high},h.getHighBitsUnsigned=function a(){return this.high>>>0},h.getLowBits=function a(){return this.low},h.getLowBitsUnsigned=function a(){return this.low>>>0},h.getNumBitsAbs=function a(){if(this.isNegative())return this.eq(c)?64:this.neg().getNumBitsAbs();var x=this.high!=0?this.high:this.low;for(var e=31;e>0;e--)if((x&1<<e)!=0)break;return this.high!=0?e+33:e+1},h.isSafeInteger=function a(){var x=this.high>>21;if(!x)return!0;if(this.unsigned)return!1;return x===-1&&!(this.low===0&&this.high===-2097152)},h.isZero=function a(){return this.high===0&&this.low===0},h.eqz=h.isZero,h.isNegative=function a(){return!this.unsigned&&this.high<0},h.isPositive=function a(){return this.unsigned||this.high>=0},h.isOdd=function a(){return(this.low&1)===1},h.isEven=function a(){return(this.low&1)===0},h.equals=function a(x){if(!D(x))x=V(x);if(this.unsigned!==x.unsigned&&this.high>>>31===1&&x.high>>>31===1)return!1;return this.high===x.high&&this.low===x.low},h.eq=h.equals,h.notEquals=function a(x){return!this.eq(x)},h.neq=h.notEquals,h.ne=h.notEquals,h.lessThan=function a(x){return this.comp(x)<0},h.lt=h.lessThan,h.lessThanOrEqual=function a(x){return this.comp(x)<=0},h.lte=h.lessThanOrEqual,h.le=h.lessThanOrEqual,h.greaterThan=function a(x){return this.comp(x)>0},h.gt=h.greaterThan,h.greaterThanOrEqual=function a(x){return this.comp(x)>=0},h.gte=h.greaterThanOrEqual,h.ge=h.greaterThanOrEqual,h.compare=function a(x){if(!D(x))x=V(x);if(this.eq(x))return 0;var e=this.isNegative(),W1=x.isNegative();if(e&&!W1)return-1;if(!e&&W1)return 1;if(!this.unsigned)return this.sub(x).isNegative()?-1:1;return x.high>>>0>this.high>>>0||x.high===this.high&&x.low>>>0>this.low>>>0?-1:1},h.comp=h.compare,h.negate=function a(){if(!this.unsigned&&this.eq(c))return c;return this.not().add(R)},h.neg=h.negate,h.add=function a(x){if(!D(x))x=V(x);var e=this.high>>>16,W1=this.high&65535,U1=this.low>>>16,y1=this.low&65535,W0=x.high>>>16,F0=x.high&65535,g1=x.low>>>16,K1=x.low&65535,G1=0,L1=0,M1=0,a1=0;return a1+=y1+K1,M1+=a1>>>16,a1&=65535,M1+=U1+g1,L1+=M1>>>16,M1&=65535,L1+=W1+F0,G1+=L1>>>16,L1&=65535,G1+=e+W0,G1&=65535,W(M1<<16|a1,G1<<16|L1,this.unsigned)},h.subtract=function a(x){if(!D(x))x=V(x);return this.add(x.neg())},h.sub=h.subtract,h.multiply=function a(x){if(this.isZero())return this;if(!D(x))x=V(x);if(B){var e=B.mul(this.low,this.high,x.low,x.high);return W(e,B.get_high(),this.unsigned)}if(x.isZero())return this.unsigned?O:N;if(this.eq(c))return x.isOdd()?c:N;if(x.eq(c))return this.isOdd()?c:N;if(this.isNegative())if(x.isNegative())return this.neg().mul(x.neg());else return this.neg().mul(x).neg();else if(x.isNegative())return this.mul(x.neg()).neg();if(this.lt(L)&&x.lt(L))return Y(this.toNumber()*x.toNumber(),this.unsigned);var W1=this.high>>>16,U1=this.high&65535,y1=this.low>>>16,W0=this.low&65535,F0=x.high>>>16,g1=x.high&65535,K1=x.low>>>16,G1=x.low&65535,L1=0,M1=0,a1=0,i1=0;return i1+=W0*G1,a1+=i1>>>16,i1&=65535,a1+=y1*G1,M1+=a1>>>16,a1&=65535,a1+=W0*K1,M1+=a1>>>16,a1&=65535,M1+=U1*G1,L1+=M1>>>16,M1&=65535,M1+=y1*K1,L1+=M1>>>16,M1&=65535,M1+=W0*g1,L1+=M1>>>16,M1&=65535,L1+=W1*G1+U1*K1+y1*g1+W0*F0,L1&=65535,W(a1<<16|i1,L1<<16|M1,this.unsigned)},h.mul=h.multiply,h.divide=function a(x){if(!D(x))x=V(x);if(x.isZero())throw Error("division by zero");if(B){if(!this.unsigned&&this.high===-2147483648&&x.low===-1&&x.high===-1)return this;var e=(this.unsigned?B.div_u:B.div_s)(this.low,this.high,x.low,x.high);return W(e,B.get_high(),this.unsigned)}if(this.isZero())return this.unsigned?O:N;var W1,U1,y1;if(!this.unsigned){if(this.eq(c))if(x.eq(R)||x.eq(j))return c;else if(x.eq(c))return R;else{var W0=this.shr(1);if(W1=W0.div(x).shl(1),W1.eq(N))return x.isNegative()?R:j;else return U1=this.sub(x.mul(W1)),y1=W1.add(U1.div(x)),y1}else if(x.eq(c))return this.unsigned?O:N;if(this.isNegative()){if(x.isNegative())return this.neg().div(x.neg());return this.neg().div(x).neg()}else if(x.isNegative())return this.div(x.neg()).neg();y1=N}else{if(!x.unsigned)x=x.toUnsigned();if(x.gt(this))return O;if(x.gt(this.shru(1)))return T;y1=O}U1=this;while(U1.gte(x)){W1=Math.max(1,Math.floor(U1.toNumber()/x.toNumber()));var F0=Math.ceil(Math.log(W1)/Math.LN2),g1=F0<=48?1:J(2,F0-48),K1=Y(W1),G1=K1.mul(x);while(G1.isNegative()||G1.gt(U1))W1-=g1,K1=Y(W1,this.unsigned),G1=K1.mul(x);if(K1.isZero())K1=R;y1=y1.add(K1),U1=U1.sub(G1)}return y1},h.div=h.divide,h.modulo=function a(x){if(!D(x))x=V(x);if(B){var e=(this.unsigned?B.rem_u:B.rem_s)(this.low,this.high,x.low,x.high);return W(e,B.get_high(),this.unsigned)}return this.sub(this.div(x).mul(x))},h.mod=h.modulo,h.rem=h.modulo,h.not=function a(){return W(~this.low,~this.high,this.unsigned)},h.countLeadingZeros=function a(){return this.high?Math.clz32(this.high):Math.clz32(this.low)+32},h.clz=h.countLeadingZeros,h.countTrailingZeros=function a(){return this.low?Z(this.low):Z(this.high)+32},h.ctz=h.countTrailingZeros,h.and=function a(x){if(!D(x))x=V(x);return W(this.low&x.low,this.high&x.high,this.unsigned)},h.or=function a(x){if(!D(x))x=V(x);return W(this.low|x.low,this.high|x.high,this.unsigned)},h.xor=function a(x){if(!D(x))x=V(x);return W(this.low^x.low,this.high^x.high,this.unsigned)},h.shiftLeft=function a(x){if(D(x))x=x.toInt();if((x&=63)===0)return this;else if(x<32)return W(this.low<<x,this.high<<x|this.low>>>32-x,this.unsigned);else return W(0,this.low<<x-32,this.unsigned)},h.shl=h.shiftLeft,h.shiftRight=function a(x){if(D(x))x=x.toInt();if((x&=63)===0)return this;else if(x<32)return W(this.low>>>x|this.high<<32-x,this.high>>x,this.unsigned);else return W(this.high>>x-32,this.high>=0?0:-1,this.unsigned)},h.shr=h.shiftRight,h.shiftRightUnsigned=function a(x){if(D(x))x=x.toInt();if((x&=63)===0)return this;if(x<32)return W(this.low>>>x|this.high<<32-x,this.high>>>x,this.unsigned);if(x===32)return W(this.high,0,this.unsigned);return W(this.high>>>x-32,0,this.unsigned)},h.shru=h.shiftRightUnsigned,h.shr_u=h.shiftRightUnsigned,h.rotateLeft=function a(x){var e;if(D(x))x=x.toInt();if((x&=63)===0)return this;if(x===32)return W(this.high,this.low,this.unsigned);if(x<32)return e=32-x,W(this.low<<x|this.high>>>e,this.high<<x|this.low>>>e,this.unsigned);return x-=32,e=32-x,W(this.high<<x|this.low>>>e,this.low<<x|this.high>>>e,this.unsigned)},h.rotl=h.rotateLeft,h.rotateRight=function a(x){var e;if(D(x))x=x.toInt();if((x&=63)===0)return this;if(x===32)return W(this.high,this.low,this.unsigned);if(x<32)return e=32-x,W(this.high<<e|this.low>>>x,this.low<<e|this.high>>>x,this.unsigned);return x-=32,e=32-x,W(this.low<<e|this.high>>>x,this.high<<e|this.low>>>x,this.unsigned)},h.rotr=h.rotateRight,h.toSigned=function a(){if(!this.unsigned)return this;return W(this.low,this.high,!1)},h.toUnsigned=function a(){if(this.unsigned)return this;return W(this.low,this.high,!0)},h.toBytes=function a(x){return x?this.toBytesLE():this.toBytesBE()},h.toBytesLE=function a(){var x=this.high,e=this.low;return[e&255,e>>>8&255,e>>>16&255,e>>>24,x&255,x>>>8&255,x>>>16&255,x>>>24]},h.toBytesBE=function a(){var x=this.high,e=this.low;return[x>>>24,x>>>16&255,x>>>8&255,x&255,e>>>24,e>>>16&255,e>>>8&255,e&255]},Q.fromBytes=function a(x,e,W1){return W1?Q.fromBytesLE(x,e):Q.fromBytesBE(x,e)},Q.fromBytesLE=function a(x,e){return new Q(x[0]|x[1]<<8|x[2]<<16|x[3]<<24,x[4]|x[5]<<8|x[6]<<16|x[7]<<24,e)},Q.fromBytesBE=function a(x,e){return new Q(x[4]<<24|x[5]<<16|x[6]<<8|x[7],x[0]<<24|x[1]<<16|x[2]<<8|x[3],e)},typeof BigInt==="function")Q.fromBigInt=function a(x,e){var W1=Number(BigInt.asIntN(32,x)),U1=Number(BigInt.asIntN(32,x>>BigInt(32)));return W(W1,U1,e)},Q.fromValue=function a(x,e){if(typeof x==="bigint")return fromBigInt(x,e);return V(x,e)},h.toBigInt=function a(){var x=BigInt(this.low>>>0),e=BigInt(this.unsigned?this.high>>>0:this.high);return e<<BigInt(32)|x};var n=A.default=Q})});
var Ut2=E((zt2)=>{Object.defineProperty(zt2,"__esModule",{value:!0});zt2.convertLegacyOtlpGrpcOptions=void 0;var Nz6=ZQ(),Ht2=It2(),Lz6=a31(),Mz6=Kt2();function Rz6(A,B){if(A.headers)Nz6.diag.warn("Headers cannot be set when using grpc");let Q=A.credentials;return Ht2.mergeOtlpGrpcConfigurationWithDefaults({url:A.url,metadata:()=>{return A.metadata??Lz6.createEmptyMetadata()},compression:A.compression,timeoutMillis:A.timeoutMillis,concurrencyLimit:A.concurrencyLimit,credentials:Q!=null?()=>Q:void 0},Mz6.getOtlpGrpcConfigurationFromEnv(B),Ht2.getOtlpGrpcDefaultConfiguration())}zt2.convertLegacyOtlpGrpcOptions=Rz6});
var VE=E(($p2)=>{Object.defineProperty($p2,"__esModule",{value:!0});$p2.ConnectivityState=void 0;var wp2;(function(A){A[A.IDLE=0]="IDLE",A[A.CONNECTING=1]="CONNECTING",A[A.READY=2]="READY",A[A.TRANSIENT_FAILURE=3]="TRANSIENT_FAILURE",A[A.SHUTDOWN=4]="SHUTDOWN"})(wp2||($p2.ConnectivityState=wp2={}))});
var VT1=E((Bp2)=>{Object.defineProperty(Bp2,"__esModule",{value:!0});Bp2.CallCredentials=void 0;var IY0=tW();function dG6(A){return"getRequestHeaders"in A&&typeof A.getRequestHeaders==="function"}class co{static createFromMetadataGenerator(A){return new YY0(A)}static createFromGoogleCredential(A){return co.createFromMetadataGenerator((B,Q)=>{let D;if(dG6(A))D=A.getRequestHeaders(B.service_url);else D=new Promise((Z,G)=>{A.getRequestMetadata(B.service_url,(F,I)=>{if(F){G(F);return}if(!I){G(new Error("Headers not set by metadata plugin"));return}Z(I)})});D.then((Z)=>{let G=new IY0.Metadata;for(let F of Object.keys(Z))G.add(F,Z[F]);Q(null,G)},(Z)=>{Q(Z)})})}static createEmpty(){return new WY0}}Bp2.CallCredentials=co;class XT1 extends co{constructor(A){super();this.creds=A}async generateMetadata(A){let B=new IY0.Metadata,Q=await Promise.all(this.creds.map((D)=>D.generateMetadata(A)));for(let D of Q)B.merge(D);return B}compose(A){return new XT1(this.creds.concat([A]))}_equals(A){if(this===A)return!0;if(A instanceof XT1)return this.creds.every((B,Q)=>B._equals(A.creds[Q]));else return!1}}class YY0 extends co{constructor(A){super();this.metadataGenerator=A}generateMetadata(A){return new Promise((B,Q)=>{this.metadataGenerator(A,(D,Z)=>{if(Z!==void 0)B(Z);else Q(D)})})}compose(A){return new XT1([this,A])}_equals(A){if(this===A)return!0;if(A instanceof YY0)return this.metadataGenerator===A.metadataGenerator;else return!1}}class WY0 extends co{generateMetadata(A){return Promise.resolve(new IY0.Metadata)}compose(A){return A}_equals(A){return A instanceof WY0}}});
var Vi2=E((Ji2)=>{Object.defineProperty(Ji2,"__esModule",{value:!0});Ji2.InterceptingListenerImpl=void 0;Ji2.isInterceptingListener=lI6;function lI6(A){return A.onReceiveMetadata!==void 0&&A.onReceiveMetadata.length===1}class Wi2{constructor(A,B){this.listener=A,this.nextListener=B,this.processingMetadata=!1,this.hasPendingMessage=!1,this.processingMessage=!1,this.pendingStatus=null}processPendingMessage(){if(this.hasPendingMessage)this.nextListener.onReceiveMessage(this.pendingMessage),this.pendingMessage=null,this.hasPendingMessage=!1}processPendingStatus(){if(this.pendingStatus)this.nextListener.onReceiveStatus(this.pendingStatus)}onReceiveMetadata(A){this.processingMetadata=!0,this.listener.onReceiveMetadata(A,(B)=>{this.processingMetadata=!1,this.nextListener.onReceiveMetadata(B),this.processPendingMessage(),this.processPendingStatus()})}onReceiveMessage(A){this.processingMessage=!0,this.listener.onReceiveMessage(A,(B)=>{if(this.processingMessage=!1,this.processingMetadata)this.pendingMessage=B,this.hasPendingMessage=!0;else this.nextListener.onReceiveMessage(B),this.processPendingStatus()})}onReceiveStatus(A){this.listener.onReceiveStatus(A,(B)=>{if(this.processingMetadata||this.processingMessage)this.pendingStatus=B;else this.nextListener.onReceiveStatus(B)})}}Ji2.InterceptingListenerImpl=Wi2});
var Wa2=E((Fx5,HJ6)=>{HJ6.exports={nested:{google:{nested:{protobuf:{nested:{Api:{fields:{name:{type:"string",id:1},methods:{rule:"repeated",type:"Method",id:2},options:{rule:"repeated",type:"Option",id:3},version:{type:"string",id:4},sourceContext:{type:"SourceContext",id:5},mixins:{rule:"repeated",type:"Mixin",id:6},syntax:{type:"Syntax",id:7}}},Method:{fields:{name:{type:"string",id:1},requestTypeUrl:{type:"string",id:2},requestStreaming:{type:"bool",id:3},responseTypeUrl:{type:"string",id:4},responseStreaming:{type:"bool",id:5},options:{rule:"repeated",type:"Option",id:6},syntax:{type:"Syntax",id:7}}},Mixin:{fields:{name:{type:"string",id:1},root:{type:"string",id:2}}},SourceContext:{fields:{fileName:{type:"string",id:1}}},Option:{fields:{name:{type:"string",id:1},value:{type:"Any",id:2}}},Syntax:{values:{SYNTAX_PROTO2:0,SYNTAX_PROTO3:1}}}}}}}}});
var XJ0=E((D8)=>{Object.defineProperty(D8,"__esModule",{value:!0});D8.SUBCHANNEL_ARGS_EXCLUDE_KEY_PREFIX=D8.createCertificateProviderChannelCredentials=D8.FileWatcherCertificateProvider=D8.createCertificateProviderServerCredentials=D8.createServerCredentialsWithInterceptors=D8.BaseSubchannelWrapper=D8.registerAdminService=D8.FilterStackFactory=D8.BaseFilter=D8.PickResultType=D8.QueuePicker=D8.UnavailablePicker=D8.ChildLoadBalancerHandler=D8.EndpointMap=D8.endpointHasAddress=D8.endpointToString=D8.subchannelAddressToString=D8.LeafLoadBalancer=D8.isLoadBalancerNameRegistered=D8.parseLoadBalancingConfig=D8.selectLbConfigFromList=D8.registerLoadBalancerType=D8.createChildChannelControlHelper=D8.BackoffTimeout=D8.parseDuration=D8.durationToMs=D8.splitHostPort=D8.uriToString=D8.createResolver=D8.registerResolver=D8.log=D8.trace=void 0;var Ko2=D7();Object.defineProperty(D8,"trace",{enumerable:!0,get:function(){return Ko2.trace}});Object.defineProperty(D8,"log",{enumerable:!0,get:function(){return Ko2.log}});var Ho2=nL();Object.defineProperty(D8,"registerResolver",{enumerable:!0,get:function(){return Ho2.registerResolver}});Object.defineProperty(D8,"createResolver",{enumerable:!0,get:function(){return Ho2.createResolver}});var zo2=SV();Object.defineProperty(D8,"uriToString",{enumerable:!0,get:function(){return zo2.uriToString}});Object.defineProperty(D8,"splitHostPort",{enumerable:!0,get:function(){return zo2.splitHostPort}});var Eo2=IJ0();Object.defineProperty(D8,"durationToMs",{enumerable:!0,get:function(){return Eo2.durationToMs}});Object.defineProperty(D8,"parseDuration",{enumerable:!0,get:function(){return Eo2.parseDuration}});var kK6=H31();Object.defineProperty(D8,"BackoffTimeout",{enumerable:!0,get:function(){return kK6.BackoffTimeout}});var p31=yu();Object.defineProperty(D8,"createChildChannelControlHelper",{enumerable:!0,get:function(){return p31.createChildChannelControlHelper}});Object.defineProperty(D8,"registerLoadBalancerType",{enumerable:!0,get:function(){return p31.registerLoadBalancerType}});Object.defineProperty(D8,"selectLbConfigFromList",{enumerable:!0,get:function(){return p31.selectLbConfigFromList}});Object.defineProperty(D8,"parseLoadBalancingConfig",{enumerable:!0,get:function(){return p31.parseLoadBalancingConfig}});Object.defineProperty(D8,"isLoadBalancerNameRegistered",{enumerable:!0,get:function(){return p31.isLoadBalancerNameRegistered}});var yK6=UP1();Object.defineProperty(D8,"LeafLoadBalancer",{enumerable:!0,get:function(){return yK6.LeafLoadBalancer}});var $P1=CE();Object.defineProperty(D8,"subchannelAddressToString",{enumerable:!0,get:function(){return $P1.subchannelAddressToString}});Object.defineProperty(D8,"endpointToString",{enumerable:!0,get:function(){return $P1.endpointToString}});Object.defineProperty(D8,"endpointHasAddress",{enumerable:!0,get:function(){return $P1.endpointHasAddress}});Object.defineProperty(D8,"EndpointMap",{enumerable:!0,get:function(){return $P1.EndpointMap}});var _K6=qT1();Object.defineProperty(D8,"ChildLoadBalancerHandler",{enumerable:!0,get:function(){return _K6.ChildLoadBalancerHandler}});var JJ0=p_();Object.defineProperty(D8,"UnavailablePicker",{enumerable:!0,get:function(){return JJ0.UnavailablePicker}});Object.defineProperty(D8,"QueuePicker",{enumerable:!0,get:function(){return JJ0.QueuePicker}});Object.defineProperty(D8,"PickResultType",{enumerable:!0,get:function(){return JJ0.PickResultType}});var xK6=fW0();Object.defineProperty(D8,"BaseFilter",{enumerable:!0,get:function(){return xK6.BaseFilter}});var vK6=vW0();Object.defineProperty(D8,"FilterStackFactory",{enumerable:!0,get:function(){return vK6.FilterStackFactory}});var bK6=LT1();Object.defineProperty(D8,"registerAdminService",{enumerable:!0,get:function(){return bK6.registerAdminService}});var fK6=JP1();Object.defineProperty(D8,"BaseSubchannelWrapper",{enumerable:!0,get:function(){return fK6.BaseSubchannelWrapper}});var Uo2=KP1();Object.defineProperty(D8,"createServerCredentialsWithInterceptors",{enumerable:!0,get:function(){return Uo2.createServerCredentialsWithInterceptors}});Object.defineProperty(D8,"createCertificateProviderServerCredentials",{enumerable:!0,get:function(){return Uo2.createCertificateProviderServerCredentials}});var hK6=Co2();Object.defineProperty(D8,"FileWatcherCertificateProvider",{enumerable:!0,get:function(){return hK6.FileWatcherCertificateProvider}});var gK6=C31();Object.defineProperty(D8,"createCertificateProviderChannelCredentials",{enumerable:!0,get:function(){return gK6.createCertificateProviderChannelCredentials}});var uK6=lW0();Object.defineProperty(D8,"SUBCHANNEL_ARGS_EXCLUDE_KEY_PREFIX",{enumerable:!0,get:function(){return uK6.SUBCHANNEL_ARGS_EXCLUDE_KEY_PREFIX}})});
var XY0=E((Zp2)=>{Object.defineProperty(Zp2,"__esModule",{value:!0});Zp2.CIPHER_SUITES=void 0;Zp2.getDefaultRootsData=lG6;var cG6=J1("fs");Zp2.CIPHER_SUITES=process.env.GRPC_SSL_CIPHER_SUITES;var Dp2=process.env.GRPC_DEFAULT_SSL_ROOTS_FILE_PATH,JY0=null;function lG6(){if(Dp2){if(JY0===null)JY0=cG6.readFileSync(Dp2);return JY0}return null}});
var Xa2=E((Yx5,EJ6)=>{EJ6.exports={nested:{google:{nested:{protobuf:{nested:{Type:{fields:{name:{type:"string",id:1},fields:{rule:"repeated",type:"Field",id:2},oneofs:{rule:"repeated",type:"string",id:3},options:{rule:"repeated",type:"Option",id:4},sourceContext:{type:"SourceContext",id:5},syntax:{type:"Syntax",id:6}}},Field:{fields:{kind:{type:"Kind",id:1},cardinality:{type:"Cardinality",id:2},number:{type:"int32",id:3},name:{type:"string",id:4},typeUrl:{type:"string",id:6},oneofIndex:{type:"int32",id:7},packed:{type:"bool",id:8},options:{rule:"repeated",type:"Option",id:9},jsonName:{type:"string",id:10},defaultValue:{type:"string",id:11}},nested:{Kind:{values:{TYPE_UNKNOWN:0,TYPE_DOUBLE:1,TYPE_FLOAT:2,TYPE_INT64:3,TYPE_UINT64:4,TYPE_INT32:5,TYPE_FIXED64:6,TYPE_FIXED32:7,TYPE_BOOL:8,TYPE_STRING:9,TYPE_GROUP:10,TYPE_MESSAGE:11,TYPE_BYTES:12,TYPE_UINT32:13,TYPE_ENUM:14,TYPE_SFIXED32:15,TYPE_SFIXED64:16,TYPE_SINT32:17,TYPE_SINT64:18}},Cardinality:{values:{CARDINALITY_UNKNOWN:0,CARDINALITY_OPTIONAL:1,CARDINALITY_REQUIRED:2,CARDINALITY_REPEATED:3}}}},Enum:{fields:{name:{type:"string",id:1},enumvalue:{rule:"repeated",type:"EnumValue",id:2},options:{rule:"repeated",type:"Option",id:3},sourceContext:{type:"SourceContext",id:4},syntax:{type:"Syntax",id:5}}},EnumValue:{fields:{name:{type:"string",id:1},number:{type:"int32",id:2},options:{rule:"repeated",type:"Option",id:3}}},Option:{fields:{name:{type:"string",id:1},value:{type:"Any",id:2}}},Syntax:{values:{SYNTAX_PROTO2:0,SYNTAX_PROTO3:1}},Any:{fields:{type_url:{type:"string",id:1},value:{type:"bytes",id:2}}},SourceContext:{fields:{fileName:{type:"string",id:1}}}}}}}}}});
var YT1=E((rl2)=>{Object.defineProperty(rl2,"__esModule",{value:!0});rl2.getErrorMessage=SG6;rl2.getErrorCode=jG6;function SG6(A){if(A instanceof Error)return A.message;else return String(A)}function jG6(A){if(typeof A==="object"&&A!==null&&"code"in A&&typeof A.code==="number")return A.code;else return null}});
var Ya2=E((r4,Ia2)=>{var sJ=pT1();Ia2.exports=r4=sJ.descriptor=sJ.Root.fromJSON(JW0()).lookup(".google.protobuf");var{Namespace:Za2,Root:k31,Enum:VP,Type:o_,Field:t_,MapField:GJ6,OneOf:iT1,Service:y31,Method:nT1}=sJ;k31.fromDescriptor=function A(B){if(typeof B.length==="number")B=r4.FileDescriptorSet.decode(B);var Q=new k31;if(B.file){var D,Z;for(var G=0,F;G<B.file.length;++G){if(Z=Q,(D=B.file[G]).package&&D.package.length)Z=Q.define(D.package);if(D.name&&D.name.length)Q.files.push(Z.filename=D.name);if(D.messageType)for(F=0;F<D.messageType.length;++F)Z.add(o_.fromDescriptor(D.messageType[F],D.syntax));if(D.enumType)for(F=0;F<D.enumType.length;++F)Z.add(VP.fromDescriptor(D.enumType[F]));if(D.extension)for(F=0;F<D.extension.length;++F)Z.add(t_.fromDescriptor(D.extension[F]));if(D.service)for(F=0;F<D.service.length;++F)Z.add(y31.fromDescriptor(D.service[F]));var I=Qt(D.options,r4.FileOptions);if(I){var Y=Object.keys(I);for(F=0;F<Y.length;++F)Z.setOption(Y[F],I[Y[F]])}}}return Q};k31.prototype.toDescriptor=function A(B){var Q=r4.FileDescriptorSet.create();return Ga2(this,Q.file,B),Q};function Ga2(A,B,Q){var D=r4.FileDescriptorProto.create({name:A.filename||(A.fullName.substring(1).replace(/\./g,"_")||"root")+".proto"});if(Q)D.syntax=Q;if(!(A instanceof k31))D.package=A.fullName.substring(1);for(var Z=0,G;Z<A.nestedArray.length;++Z)if((G=A._nestedArray[Z])instanceof o_)D.messageType.push(G.toDescriptor(Q));else if(G instanceof VP)D.enumType.push(G.toDescriptor());else if(G instanceof t_)D.extension.push(G.toDescriptor(Q));else if(G instanceof y31)D.service.push(G.toDescriptor());else if(G instanceof Za2)Ga2(G,B,Q);if(D.options=Dt(A.options,r4.FileOptions),D.messageType.length+D.enumType.length+D.extension.length+D.service.length)B.push(D)}var FJ6=0;o_.fromDescriptor=function A(B,Q){if(typeof B.length==="number")B=r4.DescriptorProto.decode(B);var D=new o_(B.name.length?B.name:"Type"+FJ6++,Qt(B.options,r4.MessageOptions)),Z;if(B.oneofDecl)for(Z=0;Z<B.oneofDecl.length;++Z)D.add(iT1.fromDescriptor(B.oneofDecl[Z]));if(B.field)for(Z=0;Z<B.field.length;++Z){var G=t_.fromDescriptor(B.field[Z],Q);if(D.add(G),B.field[Z].hasOwnProperty("oneofIndex"))D.oneofsArray[B.field[Z].oneofIndex].add(G)}if(B.extension)for(Z=0;Z<B.extension.length;++Z)D.add(t_.fromDescriptor(B.extension[Z],Q));if(B.nestedType){for(Z=0;Z<B.nestedType.length;++Z)if(D.add(o_.fromDescriptor(B.nestedType[Z],Q)),B.nestedType[Z].options&&B.nestedType[Z].options.mapEntry)D.setOption("map_entry",!0)}if(B.enumType)for(Z=0;Z<B.enumType.length;++Z)D.add(VP.fromDescriptor(B.enumType[Z]));if(B.extensionRange&&B.extensionRange.length){D.extensions=[];for(Z=0;Z<B.extensionRange.length;++Z)D.extensions.push([B.extensionRange[Z].start,B.extensionRange[Z].end])}if(B.reservedRange&&B.reservedRange.length||B.reservedName&&B.reservedName.length){if(D.reserved=[],B.reservedRange)for(Z=0;Z<B.reservedRange.length;++Z)D.reserved.push([B.reservedRange[Z].start,B.reservedRange[Z].end]);if(B.reservedName)for(Z=0;Z<B.reservedName.length;++Z)D.reserved.push(B.reservedName[Z])}return D};o_.prototype.toDescriptor=function A(B){var Q=r4.DescriptorProto.create({name:this.name}),D;for(D=0;D<this.fieldsArray.length;++D){var Z;if(Q.field.push(Z=this._fieldsArray[D].toDescriptor(B)),this._fieldsArray[D]instanceof GJ6){var G=XW0(this._fieldsArray[D].keyType,this._fieldsArray[D].resolvedKeyType),F=XW0(this._fieldsArray[D].type,this._fieldsArray[D].resolvedType),I=F===11||F===14?this._fieldsArray[D].resolvedType&&Fa2(this.parent,this._fieldsArray[D].resolvedType)||this._fieldsArray[D].type:void 0;Q.nestedType.push(r4.DescriptorProto.create({name:Z.typeName,field:[r4.FieldDescriptorProto.create({name:"key",number:1,label:1,type:G}),r4.FieldDescriptorProto.create({name:"value",number:2,label:1,type:F,typeName:I})],options:r4.MessageOptions.create({mapEntry:!0})}))}}for(D=0;D<this.oneofsArray.length;++D)Q.oneofDecl.push(this._oneofsArray[D].toDescriptor());for(D=0;D<this.nestedArray.length;++D)if(this._nestedArray[D]instanceof t_)Q.field.push(this._nestedArray[D].toDescriptor(B));else if(this._nestedArray[D]instanceof o_)Q.nestedType.push(this._nestedArray[D].toDescriptor(B));else if(this._nestedArray[D]instanceof VP)Q.enumType.push(this._nestedArray[D].toDescriptor());if(this.extensions)for(D=0;D<this.extensions.length;++D)Q.extensionRange.push(r4.DescriptorProto.ExtensionRange.create({start:this.extensions[D][0],end:this.extensions[D][1]}));if(this.reserved)for(D=0;D<this.reserved.length;++D)if(typeof this.reserved[D]==="string")Q.reservedName.push(this.reserved[D]);else Q.reservedRange.push(r4.DescriptorProto.ReservedRange.create({start:this.reserved[D][0],end:this.reserved[D][1]}));return Q.options=Dt(this.options,r4.MessageOptions),Q};var IJ6=/^(?![eE])[0-9]*(?:\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/;t_.fromDescriptor=function A(B,Q){if(typeof B.length==="number")B=r4.DescriptorProto.decode(B);if(typeof B.number!=="number")throw Error("missing field id");var D;if(B.typeName&&B.typeName.length)D=B.typeName;else D=VJ6(B.type);var Z;switch(B.label){case 1:Z=void 0;break;case 2:Z="required";break;case 3:Z="repeated";break;default:throw Error("illegal label: "+B.label)}var G=B.extendee;if(B.extendee!==void 0)G=G.length?G:void 0;var F=new t_(B.name.length?B.name:"field"+B.number,B.number,D,Z,G);if(F.options=Qt(B.options,r4.FieldOptions),B.defaultValue&&B.defaultValue.length){var I=B.defaultValue;switch(I){case"true":case"TRUE":I=!0;break;case"false":case"FALSE":I=!1;break;default:var Y=IJ6.exec(I);if(Y)I=parseInt(I);break}F.setOption("default",I)}if(CJ6(B.type)){if(Q==="proto3"){if(B.options&&!B.options.packed)F.setOption("packed",!1)}else if(!(B.options&&B.options.packed))F.setOption("packed",!1)}return F};t_.prototype.toDescriptor=function A(B){var Q=r4.FieldDescriptorProto.create({name:this.name,number:this.id});if(this.map)Q.type=11,Q.typeName=sJ.util.ucFirst(this.name),Q.label=3;else{switch(Q.type=XW0(this.type,this.resolve().resolvedType)){case 10:case 11:case 14:Q.typeName=this.resolvedType?Fa2(this.parent,this.resolvedType):this.type;break}switch(this.rule){case"repeated":Q.label=3;break;case"required":Q.label=2;break;default:Q.label=1;break}}if(Q.extendee=this.extensionField?this.extensionField.parent.fullName:this.extend,this.partOf){if((Q.oneofIndex=this.parent.oneofsArray.indexOf(this.partOf))<0)throw Error("missing oneof")}if(this.options){if(Q.options=Dt(this.options,r4.FieldOptions),this.options.default!=null)Q.defaultValue=String(this.options.default)}if(B==="proto3"){if(!this.packed)(Q.options||(Q.options=r4.FieldOptions.create())).packed=!1}else if(this.packed)(Q.options||(Q.options=r4.FieldOptions.create())).packed=!0;return Q};var YJ6=0;VP.fromDescriptor=function A(B){if(typeof B.length==="number")B=r4.EnumDescriptorProto.decode(B);var Q={};if(B.value)for(var D=0;D<B.value.length;++D){var Z=B.value[D].name,G=B.value[D].number||0;Q[Z&&Z.length?Z:"NAME"+G]=G}return new VP(B.name&&B.name.length?B.name:"Enum"+YJ6++,Q,Qt(B.options,r4.EnumOptions))};VP.prototype.toDescriptor=function A(){var B=[];for(var Q=0,D=Object.keys(this.values);Q<D.length;++Q)B.push(r4.EnumValueDescriptorProto.create({name:D[Q],number:this.values[D[Q]]}));return r4.EnumDescriptorProto.create({name:this.name,value:B,options:Dt(this.options,r4.EnumOptions)})};var WJ6=0;iT1.fromDescriptor=function A(B){if(typeof B.length==="number")B=r4.OneofDescriptorProto.decode(B);return new iT1(B.name&&B.name.length?B.name:"oneof"+WJ6++)};iT1.prototype.toDescriptor=function A(){return r4.OneofDescriptorProto.create({name:this.name})};var JJ6=0;y31.fromDescriptor=function A(B){if(typeof B.length==="number")B=r4.ServiceDescriptorProto.decode(B);var Q=new y31(B.name&&B.name.length?B.name:"Service"+JJ6++,Qt(B.options,r4.ServiceOptions));if(B.method)for(var D=0;D<B.method.length;++D)Q.add(nT1.fromDescriptor(B.method[D]));return Q};y31.prototype.toDescriptor=function A(){var B=[];for(var Q=0;Q<this.methodsArray.length;++Q)B.push(this._methodsArray[Q].toDescriptor());return r4.ServiceDescriptorProto.create({name:this.name,method:B,options:Dt(this.options,r4.ServiceOptions)})};var XJ6=0;nT1.fromDescriptor=function A(B){if(typeof B.length==="number")B=r4.MethodDescriptorProto.decode(B);return new nT1(B.name&&B.name.length?B.name:"Method"+XJ6++,"rpc",B.inputType,B.outputType,Boolean(B.clientStreaming),Boolean(B.serverStreaming),Qt(B.options,r4.MethodOptions))};nT1.prototype.toDescriptor=function A(){return r4.MethodDescriptorProto.create({name:this.name,inputType:this.resolvedRequestType?this.resolvedRequestType.fullName:this.requestType,outputType:this.resolvedResponseType?this.resolvedResponseType.fullName:this.responseType,clientStreaming:this.requestStream,serverStreaming:this.responseStream,options:Dt(this.options,r4.MethodOptions)})};function VJ6(A){switch(A){case 1:return"double";case 2:return"float";case 3:return"int64";case 4:return"uint64";case 5:return"int32";case 6:return"fixed64";case 7:return"fixed32";case 8:return"bool";case 9:return"string";case 12:return"bytes";case 13:return"uint32";case 15:return"sfixed32";case 16:return"sfixed64";case 17:return"sint32";case 18:return"sint64"}throw Error("illegal type: "+A)}function CJ6(A){switch(A){case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 13:case 14:case 15:case 16:case 17:case 18:return!0}return!1}function XW0(A,B){switch(A){case"double":return 1;case"float":return 2;case"int64":return 3;case"uint64":return 4;case"int32":return 5;case"fixed64":return 6;case"fixed32":return 7;case"bool":return 8;case"string":return 9;case"bytes":return 12;case"uint32":return 13;case"sfixed32":return 15;case"sfixed64":return 16;case"sint32":return 17;case"sint64":return 18}if(B instanceof VP)return 14;if(B instanceof o_)return B.group?10:11;throw Error("illegal type: "+A)}function Qt(A,B){if(!A)return;var Q=[];for(var D=0,Z,G,F;D<B.fieldsArray.length;++D)if((G=(Z=B._fieldsArray[D]).name)!=="uninterpretedOption"){if(A.hasOwnProperty(G)){if(F=A[G],Z.resolvedType instanceof VP&&typeof F==="number"&&Z.resolvedType.valuesById[F]!==void 0)F=Z.resolvedType.valuesById[F];Q.push(KJ6(G),F)}}return Q.length?sJ.util.toObject(Q):void 0}function Dt(A,B){if(!A)return;var Q=[];for(var D=0,Z=Object.keys(A),G,F;D<Z.length;++D){if(F=A[G=Z[D]],G==="default")continue;var I=B.fields[G];if(!I&&!(I=B.fields[G=sJ.util.camelCase(G)]))continue;Q.push(G,F)}return Q.length?B.fromObject(sJ.util.toObject(Q)):void 0}function Fa2(A,B){var Q=A.fullName.split("."),D=B.fullName.split("."),Z=0,G=0,F=D.length-1;if(!(A instanceof k31)&&B instanceof Za2)while(Z<Q.length&&G<F&&Q[Z]===D[G]){var I=B.lookup(Q[Z++],!0);if(I!==null&&I!==B)break;++G}else for(;Z<Q.length&&G<F&&Q[Z]===D[G];++Z,++G);return D.slice(G).join(".")}function KJ6(A){return A.substring(0,1)+A.substring(1).replace(/([A-Z])(?=[a-z]|$)/g,function(B,Q){return"_"+Q.toLowerCase()})}});
var Yi2=E((Fi2)=>{Object.defineProperty(Fi2,"__esModule",{value:!0});Fi2.ClientDuplexStreamImpl=Fi2.ClientWritableStreamImpl=Fi2.ClientReadableStreamImpl=Fi2.ClientUnaryCallImpl=void 0;Fi2.callErrorFromStatus=gI6;var hI6=J1("events"),$Y0=J1("stream"),w31=S6();function gI6(A,B){let Q=`${A.code} ${w31.Status[A.code]}: ${A.details}`,Z=`${new Error(Q).stack}
for call at
${B}`;return Object.assign(new Error(Q),A,{stack:Z})}class Qi2 extends hI6.EventEmitter{constructor(){super()}cancel(){var A;(A=this.call)===null||A===void 0||A.cancelWithStatus(w31.Status.CANCELLED,"Cancelled on client")}getPeer(){var A,B;return(B=(A=this.call)===null||A===void 0?void 0:A.getPeer())!==null&&B!==void 0?B:"unknown"}}Fi2.ClientUnaryCallImpl=Qi2;class Di2 extends $Y0.Readable{constructor(A){super({objectMode:!0});this.deserialize=A}cancel(){var A;(A=this.call)===null||A===void 0||A.cancelWithStatus(w31.Status.CANCELLED,"Cancelled on client")}getPeer(){var A,B;return(B=(A=this.call)===null||A===void 0?void 0:A.getPeer())!==null&&B!==void 0?B:"unknown"}_read(A){var B;(B=this.call)===null||B===void 0||B.startRead()}}Fi2.ClientReadableStreamImpl=Di2;class Zi2 extends $Y0.Writable{constructor(A){super({objectMode:!0});this.serialize=A}cancel(){var A;(A=this.call)===null||A===void 0||A.cancelWithStatus(w31.Status.CANCELLED,"Cancelled on client")}getPeer(){var A,B;return(B=(A=this.call)===null||A===void 0?void 0:A.getPeer())!==null&&B!==void 0?B:"unknown"}_write(A,B,Q){var D;let Z={callback:Q},G=Number(B);if(!Number.isNaN(G))Z.flags=G;(D=this.call)===null||D===void 0||D.sendMessageWithContext(Z,A)}_final(A){var B;(B=this.call)===null||B===void 0||B.halfClose(),A()}}Fi2.ClientWritableStreamImpl=Zi2;class Gi2 extends $Y0.Duplex{constructor(A,B){super({objectMode:!0});this.serialize=A,this.deserialize=B}cancel(){var A;(A=this.call)===null||A===void 0||A.cancelWithStatus(w31.Status.CANCELLED,"Cancelled on client")}getPeer(){var A,B;return(B=(A=this.call)===null||A===void 0?void 0:A.getPeer())!==null&&B!==void 0?B:"unknown"}_read(A){var B;(B=this.call)===null||B===void 0||B.startRead()}_write(A,B,Q){var D;let Z={callback:Q},G=Number(B);if(!Number.isNaN(G))Z.flags=G;(D=this.call)===null||D===void 0||D.sendMessageWithContext(Z,A)}_final(A){var B;(B=this.call)===null||B===void 0||B.halfClose(),A()}}Fi2.ClientDuplexStreamImpl=Gi2});
var Zr2=E((Qr2)=>{Object.defineProperty(Qr2,"__esModule",{value:!0});Qr2.RetryingCall=Qr2.MessageBufferTracker=Qr2.RetryThrottler=void 0;var WP1=S6(),AC6=u31(),BC6=tW(),QC6=D7(),DC6="retrying_call";class es2{constructor(A,B,Q){if(this.maxTokens=A,this.tokenRatio=B,Q)this.tokens=Q.tokens*(A/Q.maxTokens);else this.tokens=A}addCallSucceeded(){this.tokens=Math.min(this.tokens+this.tokenRatio,this.maxTokens)}addCallFailed(){this.tokens=Math.max(this.tokens-1,0)}canRetryCall(){return this.tokens>this.maxTokens/2}}Qr2.RetryThrottler=es2;class Ar2{constructor(A,B){this.totalLimit=A,this.limitPerCall=B,this.totalAllocated=0,this.allocatedPerCall=new Map}allocate(A,B){var Q;let D=(Q=this.allocatedPerCall.get(B))!==null&&Q!==void 0?Q:0;if(this.limitPerCall-D<A||this.totalLimit-this.totalAllocated<A)return!1;return this.allocatedPerCall.set(B,D+A),this.totalAllocated+=A,!0}free(A,B){var Q;if(this.totalAllocated<A)throw new Error(`Invalid buffer allocation state: call ${B} freed ${A} > total allocated ${this.totalAllocated}`);this.totalAllocated-=A;let D=(Q=this.allocatedPerCall.get(B))!==null&&Q!==void 0?Q:0;if(D<A)throw new Error(`Invalid buffer allocation state: call ${B} freed ${A} > allocated for call ${D}`);this.allocatedPerCall.set(B,D-A)}freeAll(A){var B;let Q=(B=this.allocatedPerCall.get(A))!==null&&B!==void 0?B:0;if(this.totalAllocated<Q)throw new Error(`Invalid buffer allocation state: call ${A} allocated ${Q} > total allocated ${this.totalAllocated}`);this.totalAllocated-=Q,this.allocatedPerCall.delete(A)}}Qr2.MessageBufferTracker=Ar2;var uW0="grpc-previous-rpc-attempts",ZC6=5;class Br2{constructor(A,B,Q,D,Z,G,F,I,Y){var W;this.channel=A,this.callConfig=B,this.methodName=Q,this.host=D,this.credentials=Z,this.deadline=G,this.callNumber=F,this.bufferTracker=I,this.retryThrottler=Y,this.listener=null,this.initialMetadata=null,this.underlyingCalls=[],this.writeBuffer=[],this.writeBufferOffset=0,this.readStarted=!1,this.transparentRetryUsed=!1,this.attempts=0,this.hedgingTimer=null,this.committedCallIndex=null,this.initialRetryBackoffSec=0,this.nextRetryBackoffSec=0;let J=(W=A.getOptions()["grpc-node.retry_max_attempts_limit"])!==null&&W!==void 0?W:ZC6;if(A.getOptions()["grpc.enable_retries"]===0)this.state="NO_RETRY",this.maxAttempts=1;else if(B.methodConfig.retryPolicy){this.state="RETRY";let X=B.methodConfig.retryPolicy;this.nextRetryBackoffSec=this.initialRetryBackoffSec=Number(X.initialBackoff.substring(0,X.initialBackoff.length-1)),this.maxAttempts=Math.min(X.maxAttempts,J)}else if(B.methodConfig.hedgingPolicy)this.state="HEDGING",this.maxAttempts=Math.min(B.methodConfig.hedgingPolicy.maxAttempts,J);else this.state="TRANSPARENT_ONLY",this.maxAttempts=1;this.startTime=new Date}getDeadlineInfo(){if(this.underlyingCalls.length===0)return[];let A=[],B=this.underlyingCalls[this.underlyingCalls.length-1];if(this.underlyingCalls.length>1)A.push(`previous attempts: ${this.underlyingCalls.length-1}`);if(B.startTime>this.startTime)A.push(`time to current attempt start: ${AC6.formatDateDifference(this.startTime,B.startTime)}`);return A.push(...B.call.getDeadlineInfo()),A}getCallNumber(){return this.callNumber}trace(A){QC6.trace(WP1.LogVerbosity.DEBUG,DC6,"["+this.callNumber+"] "+A)}reportStatus(A){this.trace("ended with status: code="+A.code+' details="'+A.details+'" start time='+this.startTime.toISOString()),this.bufferTracker.freeAll(this.callNumber),this.writeBufferOffset=this.writeBufferOffset+this.writeBuffer.length,this.writeBuffer=[],process.nextTick(()=>{var B;(B=this.listener)===null||B===void 0||B.onReceiveStatus({code:A.code,details:A.details,metadata:A.metadata})})}cancelWithStatus(A,B){this.trace("cancelWithStatus code: "+A+' details: "'+B+'"'),this.reportStatus({code:A,details:B,metadata:new BC6.Metadata});for(let{call:Q}of this.underlyingCalls)Q.cancelWithStatus(A,B)}getPeer(){if(this.committedCallIndex!==null)return this.underlyingCalls[this.committedCallIndex].call.getPeer();else return"unknown"}getBufferEntry(A){var B;return(B=this.writeBuffer[A-this.writeBufferOffset])!==null&&B!==void 0?B:{entryType:"FREED",allocated:!1}}getNextBufferIndex(){return this.writeBufferOffset+this.writeBuffer.length}clearSentMessages(){if(this.state!=="COMMITTED")return;let A;if(this.underlyingCalls[this.committedCallIndex].state==="COMPLETED")A=this.getNextBufferIndex();else A=this.underlyingCalls[this.committedCallIndex].nextMessageToSend;for(let B=this.writeBufferOffset;B<A;B++){let Q=this.getBufferEntry(B);if(Q.allocated)this.bufferTracker.free(Q.message.message.length,this.callNumber)}this.writeBuffer=this.writeBuffer.slice(A-this.writeBufferOffset),this.writeBufferOffset=A}commitCall(A){var B,Q;if(this.state==="COMMITTED")return;this.trace("Committing call ["+this.underlyingCalls[A].call.getCallNumber()+"] at index "+A),this.state="COMMITTED",(Q=(B=this.callConfig).onCommitted)===null||Q===void 0||Q.call(B),this.committedCallIndex=A;for(let D=0;D<this.underlyingCalls.length;D++){if(D===A)continue;if(this.underlyingCalls[D].state==="COMPLETED")continue;this.underlyingCalls[D].state="COMPLETED",this.underlyingCalls[D].call.cancelWithStatus(WP1.Status.CANCELLED,"Discarded in favor of other hedged attempt")}this.clearSentMessages()}commitCallWithMostMessages(){if(this.state==="COMMITTED")return;let A=-1,B=-1;for(let[Q,D]of this.underlyingCalls.entries())if(D.state==="ACTIVE"&&D.nextMessageToSend>A)A=D.nextMessageToSend,B=Q;if(B===-1)this.state="TRANSPARENT_ONLY";else this.commitCall(B)}isStatusCodeInList(A,B){return A.some((Q)=>{var D;return Q===B||Q.toString().toLowerCase()===((D=WP1.Status[B])===null||D===void 0?void 0:D.toLowerCase())})}getNextRetryBackoffMs(){var A;let B=(A=this.callConfig)===null||A===void 0?void 0:A.methodConfig.retryPolicy;if(!B)return 0;let Q=Math.random()*this.nextRetryBackoffSec*1000,D=Number(B.maxBackoff.substring(0,B.maxBackoff.length-1));return this.nextRetryBackoffSec=Math.min(this.nextRetryBackoffSec*B.backoffMultiplier,D),Q}maybeRetryCall(A,B){if(this.state!=="RETRY"){B(!1);return}if(this.attempts>=this.maxAttempts){B(!1);return}let Q;if(A===null)Q=this.getNextRetryBackoffMs();else if(A<0){this.state="TRANSPARENT_ONLY",B(!1);return}else Q=A,this.nextRetryBackoffSec=this.initialRetryBackoffSec;setTimeout(()=>{var D,Z;if(this.state!=="RETRY"){B(!1);return}if((Z=(D=this.retryThrottler)===null||D===void 0?void 0:D.canRetryCall())!==null&&Z!==void 0?Z:!0)B(!0),this.attempts+=1,this.startNewAttempt();else this.trace("Retry attempt denied by throttling policy"),B(!1)},Q)}countActiveCalls(){let A=0;for(let B of this.underlyingCalls)if((B===null||B===void 0?void 0:B.state)==="ACTIVE")A+=1;return A}handleProcessedStatus(A,B,Q){var D,Z,G;switch(this.state){case"COMMITTED":case"NO_RETRY":case"TRANSPARENT_ONLY":this.commitCall(B),this.reportStatus(A);break;case"HEDGING":if(this.isStatusCodeInList((D=this.callConfig.methodConfig.hedgingPolicy.nonFatalStatusCodes)!==null&&D!==void 0?D:[],A.code)){(Z=this.retryThrottler)===null||Z===void 0||Z.addCallFailed();let F;if(Q===null)F=0;else if(Q<0){this.state="TRANSPARENT_ONLY",this.commitCall(B),this.reportStatus(A);return}else F=Q;setTimeout(()=>{if(this.maybeStartHedgingAttempt(),this.countActiveCalls()===0)this.commitCall(B),this.reportStatus(A)},F)}else this.commitCall(B),this.reportStatus(A);break;case"RETRY":if(this.isStatusCodeInList(this.callConfig.methodConfig.retryPolicy.retryableStatusCodes,A.code))(G=this.retryThrottler)===null||G===void 0||G.addCallFailed(),this.maybeRetryCall(Q,(F)=>{if(!F)this.commitCall(B),this.reportStatus(A)});else this.commitCall(B),this.reportStatus(A);break}}getPushback(A){let B=A.get("grpc-retry-pushback-ms");if(B.length===0)return null;try{return parseInt(B[0])}catch(Q){return-1}}handleChildStatus(A,B){var Q;if(this.underlyingCalls[B].state==="COMPLETED")return;if(this.trace("state="+this.state+" handling status with progress "+A.progress+" from child ["+this.underlyingCalls[B].call.getCallNumber()+"] in state "+this.underlyingCalls[B].state),this.underlyingCalls[B].state="COMPLETED",A.code===WP1.Status.OK){(Q=this.retryThrottler)===null||Q===void 0||Q.addCallSucceeded(),this.commitCall(B),this.reportStatus(A);return}if(this.state==="NO_RETRY"){this.commitCall(B),this.reportStatus(A);return}if(this.state==="COMMITTED"){this.reportStatus(A);return}let D=this.getPushback(A.metadata);switch(A.progress){case"NOT_STARTED":this.startNewAttempt();break;case"REFUSED":if(this.transparentRetryUsed)this.handleProcessedStatus(A,B,D);else this.transparentRetryUsed=!0,this.startNewAttempt();break;case"DROP":this.commitCall(B),this.reportStatus(A);break;case"PROCESSED":this.handleProcessedStatus(A,B,D);break}}maybeStartHedgingAttempt(){if(this.state!=="HEDGING")return;if(!this.callConfig.methodConfig.hedgingPolicy)return;if(this.attempts>=this.maxAttempts)return;this.attempts+=1,this.startNewAttempt(),this.maybeStartHedgingTimer()}maybeStartHedgingTimer(){var A,B,Q;if(this.hedgingTimer)clearTimeout(this.hedgingTimer);if(this.state!=="HEDGING")return;if(!this.callConfig.methodConfig.hedgingPolicy)return;let D=this.callConfig.methodConfig.hedgingPolicy;if(this.attempts>=this.maxAttempts)return;let Z=(A=D.hedgingDelay)!==null&&A!==void 0?A:"0s",G=Number(Z.substring(0,Z.length-1));this.hedgingTimer=setTimeout(()=>{this.maybeStartHedgingAttempt()},G*1000),(Q=(B=this.hedgingTimer).unref)===null||Q===void 0||Q.call(B)}startNewAttempt(){let A=this.channel.createLoadBalancingCall(this.callConfig,this.methodName,this.host,this.credentials,this.deadline);this.trace("Created child call ["+A.getCallNumber()+"] for attempt "+this.attempts);let B=this.underlyingCalls.length;this.underlyingCalls.push({state:"ACTIVE",call:A,nextMessageToSend:0,startTime:new Date});let Q=this.attempts-1,D=this.initialMetadata.clone();if(Q>0)D.set(uW0,`${Q}`);let Z=!1;if(A.start(D,{onReceiveMetadata:(G)=>{if(this.trace("Received metadata from child ["+A.getCallNumber()+"]"),this.commitCall(B),Z=!0,Q>0)G.set(uW0,`${Q}`);if(this.underlyingCalls[B].state==="ACTIVE")this.listener.onReceiveMetadata(G)},onReceiveMessage:(G)=>{if(this.trace("Received message from child ["+A.getCallNumber()+"]"),this.commitCall(B),this.underlyingCalls[B].state==="ACTIVE")this.listener.onReceiveMessage(G)},onReceiveStatus:(G)=>{if(this.trace("Received status from child ["+A.getCallNumber()+"]"),!Z&&Q>0)G.metadata.set(uW0,`${Q}`);this.handleChildStatus(G,B)}}),this.sendNextChildMessage(B),this.readStarted)A.startRead()}start(A,B){this.trace("start called"),this.listener=B,this.initialMetadata=A,this.attempts+=1,this.startNewAttempt(),this.maybeStartHedgingTimer()}handleChildWriteCompleted(A){var B,Q;let D=this.underlyingCalls[A],Z=D.nextMessageToSend;(Q=(B=this.getBufferEntry(Z)).callback)===null||Q===void 0||Q.call(B),this.clearSentMessages(),D.nextMessageToSend+=1,this.sendNextChildMessage(A)}sendNextChildMessage(A){let B=this.underlyingCalls[A];if(B.state==="COMPLETED")return;if(this.getBufferEntry(B.nextMessageToSend)){let Q=this.getBufferEntry(B.nextMessageToSend);switch(Q.entryType){case"MESSAGE":B.call.sendMessageWithContext({callback:(D)=>{this.handleChildWriteCompleted(A)}},Q.message.message);break;case"HALF_CLOSE":B.nextMessageToSend+=1,B.call.halfClose();break;case"FREED":break}}}sendMessageWithContext(A,B){var Q;this.trace("write() called with message of length "+B.length);let D={message:B,flags:A.flags},Z=this.getNextBufferIndex(),G={entryType:"MESSAGE",message:D,allocated:this.bufferTracker.allocate(B.length,this.callNumber)};if(this.writeBuffer.push(G),G.allocated){(Q=A.callback)===null||Q===void 0||Q.call(A);for(let[F,I]of this.underlyingCalls.entries())if(I.state==="ACTIVE"&&I.nextMessageToSend===Z)I.call.sendMessageWithContext({callback:(Y)=>{this.handleChildWriteCompleted(F)}},B)}else{if(this.commitCallWithMostMessages(),this.committedCallIndex===null)return;let F=this.underlyingCalls[this.committedCallIndex];if(G.callback=A.callback,F.state==="ACTIVE"&&F.nextMessageToSend===Z)F.call.sendMessageWithContext({callback:(I)=>{this.handleChildWriteCompleted(this.committedCallIndex)}},B)}}startRead(){this.trace("startRead called"),this.readStarted=!0;for(let A of this.underlyingCalls)if((A===null||A===void 0?void 0:A.state)==="ACTIVE")A.call.startRead()}halfClose(){this.trace("halfClose called");let A=this.getNextBufferIndex();this.writeBuffer.push({entryType:"HALF_CLOSE",allocated:!1});for(let B of this.underlyingCalls)if((B===null||B===void 0?void 0:B.state)==="ACTIVE"&&B.nextMessageToSend===A)B.nextMessageToSend+=1,B.call.halfClose()}setCredentials(A){throw new Error("Method not implemented.")}getMethod(){return this.methodName}getHost(){return this.host}}Qr2.RetryingCall=Br2});
var _T1=E((m_5,Un2)=>{Un2.exports=bu;var uY0=vu();((bu.prototype=Object.create(uY0.prototype)).constructor=bu).className="Method";var Bt=KI();function bu(A,B,Q,D,Z,G,F,I,Y){if(Bt.isObject(Z))F=Z,Z=G=void 0;else if(Bt.isObject(G))F=G,G=void 0;if(!(B===void 0||Bt.isString(B)))throw TypeError("type must be a string");if(!Bt.isString(Q))throw TypeError("requestType must be a string");if(!Bt.isString(D))throw TypeError("responseType must be a string");uY0.call(this,A,F),this.type=B||"rpc",this.requestType=Q,this.requestStream=Z?!0:void 0,this.responseType=D,this.responseStream=G?!0:void 0,this.resolvedRequestType=null,this.resolvedResponseType=null,this.comment=I,this.parsedOptions=Y}bu.fromJSON=function A(B,Q){return new bu(B,Q.type,Q.requestType,Q.responseType,Q.requestStream,Q.responseStream,Q.options,Q.comment,Q.parsedOptions)};bu.prototype.toJSON=function A(B){var Q=B?Boolean(B.keepComments):!1;return Bt.toObject(["type",this.type!=="rpc"&&this.type||void 0,"requestType",this.requestType,"requestStream",this.requestStream,"responseType",this.responseType,"responseStream",this.responseStream,"options",this.options,"comment",Q?this.comment:void 0,"parsedOptions",this.parsedOptions])};bu.prototype.resolve=function A(){if(this.resolved)return this;return this.resolvedRequestType=this.parent.lookupType(this.requestType),this.resolvedResponseType=this.parent.lookupType(this.responseType),uY0.prototype.resolve.call(this)}});
var a31=E((ro2)=>{Object.defineProperty(ro2,"__esModule",{value:!0});ro2.createOtlpGrpcExporterTransport=ro2.GrpcExporterTransport=ro2.createEmptyMetadata=ro2.createSslCredentials=ro2.createInsecureCredentials=void 0;var aH6=0,sH6=2;function rH6(A){return A==="gzip"?sH6:aH6}function oH6(){let{credentials:A}=n31();return A.createInsecure()}ro2.createInsecureCredentials=oH6;function tH6(A,B,Q){let{credentials:D}=n31();return D.createSsl(A,B,Q)}ro2.createSslCredentials=tH6;function eH6(){let{Metadata:A}=n31();return new A}ro2.createEmptyMetadata=eH6;class PJ0{_parameters;_client;_metadata;constructor(A){this._parameters=A}shutdown(){this._client?.close()}send(A,B){let Q=Buffer.from(A);if(this._client==null){let{createServiceClientConstructor:D}=so2();try{this._metadata=this._parameters.metadata()}catch(G){return Promise.resolve({status:"failure",error:G})}let Z=D(this._parameters.grpcPath,this._parameters.grpcName);try{this._client=new Z(this._parameters.address,this._parameters.credentials(),{"grpc.default_compression_algorithm":rH6(this._parameters.compression)})}catch(G){return Promise.resolve({status:"failure",error:G})}}return new Promise((D)=>{let Z=Date.now()+B;if(this._metadata==null)return D({error:new Error("metadata was null"),status:"failure"});this._client.export(Q,this._metadata,{deadline:Z},(G,F)=>{if(G)D({status:"failure",error:G});else D({data:F,status:"success"})})})}}ro2.GrpcExporterTransport=PJ0;function Az6(A){return new PJ0(A)}ro2.createOtlpGrpcExporterTransport=Az6});
var aY0=E((On2)=>{var Rn2=On2,S31=sw(),rL=KI();function iY0(A,B,Q,D){var Z=!1;if(B.resolvedType)if(B.resolvedType instanceof S31){A("switch(d%s){",D);for(var G=B.resolvedType.values,F=Object.keys(G),I=0;I<F.length;++I){if(G[F[I]]===B.typeDefault&&!Z){if(A("default:")('if(typeof(d%s)==="number"){m%s=d%s;break}',D,D,D),!B.repeated)A("break");Z=!0}A("case%j:",F[I])("case %i:",G[F[I]])("m%s=%j",D,G[F[I]])("break")}A("}")}else A('if(typeof d%s!=="object")',D)("throw TypeError(%j)",B.fullName+": object expected")("m%s=types[%i].fromObject(d%s)",D,Q,D);else{var Y=!1;switch(B.type){case"double":case"float":A("m%s=Number(d%s)",D,D);break;case"uint32":case"fixed32":A("m%s=d%s>>>0",D,D);break;case"int32":case"sint32":case"sfixed32":A("m%s=d%s|0",D,D);break;case"uint64":Y=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":A("if(util.Long)")("(m%s=util.Long.fromValue(d%s)).unsigned=%j",D,D,Y)('else if(typeof d%s==="string")',D)("m%s=parseInt(d%s,10)",D,D)('else if(typeof d%s==="number")',D)("m%s=d%s",D,D)('else if(typeof d%s==="object")',D)("m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)",D,D,D,Y?"true":"");break;case"bytes":A('if(typeof d%s==="string")',D)("util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)",D,D,D)("else if(d%s.length >= 0)",D)("m%s=d%s",D,D);break;case"string":A("m%s=String(d%s)",D,D);break;case"bool":A("m%s=Boolean(d%s)",D,D);break}}return A}Rn2.fromObject=function A(B){var Q=B.fieldsArray,D=rL.codegen(["d"],B.name+"$fromObject")("if(d instanceof this.ctor)")("return d");if(!Q.length)return D("return new this.ctor");D("var m=new this.ctor");for(var Z=0;Z<Q.length;++Z){var G=Q[Z].resolve(),F=rL.safeProp(G.name);if(G.map)D("if(d%s){",F)('if(typeof d%s!=="object")',F)("throw TypeError(%j)",G.fullName+": object expected")("m%s={}",F)("for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){",F),iY0(D,G,Z,F+"[ks[i]]")("}")("}");else if(G.repeated)D("if(d%s){",F)("if(!Array.isArray(d%s))",F)("throw TypeError(%j)",G.fullName+": array expected")("m%s=[]",F)("for(var i=0;i<d%s.length;++i){",F),iY0(D,G,Z,F+"[i]")("}")("}");else{if(!(G.resolvedType instanceof S31))D("if(d%s!=null){",F);if(iY0(D,G,Z,F),!(G.resolvedType instanceof S31))D("}")}}return D("return m")};function nY0(A,B,Q,D){if(B.resolvedType)if(B.resolvedType instanceof S31)A("d%s=o.enums===String?(types[%i].values[m%s]===undefined?m%s:types[%i].values[m%s]):m%s",D,Q,D,D,Q,D,D);else A("d%s=types[%i].toObject(m%s,o)",D,Q,D);else{var Z=!1;switch(B.type){case"double":case"float":A("d%s=o.json&&!isFinite(m%s)?String(m%s):m%s",D,D,D,D);break;case"uint64":Z=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":A('if(typeof m%s==="number")',D)("d%s=o.longs===String?String(m%s):m%s",D,D,D)("else")("d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s",D,D,D,D,Z?"true":"",D);break;case"bytes":A("d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s",D,D,D,D,D);break;default:A("d%s=m%s",D,D);break}}return A}Rn2.toObject=function A(B){var Q=B.fieldsArray.slice().sort(rL.compareFieldsById);if(!Q.length)return rL.codegen()("return {}");var D=rL.codegen(["m","o"],B.name+"$toObject")("if(!o)")("o={}")("var d={}"),Z=[],G=[],F=[],I=0;for(;I<Q.length;++I)if(!Q[I].partOf)(Q[I].resolve().repeated?Z:Q[I].map?G:F).push(Q[I]);if(Z.length){D("if(o.arrays||o.defaults){");for(I=0;I<Z.length;++I)D("d%s=[]",rL.safeProp(Z[I].name));D("}")}if(G.length){D("if(o.objects||o.defaults){");for(I=0;I<G.length;++I)D("d%s={}",rL.safeProp(G[I].name));D("}")}if(F.length){D("if(o.defaults){");for(I=0;I<F.length;++I){var Y=F[I],W=rL.safeProp(Y.name);if(Y.resolvedType instanceof S31)D("d%s=o.enums===String?%j:%j",W,Y.resolvedType.valuesById[Y.typeDefault],Y.typeDefault);else if(Y.long)D("if(util.Long){")("var n=new util.Long(%i,%i,%j)",Y.typeDefault.low,Y.typeDefault.high,Y.typeDefault.unsigned)("d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n",W)("}else")("d%s=o.longs===String?%j:%i",W,Y.typeDefault.toString(),Y.typeDefault.toNumber());else if(Y.bytes){var J="["+Array.prototype.slice.call(Y.typeDefault).join(",")+"]";D("if(o.bytes===String)d%s=%j",W,String.fromCharCode.apply(String,Y.typeDefault))("else{")("d%s=%s",W,J)("if(o.bytes!==Array)d%s=util.newBuffer(d%s)",W,W)("}")}else D("d%s=%j",W,Y.typeDefault)}D("}")}var X=!1;for(I=0;I<Q.length;++I){var Y=Q[I],V=B._fieldsArray.indexOf(Y),W=rL.safeProp(Y.name);if(Y.map){if(!X)X=!0,D("var ks2");D("if(m%s&&(ks2=Object.keys(m%s)).length){",W,W)("d%s={}",W)("for(var j=0;j<ks2.length;++j){"),nY0(D,Y,V,W+"[ks2[j]]")("}")}else if(Y.repeated)D("if(m%s&&m%s.length){",W,W)("d%s=[]",W)("for(var j=0;j<m%s.length;++j){",W),nY0(D,Y,V,W+"[j]")("}");else if(D("if(m%s!=null&&m.hasOwnProperty(%j)){",W,Y.name),nY0(D,Y,V,W),Y.partOf)D("if(o.oneofs)")("d%s=%j",rL.safeProp(Y.partOf.name),Y.name);D("}")}return D("return d")}});
var bW0=E((Os2)=>{Object.defineProperty(Os2,"__esModule",{value:!0});Os2.CompressionAlgorithms=void 0;var Rs2;(function(A){A[A.identity=0]="identity",A[A.deflate=1]="deflate",A[A.gzip=2]="gzip"})(Rs2||(Os2.CompressionAlgorithms=Rs2={}))});
var dY0=E((l_5,Ln2)=>{Ln2.exports=CW6;var XW6=sw(),JP=xu(),Nn2=KI();function VW6(A){return"missing required '"+A.name+"'"}function CW6(A){var B=Nn2.codegen(["r","l"],A.name+"$decode")("if(!(r instanceof Reader))")("r=Reader.create(r)")("var c=l===undefined?r.len:r.pos+l,m=new this.ctor"+(A.fieldsArray.filter(function(I){return I.map}).length?",k,value":""))("while(r.pos<c){")("var t=r.uint32()");if(A.group)B("if((t&7)===4)")("break");B("switch(t>>>3){");var Q=0;for(;Q<A.fieldsArray.length;++Q){var D=A._fieldsArray[Q].resolve(),Z=D.resolvedType instanceof XW6?"int32":D.type,G="m"+Nn2.safeProp(D.name);if(B("case %i: {",D.id),D.map){if(B("if(%s===util.emptyObject)",G)("%s={}",G)("var c2 = r.uint32()+r.pos"),JP.defaults[D.keyType]!==void 0)B("k=%j",JP.defaults[D.keyType]);else B("k=null");if(JP.defaults[Z]!==void 0)B("value=%j",JP.defaults[Z]);else B("value=null");if(B("while(r.pos<c2){")("var tag2=r.uint32()")("switch(tag2>>>3){")("case 1: k=r.%s(); break",D.keyType)("case 2:"),JP.basic[Z]===void 0)B("value=types[%i].decode(r,r.uint32())",Q);else B("value=r.%s()",Z);if(B("break")("default:")("r.skipType(tag2&7)")("break")("}")("}"),JP.long[D.keyType]!==void 0)B('%s[typeof k==="object"?util.longToHash(k):k]=value',G);else B("%s[k]=value",G)}else if(D.repeated){if(B("if(!(%s&&%s.length))",G,G)("%s=[]",G),JP.packed[Z]!==void 0)B("if((t&7)===2){")("var c2=r.uint32()+r.pos")("while(r.pos<c2)")("%s.push(r.%s())",G,Z)("}else");if(JP.basic[Z]===void 0)B(D.resolvedType.group?"%s.push(types[%i].decode(r))":"%s.push(types[%i].decode(r,r.uint32()))",G,Q);else B("%s.push(r.%s())",G,Z)}else if(JP.basic[Z]===void 0)B(D.resolvedType.group?"%s=types[%i].decode(r)":"%s=types[%i].decode(r,r.uint32())",G,Q);else B("%s=r.%s()",G,Z);B("break")("}")}B("default:")("r.skipType(t&7)")("break")("}")("}");for(Q=0;Q<A._fieldsArray.length;++Q){var F=A._fieldsArray[Q];if(F.required)B("if(!m.hasOwnProperty(%j))",F.name)("throw util.ProtocolError(%j,{instance:m})",VW6(F))}return B("return m")}});
var do2=E((uo2)=>{var wJ0;Object.defineProperty(uo2,"__esModule",{value:!0});uo2.OutlierDetectionLoadBalancer=uo2.OutlierDetectionLoadBalancingConfig=void 0;uo2.setup=VH6;var QH6=VE(),vo2=S6(),cu=IJ0(),bo2=XJ0(),DH6=yu(),ZH6=qT1(),GH6=p_(),$J0=CE(),FH6=JP1(),IH6=D7(),YH6="outlier_detection";function HI(A){IH6.trace(vo2.LogVerbosity.DEBUG,YH6,A)}var LJ0="outlier_detection",WH6=((wJ0=process.env.GRPC_EXPERIMENTAL_ENABLE_OUTLIER_DETECTION)!==null&&wJ0!==void 0?wJ0:"true")==="true",JH6={stdev_factor:1900,enforcement_percentage:100,minimum_hosts:5,request_volume:100},XH6={threshold:85,enforcement_percentage:100,minimum_hosts:5,request_volume:50};function zt(A,B,Q,D){if(B in A&&A[B]!==void 0&&typeof A[B]!==Q){let Z=D?`${D}.${B}`:B;throw new Error(`outlier detection config ${Z} parse error: expected ${Q}, got ${typeof A[B]}`)}}function qJ0(A,B,Q){let D=Q?`${Q}.${B}`:B;if(B in A&&A[B]!==void 0){if(!cu.isDuration(A[B]))throw new Error(`outlier detection config ${D} parse error: expected Duration, got ${typeof A[B]}`);if(!(A[B].seconds>=0&&A[B].seconds<=315576000000&&A[B].nanos>=0&&A[B].nanos<=999999999))throw new Error(`outlier detection config ${D} parse error: values out of range for non-negative Duaration`)}}function LP1(A,B,Q){let D=Q?`${Q}.${B}`:B;if(zt(A,B,"number",Q),B in A&&A[B]!==void 0&&!(A[B]>=0&&A[B]<=100))throw new Error(`outlier detection config ${D} parse error: value out of range for percentage (0-100)`)}class i31{constructor(A,B,Q,D,Z,G,F){if(this.childPolicy=F,F.getLoadBalancerName()==="pick_first")throw new Error("outlier_detection LB policy cannot have a pick_first child policy");this.intervalMs=A!==null&&A!==void 0?A:1e4,this.baseEjectionTimeMs=B!==null&&B!==void 0?B:30000,this.maxEjectionTimeMs=Q!==null&&Q!==void 0?Q:300000,this.maxEjectionPercent=D!==null&&D!==void 0?D:10,this.successRateEjection=Z?Object.assign(Object.assign({},JH6),Z):null,this.failurePercentageEjection=G?Object.assign(Object.assign({},XH6),G):null}getLoadBalancerName(){return LJ0}toJsonObject(){var A,B;return{outlier_detection:{interval:cu.msToDuration(this.intervalMs),base_ejection_time:cu.msToDuration(this.baseEjectionTimeMs),max_ejection_time:cu.msToDuration(this.maxEjectionTimeMs),max_ejection_percent:this.maxEjectionPercent,success_rate_ejection:(A=this.successRateEjection)!==null&&A!==void 0?A:void 0,failure_percentage_ejection:(B=this.failurePercentageEjection)!==null&&B!==void 0?B:void 0,child_policy:[this.childPolicy.toJsonObject()]}}}getIntervalMs(){return this.intervalMs}getBaseEjectionTimeMs(){return this.baseEjectionTimeMs}getMaxEjectionTimeMs(){return this.maxEjectionTimeMs}getMaxEjectionPercent(){return this.maxEjectionPercent}getSuccessRateEjectionConfig(){return this.successRateEjection}getFailurePercentageEjectionConfig(){return this.failurePercentageEjection}getChildPolicy(){return this.childPolicy}static createFromJson(A){var B;if(qJ0(A,"interval"),qJ0(A,"base_ejection_time"),qJ0(A,"max_ejection_time"),LP1(A,"max_ejection_percent"),"success_rate_ejection"in A&&A.success_rate_ejection!==void 0){if(typeof A.success_rate_ejection!=="object")throw new Error("outlier detection config success_rate_ejection must be an object");zt(A.success_rate_ejection,"stdev_factor","number","success_rate_ejection"),LP1(A.success_rate_ejection,"enforcement_percentage","success_rate_ejection"),zt(A.success_rate_ejection,"minimum_hosts","number","success_rate_ejection"),zt(A.success_rate_ejection,"request_volume","number","success_rate_ejection")}if("failure_percentage_ejection"in A&&A.failure_percentage_ejection!==void 0){if(typeof A.failure_percentage_ejection!=="object")throw new Error("outlier detection config failure_percentage_ejection must be an object");LP1(A.failure_percentage_ejection,"threshold","failure_percentage_ejection"),LP1(A.failure_percentage_ejection,"enforcement_percentage","failure_percentage_ejection"),zt(A.failure_percentage_ejection,"minimum_hosts","number","failure_percentage_ejection"),zt(A.failure_percentage_ejection,"request_volume","number","failure_percentage_ejection")}if(!("child_policy"in A)||!Array.isArray(A.child_policy))throw new Error("outlier detection config child_policy must be an array");let Q=DH6.selectLbConfigFromList(A.child_policy);if(!Q)throw new Error("outlier detection config child_policy: no valid recognized policy found");return new i31(A.interval?cu.durationToMs(A.interval):null,A.base_ejection_time?cu.durationToMs(A.base_ejection_time):null,A.max_ejection_time?cu.durationToMs(A.max_ejection_time):null,(B=A.max_ejection_percent)!==null&&B!==void 0?B:null,A.success_rate_ejection,A.failure_percentage_ejection,Q)}}uo2.OutlierDetectionLoadBalancingConfig=i31;class fo2 extends FH6.BaseSubchannelWrapper{constructor(A,B){super(A);this.mapEntry=B,this.refCount=0}ref(){this.child.ref(),this.refCount+=1}unref(){if(this.child.unref(),this.refCount-=1,this.refCount<=0){if(this.mapEntry){let A=this.mapEntry.subchannelWrappers.indexOf(this);if(A>=0)this.mapEntry.subchannelWrappers.splice(A,1)}}}eject(){this.setHealthy(!1)}uneject(){this.setHealthy(!0)}getMapEntry(){return this.mapEntry}getWrappedSubchannel(){return this.child}}function NJ0(){return{success:0,failure:0}}class ho2{constructor(){this.activeBucket=NJ0(),this.inactiveBucket=NJ0()}addSuccess(){this.activeBucket.success+=1}addFailure(){this.activeBucket.failure+=1}switchBuckets(){this.inactiveBucket=this.activeBucket,this.activeBucket=NJ0()}getLastSuccesses(){return this.inactiveBucket.success}getLastFailures(){return this.inactiveBucket.failure}}class go2{constructor(A,B){this.wrappedPicker=A,this.countCalls=B}pick(A){let B=this.wrappedPicker.pick(A);if(B.pickResultType===GH6.PickResultType.COMPLETE){let Q=B.subchannel,D=Q.getMapEntry();if(D){let Z=B.onCallEnded;if(this.countCalls)Z=(G)=>{var F;if(G===vo2.Status.OK)D.counter.addSuccess();else D.counter.addFailure();(F=B.onCallEnded)===null||F===void 0||F.call(B,G)};return Object.assign(Object.assign({},B),{subchannel:Q.getWrappedSubchannel(),onCallEnded:Z})}else return Object.assign(Object.assign({},B),{subchannel:Q.getWrappedSubchannel()})}else return B}}class MJ0{constructor(A){this.entryMap=new $J0.EndpointMap,this.latestConfig=null,this.timerStartTime=null,this.childBalancer=new ZH6.ChildLoadBalancerHandler(bo2.createChildChannelControlHelper(A,{createSubchannel:(B,Q)=>{let D=A.createSubchannel(B,Q),Z=this.entryMap.getForSubchannelAddress(B),G=new fo2(D,Z);if((Z===null||Z===void 0?void 0:Z.currentEjectionTimestamp)!==null)G.eject();return Z===null||Z===void 0||Z.subchannelWrappers.push(G),G},updateState:(B,Q,D)=>{if(B===QH6.ConnectivityState.READY)A.updateState(B,new go2(Q,this.isCountingEnabled()),D);else A.updateState(B,Q,D)}})),this.ejectionTimer=setInterval(()=>{},0),clearInterval(this.ejectionTimer)}isCountingEnabled(){return this.latestConfig!==null&&(this.latestConfig.getSuccessRateEjectionConfig()!==null||this.latestConfig.getFailurePercentageEjectionConfig()!==null)}getCurrentEjectionPercent(){let A=0;for(let B of this.entryMap.values())if(B.currentEjectionTimestamp!==null)A+=1;return A*100/this.entryMap.size}runSuccessRateCheck(A){if(!this.latestConfig)return;let B=this.latestConfig.getSuccessRateEjectionConfig();if(!B)return;HI("Running success rate check");let Q=B.request_volume,D=0,Z=[];for(let[J,X]of this.entryMap.entries()){let V=X.counter.getLastSuccesses(),C=X.counter.getLastFailures();if(HI("Stats for "+$J0.endpointToString(J)+": successes="+V+" failures="+C+" targetRequestVolume="+Q),V+C>=Q)D+=1,Z.push(V/(V+C))}if(HI("Found "+D+" success rate candidates; currentEjectionPercent="+this.getCurrentEjectionPercent()+" successRates=["+Z+"]"),D<B.minimum_hosts)return;let G=Z.reduce((J,X)=>J+X)/Z.length,F=0;for(let J of Z){let X=J-G;F+=X*X}let I=F/Z.length,Y=Math.sqrt(I),W=G-Y*(B.stdev_factor/1000);HI("stdev="+Y+" ejectionThreshold="+W);for(let[J,X]of this.entryMap.entries()){if(this.getCurrentEjectionPercent()>=this.latestConfig.getMaxEjectionPercent())break;let V=X.counter.getLastSuccesses(),C=X.counter.getLastFailures();if(V+C<Q)continue;let K=V/(V+C);if(HI("Checking candidate "+J+" successRate="+K),K<W){let H=Math.random()*100;if(HI("Candidate "+J+" randomNumber="+H+" enforcement_percentage="+B.enforcement_percentage),H<B.enforcement_percentage)HI("Ejecting candidate "+J),this.eject(X,A)}}}runFailurePercentageCheck(A){if(!this.latestConfig)return;let B=this.latestConfig.getFailurePercentageEjectionConfig();if(!B)return;HI("Running failure percentage check. threshold="+B.threshold+" request volume threshold="+B.request_volume);let Q=0;for(let D of this.entryMap.values()){let Z=D.counter.getLastSuccesses(),G=D.counter.getLastFailures();if(Z+G>=B.request_volume)Q+=1}if(Q<B.minimum_hosts)return;for(let[D,Z]of this.entryMap.entries()){if(this.getCurrentEjectionPercent()>=this.latestConfig.getMaxEjectionPercent())break;let G=Z.counter.getLastSuccesses(),F=Z.counter.getLastFailures();if(HI("Candidate successes="+G+" failures="+F),G+F<B.request_volume)continue;if(F*100/(F+G)>B.threshold){let Y=Math.random()*100;if(HI("Candidate "+D+" randomNumber="+Y+" enforcement_percentage="+B.enforcement_percentage),Y<B.enforcement_percentage)HI("Ejecting candidate "+D),this.eject(Z,A)}}}eject(A,B){A.currentEjectionTimestamp=new Date,A.ejectionTimeMultiplier+=1;for(let Q of A.subchannelWrappers)Q.eject()}uneject(A){A.currentEjectionTimestamp=null;for(let B of A.subchannelWrappers)B.uneject()}switchAllBuckets(){for(let A of this.entryMap.values())A.counter.switchBuckets()}startTimer(A){var B,Q;this.ejectionTimer=setTimeout(()=>this.runChecks(),A),(Q=(B=this.ejectionTimer).unref)===null||Q===void 0||Q.call(B)}runChecks(){let A=new Date;if(HI("Ejection timer running"),this.switchAllBuckets(),!this.latestConfig)return;this.timerStartTime=A,this.startTimer(this.latestConfig.getIntervalMs()),this.runSuccessRateCheck(A),this.runFailurePercentageCheck(A);for(let[B,Q]of this.entryMap.entries())if(Q.currentEjectionTimestamp===null){if(Q.ejectionTimeMultiplier>0)Q.ejectionTimeMultiplier-=1}else{let D=this.latestConfig.getBaseEjectionTimeMs(),Z=this.latestConfig.getMaxEjectionTimeMs(),G=new Date(Q.currentEjectionTimestamp.getTime());if(G.setMilliseconds(G.getMilliseconds()+Math.min(D*Q.ejectionTimeMultiplier,Math.max(D,Z))),G<new Date)HI("Unejecting "+B),this.uneject(Q)}}updateAddressList(A,B,Q){if(!(B instanceof i31))return;HI("Received update with config: "+JSON.stringify(B.toJsonObject(),void 0,2));for(let Z of A)if(!this.entryMap.has(Z))HI("Adding map entry for "+$J0.endpointToString(Z)),this.entryMap.set(Z,{counter:new ho2,currentEjectionTimestamp:null,ejectionTimeMultiplier:0,subchannelWrappers:[]});this.entryMap.deleteMissing(A);let D=B.getChildPolicy();if(this.childBalancer.updateAddressList(A,D,Q),B.getSuccessRateEjectionConfig()||B.getFailurePercentageEjectionConfig())if(this.timerStartTime){HI("Previous timer existed. Replacing timer"),clearTimeout(this.ejectionTimer);let Z=B.getIntervalMs()-(new Date().getTime()-this.timerStartTime.getTime());this.startTimer(Z)}else HI("Starting new timer"),this.timerStartTime=new Date,this.startTimer(B.getIntervalMs()),this.switchAllBuckets();else{HI("Counting disabled. Cancelling timer."),this.timerStartTime=null,clearTimeout(this.ejectionTimer);for(let Z of this.entryMap.values())this.uneject(Z),Z.ejectionTimeMultiplier=0}this.latestConfig=B}exitIdle(){this.childBalancer.exitIdle()}resetBackoff(){this.childBalancer.resetBackoff()}destroy(){clearTimeout(this.ejectionTimer),this.childBalancer.destroy()}getTypeName(){return LJ0}}uo2.OutlierDetectionLoadBalancer=MJ0;function VH6(){if(WH6)bo2.registerLoadBalancerType(LJ0,MJ0,i31)}});
var eY0=E((e_5,dn2)=>{dn2.exports=_W6;var yW6=sw(),ZW0=xu(),GW0=KI();function mn2(A,B,Q,D){return B.resolvedType.group?A("types[%i].encode(%s,w.uint32(%i)).uint32(%i)",Q,D,(B.id<<3|3)>>>0,(B.id<<3|4)>>>0):A("types[%i].encode(%s,w.uint32(%i).fork()).ldelim()",Q,D,(B.id<<3|2)>>>0)}function _W6(A){var B=GW0.codegen(["m","w"],A.name+"$encode")("if(!w)")("w=Writer.create()"),Q,D,Z=A.fieldsArray.slice().sort(GW0.compareFieldsById);for(var Q=0;Q<Z.length;++Q){var G=Z[Q].resolve(),F=A._fieldsArray.indexOf(G),I=G.resolvedType instanceof yW6?"int32":G.type,Y=ZW0.basic[I];if(D="m"+GW0.safeProp(G.name),G.map){if(B("if(%s!=null&&Object.hasOwnProperty.call(m,%j)){",D,G.name)("for(var ks=Object.keys(%s),i=0;i<ks.length;++i){",D)("w.uint32(%i).fork().uint32(%i).%s(ks[i])",(G.id<<3|2)>>>0,8|ZW0.mapKey[G.keyType],G.keyType),Y===void 0)B("types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()",F,D);else B(".uint32(%i).%s(%s[ks[i]]).ldelim()",16|Y,I,D);B("}")("}")}else if(G.repeated){if(B("if(%s!=null&&%s.length){",D,D),G.packed&&ZW0.packed[I]!==void 0)B("w.uint32(%i).fork()",(G.id<<3|2)>>>0)("for(var i=0;i<%s.length;++i)",D)("w.%s(%s[i])",I,D)("w.ldelim()");else if(B("for(var i=0;i<%s.length;++i)",D),Y===void 0)mn2(B,G,F,D+"[i]");else B("w.uint32(%i).%s(%s[i])",(G.id<<3|Y)>>>0,I,D);B("}")}else{if(G.optional)B("if(%s!=null&&Object.hasOwnProperty.call(m,%j))",D,G.name);if(Y===void 0)mn2(B,G,F,D);else B("w.uint32(%i).%s(%s)",(G.id<<3|Y)>>>0,I,D)}}return B("return w")}});
var en2=E((Qx5,tn2)=>{tn2.exports=XP;XP.filename=null;XP.defaults={keepCase:!1};var lW6=IW0(),an2=mT1(),sn2=hT1(),rn2=n_(),pW6=yT1(),on2=oo(),iW6=sw(),nW6=xT1(),aW6=_T1(),YW0=xu(),WW0=KI(),sW6=/^[1-9][0-9]*$/,rW6=/^-?[1-9][0-9]*$/,oW6=/^0[x][0-9a-fA-F]+$/,tW6=/^-?0[x][0-9a-fA-F]+$/,eW6=/^0[0-7]+$/,AJ6=/^-?0[0-7]+$/,BJ6=/^(?![eE])[0-9]*(?:\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,tL=/^[a-zA-Z_][a-zA-Z_0-9]*$/,eL=/^(?:\.?[a-zA-Z_][a-zA-Z_0-9]*)(?:\.[a-zA-Z_][a-zA-Z_0-9]*)*$/,QJ6=/^(?:\.[a-zA-Z_][a-zA-Z_0-9]*)+$/;function XP(A,B,Q){if(!(B instanceof an2))Q=B,B=new an2;if(!Q)Q=XP.defaults;var D=Q.preferTrailingComment||!1,Z=lW6(A,Q.alternateCommentMode||!1),G=Z.next,F=Z.push,I=Z.peek,Y=Z.skip,W=Z.cmnt,J=!0,X,V,C,K,H=!1,z=B,$=Q.keepCase?function(B1){return B1}:WW0.camelCase;function L(B1,A1,I1){var q1=XP.filename;if(!I1)XP.filename=null;return Error("illegal "+(A1||"token")+" '"+B1+"' ("+(q1?q1+", ":"")+"line "+Z.line+")")}function N(){var B1=[],A1;do{if((A1=G())!=='"'&&A1!=="'")throw L(A1);B1.push(G()),Y(A1),A1=I()}while(A1==='"'||A1==="'");return B1.join("")}function O(B1){var A1=G();switch(A1){case"'":case'"':return F(A1),N();case"true":case"TRUE":return!0;case"false":case"FALSE":return!1}try{return T(A1,!0)}catch(I1){if(B1&&eL.test(A1))return A1;throw L(A1,"value")}}function R(B1,A1){var I1,q1;do if(A1&&((I1=I())==='"'||I1==="'"))B1.push(N());else B1.push([q1=j(G()),Y("to",!0)?j(G()):q1]);while(Y(",",!0));var P1={options:void 0};P1.setOption=function(Q1,f1){if(this.options===void 0)this.options={};this.options[Q1]=f1},n(P1,function Q1(f1){if(f1==="option")F0(P1,f1),Y(";");else throw L(f1)},function Q1(){L1(P1)})}function T(B1,A1){var I1=1;if(B1.charAt(0)==="-")I1=-1,B1=B1.substring(1);switch(B1){case"inf":case"INF":case"Inf":return I1*(1/0);case"nan":case"NAN":case"Nan":case"NaN":return NaN;case"0":return 0}if(sW6.test(B1))return I1*parseInt(B1,10);if(oW6.test(B1))return I1*parseInt(B1,16);if(eW6.test(B1))return I1*parseInt(B1,8);if(BJ6.test(B1))return I1*parseFloat(B1);throw L(B1,"number",A1)}function j(B1,A1){switch(B1){case"max":case"MAX":case"Max":return 536870911;case"0":return 0}if(!A1&&B1.charAt(0)==="-")throw L(B1,"id");if(rW6.test(B1))return parseInt(B1,10);if(tW6.test(B1))return parseInt(B1,16);if(AJ6.test(B1))return parseInt(B1,8);throw L(B1,"id")}function f(){if(X!==void 0)throw L("package");if(X=G(),!eL.test(X))throw L(X,"name");z=z.define(X),Y(";")}function k(){var B1=I(),A1;switch(B1){case"weak":A1=C||(C=[]),G();break;case"public":G();default:A1=V||(V=[]);break}B1=N(),Y(";"),A1.push(B1)}function c(){if(Y("="),K=N(),H=K==="proto3",!H&&K!=="proto2")throw L(K,"syntax");B.setOption("syntax",K),Y(";")}function h(B1,A1){switch(A1){case"option":return F0(B1,A1),Y(";"),!0;case"message":return a(B1,A1),!0;case"enum":return y1(B1,A1),!0;case"service":return M1(B1,A1),!0;case"extend":return i1(B1,A1),!0}return!1}function n(B1,A1,I1){var q1=Z.line;if(B1){if(typeof B1.comment!=="string")B1.comment=W();B1.filename=XP.filename}if(Y("{",!0)){var P1;while((P1=G())!=="}")A1(P1);Y(";",!0)}else{if(I1)I1();if(Y(";"),B1&&(typeof B1.comment!=="string"||D))B1.comment=W(q1)||B1.comment}}function a(B1,A1){if(!tL.test(A1=G()))throw L(A1,"type name");var I1=new sn2(A1);n(I1,function q1(P1){if(h(I1,P1))return;switch(P1){case"map":W1(I1,P1);break;case"required":case"repeated":x(I1,P1);break;case"optional":if(H)x(I1,"proto3_optional");else x(I1,"optional");break;case"oneof":U1(I1,P1);break;case"extensions":R(I1.extensions||(I1.extensions=[]));break;case"reserved":R(I1.reserved||(I1.reserved=[]),!0);break;default:if(!H||!eL.test(P1))throw L(P1);F(P1),x(I1,"optional");break}}),B1.add(I1)}function x(B1,A1,I1){var q1=G();if(q1==="group"){e(B1,A1);return}while(q1.endsWith(".")||I().startsWith("."))q1+=G();if(!eL.test(q1))throw L(q1,"type");var P1=G();if(!tL.test(P1))throw L(P1,"name");P1=$(P1),Y("=");var Q1=new rn2(P1,j(G()),q1,A1,I1);if(n(Q1,function l1(n1){if(n1==="option")F0(Q1,n1),Y(";");else throw L(n1)},function l1(){L1(Q1)}),A1==="proto3_optional"){var f1=new on2("_"+P1);Q1.setOption("proto3_optional",!0),f1.add(Q1),B1.add(f1)}else B1.add(Q1);if(!H&&Q1.repeated&&(YW0.packed[q1]!==void 0||YW0.basic[q1]===void 0))Q1.setOption("packed",!1,!0)}function e(B1,A1){var I1=G();if(!tL.test(I1))throw L(I1,"name");var q1=WW0.lcFirst(I1);if(I1===q1)I1=WW0.ucFirst(I1);Y("=");var P1=j(G()),Q1=new sn2(I1);Q1.group=!0;var f1=new rn2(q1,P1,I1,A1);f1.filename=XP.filename,n(Q1,function l1(n1){switch(n1){case"option":F0(Q1,n1),Y(";");break;case"required":case"repeated":x(Q1,n1);break;case"optional":if(H)x(Q1,"proto3_optional");else x(Q1,"optional");break;case"message":a(Q1,n1);break;case"enum":y1(Q1,n1);break;default:throw L(n1)}}),B1.add(Q1).add(f1)}function W1(B1){Y("<");var A1=G();if(YW0.mapKey[A1]===void 0)throw L(A1,"type");Y(",");var I1=G();if(!eL.test(I1))throw L(I1,"type");Y(">");var q1=G();if(!tL.test(q1))throw L(q1,"name");Y("=");var P1=new pW6($(q1),j(G()),A1,I1);n(P1,function Q1(f1){if(f1==="option")F0(P1,f1),Y(";");else throw L(f1)},function Q1(){L1(P1)}),B1.add(P1)}function U1(B1,A1){if(!tL.test(A1=G()))throw L(A1,"name");var I1=new on2($(A1));n(I1,function q1(P1){if(P1==="option")F0(I1,P1),Y(";");else F(P1),x(I1,"optional")}),B1.add(I1)}function y1(B1,A1){if(!tL.test(A1=G()))throw L(A1,"name");var I1=new iW6(A1);n(I1,function q1(P1){switch(P1){case"option":F0(I1,P1),Y(";");break;case"reserved":R(I1.reserved||(I1.reserved=[]),!0);break;default:W0(I1,P1)}}),B1.add(I1)}function W0(B1,A1){if(!tL.test(A1))throw L(A1,"name");Y("=");var I1=j(G(),!0),q1={options:void 0};q1.setOption=function(P1,Q1){if(this.options===void 0)this.options={};this.options[P1]=Q1},n(q1,function P1(Q1){if(Q1==="option")F0(q1,Q1),Y(";");else throw L(Q1)},function P1(){L1(q1)}),B1.add(A1,I1,q1.comment,q1.options)}function F0(B1,A1){var I1=Y("(",!0);if(!eL.test(A1=G()))throw L(A1,"name");var q1=A1,P1=q1,Q1;if(I1){if(Y(")"),q1="("+q1+")",P1=q1,A1=I(),QJ6.test(A1))Q1=A1.slice(1),q1+=A1,G()}Y("=");var f1=g1(B1,q1);G1(B1,P1,f1,Q1)}function g1(B1,A1){if(Y("{",!0)){var I1={};while(!Y("}",!0)){if(!tL.test(E0=G()))throw L(E0,"name");if(E0===null)throw L(E0,"end of input");var q1,P1=E0;if(Y(":",!0),I()==="{")q1=g1(B1,A1+"."+E0);else if(I()==="["){q1=[];var Q1;if(Y("[",!0)){do Q1=O(!0),q1.push(Q1);while(Y(",",!0));if(Y("]"),typeof Q1!=="undefined")K1(B1,A1+"."+E0,Q1)}}else q1=O(!0),K1(B1,A1+"."+E0,q1);var f1=I1[P1];if(f1)q1=[].concat(f1).concat(q1);I1[P1]=q1,Y(",",!0),Y(";",!0)}return I1}var l1=O(!0);return K1(B1,A1,l1),l1}function K1(B1,A1,I1){if(B1.setOption)B1.setOption(A1,I1)}function G1(B1,A1,I1,q1){if(B1.setParsedOption)B1.setParsedOption(A1,I1,q1)}function L1(B1){if(Y("[",!0)){do F0(B1,"option");while(Y(",",!0));Y("]")}return B1}function M1(B1,A1){if(!tL.test(A1=G()))throw L(A1,"service name");var I1=new nW6(A1);n(I1,function q1(P1){if(h(I1,P1))return;if(P1==="rpc")a1(I1,P1);else throw L(P1)}),B1.add(I1)}function a1(B1,A1){var I1=W(),q1=A1;if(!tL.test(A1=G()))throw L(A1,"name");var P1=A1,Q1,f1,l1,n1;if(Y("("),Y("stream",!0))f1=!0;if(!eL.test(A1=G()))throw L(A1);if(Q1=A1,Y(")"),Y("returns"),Y("("),Y("stream",!0))n1=!0;if(!eL.test(A1=G()))throw L(A1);l1=A1,Y(")");var V0=new aW6(P1,q1,Q1,l1,f1,n1);V0.comment=I1,n(V0,function I0(M0){if(M0==="option")F0(V0,M0),Y(";");else throw L(M0)}),B1.add(V0)}function i1(B1,A1){if(!eL.test(A1=G()))throw L(A1,"reference");var I1=A1;n(null,function q1(P1){switch(P1){case"required":case"repeated":x(B1,P1,I1);break;case"optional":if(H)x(B1,"proto3_optional",I1);else x(B1,"optional",I1);break;default:if(!H||!eL.test(P1))throw L(P1);F(P1),x(B1,"optional",I1);break}})}var E0;while((E0=G())!==null)switch(E0){case"package":if(!J)throw L(E0);f();break;case"import":if(!J)throw L(E0);k();break;case"syntax":if(!J)throw L(E0);c();break;case"option":F0(z,E0),Y(";");break;default:if(h(z,E0)){J=!1;continue}throw L(E0)}return XP.filename=null,{package:X,imports:V,weakImports:C,syntax:K,root:B}}});
var ep2=E((tp2)=>{Object.defineProperty(tp2,"t",{value:!0});class wY0{constructor(A,B,Q=1){this.i=void 0,this.h=void 0,this.o=void 0,this.u=A,this.l=B,this.p=Q}I(){let A=this,B=A.o.o===A;if(B&&A.p===1)A=A.h;else if(A.i){A=A.i;while(A.h)A=A.h}else{if(B)return A.o;let Q=A.o;while(Q.i===A)A=Q,Q=A.o;A=Q}return A}B(){let A=this;if(A.h){A=A.h;while(A.i)A=A.i;return A}else{let B=A.o;while(B.h===A)A=B,B=A.o;if(A.h!==B)return B;else return A}}_(){let A=this.o,B=this.h,Q=B.i;if(A.o===this)A.o=B;else if(A.i===this)A.i=B;else A.h=B;if(B.o=A,B.i=this,this.o=B,this.h=Q,Q)Q.o=this;return B}g(){let A=this.o,B=this.i,Q=B.h;if(A.o===this)A.o=B;else if(A.i===this)A.i=B;else A.h=B;if(B.o=A,B.h=this,this.o=B,this.i=Q,Q)Q.o=this;return B}}class pp2 extends wY0{constructor(){super(...arguments);this.M=1}_(){let A=super._();return this.O(),A.O(),A}g(){let A=super.g();return this.O(),A.O(),A}O(){if(this.M=1,this.i)this.M+=this.i.M;if(this.h)this.M+=this.h.M}}class ip2{constructor(A=0){this.iteratorType=A}equals(A){return this.T===A.T}}class np2{constructor(){this.m=0}get length(){return this.m}size(){return this.m}empty(){return this.m===0}}class ap2 extends np2{}function _u(){throw new RangeError("Iterator access denied!")}class sp2 extends ap2{constructor(A=function(Q,D){if(Q<D)return-1;if(Q>D)return 1;return 0},B=!1){super();this.v=void 0,this.A=A,this.enableIndex=B,this.N=B?pp2:wY0,this.C=new this.N}R(A,B){let Q=this.C;while(A){let D=this.A(A.u,B);if(D<0)A=A.h;else if(D>0)Q=A,A=A.i;else return A}return Q}K(A,B){let Q=this.C;while(A)if(this.A(A.u,B)<=0)A=A.h;else Q=A,A=A.i;return Q}L(A,B){let Q=this.C;while(A){let D=this.A(A.u,B);if(D<0)Q=A,A=A.h;else if(D>0)A=A.i;else return A}return Q}k(A,B){let Q=this.C;while(A)if(this.A(A.u,B)<0)Q=A,A=A.h;else A=A.i;return Q}P(A){while(!0){let B=A.o;if(B===this.C)return;if(A.p===1){A.p=0;return}if(A===B.i){let Q=B.h;if(Q.p===1)if(Q.p=0,B.p=1,B===this.v)this.v=B._();else B._();else if(Q.h&&Q.h.p===1){if(Q.p=B.p,B.p=0,Q.h.p=0,B===this.v)this.v=B._();else B._();return}else if(Q.i&&Q.i.p===1)Q.p=1,Q.i.p=0,Q.g();else Q.p=1,A=B}else{let Q=B.i;if(Q.p===1)if(Q.p=0,B.p=1,B===this.v)this.v=B.g();else B.g();else if(Q.i&&Q.i.p===1){if(Q.p=B.p,B.p=0,Q.i.p=0,B===this.v)this.v=B.g();else B.g();return}else if(Q.h&&Q.h.p===1)Q.p=1,Q.h.p=0,Q._();else Q.p=1,A=B}}}S(A){if(this.m===1){this.clear();return}let B=A;while(B.i||B.h){if(B.h){B=B.h;while(B.i)B=B.i}else B=B.i;let D=A.u;A.u=B.u,B.u=D;let Z=A.l;A.l=B.l,B.l=Z,A=B}if(this.C.i===B)this.C.i=B.o;else if(this.C.h===B)this.C.h=B.o;this.P(B);let Q=B.o;if(B===Q.i)Q.i=void 0;else Q.h=void 0;if(this.m-=1,this.v.p=0,this.enableIndex)while(Q!==this.C)Q.M-=1,Q=Q.o}U(A){let B=typeof A==="number"?A:void 0,Q=typeof A==="function"?A:void 0,D=typeof A==="undefined"?[]:void 0,Z=0,G=this.v,F=[];while(F.length||G)if(G)F.push(G),G=G.i;else{if(G=F.pop(),Z===B)return G;D&&D.push(G),Q&&Q(G,Z,this),Z+=1,G=G.h}return D}j(A){while(!0){let B=A.o;if(B.p===0)return;let Q=B.o;if(B===Q.i){let D=Q.h;if(D&&D.p===1){if(D.p=B.p=0,Q===this.v)return;Q.p=1,A=Q;continue}else if(A===B.h){if(A.p=0,A.i)A.i.o=B;if(A.h)A.h.o=Q;if(B.h=A.i,Q.i=A.h,A.i=B,A.h=Q,Q===this.v)this.v=A,this.C.o=A;else{let Z=Q.o;if(Z.i===Q)Z.i=A;else Z.h=A}A.o=Q.o,B.o=A,Q.o=A,Q.p=1}else{if(B.p=0,Q===this.v)this.v=Q.g();else Q.g();Q.p=1;return}}else{let D=Q.i;if(D&&D.p===1){if(D.p=B.p=0,Q===this.v)return;Q.p=1,A=Q;continue}else if(A===B.i){if(A.p=0,A.i)A.i.o=Q;if(A.h)A.h.o=B;if(Q.h=A.i,B.i=A.h,A.i=Q,A.h=B,Q===this.v)this.v=A,this.C.o=A;else{let Z=Q.o;if(Z.i===Q)Z.i=A;else Z.h=A}A.o=Q.o,B.o=A,Q.o=A,Q.p=1}else{if(B.p=0,Q===this.v)this.v=Q._();else Q._();Q.p=1;return}}if(this.enableIndex)B.O(),Q.O(),A.O();return}}q(A,B,Q){if(this.v===void 0)return this.m+=1,this.v=new this.N(A,B,0),this.v.o=this.C,this.C.o=this.C.i=this.C.h=this.v,this.m;let D,Z=this.C.i,G=this.A(Z.u,A);if(G===0)return Z.l=B,this.m;else if(G>0)Z.i=new this.N(A,B),Z.i.o=Z,D=Z.i,this.C.i=D;else{let F=this.C.h,I=this.A(F.u,A);if(I===0)return F.l=B,this.m;else if(I<0)F.h=new this.N(A,B),F.h.o=F,D=F.h,this.C.h=D;else{if(Q!==void 0){let Y=Q.T;if(Y!==this.C){let W=this.A(Y.u,A);if(W===0)return Y.l=B,this.m;else if(W>0){let J=Y.I(),X=this.A(J.u,A);if(X===0)return J.l=B,this.m;else if(X<0)if(D=new this.N(A,B),J.h===void 0)J.h=D,D.o=J;else Y.i=D,D.o=Y}}}if(D===void 0){D=this.v;while(!0){let Y=this.A(D.u,A);if(Y>0){if(D.i===void 0){D.i=new this.N(A,B),D.i.o=D,D=D.i;break}D=D.i}else if(Y<0){if(D.h===void 0){D.h=new this.N(A,B),D.h.o=D,D=D.h;break}D=D.h}else return D.l=B,this.m}}}}if(this.enableIndex){let F=D.o;while(F!==this.C)F.M+=1,F=F.o}return this.j(D),this.m+=1,this.m}H(A,B){while(A){let Q=this.A(A.u,B);if(Q<0)A=A.h;else if(Q>0)A=A.i;else return A}return A||this.C}clear(){this.m=0,this.v=void 0,this.C.o=void 0,this.C.i=this.C.h=void 0}updateKeyByIterator(A,B){let Q=A.T;if(Q===this.C)_u();if(this.m===1)return Q.u=B,!0;let D=Q.B().u;if(Q===this.C.i){if(this.A(D,B)>0)return Q.u=B,!0;return!1}let Z=Q.I().u;if(Q===this.C.h){if(this.A(Z,B)<0)return Q.u=B,!0;return!1}if(this.A(Z,B)>=0||this.A(D,B)<=0)return!1;return Q.u=B,!0}eraseElementByPos(A){if(A<0||A>this.m-1)throw new RangeError;let B=this.U(A);return this.S(B),this.m}eraseElementByKey(A){if(this.m===0)return!1;let B=this.H(this.v,A);if(B===this.C)return!1;return this.S(B),!0}eraseElementByIterator(A){let B=A.T;if(B===this.C)_u();let Q=B.h===void 0;if(A.iteratorType===0){if(Q)A.next()}else if(!Q||B.i===void 0)A.next();return this.S(B),A}getHeight(){if(this.m===0)return 0;function A(B){if(!B)return 0;return Math.max(A(B.i),A(B.h))+1}return A(this.v)}}class rp2 extends ip2{constructor(A,B,Q){super(Q);if(this.T=A,this.C=B,this.iteratorType===0)this.pre=function(){if(this.T===this.C.i)_u();return this.T=this.T.I(),this},this.next=function(){if(this.T===this.C)_u();return this.T=this.T.B(),this};else this.pre=function(){if(this.T===this.C.h)_u();return this.T=this.T.B(),this},this.next=function(){if(this.T===this.C)_u();return this.T=this.T.I(),this}}get index(){let A=this.T,B=this.C.o;if(A===this.C){if(B)return B.M-1;return 0}let Q=0;if(A.i)Q+=A.i.M;while(A!==B){let D=A.o;if(A===D.h){if(Q+=1,D.i)Q+=D.i.M}A=D}return Q}isAccessible(){return this.T!==this.C}}class nw extends rp2{constructor(A,B,Q,D){super(A,B,D);this.container=Q}get pointer(){if(this.T===this.C)_u();let A=this;return new Proxy([],{get(B,Q){if(Q==="0")return A.T.u;else if(Q==="1")return A.T.l;return B[0]=A.T.u,B[1]=A.T.l,B[Q]},set(B,Q,D){if(Q!=="1")throw new TypeError("prop must be 1");return A.T.l=D,!0}})}copy(){return new nw(this.T,this.C,this.container,this.iteratorType)}}class op2 extends sp2{constructor(A=[],B,Q){super(B,Q);let D=this;A.forEach(function(Z){D.setElement(Z[0],Z[1])})}begin(){return new nw(this.C.i||this.C,this.C,this)}end(){return new nw(this.C,this.C,this)}rBegin(){return new nw(this.C.h||this.C,this.C,this,1)}rEnd(){return new nw(this.C,this.C,this,1)}front(){if(this.m===0)return;let A=this.C.i;return[A.u,A.l]}back(){if(this.m===0)return;let A=this.C.h;return[A.u,A.l]}lowerBound(A){let B=this.R(this.v,A);return new nw(B,this.C,this)}upperBound(A){let B=this.K(this.v,A);return new nw(B,this.C,this)}reverseLowerBound(A){let B=this.L(this.v,A);return new nw(B,this.C,this)}reverseUpperBound(A){let B=this.k(this.v,A);return new nw(B,this.C,this)}forEach(A){this.U(function(B,Q,D){A([B.u,B.l],Q,D)})}setElement(A,B,Q){return this.q(A,B,Q)}getElementByPos(A){if(A<0||A>this.m-1)throw new RangeError;let B=this.U(A);return[B.u,B.l]}find(A){let B=this.H(this.v,A);return new nw(B,this.C,this)}getElementByKey(A){return this.H(this.v,A).l}union(A){let B=this;return A.forEach(function(Q){B.setElement(Q[0],Q[1])}),this.m}*[Symbol.iterator](){let A=this.m,B=this.U();for(let Q=0;Q<A;++Q){let D=B[Q];yield[D.u,D.l]}}}tp2.OrderedMap=op2});
var er2=E((or2)=>{Object.defineProperty(or2,"__esModule",{value:!0});or2.StatusBuilder=void 0;class rr2{constructor(){this.code=null,this.details=null,this.metadata=null}withCode(A){return this.code=A,this}withDetails(A){return this.details=A,this}withMetadata(A){return this.metadata=A,this}build(){let A={};if(this.code!==null)A.code=this.code;if(this.details!==null)A.details=this.details;if(this.metadata!==null)A.metadata=this.metadata;return A}}or2.StatusBuilder=rr2});
var fW0=E((Ss2)=>{Object.defineProperty(Ss2,"__esModule",{value:!0});Ss2.BaseFilter=void 0;class Ps2{async sendMetadata(A){return A}receiveMetadata(A){return A}async sendMessage(A){return A}async receiveMessage(A){return A}receiveTrailers(A){return A}}Ss2.BaseFilter=Ps2});
var gs2=E((fs2)=>{Object.defineProperty(fs2,"__esModule",{value:!0});fs2.CompressionFilterFactory=fs2.CompressionFilter=void 0;var GP1=J1("zlib"),ys2=bW0(),Yt=S6(),TV6=fW0(),PV6=D7(),SV6=(A)=>{return typeof A==="number"&&typeof ys2.CompressionAlgorithms[A]==="string"};class g31{async writeMessage(A,B){let Q=A;if(B)Q=await this.compressMessage(Q);let D=Buffer.allocUnsafe(Q.length+5);return D.writeUInt8(B?1:0,0),D.writeUInt32BE(Q.length,1),Q.copy(D,5),D}async readMessage(A){let B=A.readUInt8(0)===1,Q=A.slice(5);if(B)Q=await this.decompressMessage(Q);return Q}}class Wt extends g31{async compressMessage(A){return A}async writeMessage(A,B){let Q=Buffer.allocUnsafe(A.length+5);return Q.writeUInt8(0,0),Q.writeUInt32BE(A.length,1),A.copy(Q,5),Q}decompressMessage(A){return Promise.reject(new Error('Received compressed message but "grpc-encoding" header was identity'))}}class _s2 extends g31{constructor(A){super();this.maxRecvMessageLength=A}compressMessage(A){return new Promise((B,Q)=>{GP1.deflate(A,(D,Z)=>{if(D)Q(D);else B(Z)})})}decompressMessage(A){return new Promise((B,Q)=>{let D=0,Z=[],G=GP1.createInflate();G.on("data",(F)=>{if(Z.push(F),D+=F.byteLength,this.maxRecvMessageLength!==-1&&D>this.maxRecvMessageLength)G.destroy(),Q({code:Yt.Status.RESOURCE_EXHAUSTED,details:`Received message that decompresses to a size larger than ${this.maxRecvMessageLength}`})}),G.on("end",()=>{B(Buffer.concat(Z))}),G.write(A),G.end()})}}class xs2 extends g31{constructor(A){super();this.maxRecvMessageLength=A}compressMessage(A){return new Promise((B,Q)=>{GP1.gzip(A,(D,Z)=>{if(D)Q(D);else B(Z)})})}decompressMessage(A){return new Promise((B,Q)=>{let D=0,Z=[],G=GP1.createGunzip();G.on("data",(F)=>{if(Z.push(F),D+=F.byteLength,this.maxRecvMessageLength!==-1&&D>this.maxRecvMessageLength)G.destroy(),Q({code:Yt.Status.RESOURCE_EXHAUSTED,details:`Received message that decompresses to a size larger than ${this.maxRecvMessageLength}`})}),G.on("end",()=>{B(Buffer.concat(Z))}),G.write(A),G.end()})}}class vs2 extends g31{constructor(A){super();this.compressionName=A}compressMessage(A){return Promise.reject(new Error(`Received message compressed with unsupported compression method ${this.compressionName}`))}decompressMessage(A){return Promise.reject(new Error(`Compression method not supported: ${this.compressionName}`))}}function ks2(A,B){switch(A){case"identity":return new Wt;case"deflate":return new _s2(B);case"gzip":return new xs2(B);default:return new vs2(A)}}class hW0 extends TV6.BaseFilter{constructor(A,B){var Q,D,Z;super();this.sharedFilterConfig=B,this.sendCompression=new Wt,this.receiveCompression=new Wt,this.currentCompressionAlgorithm="identity";let G=A["grpc.default_compression_algorithm"];if(this.maxReceiveMessageLength=(Q=A["grpc.max_receive_message_length"])!==null&&Q!==void 0?Q:Yt.DEFAULT_MAX_RECEIVE_MESSAGE_LENGTH,this.maxSendMessageLength=(D=A["grpc.max_send_message_length"])!==null&&D!==void 0?D:Yt.DEFAULT_MAX_SEND_MESSAGE_LENGTH,G!==void 0)if(SV6(G)){let F=ys2.CompressionAlgorithms[G],I=(Z=B.serverSupportedEncodingHeader)===null||Z===void 0?void 0:Z.split(",");if(!I||I.includes(F))this.currentCompressionAlgorithm=F,this.sendCompression=ks2(this.currentCompressionAlgorithm,-1)}else PV6.log(Yt.LogVerbosity.ERROR,`Invalid value provided for grpc.default_compression_algorithm option: ${G}`)}async sendMetadata(A){let B=await A;if(B.set("grpc-accept-encoding","identity,deflate,gzip"),B.set("accept-encoding","identity"),this.currentCompressionAlgorithm==="identity")B.remove("grpc-encoding");else B.set("grpc-encoding",this.currentCompressionAlgorithm);return B}receiveMetadata(A){let B=A.get("grpc-encoding");if(B.length>0){let D=B[0];if(typeof D==="string")this.receiveCompression=ks2(D,this.maxReceiveMessageLength)}A.remove("grpc-encoding");let Q=A.get("grpc-accept-encoding")[0];if(Q){if(this.sharedFilterConfig.serverSupportedEncodingHeader=Q,!Q.split(",").includes(this.currentCompressionAlgorithm))this.sendCompression=new Wt,this.currentCompressionAlgorithm="identity"}return A.remove("grpc-accept-encoding"),A}async sendMessage(A){var B;let Q=await A;if(this.maxSendMessageLength!==-1&&Q.message.length>this.maxSendMessageLength)throw{code:Yt.Status.RESOURCE_EXHAUSTED,details:`Attempted to send message with a size larger than ${this.maxSendMessageLength}`};let D;if(this.sendCompression instanceof Wt)D=!1;else D=(((B=Q.flags)!==null&&B!==void 0?B:0)&2)===0;return{message:await this.sendCompression.writeMessage(Q.message,D),flags:Q.flags}}async receiveMessage(A){return this.receiveCompression.readMessage(await A)}}fs2.CompressionFilter=hW0;class bs2{constructor(A,B){this.options=B,this.sharedFilterConfig={}}createFilter(){return new hW0(this.options,this.sharedFilterConfig)}}fs2.CompressionFilterFactory=bs2});
var hT1=E((a_5,Sn2)=>{Sn2.exports=Z7;var rw=At();((Z7.prototype=Object.create(rw.prototype)).constructor=Z7).className="Type";var wW6=sw(),tY0=oo(),bT1=n_(),$W6=yT1(),qW6=xT1(),rY0=vT1(),oY0=AT1(),NW6=tO1(),eW=KI(),LW6=eY0(),MW6=dY0(),RW6=pY0(),Pn2=aY0(),OW6=sY0();function Z7(A,B){rw.call(this,A,B),this.fields={},this.oneofs=void 0,this.extensions=void 0,this.reserved=void 0,this.group=void 0,this._fieldsById=null,this._fieldsArray=null,this._oneofsArray=null,this._ctor=null}Object.defineProperties(Z7.prototype,{fieldsById:{get:function(){if(this._fieldsById)return this._fieldsById;this._fieldsById={};for(var A=Object.keys(this.fields),B=0;B<A.length;++B){var Q=this.fields[A[B]],D=Q.id;if(this._fieldsById[D])throw Error("duplicate id "+D+" in "+this);this._fieldsById[D]=Q}return this._fieldsById}},fieldsArray:{get:function(){return this._fieldsArray||(this._fieldsArray=eW.toArray(this.fields))}},oneofsArray:{get:function(){return this._oneofsArray||(this._oneofsArray=eW.toArray(this.oneofs))}},ctor:{get:function(){return this._ctor||(this.ctor=Z7.generateConstructor(this)())},set:function(A){var B=A.prototype;if(!(B instanceof rY0))(A.prototype=new rY0).constructor=A,eW.merge(A.prototype,B);A.$type=A.prototype.$type=this,eW.merge(A,rY0,!0),this._ctor=A;var Q=0;for(;Q<this.fieldsArray.length;++Q)this._fieldsArray[Q].resolve();var D={};for(Q=0;Q<this.oneofsArray.length;++Q)D[this._oneofsArray[Q].resolve().name]={get:eW.oneOfGetter(this._oneofsArray[Q].oneof),set:eW.oneOfSetter(this._oneofsArray[Q].oneof)};if(Q)Object.defineProperties(A.prototype,D)}}});Z7.generateConstructor=function A(B){var Q=eW.codegen(["p"],B.name);for(var D=0,Z;D<B.fieldsArray.length;++D)if((Z=B._fieldsArray[D]).map)Q("this%s={}",eW.safeProp(Z.name));else if(Z.repeated)Q("this%s=[]",eW.safeProp(Z.name));return Q("if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)")("this[ks[i]]=p[ks[i]]")};function fT1(A){return A._fieldsById=A._fieldsArray=A._oneofsArray=null,delete A.encode,delete A.decode,delete A.verify,A}Z7.fromJSON=function A(B,Q){var D=new Z7(B,Q.options);D.extensions=Q.extensions,D.reserved=Q.reserved;var Z=Object.keys(Q.fields),G=0;for(;G<Z.length;++G)D.add((typeof Q.fields[Z[G]].keyType!=="undefined"?$W6.fromJSON:bT1.fromJSON)(Z[G],Q.fields[Z[G]]));if(Q.oneofs)for(Z=Object.keys(Q.oneofs),G=0;G<Z.length;++G)D.add(tY0.fromJSON(Z[G],Q.oneofs[Z[G]]));if(Q.nested)for(Z=Object.keys(Q.nested),G=0;G<Z.length;++G){var F=Q.nested[Z[G]];D.add((F.id!==void 0?bT1.fromJSON:F.fields!==void 0?Z7.fromJSON:F.values!==void 0?wW6.fromJSON:F.methods!==void 0?qW6.fromJSON:rw.fromJSON)(Z[G],F))}if(Q.extensions&&Q.extensions.length)D.extensions=Q.extensions;if(Q.reserved&&Q.reserved.length)D.reserved=Q.reserved;if(Q.group)D.group=!0;if(Q.comment)D.comment=Q.comment;return D};Z7.prototype.toJSON=function A(B){var Q=rw.prototype.toJSON.call(this,B),D=B?Boolean(B.keepComments):!1;return eW.toObject(["options",Q&&Q.options||void 0,"oneofs",rw.arrayToJSON(this.oneofsArray,B),"fields",rw.arrayToJSON(this.fieldsArray.filter(function(Z){return!Z.declaringField}),B)||{},"extensions",this.extensions&&this.extensions.length?this.extensions:void 0,"reserved",this.reserved&&this.reserved.length?this.reserved:void 0,"group",this.group||void 0,"nested",Q&&Q.nested||void 0,"comment",D?this.comment:void 0])};Z7.prototype.resolveAll=function A(){var B=this.fieldsArray,Q=0;while(Q<B.length)B[Q++].resolve();var D=this.oneofsArray;Q=0;while(Q<D.length)D[Q++].resolve();return rw.prototype.resolveAll.call(this)};Z7.prototype.get=function A(B){return this.fields[B]||this.oneofs&&this.oneofs[B]||this.nested&&this.nested[B]||null};Z7.prototype.add=function A(B){if(this.get(B.name))throw Error("duplicate name '"+B.name+"' in "+this);if(B instanceof bT1&&B.extend===void 0){if(this._fieldsById?this._fieldsById[B.id]:this.fieldsById[B.id])throw Error("duplicate id "+B.id+" in "+this);if(this.isReservedId(B.id))throw Error("id "+B.id+" is reserved in "+this);if(this.isReservedName(B.name))throw Error("name '"+B.name+"' is reserved in "+this);if(B.parent)B.parent.remove(B);return this.fields[B.name]=B,B.message=this,B.onAdd(this),fT1(this)}if(B instanceof tY0){if(!this.oneofs)this.oneofs={};return this.oneofs[B.name]=B,B.onAdd(this),fT1(this)}return rw.prototype.add.call(this,B)};Z7.prototype.remove=function A(B){if(B instanceof bT1&&B.extend===void 0){if(!this.fields||this.fields[B.name]!==B)throw Error(B+" is not a member of "+this);return delete this.fields[B.name],B.parent=null,B.onRemove(this),fT1(this)}if(B instanceof tY0){if(!this.oneofs||this.oneofs[B.name]!==B)throw Error(B+" is not a member of "+this);return delete this.oneofs[B.name],B.parent=null,B.onRemove(this),fT1(this)}return rw.prototype.remove.call(this,B)};Z7.prototype.isReservedId=function A(B){return rw.isReservedId(this.reserved,B)};Z7.prototype.isReservedName=function A(B){return rw.isReservedName(this.reserved,B)};Z7.prototype.create=function A(B){return new this.ctor(B)};Z7.prototype.setup=function A(){var B=this.fullName,Q=[];for(var D=0;D<this.fieldsArray.length;++D)Q.push(this._fieldsArray[D].resolve().resolvedType);this.encode=LW6(this)({Writer:NW6,types:Q,util:eW}),this.decode=MW6(this)({Reader:oY0,types:Q,util:eW}),this.verify=RW6(this)({types:Q,util:eW}),this.fromObject=Pn2.fromObject(this)({types:Q,util:eW}),this.toObject=Pn2.toObject(this)({types:Q,util:eW});var Z=OW6[B];if(Z){var G=Object.create(this);G.fromObject=this.fromObject,this.fromObject=Z.fromObject.bind(G),G.toObject=this.toObject,this.toObject=Z.toObject.bind(G)}return this};Z7.prototype.encode=function A(B,Q){return this.setup().encode(B,Q)};Z7.prototype.encodeDelimited=function A(B,Q){return this.encode(B,Q&&Q.len?Q.fork():Q).ldelim()};Z7.prototype.decode=function A(B,Q){return this.setup().decode(B,Q)};Z7.prototype.decodeDelimited=function A(B){if(!(B instanceof oY0))B=oY0.create(B);return this.decode(B,B.uint32())};Z7.prototype.verify=function A(B){return this.setup().verify(B)};Z7.prototype.fromObject=function A(B){return this.setup().fromObject(B)};Z7.prototype.toObject=function A(B,Q){return this.setup().toObject(B,Q)};Z7.d=function A(B){return function Q(D){eW.decorateType(D,B)}}});
var hp2=E((bp2)=>{Object.defineProperty(bp2,"__esModule",{value:!0});bp2.recognizedOptions=void 0;bp2.channelOptionsEqual=$I6;bp2.recognizedOptions={"grpc.ssl_target_name_override":!0,"grpc.primary_user_agent":!0,"grpc.secondary_user_agent":!0,"grpc.default_authority":!0,"grpc.keepalive_time_ms":!0,"grpc.keepalive_timeout_ms":!0,"grpc.keepalive_permit_without_calls":!0,"grpc.service_config":!0,"grpc.max_concurrent_streams":!0,"grpc.initial_reconnect_backoff_ms":!0,"grpc.max_reconnect_backoff_ms":!0,"grpc.use_local_subchannel_pool":!0,"grpc.max_send_message_length":!0,"grpc.max_receive_message_length":!0,"grpc.enable_http_proxy":!0,"grpc.enable_channelz":!0,"grpc.dns_min_time_between_resolutions_ms":!0,"grpc.enable_retries":!0,"grpc.per_rpc_retry_buffer_size":!0,"grpc.retry_buffer_size":!0,"grpc.max_connection_age_ms":!0,"grpc.max_connection_age_grace_ms":!0,"grpc-node.max_session_memory":!0,"grpc.service_config_disable_resolution":!0,"grpc.client_idle_timeout_ms":!0,"grpc-node.tls_enable_trace":!0,"grpc.lb.ring_hash.ring_size_cap":!0,"grpc-node.retry_max_attempts_limit":!0,"grpc-node.flow_control_window":!0};function $I6(A,B){let Q=Object.keys(A).sort(),D=Object.keys(B).sort();if(Q.length!==D.length)return!1;for(let Z=0;Z<Q.length;Z+=1){if(Q[Z]!==D[Z])return!1;if(A[Q[Z]]!==B[D[Z]])return!1}return!0}});
var hu=E((da2)=>{var __dirname="/home/<USER>/code/tmp/claude-cli-external-build-2542/node_modules/@grpc/grpc-js/build/src";Object.defineProperty(da2,"__esModule",{value:!0});da2.registerChannelzSocket=da2.registerChannelzServer=da2.registerChannelzSubchannel=da2.registerChannelzChannel=da2.ChannelzCallTrackerStub=da2.ChannelzCallTracker=da2.ChannelzChildrenTrackerStub=da2.ChannelzChildrenTracker=da2.ChannelzTrace=da2.ChannelzTraceStub=void 0;da2.unregisterChannelzRef=sJ6;da2.getChannelzHandlers=ua2;da2.getChannelzServiceDefinition=ma2;da2.setup=FX6;var rT1=J1("net"),fu=ep2(),x31=VE(),v31=S6(),pJ6=CE(),iJ6=LT1(),nJ6=PY0();function zW0(A){return{channel_id:A.id,name:A.name}}function EW0(A){return{subchannel_id:A.id,name:A.name}}function aJ6(A){return{server_id:A.id}}function oT1(A){return{socket_id:A.id,name:A.name}}var Sa2=32,UW0=100;class _a2{constructor(){this.events=[],this.creationTimestamp=new Date,this.eventsLogged=0}addTrace(){}getTraceMessage(){return{creation_timestamp:BM(this.creationTimestamp),num_events_logged:this.eventsLogged,events:[]}}}da2.ChannelzTraceStub=_a2;class xa2{constructor(){this.events=[],this.eventsLogged=0,this.creationTimestamp=new Date}addTrace(A,B,Q){let D=new Date;if(this.events.push({description:B,severity:A,timestamp:D,childChannel:(Q===null||Q===void 0?void 0:Q.kind)==="channel"?Q:void 0,childSubchannel:(Q===null||Q===void 0?void 0:Q.kind)==="subchannel"?Q:void 0}),this.events.length>=Sa2*2)this.events=this.events.slice(Sa2);this.eventsLogged+=1}getTraceMessage(){return{creation_timestamp:BM(this.creationTimestamp),num_events_logged:this.eventsLogged,events:this.events.map((A)=>{return{description:A.description,severity:A.severity,timestamp:BM(A.timestamp),channel_ref:A.childChannel?zW0(A.childChannel):null,subchannel_ref:A.childSubchannel?EW0(A.childSubchannel):null}})}}}da2.ChannelzTrace=xa2;class wW0{constructor(){this.channelChildren=new fu.OrderedMap,this.subchannelChildren=new fu.OrderedMap,this.socketChildren=new fu.OrderedMap,this.trackerMap={["channel"]:this.channelChildren,["subchannel"]:this.subchannelChildren,["socket"]:this.socketChildren}}refChild(A){let B=this.trackerMap[A.kind],Q=B.find(A.id);if(Q.equals(B.end()))B.setElement(A.id,{ref:A,count:1},Q);else Q.pointer[1].count+=1}unrefChild(A){let B=this.trackerMap[A.kind],Q=B.getElementByKey(A.id);if(Q!==void 0){if(Q.count-=1,Q.count===0)B.eraseElementByKey(A.id)}}getChildLists(){return{channels:this.channelChildren,subchannels:this.subchannelChildren,sockets:this.socketChildren}}}da2.ChannelzChildrenTracker=wW0;class va2 extends wW0{refChild(){}unrefChild(){}}da2.ChannelzChildrenTrackerStub=va2;class $W0{constructor(){this.callsStarted=0,this.callsSucceeded=0,this.callsFailed=0,this.lastCallStartedTimestamp=null}addCallStarted(){this.callsStarted+=1,this.lastCallStartedTimestamp=new Date}addCallSucceeded(){this.callsSucceeded+=1}addCallFailed(){this.callsFailed+=1}}da2.ChannelzCallTracker=$W0;class ba2 extends $W0{addCallStarted(){}addCallSucceeded(){}addCallFailed(){}}da2.ChannelzCallTrackerStub=ba2;var CP={["channel"]:new fu.OrderedMap,["subchannel"]:new fu.OrderedMap,["server"]:new fu.OrderedMap,["socket"]:new fu.OrderedMap},tT1=(A)=>{let B=1;function Q(){return B++}let D=CP[A];return(Z,G,F)=>{let I=Q(),Y={id:I,name:Z,kind:A};if(F)D.setElement(I,{ref:Y,getInfo:G});return Y}};da2.registerChannelzChannel=tT1("channel");da2.registerChannelzSubchannel=tT1("subchannel");da2.registerChannelzServer=tT1("server");da2.registerChannelzSocket=tT1("socket");function sJ6(A){CP[A.kind].eraseElementByKey(A.id)}function rJ6(A){let B=Number.parseInt(A,16);return[B/256|0,B%256]}function ja2(A){if(A==="")return[];let B=A.split(":").map((D)=>rJ6(D));return[].concat(...B)}function oJ6(A){return rT1.isIPv6(A)&&A.toLowerCase().startsWith("::ffff:")&&rT1.isIPv4(A.substring(7))}function ka2(A){return Buffer.from(Uint8Array.from(A.split(".").map((B)=>Number.parseInt(B))))}function tJ6(A){if(rT1.isIPv4(A))return ka2(A);else if(oJ6(A))return ka2(A.substring(7));else if(rT1.isIPv6(A)){let B,Q,D=A.indexOf("::");if(D===-1)B=A,Q="";else B=A.substring(0,D),Q=A.substring(D+2);let Z=Buffer.from(ja2(B)),G=Buffer.from(ja2(Q)),F=Buffer.alloc(16-Z.length-G.length,0);return Buffer.concat([Z,F,G])}else return null}function fa2(A){switch(A){case x31.ConnectivityState.CONNECTING:return{state:"CONNECTING"};case x31.ConnectivityState.IDLE:return{state:"IDLE"};case x31.ConnectivityState.READY:return{state:"READY"};case x31.ConnectivityState.SHUTDOWN:return{state:"SHUTDOWN"};case x31.ConnectivityState.TRANSIENT_FAILURE:return{state:"TRANSIENT_FAILURE"};default:return{state:"UNKNOWN"}}}function BM(A){if(!A)return null;let B=A.getTime();return{seconds:B/1000|0,nanos:B%1000*1e6}}function ha2(A){let B=A.getInfo(),Q=[],D=[];return B.children.channels.forEach((Z)=>{Q.push(zW0(Z[1].ref))}),B.children.subchannels.forEach((Z)=>{D.push(EW0(Z[1].ref))}),{ref:zW0(A.ref),data:{target:B.target,state:fa2(B.state),calls_started:B.callTracker.callsStarted,calls_succeeded:B.callTracker.callsSucceeded,calls_failed:B.callTracker.callsFailed,last_call_started_timestamp:BM(B.callTracker.lastCallStartedTimestamp),trace:B.trace.getTraceMessage()},channel_ref:Q,subchannel_ref:D}}function eJ6(A,B){let Q=parseInt(A.request.channel_id,10),D=CP.channel.getElementByKey(Q);if(D===void 0){B({code:v31.Status.NOT_FOUND,details:"No channel data found for id "+Q});return}B(null,{channel:ha2(D)})}function AX6(A,B){let Q=parseInt(A.request.max_results,10)||UW0,D=[],Z=parseInt(A.request.start_channel_id,10),G=CP.channel,F;for(F=G.lowerBound(Z);!F.equals(G.end())&&D.length<Q;F=F.next())D.push(ha2(F.pointer[1]));B(null,{channel:D,end:F.equals(G.end())})}function ga2(A){let B=A.getInfo(),Q=[];return B.listenerChildren.sockets.forEach((D)=>{Q.push(oT1(D[1].ref))}),{ref:aJ6(A.ref),data:{calls_started:B.callTracker.callsStarted,calls_succeeded:B.callTracker.callsSucceeded,calls_failed:B.callTracker.callsFailed,last_call_started_timestamp:BM(B.callTracker.lastCallStartedTimestamp),trace:B.trace.getTraceMessage()},listen_socket:Q}}function BX6(A,B){let Q=parseInt(A.request.server_id,10),Z=CP.server.getElementByKey(Q);if(Z===void 0){B({code:v31.Status.NOT_FOUND,details:"No server data found for id "+Q});return}B(null,{server:ga2(Z)})}function QX6(A,B){let Q=parseInt(A.request.max_results,10)||UW0,D=parseInt(A.request.start_server_id,10),Z=CP.server,G=[],F;for(F=Z.lowerBound(D);!F.equals(Z.end())&&G.length<Q;F=F.next())G.push(ga2(F.pointer[1]));B(null,{server:G,end:F.equals(Z.end())})}function DX6(A,B){let Q=parseInt(A.request.subchannel_id,10),D=CP.subchannel.getElementByKey(Q);if(D===void 0){B({code:v31.Status.NOT_FOUND,details:"No subchannel data found for id "+Q});return}let Z=D.getInfo(),G=[];Z.children.sockets.forEach((I)=>{G.push(oT1(I[1].ref))});let F={ref:EW0(D.ref),data:{target:Z.target,state:fa2(Z.state),calls_started:Z.callTracker.callsStarted,calls_succeeded:Z.callTracker.callsSucceeded,calls_failed:Z.callTracker.callsFailed,last_call_started_timestamp:BM(Z.callTracker.lastCallStartedTimestamp),trace:Z.trace.getTraceMessage()},socket_ref:G};B(null,{subchannel:F})}function ya2(A){var B;if(pJ6.isTcpSubchannelAddress(A))return{address:"tcpip_address",tcpip_address:{ip_address:(B=tJ6(A.host))!==null&&B!==void 0?B:void 0,port:A.port}};else return{address:"uds_address",uds_address:{filename:A.path}}}function ZX6(A,B){var Q,D,Z,G,F;let I=parseInt(A.request.socket_id,10),Y=CP.socket.getElementByKey(I);if(Y===void 0){B({code:v31.Status.NOT_FOUND,details:"No socket data found for id "+I});return}let W=Y.getInfo(),J=W.security?{model:"tls",tls:{cipher_suite:W.security.cipherSuiteStandardName?"standard_name":"other_name",standard_name:(Q=W.security.cipherSuiteStandardName)!==null&&Q!==void 0?Q:void 0,other_name:(D=W.security.cipherSuiteOtherName)!==null&&D!==void 0?D:void 0,local_certificate:(Z=W.security.localCertificate)!==null&&Z!==void 0?Z:void 0,remote_certificate:(G=W.security.remoteCertificate)!==null&&G!==void 0?G:void 0}}:null,X={ref:oT1(Y.ref),local:W.localAddress?ya2(W.localAddress):null,remote:W.remoteAddress?ya2(W.remoteAddress):null,remote_name:(F=W.remoteName)!==null&&F!==void 0?F:void 0,security:J,data:{keep_alives_sent:W.keepAlivesSent,streams_started:W.streamsStarted,streams_succeeded:W.streamsSucceeded,streams_failed:W.streamsFailed,last_local_stream_created_timestamp:BM(W.lastLocalStreamCreatedTimestamp),last_remote_stream_created_timestamp:BM(W.lastRemoteStreamCreatedTimestamp),messages_received:W.messagesReceived,messages_sent:W.messagesSent,last_message_received_timestamp:BM(W.lastMessageReceivedTimestamp),last_message_sent_timestamp:BM(W.lastMessageSentTimestamp),local_flow_control_window:W.localFlowControlWindow?{value:W.localFlowControlWindow}:null,remote_flow_control_window:W.remoteFlowControlWindow?{value:W.remoteFlowControlWindow}:null}};B(null,{socket:X})}function GX6(A,B){let Q=parseInt(A.request.server_id,10),D=CP.server.getElementByKey(Q);if(D===void 0){B({code:v31.Status.NOT_FOUND,details:"No server data found for id "+Q});return}let Z=parseInt(A.request.start_socket_id,10),G=parseInt(A.request.max_results,10)||UW0,I=D.getInfo().sessionChildren.sockets,Y=[],W;for(W=I.lowerBound(Z);!W.equals(I.end())&&Y.length<G;W=W.next())Y.push(oT1(W.pointer[1].ref));B(null,{socket_ref:Y,end:W.equals(I.end())})}function ua2(){return{GetChannel:eJ6,GetTopChannels:AX6,GetServer:BX6,GetServers:QX6,GetSubchannel:DX6,GetSocket:ZX6,GetServerSockets:GX6}}var sT1=null;function ma2(){if(sT1)return sT1;let A=Pa2().loadSync,B=A("channelz.proto",{keepCase:!0,longs:String,enums:String,defaults:!0,oneofs:!0,includeDirs:[`${__dirname}/../../proto`]});return sT1=nJ6.loadPackageDefinition(B).grpc.channelz.v1.Channelz.service,sT1}function FX6(){iJ6.registerAdminService(ma2,ua2)}});
var jW0=E((Ks2)=>{Object.defineProperty(Ks2,"__esModule",{value:!0});Ks2.getNextCallNumber=AV6;var eX6=0;function AV6(){return eX6++}});
var kJ0=E((TP1)=>{Object.defineProperty(TP1,"__esModule",{value:!0});TP1.createOtlpGrpcExportDelegate=TP1.convertLegacyOtlpGrpcOptions=void 0;var Sz6=Ut2();Object.defineProperty(TP1,"convertLegacyOtlpGrpcOptions",{enumerable:!0,get:function(){return Sz6.convertLegacyOtlpGrpcOptions}});var jz6=qt2();Object.defineProperty(TP1,"createOtlpGrpcExportDelegate",{enumerable:!0,get:function(){return jz6.createOtlpGrpcExportDelegate}})});
var lW0=E((Cr2)=>{Object.defineProperty(Cr2,"__esModule",{value:!0});Cr2.InternalChannel=Cr2.SUBCHANNEL_ARGS_EXCLUDE_KEY_PREFIX=void 0;var IC6=C31(),YC6=vp2(),WC6=Ns2(),cW0=p_(),JC6=tW(),Ax=S6(),XC6=vW0(),VC6=gs2(),Yr2=nL(),XP1=D7(),CC6=TW0(),VP1=SV(),UE=VE(),d31=hu(),KC6=ns2(),HC6=u31(),zC6=ts2(),mW0=jW0(),EC6=FP1(),dW0=Zr2(),UC6=JP1(),wC6=2147483647,$C6=1000,qC6=1800000,CP1=new Map,NC6=16777216,LC6=1048576;class Wr2 extends UC6.BaseSubchannelWrapper{constructor(A,B){super(A);this.channel=B,this.refCount=0,this.subchannelStateListener=(Q,D,Z,G)=>{B.throttleKeepalive(G)}}ref(){if(this.refCount===0)this.child.addConnectivityStateListener(this.subchannelStateListener),this.channel.addWrappedSubchannel(this);this.child.ref(),this.refCount+=1}unref(){if(this.child.unref(),this.refCount-=1,this.refCount<=0)this.child.removeConnectivityStateListener(this.subchannelStateListener),this.channel.removeWrappedSubchannel(this)}}class Jr2{pick(A){return{pickResultType:cW0.PickResultType.DROP,status:{code:Ax.Status.UNAVAILABLE,details:"Channel closed before call started",metadata:new JC6.Metadata},subchannel:null,onCallStarted:null,onCallEnded:null}}}Cr2.SUBCHANNEL_ARGS_EXCLUDE_KEY_PREFIX="grpc.internal.no_subchannel";class Xr2{constructor(A){this.target=A,this.trace=new d31.ChannelzTrace,this.callTracker=new d31.ChannelzCallTracker,this.childrenTracker=new d31.ChannelzChildrenTracker,this.state=UE.ConnectivityState.IDLE}getChannelzInfoCallback(){return()=>{return{target:this.target,state:this.state,trace:this.trace,callTracker:this.callTracker,children:this.childrenTracker.getChildLists()}}}}class Vr2{constructor(A,B,Q){var D,Z,G,F,I,Y;if(this.credentials=B,this.options=Q,this.connectivityState=UE.ConnectivityState.IDLE,this.currentPicker=new cW0.UnavailablePicker,this.configSelectionQueue=[],this.pickQueue=[],this.connectivityStateWatchers=[],this.callRefTimer=null,this.configSelector=null,this.currentResolutionError=null,this.wrappedSubchannels=new Set,this.callCount=0,this.idleTimer=null,this.channelzEnabled=!0,this.randomChannelId=Math.floor(Math.random()*Number.MAX_SAFE_INTEGER),typeof A!=="string")throw new TypeError("Channel target must be a string");if(!(B instanceof IC6.ChannelCredentials))throw new TypeError("Channel credentials must be a ChannelCredentials object");if(Q){if(typeof Q!=="object")throw new TypeError("Channel options must be an object")}this.channelzInfoTracker=new Xr2(A);let W=VP1.parseUri(A);if(W===null)throw new Error(`Could not parse target name "${A}"`);let J=Yr2.mapUriDefaultScheme(W);if(J===null)throw new Error(`Could not find a default scheme for target name "${A}"`);if(this.options["grpc.enable_channelz"]===0)this.channelzEnabled=!1;if(this.channelzRef=d31.registerChannelzChannel(A,this.channelzInfoTracker.getChannelzInfoCallback(),this.channelzEnabled),this.channelzEnabled)this.channelzInfoTracker.trace.addTrace("CT_INFO","Channel created");if(this.options["grpc.default_authority"])this.defaultAuthority=this.options["grpc.default_authority"];else this.defaultAuthority=Yr2.getDefaultAuthority(J);let X=CC6.mapProxyName(J,Q);this.target=X.target,this.options=Object.assign({},this.options,X.extraOptions),this.subchannelPool=WC6.getSubchannelPool(((D=Q["grpc.use_local_subchannel_pool"])!==null&&D!==void 0?D:0)===0),this.retryBufferTracker=new dW0.MessageBufferTracker((Z=Q["grpc.retry_buffer_size"])!==null&&Z!==void 0?Z:NC6,(G=Q["grpc.per_rpc_retry_buffer_size"])!==null&&G!==void 0?G:LC6),this.keepaliveTime=(F=Q["grpc.keepalive_time_ms"])!==null&&F!==void 0?F:-1,this.idleTimeoutMs=Math.max((I=Q["grpc.client_idle_timeout_ms"])!==null&&I!==void 0?I:qC6,$C6);let V={createSubchannel:(K,H)=>{let z={};for(let[N,O]of Object.entries(H))if(!N.startsWith(Cr2.SUBCHANNEL_ARGS_EXCLUDE_KEY_PREFIX))z[N]=O;let $=this.subchannelPool.getOrCreateSubchannel(this.target,K,z,this.credentials);if($.throttleKeepalive(this.keepaliveTime),this.channelzEnabled)this.channelzInfoTracker.trace.addTrace("CT_INFO","Created subchannel or used existing subchannel",$.getChannelzRef());return new Wr2($,this)},updateState:(K,H)=>{this.currentPicker=H;let z=this.pickQueue.slice();if(this.pickQueue=[],z.length>0)this.callRefTimerUnref();for(let $ of z)$.doPick();this.updateState(K)},requestReresolution:()=>{throw new Error("Resolving load balancer should never call requestReresolution")},addChannelzChild:(K)=>{if(this.channelzEnabled)this.channelzInfoTracker.childrenTracker.refChild(K)},removeChannelzChild:(K)=>{if(this.channelzEnabled)this.channelzInfoTracker.childrenTracker.unrefChild(K)}};this.resolvingLoadBalancer=new YC6.ResolvingLoadBalancer(this.target,V,Q,(K,H)=>{var z;if(K.retryThrottling)CP1.set(this.getTarget(),new dW0.RetryThrottler(K.retryThrottling.maxTokens,K.retryThrottling.tokenRatio,CP1.get(this.getTarget())));else CP1.delete(this.getTarget());if(this.channelzEnabled)this.channelzInfoTracker.trace.addTrace("CT_INFO","Address resolution succeeded");(z=this.configSelector)===null||z===void 0||z.unref(),this.configSelector=H,this.currentResolutionError=null,process.nextTick(()=>{let $=this.configSelectionQueue;if(this.configSelectionQueue=[],$.length>0)this.callRefTimerUnref();for(let L of $)L.getConfig()})},(K)=>{if(this.channelzEnabled)this.channelzInfoTracker.trace.addTrace("CT_WARNING","Address resolution failed with code "+K.code+' and details "'+K.details+'"');if(this.configSelectionQueue.length>0)this.trace("Name resolution failed with calls queued for config selection");if(this.configSelector===null)this.currentResolutionError=Object.assign(Object.assign({},EC6.restrictControlPlaneStatusCode(K.code,K.details)),{metadata:K.metadata});let H=this.configSelectionQueue;if(this.configSelectionQueue=[],H.length>0)this.callRefTimerUnref();for(let z of H)z.reportResolverError(K)}),this.filterStackFactory=new XC6.FilterStackFactory([new VC6.CompressionFilterFactory(this,this.options)]),this.trace("Channel constructed with options "+JSON.stringify(Q,void 0,2));let C=new Error;if(XP1.isTracerEnabled("channel_stacktrace"))XP1.trace(Ax.LogVerbosity.DEBUG,"channel_stacktrace","("+this.channelzRef.id+`) Channel constructed 
`+((Y=C.stack)===null||Y===void 0?void 0:Y.substring(C.stack.indexOf(`
`)+1)));this.lastActivityTimestamp=new Date}trace(A,B){XP1.trace(B!==null&&B!==void 0?B:Ax.LogVerbosity.DEBUG,"channel","("+this.channelzRef.id+") "+VP1.uriToString(this.target)+" "+A)}callRefTimerRef(){var A,B,Q,D;if(!this.callRefTimer)this.callRefTimer=setInterval(()=>{},wC6);if(!((B=(A=this.callRefTimer).hasRef)===null||B===void 0?void 0:B.call(A)))this.trace("callRefTimer.ref | configSelectionQueue.length="+this.configSelectionQueue.length+" pickQueue.length="+this.pickQueue.length),(D=(Q=this.callRefTimer).ref)===null||D===void 0||D.call(Q)}callRefTimerUnref(){var A,B,Q;if(!((A=this.callRefTimer)===null||A===void 0?void 0:A.hasRef)||this.callRefTimer.hasRef())this.trace("callRefTimer.unref | configSelectionQueue.length="+this.configSelectionQueue.length+" pickQueue.length="+this.pickQueue.length),(Q=(B=this.callRefTimer)===null||B===void 0?void 0:B.unref)===null||Q===void 0||Q.call(B)}removeConnectivityStateWatcher(A){let B=this.connectivityStateWatchers.findIndex((Q)=>Q===A);if(B>=0)this.connectivityStateWatchers.splice(B,1)}updateState(A){if(XP1.trace(Ax.LogVerbosity.DEBUG,"connectivity_state","("+this.channelzRef.id+") "+VP1.uriToString(this.target)+" "+UE.ConnectivityState[this.connectivityState]+" -> "+UE.ConnectivityState[A]),this.channelzEnabled)this.channelzInfoTracker.trace.addTrace("CT_INFO","Connectivity state change to "+UE.ConnectivityState[A]);this.connectivityState=A,this.channelzInfoTracker.state=A;let B=this.connectivityStateWatchers.slice();for(let Q of B)if(A!==Q.currentState){if(Q.timer)clearTimeout(Q.timer);this.removeConnectivityStateWatcher(Q),Q.callback()}if(A!==UE.ConnectivityState.TRANSIENT_FAILURE)this.currentResolutionError=null}throttleKeepalive(A){if(A>this.keepaliveTime){this.keepaliveTime=A;for(let B of this.wrappedSubchannels)B.throttleKeepalive(A)}}addWrappedSubchannel(A){this.wrappedSubchannels.add(A)}removeWrappedSubchannel(A){this.wrappedSubchannels.delete(A)}doPick(A,B){return this.currentPicker.pick({metadata:A,extraPickInfo:B})}queueCallForPick(A){this.pickQueue.push(A),this.callRefTimerRef()}getConfig(A,B){if(this.connectivityState!==UE.ConnectivityState.SHUTDOWN)this.resolvingLoadBalancer.exitIdle();if(this.configSelector)return{type:"SUCCESS",config:this.configSelector.invoke(A,B,this.randomChannelId)};else if(this.currentResolutionError)return{type:"ERROR",error:this.currentResolutionError};else return{type:"NONE"}}queueCallForConfig(A){this.configSelectionQueue.push(A),this.callRefTimerRef()}enterIdle(){if(this.resolvingLoadBalancer.destroy(),this.updateState(UE.ConnectivityState.IDLE),this.currentPicker=new cW0.QueuePicker(this.resolvingLoadBalancer),this.idleTimer)clearTimeout(this.idleTimer),this.idleTimer=null;if(this.callRefTimer)clearInterval(this.callRefTimer),this.callRefTimer=null}startIdleTimeout(A){var B,Q;this.idleTimer=setTimeout(()=>{if(this.callCount>0){this.startIdleTimeout(this.idleTimeoutMs);return}let Z=new Date().valueOf()-this.lastActivityTimestamp.valueOf();if(Z>=this.idleTimeoutMs)this.trace("Idle timer triggered after "+this.idleTimeoutMs+"ms of inactivity"),this.enterIdle();else this.startIdleTimeout(this.idleTimeoutMs-Z)},A),(Q=(B=this.idleTimer).unref)===null||Q===void 0||Q.call(B)}maybeStartIdleTimer(){if(this.connectivityState!==UE.ConnectivityState.SHUTDOWN&&!this.idleTimer)this.startIdleTimeout(this.idleTimeoutMs)}onCallStart(){if(this.channelzEnabled)this.channelzInfoTracker.callTracker.addCallStarted();this.callCount+=1}onCallEnd(A){if(this.channelzEnabled)if(A.code===Ax.Status.OK)this.channelzInfoTracker.callTracker.addCallSucceeded();else this.channelzInfoTracker.callTracker.addCallFailed();this.callCount-=1,this.lastActivityTimestamp=new Date,this.maybeStartIdleTimer()}createLoadBalancingCall(A,B,Q,D,Z){let G=mW0.getNextCallNumber();return this.trace("createLoadBalancingCall ["+G+'] method="'+B+'"'),new KC6.LoadBalancingCall(this,A,B,Q,D,Z,G)}createRetryingCall(A,B,Q,D,Z){let G=mW0.getNextCallNumber();return this.trace("createRetryingCall ["+G+'] method="'+B+'"'),new dW0.RetryingCall(this,A,B,Q,D,Z,G,this.retryBufferTracker,CP1.get(this.getTarget()))}createResolvingCall(A,B,Q,D,Z){let G=mW0.getNextCallNumber();this.trace("createResolvingCall ["+G+'] method="'+A+'", deadline='+HC6.deadlineToString(B));let F={deadline:B,flags:Z!==null&&Z!==void 0?Z:Ax.Propagate.DEFAULTS,host:Q!==null&&Q!==void 0?Q:this.defaultAuthority,parentCall:D},I=new zC6.ResolvingCall(this,A,F,this.filterStackFactory.clone(),G);return this.onCallStart(),I.addStatusWatcher((Y)=>{this.onCallEnd(Y)}),I}close(){var A;this.resolvingLoadBalancer.destroy(),this.updateState(UE.ConnectivityState.SHUTDOWN),this.currentPicker=new Jr2;for(let B of this.configSelectionQueue)B.cancelWithStatus(Ax.Status.UNAVAILABLE,"Channel closed before call started");this.configSelectionQueue=[];for(let B of this.pickQueue)B.cancelWithStatus(Ax.Status.UNAVAILABLE,"Channel closed before call started");if(this.pickQueue=[],this.callRefTimer)clearInterval(this.callRefTimer);if(this.idleTimer)clearTimeout(this.idleTimer);if(this.channelzEnabled)d31.unregisterChannelzRef(this.channelzRef);this.subchannelPool.unrefUnusedSubchannels(),(A=this.configSelector)===null||A===void 0||A.unref(),this.configSelector=null}getTarget(){return VP1.uriToString(this.target)}getConnectivityState(A){let B=this.connectivityState;if(A)this.resolvingLoadBalancer.exitIdle(),this.lastActivityTimestamp=new Date,this.maybeStartIdleTimer();return B}watchConnectivityState(A,B,Q){if(this.connectivityState===UE.ConnectivityState.SHUTDOWN)throw new Error("Channel has been shut down");let D=null;if(B!==1/0){let G=B instanceof Date?B:new Date(B),F=new Date;if(B===-1/0||G<=F){process.nextTick(Q,new Error("Deadline passed without connectivity state change"));return}D=setTimeout(()=>{this.removeConnectivityStateWatcher(Z),Q(new Error("Deadline passed without connectivity state change"))},G.getTime()-F.getTime())}let Z={currentState:A,callback:Q,timer:D};this.connectivityStateWatchers.push(Z)}getChannelzRef(){return this.channelzRef}createCall(A,B,Q,D,Z){if(typeof A!=="string")throw new TypeError("Channel#createCall: method must be a string");if(!(typeof B==="number"||B instanceof Date))throw new TypeError("Channel#createCall: deadline must be a number or Date");if(this.connectivityState===UE.ConnectivityState.SHUTDOWN)throw new Error("Channel has been shut down");return this.createResolvingCall(A,B,Q,D,Z)}getOptions(){return this.options}}Cr2.InternalChannel=Vr2});
var ln2=E((Ax5,cn2)=>{var Q8=cn2.exports=xI0();Q8.build="light";function xW6(A,B,Q){if(typeof B==="function")Q=B,B=new Q8.Root;else if(!B)B=new Q8.Root;return B.load(A,Q)}Q8.load=xW6;function vW6(A,B){if(!B)B=new Q8.Root;return B.loadSync(A)}Q8.loadSync=vW6;Q8.encoder=eY0();Q8.decoder=dY0();Q8.verifier=pY0();Q8.converter=aY0();Q8.ReflectionObject=vu();Q8.Namespace=At();Q8.Root=mT1();Q8.Enum=sw();Q8.Type=hT1();Q8.Field=n_();Q8.OneOf=oo();Q8.MapField=yT1();Q8.Service=xT1();Q8.Method=_T1();Q8.Message=vT1();Q8.wrappers=sY0();Q8.types=xu();Q8.util=KI();Q8.ReflectionObject._configure(Q8.Root);Q8.Namespace._configure(Q8.Type,Q8.Service,Q8.Enum);Q8.Root._configure(Q8.Type);Q8.Field._configure(Q8.Type)});
var mT1=E((s_5,xn2)=>{xn2.exports=TK;var uT1=At();((TK.prototype=Object.create(uT1.prototype)).constructor=TK).className="Root";var BW0=n_(),kn2=sw(),TW6=oo(),s_=KI(),yn2,AW0,j31;function TK(A){uT1.call(this,"",A),this.deferred=[],this.files=[]}TK.fromJSON=function A(B,Q){if(!Q)Q=new TK;if(B.options)Q.setOptions(B.options);return Q.addJSON(B.nested)};TK.prototype.resolvePath=s_.path.resolve;TK.prototype.fetch=s_.fetch;function _n2(){}TK.prototype.load=function A(B,Q,D){if(typeof Q==="function")D=Q,Q=void 0;var Z=this;if(!D)return s_.asPromise(A,Z,B,Q);var G=D===_n2;function F(C,K){if(!D)return;if(G)throw C;var H=D;D=null,H(C,K)}function I(C){var K=C.lastIndexOf("google/protobuf/");if(K>-1){var H=C.substring(K);if(H in j31)return H}return null}function Y(C,K){try{if(s_.isString(K)&&K.charAt(0)==="{")K=JSON.parse(K);if(!s_.isString(K))Z.setOptions(K.options).addJSON(K.nested);else{AW0.filename=C;var H=AW0(K,Z,Q),z,$=0;if(H.imports){for(;$<H.imports.length;++$)if(z=I(H.imports[$])||Z.resolvePath(C,H.imports[$]))W(z)}if(H.weakImports){for($=0;$<H.weakImports.length;++$)if(z=I(H.weakImports[$])||Z.resolvePath(C,H.weakImports[$]))W(z,!0)}}}catch(L){F(L)}if(!G&&!J)F(null,Z)}function W(C,K){if(C=I(C)||C,Z.files.indexOf(C)>-1)return;if(Z.files.push(C),C in j31){if(G)Y(C,j31[C]);else++J,setTimeout(function(){--J,Y(C,j31[C])});return}if(G){var H;try{H=s_.fs.readFileSync(C).toString("utf8")}catch(z){if(!K)F(z);return}Y(C,H)}else++J,Z.fetch(C,function(z,$){if(--J,!D)return;if(z){if(!K)F(z);else if(!J)F(null,Z);return}Y(C,$)})}var J=0;if(s_.isString(B))B=[B];for(var X=0,V;X<B.length;++X)if(V=Z.resolvePath("",B[X]))W(V);if(G)return Z;if(!J)F(null,Z);return};TK.prototype.loadSync=function A(B,Q){if(!s_.isNode)throw Error("not supported");return this.load(B,Q,_n2)};TK.prototype.resolveAll=function A(){if(this.deferred.length)throw Error("unresolvable extensions: "+this.deferred.map(function(B){return"'extend "+B.extend+"' in "+B.parent.fullName}).join(", "));return uT1.prototype.resolveAll.call(this)};var gT1=/^[A-Z]/;function jn2(A,B){var Q=B.parent.lookup(B.extend);if(Q){var D=new BW0(B.fullName,B.id,B.type,B.rule,void 0,B.options);if(Q.get(D.name))return!0;return D.declaringField=B,B.extensionField=D,Q.add(D),!0}return!1}TK.prototype._handleAdd=function A(B){if(B instanceof BW0){if(B.extend!==void 0&&!B.extensionField){if(!jn2(this,B))this.deferred.push(B)}}else if(B instanceof kn2){if(gT1.test(B.name))B.parent[B.name]=B.values}else if(!(B instanceof TW6)){if(B instanceof yn2)for(var Q=0;Q<this.deferred.length;)if(jn2(this,this.deferred[Q]))this.deferred.splice(Q,1);else++Q;for(var D=0;D<B.nestedArray.length;++D)this._handleAdd(B._nestedArray[D]);if(gT1.test(B.name))B.parent[B.name]=B}};TK.prototype._handleRemove=function A(B){if(B instanceof BW0){if(B.extend!==void 0)if(B.extensionField)B.extensionField.parent.remove(B.extensionField),B.extensionField=null;else{var Q=this.deferred.indexOf(B);if(Q>-1)this.deferred.splice(Q,1)}}else if(B instanceof kn2){if(gT1.test(B.name))delete B.parent[B.name]}else if(B instanceof uT1){for(var D=0;D<B.nestedArray.length;++D)this._handleRemove(B._nestedArray[D]);if(gT1.test(B.name))delete B.parent[B.name]}};TK._configure=function(A,B,Q){yn2=A,AW0=B,j31=Q}});
var n31=E((D3)=>{Object.defineProperty(D3,"__esModule",{value:!0});D3.experimental=D3.ServerInterceptingCall=D3.ResponderBuilder=D3.ServerListenerBuilder=D3.addAdminServicesToServer=D3.getChannelzHandlers=D3.getChannelzServiceDefinition=D3.InterceptorConfigurationError=D3.InterceptingCall=D3.RequesterBuilder=D3.ListenerBuilder=D3.StatusBuilder=D3.getClientChannel=D3.ServerCredentials=D3.Server=D3.setLogVerbosity=D3.setLogger=D3.load=D3.loadObject=D3.CallCredentials=D3.ChannelCredentials=D3.waitForClientReady=D3.closeClient=D3.Channel=D3.makeGenericClientConstructor=D3.makeClientConstructor=D3.loadPackageDefinition=D3.Client=D3.compressionAlgorithms=D3.propagate=D3.connectivityState=D3.status=D3.logVerbosity=D3.Metadata=D3.credentials=void 0;var MP1=VT1();Object.defineProperty(D3,"CallCredentials",{enumerable:!0,get:function(){return MP1.CallCredentials}});var HH6=OY0();Object.defineProperty(D3,"Channel",{enumerable:!0,get:function(){return HH6.ChannelImplementation}});var zH6=bW0();Object.defineProperty(D3,"compressionAlgorithms",{enumerable:!0,get:function(){return zH6.CompressionAlgorithms}});var EH6=VE();Object.defineProperty(D3,"connectivityState",{enumerable:!0,get:function(){return EH6.ConnectivityState}});var RP1=C31();Object.defineProperty(D3,"ChannelCredentials",{enumerable:!0,get:function(){return RP1.ChannelCredentials}});var co2=RY0();Object.defineProperty(D3,"Client",{enumerable:!0,get:function(){return co2.Client}});var RJ0=S6();Object.defineProperty(D3,"logVerbosity",{enumerable:!0,get:function(){return RJ0.LogVerbosity}});Object.defineProperty(D3,"status",{enumerable:!0,get:function(){return RJ0.Status}});Object.defineProperty(D3,"propagate",{enumerable:!0,get:function(){return RJ0.Propagate}});var lo2=D7(),OJ0=PY0();Object.defineProperty(D3,"loadPackageDefinition",{enumerable:!0,get:function(){return OJ0.loadPackageDefinition}});Object.defineProperty(D3,"makeClientConstructor",{enumerable:!0,get:function(){return OJ0.makeClientConstructor}});Object.defineProperty(D3,"makeGenericClientConstructor",{enumerable:!0,get:function(){return OJ0.makeClientConstructor}});var UH6=tW();Object.defineProperty(D3,"Metadata",{enumerable:!0,get:function(){return UH6.Metadata}});var wH6=sr2();Object.defineProperty(D3,"Server",{enumerable:!0,get:function(){return wH6.Server}});var $H6=KP1();Object.defineProperty(D3,"ServerCredentials",{enumerable:!0,get:function(){return $H6.ServerCredentials}});var qH6=er2();Object.defineProperty(D3,"StatusBuilder",{enumerable:!0,get:function(){return qH6.StatusBuilder}});D3.credentials={combineChannelCredentials:(A,...B)=>{return B.reduce((Q,D)=>Q.compose(D),A)},combineCallCredentials:(A,...B)=>{return B.reduce((Q,D)=>Q.compose(D),A)},createInsecure:RP1.ChannelCredentials.createInsecure,createSsl:RP1.ChannelCredentials.createSsl,createFromSecureContext:RP1.ChannelCredentials.createFromSecureContext,createFromMetadataGenerator:MP1.CallCredentials.createFromMetadataGenerator,createFromGoogleCredential:MP1.CallCredentials.createFromGoogleCredential,createEmpty:MP1.CallCredentials.createEmpty};var NH6=(A)=>A.close();D3.closeClient=NH6;var LH6=(A,B,Q)=>A.waitForReady(B,Q);D3.waitForClientReady=LH6;var MH6=(A,B)=>{throw new Error("Not available in this library. Use @grpc/proto-loader and loadPackageDefinition instead")};D3.loadObject=MH6;var RH6=(A,B,Q)=>{throw new Error("Not available in this library. Use @grpc/proto-loader and loadPackageDefinition instead")};D3.load=RH6;var OH6=(A)=>{lo2.setLogger(A)};D3.setLogger=OH6;var TH6=(A)=>{lo2.setLoggerVerbosity(A)};D3.setLogVerbosity=TH6;var PH6=(A)=>{return co2.Client.prototype.getChannel.call(A)};D3.getClientChannel=PH6;var OP1=LY0();Object.defineProperty(D3,"ListenerBuilder",{enumerable:!0,get:function(){return OP1.ListenerBuilder}});Object.defineProperty(D3,"RequesterBuilder",{enumerable:!0,get:function(){return OP1.RequesterBuilder}});Object.defineProperty(D3,"InterceptingCall",{enumerable:!0,get:function(){return OP1.InterceptingCall}});Object.defineProperty(D3,"InterceptorConfigurationError",{enumerable:!0,get:function(){return OP1.InterceptorConfigurationError}});var po2=hu();Object.defineProperty(D3,"getChannelzServiceDefinition",{enumerable:!0,get:function(){return po2.getChannelzServiceDefinition}});Object.defineProperty(D3,"getChannelzHandlers",{enumerable:!0,get:function(){return po2.getChannelzHandlers}});var SH6=LT1();Object.defineProperty(D3,"addAdminServicesToServer",{enumerable:!0,get:function(){return SH6.addAdminServicesToServer}});var TJ0=DJ0();Object.defineProperty(D3,"ServerListenerBuilder",{enumerable:!0,get:function(){return TJ0.ServerListenerBuilder}});Object.defineProperty(D3,"ResponderBuilder",{enumerable:!0,get:function(){return TJ0.ResponderBuilder}});Object.defineProperty(D3,"ServerInterceptingCall",{enumerable:!0,get:function(){return TJ0.ServerInterceptingCall}});var jH6=XJ0();D3.experimental=jH6;var kH6=OW0(),yH6=qo2(),_H6=To2(),xH6=UP1(),vH6=xo2(),bH6=do2(),fH6=hu();(()=>{kH6.setup(),yH6.setup(),_H6.setup(),xH6.setup(),vH6.setup(),bH6.setup(),fH6.setup()})()});
var nL=E((Yp2)=>{Object.defineProperty(Yp2,"__esModule",{value:!0});Yp2.registerResolver=BF6;Yp2.registerDefaultScheme=QF6;Yp2.createResolver=DF6;Yp2.getDefaultAuthority=ZF6;Yp2.mapUriDefaultScheme=GF6;var CY0=SV(),lo={},VY0=null;function BF6(A,B){lo[A]=B}function QF6(A){VY0=A}function DF6(A,B,Q){if(A.scheme!==void 0&&A.scheme in lo)return new lo[A.scheme](A,B,Q);else throw new Error(`No resolver could be created for target ${CY0.uriToString(A)}`)}function ZF6(A){if(A.scheme!==void 0&&A.scheme in lo)return lo[A.scheme].getDefaultAuthority(A);else throw new Error(`Invalid target ${CY0.uriToString(A)}`)}function GF6(A){if(A.scheme===void 0||!(A.scheme in lo))if(VY0!==null)return{scheme:VY0,authority:void 0,path:CY0.uriToString(A)};else return null;return A}});
var n_=E((f_5,Wn2)=>{Wn2.exports=KE;var PT1=vu();((KE.prototype=Object.create(PT1.prototype)).constructor=KE).className="Field";var In2=sw(),Yn2=xu(),dG=KI(),fY0,FW6=/^required|optional|repeated$/;KE.fromJSON=function A(B,Q){return new KE(B,Q.id,Q.type,Q.rule,Q.extend,Q.options,Q.comment)};function KE(A,B,Q,D,Z,G,F){if(dG.isObject(D))F=Z,G=D,D=Z=void 0;else if(dG.isObject(Z))F=G,G=Z,Z=void 0;if(PT1.call(this,A,G),!dG.isInteger(B)||B<0)throw TypeError("id must be a non-negative integer");if(!dG.isString(Q))throw TypeError("type must be a string");if(D!==void 0&&!FW6.test(D=D.toString().toLowerCase()))throw TypeError("rule must be a string rule");if(Z!==void 0&&!dG.isString(Z))throw TypeError("extend must be a string");if(D==="proto3_optional")D="optional";this.rule=D&&D!=="optional"?D:void 0,this.type=Q,this.id=B,this.extend=Z||void 0,this.required=D==="required",this.optional=!this.required,this.repeated=D==="repeated",this.map=!1,this.message=null,this.partOf=null,this.typeDefault=null,this.defaultValue=null,this.long=dG.Long?Yn2.long[Q]!==void 0:!1,this.bytes=Q==="bytes",this.resolvedType=null,this.extensionField=null,this.declaringField=null,this._packed=null,this.comment=F}Object.defineProperty(KE.prototype,"packed",{get:function(){if(this._packed===null)this._packed=this.getOption("packed")!==!1;return this._packed}});KE.prototype.setOption=function A(B,Q,D){if(B==="packed")this._packed=null;return PT1.prototype.setOption.call(this,B,Q,D)};KE.prototype.toJSON=function A(B){var Q=B?Boolean(B.keepComments):!1;return dG.toObject(["rule",this.rule!=="optional"&&this.rule||void 0,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",Q?this.comment:void 0])};KE.prototype.resolve=function A(){if(this.resolved)return this;if((this.typeDefault=Yn2.defaults[this.type])===void 0)if(this.resolvedType=(this.declaringField?this.declaringField.parent:this.parent).lookupTypeOrEnum(this.type),this.resolvedType instanceof fY0)this.typeDefault=null;else this.typeDefault=this.resolvedType.values[Object.keys(this.resolvedType.values)[0]];else if(this.options&&this.options.proto3_optional)this.typeDefault=null;if(this.options&&this.options.default!=null){if(this.typeDefault=this.options.default,this.resolvedType instanceof In2&&typeof this.typeDefault==="string")this.typeDefault=this.resolvedType.values[this.typeDefault]}if(this.options){if(this.options.packed===!0||this.options.packed!==void 0&&this.resolvedType&&!(this.resolvedType instanceof In2))delete this.options.packed;if(!Object.keys(this.options).length)this.options=void 0}if(this.long){if(this.typeDefault=dG.Long.fromNumber(this.typeDefault,this.type.charAt(0)==="u"),Object.freeze)Object.freeze(this.typeDefault)}else if(this.bytes&&typeof this.typeDefault==="string"){var B;if(dG.base64.test(this.typeDefault))dG.base64.decode(this.typeDefault,B=dG.newBuffer(dG.base64.length(this.typeDefault)),0);else dG.utf8.write(this.typeDefault,B=dG.newBuffer(dG.utf8.length(this.typeDefault)),0);this.typeDefault=B}if(this.map)this.defaultValue=dG.emptyObject;else if(this.repeated)this.defaultValue=dG.emptyArray;else this.defaultValue=this.typeDefault;if(this.parent instanceof fY0)this.parent.ctor.prototype[this.name]=this.defaultValue;return PT1.prototype.resolve.call(this)};KE.d=function A(B,Q,D,Z){if(typeof Q==="function")Q=dG.decorateType(Q).name;else if(Q&&typeof Q==="object")Q=dG.decorateEnum(Q).name;return function G(F,I){dG.decorateType(F.constructor).add(new KE(I,B,Q,D,{default:Z}))}};KE._configure=function A(B){fY0=B}});
var na2=E((pa2)=>{Object.defineProperty(pa2,"__esModule",{value:!0});pa2.Subchannel=void 0;var B3=VE(),$X6=H31(),qW0=D7(),eT1=S6(),qX6=SV(),NX6=CE(),QM=hu(),LX6="subchannel",MX6=2147483647;class la2{constructor(A,B,Q,D,Z){var G;this.channelTarget=A,this.subchannelAddress=B,this.options=Q,this.connector=Z,this.connectivityState=B3.ConnectivityState.IDLE,this.transport=null,this.continueConnecting=!1,this.stateListeners=new Set,this.refcount=0,this.channelzEnabled=!0;let F={initialDelay:Q["grpc.initial_reconnect_backoff_ms"],maxDelay:Q["grpc.max_reconnect_backoff_ms"]};if(this.backoffTimeout=new $X6.BackoffTimeout(()=>{this.handleBackoffTimer()},F),this.backoffTimeout.unref(),this.subchannelAddressString=NX6.subchannelAddressToString(B),this.keepaliveTime=(G=Q["grpc.keepalive_time_ms"])!==null&&G!==void 0?G:-1,Q["grpc.enable_channelz"]===0)this.channelzEnabled=!1,this.channelzTrace=new QM.ChannelzTraceStub,this.callTracker=new QM.ChannelzCallTrackerStub,this.childrenTracker=new QM.ChannelzChildrenTrackerStub,this.streamTracker=new QM.ChannelzCallTrackerStub;else this.channelzTrace=new QM.ChannelzTrace,this.callTracker=new QM.ChannelzCallTracker,this.childrenTracker=new QM.ChannelzChildrenTracker,this.streamTracker=new QM.ChannelzCallTracker;this.channelzRef=QM.registerChannelzSubchannel(this.subchannelAddressString,()=>this.getChannelzInfo(),this.channelzEnabled),this.channelzTrace.addTrace("CT_INFO","Subchannel created"),this.trace("Subchannel constructed with options "+JSON.stringify(Q,void 0,2)),this.secureConnector=D._createSecureConnector(A,Q)}getChannelzInfo(){return{state:this.connectivityState,trace:this.channelzTrace,callTracker:this.callTracker,children:this.childrenTracker.getChildLists(),target:this.subchannelAddressString}}trace(A){qW0.trace(eT1.LogVerbosity.DEBUG,LX6,"("+this.channelzRef.id+") "+this.subchannelAddressString+" "+A)}refTrace(A){qW0.trace(eT1.LogVerbosity.DEBUG,"subchannel_refcount","("+this.channelzRef.id+") "+this.subchannelAddressString+" "+A)}handleBackoffTimer(){if(this.continueConnecting)this.transitionToState([B3.ConnectivityState.TRANSIENT_FAILURE],B3.ConnectivityState.CONNECTING);else this.transitionToState([B3.ConnectivityState.TRANSIENT_FAILURE],B3.ConnectivityState.IDLE)}startBackoff(){this.backoffTimeout.runOnce()}stopBackoff(){this.backoffTimeout.stop(),this.backoffTimeout.reset()}startConnectingInternal(){let A=this.options;if(A["grpc.keepalive_time_ms"]){let B=Math.min(this.keepaliveTime,MX6);A=Object.assign(Object.assign({},A),{"grpc.keepalive_time_ms":B})}this.connector.connect(this.subchannelAddress,this.secureConnector,A).then((B)=>{if(this.transitionToState([B3.ConnectivityState.CONNECTING],B3.ConnectivityState.READY)){if(this.transport=B,this.channelzEnabled)this.childrenTracker.refChild(B.getChannelzRef());B.addDisconnectListener((Q)=>{if(this.transitionToState([B3.ConnectivityState.READY],B3.ConnectivityState.IDLE),Q&&this.keepaliveTime>0)this.keepaliveTime*=2,qW0.log(eT1.LogVerbosity.ERROR,`Connection to ${qX6.uriToString(this.channelTarget)} at ${this.subchannelAddressString} rejected by server because of excess pings. Increasing ping interval to ${this.keepaliveTime} ms`)})}else B.shutdown()},(B)=>{this.transitionToState([B3.ConnectivityState.CONNECTING],B3.ConnectivityState.TRANSIENT_FAILURE,`${B}`)})}transitionToState(A,B,Q){var D,Z;if(A.indexOf(this.connectivityState)===-1)return!1;if(Q)this.trace(B3.ConnectivityState[this.connectivityState]+" -> "+B3.ConnectivityState[B]+' with error "'+Q+'"');else this.trace(B3.ConnectivityState[this.connectivityState]+" -> "+B3.ConnectivityState[B]);if(this.channelzEnabled)this.channelzTrace.addTrace("CT_INFO","Connectivity state change to "+B3.ConnectivityState[B]);let G=this.connectivityState;switch(this.connectivityState=B,B){case B3.ConnectivityState.READY:this.stopBackoff();break;case B3.ConnectivityState.CONNECTING:this.startBackoff(),this.startConnectingInternal(),this.continueConnecting=!1;break;case B3.ConnectivityState.TRANSIENT_FAILURE:if(this.channelzEnabled&&this.transport)this.childrenTracker.unrefChild(this.transport.getChannelzRef());if((D=this.transport)===null||D===void 0||D.shutdown(),this.transport=null,!this.backoffTimeout.isRunning())process.nextTick(()=>{this.handleBackoffTimer()});break;case B3.ConnectivityState.IDLE:if(this.channelzEnabled&&this.transport)this.childrenTracker.unrefChild(this.transport.getChannelzRef());(Z=this.transport)===null||Z===void 0||Z.shutdown(),this.transport=null;break;default:throw new Error(`Invalid state: unknown ConnectivityState ${B}`)}for(let F of this.stateListeners)F(this,G,B,this.keepaliveTime,Q);return!0}ref(){this.refTrace("refcount "+this.refcount+" -> "+(this.refcount+1)),this.refcount+=1}unref(){if(this.refTrace("refcount "+this.refcount+" -> "+(this.refcount-1)),this.refcount-=1,this.refcount===0)this.channelzTrace.addTrace("CT_INFO","Shutting down"),QM.unregisterChannelzRef(this.channelzRef),this.secureConnector.destroy(),process.nextTick(()=>{this.transitionToState([B3.ConnectivityState.CONNECTING,B3.ConnectivityState.READY],B3.ConnectivityState.IDLE)})}unrefIfOneRef(){if(this.refcount===1)return this.unref(),!0;return!1}createCall(A,B,Q,D){if(!this.transport)throw new Error("Cannot create call, subchannel not READY");let Z;if(this.channelzEnabled)this.callTracker.addCallStarted(),this.streamTracker.addCallStarted(),Z={onCallEnd:(G)=>{if(G.code===eT1.Status.OK)this.callTracker.addCallSucceeded();else this.callTracker.addCallFailed()}};else Z={};return this.transport.createCall(A,B,Q,D,Z)}startConnecting(){process.nextTick(()=>{if(!this.transitionToState([B3.ConnectivityState.IDLE],B3.ConnectivityState.CONNECTING)){if(this.connectivityState===B3.ConnectivityState.TRANSIENT_FAILURE)this.continueConnecting=!0}})}getConnectivityState(){return this.connectivityState}addConnectivityStateListener(A){this.stateListeners.add(A)}removeConnectivityStateListener(A){this.stateListeners.delete(A)}resetBackoff(){process.nextTick(()=>{this.backoffTimeout.reset(),this.transitionToState([B3.ConnectivityState.TRANSIENT_FAILURE],B3.ConnectivityState.CONNECTING)})}getAddress(){return this.subchannelAddressString}getChannelzRef(){return this.channelzRef}isHealthy(){return!0}addHealthStateWatcher(A){}removeHealthStateWatcher(A){}getRealSubchannel(){return this}realSubchannelEquals(A){return A.getRealSubchannel()===this}throttleKeepalive(A){if(A>this.keepaliveTime)this.keepaliveTime=A}getCallCredentials(){return this.secureConnector.getCallCredentials()}}pa2.Subchannel=la2});
var ns2=E((ps2)=>{Object.defineProperty(ps2,"__esModule",{value:!0});ps2.LoadBalancingCall=void 0;var ds2=VE(),IP1=S6(),cs2=u31(),YP1=tW(),m31=p_(),iV6=SV(),nV6=D7(),gW0=FP1(),aV6=J1("http2"),sV6="load_balancing_call";class ls2{constructor(A,B,Q,D,Z,G,F){var I,Y;this.channel=A,this.callConfig=B,this.methodName=Q,this.host=D,this.credentials=Z,this.deadline=G,this.callNumber=F,this.child=null,this.readPending=!1,this.pendingMessage=null,this.pendingHalfClose=!1,this.ended=!1,this.metadata=null,this.listener=null,this.onCallEnded=null,this.childStartTime=null;let W=this.methodName.split("/"),J="";if(W.length>=2)J=W[1];let X=(Y=(I=iV6.splitHostPort(this.host))===null||I===void 0?void 0:I.host)!==null&&Y!==void 0?Y:"localhost";this.serviceUrl=`https://${X}/${J}`,this.startTime=new Date}getDeadlineInfo(){var A,B;let Q=[];if(this.childStartTime){if(this.childStartTime>this.startTime){if((A=this.metadata)===null||A===void 0?void 0:A.getOptions().waitForReady)Q.push("wait_for_ready");Q.push(`LB pick: ${cs2.formatDateDifference(this.startTime,this.childStartTime)}`)}return Q.push(...this.child.getDeadlineInfo()),Q}else{if((B=this.metadata)===null||B===void 0?void 0:B.getOptions().waitForReady)Q.push("wait_for_ready");Q.push("Waiting for LB pick")}return Q}trace(A){nV6.trace(IP1.LogVerbosity.DEBUG,sV6,"["+this.callNumber+"] "+A)}outputStatus(A,B){var Q,D;if(!this.ended){this.ended=!0,this.trace("ended with status: code="+A.code+' details="'+A.details+'" start time='+this.startTime.toISOString());let Z=Object.assign(Object.assign({},A),{progress:B});(Q=this.listener)===null||Q===void 0||Q.onReceiveStatus(Z),(D=this.onCallEnded)===null||D===void 0||D.call(this,Z.code)}}doPick(){var A,B;if(this.ended)return;if(!this.metadata)throw new Error("doPick called before start");this.trace("Pick called");let Q=this.metadata.clone(),D=this.channel.doPick(Q,this.callConfig.pickInformation),Z=D.subchannel?"("+D.subchannel.getChannelzRef().id+") "+D.subchannel.getAddress():""+D.subchannel;switch(this.trace("Pick result: "+m31.PickResultType[D.pickResultType]+" subchannel: "+Z+" status: "+((A=D.status)===null||A===void 0?void 0:A.code)+" "+((B=D.status)===null||B===void 0?void 0:B.details)),D.pickResultType){case m31.PickResultType.COMPLETE:this.credentials.compose(D.subchannel.getCallCredentials()).generateMetadata({method_name:this.methodName,service_url:this.serviceUrl}).then((Y)=>{var W;if(this.ended){this.trace("Credentials metadata generation finished after call ended");return}if(Q.merge(Y),Q.get("authorization").length>1)this.outputStatus({code:IP1.Status.INTERNAL,details:'"authorization" metadata cannot have multiple values',metadata:new YP1.Metadata},"PROCESSED");if(D.subchannel.getConnectivityState()!==ds2.ConnectivityState.READY){this.trace("Picked subchannel "+Z+" has state "+ds2.ConnectivityState[D.subchannel.getConnectivityState()]+" after getting credentials metadata. Retrying pick"),this.doPick();return}if(this.deadline!==1/0)Q.set("grpc-timeout",cs2.getDeadlineTimeoutString(this.deadline));try{this.child=D.subchannel.getRealSubchannel().createCall(Q,this.host,this.methodName,{onReceiveMetadata:(J)=>{this.trace("Received metadata"),this.listener.onReceiveMetadata(J)},onReceiveMessage:(J)=>{this.trace("Received message"),this.listener.onReceiveMessage(J)},onReceiveStatus:(J)=>{if(this.trace("Received status"),J.rstCode===aV6.constants.NGHTTP2_REFUSED_STREAM)this.outputStatus(J,"REFUSED");else this.outputStatus(J,"PROCESSED")}}),this.childStartTime=new Date}catch(J){this.trace("Failed to start call on picked subchannel "+Z+" with error "+J.message),this.outputStatus({code:IP1.Status.INTERNAL,details:"Failed to start HTTP/2 stream with error "+J.message,metadata:new YP1.Metadata},"NOT_STARTED");return}if((W=D.onCallStarted)===null||W===void 0||W.call(D),this.onCallEnded=D.onCallEnded,this.trace("Created child call ["+this.child.getCallNumber()+"]"),this.readPending)this.child.startRead();if(this.pendingMessage)this.child.sendMessageWithContext(this.pendingMessage.context,this.pendingMessage.message);if(this.pendingHalfClose)this.child.halfClose()},(Y)=>{let{code:W,details:J}=gW0.restrictControlPlaneStatusCode(typeof Y.code==="number"?Y.code:IP1.Status.UNKNOWN,`Getting metadata from plugin failed with error: ${Y.message}`);this.outputStatus({code:W,details:J,metadata:new YP1.Metadata},"PROCESSED")});break;case m31.PickResultType.DROP:let{code:F,details:I}=gW0.restrictControlPlaneStatusCode(D.status.code,D.status.details);setImmediate(()=>{this.outputStatus({code:F,details:I,metadata:D.status.metadata},"DROP")});break;case m31.PickResultType.TRANSIENT_FAILURE:if(this.metadata.getOptions().waitForReady)this.channel.queueCallForPick(this);else{let{code:Y,details:W}=gW0.restrictControlPlaneStatusCode(D.status.code,D.status.details);setImmediate(()=>{this.outputStatus({code:Y,details:W,metadata:D.status.metadata},"PROCESSED")})}break;case m31.PickResultType.QUEUE:this.channel.queueCallForPick(this)}}cancelWithStatus(A,B){var Q;this.trace("cancelWithStatus code: "+A+' details: "'+B+'"'),(Q=this.child)===null||Q===void 0||Q.cancelWithStatus(A,B),this.outputStatus({code:A,details:B,metadata:new YP1.Metadata},"PROCESSED")}getPeer(){var A,B;return(B=(A=this.child)===null||A===void 0?void 0:A.getPeer())!==null&&B!==void 0?B:this.channel.getTarget()}start(A,B){this.trace("start called"),this.listener=B,this.metadata=A,this.doPick()}sendMessageWithContext(A,B){if(this.trace("write() called with message of length "+B.length),this.child)this.child.sendMessageWithContext(A,B);else this.pendingMessage={context:A,message:B}}startRead(){if(this.trace("startRead called"),this.child)this.child.startRead();else this.readPending=!0}halfClose(){if(this.trace("halfClose called"),this.child)this.child.halfClose();else this.pendingHalfClose=!0}setCredentials(A){throw new Error("Method not implemented.")}getCallNumber(){return this.callNumber}}ps2.LoadBalancingCall=ls2});
var oo=E((h_5,Vn2)=>{Vn2.exports=HE;var jT1=vu();((HE.prototype=Object.create(jT1.prototype)).constructor=HE).className="OneOf";var Jn2=n_(),ST1=KI();function HE(A,B,Q,D){if(!Array.isArray(B))Q=B,B=void 0;if(jT1.call(this,A,Q),!(B===void 0||Array.isArray(B)))throw TypeError("fieldNames must be an Array");this.oneof=B||[],this.fieldsArray=[],this.comment=D}HE.fromJSON=function A(B,Q){return new HE(B,Q.oneof,Q.options,Q.comment)};HE.prototype.toJSON=function A(B){var Q=B?Boolean(B.keepComments):!1;return ST1.toObject(["options",this.options,"oneof",this.oneof,"comment",Q?this.comment:void 0])};function Xn2(A){if(A.parent){for(var B=0;B<A.fieldsArray.length;++B)if(!A.fieldsArray[B].parent)A.parent.add(A.fieldsArray[B])}}HE.prototype.add=function A(B){if(!(B instanceof Jn2))throw TypeError("field must be a Field");if(B.parent&&B.parent!==this.parent)B.parent.remove(B);return this.oneof.push(B.name),this.fieldsArray.push(B),B.partOf=this,Xn2(this),this};HE.prototype.remove=function A(B){if(!(B instanceof Jn2))throw TypeError("field must be a Field");var Q=this.fieldsArray.indexOf(B);if(Q<0)throw Error(B+" is not a member of "+this);if(this.fieldsArray.splice(Q,1),Q=this.oneof.indexOf(B.name),Q>-1)this.oneof.splice(Q,1);return B.partOf=null,this};HE.prototype.onAdd=function A(B){jT1.prototype.onAdd.call(this,B);var Q=this;for(var D=0;D<this.oneof.length;++D){var Z=B.get(this.oneof[D]);if(Z&&!Z.partOf)Z.partOf=Q,Q.fieldsArray.push(Z)}Xn2(this)};HE.prototype.onRemove=function A(B){for(var Q=0,D;Q<this.fieldsArray.length;++Q)if((D=this.fieldsArray[Q]).parent)D.parent.remove(D);jT1.prototype.onRemove.call(this,B)};HE.d=function A(){var B=new Array(arguments.length),Q=0;while(Q<arguments.length)B[Q]=arguments[Q++];return function D(Z,G){ST1.decorateType(Z.constructor).add(new HE(G,B)),Object.defineProperty(Z,G,{get:ST1.oneOfGetter(B),set:ST1.oneOfSetter(B)})}}});
var pT1=E((Zx5,Da2)=>{var r_=Da2.exports=ln2();r_.build="full";r_.tokenize=IW0();r_.parse=en2();r_.common=Qa2();r_.Root._configure(r_.Type,r_.parse,r_.common)});
var pY0=E((p_5,Mn2)=>{Mn2.exports=zW6;var KW6=sw(),cY0=KI();function EE(A,B){return A.name+": "+B+(A.repeated&&B!=="array"?"[]":A.map&&B!=="object"?"{k:"+A.keyType+"}":"")+" expected"}function lY0(A,B,Q,D){if(B.resolvedType)if(B.resolvedType instanceof KW6){A("switch(%s){",D)("default:")("return%j",EE(B,"enum value"));for(var Z=Object.keys(B.resolvedType.values),G=0;G<Z.length;++G)A("case %i:",B.resolvedType.values[Z[G]]);A("break")("}")}else A("{")("var e=types[%i].verify(%s);",Q,D)("if(e)")("return%j+e",B.name+".")("}");else switch(B.type){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":A("if(!util.isInteger(%s))",D)("return%j",EE(B,"integer"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":A("if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))",D,D,D,D)("return%j",EE(B,"integer|Long"));break;case"float":case"double":A('if(typeof %s!=="number")',D)("return%j",EE(B,"number"));break;case"bool":A('if(typeof %s!=="boolean")',D)("return%j",EE(B,"boolean"));break;case"string":A("if(!util.isString(%s))",D)("return%j",EE(B,"string"));break;case"bytes":A('if(!(%s&&typeof %s.length==="number"||util.isString(%s)))',D,D,D)("return%j",EE(B,"buffer"));break}return A}function HW6(A,B,Q){switch(B.keyType){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":A("if(!util.key32Re.test(%s))",Q)("return%j",EE(B,"integer key"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":A("if(!util.key64Re.test(%s))",Q)("return%j",EE(B,"integer|Long key"));break;case"bool":A("if(!util.key2Re.test(%s))",Q)("return%j",EE(B,"boolean key"));break}return A}function zW6(A){var B=cY0.codegen(["m"],A.name+"$verify")('if(typeof m!=="object"||m===null)')("return%j","object expected"),Q=A.oneofsArray,D={};if(Q.length)B("var p={}");for(var Z=0;Z<A.fieldsArray.length;++Z){var G=A._fieldsArray[Z].resolve(),F="m"+cY0.safeProp(G.name);if(G.optional)B("if(%s!=null&&m.hasOwnProperty(%j)){",F,G.name);if(G.map)B("if(!util.isObject(%s))",F)("return%j",EE(G,"object"))("var k=Object.keys(%s)",F)("for(var i=0;i<k.length;++i){"),HW6(B,G,"k[i]"),lY0(B,G,Z,F+"[k[i]]")("}");else if(G.repeated)B("if(!Array.isArray(%s))",F)("return%j",EE(G,"array"))("for(var i=0;i<%s.length;++i){",F),lY0(B,G,Z,F+"[i]")("}");else{if(G.partOf){var I=cY0.safeProp(G.partOf.name);if(D[G.partOf.name]===1)B("if(p%s===1)",I)("return%j",G.partOf.name+": multiple values");D[G.partOf.name]=1,B("p%s=1",I)}lY0(B,G,Z,F)}if(G.optional)B("}")}return B("return null")}});
var p_=E((Mp2)=>{Object.defineProperty(Mp2,"__esModule",{value:!0});Mp2.QueuePicker=Mp2.UnavailablePicker=Mp2.PickResultType=void 0;var pF6=tW(),iF6=S6(),wT1;(function(A){A[A.COMPLETE=0]="COMPLETE",A[A.QUEUE=1]="QUEUE",A[A.TRANSIENT_FAILURE=2]="TRANSIENT_FAILURE",A[A.DROP=3]="DROP"})(wT1||(Mp2.PickResultType=wT1={}));class Np2{constructor(A){this.status=Object.assign({code:iF6.Status.UNAVAILABLE,details:"No connection established",metadata:new pF6.Metadata},A)}pick(A){return{pickResultType:wT1.TRANSIENT_FAILURE,subchannel:null,status:this.status,onCallStarted:null,onCallEnded:null}}}Mp2.UnavailablePicker=Np2;class Lp2{constructor(A,B){this.loadBalancer=A,this.childPicker=B,this.calledExitIdle=!1}pick(A){if(!this.calledExitIdle)process.nextTick(()=>{this.loadBalancer.exitIdle()}),this.calledExitIdle=!0;if(this.childPicker)return this.childPicker.pick(A);else return{pickResultType:wT1.QUEUE,subchannel:null,status:null,onCallStarted:null,onCallEnded:null}}}Mp2.QueuePicker=Lp2});
var qT1=E((Sp2)=>{Object.defineProperty(Sp2,"__esModule",{value:!0});Sp2.ChildLoadBalancerHandler=void 0;var DI6=yu(),ZI6=VE(),GI6="child_load_balancer_helper";class Pp2{constructor(A){this.channelControlHelper=A,this.currentChild=null,this.pendingChild=null,this.latestConfig=null,this.ChildPolicyHelper=class{constructor(B){this.parent=B,this.child=null}createSubchannel(B,Q){return this.parent.channelControlHelper.createSubchannel(B,Q)}updateState(B,Q,D){var Z;if(this.calledByPendingChild()){if(B===ZI6.ConnectivityState.CONNECTING)return;(Z=this.parent.currentChild)===null||Z===void 0||Z.destroy(),this.parent.currentChild=this.parent.pendingChild,this.parent.pendingChild=null}else if(!this.calledByCurrentChild())return;this.parent.channelControlHelper.updateState(B,Q,D)}requestReresolution(){var B;let Q=(B=this.parent.pendingChild)!==null&&B!==void 0?B:this.parent.currentChild;if(this.child===Q)this.parent.channelControlHelper.requestReresolution()}setChild(B){this.child=B}addChannelzChild(B){this.parent.channelControlHelper.addChannelzChild(B)}removeChannelzChild(B){this.parent.channelControlHelper.removeChannelzChild(B)}calledByPendingChild(){return this.child===this.parent.pendingChild}calledByCurrentChild(){return this.child===this.parent.currentChild}}}configUpdateRequiresNewPolicyInstance(A,B){return A.getLoadBalancerName()!==B.getLoadBalancerName()}updateAddressList(A,B,Q){let D;if(this.currentChild===null||this.latestConfig===null||this.configUpdateRequiresNewPolicyInstance(this.latestConfig,B)){let Z=new this.ChildPolicyHelper(this),G=DI6.createLoadBalancer(B,Z);if(Z.setChild(G),this.currentChild===null)this.currentChild=G,D=this.currentChild;else{if(this.pendingChild)this.pendingChild.destroy();this.pendingChild=G,D=this.pendingChild}}else if(this.pendingChild===null)D=this.currentChild;else D=this.pendingChild;this.latestConfig=B,D.updateAddressList(A,B,Q)}exitIdle(){if(this.currentChild){if(this.currentChild.exitIdle(),this.pendingChild)this.pendingChild.exitIdle()}}resetBackoff(){if(this.currentChild){if(this.currentChild.resetBackoff(),this.pendingChild)this.pendingChild.resetBackoff()}}destroy(){if(this.currentChild)this.currentChild.destroy(),this.currentChild=null;if(this.pendingChild)this.pendingChild.destroy(),this.pendingChild=null}getTypeName(){return GI6}}Sp2.ChildLoadBalancerHandler=Pp2});
var qo2=E(($o2)=>{Object.defineProperty($o2,"__esModule",{value:!0});$o2.setup=cK6;var dK6=nL();class wo2{constructor(A,B,Q){this.listener=B,this.hasReturnedResult=!1,this.endpoints=[];let D;if(A.authority==="")D="/"+A.path;else D=A.path;this.endpoints=[{addresses:[{path:D}]}]}updateResolution(){if(!this.hasReturnedResult)this.hasReturnedResult=!0,process.nextTick(this.listener.onSuccessfulResolution,this.endpoints,null,null,null,{})}destroy(){this.hasReturnedResult=!1}static getDefaultAuthority(A){return"localhost"}}function cK6(){dK6.registerResolver("unix",wo2)}});
var qt2=E((wt2)=>{Object.defineProperty(wt2,"__esModule",{value:!0});wt2.createOtlpGrpcExportDelegate=void 0;var Oz6=Tu(),Tz6=a31();function Pz6(A,B,Q,D){return Oz6.createOtlpNetworkExportDelegate(A,B,Tz6.createOtlpGrpcExporterTransport({address:A.url,compression:A.compression,credentials:A.credentials,metadata:A.metadata,grpcName:Q,grpcPath:D}))}wt2.createOtlpGrpcExportDelegate=Pz6});
var ra2=E((aa2)=>{var NW0;Object.defineProperty(aa2,"__esModule",{value:!0});aa2.GRPC_NODE_USE_ALTERNATIVE_RESOLVER=void 0;aa2.GRPC_NODE_USE_ALTERNATIVE_RESOLVER=((NW0=process.env.GRPC_NODE_USE_ALTERNATIVE_RESOLVER)!==null&&NW0!==void 0?NW0:"false")==="true"});
var sY0=E((Tn2)=>{var EW6=Tn2,UW6=vT1();EW6[".google.protobuf.Any"]={fromObject:function(A){if(A&&A["@type"]){var B=A["@type"].substring(A["@type"].lastIndexOf("/")+1),Q=this.lookup(B);if(Q){var D=A["@type"].charAt(0)==="."?A["@type"].slice(1):A["@type"];if(D.indexOf("/")===-1)D="/"+D;return this.create({type_url:D,value:Q.encode(Q.fromObject(A)).finish()})}}return this.fromObject(A)},toObject:function(A,B){var Q="type.googleapis.com/",D="",Z="";if(B&&B.json&&A.type_url&&A.value){Z=A.type_url.substring(A.type_url.lastIndexOf("/")+1),D=A.type_url.substring(0,A.type_url.lastIndexOf("/")+1);var G=this.lookup(Z);if(G)A=G.decode(A.value)}if(!(A instanceof this.ctor)&&A instanceof UW6){var F=A.$type.toObject(A,B),I=A.$type.fullName[0]==="."?A.$type.fullName.slice(1):A.$type.fullName;if(D==="")D=Q;return Z=D+I,F["@type"]=Z,F}return this.toObject(A,B)}}});
var so2=E((no2)=>{Object.defineProperty(no2,"__esModule",{value:!0});no2.createServiceClientConstructor=void 0;var iH6=n31();function nH6(A,B){let Q={export:{path:A,requestStream:!1,responseStream:!1,requestSerialize:(D)=>{return D},requestDeserialize:(D)=>{return D},responseSerialize:(D)=>{return D},responseDeserialize:(D)=>{return D}}};return iH6.makeGenericClientConstructor(Q,B)}no2.createServiceClientConstructor=nH6});
var sr2=E((Qx)=>{var nC6=Qx&&Qx.__runInitializers||function(A,B,Q){var D=arguments.length>2;for(var Z=0;Z<B.length;Z++)Q=D?B[Z].call(A,Q):B[Z].call(A);return D?Q:void 0},aC6=Qx&&Qx.__esDecorate||function(A,B,Q,D,Z,G){function F($){if($!==void 0&&typeof $!=="function")throw new TypeError("Function expected");return $}var I=D.kind,Y=I==="getter"?"get":I==="setter"?"set":"value",W=!B&&A?D.static?A:A.prototype:null,J=B||(W?Object.getOwnPropertyDescriptor(W,D.name):{}),X,V=!1;for(var C=Q.length-1;C>=0;C--){var K={};for(var H in D)K[H]=H==="access"?{}:D[H];for(var H in D.access)K.access[H]=D.access[H];K.addInitializer=function($){if(V)throw new TypeError("Cannot add initializers after decoration has completed");G.push(F($||null))};var z=Q[C](I==="accessor"?{get:J.get,set:J.set}:J[Y],K);if(I==="accessor"){if(z===void 0)continue;if(z===null||typeof z!=="object")throw new TypeError("Object expected");if(X=F(z.get))J.get=X;if(X=F(z.set))J.set=X;if(X=F(z.init))Z.unshift(X)}else if(X=F(z))if(I==="field")Z.unshift(X);else J[Y]=X}if(W)Object.defineProperty(W,D.name,J);V=!0};Object.defineProperty(Qx,"__esModule",{value:!0});Qx.Server=void 0;var yV=J1("http2"),sC6=J1("util"),AG=S6(),Kt=Or2(),ZJ0=KP1(),cr2=nL(),Ct=D7(),Bx=CE(),tw=SV(),NF=hu(),lr2=DJ0(),Vt=2147483647,GJ0=2147483647,rC6=20000,pr2=2147483647,{HTTP2_HEADER_PATH:ir2}=yV.constants,oC6="server",nr2=Buffer.from("max_age");function ar2(A){Ct.trace(AG.LogVerbosity.DEBUG,"server_call",A)}function tC6(){}function eC6(A){return function(B,Q){return sC6.deprecate(B,A)}}function FJ0(A){return{code:AG.Status.UNIMPLEMENTED,details:`The server does not implement the method ${A}`}}function AK6(A,B){let Q=FJ0(B);switch(A){case"unary":return(D,Z)=>{Z(Q,null)};case"clientStream":return(D,Z)=>{Z(Q,null)};case"serverStream":return(D)=>{D.emit("error",Q)};case"bidi":return(D)=>{D.emit("error",Q)};default:throw new Error(`Invalid handlerType ${A}`)}}var BK6=(()=>{var A;let B=[],Q;return A=class D{constructor(Z){var G,F,I,Y,W,J;if(this.boundPorts=(nC6(this,B),new Map),this.http2Servers=new Map,this.sessionIdleTimeouts=new Map,this.handlers=new Map,this.sessions=new Map,this.started=!1,this.shutdown=!1,this.serverAddressString="null",this.channelzEnabled=!0,this.options=Z!==null&&Z!==void 0?Z:{},this.options["grpc.enable_channelz"]===0)this.channelzEnabled=!1,this.channelzTrace=new NF.ChannelzTraceStub,this.callTracker=new NF.ChannelzCallTrackerStub,this.listenerChildrenTracker=new NF.ChannelzChildrenTrackerStub,this.sessionChildrenTracker=new NF.ChannelzChildrenTrackerStub;else this.channelzTrace=new NF.ChannelzTrace,this.callTracker=new NF.ChannelzCallTracker,this.listenerChildrenTracker=new NF.ChannelzChildrenTracker,this.sessionChildrenTracker=new NF.ChannelzChildrenTracker;if(this.channelzRef=NF.registerChannelzServer("server",()=>this.getChannelzInfo(),this.channelzEnabled),this.channelzTrace.addTrace("CT_INFO","Server created"),this.maxConnectionAgeMs=(G=this.options["grpc.max_connection_age_ms"])!==null&&G!==void 0?G:Vt,this.maxConnectionAgeGraceMs=(F=this.options["grpc.max_connection_age_grace_ms"])!==null&&F!==void 0?F:Vt,this.keepaliveTimeMs=(I=this.options["grpc.keepalive_time_ms"])!==null&&I!==void 0?I:GJ0,this.keepaliveTimeoutMs=(Y=this.options["grpc.keepalive_timeout_ms"])!==null&&Y!==void 0?Y:rC6,this.sessionIdleTimeout=(W=this.options["grpc.max_connection_idle_ms"])!==null&&W!==void 0?W:pr2,this.commonServerOptions={maxSendHeaderBlockLength:Number.MAX_SAFE_INTEGER},"grpc-node.max_session_memory"in this.options)this.commonServerOptions.maxSessionMemory=this.options["grpc-node.max_session_memory"];else this.commonServerOptions.maxSessionMemory=Number.MAX_SAFE_INTEGER;if("grpc.max_concurrent_streams"in this.options)this.commonServerOptions.settings={maxConcurrentStreams:this.options["grpc.max_concurrent_streams"]};this.interceptors=(J=this.options.interceptors)!==null&&J!==void 0?J:[],this.trace("Server constructed")}getChannelzInfo(){return{trace:this.channelzTrace,callTracker:this.callTracker,listenerChildren:this.listenerChildrenTracker.getChildLists(),sessionChildren:this.sessionChildrenTracker.getChildLists()}}getChannelzSessionInfo(Z){var G,F,I;let Y=this.sessions.get(Z),W=Z.socket,J=W.remoteAddress?Bx.stringToSubchannelAddress(W.remoteAddress,W.remotePort):null,X=W.localAddress?Bx.stringToSubchannelAddress(W.localAddress,W.localPort):null,V;if(Z.encrypted){let K=W,H=K.getCipher(),z=K.getCertificate(),$=K.getPeerCertificate();V={cipherSuiteStandardName:(G=H.standardName)!==null&&G!==void 0?G:null,cipherSuiteOtherName:H.standardName?null:H.name,localCertificate:z&&"raw"in z?z.raw:null,remoteCertificate:$&&"raw"in $?$.raw:null}}else V=null;return{remoteAddress:J,localAddress:X,security:V,remoteName:null,streamsStarted:Y.streamTracker.callsStarted,streamsSucceeded:Y.streamTracker.callsSucceeded,streamsFailed:Y.streamTracker.callsFailed,messagesSent:Y.messagesSent,messagesReceived:Y.messagesReceived,keepAlivesSent:Y.keepAlivesSent,lastLocalStreamCreatedTimestamp:null,lastRemoteStreamCreatedTimestamp:Y.streamTracker.lastCallStartedTimestamp,lastMessageSentTimestamp:Y.lastMessageSentTimestamp,lastMessageReceivedTimestamp:Y.lastMessageReceivedTimestamp,localFlowControlWindow:(F=Z.state.localWindowSize)!==null&&F!==void 0?F:null,remoteFlowControlWindow:(I=Z.state.remoteWindowSize)!==null&&I!==void 0?I:null}}trace(Z){Ct.trace(AG.LogVerbosity.DEBUG,oC6,"("+this.channelzRef.id+") "+Z)}keepaliveTrace(Z){Ct.trace(AG.LogVerbosity.DEBUG,"keepalive","("+this.channelzRef.id+") "+Z)}addProtoService(){throw new Error("Not implemented. Use addService() instead")}addService(Z,G){if(Z===null||typeof Z!=="object"||G===null||typeof G!=="object")throw new Error("addService() requires two objects as arguments");let F=Object.keys(Z);if(F.length===0)throw new Error("Cannot add an empty service to a server");F.forEach((I)=>{let Y=Z[I],W;if(Y.requestStream)if(Y.responseStream)W="bidi";else W="clientStream";else if(Y.responseStream)W="serverStream";else W="unary";let J=G[I],X;if(J===void 0&&typeof Y.originalName==="string")J=G[Y.originalName];if(J!==void 0)X=J.bind(G);else X=AK6(W,I);if(this.register(Y.path,X,Y.responseSerialize,Y.requestDeserialize,W)===!1)throw new Error(`Method handler for ${Y.path} already provided.`)})}removeService(Z){if(Z===null||typeof Z!=="object")throw new Error("removeService() requires object as argument");Object.keys(Z).forEach((F)=>{let I=Z[F];this.unregister(I.path)})}bind(Z,G){throw new Error("Not implemented. Use bindAsync() instead")}experimentalRegisterListenerToChannelz(Z){return NF.registerChannelzSocket(Bx.subchannelAddressToString(Z),()=>{return{localAddress:Z,remoteAddress:null,security:null,remoteName:null,streamsStarted:0,streamsSucceeded:0,streamsFailed:0,messagesSent:0,messagesReceived:0,keepAlivesSent:0,lastLocalStreamCreatedTimestamp:null,lastRemoteStreamCreatedTimestamp:null,lastMessageSentTimestamp:null,lastMessageReceivedTimestamp:null,localFlowControlWindow:null,remoteFlowControlWindow:null}},this.channelzEnabled)}experimentalUnregisterListenerFromChannelz(Z){NF.unregisterChannelzRef(Z)}createHttp2Server(Z){let G;if(Z._isSecure()){let F=Z._getConstructorOptions(),I=Z._getSecureContextOptions(),Y=Object.assign(Object.assign(Object.assign(Object.assign({},this.commonServerOptions),F),I),{enableTrace:this.options["grpc-node.tls_enable_trace"]===1}),W=I!==null;this.trace("Initial credentials valid: "+W),G=yV.createSecureServer(Y),G.prependListener("connection",(X)=>{if(!W)this.trace("Dropped connection from "+JSON.stringify(X.address())+" due to unloaded credentials"),X.destroy()}),G.on("secureConnection",(X)=>{X.on("error",(V)=>{this.trace("An incoming TLS connection closed with error: "+V.message)})});let J=(X)=>{if(X){let V=G;try{V.setSecureContext(X)}catch(C){Ct.log(AG.LogVerbosity.ERROR,"Failed to set secure context with error "+C.message),X=null}}W=X!==null,this.trace("Post-update credentials valid: "+W)};Z._addWatcher(J),G.on("close",()=>{Z._removeWatcher(J)})}else G=yV.createServer(this.commonServerOptions);return G.setTimeout(0,tC6),this._setupHandlers(G,Z._getInterceptors()),G}bindOneAddress(Z,G){this.trace("Attempting to bind "+Bx.subchannelAddressToString(Z));let F=this.createHttp2Server(G.credentials);return new Promise((I,Y)=>{let W=(J)=>{this.trace("Failed to bind "+Bx.subchannelAddressToString(Z)+" with error "+J.message),I({port:"port"in Z?Z.port:1,error:J.message})};F.once("error",W),F.listen(Z,()=>{let J=F.address(),X;if(typeof J==="string")X={path:J};else X={host:J.address,port:J.port};let V=this.experimentalRegisterListenerToChannelz(X);this.listenerChildrenTracker.refChild(V),this.http2Servers.set(F,{channelzRef:V,sessions:new Set,ownsChannelzRef:!0}),G.listeningServers.add(F),this.trace("Successfully bound "+Bx.subchannelAddressToString(X)),I({port:"port"in X?X.port:1}),F.removeListener("error",W)})})}async bindManyPorts(Z,G){if(Z.length===0)return{count:0,port:0,errors:[]};if(Bx.isTcpSubchannelAddress(Z[0])&&Z[0].port===0){let F=await this.bindOneAddress(Z[0],G);if(F.error){let I=await this.bindManyPorts(Z.slice(1),G);return Object.assign(Object.assign({},I),{errors:[F.error,...I.errors]})}else{let I=Z.slice(1).map((J)=>Bx.isTcpSubchannelAddress(J)?{host:J.host,port:F.port}:J),Y=await Promise.all(I.map((J)=>this.bindOneAddress(J,G))),W=[F,...Y];return{count:W.filter((J)=>J.error===void 0).length,port:F.port,errors:W.filter((J)=>J.error).map((J)=>J.error)}}}else{let F=await Promise.all(Z.map((I)=>this.bindOneAddress(I,G)));return{count:F.filter((I)=>I.error===void 0).length,port:F[0].port,errors:F.filter((I)=>I.error).map((I)=>I.error)}}}async bindAddressList(Z,G){let F=await this.bindManyPorts(Z,G);if(F.count>0){if(F.count<Z.length)Ct.log(AG.LogVerbosity.INFO,`WARNING Only ${F.count} addresses added out of total ${Z.length} resolved`);return F.port}else{let I=`No address added out of total ${Z.length} resolved`;throw Ct.log(AG.LogVerbosity.ERROR,I),new Error(`${I} errors: [${F.errors.join(",")}]`)}}resolvePort(Z){return new Promise((G,F)=>{let I={onSuccessfulResolution:(W,J,X)=>{I.onSuccessfulResolution=()=>{};let V=[].concat(...W.map((C)=>C.addresses));if(V.length===0){F(new Error(`No addresses resolved for port ${Z}`));return}G(V)},onError:(W)=>{F(new Error(W.details))}};cr2.createResolver(Z,I,this.options).updateResolution()})}async bindPort(Z,G){let F=await this.resolvePort(Z);if(G.cancelled)throw this.completeUnbind(G),new Error("bindAsync operation cancelled by unbind call");let I=await this.bindAddressList(F,G);if(G.cancelled)throw this.completeUnbind(G),new Error("bindAsync operation cancelled by unbind call");return I}normalizePort(Z){let G=tw.parseUri(Z);if(G===null)throw new Error(`Could not parse port "${Z}"`);let F=cr2.mapUriDefaultScheme(G);if(F===null)throw new Error(`Could not get a default scheme for port "${Z}"`);return F}bindAsync(Z,G,F){if(this.shutdown)throw new Error("bindAsync called after shutdown");if(typeof Z!=="string")throw new TypeError("port must be a string");if(G===null||!(G instanceof ZJ0.ServerCredentials))throw new TypeError("creds must be a ServerCredentials object");if(typeof F!=="function")throw new TypeError("callback must be a function");this.trace("bindAsync port="+Z);let I=this.normalizePort(Z),Y=(V,C)=>{process.nextTick(()=>F(V,C))},W=this.boundPorts.get(tw.uriToString(I));if(W){if(!G._equals(W.credentials)){Y(new Error(`${Z} already bound with incompatible credentials`),0);return}if(W.cancelled=!1,W.completionPromise)W.completionPromise.then((V)=>F(null,V),(V)=>F(V,0));else Y(null,W.portNumber);return}W={mapKey:tw.uriToString(I),originalUri:I,completionPromise:null,cancelled:!1,portNumber:0,credentials:G,listeningServers:new Set};let J=tw.splitHostPort(I.path),X=this.bindPort(I,W);if(W.completionPromise=X,(J===null||J===void 0?void 0:J.port)===0)X.then((V)=>{let C={scheme:I.scheme,authority:I.authority,path:tw.combineHostPort({host:J.host,port:V})};W.mapKey=tw.uriToString(C),W.completionPromise=null,W.portNumber=V,this.boundPorts.set(W.mapKey,W),F(null,V)},(V)=>{F(V,0)});else this.boundPorts.set(W.mapKey,W),X.then((V)=>{W.completionPromise=null,W.portNumber=V,F(null,V)},(V)=>{F(V,0)})}registerInjectorToChannelz(){return NF.registerChannelzSocket("injector",()=>{return{localAddress:null,remoteAddress:null,security:null,remoteName:null,streamsStarted:0,streamsSucceeded:0,streamsFailed:0,messagesSent:0,messagesReceived:0,keepAlivesSent:0,lastLocalStreamCreatedTimestamp:null,lastRemoteStreamCreatedTimestamp:null,lastMessageSentTimestamp:null,lastMessageReceivedTimestamp:null,localFlowControlWindow:null,remoteFlowControlWindow:null}},this.channelzEnabled)}experimentalCreateConnectionInjectorWithChannelzRef(Z,G,F=!1){if(Z===null||!(Z instanceof ZJ0.ServerCredentials))throw new TypeError("creds must be a ServerCredentials object");if(this.channelzEnabled)this.listenerChildrenTracker.refChild(G);let I=this.createHttp2Server(Z),Y=new Set;return this.http2Servers.set(I,{channelzRef:G,sessions:Y,ownsChannelzRef:F}),{injectConnection:(W)=>{I.emit("connection",W)},drain:(W)=>{var J,X;for(let V of Y)this.closeSession(V);(X=(J=setTimeout(()=>{for(let V of Y)V.destroy(yV.constants.NGHTTP2_CANCEL)},W)).unref)===null||X===void 0||X.call(J)},destroy:()=>{this.closeServer(I);for(let W of Y)this.closeSession(W)}}}createConnectionInjector(Z){if(Z===null||!(Z instanceof ZJ0.ServerCredentials))throw new TypeError("creds must be a ServerCredentials object");let G=this.registerInjectorToChannelz();return this.experimentalCreateConnectionInjectorWithChannelzRef(Z,G,!0)}closeServer(Z,G){this.trace("Closing server with address "+JSON.stringify(Z.address()));let F=this.http2Servers.get(Z);Z.close(()=>{if(F&&F.ownsChannelzRef)this.listenerChildrenTracker.unrefChild(F.channelzRef),NF.unregisterChannelzRef(F.channelzRef);this.http2Servers.delete(Z),G===null||G===void 0||G()})}closeSession(Z,G){var F;this.trace("Closing session initiated by "+((F=Z.socket)===null||F===void 0?void 0:F.remoteAddress));let I=this.sessions.get(Z),Y=()=>{if(I)this.sessionChildrenTracker.unrefChild(I.ref),NF.unregisterChannelzRef(I.ref);G===null||G===void 0||G()};if(Z.closed)queueMicrotask(Y);else Z.close(Y)}completeUnbind(Z){for(let G of Z.listeningServers){let F=this.http2Servers.get(G);if(this.closeServer(G,()=>{Z.listeningServers.delete(G)}),F)for(let I of F.sessions)this.closeSession(I)}this.boundPorts.delete(Z.mapKey)}unbind(Z){this.trace("unbind port="+Z);let G=this.normalizePort(Z),F=tw.splitHostPort(G.path);if((F===null||F===void 0?void 0:F.port)===0)throw new Error("Cannot unbind port 0");let I=this.boundPorts.get(tw.uriToString(G));if(I)if(this.trace("unbinding "+I.mapKey+" originally bound as "+tw.uriToString(I.originalUri)),I.completionPromise)I.cancelled=!0;else this.completeUnbind(I)}drain(Z,G){var F,I;this.trace("drain port="+Z+" graceTimeMs="+G);let Y=this.normalizePort(Z),W=tw.splitHostPort(Y.path);if((W===null||W===void 0?void 0:W.port)===0)throw new Error("Cannot drain port 0");let J=this.boundPorts.get(tw.uriToString(Y));if(!J)return;let X=new Set;for(let V of J.listeningServers){let C=this.http2Servers.get(V);if(C)for(let K of C.sessions)X.add(K),this.closeSession(K,()=>{X.delete(K)})}(I=(F=setTimeout(()=>{for(let V of X)V.destroy(yV.constants.NGHTTP2_CANCEL)},G)).unref)===null||I===void 0||I.call(F)}forceShutdown(){for(let Z of this.boundPorts.values())Z.cancelled=!0;this.boundPorts.clear();for(let Z of this.http2Servers.keys())this.closeServer(Z);this.sessions.forEach((Z,G)=>{this.closeSession(G),G.destroy(yV.constants.NGHTTP2_CANCEL)}),this.sessions.clear(),NF.unregisterChannelzRef(this.channelzRef),this.shutdown=!0}register(Z,G,F,I,Y){if(this.handlers.has(Z))return!1;return this.handlers.set(Z,{func:G,serialize:F,deserialize:I,type:Y,path:Z}),!0}unregister(Z){return this.handlers.delete(Z)}start(){if(this.http2Servers.size===0||[...this.http2Servers.keys()].every((Z)=>!Z.listening))throw new Error("server must be bound in order to start");if(this.started===!0)throw new Error("server is already started");this.started=!0}tryShutdown(Z){var G;let F=(W)=>{NF.unregisterChannelzRef(this.channelzRef),Z(W)},I=0;function Y(){if(I--,I===0)F()}this.shutdown=!0;for(let[W,J]of this.http2Servers.entries()){I++;let X=J.channelzRef.name;this.trace("Waiting for server "+X+" to close"),this.closeServer(W,()=>{this.trace("Server "+X+" finished closing"),Y()});for(let V of J.sessions.keys()){I++;let C=(G=V.socket)===null||G===void 0?void 0:G.remoteAddress;this.trace("Waiting for session "+C+" to close"),this.closeSession(V,()=>{this.trace("Session "+C+" finished closing"),Y()})}}if(I===0)F()}addHttp2Port(){throw new Error("Not yet implemented")}getChannelzRef(){return this.channelzRef}_verifyContentType(Z,G){let F=G[yV.constants.HTTP2_HEADER_CONTENT_TYPE];if(typeof F!=="string"||!F.startsWith("application/grpc"))return Z.respond({[yV.constants.HTTP2_HEADER_STATUS]:yV.constants.HTTP_STATUS_UNSUPPORTED_MEDIA_TYPE},{endStream:!0}),!1;return!0}_retrieveHandler(Z){ar2("Received call to method "+Z+" at address "+this.serverAddressString);let G=this.handlers.get(Z);if(G===void 0)return ar2("No handler registered for method "+Z+". Sending UNIMPLEMENTED status."),null;return G}_respondWithError(Z,G,F=null){var I,Y;let W=Object.assign({"grpc-status":(I=Z.code)!==null&&I!==void 0?I:AG.Status.INTERNAL,"grpc-message":Z.details,[yV.constants.HTTP2_HEADER_STATUS]:yV.constants.HTTP_STATUS_OK,[yV.constants.HTTP2_HEADER_CONTENT_TYPE]:"application/grpc+proto"},(Y=Z.metadata)===null||Y===void 0?void 0:Y.toHttp2Headers());G.respond(W,{endStream:!0}),this.callTracker.addCallFailed(),F===null||F===void 0||F.streamTracker.addCallFailed()}_channelzHandler(Z,G,F){this.onStreamOpened(G);let I=this.sessions.get(G.session);if(this.callTracker.addCallStarted(),I===null||I===void 0||I.streamTracker.addCallStarted(),!this._verifyContentType(G,F)){this.callTracker.addCallFailed(),I===null||I===void 0||I.streamTracker.addCallFailed();return}let Y=F[ir2],W=this._retrieveHandler(Y);if(!W){this._respondWithError(FJ0(Y),G,I);return}let J={addMessageSent:()=>{if(I)I.messagesSent+=1,I.lastMessageSentTimestamp=new Date},addMessageReceived:()=>{if(I)I.messagesReceived+=1,I.lastMessageReceivedTimestamp=new Date},onCallEnd:(V)=>{if(V.code===AG.Status.OK)this.callTracker.addCallSucceeded();else this.callTracker.addCallFailed()},onStreamEnd:(V)=>{if(I)if(V)I.streamTracker.addCallSucceeded();else I.streamTracker.addCallFailed()}},X=lr2.getServerInterceptingCall([...Z,...this.interceptors],G,F,J,W,this.options);if(!this._runHandlerForCall(X,W))this.callTracker.addCallFailed(),I===null||I===void 0||I.streamTracker.addCallFailed(),X.sendStatus({code:AG.Status.INTERNAL,details:`Unknown handler type: ${W.type}`})}_streamHandler(Z,G,F){if(this.onStreamOpened(G),this._verifyContentType(G,F)!==!0)return;let I=F[ir2],Y=this._retrieveHandler(I);if(!Y){this._respondWithError(FJ0(I),G,null);return}let W=lr2.getServerInterceptingCall([...Z,...this.interceptors],G,F,null,Y,this.options);if(!this._runHandlerForCall(W,Y))W.sendStatus({code:AG.Status.INTERNAL,details:`Unknown handler type: ${Y.type}`})}_runHandlerForCall(Z,G){let{type:F}=G;if(F==="unary")QK6(Z,G);else if(F==="clientStream")DK6(Z,G);else if(F==="serverStream")ZK6(Z,G);else if(F==="bidi")GK6(Z,G);else return!1;return!0}_setupHandlers(Z,G){if(Z===null)return;let F=Z.address(),I="null";if(F)if(typeof F==="string")I=F;else I=F.address+":"+F.port;this.serverAddressString=I;let Y=this.channelzEnabled?this._channelzHandler:this._streamHandler,W=this.channelzEnabled?this._channelzSessionHandler(Z):this._sessionHandler(Z);Z.on("stream",Y.bind(this,G)),Z.on("session",W)}_sessionHandler(Z){return(G)=>{var F,I;(F=this.http2Servers.get(Z))===null||F===void 0||F.sessions.add(G);let Y=null,W=null,J=null,X=!1,V=this.enableIdleTimeout(G);if(this.maxConnectionAgeMs!==Vt){let $=this.maxConnectionAgeMs/10,L=Math.random()*$*2-$;Y=setTimeout(()=>{var N,O;X=!0,this.trace("Connection dropped by max connection age: "+((N=G.socket)===null||N===void 0?void 0:N.remoteAddress));try{G.goaway(yV.constants.NGHTTP2_NO_ERROR,2147483647,nr2)}catch(R){G.destroy();return}if(G.close(),this.maxConnectionAgeGraceMs!==Vt)W=setTimeout(()=>{G.destroy()},this.maxConnectionAgeGraceMs),(O=W.unref)===null||O===void 0||O.call(W)},this.maxConnectionAgeMs+L),(I=Y.unref)===null||I===void 0||I.call(Y)}let C=()=>{if(J)clearTimeout(J),J=null},K=()=>{return!G.destroyed&&this.keepaliveTimeMs<GJ0&&this.keepaliveTimeMs>0},H,z=()=>{var $;if(!K())return;this.keepaliveTrace("Starting keepalive timer for "+this.keepaliveTimeMs+"ms"),J=setTimeout(()=>{C(),H()},this.keepaliveTimeMs),($=J.unref)===null||$===void 0||$.call(J)};H=()=>{var $;if(!K())return;this.keepaliveTrace("Sending ping with timeout "+this.keepaliveTimeoutMs+"ms");let L="";try{if(!G.ping((O,R,T)=>{if(C(),O)this.keepaliveTrace("Ping failed with error: "+O.message),X=!0,G.close();else this.keepaliveTrace("Received ping response"),z()}))L="Ping returned false"}catch(N){L=(N instanceof Error?N.message:"")||"Unknown error"}if(L){this.keepaliveTrace("Ping send failed: "+L),this.trace("Connection dropped due to ping send error: "+L),X=!0,G.close();return}J=setTimeout(()=>{C(),this.keepaliveTrace("Ping timeout passed without response"),this.trace("Connection dropped by keepalive timeout"),X=!0,G.close()},this.keepaliveTimeoutMs),($=J.unref)===null||$===void 0||$.call(J)},z(),G.on("close",()=>{var $,L;if(!X)this.trace(`Connection dropped by client ${($=G.socket)===null||$===void 0?void 0:$.remoteAddress}`);if(Y)clearTimeout(Y);if(W)clearTimeout(W);if(C(),V!==null)clearTimeout(V.timeout),this.sessionIdleTimeouts.delete(G);(L=this.http2Servers.get(Z))===null||L===void 0||L.sessions.delete(G)})}}_channelzSessionHandler(Z){return(G)=>{var F,I,Y,W;let J=NF.registerChannelzSocket((I=(F=G.socket)===null||F===void 0?void 0:F.remoteAddress)!==null&&I!==void 0?I:"unknown",this.getChannelzSessionInfo.bind(this,G),this.channelzEnabled),X={ref:J,streamTracker:new NF.ChannelzCallTracker,messagesSent:0,messagesReceived:0,keepAlivesSent:0,lastMessageSentTimestamp:null,lastMessageReceivedTimestamp:null};(Y=this.http2Servers.get(Z))===null||Y===void 0||Y.sessions.add(G),this.sessions.set(G,X);let V=`${G.socket.remoteAddress}:${G.socket.remotePort}`;this.channelzTrace.addTrace("CT_INFO","Connection established by client "+V),this.trace("Connection established by client "+V),this.sessionChildrenTracker.refChild(J);let C=null,K=null,H=null,z=!1,$=this.enableIdleTimeout(G);if(this.maxConnectionAgeMs!==Vt){let T=this.maxConnectionAgeMs/10,j=Math.random()*T*2-T;C=setTimeout(()=>{var f;z=!0,this.channelzTrace.addTrace("CT_INFO","Connection dropped by max connection age from "+V);try{G.goaway(yV.constants.NGHTTP2_NO_ERROR,2147483647,nr2)}catch(k){G.destroy();return}if(G.close(),this.maxConnectionAgeGraceMs!==Vt)K=setTimeout(()=>{G.destroy()},this.maxConnectionAgeGraceMs),(f=K.unref)===null||f===void 0||f.call(K)},this.maxConnectionAgeMs+j),(W=C.unref)===null||W===void 0||W.call(C)}let L=()=>{if(H)clearTimeout(H),H=null},N=()=>{return!G.destroyed&&this.keepaliveTimeMs<GJ0&&this.keepaliveTimeMs>0},O,R=()=>{var T;if(!N())return;this.keepaliveTrace("Starting keepalive timer for "+this.keepaliveTimeMs+"ms"),H=setTimeout(()=>{L(),O()},this.keepaliveTimeMs),(T=H.unref)===null||T===void 0||T.call(H)};O=()=>{var T;if(!N())return;this.keepaliveTrace("Sending ping with timeout "+this.keepaliveTimeoutMs+"ms");let j="";try{if(!G.ping((k,c,h)=>{if(L(),k)this.keepaliveTrace("Ping failed with error: "+k.message),this.channelzTrace.addTrace("CT_INFO","Connection dropped due to error of a ping frame "+k.message+" return in "+c),z=!0,G.close();else this.keepaliveTrace("Received ping response"),R()}))j="Ping returned false"}catch(f){j=(f instanceof Error?f.message:"")||"Unknown error"}if(j){this.keepaliveTrace("Ping send failed: "+j),this.channelzTrace.addTrace("CT_INFO","Connection dropped due to ping send error: "+j),z=!0,G.close();return}X.keepAlivesSent+=1,H=setTimeout(()=>{L(),this.keepaliveTrace("Ping timeout passed without response"),this.channelzTrace.addTrace("CT_INFO","Connection dropped by keepalive timeout from "+V),z=!0,G.close()},this.keepaliveTimeoutMs),(T=H.unref)===null||T===void 0||T.call(H)},R(),G.on("close",()=>{var T;if(!z)this.channelzTrace.addTrace("CT_INFO","Connection dropped by client "+V);if(this.sessionChildrenTracker.unrefChild(J),NF.unregisterChannelzRef(J),C)clearTimeout(C);if(K)clearTimeout(K);if(L(),$!==null)clearTimeout($.timeout),this.sessionIdleTimeouts.delete(G);(T=this.http2Servers.get(Z))===null||T===void 0||T.sessions.delete(G),this.sessions.delete(G)})}}enableIdleTimeout(Z){var G,F;if(this.sessionIdleTimeout>=pr2)return null;let I={activeStreams:0,lastIdle:Date.now(),onClose:this.onStreamClose.bind(this,Z),timeout:setTimeout(this.onIdleTimeout,this.sessionIdleTimeout,this,Z)};(F=(G=I.timeout).unref)===null||F===void 0||F.call(G),this.sessionIdleTimeouts.set(Z,I);let{socket:Y}=Z;return this.trace("Enable idle timeout for "+Y.remoteAddress+":"+Y.remotePort),I}onIdleTimeout(Z,G){let{socket:F}=G,I=Z.sessionIdleTimeouts.get(G);if(I!==void 0&&I.activeStreams===0)if(Date.now()-I.lastIdle>=Z.sessionIdleTimeout)Z.trace("Session idle timeout triggered for "+(F===null||F===void 0?void 0:F.remoteAddress)+":"+(F===null||F===void 0?void 0:F.remotePort)+" last idle at "+I.lastIdle),Z.closeSession(G);else I.timeout.refresh()}onStreamOpened(Z){let G=Z.session,F=this.sessionIdleTimeouts.get(G);if(F)F.activeStreams+=1,Z.once("close",F.onClose)}onStreamClose(Z){var G,F;let I=this.sessionIdleTimeouts.get(Z);if(I){if(I.activeStreams-=1,I.activeStreams===0)I.lastIdle=Date.now(),I.timeout.refresh(),this.trace("Session onStreamClose"+((G=Z.socket)===null||G===void 0?void 0:G.remoteAddress)+":"+((F=Z.socket)===null||F===void 0?void 0:F.remotePort)+" at "+I.lastIdle)}}},(()=>{let D=typeof Symbol==="function"&&Symbol.metadata?Object.create(null):void 0;if(Q=[eC6("Calling start() is no longer necessary. It can be safely omitted.")],aC6(A,null,Q,{kind:"method",name:"start",static:!1,private:!1,access:{has:(Z)=>("start"in Z),get:(Z)=>Z.start},metadata:D},null,B),D)Object.defineProperty(A,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:D})})(),A})();Qx.Server=BK6;async function QK6(A,B){let Q;function D(F,I,Y,W){if(F){A.sendStatus(Kt.serverErrorToStatus(F,Y));return}A.sendMessage(I,()=>{A.sendStatus({code:AG.Status.OK,details:"OK",metadata:Y!==null&&Y!==void 0?Y:null})})}let Z,G=null;A.start({onReceiveMetadata(F){Z=F,A.startRead()},onReceiveMessage(F){if(G){A.sendStatus({code:AG.Status.UNIMPLEMENTED,details:`Received a second request message for server streaming method ${B.path}`,metadata:null});return}G=F,A.startRead()},onReceiveHalfClose(){if(!G){A.sendStatus({code:AG.Status.UNIMPLEMENTED,details:`Received no request message for server streaming method ${B.path}`,metadata:null});return}Q=new Kt.ServerWritableStreamImpl(B.path,A,Z,G);try{B.func(Q,D)}catch(F){A.sendStatus({code:AG.Status.UNKNOWN,details:`Server method handler threw error ${F.message}`,metadata:null})}},onCancel(){if(Q)Q.cancelled=!0,Q.emit("cancelled","cancelled")}})}function DK6(A,B){let Q;function D(Z,G,F,I){if(Z){A.sendStatus(Kt.serverErrorToStatus(Z,F));return}A.sendMessage(G,()=>{A.sendStatus({code:AG.Status.OK,details:"OK",metadata:F!==null&&F!==void 0?F:null})})}A.start({onReceiveMetadata(Z){Q=new Kt.ServerDuplexStreamImpl(B.path,A,Z);try{B.func(Q,D)}catch(G){A.sendStatus({code:AG.Status.UNKNOWN,details:`Server method handler threw error ${G.message}`,metadata:null})}},onReceiveMessage(Z){Q.push(Z)},onReceiveHalfClose(){Q.push(null)},onCancel(){if(Q)Q.cancelled=!0,Q.emit("cancelled","cancelled"),Q.destroy()}})}function ZK6(A,B){let Q,D,Z=null;A.start({onReceiveMetadata(G){D=G,A.startRead()},onReceiveMessage(G){if(Z){A.sendStatus({code:AG.Status.UNIMPLEMENTED,details:`Received a second request message for server streaming method ${B.path}`,metadata:null});return}Z=G,A.startRead()},onReceiveHalfClose(){if(!Z){A.sendStatus({code:AG.Status.UNIMPLEMENTED,details:`Received no request message for server streaming method ${B.path}`,metadata:null});return}Q=new Kt.ServerWritableStreamImpl(B.path,A,D,Z);try{B.func(Q)}catch(G){A.sendStatus({code:AG.Status.UNKNOWN,details:`Server method handler threw error ${G.message}`,metadata:null})}},onCancel(){if(Q)Q.cancelled=!0,Q.emit("cancelled","cancelled"),Q.destroy()}})}function GK6(A,B){let Q;A.start({onReceiveMetadata(D){Q=new Kt.ServerDuplexStreamImpl(B.path,A,D);try{B.func(Q)}catch(Z){A.sendStatus({code:AG.Status.UNKNOWN,details:`Server method handler threw error ${Z.message}`,metadata:null})}},onReceiveMessage(D){Q.push(D)},onReceiveHalfClose(){Q.push(null)},onCancel(){if(Q)Q.cancelled=!0,Q.emit("cancelled","cancelled"),Q.destroy()}})}});
var sw=E((t_5,un2)=>{un2.exports=oL;var hn2=vu();((oL.prototype=Object.create(hn2.prototype)).constructor=oL).className="Enum";var gn2=At(),lT1=KI();function oL(A,B,Q,D,Z,G){if(hn2.call(this,A,Q),B&&typeof B!=="object")throw TypeError("values must be an object");if(this.valuesById={},this.values=Object.create(this.valuesById),this.comment=D,this.comments=Z||{},this.valuesOptions=G,this.reserved=void 0,B){for(var F=Object.keys(B),I=0;I<F.length;++I)if(typeof B[F[I]]==="number")this.valuesById[this.values[F[I]]=B[F[I]]]=F[I]}}oL.fromJSON=function A(B,Q){var D=new oL(B,Q.values,Q.options,Q.comment,Q.comments);return D.reserved=Q.reserved,D};oL.prototype.toJSON=function A(B){var Q=B?Boolean(B.keepComments):!1;return lT1.toObject(["options",this.options,"valuesOptions",this.valuesOptions,"values",this.values,"reserved",this.reserved&&this.reserved.length?this.reserved:void 0,"comment",Q?this.comment:void 0,"comments",Q?this.comments:void 0])};oL.prototype.add=function A(B,Q,D,Z){if(!lT1.isString(B))throw TypeError("name must be a string");if(!lT1.isInteger(Q))throw TypeError("id must be an integer");if(this.values[B]!==void 0)throw Error("duplicate name '"+B+"' in "+this);if(this.isReservedId(Q))throw Error("id "+Q+" is reserved in "+this);if(this.isReservedName(B))throw Error("name '"+B+"' is reserved in "+this);if(this.valuesById[Q]!==void 0){if(!(this.options&&this.options.allow_alias))throw Error("duplicate id "+Q+" in "+this);this.values[B]=Q}else this.valuesById[this.values[B]=Q]=B;if(Z){if(this.valuesOptions===void 0)this.valuesOptions={};this.valuesOptions[B]=Z||null}return this.comments[B]=D||null,this};oL.prototype.remove=function A(B){if(!lT1.isString(B))throw TypeError("name must be a string");var Q=this.values[B];if(Q==null)throw Error("name '"+B+"' does not exist in "+this);if(delete this.valuesById[Q],delete this.values[B],delete this.comments[B],this.valuesOptions)delete this.valuesOptions[B];return this};oL.prototype.isReservedId=function A(B){return gn2.isReservedId(this.reserved,B)};oL.prototype.isReservedName=function A(B){return gn2.isReservedName(this.reserved,B)}});
var tW=E((el2)=>{Object.defineProperty(el2,"__esModule",{value:!0});el2.Metadata=void 0;var _G6=D7(),xG6=S6(),vG6=YT1(),bG6=/^[0-9a-z_.-]+$/,fG6=/^[ -~]*$/;function hG6(A){return bG6.test(A)}function gG6(A){return fG6.test(A)}function tl2(A){return A.endsWith("-bin")}function uG6(A){return!A.startsWith("grpc-")}function WT1(A){return A.toLowerCase()}function ol2(A,B){if(!hG6(A))throw new Error('Metadata key "'+A+'" contains illegal characters');if(B!==null&&B!==void 0)if(tl2(A)){if(!Buffer.isBuffer(B))throw new Error("keys that end with '-bin' must have Buffer values")}else{if(Buffer.isBuffer(B))throw new Error("keys that don't end with '-bin' must have String values");if(!gG6(B))throw new Error('Metadata string value "'+B+'" contains illegal characters')}}class JT1{constructor(A={}){this.internalRepr=new Map,this.options=A}set(A,B){A=WT1(A),ol2(A,B),this.internalRepr.set(A,[B])}add(A,B){A=WT1(A),ol2(A,B);let Q=this.internalRepr.get(A);if(Q===void 0)this.internalRepr.set(A,[B]);else Q.push(B)}remove(A){A=WT1(A),this.internalRepr.delete(A)}get(A){return A=WT1(A),this.internalRepr.get(A)||[]}getMap(){let A={};for(let[B,Q]of this.internalRepr)if(Q.length>0){let D=Q[0];A[B]=Buffer.isBuffer(D)?Buffer.from(D):D}return A}clone(){let A=new JT1(this.options),B=A.internalRepr;for(let[Q,D]of this.internalRepr){let Z=D.map((G)=>{if(Buffer.isBuffer(G))return Buffer.from(G);else return G});B.set(Q,Z)}return A}merge(A){for(let[B,Q]of A.internalRepr){let D=(this.internalRepr.get(B)||[]).concat(Q);this.internalRepr.set(B,D)}}setOptions(A){this.options=A}getOptions(){return this.options}toHttp2Headers(){let A={};for(let[B,Q]of this.internalRepr)A[B]=Q.map(mG6);return A}toJSON(){let A={};for(let[B,Q]of this.internalRepr)A[B]=Q;return A}static fromHttp2Headers(A){let B=new JT1;for(let Q of Object.keys(A)){if(Q.charAt(0)===":")continue;let D=A[Q];try{if(tl2(Q)){if(Array.isArray(D))D.forEach((Z)=>{B.add(Q,Buffer.from(Z,"base64"))});else if(D!==void 0)if(uG6(Q))D.split(",").forEach((Z)=>{B.add(Q,Buffer.from(Z.trim(),"base64"))});else B.add(Q,Buffer.from(D,"base64"))}else if(Array.isArray(D))D.forEach((Z)=>{B.add(Q,Z)});else if(D!==void 0)B.add(Q,D)}catch(Z){let G=`Failed to add metadata entry ${Q}: ${D}. ${vG6.getErrorMessage(Z)}. For more information see https://github.com/grpc/grpc-node/issues/1173`;_G6.log(xG6.LogVerbosity.ERROR,G)}}return B}}el2.Metadata=JT1;var mG6=(A)=>{return Buffer.isBuffer(A)?A.toString("base64"):A}});
var ti2=E((y_5,oi2)=>{var YY6=1/0,WY6="[object Symbol]",JY6=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,XY6=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,OT1="\\ud800-\\udfff",vi2="\\u0300-\\u036f\\ufe20-\\ufe23",bi2="\\u20d0-\\u20f0",fi2="\\u2700-\\u27bf",hi2="a-z\\xdf-\\xf6\\xf8-\\xff",VY6="\\xac\\xb1\\xd7\\xf7",CY6="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",KY6="\\u2000-\\u206f",HY6=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",gi2="A-Z\\xc0-\\xd6\\xd8-\\xde",ui2="\\ufe0e\\ufe0f",mi2=VY6+CY6+KY6+HY6,jY0="['’]",zY6="["+OT1+"]",Pi2="["+mi2+"]",RT1="["+vi2+bi2+"]",di2="\\d+",EY6="["+fi2+"]",ci2="["+hi2+"]",li2="[^"+OT1+mi2+di2+fi2+hi2+gi2+"]",SY0="\\ud83c[\\udffb-\\udfff]",UY6="(?:"+RT1+"|"+SY0+")",pi2="[^"+OT1+"]",kY0="(?:\\ud83c[\\udde6-\\uddff]){2}",yY0="[\\ud800-\\udbff][\\udc00-\\udfff]",ro="["+gi2+"]",ii2="\\u200d",Si2="(?:"+ci2+"|"+li2+")",wY6="(?:"+ro+"|"+li2+")",ji2="(?:"+jY0+"(?:d|ll|m|re|s|t|ve))?",ki2="(?:"+jY0+"(?:D|LL|M|RE|S|T|VE))?",ni2=UY6+"?",ai2="["+ui2+"]?",$Y6="(?:"+ii2+"(?:"+[pi2,kY0,yY0].join("|")+")"+ai2+ni2+")*",si2=ai2+ni2+$Y6,qY6="(?:"+[EY6,kY0,yY0].join("|")+")"+si2,NY6="(?:"+[pi2+RT1+"?",RT1,kY0,yY0,zY6].join("|")+")",LY6=RegExp(jY0,"g"),MY6=RegExp(RT1,"g"),RY6=RegExp(SY0+"(?="+SY0+")|"+NY6+si2,"g"),OY6=RegExp([ro+"?"+ci2+"+"+ji2+"(?="+[Pi2,ro,"$"].join("|")+")",wY6+"+"+ki2+"(?="+[Pi2,ro+Si2,"$"].join("|")+")",ro+"?"+Si2+"+"+ji2,ro+"+"+ki2,di2,qY6].join("|"),"g"),TY6=RegExp("["+ii2+OT1+vi2+bi2+ui2+"]"),PY6=/[a-z][A-Z]|[A-Z]{2,}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,SY6={"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"ss"},jY6=typeof global=="object"&&global&&global.Object===Object&&global,kY6=typeof self=="object"&&self&&self.Object===Object&&self,yY6=jY6||kY6||Function("return this")();function _Y6(A,B,Q,D){var Z=-1,G=A?A.length:0;if(D&&G)Q=A[++Z];while(++Z<G)Q=B(Q,A[Z],Z,A);return Q}function xY6(A){return A.split("")}function vY6(A){return A.match(JY6)||[]}function bY6(A){return function(B){return A==null?void 0:A[B]}}var fY6=bY6(SY6);function ri2(A){return TY6.test(A)}function hY6(A){return PY6.test(A)}function gY6(A){return ri2(A)?uY6(A):xY6(A)}function uY6(A){return A.match(RY6)||[]}function mY6(A){return A.match(OY6)||[]}var dY6=Object.prototype,cY6=dY6.toString,yi2=yY6.Symbol,_i2=yi2?yi2.prototype:void 0,xi2=_i2?_i2.toString:void 0;function lY6(A,B,Q){var D=-1,Z=A.length;if(B<0)B=-B>Z?0:Z+B;if(Q=Q>Z?Z:Q,Q<0)Q+=Z;Z=B>Q?0:Q-B>>>0,B>>>=0;var G=Array(Z);while(++D<Z)G[D]=A[D+B];return G}function pY6(A){if(typeof A=="string")return A;if(rY6(A))return xi2?xi2.call(A):"";var B=A+"";return B=="0"&&1/A==-YY6?"-0":B}function iY6(A,B,Q){var D=A.length;return Q=Q===void 0?D:Q,!B&&Q>=D?A:lY6(A,B,Q)}function nY6(A){return function(B){B=TT1(B);var Q=ri2(B)?gY6(B):void 0,D=Q?Q[0]:B.charAt(0),Z=Q?iY6(Q,1).join(""):B.slice(1);return D[A]()+Z}}function aY6(A){return function(B){return _Y6(BW6(eY6(B).replace(LY6,"")),A,"")}}function sY6(A){return!!A&&typeof A=="object"}function rY6(A){return typeof A=="symbol"||sY6(A)&&cY6.call(A)==WY6}function TT1(A){return A==null?"":pY6(A)}var oY6=aY6(function(A,B,Q){return B=B.toLowerCase(),A+(Q?tY6(B):B)});function tY6(A){return AW6(TT1(A).toLowerCase())}function eY6(A){return A=TT1(A),A&&A.replace(XY6,fY6).replace(MY6,"")}var AW6=nY6("toUpperCase");function BW6(A,B,Q){if(A=TT1(A),B=Q?void 0:B,B===void 0)return hY6(A)?mY6(A):vY6(A);return A.match(B)||[]}oi2.exports=oY6});
var ts2=E((rs2)=>{Object.defineProperty(rs2,"__esModule",{value:!0});rs2.ResolvingCall=void 0;var rV6=VT1(),gu=S6(),uu=u31(),as2=tW(),oV6=D7(),tV6=FP1(),eV6="resolving_call";class ss2{constructor(A,B,Q,D,Z){if(this.channel=A,this.method=B,this.filterStackFactory=D,this.callNumber=Z,this.child=null,this.readPending=!1,this.pendingMessage=null,this.pendingHalfClose=!1,this.ended=!1,this.readFilterPending=!1,this.writeFilterPending=!1,this.pendingChildStatus=null,this.metadata=null,this.listener=null,this.statusWatchers=[],this.deadlineTimer=setTimeout(()=>{},0),this.filterStack=null,this.deadlineStartTime=null,this.configReceivedTime=null,this.childStartTime=null,this.credentials=rV6.CallCredentials.createEmpty(),this.deadline=Q.deadline,this.host=Q.host,Q.parentCall){if(Q.flags&gu.Propagate.CANCELLATION)Q.parentCall.on("cancelled",()=>{this.cancelWithStatus(gu.Status.CANCELLED,"Cancelled by parent call")});if(Q.flags&gu.Propagate.DEADLINE)this.trace("Propagating deadline from parent: "+Q.parentCall.getDeadline()),this.deadline=uu.minDeadline(this.deadline,Q.parentCall.getDeadline())}this.trace("Created"),this.runDeadlineTimer()}trace(A){oV6.trace(gu.LogVerbosity.DEBUG,eV6,"["+this.callNumber+"] "+A)}runDeadlineTimer(){clearTimeout(this.deadlineTimer),this.deadlineStartTime=new Date,this.trace("Deadline: "+uu.deadlineToString(this.deadline));let A=uu.getRelativeTimeout(this.deadline);if(A!==1/0){this.trace("Deadline will be reached in "+A+"ms");let B=()=>{if(!this.deadlineStartTime){this.cancelWithStatus(gu.Status.DEADLINE_EXCEEDED,"Deadline exceeded");return}let Q=[],D=new Date;if(Q.push(`Deadline exceeded after ${uu.formatDateDifference(this.deadlineStartTime,D)}`),this.configReceivedTime){if(this.configReceivedTime>this.deadlineStartTime)Q.push(`name resolution: ${uu.formatDateDifference(this.deadlineStartTime,this.configReceivedTime)}`);if(this.childStartTime){if(this.childStartTime>this.configReceivedTime)Q.push(`metadata filters: ${uu.formatDateDifference(this.configReceivedTime,this.childStartTime)}`)}else Q.push("waiting for metadata filters")}else Q.push("waiting for name resolution");if(this.child)Q.push(...this.child.getDeadlineInfo());this.cancelWithStatus(gu.Status.DEADLINE_EXCEEDED,Q.join(","))};if(A<=0)process.nextTick(B);else this.deadlineTimer=setTimeout(B,A)}}outputStatus(A){if(!this.ended){if(this.ended=!0,!this.filterStack)this.filterStack=this.filterStackFactory.createFilter();clearTimeout(this.deadlineTimer);let B=this.filterStack.receiveTrailers(A);this.trace("ended with status: code="+B.code+' details="'+B.details+'"'),this.statusWatchers.forEach((Q)=>Q(B)),process.nextTick(()=>{var Q;(Q=this.listener)===null||Q===void 0||Q.onReceiveStatus(B)})}}sendMessageOnChild(A,B){if(!this.child)throw new Error("sendMessageonChild called with child not populated");let Q=this.child;this.writeFilterPending=!0,this.filterStack.sendMessage(Promise.resolve({message:B,flags:A.flags})).then((D)=>{if(this.writeFilterPending=!1,Q.sendMessageWithContext(A,D.message),this.pendingHalfClose)Q.halfClose()},(D)=>{this.cancelWithStatus(D.code,D.details)})}getConfig(){if(this.ended)return;if(!this.metadata||!this.listener)throw new Error("getConfig called before start");let A=this.channel.getConfig(this.method,this.metadata);if(A.type==="NONE"){this.channel.queueCallForConfig(this);return}else if(A.type==="ERROR"){if(this.metadata.getOptions().waitForReady)this.channel.queueCallForConfig(this);else this.outputStatus(A.error);return}this.configReceivedTime=new Date;let B=A.config;if(B.status!==gu.Status.OK){let{code:Q,details:D}=tV6.restrictControlPlaneStatusCode(B.status,"Failed to route call to method "+this.method);this.outputStatus({code:Q,details:D,metadata:new as2.Metadata});return}if(B.methodConfig.timeout){let Q=new Date;Q.setSeconds(Q.getSeconds()+B.methodConfig.timeout.seconds),Q.setMilliseconds(Q.getMilliseconds()+B.methodConfig.timeout.nanos/1e6),this.deadline=uu.minDeadline(this.deadline,Q),this.runDeadlineTimer()}this.filterStackFactory.push(B.dynamicFilterFactories),this.filterStack=this.filterStackFactory.createFilter(),this.filterStack.sendMetadata(Promise.resolve(this.metadata)).then((Q)=>{if(this.child=this.channel.createRetryingCall(B,this.method,this.host,this.credentials,this.deadline),this.trace("Created child ["+this.child.getCallNumber()+"]"),this.childStartTime=new Date,this.child.start(Q,{onReceiveMetadata:(D)=>{this.trace("Received metadata"),this.listener.onReceiveMetadata(this.filterStack.receiveMetadata(D))},onReceiveMessage:(D)=>{this.trace("Received message"),this.readFilterPending=!0,this.filterStack.receiveMessage(D).then((Z)=>{if(this.trace("Finished filtering received message"),this.readFilterPending=!1,this.listener.onReceiveMessage(Z),this.pendingChildStatus)this.outputStatus(this.pendingChildStatus)},(Z)=>{this.cancelWithStatus(Z.code,Z.details)})},onReceiveStatus:(D)=>{if(this.trace("Received status"),this.readFilterPending)this.pendingChildStatus=D;else this.outputStatus(D)}}),this.readPending)this.child.startRead();if(this.pendingMessage)this.sendMessageOnChild(this.pendingMessage.context,this.pendingMessage.message);else if(this.pendingHalfClose)this.child.halfClose()},(Q)=>{this.outputStatus(Q)})}reportResolverError(A){var B;if((B=this.metadata)===null||B===void 0?void 0:B.getOptions().waitForReady)this.channel.queueCallForConfig(this);else this.outputStatus(A)}cancelWithStatus(A,B){var Q;this.trace("cancelWithStatus code: "+A+' details: "'+B+'"'),(Q=this.child)===null||Q===void 0||Q.cancelWithStatus(A,B),this.outputStatus({code:A,details:B,metadata:new as2.Metadata})}getPeer(){var A,B;return(B=(A=this.child)===null||A===void 0?void 0:A.getPeer())!==null&&B!==void 0?B:this.channel.getTarget()}start(A,B){this.trace("start called"),this.metadata=A.clone(),this.listener=B,this.getConfig()}sendMessageWithContext(A,B){if(this.trace("write() called with message of length "+B.length),this.child)this.sendMessageOnChild(A,B);else this.pendingMessage={context:A,message:B}}startRead(){if(this.trace("startRead called"),this.child)this.child.startRead();else this.readPending=!0}halfClose(){if(this.trace("halfClose called"),this.child&&!this.writeFilterPending)this.child.halfClose();else this.pendingHalfClose=!0}setCredentials(A){this.credentials=A}addStatusWatcher(A){this.statusWatchers.push(A)}getCallNumber(){return this.callNumber}}rs2.ResolvingCall=ss2});
var u31=E((us2)=>{Object.defineProperty(us2,"__esModule",{value:!0});us2.minDeadline=kV6;us2.getDeadlineTimeoutString=_V6;us2.getRelativeTimeout=vV6;us2.deadlineToString=bV6;us2.formatDateDifference=fV6;function kV6(...A){let B=1/0;for(let Q of A){let D=Q instanceof Date?Q.getTime():Q;if(D<B)B=D}return B}var yV6=[["m",1],["S",1000],["M",60000],["H",3600000]];function _V6(A){let B=new Date().getTime();if(A instanceof Date)A=A.getTime();let Q=Math.max(A-B,0);for(let[D,Z]of yV6){let G=Q/Z;if(G<1e8)return String(Math.ceil(G))+D}throw new Error("Deadline is too far in the future")}var xV6=2147483647;function vV6(A){let B=A instanceof Date?A.getTime():A,Q=new Date().getTime(),D=B-Q;if(D<0)return 0;else if(D>xV6)return 1/0;else return D}function bV6(A){if(A instanceof Date)return A.toISOString();else{let B=new Date(A);if(Number.isNaN(B.getTime()))return""+A;else return B.toISOString()}}function fV6(A,B){return((B.getTime()-A.getTime())/1000).toFixed(3)+"s"}});
var vT1=E((c_5,qn2)=>{qn2.exports=sL;var JW6=pL();function sL(A){if(A)for(var B=Object.keys(A),Q=0;Q<B.length;++Q)this[B[Q]]=A[B[Q]]}sL.create=function A(B){return this.$type.create(B)};sL.encode=function A(B,Q){return this.$type.encode(B,Q)};sL.encodeDelimited=function A(B,Q){return this.$type.encodeDelimited(B,Q)};sL.decode=function A(B){return this.$type.decode(B)};sL.decodeDelimited=function A(B){return this.$type.decodeDelimited(B)};sL.verify=function A(B){return this.$type.verify(B)};sL.fromObject=function A(B){return this.$type.fromObject(B)};sL.toObject=function A(B,Q){return this.$type.toObject(B,Q)};sL.prototype.toJSON=function A(){return this.$type.toObject(this,JW6.toJSONOptions)}});
var vW0=E((Ls2)=>{Object.defineProperty(Ls2,"__esModule",{value:!0});Ls2.FilterStackFactory=Ls2.FilterStack=void 0;class _W0{constructor(A){this.filters=A}sendMetadata(A){let B=A;for(let Q=0;Q<this.filters.length;Q++)B=this.filters[Q].sendMetadata(B);return B}receiveMetadata(A){let B=A;for(let Q=this.filters.length-1;Q>=0;Q--)B=this.filters[Q].receiveMetadata(B);return B}sendMessage(A){let B=A;for(let Q=0;Q<this.filters.length;Q++)B=this.filters[Q].sendMessage(B);return B}receiveMessage(A){let B=A;for(let Q=this.filters.length-1;Q>=0;Q--)B=this.filters[Q].receiveMessage(B);return B}receiveTrailers(A){let B=A;for(let Q=this.filters.length-1;Q>=0;Q--)B=this.filters[Q].receiveTrailers(B);return B}push(A){this.filters.unshift(...A)}getFilters(){return this.filters}}Ls2.FilterStack=_W0;class xW0{constructor(A){this.factories=A}push(A){this.factories.unshift(...A)}clone(){return new xW0([...this.factories])}createFilter(){return new _W0(this.factories.map((A)=>A.createFilter()))}}Ls2.FilterStackFactory=xW0});
var vp2=E((_p2)=>{Object.defineProperty(_p2,"__esModule",{value:!0});_p2.ResolvingLoadBalancer=void 0;var FI6=yu(),II6=EY0(),jV=VE(),YI6=nL(),z31=p_(),WI6=H31(),UY0=S6(),JI6=tW(),XI6=D7(),VI6=S6(),CI6=SV(),KI6=qT1(),HI6="resolving_load_balancer";function kp2(A){XI6.trace(VI6.LogVerbosity.DEBUG,HI6,A)}var zI6=["SERVICE_AND_METHOD","SERVICE","EMPTY"];function EI6(A,B,Q,D){for(let Z of Q.name)switch(D){case"EMPTY":if(!Z.service&&!Z.method)return!0;break;case"SERVICE":if(Z.service===A&&!Z.method)return!0;break;case"SERVICE_AND_METHOD":if(Z.service===A&&Z.method===B)return!0}return!1}function UI6(A,B,Q,D){for(let Z of Q)if(EI6(A,B,Z,D))return Z;return null}function wI6(A){return{invoke(B,Q){var D,Z;let G=B.split("/").filter((Y)=>Y.length>0),F=(D=G[0])!==null&&D!==void 0?D:"",I=(Z=G[1])!==null&&Z!==void 0?Z:"";if(A&&A.methodConfig)for(let Y of zI6){let W=UI6(F,I,A.methodConfig,Y);if(W)return{methodConfig:W,pickInformation:{},status:UY0.Status.OK,dynamicFilterFactories:[]}}return{methodConfig:{name:[]},pickInformation:{},status:UY0.Status.OK,dynamicFilterFactories:[]}},unref(){}}}class yp2{constructor(A,B,Q,D,Z){if(this.target=A,this.channelControlHelper=B,this.channelOptions=Q,this.onSuccessfulResolution=D,this.onFailedResolution=Z,this.latestChildState=jV.ConnectivityState.IDLE,this.latestChildPicker=new z31.QueuePicker(this),this.latestChildErrorMessage=null,this.currentState=jV.ConnectivityState.IDLE,this.previousServiceConfig=null,this.continueResolving=!1,Q["grpc.service_config"])this.defaultServiceConfig=II6.validateServiceConfig(JSON.parse(Q["grpc.service_config"]));else this.defaultServiceConfig={loadBalancingConfig:[],methodConfig:[]};this.updateState(jV.ConnectivityState.IDLE,new z31.QueuePicker(this),null),this.childLoadBalancer=new KI6.ChildLoadBalancerHandler({createSubchannel:B.createSubchannel.bind(B),requestReresolution:()=>{if(this.backoffTimeout.isRunning())kp2("requestReresolution delayed by backoff timer until "+this.backoffTimeout.getEndTime().toISOString()),this.continueResolving=!0;else this.updateResolution()},updateState:(F,I,Y)=>{this.latestChildState=F,this.latestChildPicker=I,this.latestChildErrorMessage=Y,this.updateState(F,I,Y)},addChannelzChild:B.addChannelzChild.bind(B),removeChannelzChild:B.removeChannelzChild.bind(B)}),this.innerResolver=YI6.createResolver(A,{onSuccessfulResolution:(F,I,Y,W,J)=>{var X;this.backoffTimeout.stop(),this.backoffTimeout.reset();let V=null;if(I===null)if(Y===null)this.previousServiceConfig=null,V=this.defaultServiceConfig;else if(this.previousServiceConfig===null)this.handleResolutionFailure(Y);else V=this.previousServiceConfig;else V=I,this.previousServiceConfig=I;let C=(X=V===null||V===void 0?void 0:V.loadBalancingConfig)!==null&&X!==void 0?X:[],K=FI6.selectLbConfigFromList(C,!0);if(K===null){this.handleResolutionFailure({code:UY0.Status.UNAVAILABLE,details:"All load balancer options in service config are not compatible",metadata:new JI6.Metadata}),W===null||W===void 0||W.unref();return}this.childLoadBalancer.updateAddressList(F,K,Object.assign(Object.assign({},this.channelOptions),J));let H=V!==null&&V!==void 0?V:this.defaultServiceConfig;this.onSuccessfulResolution(H,W!==null&&W!==void 0?W:wI6(H))},onError:(F)=>{this.handleResolutionFailure(F)}},Q);let G={initialDelay:Q["grpc.initial_reconnect_backoff_ms"],maxDelay:Q["grpc.max_reconnect_backoff_ms"]};this.backoffTimeout=new WI6.BackoffTimeout(()=>{if(this.continueResolving)this.updateResolution(),this.continueResolving=!1;else this.updateState(this.latestChildState,this.latestChildPicker,this.latestChildErrorMessage)},G),this.backoffTimeout.unref()}updateResolution(){if(this.innerResolver.updateResolution(),this.currentState===jV.ConnectivityState.IDLE)this.updateState(jV.ConnectivityState.CONNECTING,this.latestChildPicker,this.latestChildErrorMessage);this.backoffTimeout.runOnce()}updateState(A,B,Q){if(kp2(CI6.uriToString(this.target)+" "+jV.ConnectivityState[this.currentState]+" -> "+jV.ConnectivityState[A]),A===jV.ConnectivityState.IDLE)B=new z31.QueuePicker(this,B);this.currentState=A,this.channelControlHelper.updateState(A,B,Q)}handleResolutionFailure(A){if(this.latestChildState===jV.ConnectivityState.IDLE)this.updateState(jV.ConnectivityState.TRANSIENT_FAILURE,new z31.UnavailablePicker(A),A.details),this.onFailedResolution(A)}exitIdle(){if(this.currentState===jV.ConnectivityState.IDLE||this.currentState===jV.ConnectivityState.TRANSIENT_FAILURE)if(this.backoffTimeout.isRunning())this.continueResolving=!0;else this.updateResolution();this.childLoadBalancer.exitIdle()}updateAddressList(A,B){throw new Error("updateAddressList not supported on ResolvingLoadBalancer")}resetBackoff(){this.backoffTimeout.reset(),this.childLoadBalancer.resetBackoff()}destroy(){this.childLoadBalancer.destroy(),this.innerResolver.destroy(),this.backoffTimeout.reset(),this.backoffTimeout.stop(),this.latestChildState=jV.ConnectivityState.IDLE,this.latestChildPicker=new z31.QueuePicker(this),this.currentState=jV.ConnectivityState.IDLE,this.previousServiceConfig=null,this.continueResolving=!1}getTypeName(){return"resolving_load_balancer"}}_p2.ResolvingLoadBalancer=yp2});
var vu=E((o_5,fn2)=>{fn2.exports=PK;PK.className="ReflectionObject";var dT1=KI(),cT1;function PK(A,B){if(!dT1.isString(A))throw TypeError("name must be a string");if(B&&!dT1.isObject(B))throw TypeError("options must be an object");this.options=B,this.parsedOptions=null,this.name=A,this.parent=null,this.resolved=!1,this.comment=null,this.filename=null}Object.defineProperties(PK.prototype,{root:{get:function(){var A=this;while(A.parent!==null)A=A.parent;return A}},fullName:{get:function(){var A=[this.name],B=this.parent;while(B)A.unshift(B.name),B=B.parent;return A.join(".")}}});PK.prototype.toJSON=function A(){throw Error()};PK.prototype.onAdd=function A(B){if(this.parent&&this.parent!==B)this.parent.remove(this);this.parent=B,this.resolved=!1;var Q=B.root;if(Q instanceof cT1)Q._handleAdd(this)};PK.prototype.onRemove=function A(B){var Q=B.root;if(Q instanceof cT1)Q._handleRemove(this);this.parent=null,this.resolved=!1};PK.prototype.resolve=function A(){if(this.resolved)return this;if(this.root instanceof cT1)this.resolved=!0;return this};PK.prototype.getOption=function A(B){if(this.options)return this.options[B];return};PK.prototype.setOption=function A(B,Q,D){if(!D||!this.options||this.options[B]===void 0)(this.options||(this.options={}))[B]=Q;return this};PK.prototype.setParsedOption=function A(B,Q,D){if(!this.parsedOptions)this.parsedOptions=[];var Z=this.parsedOptions;if(D){var G=Z.find(function(Y){return Object.prototype.hasOwnProperty.call(Y,B)});if(G){var F=G[B];dT1.setProperty(F,D,Q)}else G={},G[B]=dT1.setProperty({},D,Q),Z.push(G)}else{var I={};I[B]=Q,Z.push(I)}return this};PK.prototype.setOptions=function A(B,Q){if(B)for(var D=Object.keys(B),Z=0;Z<D.length;++Z)this.setOption(D[Z],B[D[Z]],Q);return this};PK.prototype.toString=function A(){var B=this.constructor.className,Q=this.fullName;if(Q.length)return B+" "+Q;return B};PK._configure=function(A){cT1=A}});
var ws2=E((Es2)=>{Object.defineProperty(Es2,"__esModule",{value:!0});Es2.Http2SubchannelConnector=void 0;var DP1=J1("http2"),BP1=hu(),h31=S6(),QV6=TW0(),It=D7(),DV6=nL(),QP1=CE(),kW0=SV(),ZV6=J1("net"),GV6=Cs2(),FV6=jW0(),yW0="transport",IV6="transport_flowctrl",YV6=AY0().version,{HTTP2_HEADER_AUTHORITY:WV6,HTTP2_HEADER_CONTENT_TYPE:JV6,HTTP2_HEADER_METHOD:XV6,HTTP2_HEADER_PATH:VV6,HTTP2_HEADER_TE:CV6,HTTP2_HEADER_USER_AGENT:KV6}=DP1.constants,HV6=20000,zV6=Buffer.from("too_many_pings","ascii");class Hs2{constructor(A,B,Q,D){if(this.session=A,this.options=Q,this.remoteName=D,this.keepaliveTimer=null,this.pendingSendKeepalivePing=!1,this.activeCalls=new Set,this.disconnectListeners=[],this.disconnectHandled=!1,this.channelzEnabled=!0,this.keepalivesSent=0,this.messagesSent=0,this.messagesReceived=0,this.lastMessageSentTimestamp=null,this.lastMessageReceivedTimestamp=null,this.subchannelAddressString=QP1.subchannelAddressToString(B),Q["grpc.enable_channelz"]===0)this.channelzEnabled=!1,this.streamTracker=new BP1.ChannelzCallTrackerStub;else this.streamTracker=new BP1.ChannelzCallTracker;if(this.channelzRef=BP1.registerChannelzSocket(this.subchannelAddressString,()=>this.getChannelzInfo(),this.channelzEnabled),this.userAgent=[Q["grpc.primary_user_agent"],`grpc-node-js/${YV6}`,Q["grpc.secondary_user_agent"]].filter((Z)=>Z).join(" "),"grpc.keepalive_time_ms"in Q)this.keepaliveTimeMs=Q["grpc.keepalive_time_ms"];else this.keepaliveTimeMs=-1;if("grpc.keepalive_timeout_ms"in Q)this.keepaliveTimeoutMs=Q["grpc.keepalive_timeout_ms"];else this.keepaliveTimeoutMs=HV6;if("grpc.keepalive_permit_without_calls"in Q)this.keepaliveWithoutCalls=Q["grpc.keepalive_permit_without_calls"]===1;else this.keepaliveWithoutCalls=!1;if(A.once("close",()=>{this.trace("session closed"),this.handleDisconnect()}),A.once("goaway",(Z,G,F)=>{let I=!1;if(Z===DP1.constants.NGHTTP2_ENHANCE_YOUR_CALM&&F&&F.equals(zV6))I=!0;this.trace("connection closed by GOAWAY with code "+Z+" and data "+(F===null||F===void 0?void 0:F.toString())),this.reportDisconnectToOwner(I)}),A.once("error",(Z)=>{this.trace("connection closed with error "+Z.message),this.handleDisconnect()}),A.socket.once("close",(Z)=>{this.trace("connection closed. hadError="+Z),this.handleDisconnect()}),It.isTracerEnabled(yW0))A.on("remoteSettings",(Z)=>{this.trace("new settings received"+(this.session!==A?" on the old connection":"")+": "+JSON.stringify(Z))}),A.on("localSettings",(Z)=>{this.trace("local settings acknowledged by remote"+(this.session!==A?" on the old connection":"")+": "+JSON.stringify(Z))});if(this.keepaliveWithoutCalls)this.maybeStartKeepalivePingTimer()}getChannelzInfo(){var A,B,Q;let D=this.session.socket,Z=D.remoteAddress?QP1.stringToSubchannelAddress(D.remoteAddress,D.remotePort):null,G=D.localAddress?QP1.stringToSubchannelAddress(D.localAddress,D.localPort):null,F;if(this.session.encrypted){let Y=D,W=Y.getCipher(),J=Y.getCertificate(),X=Y.getPeerCertificate();F={cipherSuiteStandardName:(A=W.standardName)!==null&&A!==void 0?A:null,cipherSuiteOtherName:W.standardName?null:W.name,localCertificate:J&&"raw"in J?J.raw:null,remoteCertificate:X&&"raw"in X?X.raw:null}}else F=null;return{remoteAddress:Z,localAddress:G,security:F,remoteName:this.remoteName,streamsStarted:this.streamTracker.callsStarted,streamsSucceeded:this.streamTracker.callsSucceeded,streamsFailed:this.streamTracker.callsFailed,messagesSent:this.messagesSent,messagesReceived:this.messagesReceived,keepAlivesSent:this.keepalivesSent,lastLocalStreamCreatedTimestamp:this.streamTracker.lastCallStartedTimestamp,lastRemoteStreamCreatedTimestamp:null,lastMessageSentTimestamp:this.lastMessageSentTimestamp,lastMessageReceivedTimestamp:this.lastMessageReceivedTimestamp,localFlowControlWindow:(B=this.session.state.localWindowSize)!==null&&B!==void 0?B:null,remoteFlowControlWindow:(Q=this.session.state.remoteWindowSize)!==null&&Q!==void 0?Q:null}}trace(A){It.trace(h31.LogVerbosity.DEBUG,yW0,"("+this.channelzRef.id+") "+this.subchannelAddressString+" "+A)}keepaliveTrace(A){It.trace(h31.LogVerbosity.DEBUG,"keepalive","("+this.channelzRef.id+") "+this.subchannelAddressString+" "+A)}flowControlTrace(A){It.trace(h31.LogVerbosity.DEBUG,IV6,"("+this.channelzRef.id+") "+this.subchannelAddressString+" "+A)}internalsTrace(A){It.trace(h31.LogVerbosity.DEBUG,"transport_internals","("+this.channelzRef.id+") "+this.subchannelAddressString+" "+A)}reportDisconnectToOwner(A){if(this.disconnectHandled)return;this.disconnectHandled=!0,this.disconnectListeners.forEach((B)=>B(A))}handleDisconnect(){this.clearKeepaliveTimeout(),this.reportDisconnectToOwner(!1);for(let A of this.activeCalls)A.onDisconnect();setImmediate(()=>{this.session.destroy()})}addDisconnectListener(A){this.disconnectListeners.push(A)}canSendPing(){return!this.session.destroyed&&this.keepaliveTimeMs>0&&(this.keepaliveWithoutCalls||this.activeCalls.size>0)}maybeSendPing(){var A,B;if(!this.canSendPing()){this.pendingSendKeepalivePing=!0;return}if(this.keepaliveTimer){console.error("keepaliveTimeout is not null");return}if(this.channelzEnabled)this.keepalivesSent+=1;this.keepaliveTrace("Sending ping with timeout "+this.keepaliveTimeoutMs+"ms"),this.keepaliveTimer=setTimeout(()=>{this.keepaliveTimer=null,this.keepaliveTrace("Ping timeout passed without response"),this.handleDisconnect()},this.keepaliveTimeoutMs),(B=(A=this.keepaliveTimer).unref)===null||B===void 0||B.call(A);let Q="";try{if(!this.session.ping((Z,G,F)=>{if(this.clearKeepaliveTimeout(),Z)this.keepaliveTrace("Ping failed with error "+Z.message),this.handleDisconnect();else this.keepaliveTrace("Received ping response"),this.maybeStartKeepalivePingTimer()}))Q="Ping returned false"}catch(D){Q=(D instanceof Error?D.message:"")||"Unknown error"}if(Q)this.keepaliveTrace("Ping send failed: "+Q),this.handleDisconnect()}maybeStartKeepalivePingTimer(){var A,B;if(!this.canSendPing())return;if(this.pendingSendKeepalivePing)this.pendingSendKeepalivePing=!1,this.maybeSendPing();else if(!this.keepaliveTimer)this.keepaliveTrace("Starting keepalive timer for "+this.keepaliveTimeMs+"ms"),this.keepaliveTimer=setTimeout(()=>{this.keepaliveTimer=null,this.maybeSendPing()},this.keepaliveTimeMs),(B=(A=this.keepaliveTimer).unref)===null||B===void 0||B.call(A)}clearKeepaliveTimeout(){if(this.keepaliveTimer)clearTimeout(this.keepaliveTimer),this.keepaliveTimer=null}removeActiveCall(A){if(this.activeCalls.delete(A),this.activeCalls.size===0)this.session.unref()}addActiveCall(A){if(this.activeCalls.add(A),this.activeCalls.size===1){if(this.session.ref(),!this.keepaliveWithoutCalls)this.maybeStartKeepalivePingTimer()}}createCall(A,B,Q,D,Z){let G=A.toHttp2Headers();G[WV6]=B,G[KV6]=this.userAgent,G[JV6]="application/grpc",G[XV6]="POST",G[VV6]=Q,G[CV6]="trailers";let F;try{F=this.session.request(G)}catch(W){throw this.handleDisconnect(),W}this.flowControlTrace("local window size: "+this.session.state.localWindowSize+" remote window size: "+this.session.state.remoteWindowSize),this.internalsTrace("session.closed="+this.session.closed+" session.destroyed="+this.session.destroyed+" session.socket.destroyed="+this.session.socket.destroyed);let I,Y;if(this.channelzEnabled)this.streamTracker.addCallStarted(),I={addMessageSent:()=>{var W;this.messagesSent+=1,this.lastMessageSentTimestamp=new Date,(W=Z.addMessageSent)===null||W===void 0||W.call(Z)},addMessageReceived:()=>{var W;this.messagesReceived+=1,this.lastMessageReceivedTimestamp=new Date,(W=Z.addMessageReceived)===null||W===void 0||W.call(Z)},onCallEnd:(W)=>{var J;(J=Z.onCallEnd)===null||J===void 0||J.call(Z,W),this.removeActiveCall(Y)},onStreamEnd:(W)=>{var J;if(W)this.streamTracker.addCallSucceeded();else this.streamTracker.addCallFailed();(J=Z.onStreamEnd)===null||J===void 0||J.call(Z,W)}};else I={addMessageSent:()=>{var W;(W=Z.addMessageSent)===null||W===void 0||W.call(Z)},addMessageReceived:()=>{var W;(W=Z.addMessageReceived)===null||W===void 0||W.call(Z)},onCallEnd:(W)=>{var J;(J=Z.onCallEnd)===null||J===void 0||J.call(Z,W),this.removeActiveCall(Y)},onStreamEnd:(W)=>{var J;(J=Z.onStreamEnd)===null||J===void 0||J.call(Z,W)}};return Y=new GV6.Http2SubchannelCall(F,I,D,this,FV6.getNextCallNumber()),this.addActiveCall(Y),Y}getChannelzRef(){return this.channelzRef}getPeerName(){return this.subchannelAddressString}getOptions(){return this.options}shutdown(){this.session.close(),BP1.unregisterChannelzRef(this.channelzRef)}}class zs2{constructor(A){this.channelTarget=A,this.session=null,this.isShutdown=!1}trace(A){It.trace(h31.LogVerbosity.DEBUG,yW0,kW0.uriToString(this.channelTarget)+" "+A)}createSession(A,B,Q){if(this.isShutdown)return Promise.reject();if(A.socket.closed)return Promise.reject("Connection closed before starting HTTP/2 handshake");return new Promise((D,Z)=>{var G;let F=null,I=this.channelTarget;if("grpc.http_connect_target"in Q){let H=kW0.parseUri(Q["grpc.http_connect_target"]);if(H)I=H,F=kW0.uriToString(H)}let Y=A.secure?"https":"http",W=DV6.getDefaultAuthority(I),J=()=>{var H;(H=this.session)===null||H===void 0||H.destroy(),this.session=null,setImmediate(()=>{if(!K)K=!0,Z(`${C.trim()} (${new Date().toISOString()})`)})},X=(H)=>{var z;if((z=this.session)===null||z===void 0||z.destroy(),C=H.message,this.trace("connection failed with error "+C),!K)K=!0,Z(`${C} (${new Date().toISOString()})`)},V=DP1.connect(`${Y}://${W}`,{createConnection:(H,z)=>{return A.socket},settings:{initialWindowSize:(G=Q["grpc-node.flow_control_window"])!==null&&G!==void 0?G:DP1.getDefaultSettings().initialWindowSize}});this.session=V;let C="Failed to connect",K=!1;V.unref(),V.once("remoteSettings",()=>{V.removeAllListeners(),A.socket.removeListener("close",J),A.socket.removeListener("error",X),D(new Hs2(V,B,Q,F)),this.session=null}),V.once("close",J),V.once("error",X),A.socket.once("close",J),A.socket.once("error",X)})}tcpConnect(A,B){return QV6.getProxiedConnection(A,B).then((Q)=>{if(Q)return Q;else return new Promise((D,Z)=>{let G=()=>{Z(new Error("Socket closed"))},F=(Y)=>{Z(Y)},I=ZV6.connect(A,()=>{I.removeListener("close",G),I.removeListener("error",F),D(I)});I.once("close",G),I.once("error",F)})})}async connect(A,B,Q){if(this.isShutdown)return Promise.reject();let D=null,Z=null,G=QP1.subchannelAddressToString(A);try{return this.trace(G+" Waiting for secureConnector to be ready"),await B.waitForReady(),this.trace(G+" secureConnector is ready"),D=await this.tcpConnect(A,Q),this.trace(G+" Established TCP connection"),Z=await B.connect(D),this.trace(G+" Established secure connection"),this.createSession(Z,A,Q)}catch(F){throw D===null||D===void 0||D.destroy(),Z===null||Z===void 0||Z.socket.destroy(),F}}shutdown(){var A;this.isShutdown=!0,(A=this.session)===null||A===void 0||A.close(),this.session=null}}Es2.Http2SubchannelConnector=zs2});
var xT1=E((d_5,$n2)=>{$n2.exports=zE;var a_=At();((zE.prototype=Object.create(a_.prototype)).constructor=zE).className="Service";var mY0=_T1(),P31=KI(),WW6=yI0();function zE(A,B){a_.call(this,A,B),this.methods={},this._methodsArray=null}zE.fromJSON=function A(B,Q){var D=new zE(B,Q.options);if(Q.methods)for(var Z=Object.keys(Q.methods),G=0;G<Z.length;++G)D.add(mY0.fromJSON(Z[G],Q.methods[Z[G]]));if(Q.nested)D.addJSON(Q.nested);return D.comment=Q.comment,D};zE.prototype.toJSON=function A(B){var Q=a_.prototype.toJSON.call(this,B),D=B?Boolean(B.keepComments):!1;return P31.toObject(["options",Q&&Q.options||void 0,"methods",a_.arrayToJSON(this.methodsArray,B)||{},"nested",Q&&Q.nested||void 0,"comment",D?this.comment:void 0])};Object.defineProperty(zE.prototype,"methodsArray",{get:function(){return this._methodsArray||(this._methodsArray=P31.toArray(this.methods))}});function wn2(A){return A._methodsArray=null,A}zE.prototype.get=function A(B){return this.methods[B]||a_.prototype.get.call(this,B)};zE.prototype.resolveAll=function A(){var B=this.methodsArray;for(var Q=0;Q<B.length;++Q)B[Q].resolve();return a_.prototype.resolve.call(this)};zE.prototype.add=function A(B){if(this.get(B.name))throw Error("duplicate name '"+B.name+"' in "+this);if(B instanceof mY0)return this.methods[B.name]=B,B.parent=this,wn2(this);return a_.prototype.add.call(this,B)};zE.prototype.remove=function A(B){if(B instanceof mY0){if(this.methods[B.name]!==B)throw Error(B+" is not a member of "+this);return delete this.methods[B.name],B.parent=null,wn2(this)}return a_.prototype.remove.call(this,B)};zE.prototype.create=function A(B,Q,D){var Z=new WW6.Service(B,Q,D);for(var G=0,F;G<this.methodsArray.length;++G){var I=P31.lcFirst((F=this._methodsArray[G]).resolve().name).replace(/[^$\w_]/g,"");Z[I]=P31.codegen(["r","c"],P31.isReserved(I)?I+"_":I)("return this.rpcCall(m,q,s,r,c)")({m:F,q:F.resolvedRequestType.ctor,s:F.resolvedResponseType.ctor})}return Z}});
var xo2=E((yo2)=>{Object.defineProperty(yo2,"__esModule",{value:!0});yo2.RoundRobinLoadBalancer=void 0;yo2.setup=AH6;var jo2=yu(),rJ=VE(),zJ0=p_(),rK6=D7(),oK6=S6(),Po2=CE(),tK6=UP1(),eK6="round_robin";function So2(A){rK6.trace(oK6.LogVerbosity.DEBUG,eK6,A)}var NP1="round_robin";class EJ0{getLoadBalancerName(){return NP1}constructor(){}toJsonObject(){return{[NP1]:{}}}static createFromJson(A){return new EJ0}}class ko2{constructor(A,B=0){this.children=A,this.nextIndex=B}pick(A){let B=this.children[this.nextIndex].picker;return this.nextIndex=(this.nextIndex+1)%this.children.length,B.pick(A)}peekNextEndpoint(){return this.children[this.nextIndex].endpoint}}class UJ0{constructor(A){this.channelControlHelper=A,this.children=[],this.currentState=rJ.ConnectivityState.IDLE,this.currentReadyPicker=null,this.updatesPaused=!1,this.lastError=null,this.childChannelControlHelper=jo2.createChildChannelControlHelper(A,{updateState:(B,Q,D)=>{if(this.currentState===rJ.ConnectivityState.READY&&B!==rJ.ConnectivityState.READY)this.channelControlHelper.requestReresolution();if(D)this.lastError=D;this.calculateAndUpdateState()}})}countChildrenWithState(A){return this.children.filter((B)=>B.getConnectivityState()===A).length}calculateAndUpdateState(){if(this.updatesPaused)return;if(this.countChildrenWithState(rJ.ConnectivityState.READY)>0){let A=this.children.filter((Q)=>Q.getConnectivityState()===rJ.ConnectivityState.READY),B=0;if(this.currentReadyPicker!==null){let Q=this.currentReadyPicker.peekNextEndpoint();if(B=A.findIndex((D)=>Po2.endpointEqual(D.getEndpoint(),Q)),B<0)B=0}this.updateState(rJ.ConnectivityState.READY,new ko2(A.map((Q)=>({endpoint:Q.getEndpoint(),picker:Q.getPicker()})),B),null)}else if(this.countChildrenWithState(rJ.ConnectivityState.CONNECTING)>0)this.updateState(rJ.ConnectivityState.CONNECTING,new zJ0.QueuePicker(this),null);else if(this.countChildrenWithState(rJ.ConnectivityState.TRANSIENT_FAILURE)>0){let A=`round_robin: No connection established. Last error: ${this.lastError}`;this.updateState(rJ.ConnectivityState.TRANSIENT_FAILURE,new zJ0.UnavailablePicker({details:A}),A)}else this.updateState(rJ.ConnectivityState.IDLE,new zJ0.QueuePicker(this),null);for(let A of this.children)if(A.getConnectivityState()===rJ.ConnectivityState.IDLE)A.exitIdle()}updateState(A,B,Q){if(So2(rJ.ConnectivityState[this.currentState]+" -> "+rJ.ConnectivityState[A]),A===rJ.ConnectivityState.READY)this.currentReadyPicker=B;else this.currentReadyPicker=null;this.currentState=A,this.channelControlHelper.updateState(A,B,Q)}resetSubchannelList(){for(let A of this.children)A.destroy()}updateAddressList(A,B,Q){this.resetSubchannelList(),So2("Connect to endpoint list "+A.map(Po2.endpointToString)),this.updatesPaused=!0,this.children=A.map((D)=>new tK6.LeafLoadBalancer(D,this.childChannelControlHelper,Q));for(let D of this.children)D.startConnecting();this.updatesPaused=!1,this.calculateAndUpdateState()}exitIdle(){}resetBackoff(){}destroy(){this.resetSubchannelList()}getTypeName(){return NP1}}yo2.RoundRobinLoadBalancer=UJ0;function AH6(){jo2.registerLoadBalancerType(NP1,UJ0,EJ0)}});
var xu=E((Fn2)=>{var M31=Fn2,ZW6=KI(),GW6=["double","float","int32","uint32","sint32","fixed32","sfixed32","int64","uint64","sint64","fixed64","sfixed64","bool","string","bytes"];function R31(A,B){var Q=0,D={};B|=0;while(Q<A.length)D[GW6[Q+B]]=A[Q++];return D}M31.basic=R31([1,5,0,0,0,5,5,0,0,0,1,1,0,2,2]);M31.defaults=R31([0,0,0,0,0,0,0,0,0,0,0,0,!1,"",ZW6.emptyArray,null]);M31.long=R31([0,0,0,1,1],7);M31.mapKey=R31([0,0,0,5,5,0,0,0,1,1,0,2],2);M31.packed=R31([1,5,0,0,0,5,5,0,0,0,1,1,0])});
var yT1=E((u_5,En2)=>{En2.exports=WP;var gY0=n_();((WP.prototype=Object.create(gY0.prototype)).constructor=WP).className="MapField";var YW6=xu(),T31=KI();function WP(A,B,Q,D,Z,G){if(gY0.call(this,A,B,D,void 0,void 0,Z,G),!T31.isString(Q))throw TypeError("keyType must be a string");this.keyType=Q,this.resolvedKeyType=null,this.map=!0}WP.fromJSON=function A(B,Q){return new WP(B,Q.id,Q.keyType,Q.type,Q.options,Q.comment)};WP.prototype.toJSON=function A(B){var Q=B?Boolean(B.keepComments):!1;return T31.toObject(["keyType",this.keyType,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",Q?this.comment:void 0])};WP.prototype.resolve=function A(){if(this.resolved)return this;if(YW6.mapKey[this.keyType]===void 0)throw Error("invalid key type: "+this.keyType);return gY0.prototype.resolve.call(this)};WP.d=function A(B,Q,D){if(typeof D==="function")D=T31.decorateType(D).name;else if(D&&typeof D==="object")D=T31.decorateEnum(D).name;return function Z(G,F){T31.decorateType(G.constructor).add(new WP(F,B,Q,D))}}});
var yu=E((Hp2)=>{Object.defineProperty(Hp2,"__esModule",{value:!0});Hp2.createChildChannelControlHelper=EF6;Hp2.registerLoadBalancerType=UF6;Hp2.registerDefaultLoadBalancerType=wF6;Hp2.createLoadBalancer=$F6;Hp2.isLoadBalancerNameRegistered=qF6;Hp2.parseLoadBalancingConfig=Kp2;Hp2.getDefaultConfig=NF6;Hp2.selectLbConfigFromList=LF6;var HF6=D7(),zF6=S6();function EF6(A,B){var Q,D,Z,G,F,I,Y,W,J,X;return{createSubchannel:(D=(Q=B.createSubchannel)===null||Q===void 0?void 0:Q.bind(B))!==null&&D!==void 0?D:A.createSubchannel.bind(A),updateState:(G=(Z=B.updateState)===null||Z===void 0?void 0:Z.bind(B))!==null&&G!==void 0?G:A.updateState.bind(A),requestReresolution:(I=(F=B.requestReresolution)===null||F===void 0?void 0:F.bind(B))!==null&&I!==void 0?I:A.requestReresolution.bind(A),addChannelzChild:(W=(Y=B.addChannelzChild)===null||Y===void 0?void 0:Y.bind(B))!==null&&W!==void 0?W:A.addChannelzChild.bind(A),removeChannelzChild:(X=(J=B.removeChannelzChild)===null||J===void 0?void 0:J.bind(B))!==null&&X!==void 0?X:A.removeChannelzChild.bind(A)}}var l_={},K31=null;function UF6(A,B,Q){l_[A]={LoadBalancer:B,LoadBalancingConfig:Q}}function wF6(A){K31=A}function $F6(A,B){let Q=A.getLoadBalancerName();if(Q in l_)return new l_[Q].LoadBalancer(B);else return null}function qF6(A){return A in l_}function Kp2(A){let B=Object.keys(A);if(B.length!==1)throw new Error("Provided load balancing config has multiple conflicting entries");let Q=B[0];if(Q in l_)try{return l_[Q].LoadBalancingConfig.createFromJson(A[Q])}catch(D){throw new Error(`${Q}: ${D.message}`)}else throw new Error(`Unrecognized load balancing config name ${Q}`)}function NF6(){if(!K31)throw new Error("No default load balancer type registered");return new l_[K31].LoadBalancingConfig}function LF6(A,B=!1){for(let Q of A)try{return Kp2(Q)}catch(D){HF6.log(zF6.LogVerbosity.DEBUG,"Config parsing failed with error",D.message);continue}if(B)if(K31)return new l_[K31].LoadBalancingConfig;else return null;else return null}});

module.exports = Tt2;
