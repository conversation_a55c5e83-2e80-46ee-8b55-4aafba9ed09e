// dependency_chain package extracted with entry point: mXB

var AD=E((O55,oXA)=>{var{defineProperty:Lz1,getOwnPropertyDescriptor:TZQ,getOwnPropertyNames:PZQ}=Object,SZQ=Object.prototype.hasOwnProperty,sXA=(A,B)=>Lz1(A,"name",{value:B,configurable:!0}),jZQ=(A,B)=>{for(var Q in B)Lz1(A,Q,{get:B[Q],enumerable:!0})},kZQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of PZQ(B))if(!SZQ.call(A,Z)&&Z!==Q)Lz1(A,Z,{get:()=>B[Z],enumerable:!(D=TZQ(B,Z))||D.enumerable})}return A},yZQ=(A)=>kZQ(Lz1({},"__esModule",{value:!0}),A),rXA={};jZQ(rXA,{fromArrayBuffer:()=>xZQ,fromString:()=>vZQ});oXA.exports=yZQ(rXA);var _ZQ=aXA(),Fo1=J1("buffer"),xZQ=sXA((A,B=0,Q=A.byteLength-B)=>{if(!_ZQ.isArrayBuffer(A))throw new TypeError(`The "input" argument must be ArrayBuffer. Received type ${typeof A} (${A})`);return Fo1.Buffer.from(A,B,Q)},"fromArrayBuffer"),vZQ=sXA((A,B)=>{if(typeof A!=="string")throw new TypeError(`The "input" argument must be of type string. Received type ${typeof A} (${A})`);return B?Fo1.Buffer.from(A,B):Fo1.Buffer.from(A)},"fromString")});
var B3B=E((Ka5,A3B)=>{var{defineProperty:rj1,getOwnPropertyDescriptor:Wj6,getOwnPropertyNames:Jj6}=Object,Xj6=Object.prototype.hasOwnProperty,oj1=(A,B)=>rj1(A,"name",{value:B,configurable:!0}),Vj6=(A,B)=>{for(var Q in B)rj1(A,Q,{get:B[Q],enumerable:!0})},Cj6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Jj6(B))if(!Xj6.call(A,Z)&&Z!==Q)rj1(A,Z,{get:()=>B[Z],enumerable:!(D=Wj6(B,Z))||D.enumerable})}return A},Kj6=(A)=>Cj6(rj1({},"__esModule",{value:!0}),A),i5B={};Vj6(i5B,{AlgorithmId:()=>r5B,EndpointURLScheme:()=>s5B,FieldPosition:()=>o5B,HttpApiKeyAuthLocation:()=>a5B,HttpAuthLocation:()=>n5B,IniSectionType:()=>t5B,RequestHandlerProtocol:()=>e5B,SMITHY_CONTEXT_KEY:()=>wj6,getDefaultClientConfiguration:()=>Ej6,resolveDefaultRuntimeConfig:()=>Uj6});A3B.exports=Kj6(i5B);var n5B=((A)=>{return A.HEADER="header",A.QUERY="query",A})(n5B||{}),a5B=((A)=>{return A.HEADER="header",A.QUERY="query",A})(a5B||{}),s5B=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(s5B||{}),r5B=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(r5B||{}),Hj6=oj1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),zj6=oj1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),Ej6=oj1((A)=>{return Hj6(A)},"getDefaultClientConfiguration"),Uj6=oj1((A)=>{return zj6(A)},"resolveDefaultRuntimeConfig"),o5B=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(o5B||{}),wj6="__smithy_context",t5B=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(t5B||{}),e5B=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(e5B||{})});
var H3B=E(($a5,K3B)=>{var{defineProperty:Ak1,getOwnPropertyDescriptor:uj6,getOwnPropertyNames:mj6}=Object,dj6=Object.prototype.hasOwnProperty,cj6=(A,B)=>Ak1(A,"name",{value:B,configurable:!0}),lj6=(A,B)=>{for(var Q in B)Ak1(A,Q,{get:B[Q],enumerable:!0})},pj6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of mj6(B))if(!dj6.call(A,Z)&&Z!==Q)Ak1(A,Z,{get:()=>B[Z],enumerable:!(D=uj6(B,Z))||D.enumerable})}return A},ij6=(A)=>pj6(Ak1({},"__esModule",{value:!0}),A),V3B={};lj6(V3B,{buildQueryString:()=>C3B});K3B.exports=ij6(V3B);var YK0=X3B();function C3B(A){let B=[];for(let Q of Object.keys(A).sort()){let D=A[Q];if(Q=YK0.escapeUri(Q),Array.isArray(D))for(let Z=0,G=D.length;Z<G;Z++)B.push(`${Q}=${YK0.escapeUri(D[Z])}`);else{let Z=Q;if(D||typeof D==="string")Z+=`=${YK0.escapeUri(D)}`;B.push(Z)}}return B.join("&")}cj6(C3B,"buildQueryString")});
var I3B=E((Ha5,F3B)=>{var{defineProperty:tj1,getOwnPropertyDescriptor:$j6,getOwnPropertyNames:qj6}=Object,Nj6=Object.prototype.hasOwnProperty,Tx=(A,B)=>tj1(A,"name",{value:B,configurable:!0}),Lj6=(A,B)=>{for(var Q in B)tj1(A,Q,{get:B[Q],enumerable:!0})},Mj6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of qj6(B))if(!Nj6.call(A,Z)&&Z!==Q)tj1(A,Z,{get:()=>B[Z],enumerable:!(D=$j6(B,Z))||D.enumerable})}return A},Rj6=(A)=>Mj6(tj1({},"__esModule",{value:!0}),A),Q3B={};Lj6(Q3B,{Field:()=>Pj6,Fields:()=>Sj6,HttpRequest:()=>jj6,HttpResponse:()=>kj6,IHttpRequest:()=>D3B.HttpRequest,getHttpHandlerExtensionConfiguration:()=>Oj6,isValidHostname:()=>G3B,resolveHttpHandlerRuntimeConfig:()=>Tj6});F3B.exports=Rj6(Q3B);var Oj6=Tx((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),Tj6=Tx((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),D3B=B3B(),Pj6=class{static{Tx(this,"Field")}constructor({name:A,kind:B=D3B.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},Sj6=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{Tx(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},jj6=class A{static{Tx(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=Z3B(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function Z3B(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}Tx(Z3B,"cloneQuery");var kj6=class{static{Tx(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function G3B(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}Tx(G3B,"isValidHostname")});
var M3B=E((La5,Bk1)=>{var{defineProperty:N3B,getOwnPropertyDescriptor:ej6,getOwnPropertyNames:Ak6}=Object,Bk6=Object.prototype.hasOwnProperty,WK0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Ak6(B))if(!Bk6.call(A,Z)&&Z!==Q)N3B(A,Z,{get:()=>B[Z],enumerable:!(D=ej6(B,Z))||D.enumerable})}return A},L3B=(A,B,Q)=>(WK0(A,B,"default"),Q&&WK0(Q,B,"default")),Qk6=(A)=>WK0(N3B({},"__esModule",{value:!0}),A),JK0={};Bk1.exports=Qk6(JK0);L3B(JK0,U3B(),Bk1.exports);L3B(JK0,q3B(),Bk1.exports)});
var U3B=E((z3B)=>{Object.defineProperty(z3B,"__esModule",{value:!0});z3B.fromBase64=void 0;var nj6=AD(),aj6=/^[A-Za-z0-9+/]*={0,2}$/,sj6=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!aj6.exec(A))throw new TypeError("Invalid base64 string.");let B=nj6.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};z3B.fromBase64=sj6});
var VK0=E((Ma5,k3B)=>{var{defineProperty:Dk1,getOwnPropertyDescriptor:Dk6,getOwnPropertyNames:Zk6}=Object,Gk6=Object.prototype.hasOwnProperty,zM=(A,B)=>Dk1(A,"name",{value:B,configurable:!0}),Fk6=(A,B)=>{for(var Q in B)Dk1(A,Q,{get:B[Q],enumerable:!0})},Ik6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Zk6(B))if(!Gk6.call(A,Z)&&Z!==Q)Dk1(A,Z,{get:()=>B[Z],enumerable:!(D=Dk6(B,Z))||D.enumerable})}return A},Yk6=(A)=>Ik6(Dk1({},"__esModule",{value:!0}),A),O3B={};Fk6(O3B,{FetchHttpHandler:()=>Jk6,keepAliveSupport:()=>Qk1,streamCollector:()=>Vk6});k3B.exports=Yk6(O3B);var R3B=I3B(),Wk6=H3B();function XK0(A,B){return new Request(A,B)}zM(XK0,"createRequest");function T3B(A=0){return new Promise((B,Q)=>{if(A)setTimeout(()=>{let D=new Error(`Request did not complete within ${A} ms`);D.name="TimeoutError",Q(D)},A)})}zM(T3B,"requestTimeout");var Qk1={supported:void 0},Jk6=class A{static{zM(this,"FetchHttpHandler")}static create(B){if(typeof B?.handle==="function")return B;return new A(B)}constructor(B){if(typeof B==="function")this.configProvider=B().then((Q)=>Q||{});else this.config=B??{},this.configProvider=Promise.resolve(this.config);if(Qk1.supported===void 0)Qk1.supported=Boolean(typeof Request!=="undefined"&&"keepalive"in XK0("https://[::1]"))}destroy(){}async handle(B,{abortSignal:Q}={}){if(!this.config)this.config=await this.configProvider;let D=this.config.requestTimeout,Z=this.config.keepAlive===!0,G=this.config.credentials;if(Q?.aborted){let $=new Error("Request aborted");return $.name="AbortError",Promise.reject($)}let F=B.path,I=Wk6.buildQueryString(B.query||{});if(I)F+=`?${I}`;if(B.fragment)F+=`#${B.fragment}`;let Y="";if(B.username!=null||B.password!=null){let $=B.username??"",L=B.password??"";Y=`${$}:${L}@`}let{port:W,method:J}=B,X=`${B.protocol}//${Y}${B.hostname}${W?`:${W}`:""}${F}`,V=J==="GET"||J==="HEAD"?void 0:B.body,C={body:V,headers:new Headers(B.headers),method:J,credentials:G};if(this.config?.cache)C.cache=this.config.cache;if(V)C.duplex="half";if(typeof AbortController!=="undefined")C.signal=Q;if(Qk1.supported)C.keepalive=Z;if(typeof this.config.requestInit==="function")Object.assign(C,this.config.requestInit(B));let K=zM(()=>{},"removeSignalEventListener"),H=XK0(X,C),z=[fetch(H).then(($)=>{let L=$.headers,N={};for(let R of L.entries())N[R[0]]=R[1];if($.body==null)return $.blob().then((R)=>({response:new R3B.HttpResponse({headers:N,reason:$.statusText,statusCode:$.status,body:R})}));return{response:new R3B.HttpResponse({headers:N,reason:$.statusText,statusCode:$.status,body:$.body})}}),T3B(D)];if(Q)z.push(new Promise(($,L)=>{let N=zM(()=>{let O=new Error("Request aborted");O.name="AbortError",L(O)},"onAbort");if(typeof Q.addEventListener==="function"){let O=Q;O.addEventListener("abort",N,{once:!0}),K=zM(()=>O.removeEventListener("abort",N),"removeSignalEventListener")}else Q.onabort=N}));return Promise.race(z).finally(K)}updateHttpClientConfig(B,Q){this.config=void 0,this.configProvider=this.configProvider.then((D)=>{return D[B]=Q,D})}httpHandlerConfigs(){return this.config??{}}},Xk6=M3B(),Vk6=zM(async(A)=>{if(typeof Blob==="function"&&A instanceof Blob||A.constructor?.name==="Blob"){if(Blob.prototype.arrayBuffer!==void 0)return new Uint8Array(await A.arrayBuffer());return P3B(A)}return S3B(A)},"streamCollector");async function P3B(A){let B=await j3B(A),Q=Xk6.fromBase64(B);return new Uint8Array(Q)}zM(P3B,"collectBlob");async function S3B(A){let B=[],Q=A.getReader(),D=!1,Z=0;while(!D){let{done:I,value:Y}=await Q.read();if(Y)B.push(Y),Z+=Y.length;D=I}let G=new Uint8Array(Z),F=0;for(let I of B)G.set(I,F),F+=I.length;return G}zM(S3B,"collectStream");function j3B(A){return new Promise((B,Q)=>{let D=new FileReader;D.onloadend=()=>{if(D.readyState!==2)return Q(new Error("Reader aborted too early"));let Z=D.result??"",G=Z.indexOf(","),F=G>-1?G+1:Z.length;B(Z.substring(F))},D.onabort=()=>Q(new Error("Read aborted")),D.onerror=()=>Q(D.error),D.readAsDataURL(A)})}zM(j3B,"readToBase64")});
var X3B=E((wa5,J3B)=>{var{defineProperty:ej1,getOwnPropertyDescriptor:yj6,getOwnPropertyNames:_j6}=Object,xj6=Object.prototype.hasOwnProperty,IK0=(A,B)=>ej1(A,"name",{value:B,configurable:!0}),vj6=(A,B)=>{for(var Q in B)ej1(A,Q,{get:B[Q],enumerable:!0})},bj6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of _j6(B))if(!xj6.call(A,Z)&&Z!==Q)ej1(A,Z,{get:()=>B[Z],enumerable:!(D=yj6(B,Z))||D.enumerable})}return A},fj6=(A)=>bj6(ej1({},"__esModule",{value:!0}),A),Y3B={};vj6(Y3B,{escapeUri:()=>W3B,escapeUriPath:()=>gj6});J3B.exports=fj6(Y3B);var W3B=IK0((A)=>encodeURIComponent(A).replace(/[!'()*]/g,hj6),"escapeUri"),hj6=IK0((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),gj6=IK0((A)=>A.split("/").map(W3B).join("/"),"escapeUriPath")});
var aXA=E((R55,nXA)=>{var{defineProperty:Nz1,getOwnPropertyDescriptor:wZQ,getOwnPropertyNames:$ZQ}=Object,qZQ=Object.prototype.hasOwnProperty,NZQ=(A,B)=>Nz1(A,"name",{value:B,configurable:!0}),LZQ=(A,B)=>{for(var Q in B)Nz1(A,Q,{get:B[Q],enumerable:!0})},MZQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of $ZQ(B))if(!qZQ.call(A,Z)&&Z!==Q)Nz1(A,Z,{get:()=>B[Z],enumerable:!(D=wZQ(B,Z))||D.enumerable})}return A},RZQ=(A)=>MZQ(Nz1({},"__esModule",{value:!0}),A),iXA={};LZQ(iXA,{isArrayBuffer:()=>OZQ});nXA.exports=RZQ(iXA);var OZQ=NZQ((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var cB=E((P55,ZVA)=>{var{defineProperty:Mz1,getOwnPropertyDescriptor:gZQ,getOwnPropertyNames:uZQ}=Object,mZQ=Object.prototype.hasOwnProperty,Io1=(A,B)=>Mz1(A,"name",{value:B,configurable:!0}),dZQ=(A,B)=>{for(var Q in B)Mz1(A,Q,{get:B[Q],enumerable:!0})},cZQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of uZQ(B))if(!mZQ.call(A,Z)&&Z!==Q)Mz1(A,Z,{get:()=>B[Z],enumerable:!(D=gZQ(B,Z))||D.enumerable})}return A},lZQ=(A)=>cZQ(Mz1({},"__esModule",{value:!0}),A),BVA={};dZQ(BVA,{fromUtf8:()=>DVA,toUint8Array:()=>pZQ,toUtf8:()=>iZQ});ZVA.exports=lZQ(BVA);var QVA=AD(),DVA=Io1((A)=>{let B=QVA.fromString(A,"utf8");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength/Uint8Array.BYTES_PER_ELEMENT)},"fromUtf8"),pZQ=Io1((A)=>{if(typeof A==="string")return DVA(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)},"toUint8Array"),iZQ=Io1((A)=>{if(typeof A==="string")return A;if(typeof A!=="object"||typeof A.byteOffset!=="number"||typeof A.byteLength!=="number")throw new Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return QVA.fromArrayBuffer(A.buffer,A.byteOffset,A.byteLength).toString("utf8")},"toUtf8")});
var q3B=E((w3B)=>{Object.defineProperty(w3B,"__esModule",{value:!0});w3B.toBase64=void 0;var rj6=AD(),oj6=cB(),tj6=(A)=>{let B;if(typeof A==="string")B=oj6.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return rj6.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};w3B.toBase64=tj6});

module.exports = mXB;
