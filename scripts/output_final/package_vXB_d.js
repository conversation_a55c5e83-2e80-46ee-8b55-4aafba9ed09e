// E_module package extracted with entry point: vXB

var A52=E((VK5,e82)=>{var{defineProperty:$N1,getOwnPropertyDescriptor:AK4,getOwnPropertyNames:BK4}=Object,QK4=Object.prototype.hasOwnProperty,qN1=(A,B)=>$N1(A,"name",{value:B,configurable:!0}),DK4=(A,B)=>{for(var Q in B)$N1(A,Q,{get:B[Q],enumerable:!0})},ZK4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of BK4(B))if(!QK4.call(A,Z)&&Z!==Q)$N1(A,Z,{get:()=>B[Z],enumerable:!(D=AK4(B,Z))||D.enumerable})}return A},GK4=(A)=>ZK4($N1({},"__esModule",{value:!0}),A),p82={};DK4(p82,{AlgorithmId:()=>s82,EndpointURLScheme:()=>a82,FieldPosition:()=>r82,HttpApiKeyAuthLocation:()=>n82,HttpAuthLocation:()=>i82,IniSectionType:()=>o82,RequestHandlerProtocol:()=>t82,SMITHY_CONTEXT_KEY:()=>JK4,getDefaultClientConfiguration:()=>YK4,resolveDefaultRuntimeConfig:()=>WK4});e82.exports=GK4(p82);var i82=((A)=>{return A.HEADER="header",A.QUERY="query",A})(i82||{}),n82=((A)=>{return A.HEADER="header",A.QUERY="query",A})(n82||{}),a82=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(a82||{}),s82=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(s82||{}),FK4=qN1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),IK4=qN1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),YK4=qN1((A)=>{return FK4(A)},"getDefaultClientConfiguration"),WK4=qN1((A)=>{return IK4(A)},"resolveDefaultRuntimeConfig"),r82=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(r82||{}),JK4="__smithy_context",o82=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(o82||{}),t82=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(t82||{})});
var A70=E((Lz5,oJ2)=>{var{create:iP4,defineProperty:K81,getOwnPropertyDescriptor:nP4,getOwnPropertyNames:aP4,getPrototypeOf:sP4}=Object,rP4=Object.prototype.hasOwnProperty,iL1=(A,B)=>K81(A,"name",{value:B,configurable:!0}),oP4=(A,B)=>{for(var Q in B)K81(A,Q,{get:B[Q],enumerable:!0})},nJ2=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of aP4(B))if(!rP4.call(A,Z)&&Z!==Q)K81(A,Z,{get:()=>B[Z],enumerable:!(D=nP4(B,Z))||D.enumerable})}return A},Wr=(A,B,Q)=>(Q=A!=null?iP4(sP4(A)):{},nJ2(B||!A||!A.__esModule?K81(Q,"default",{value:A,enumerable:!0}):Q,A)),tP4=(A)=>nJ2(K81({},"__esModule",{value:!0}),A),aJ2={};oP4(aJ2,{credentialsTreatedAsExpired:()=>rJ2,credentialsWillNeedRefresh:()=>sJ2,defaultProvider:()=>BS4});oJ2.exports=tP4(aJ2);var e30=W80(),eP4=e5(),kg=eB(),pJ2="AWS_EC2_METADATA_DISABLED",AS4=iL1(async(A)=>{let{ENV_CMDS_FULL_URI:B,ENV_CMDS_RELATIVE_URI:Q,fromContainerMetadata:D,fromInstanceMetadata:Z}=await Promise.resolve().then(()=>Wr($F()));if(process.env[Q]||process.env[B]){A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromHttp/fromContainerMetadata");let{fromHttp:G}=await Promise.resolve().then(()=>Wr(O80()));return kg.chain(G(A),D(A))}if(process.env[pJ2]&&process.env[pJ2]!=="false")return async()=>{throw new kg.CredentialsProviderError("EC2 Instance Metadata Service access disabled",{logger:A.logger})};return A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromInstanceMetadata"),Z(A)},"remoteProvider"),iJ2=!1,BS4=iL1((A={})=>kg.memoize(kg.chain(async()=>{if(A.profile??process.env[eP4.ENV_PROFILE]){if(process.env[e30.ENV_KEY]&&process.env[e30.ENV_SECRET]){if(!iJ2)(A.logger?.warn&&A.logger?.constructor?.name!=="NoOpLogger"?A.logger.warn:console.warn)(`@aws-sdk/credential-provider-node - defaultProvider::fromEnv WARNING:
    Multiple credential sources detected: 
    Both AWS_PROFILE and the pair AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY static credentials are set.
    This SDK will proceed with the AWS_PROFILE value.
    
    However, a future version may change this behavior to prefer the ENV static credentials.
    Please ensure that your environment only sets either the AWS_PROFILE or the
    AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY pair.
`),iJ2=!0}throw new kg.CredentialsProviderError("AWS_PROFILE is set, skipping fromEnv provider.",{logger:A.logger,tryNextLink:!0})}return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromEnv"),e30.fromEnv(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromSSO");let{ssoStartUrl:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoSession:G}=A;if(!B&&!Q&&!D&&!Z&&!G)throw new kg.CredentialsProviderError("Skipping SSO provider in default chain (inputs do not include SSO fields).",{logger:A.logger});let{fromSSO:F}=await Promise.resolve().then(()=>Wr(Z30()));return F(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromIni");let{fromIni:B}=await Promise.resolve().then(()=>Wr(lJ2()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromProcess");let{fromProcess:B}=await Promise.resolve().then(()=>Wr(i30()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromTokenFile");let{fromTokenFile:B}=await Promise.resolve().then(()=>Wr(r30()));return B(A)()},async()=>{return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::remoteProvider"),(await AS4(A))()},async()=>{throw new kg.CredentialsProviderError("Could not load credentials from any providers",{tryNextLink:!1,logger:A.logger})}),rJ2,sJ2),"defaultProvider"),sJ2=iL1((A)=>A?.expiration!==void 0,"credentialsWillNeedRefresh"),rJ2=iL1((A)=>A?.expiration!==void 0&&A.expiration.getTime()-Date.now()<300000,"credentialsTreatedAsExpired")});
var AL1=E((iK5,GD2)=>{var{defineProperty:eN1,getOwnPropertyDescriptor:$E4,getOwnPropertyNames:qE4}=Object,NE4=Object.prototype.hasOwnProperty,P2=(A,B)=>eN1(A,"name",{value:B,configurable:!0}),LE4=(A,B)=>{for(var Q in B)eN1(A,Q,{get:B[Q],enumerable:!0})},ME4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of qE4(B))if(!NE4.call(A,Z)&&Z!==Q)eN1(A,Z,{get:()=>B[Z],enumerable:!(D=$E4(B,Z))||D.enumerable})}return A},RE4=(A)=>ME4(eN1({},"__esModule",{value:!0}),A),c72={};LE4(c72,{Client:()=>OE4,Command:()=>p72,LazyJsonString:()=>Tg,NoOpLogger:()=>NU4,SENSITIVE_STRING:()=>PE4,ServiceException:()=>WU4,_json:()=>r80,collectBody:()=>l80.collectBody,convertMap:()=>LU4,createAggregatedClient:()=>SE4,dateToUtcString:()=>o72,decorateServiceException:()=>t72,emitWarningIfUnsupportedVersion:()=>CU4,expectBoolean:()=>kE4,expectByte:()=>s80,expectFloat32:()=>oN1,expectInt:()=>_E4,expectInt32:()=>n80,expectLong:()=>c61,expectNonNull:()=>vE4,expectNumber:()=>d61,expectObject:()=>i72,expectShort:()=>a80,expectString:()=>bE4,expectUnion:()=>fE4,extendedEncodeURIComponent:()=>l80.extendedEncodeURIComponent,getArrayIfSingleItem:()=>$U4,getDefaultClientConfiguration:()=>UU4,getDefaultExtensionConfiguration:()=>AD2,getValueFromTextNode:()=>BD2,handleFloat:()=>uE4,isSerializableHeaderValue:()=>qU4,limitedParseDouble:()=>e80,limitedParseFloat:()=>mE4,limitedParseFloat32:()=>dE4,loadConfigsForDefaultMode:()=>VU4,logger:()=>l61,map:()=>B50,parseBoolean:()=>jE4,parseEpochTimestamp:()=>AU4,parseRfc3339DateTime:()=>nE4,parseRfc3339DateTimeWithOffset:()=>sE4,parseRfc7231DateTime:()=>eE4,quoteHeader:()=>DD2,resolveDefaultRuntimeConfig:()=>wU4,resolvedPath:()=>l80.resolvedPath,serializeDateTime:()=>SU4,serializeFloat:()=>PU4,splitEvery:()=>ZD2,splitHeader:()=>jU4,strictParseByte:()=>r72,strictParseDouble:()=>t80,strictParseFloat:()=>hE4,strictParseFloat32:()=>n72,strictParseInt:()=>cE4,strictParseInt32:()=>lE4,strictParseLong:()=>s72,strictParseShort:()=>js,take:()=>MU4,throwDefaultError:()=>e72,withBaseException:()=>JU4});GD2.exports=RE4(c72);var l72=Uw(),OE4=class{constructor(A){this.config=A,this.middlewareStack=l72.constructStack()}static{P2(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},l80=$6(),i80=j80(),p72=class{constructor(){this.middlewareStack=l72.constructStack()}static{P2(this,"Command")}static classBuilder(){return new TE4}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[i80.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},TE4=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{P2(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}build(){let A=this,B;return B=class extends p72{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this)}static{P2(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},PE4="***SensitiveInformation***",SE4=P2((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=P2(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),jE4=P2((A)=>{switch(A){case"true":return!0;case"false":return!1;default:throw new Error(`Unable to parse boolean value "${A}"`)}},"parseBoolean"),kE4=P2((A)=>{if(A===null||A===void 0)return;if(typeof A==="number"){if(A===0||A===1)l61.warn(tN1(`Expected boolean, got ${typeof A}: ${A}`));if(A===0)return!1;if(A===1)return!0}if(typeof A==="string"){let B=A.toLowerCase();if(B==="false"||B==="true")l61.warn(tN1(`Expected boolean, got ${typeof A}: ${A}`));if(B==="false")return!1;if(B==="true")return!0}if(typeof A==="boolean")return A;throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)},"expectBoolean"),d61=P2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string"){let B=parseFloat(A);if(!Number.isNaN(B)){if(String(B)!==String(A))l61.warn(tN1(`Expected number but observed string: ${A}`));return B}}if(typeof A==="number")return A;throw new TypeError(`Expected number, got ${typeof A}: ${A}`)},"expectNumber"),yE4=Math.ceil(340282346638528860000000000000000000000),oN1=P2((A)=>{let B=d61(A);if(B!==void 0&&!Number.isNaN(B)&&B!==1/0&&B!==-1/0){if(Math.abs(B)>yE4)throw new TypeError(`Expected 32-bit float, got ${A}`)}return B},"expectFloat32"),c61=P2((A)=>{if(A===null||A===void 0)return;if(Number.isInteger(A)&&!Number.isNaN(A))return A;throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)},"expectLong"),_E4=c61,n80=P2((A)=>o80(A,32),"expectInt32"),a80=P2((A)=>o80(A,16),"expectShort"),s80=P2((A)=>o80(A,8),"expectByte"),o80=P2((A,B)=>{let Q=c61(A);if(Q!==void 0&&xE4(Q,B)!==Q)throw new TypeError(`Expected ${B}-bit integer, got ${A}`);return Q},"expectSizedInt"),xE4=P2((A,B)=>{switch(B){case 32:return Int32Array.of(A)[0];case 16:return Int16Array.of(A)[0];case 8:return Int8Array.of(A)[0]}},"castInt"),vE4=P2((A,B)=>{if(A===null||A===void 0){if(B)throw new TypeError(`Expected a non-null value for ${B}`);throw new TypeError("Expected a non-null value")}return A},"expectNonNull"),i72=P2((A)=>{if(A===null||A===void 0)return;if(typeof A==="object"&&!Array.isArray(A))return A;let B=Array.isArray(A)?"array":typeof A;throw new TypeError(`Expected object, got ${B}: ${A}`)},"expectObject"),bE4=P2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string")return A;if(["boolean","number","bigint"].includes(typeof A))return l61.warn(tN1(`Expected string, got ${typeof A}: ${A}`)),String(A);throw new TypeError(`Expected string, got ${typeof A}: ${A}`)},"expectString"),fE4=P2((A)=>{if(A===null||A===void 0)return;let B=i72(A),Q=Object.entries(B).filter(([,D])=>D!=null).map(([D])=>D);if(Q.length===0)throw new TypeError("Unions must have exactly one non-null member. None were found.");if(Q.length>1)throw new TypeError(`Unions must have exactly one non-null member. Keys ${Q} were not null.`);return B},"expectUnion"),t80=P2((A)=>{if(typeof A=="string")return d61(ys(A));return d61(A)},"strictParseDouble"),hE4=t80,n72=P2((A)=>{if(typeof A=="string")return oN1(ys(A));return oN1(A)},"strictParseFloat32"),gE4=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,ys=P2((A)=>{let B=A.match(gE4);if(B===null||B[0].length!==A.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(A)},"parseNumber"),e80=P2((A)=>{if(typeof A=="string")return a72(A);return d61(A)},"limitedParseDouble"),uE4=e80,mE4=e80,dE4=P2((A)=>{if(typeof A=="string")return a72(A);return oN1(A)},"limitedParseFloat32"),a72=P2((A)=>{switch(A){case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error(`Unable to parse float value: ${A}`)}},"parseFloatString"),s72=P2((A)=>{if(typeof A==="string")return c61(ys(A));return c61(A)},"strictParseLong"),cE4=s72,lE4=P2((A)=>{if(typeof A==="string")return n80(ys(A));return n80(A)},"strictParseInt32"),js=P2((A)=>{if(typeof A==="string")return a80(ys(A));return a80(A)},"strictParseShort"),r72=P2((A)=>{if(typeof A==="string")return s80(ys(A));return s80(A)},"strictParseByte"),tN1=P2((A)=>{return String(new TypeError(A).stack||A).split(`
`).slice(0,5).filter((B)=>!B.includes("stackTraceWarning")).join(`
`)},"stackTraceWarning"),l61={warn:console.warn},pE4=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],A50=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function o72(A){let B=A.getUTCFullYear(),Q=A.getUTCMonth(),D=A.getUTCDay(),Z=A.getUTCDate(),G=A.getUTCHours(),F=A.getUTCMinutes(),I=A.getUTCSeconds(),Y=Z<10?`0${Z}`:`${Z}`,W=G<10?`0${G}`:`${G}`,J=F<10?`0${F}`:`${F}`,X=I<10?`0${I}`:`${I}`;return`${pE4[D]}, ${Y} ${A50[Q]} ${B} ${W}:${J}:${X} GMT`}P2(o72,"dateToUtcString");var iE4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),nE4=P2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=iE4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W]=B,J=js(ks(D)),X=EL(Z,"month",1,12),V=EL(G,"day",1,31);return m61(J,X,V,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})},"parseRfc3339DateTime"),aE4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),sE4=P2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=aE4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W,J]=B,X=js(ks(D)),V=EL(Z,"month",1,12),C=EL(G,"day",1,31),K=m61(X,V,C,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W});if(J.toUpperCase()!="Z")K.setTime(K.getTime()-YU4(J));return K},"parseRfc3339DateTimeWithOffset"),rE4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),oE4=new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),tE4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/),eE4=P2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-7231 date-times must be expressed as strings");let B=rE4.exec(A);if(B){let[Q,D,Z,G,F,I,Y,W]=B;return m61(js(ks(G)),p80(Z),EL(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})}if(B=oE4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return DU4(m61(BU4(G),p80(Z),EL(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W}))}if(B=tE4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return m61(js(ks(W)),p80(D),EL(Z.trimLeft(),"day",1,31),{hours:G,minutes:F,seconds:I,fractionalMilliseconds:Y})}throw new TypeError("Invalid RFC-7231 date-time value")},"parseRfc7231DateTime"),AU4=P2((A)=>{if(A===null||A===void 0)return;let B;if(typeof A==="number")B=A;else if(typeof A==="string")B=t80(A);else if(typeof A==="object"&&A.tag===1)B=A.value;else throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(B)||B===1/0||B===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(B*1000))},"parseEpochTimestamp"),m61=P2((A,B,Q,D)=>{let Z=B-1;return GU4(A,Z,Q),new Date(Date.UTC(A,Z,Q,EL(D.hours,"hour",0,23),EL(D.minutes,"minute",0,59),EL(D.seconds,"seconds",0,60),IU4(D.fractionalMilliseconds)))},"buildDate"),BU4=P2((A)=>{let B=new Date().getUTCFullYear(),Q=Math.floor(B/100)*100+js(ks(A));if(Q<B)return Q+100;return Q},"parseTwoDigitYear"),QU4=1576800000000,DU4=P2((A)=>{if(A.getTime()-new Date().getTime()>QU4)return new Date(Date.UTC(A.getUTCFullYear()-100,A.getUTCMonth(),A.getUTCDate(),A.getUTCHours(),A.getUTCMinutes(),A.getUTCSeconds(),A.getUTCMilliseconds()));return A},"adjustRfc850Year"),p80=P2((A)=>{let B=A50.indexOf(A);if(B<0)throw new TypeError(`Invalid month: ${A}`);return B+1},"parseMonthByShortName"),ZU4=[31,28,31,30,31,30,31,31,30,31,30,31],GU4=P2((A,B,Q)=>{let D=ZU4[B];if(B===1&&FU4(A))D=29;if(Q>D)throw new TypeError(`Invalid day for ${A50[B]} in ${A}: ${Q}`)},"validateDayOfMonth"),FU4=P2((A)=>{return A%4===0&&(A%100!==0||A%400===0)},"isLeapYear"),EL=P2((A,B,Q,D)=>{let Z=r72(ks(A));if(Z<Q||Z>D)throw new TypeError(`${B} must be between ${Q} and ${D}, inclusive`);return Z},"parseDateValue"),IU4=P2((A)=>{if(A===null||A===void 0)return 0;return n72("0."+A)*1000},"parseMilliseconds"),YU4=P2((A)=>{let B=A[0],Q=1;if(B=="+")Q=1;else if(B=="-")Q=-1;else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);let D=Number(A.substring(1,3)),Z=Number(A.substring(4,6));return Q*(D*60+Z)*60*1000},"parseOffsetToMilliseconds"),ks=P2((A)=>{let B=0;while(B<A.length-1&&A.charAt(B)==="0")B++;if(B===0)return A;return A.slice(B)},"stripLeadingZeroes"),WU4=class A extends Error{static{P2(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},t72=P2((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),e72=P2(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=XU4(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw t72(F,B)},"throwDefaultError"),JU4=P2((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{e72({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),XU4=P2((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),VU4=P2((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),d72=!1,CU4=P2((A)=>{if(A&&!d72&&parseInt(A.substring(1,A.indexOf(".")))<16)d72=!0},"emitWarningIfUnsupportedVersion"),KU4=P2((A)=>{let B=[];for(let Q in i80.AlgorithmId){let D=i80.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),HU4=P2((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),zU4=P2((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),EU4=P2((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),AD2=P2((A)=>{return Object.assign(KU4(A),zU4(A))},"getDefaultExtensionConfiguration"),UU4=AD2,wU4=P2((A)=>{return Object.assign(HU4(A),EU4(A))},"resolveDefaultRuntimeConfig"),$U4=P2((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),BD2=P2((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=BD2(A[Q]);return A},"getValueFromTextNode"),qU4=P2((A)=>{return A!=null},"isSerializableHeaderValue"),Tg=P2(function A(B){return Object.assign(new String(B),{deserializeJSON(){return JSON.parse(String(B))},toString(){return String(B)},toJSON(){return String(B)}})},"LazyJsonString");Tg.from=(A)=>{if(A&&typeof A==="object"&&(A instanceof Tg||("deserializeJSON"in A)))return A;else if(typeof A==="string"||Object.getPrototypeOf(A)===String.prototype)return Tg(String(A));return Tg(JSON.stringify(A))};Tg.fromObject=Tg.from;var NU4=class{static{P2(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function B50(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,RU4(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}QD2(D,null,G,F)}return D}P2(B50,"map");var LU4=P2((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),MU4=P2((A,B)=>{let Q={};for(let D in B)QD2(Q,A,B,D);return Q},"take"),RU4=P2((A,B,Q)=>{return B50(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),QD2=P2((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=OU4,Y=TU4,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),OU4=P2((A)=>A!=null,"nonNullish"),TU4=P2((A)=>A,"pass");function DD2(A){if(A.includes(",")||A.includes('"'))A=`"${A.replace(/"/g,"\\\"")}"`;return A}P2(DD2,"quoteHeader");var PU4=P2((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),SU4=P2((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),r80=P2((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(r80);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=r80(A[Q])}return B}return A},"_json");function ZD2(A,B,Q){if(Q<=0||!Number.isInteger(Q))throw new Error("Invalid number of delimiters ("+Q+") for splitEvery.");let D=A.split(B);if(Q===1)return D;let Z=[],G="";for(let F=0;F<D.length;F++){if(G==="")G=D[F];else G+=B+D[F];if((F+1)%Q===0)Z.push(G),G=""}if(G!=="")Z.push(G);return Z}P2(ZD2,"splitEvery");var jU4=P2((A)=>{let B=A.length,Q=[],D=!1,Z=void 0,G=0;for(let F=0;F<B;++F){let I=A[F];switch(I){case'"':if(Z!=="\\")D=!D;break;case",":if(!D)Q.push(A.slice(G,F)),G=F+1;break;default:}Z=I}return Q.push(A.slice(G)),Q.map((F)=>{F=F.trim();let I=F.length;if(I<2)return F;if(F[0]==='"'&&F[I-1]==='"')F=F.slice(1,I-1);return F.replace(/\\"/g,'"')})},"splitHeader")});
var AZ2=E((QH5,IL1)=>{var OD2,TD2,PD2,SD2,jD2,kD2,yD2,_D2,xD2,vD2,bD2,fD2,hD2,GL1,I50,gD2,uD2,mD2,xs,dD2,cD2,lD2,pD2,iD2,nD2,aD2,sD2,rD2,FL1,oD2,tD2,eD2;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof IL1==="object"&&typeof QH5==="object")A(Q(B,Q(QH5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};OD2=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},TD2=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},PD2=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},SD2=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},jD2=function(G,F){return function(I,Y){F(I,Y,G)}},kD2=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},yD2=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},_D2=function(G){return typeof G==="symbol"?G:"".concat(G)},xD2=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},vD2=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},bD2=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},fD2=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},hD2=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))FL1(F,G,I)},FL1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},GL1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},I50=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},gD2=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(I50(arguments[F]));return G},uD2=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},mD2=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},xs=function(G){return this instanceof xs?(this.v=G,this):new xs(G)},dD2=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof xs?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},cD2=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:xs(G[W](X)),done:!1}:J?J(X):X}:J}},lD2=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof GL1==="function"?GL1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},pD2=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};iD2=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")FL1(F,G,I[Y])}return Q(F,G),F},nD2=function(G){return G&&G.__esModule?G:{default:G}},aD2=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},sD2=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},rD2=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},oD2=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};tD2=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},eD2=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",OD2),A("__assign",TD2),A("__rest",PD2),A("__decorate",SD2),A("__param",jD2),A("__esDecorate",kD2),A("__runInitializers",yD2),A("__propKey",_D2),A("__setFunctionName",xD2),A("__metadata",vD2),A("__awaiter",bD2),A("__generator",fD2),A("__exportStar",hD2),A("__createBinding",FL1),A("__values",GL1),A("__read",I50),A("__spread",gD2),A("__spreadArrays",uD2),A("__spreadArray",mD2),A("__await",xs),A("__asyncGenerator",dD2),A("__asyncDelegator",cD2),A("__asyncValues",lD2),A("__makeTemplateObject",pD2),A("__importStar",iD2),A("__importDefault",nD2),A("__classPrivateFieldGet",aD2),A("__classPrivateFieldSet",sD2),A("__classPrivateFieldIn",rD2),A("__addDisposableResource",oD2),A("__disposeResources",tD2),A("__rewriteRelativeImportExtension",eD2)})});
var B30=E((tH5,TY2)=>{var{defineProperty:hL1,getOwnPropertyDescriptor:eM4,getOwnPropertyNames:AR4}=Object,BR4=Object.prototype.hasOwnProperty,a4=(A,B)=>hL1(A,"name",{value:B,configurable:!0}),QR4=(A,B)=>{for(var Q in B)hL1(A,Q,{get:B[Q],enumerable:!0})},DR4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of AR4(B))if(!BR4.call(A,Z)&&Z!==Q)hL1(A,Z,{get:()=>B[Z],enumerable:!(D=eM4(B,Z))||D.enumerable})}return A},ZR4=(A)=>DR4(hL1({},"__esModule",{value:!0}),A),YY2={};QR4(YY2,{$Command:()=>XY2.Command,AccessDeniedException:()=>VY2,AuthorizationPendingException:()=>CY2,CreateTokenCommand:()=>RY2,CreateTokenRequestFilterSensitiveLog:()=>KY2,CreateTokenResponseFilterSensitiveLog:()=>HY2,ExpiredTokenException:()=>zY2,InternalServerException:()=>EY2,InvalidClientException:()=>UY2,InvalidGrantException:()=>wY2,InvalidRequestException:()=>$Y2,InvalidScopeException:()=>qY2,SSOOIDC:()=>OY2,SSOOIDCClient:()=>JY2,SSOOIDCServiceException:()=>VK,SlowDownException:()=>NY2,UnauthorizedClientException:()=>LY2,UnsupportedGrantTypeException:()=>MY2,__Client:()=>WY2.Client});TY2.exports=ZR4(YY2);var BY2=b61(),GR4=f61(),FR4=h61(),QY2=_s(),IR4=V4(),e50=VB(),YR4=TG(),WR4=q6(),DY2=v4(),WY2=QZ(),ZY2=c50(),JR4=a4((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"sso-oauth"})},"resolveClientEndpointParameters"),XR4={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},VR4=sI2(),GY2=e61(),FY2=fL1(),IY2=QZ(),CR4=a4((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),KR4=a4((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),HR4=a4((A,B)=>{let Q=Object.assign(GY2.getAwsRegionExtensionConfiguration(A),IY2.getDefaultExtensionConfiguration(A),FY2.getHttpHandlerExtensionConfiguration(A),CR4(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,GY2.resolveAwsRegionExtensionConfiguration(Q),IY2.resolveDefaultRuntimeConfig(Q),FY2.resolveHttpHandlerRuntimeConfig(Q),KR4(Q))},"resolveRuntimeExtensions"),JY2=class extends WY2.Client{static{a4(this,"SSOOIDCClient")}config;constructor(...[A]){let B=VR4.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=JR4(B),D=QY2.resolveUserAgentConfig(Q),Z=DY2.resolveRetryConfig(D),G=IR4.resolveRegionConfig(Z),F=BY2.resolveHostHeaderConfig(G),I=WR4.resolveEndpointConfig(F),Y=ZY2.resolveHttpAuthSchemeConfig(I),W=HR4(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(QY2.getUserAgentPlugin(this.config)),this.middlewareStack.use(DY2.getRetryPlugin(this.config)),this.middlewareStack.use(YR4.getContentLengthPlugin(this.config)),this.middlewareStack.use(BY2.getHostHeaderPlugin(this.config)),this.middlewareStack.use(GR4.getLoggerPlugin(this.config)),this.middlewareStack.use(FR4.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(e50.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:ZY2.defaultSSOOIDCHttpAuthSchemeParametersProvider,identityProviderConfigProvider:a4(async(J)=>new e50.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(e50.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},zR4=QZ(),ER4=q6(),UR4=T3(),XY2=QZ(),ts=QZ(),wR4=QZ(),VK=class A extends wR4.ServiceException{static{a4(this,"SSOOIDCServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},VY2=class A extends VK{static{a4(this,"AccessDeniedException")}name="AccessDeniedException";$fault="client";error;error_description;constructor(B){super({name:"AccessDeniedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},CY2=class A extends VK{static{a4(this,"AuthorizationPendingException")}name="AuthorizationPendingException";$fault="client";error;error_description;constructor(B){super({name:"AuthorizationPendingException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},KY2=a4((A)=>({...A,...A.clientSecret&&{clientSecret:ts.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:ts.SENSITIVE_STRING},...A.codeVerifier&&{codeVerifier:ts.SENSITIVE_STRING}}),"CreateTokenRequestFilterSensitiveLog"),HY2=a4((A)=>({...A,...A.accessToken&&{accessToken:ts.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:ts.SENSITIVE_STRING},...A.idToken&&{idToken:ts.SENSITIVE_STRING}}),"CreateTokenResponseFilterSensitiveLog"),zY2=class A extends VK{static{a4(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";error;error_description;constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},EY2=class A extends VK{static{a4(this,"InternalServerException")}name="InternalServerException";$fault="server";error;error_description;constructor(B){super({name:"InternalServerException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},UY2=class A extends VK{static{a4(this,"InvalidClientException")}name="InvalidClientException";$fault="client";error;error_description;constructor(B){super({name:"InvalidClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},wY2=class A extends VK{static{a4(this,"InvalidGrantException")}name="InvalidGrantException";$fault="client";error;error_description;constructor(B){super({name:"InvalidGrantException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},$Y2=class A extends VK{static{a4(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";error;error_description;constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},qY2=class A extends VK{static{a4(this,"InvalidScopeException")}name="InvalidScopeException";$fault="client";error;error_description;constructor(B){super({name:"InvalidScopeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},NY2=class A extends VK{static{a4(this,"SlowDownException")}name="SlowDownException";$fault="client";error;error_description;constructor(B){super({name:"SlowDownException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},LY2=class A extends VK{static{a4(this,"UnauthorizedClientException")}name="UnauthorizedClientException";$fault="client";error;error_description;constructor(B){super({name:"UnauthorizedClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},MY2=class A extends VK{static{a4(this,"UnsupportedGrantTypeException")}name="UnsupportedGrantTypeException";$fault="client";error;error_description;constructor(B){super({name:"UnsupportedGrantTypeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},A30=WI(),$R4=VB(),qB=QZ(),qR4=a4(async(A,B)=>{let Q=$R4.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/token");let Z;return Z=JSON.stringify(qB.take(A,{clientId:[],clientSecret:[],code:[],codeVerifier:[],deviceCode:[],grantType:[],redirectUri:[],refreshToken:[],scope:a4((G)=>qB._json(G),"scope")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateTokenCommand"),NR4=a4(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return LR4(A,B);let Q=qB.map({$metadata:az(A)}),D=qB.expectNonNull(qB.expectObject(await A30.parseJsonBody(A.body,B)),"body"),Z=qB.take(D,{accessToken:qB.expectString,expiresIn:qB.expectInt32,idToken:qB.expectString,refreshToken:qB.expectString,tokenType:qB.expectString});return Object.assign(Q,Z),Q},"de_CreateTokenCommand"),LR4=a4(async(A,B)=>{let Q={...A,body:await A30.parseJsonErrorBody(A.body,B)},D=A30.loadRestJsonErrorCode(A,Q.body);switch(D){case"AccessDeniedException":case"com.amazonaws.ssooidc#AccessDeniedException":throw await RR4(Q,B);case"AuthorizationPendingException":case"com.amazonaws.ssooidc#AuthorizationPendingException":throw await OR4(Q,B);case"ExpiredTokenException":case"com.amazonaws.ssooidc#ExpiredTokenException":throw await TR4(Q,B);case"InternalServerException":case"com.amazonaws.ssooidc#InternalServerException":throw await PR4(Q,B);case"InvalidClientException":case"com.amazonaws.ssooidc#InvalidClientException":throw await SR4(Q,B);case"InvalidGrantException":case"com.amazonaws.ssooidc#InvalidGrantException":throw await jR4(Q,B);case"InvalidRequestException":case"com.amazonaws.ssooidc#InvalidRequestException":throw await kR4(Q,B);case"InvalidScopeException":case"com.amazonaws.ssooidc#InvalidScopeException":throw await yR4(Q,B);case"SlowDownException":case"com.amazonaws.ssooidc#SlowDownException":throw await _R4(Q,B);case"UnauthorizedClientException":case"com.amazonaws.ssooidc#UnauthorizedClientException":throw await xR4(Q,B);case"UnsupportedGrantTypeException":case"com.amazonaws.ssooidc#UnsupportedGrantTypeException":throw await vR4(Q,B);default:let Z=Q.body;return MR4({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),MR4=qB.withBaseException(VK),RR4=a4(async(A,B)=>{let Q=qB.map({}),D=A.body,Z=qB.take(D,{error:qB.expectString,error_description:qB.expectString});Object.assign(Q,Z);let G=new VY2({$metadata:az(A),...Q});return qB.decorateServiceException(G,A.body)},"de_AccessDeniedExceptionRes"),OR4=a4(async(A,B)=>{let Q=qB.map({}),D=A.body,Z=qB.take(D,{error:qB.expectString,error_description:qB.expectString});Object.assign(Q,Z);let G=new CY2({$metadata:az(A),...Q});return qB.decorateServiceException(G,A.body)},"de_AuthorizationPendingExceptionRes"),TR4=a4(async(A,B)=>{let Q=qB.map({}),D=A.body,Z=qB.take(D,{error:qB.expectString,error_description:qB.expectString});Object.assign(Q,Z);let G=new zY2({$metadata:az(A),...Q});return qB.decorateServiceException(G,A.body)},"de_ExpiredTokenExceptionRes"),PR4=a4(async(A,B)=>{let Q=qB.map({}),D=A.body,Z=qB.take(D,{error:qB.expectString,error_description:qB.expectString});Object.assign(Q,Z);let G=new EY2({$metadata:az(A),...Q});return qB.decorateServiceException(G,A.body)},"de_InternalServerExceptionRes"),SR4=a4(async(A,B)=>{let Q=qB.map({}),D=A.body,Z=qB.take(D,{error:qB.expectString,error_description:qB.expectString});Object.assign(Q,Z);let G=new UY2({$metadata:az(A),...Q});return qB.decorateServiceException(G,A.body)},"de_InvalidClientExceptionRes"),jR4=a4(async(A,B)=>{let Q=qB.map({}),D=A.body,Z=qB.take(D,{error:qB.expectString,error_description:qB.expectString});Object.assign(Q,Z);let G=new wY2({$metadata:az(A),...Q});return qB.decorateServiceException(G,A.body)},"de_InvalidGrantExceptionRes"),kR4=a4(async(A,B)=>{let Q=qB.map({}),D=A.body,Z=qB.take(D,{error:qB.expectString,error_description:qB.expectString});Object.assign(Q,Z);let G=new $Y2({$metadata:az(A),...Q});return qB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),yR4=a4(async(A,B)=>{let Q=qB.map({}),D=A.body,Z=qB.take(D,{error:qB.expectString,error_description:qB.expectString});Object.assign(Q,Z);let G=new qY2({$metadata:az(A),...Q});return qB.decorateServiceException(G,A.body)},"de_InvalidScopeExceptionRes"),_R4=a4(async(A,B)=>{let Q=qB.map({}),D=A.body,Z=qB.take(D,{error:qB.expectString,error_description:qB.expectString});Object.assign(Q,Z);let G=new NY2({$metadata:az(A),...Q});return qB.decorateServiceException(G,A.body)},"de_SlowDownExceptionRes"),xR4=a4(async(A,B)=>{let Q=qB.map({}),D=A.body,Z=qB.take(D,{error:qB.expectString,error_description:qB.expectString});Object.assign(Q,Z);let G=new LY2({$metadata:az(A),...Q});return qB.decorateServiceException(G,A.body)},"de_UnauthorizedClientExceptionRes"),vR4=a4(async(A,B)=>{let Q=qB.map({}),D=A.body,Z=qB.take(D,{error:qB.expectString,error_description:qB.expectString});Object.assign(Q,Z);let G=new MY2({$metadata:az(A),...Q});return qB.decorateServiceException(G,A.body)},"de_UnsupportedGrantTypeExceptionRes"),az=a4((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),RY2=class extends XY2.Command.classBuilder().ep(XR4).m(function(A,B,Q,D){return[UR4.getSerdePlugin(Q,this.serialize,this.deserialize),ER4.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSSOOIDCService","CreateToken",{}).n("SSOOIDCClient","CreateTokenCommand").f(KY2,HY2).ser(qR4).de(NR4).build(){static{a4(this,"CreateTokenCommand")}},bR4={CreateTokenCommand:RY2},OY2=class extends JY2{static{a4(this,"SSOOIDC")}};zR4.createAggregatedClient(bR4,OY2)});
var B32=E((OK5,A32)=>{var{defineProperty:yN1,getOwnPropertyDescriptor:TH4,getOwnPropertyNames:PH4}=Object,SH4=Object.prototype.hasOwnProperty,_N1=(A,B)=>yN1(A,"name",{value:B,configurable:!0}),jH4=(A,B)=>{for(var Q in B)yN1(A,Q,{get:B[Q],enumerable:!0})},kH4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of PH4(B))if(!SH4.call(A,Z)&&Z!==Q)yN1(A,Z,{get:()=>B[Z],enumerable:!(D=TH4(B,Z))||D.enumerable})}return A},yH4=(A)=>kH4(yN1({},"__esModule",{value:!0}),A),i52={};jH4(i52,{AlgorithmId:()=>r52,EndpointURLScheme:()=>s52,FieldPosition:()=>o52,HttpApiKeyAuthLocation:()=>a52,HttpAuthLocation:()=>n52,IniSectionType:()=>t52,RequestHandlerProtocol:()=>e52,SMITHY_CONTEXT_KEY:()=>fH4,getDefaultClientConfiguration:()=>vH4,resolveDefaultRuntimeConfig:()=>bH4});A32.exports=yH4(i52);var n52=((A)=>{return A.HEADER="header",A.QUERY="query",A})(n52||{}),a52=((A)=>{return A.HEADER="header",A.QUERY="query",A})(a52||{}),s52=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(s52||{}),r52=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(r52||{}),_H4=_N1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),xH4=_N1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),vH4=_N1((A)=>{return _H4(A)},"getDefaultClientConfiguration"),bH4=_N1((A)=>{return xH4(A)},"resolveDefaultRuntimeConfig"),o52=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(o52||{}),fH4="__smithy_context",t52=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(t52||{}),e52=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(e52||{})});
var BYB=E((Ss5,AYB)=>{var{defineProperty:Gy1,getOwnPropertyDescriptor:Zg6,getOwnPropertyNames:Gg6}=Object,Fg6=Object.prototype.hasOwnProperty,zH0=(A,B)=>Gy1(A,"name",{value:B,configurable:!0}),Ig6=(A,B)=>{for(var Q in B)Gy1(A,Q,{get:B[Q],enumerable:!0})},Yg6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Gg6(B))if(!Fg6.call(A,Z)&&Z!==Q)Gy1(A,Z,{get:()=>B[Z],enumerable:!(D=Zg6(B,Z))||D.enumerable})}return A},Wg6=(A)=>Yg6(Gy1({},"__esModule",{value:!0}),A),oIB={};Ig6(oIB,{fromUtf8:()=>eIB,toUint8Array:()=>Jg6,toUtf8:()=>Xg6});AYB.exports=Wg6(oIB);var tIB=rIB(),eIB=zH0((A)=>{let B=tIB.fromString(A,"utf8");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength/Uint8Array.BYTES_PER_ELEMENT)},"fromUtf8"),Jg6=zH0((A)=>{if(typeof A==="string")return eIB(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)},"toUint8Array"),Xg6=zH0((A)=>{if(typeof A==="string")return A;if(typeof A!=="object"||typeof A.byteOffset!=="number"||typeof A.byteLength!=="number")throw new Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return tIB.fromArrayBuffer(A.buffer,A.byteOffset,A.byteLength).toString("utf8")},"toUtf8")});
var BZ2=E((DH5,zw4)=>{zw4.exports={name:"@aws-sdk/client-sso",description:"AWS SDK for JavaScript Sso Client for Node.js, Browser and React Native",version:"3.797.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-sso","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo sso"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.796.0","@aws-sdk/middleware-host-header":"3.775.0","@aws-sdk/middleware-logger":"3.775.0","@aws-sdk/middleware-recursion-detection":"3.775.0","@aws-sdk/middleware-user-agent":"3.796.0","@aws-sdk/region-config-resolver":"3.775.0","@aws-sdk/types":"3.775.0","@aws-sdk/util-endpoints":"3.787.0","@aws-sdk/util-user-agent-browser":"3.775.0","@aws-sdk/util-user-agent-node":"3.796.0","@smithy/config-resolver":"^4.1.0","@smithy/core":"^3.2.0","@smithy/fetch-http-handler":"^5.0.2","@smithy/hash-node":"^4.0.2","@smithy/invalid-dependency":"^4.0.2","@smithy/middleware-content-length":"^4.0.2","@smithy/middleware-endpoint":"^4.1.0","@smithy/middleware-retry":"^4.1.0","@smithy/middleware-serde":"^4.0.3","@smithy/middleware-stack":"^4.0.2","@smithy/node-config-provider":"^4.0.2","@smithy/node-http-handler":"^4.0.4","@smithy/protocol-http":"^5.1.0","@smithy/smithy-client":"^4.2.0","@smithy/types":"^4.2.0","@smithy/url-parser":"^4.0.2","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.8","@smithy/util-defaults-mode-node":"^4.0.8","@smithy/util-endpoints":"^3.0.2","@smithy/util-middleware":"^4.0.2","@smithy/util-retry":"^4.0.2","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.2.2"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sso",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-sso"}}});
var CI2=E((XI2)=>{Object.defineProperty(XI2,"__esModule",{value:!0});XI2.fromBase64=void 0;var YM4=AD(),WM4=/^[A-Za-z0-9+/]*={0,2}$/,JM4=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!WM4.exec(A))throw new TypeError("Invalid base64 string.");let B=YM4.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};XI2.fromBase64=JM4});
var CYB=E((XYB)=>{Object.defineProperty(XYB,"__esModule",{value:!0});XYB.uint32ArrayFrom=void 0;function Eg6(A){if(!Uint32Array.from){var B=new Uint32Array(A.length),Q=0;while(Q<A.length)B[Q]=A[Q],Q+=1;return B}return Uint32Array.from(A)}XYB.uint32ArrayFrom=Eg6});
var D72=E((hK5,Q72)=>{var{defineProperty:dN1,getOwnPropertyDescriptor:Tz4,getOwnPropertyNames:Pz4}=Object,Sz4=Object.prototype.hasOwnProperty,k80=(A,B)=>dN1(A,"name",{value:B,configurable:!0}),jz4=(A,B)=>{for(var Q in B)dN1(A,Q,{get:B[Q],enumerable:!0})},kz4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Pz4(B))if(!Sz4.call(A,Z)&&Z!==Q)dN1(A,Z,{get:()=>B[Z],enumerable:!(D=Tz4(B,Z))||D.enumerable})}return A},yz4=(A)=>kz4(dN1({},"__esModule",{value:!0}),A),A72={};jz4(A72,{escapeUri:()=>B72,escapeUriPath:()=>xz4});Q72.exports=yz4(A72);var B72=k80((A)=>encodeURIComponent(A).replace(/[!'()*]/g,_z4),"escapeUri"),_z4=k80((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),xz4=k80((A)=>A.split("/").map(B72).join("/"),"escapeUriPath")});
var EH0=E((Ue)=>{Object.defineProperty(Ue,"__esModule",{value:!0});Ue.uint32ArrayFrom=Ue.numToUint8=Ue.isEmptyData=Ue.convertToBuffer=void 0;var Ug6=ZYB();Object.defineProperty(Ue,"convertToBuffer",{enumerable:!0,get:function(){return Ug6.convertToBuffer}});var wg6=IYB();Object.defineProperty(Ue,"isEmptyData",{enumerable:!0,get:function(){return wg6.isEmptyData}});var $g6=JYB();Object.defineProperty(Ue,"numToUint8",{enumerable:!0,get:function(){return $g6.numToUint8}});var qg6=CYB();Object.defineProperty(Ue,"uint32ArrayFrom",{enumerable:!0,get:function(){return qg6.uint32ArrayFrom}})});
var F30=E((pY2)=>{Object.defineProperty(pY2,"__esModule",{value:!0});pY2.resolveHttpAuthSchemeConfig=pY2.resolveStsAuthConfig=pY2.defaultSTSHttpAuthSchemeProvider=pY2.defaultSTSHttpAuthSchemeParametersProvider=void 0;var FO4=WI(),G30=I5(),IO4=J81(),YO4=async(A,B,Q)=>{return{operation:G30.getSmithyContext(B).operation,region:await G30.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};pY2.defaultSTSHttpAuthSchemeParametersProvider=YO4;function WO4(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function JO4(A){return{schemeId:"smithy.api#noAuth"}}var XO4=(A)=>{let B=[];switch(A.operation){case"AssumeRoleWithWebIdentity":{B.push(JO4(A));break}default:B.push(WO4(A))}return B};pY2.defaultSTSHttpAuthSchemeProvider=XO4;var VO4=(A)=>Object.assign(A,{stsClientCtor:IO4.STSClient});pY2.resolveStsAuthConfig=VO4;var CO4=(A)=>{let B=pY2.resolveStsAuthConfig(A),Q=FO4.resolveAwsSdkSigV4Config(B);return Object.assign(Q,{authSchemePreference:G30.normalizeProvider(A.authSchemePreference??[])})};pY2.resolveHttpAuthSchemeConfig=CO4});
var F50=E((MD2)=>{Object.defineProperty(MD2,"__esModule",{value:!0});MD2.resolveHttpAuthSchemeConfig=MD2.defaultSSOHttpAuthSchemeProvider=MD2.defaultSSOHttpAuthSchemeParametersProvider=void 0;var Ww4=WI(),G50=I5(),Jw4=async(A,B,Q)=>{return{operation:G50.getSmithyContext(B).operation,region:await G50.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};MD2.defaultSSOHttpAuthSchemeParametersProvider=Jw4;function Xw4(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"awsssoportal",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function ZL1(A){return{schemeId:"smithy.api#noAuth"}}var Vw4=(A)=>{let B=[];switch(A.operation){case"GetRoleCredentials":{B.push(ZL1(A));break}case"ListAccountRoles":{B.push(ZL1(A));break}case"ListAccounts":{B.push(ZL1(A));break}case"Logout":{B.push(ZL1(A));break}default:B.push(Xw4(A))}return B};MD2.defaultSSOHttpAuthSchemeProvider=Vw4;var Cw4=(A)=>{let B=Ww4.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:G50.normalizeProvider(A.authSchemePreference??[])})};MD2.resolveHttpAuthSchemeConfig=Cw4});
var F52=E((CK5,G52)=>{var{defineProperty:NN1,getOwnPropertyDescriptor:XK4,getOwnPropertyNames:VK4}=Object,CK4=Object.prototype.hasOwnProperty,ry=(A,B)=>NN1(A,"name",{value:B,configurable:!0}),KK4=(A,B)=>{for(var Q in B)NN1(A,Q,{get:B[Q],enumerable:!0})},HK4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of VK4(B))if(!CK4.call(A,Z)&&Z!==Q)NN1(A,Z,{get:()=>B[Z],enumerable:!(D=XK4(B,Z))||D.enumerable})}return A},zK4=(A)=>HK4(NN1({},"__esModule",{value:!0}),A),B52={};KK4(B52,{Field:()=>wK4,Fields:()=>$K4,HttpRequest:()=>qK4,HttpResponse:()=>NK4,IHttpRequest:()=>Q52.HttpRequest,getHttpHandlerExtensionConfiguration:()=>EK4,isValidHostname:()=>Z52,resolveHttpHandlerRuntimeConfig:()=>UK4});G52.exports=zK4(B52);var EK4=ry((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),UK4=ry((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),Q52=A52(),wK4=class{static{ry(this,"Field")}constructor({name:A,kind:B=Q52.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},$K4=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{ry(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},qK4=class A{static{ry(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=D52(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function D52(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}ry(D52,"cloneQuery");var NK4=class{static{ry(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function Z52(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}ry(Z52,"isValidHostname")});
var Fy1=E((wH0)=>{Object.defineProperty(wH0,"__esModule",{value:!0});wH0.AwsCrc32=wH0.Crc32=wH0.crc32=void 0;var Mg6=KH0(),Rg6=EH0();function Og6(A){return new wYB().update(A).digest()}wH0.crc32=Og6;var wYB=function(){function A(){this.checksum=4294967295}return A.prototype.update=function(B){var Q,D;try{for(var Z=Mg6.__values(B),G=Z.next();!G.done;G=Z.next()){var F=G.value;this.checksum=this.checksum>>>8^Pg6[(this.checksum^F)&255]}}catch(I){Q={error:I}}finally{try{if(G&&!G.done&&(D=Z.return))D.call(Z)}finally{if(Q)throw Q.error}}return this},A.prototype.digest=function(){return(this.checksum^4294967295)>>>0},A}();wH0.Crc32=wYB;var Tg6=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918000,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],Pg6=Rg6.uint32ArrayFrom(Tg6),Sg6=UYB();Object.defineProperty(wH0,"AwsCrc32",{enumerable:!0,get:function(){return Sg6.AwsCrc32}})});
var HG2=E((CG2)=>{Object.defineProperty(CG2,"__esModule",{value:!0});CG2.getRuntimeConfig=void 0;var Xq4=WI(),Vq4=VB(),Cq4=o61(),Kq4=BZ(),XG2=lZ2(),VG2=cB(),Hq4=F50(),zq4=JG2(),Eq4=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??XG2.fromBase64,base64Encoder:A?.base64Encoder??XG2.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??zq4.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??Hq4.defaultSSOHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new Xq4.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new Vq4.NoAuthSigner}],logger:A?.logger??new Cq4.NoOpLogger,serviceId:A?.serviceId??"SSO",urlParser:A?.urlParser??Kq4.parseUrl,utf8Decoder:A?.utf8Decoder??VG2.fromUtf8,utf8Encoder:A?.utf8Encoder??VG2.toUtf8}};CG2.getRuntimeConfig=Eq4});
var HIB=E((Rs5,uh6)=>{uh6.exports={name:"@aws-sdk/client-bedrock-runtime",description:"AWS SDK for JavaScript Bedrock Runtime Client for Node.js, Browser and React Native",version:"3.797.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-bedrock-runtime","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo bedrock-runtime"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.796.0","@aws-sdk/credential-provider-node":"3.797.0","@aws-sdk/eventstream-handler-node":"3.775.0","@aws-sdk/middleware-eventstream":"3.775.0","@aws-sdk/middleware-host-header":"3.775.0","@aws-sdk/middleware-logger":"3.775.0","@aws-sdk/middleware-recursion-detection":"3.775.0","@aws-sdk/middleware-user-agent":"3.796.0","@aws-sdk/region-config-resolver":"3.775.0","@aws-sdk/types":"3.775.0","@aws-sdk/util-endpoints":"3.787.0","@aws-sdk/util-user-agent-browser":"3.775.0","@aws-sdk/util-user-agent-node":"3.796.0","@smithy/config-resolver":"^4.1.0","@smithy/core":"^3.2.0","@smithy/eventstream-serde-browser":"^4.0.2","@smithy/eventstream-serde-config-resolver":"^4.1.0","@smithy/eventstream-serde-node":"^4.0.2","@smithy/fetch-http-handler":"^5.0.2","@smithy/hash-node":"^4.0.2","@smithy/invalid-dependency":"^4.0.2","@smithy/middleware-content-length":"^4.0.2","@smithy/middleware-endpoint":"^4.1.0","@smithy/middleware-retry":"^4.1.0","@smithy/middleware-serde":"^4.0.3","@smithy/middleware-stack":"^4.0.2","@smithy/node-config-provider":"^4.0.2","@smithy/node-http-handler":"^4.0.4","@smithy/protocol-http":"^5.1.0","@smithy/smithy-client":"^4.2.0","@smithy/types":"^4.2.0","@smithy/url-parser":"^4.0.2","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.8","@smithy/util-defaults-mode-node":"^4.0.8","@smithy/util-endpoints":"^3.0.2","@smithy/util-middleware":"^4.0.2","@smithy/util-retry":"^4.0.2","@smithy/util-stream":"^4.2.0","@smithy/util-utf8":"^4.0.0","@types/uuid":"^9.0.1",tslib:"^2.6.2",uuid:"^9.0.1"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.2.2"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-bedrock-runtime",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-bedrock-runtime"}}});
var HL=E((nC5,C62)=>{var{defineProperty:WN1,getOwnPropertyDescriptor:kX4,getOwnPropertyNames:yX4}=Object,_X4=Object.prototype.hasOwnProperty,Y80=(A,B)=>WN1(A,"name",{value:B,configurable:!0}),xX4=(A,B)=>{for(var Q in B)WN1(A,Q,{get:B[Q],enumerable:!0})},vX4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of yX4(B))if(!_X4.call(A,Z)&&Z!==Q)WN1(A,Z,{get:()=>B[Z],enumerable:!(D=kX4(B,Z))||D.enumerable})}return A},bX4=(A)=>vX4(WN1({},"__esModule",{value:!0}),A),J62={};xX4(J62,{emitWarningIfUnsupportedVersion:()=>fX4,setCredentialFeature:()=>X62,setFeature:()=>V62,state:()=>I80});C62.exports=bX4(J62);var I80={warningEmitted:!1},fX4=Y80((A)=>{if(A&&!I80.warningEmitted&&parseInt(A.substring(1,A.indexOf(".")))<18)I80.warningEmitted=!0,process.emitWarning(`NodeDeprecationWarning: The AWS SDK for JavaScript (v3) will
no longer support Node.js 16.x on January 6, 2025.

To continue receiving updates to AWS services, bug fixes, and security
updates please upgrade to a supported Node.js LTS version.

More information can be found at: https://a.co/74kJMmI`)},"emitWarningIfUnsupportedVersion");function X62(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}Y80(X62,"setCredentialFeature");function V62(A,B,Q){if(!A.__aws_sdk_context)A.__aws_sdk_context={features:{}};else if(!A.__aws_sdk_context.features)A.__aws_sdk_context.features={};A.__aws_sdk_context.features[B]=Q}Y80(V62,"setFeature")});
var I32=E((TK5,F32)=>{var{defineProperty:xN1,getOwnPropertyDescriptor:hH4,getOwnPropertyNames:gH4}=Object,uH4=Object.prototype.hasOwnProperty,ty=(A,B)=>xN1(A,"name",{value:B,configurable:!0}),mH4=(A,B)=>{for(var Q in B)xN1(A,Q,{get:B[Q],enumerable:!0})},dH4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of gH4(B))if(!uH4.call(A,Z)&&Z!==Q)xN1(A,Z,{get:()=>B[Z],enumerable:!(D=hH4(B,Z))||D.enumerable})}return A},cH4=(A)=>dH4(xN1({},"__esModule",{value:!0}),A),Q32={};mH4(Q32,{Field:()=>iH4,Fields:()=>nH4,HttpRequest:()=>aH4,HttpResponse:()=>sH4,IHttpRequest:()=>D32.HttpRequest,getHttpHandlerExtensionConfiguration:()=>lH4,isValidHostname:()=>G32,resolveHttpHandlerRuntimeConfig:()=>pH4});F32.exports=cH4(Q32);var lH4=ty((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),pH4=ty((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),D32=B32(),iH4=class{static{ty(this,"Field")}constructor({name:A,kind:B=D32.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},nH4=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{ty(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},aH4=class A{static{ty(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=Z32(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function Z32(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}ty(Z32,"cloneQuery");var sH4=class{static{ty(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function G32(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}ty(G32,"isValidHostname")});
var IF2=E((MH5,FF2)=>{var{defineProperty:wL1,getOwnPropertyDescriptor:tq4,getOwnPropertyNames:eq4}=Object,AN4=Object.prototype.hasOwnProperty,O6=(A,B)=>wL1(A,"name",{value:B,configurable:!0}),BN4=(A,B)=>{for(var Q in B)wL1(A,Q,{get:B[Q],enumerable:!0})},QN4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of eq4(B))if(!AN4.call(A,Z)&&Z!==Q)wL1(A,Z,{get:()=>B[Z],enumerable:!(D=tq4(B,Z))||D.enumerable})}return A},DN4=(A)=>QN4(wL1({},"__esModule",{value:!0}),A),uG2={};BN4(uG2,{GetRoleCredentialsCommand:()=>DF2,GetRoleCredentialsRequestFilterSensitiveLog:()=>pG2,GetRoleCredentialsResponseFilterSensitiveLog:()=>nG2,InvalidRequestException:()=>mG2,ListAccountRolesCommand:()=>T50,ListAccountRolesRequestFilterSensitiveLog:()=>aG2,ListAccountsCommand:()=>P50,ListAccountsRequestFilterSensitiveLog:()=>sG2,LogoutCommand:()=>ZF2,LogoutRequestFilterSensitiveLog:()=>rG2,ResourceNotFoundException:()=>dG2,RoleCredentialsFilterSensitiveLog:()=>iG2,SSO:()=>GF2,SSOClient:()=>qL1,SSOServiceException:()=>cs,TooManyRequestsException:()=>cG2,UnauthorizedException:()=>lG2,__Client:()=>kB.Client,paginateListAccountRoles:()=>SN4,paginateListAccounts:()=>jN4});FF2.exports=DN4(uG2);var xG2=b61(),ZN4=f61(),GN4=h61(),vG2=_s(),FN4=V4(),yT=VB(),IN4=TG(),B81=q6(),bG2=v4(),fG2=F50(),YN4=O6((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"awsssoportal"})},"resolveClientEndpointParameters"),$L1={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},WN4=qG2(),hG2=e61(),gG2=_G2(),kB=o61(),JN4=O6((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),XN4=O6((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),VN4=O6((A,B)=>{let Q=Object.assign(hG2.getAwsRegionExtensionConfiguration(A),kB.getDefaultExtensionConfiguration(A),gG2.getHttpHandlerExtensionConfiguration(A),JN4(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,hG2.resolveAwsRegionExtensionConfiguration(Q),kB.resolveDefaultRuntimeConfig(Q),gG2.resolveHttpHandlerRuntimeConfig(Q),XN4(Q))},"resolveRuntimeExtensions"),qL1=class extends kB.Client{static{O6(this,"SSOClient")}config;constructor(...[A]){let B=WN4.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=YN4(B),D=vG2.resolveUserAgentConfig(Q),Z=bG2.resolveRetryConfig(D),G=FN4.resolveRegionConfig(Z),F=xG2.resolveHostHeaderConfig(G),I=B81.resolveEndpointConfig(F),Y=fG2.resolveHttpAuthSchemeConfig(I),W=VN4(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(vG2.getUserAgentPlugin(this.config)),this.middlewareStack.use(bG2.getRetryPlugin(this.config)),this.middlewareStack.use(IN4.getContentLengthPlugin(this.config)),this.middlewareStack.use(xG2.getHostHeaderPlugin(this.config)),this.middlewareStack.use(ZN4.getLoggerPlugin(this.config)),this.middlewareStack.use(GN4.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(yT.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:fG2.defaultSSOHttpAuthSchemeParametersProvider,identityProviderConfigProvider:O6(async(J)=>new yT.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(yT.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},NL1=T3(),cs=class A extends kB.ServiceException{static{O6(this,"SSOServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},mG2=class A extends cs{static{O6(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},dG2=class A extends cs{static{O6(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},cG2=class A extends cs{static{O6(this,"TooManyRequestsException")}name="TooManyRequestsException";$fault="client";constructor(B){super({name:"TooManyRequestsException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},lG2=class A extends cs{static{O6(this,"UnauthorizedException")}name="UnauthorizedException";$fault="client";constructor(B){super({name:"UnauthorizedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},pG2=O6((A)=>({...A,...A.accessToken&&{accessToken:kB.SENSITIVE_STRING}}),"GetRoleCredentialsRequestFilterSensitiveLog"),iG2=O6((A)=>({...A,...A.secretAccessKey&&{secretAccessKey:kB.SENSITIVE_STRING},...A.sessionToken&&{sessionToken:kB.SENSITIVE_STRING}}),"RoleCredentialsFilterSensitiveLog"),nG2=O6((A)=>({...A,...A.roleCredentials&&{roleCredentials:iG2(A.roleCredentials)}}),"GetRoleCredentialsResponseFilterSensitiveLog"),aG2=O6((A)=>({...A,...A.accessToken&&{accessToken:kB.SENSITIVE_STRING}}),"ListAccountRolesRequestFilterSensitiveLog"),sG2=O6((A)=>({...A,...A.accessToken&&{accessToken:kB.SENSITIVE_STRING}}),"ListAccountsRequestFilterSensitiveLog"),rG2=O6((A)=>({...A,...A.accessToken&&{accessToken:kB.SENSITIVE_STRING}}),"LogoutRequestFilterSensitiveLog"),A81=WI(),CN4=O6(async(A,B)=>{let Q=yT.requestBuilder(A,B),D=kB.map({},kB.isSerializableHeaderValue,{[RL1]:A[ML1]});Q.bp("/federation/credentials");let Z=kB.map({[TN4]:[,kB.expectNonNull(A[ON4],"roleName")],[tG2]:[,kB.expectNonNull(A[oG2],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_GetRoleCredentialsCommand"),KN4=O6(async(A,B)=>{let Q=yT.requestBuilder(A,B),D=kB.map({},kB.isSerializableHeaderValue,{[RL1]:A[ML1]});Q.bp("/assignment/roles");let Z=kB.map({[QF2]:[,A[BF2]],[AF2]:[()=>A.maxResults!==void 0,()=>A[eG2].toString()],[tG2]:[,kB.expectNonNull(A[oG2],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountRolesCommand"),HN4=O6(async(A,B)=>{let Q=yT.requestBuilder(A,B),D=kB.map({},kB.isSerializableHeaderValue,{[RL1]:A[ML1]});Q.bp("/assignment/accounts");let Z=kB.map({[QF2]:[,A[BF2]],[AF2]:[()=>A.maxResults!==void 0,()=>A[eG2].toString()]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountsCommand"),zN4=O6(async(A,B)=>{let Q=yT.requestBuilder(A,B),D=kB.map({},kB.isSerializableHeaderValue,{[RL1]:A[ML1]});Q.bp("/logout");let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_LogoutCommand"),EN4=O6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return LL1(A,B);let Q=kB.map({$metadata:Q_(A)}),D=kB.expectNonNull(kB.expectObject(await A81.parseJsonBody(A.body,B)),"body"),Z=kB.take(D,{roleCredentials:kB._json});return Object.assign(Q,Z),Q},"de_GetRoleCredentialsCommand"),UN4=O6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return LL1(A,B);let Q=kB.map({$metadata:Q_(A)}),D=kB.expectNonNull(kB.expectObject(await A81.parseJsonBody(A.body,B)),"body"),Z=kB.take(D,{nextToken:kB.expectString,roleList:kB._json});return Object.assign(Q,Z),Q},"de_ListAccountRolesCommand"),wN4=O6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return LL1(A,B);let Q=kB.map({$metadata:Q_(A)}),D=kB.expectNonNull(kB.expectObject(await A81.parseJsonBody(A.body,B)),"body"),Z=kB.take(D,{accountList:kB._json,nextToken:kB.expectString});return Object.assign(Q,Z),Q},"de_ListAccountsCommand"),$N4=O6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return LL1(A,B);let Q=kB.map({$metadata:Q_(A)});return await kB.collectBody(A.body,B),Q},"de_LogoutCommand"),LL1=O6(async(A,B)=>{let Q={...A,body:await A81.parseJsonErrorBody(A.body,B)},D=A81.loadRestJsonErrorCode(A,Q.body);switch(D){case"InvalidRequestException":case"com.amazonaws.sso#InvalidRequestException":throw await NN4(Q,B);case"ResourceNotFoundException":case"com.amazonaws.sso#ResourceNotFoundException":throw await LN4(Q,B);case"TooManyRequestsException":case"com.amazonaws.sso#TooManyRequestsException":throw await MN4(Q,B);case"UnauthorizedException":case"com.amazonaws.sso#UnauthorizedException":throw await RN4(Q,B);default:let Z=Q.body;return qN4({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),qN4=kB.withBaseException(cs),NN4=O6(async(A,B)=>{let Q=kB.map({}),D=A.body,Z=kB.take(D,{message:kB.expectString});Object.assign(Q,Z);let G=new mG2({$metadata:Q_(A),...Q});return kB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),LN4=O6(async(A,B)=>{let Q=kB.map({}),D=A.body,Z=kB.take(D,{message:kB.expectString});Object.assign(Q,Z);let G=new dG2({$metadata:Q_(A),...Q});return kB.decorateServiceException(G,A.body)},"de_ResourceNotFoundExceptionRes"),MN4=O6(async(A,B)=>{let Q=kB.map({}),D=A.body,Z=kB.take(D,{message:kB.expectString});Object.assign(Q,Z);let G=new cG2({$metadata:Q_(A),...Q});return kB.decorateServiceException(G,A.body)},"de_TooManyRequestsExceptionRes"),RN4=O6(async(A,B)=>{let Q=kB.map({}),D=A.body,Z=kB.take(D,{message:kB.expectString});Object.assign(Q,Z);let G=new lG2({$metadata:Q_(A),...Q});return kB.decorateServiceException(G,A.body)},"de_UnauthorizedExceptionRes"),Q_=O6((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),oG2="accountId",ML1="accessToken",tG2="account_id",eG2="maxResults",AF2="max_result",BF2="nextToken",QF2="next_token",ON4="roleName",TN4="role_name",RL1="x-amz-sso_bearer_token",DF2=class extends kB.Command.classBuilder().ep($L1).m(function(A,B,Q,D){return[NL1.getSerdePlugin(Q,this.serialize,this.deserialize),B81.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","GetRoleCredentials",{}).n("SSOClient","GetRoleCredentialsCommand").f(pG2,nG2).ser(CN4).de(EN4).build(){static{O6(this,"GetRoleCredentialsCommand")}},T50=class extends kB.Command.classBuilder().ep($L1).m(function(A,B,Q,D){return[NL1.getSerdePlugin(Q,this.serialize,this.deserialize),B81.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccountRoles",{}).n("SSOClient","ListAccountRolesCommand").f(aG2,void 0).ser(KN4).de(UN4).build(){static{O6(this,"ListAccountRolesCommand")}},P50=class extends kB.Command.classBuilder().ep($L1).m(function(A,B,Q,D){return[NL1.getSerdePlugin(Q,this.serialize,this.deserialize),B81.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccounts",{}).n("SSOClient","ListAccountsCommand").f(sG2,void 0).ser(HN4).de(wN4).build(){static{O6(this,"ListAccountsCommand")}},ZF2=class extends kB.Command.classBuilder().ep($L1).m(function(A,B,Q,D){return[NL1.getSerdePlugin(Q,this.serialize,this.deserialize),B81.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","Logout",{}).n("SSOClient","LogoutCommand").f(rG2,void 0).ser(zN4).de($N4).build(){static{O6(this,"LogoutCommand")}},PN4={GetRoleCredentialsCommand:DF2,ListAccountRolesCommand:T50,ListAccountsCommand:P50,LogoutCommand:ZF2},GF2=class extends qL1{static{O6(this,"SSO")}};kB.createAggregatedClient(PN4,GF2);var SN4=yT.createPaginator(qL1,T50,"nextToken","nextToken","maxResults"),jN4=yT.createPaginator(qL1,P50,"nextToken","nextToken","maxResults")});
var IG2=E((GG2)=>{Object.defineProperty(GG2,"__esModule",{value:!0});GG2.ruleSet=void 0;var BG2="required",cz="fn",lz="argv",ms="ref",pZ2=!0,iZ2="isSet",t61="booleanEquals",gs="error",us="endpoint",kT="tree",M50="PartitionResult",R50="getAttr",nZ2={[BG2]:!1,type:"String"},aZ2={[BG2]:!0,default:!1,type:"Boolean"},sZ2={[ms]:"Endpoint"},QG2={[cz]:t61,[lz]:[{[ms]:"UseFIPS"},!0]},DG2={[cz]:t61,[lz]:[{[ms]:"UseDualStack"},!0]},dz={},rZ2={[cz]:R50,[lz]:[{[ms]:M50},"supportsFIPS"]},ZG2={[ms]:M50},oZ2={[cz]:t61,[lz]:[!0,{[cz]:R50,[lz]:[ZG2,"supportsDualStack"]}]},tZ2=[QG2],eZ2=[DG2],AG2=[{[ms]:"Region"}],Fq4={version:"1.0",parameters:{Region:nZ2,UseDualStack:aZ2,UseFIPS:aZ2,Endpoint:nZ2},rules:[{conditions:[{[cz]:iZ2,[lz]:[sZ2]}],rules:[{conditions:tZ2,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:gs},{conditions:eZ2,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:gs},{endpoint:{url:sZ2,properties:dz,headers:dz},type:us}],type:kT},{conditions:[{[cz]:iZ2,[lz]:AG2}],rules:[{conditions:[{[cz]:"aws.partition",[lz]:AG2,assign:M50}],rules:[{conditions:[QG2,DG2],rules:[{conditions:[{[cz]:t61,[lz]:[pZ2,rZ2]},oZ2],rules:[{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:dz,headers:dz},type:us}],type:kT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:gs}],type:kT},{conditions:tZ2,rules:[{conditions:[{[cz]:t61,[lz]:[rZ2,pZ2]}],rules:[{conditions:[{[cz]:"stringEquals",[lz]:[{[cz]:R50,[lz]:[ZG2,"name"]},"aws-us-gov"]}],endpoint:{url:"https://portal.sso.{Region}.amazonaws.com",properties:dz,headers:dz},type:us},{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dnsSuffix}",properties:dz,headers:dz},type:us}],type:kT},{error:"FIPS is enabled but this partition does not support FIPS",type:gs}],type:kT},{conditions:eZ2,rules:[{conditions:[oZ2],rules:[{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:dz,headers:dz},type:us}],type:kT},{error:"DualStack is enabled but this partition does not support DualStack",type:gs}],type:kT},{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dnsSuffix}",properties:dz,headers:dz},type:us}],type:kT}],type:kT},{error:"Invalid Configuration: Missing Region",type:gs}]};GG2.ruleSet=Fq4});
var IYB=E((GYB)=>{Object.defineProperty(GYB,"__esModule",{value:!0});GYB.isEmptyData=void 0;function Hg6(A){if(typeof A==="string")return A.length===0;return A.byteLength===0}GYB.isEmptyData=Hg6});
var J81=E((V30)=>{Object.defineProperty(V30,"__esModule",{value:!0});V30.STSClient=V30.__Client=void 0;var pW2=b61(),pO4=f61(),iO4=h61(),iW2=_s(),nO4=V4(),X30=VB(),aO4=TG(),sO4=q6(),nW2=v4(),sW2=QZ();Object.defineProperty(V30,"__Client",{enumerable:!0,get:function(){return sW2.Client}});var aW2=F30(),rO4=X81(),oO4=xW2(),tO4=lW2();class rW2 extends sW2.Client{config;constructor(...[A]){let B=oO4.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=rO4.resolveClientEndpointParameters(B),D=iW2.resolveUserAgentConfig(Q),Z=nW2.resolveRetryConfig(D),G=nO4.resolveRegionConfig(Z),F=pW2.resolveHostHeaderConfig(G),I=sO4.resolveEndpointConfig(F),Y=aW2.resolveHttpAuthSchemeConfig(I),W=tO4.resolveRuntimeExtensions(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(iW2.getUserAgentPlugin(this.config)),this.middlewareStack.use(nW2.getRetryPlugin(this.config)),this.middlewareStack.use(aO4.getContentLengthPlugin(this.config)),this.middlewareStack.use(pW2.getHostHeaderPlugin(this.config)),this.middlewareStack.use(pO4.getLoggerPlugin(this.config)),this.middlewareStack.use(iO4.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(X30.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:aW2.defaultSTSHttpAuthSchemeParametersProvider,identityProviderConfigProvider:async(J)=>new X30.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials})})),this.middlewareStack.use(X30.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}}V30.STSClient=rW2});
var JG2=E((YG2)=>{Object.defineProperty(YG2,"__esModule",{value:!0});YG2.defaultEndpointResolver=void 0;var Iq4=Ts(),O50=$7(),Yq4=IG2(),Wq4=new O50.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),Jq4=(A,B={})=>{return Wq4.get(A,()=>O50.resolveEndpoint(Yq4.ruleSet,{endpointParams:A,logger:B.logger}))};YG2.defaultEndpointResolver=Jq4;O50.customEndpointFunctions.aws=Iq4.awsEndpointFunctions});
var JYB=E((YYB)=>{Object.defineProperty(YYB,"__esModule",{value:!0});YYB.numToUint8=void 0;function zg6(A){return new Uint8Array([(A&**********)>>24,(A&16711680)>>16,(A&65280)>>8,A&255])}YYB.numToUint8=zg6});
var K82=E((tC5,C82)=>{var{defineProperty:zN1,getOwnPropertyDescriptor:JV4,getOwnPropertyNames:XV4}=Object,VV4=Object.prototype.hasOwnProperty,sy=(A,B)=>zN1(A,"name",{value:B,configurable:!0}),CV4=(A,B)=>{for(var Q in B)zN1(A,Q,{get:B[Q],enumerable:!0})},KV4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of XV4(B))if(!VV4.call(A,Z)&&Z!==Q)zN1(A,Z,{get:()=>B[Z],enumerable:!(D=JV4(B,Z))||D.enumerable})}return A},HV4=(A)=>KV4(zN1({},"__esModule",{value:!0}),A),W82={};CV4(W82,{Field:()=>UV4,Fields:()=>wV4,HttpRequest:()=>$V4,HttpResponse:()=>qV4,IHttpRequest:()=>J82.HttpRequest,getHttpHandlerExtensionConfiguration:()=>zV4,isValidHostname:()=>V82,resolveHttpHandlerRuntimeConfig:()=>EV4});C82.exports=HV4(W82);var zV4=sy((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),EV4=sy((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),J82=X80(),UV4=class{static{sy(this,"Field")}constructor({name:A,kind:B=J82.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},wV4=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{sy(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},$V4=class A{static{sy(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=X82(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function X82(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}sy(X82,"cloneQuery");var qV4=class{static{sy(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function V82(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}sy(V82,"isValidHostname")});
var KH0=E((Os5,Qy1)=>{var zIB,EIB,UIB,wIB,$IB,qIB,NIB,LIB,MIB,RIB,OIB,TIB,PIB,Ay1,CH0,SIB,jIB,kIB,Ee,yIB,_IB,xIB,vIB,bIB,fIB,hIB,gIB,uIB,By1,mIB,dIB,cIB;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof Qy1==="object"&&typeof Os5==="object")A(Q(B,Q(Os5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};zIB=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},EIB=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},UIB=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},wIB=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},$IB=function(G,F){return function(I,Y){F(I,Y,G)}},qIB=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},NIB=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},LIB=function(G){return typeof G==="symbol"?G:"".concat(G)},MIB=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},RIB=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},OIB=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},TIB=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},PIB=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))By1(F,G,I)},By1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},Ay1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},CH0=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},SIB=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(CH0(arguments[F]));return G},jIB=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},kIB=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},Ee=function(G){return this instanceof Ee?(this.v=G,this):new Ee(G)},yIB=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof Ee?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},_IB=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:Ee(G[W](X)),done:!1}:J?J(X):X}:J}},xIB=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof Ay1==="function"?Ay1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},vIB=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};bIB=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")By1(F,G,I[Y])}return Q(F,G),F},fIB=function(G){return G&&G.__esModule?G:{default:G}},hIB=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},gIB=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},uIB=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},mIB=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};dIB=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},cIB=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",zIB),A("__assign",EIB),A("__rest",UIB),A("__decorate",wIB),A("__param",$IB),A("__esDecorate",qIB),A("__runInitializers",NIB),A("__propKey",LIB),A("__setFunctionName",MIB),A("__metadata",RIB),A("__awaiter",OIB),A("__generator",TIB),A("__exportStar",PIB),A("__createBinding",By1),A("__values",Ay1),A("__read",CH0),A("__spread",SIB),A("__spreadArrays",jIB),A("__spreadArray",kIB),A("__await",Ee),A("__asyncGenerator",yIB),A("__asyncDelegator",_IB),A("__asyncValues",xIB),A("__makeTemplateObject",vIB),A("__importStar",bIB),A("__importDefault",fIB),A("__classPrivateFieldGet",hIB),A("__classPrivateFieldSet",gIB),A("__classPrivateFieldIn",uIB),A("__addDisposableResource",mIB),A("__disposeResources",dIB),A("__rewriteRelativeImportExtension",cIB)})});
var KIB=E((Ms5,ek1)=>{var uFB,mFB,dFB,cFB,lFB,pFB,iFB,nFB,aFB,sFB,rFB,oFB,tFB,ok1,VH0,eFB,AIB,BIB,ze,QIB,DIB,ZIB,GIB,FIB,IIB,YIB,WIB,JIB,tk1,XIB,VIB,CIB;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof ek1==="object"&&typeof Ms5==="object")A(Q(B,Q(Ms5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};uFB=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},mFB=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},dFB=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},cFB=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},lFB=function(G,F){return function(I,Y){F(I,Y,G)}},pFB=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},iFB=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},nFB=function(G){return typeof G==="symbol"?G:"".concat(G)},aFB=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},sFB=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},rFB=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},oFB=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},tFB=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))tk1(F,G,I)},tk1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},ok1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},VH0=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},eFB=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(VH0(arguments[F]));return G},AIB=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},BIB=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},ze=function(G){return this instanceof ze?(this.v=G,this):new ze(G)},QIB=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof ze?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},DIB=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:ze(G[W](X)),done:!1}:J?J(X):X}:J}},ZIB=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof ok1==="function"?ok1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},GIB=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};FIB=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")tk1(F,G,I[Y])}return Q(F,G),F},IIB=function(G){return G&&G.__esModule?G:{default:G}},YIB=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},WIB=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},JIB=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},XIB=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};VIB=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},CIB=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",uFB),A("__assign",mFB),A("__rest",dFB),A("__decorate",cFB),A("__param",lFB),A("__esDecorate",pFB),A("__runInitializers",iFB),A("__propKey",nFB),A("__setFunctionName",aFB),A("__metadata",sFB),A("__awaiter",rFB),A("__generator",oFB),A("__exportStar",tFB),A("__createBinding",tk1),A("__values",ok1),A("__read",VH0),A("__spread",eFB),A("__spreadArrays",AIB),A("__spreadArray",BIB),A("__await",ze),A("__asyncGenerator",QIB),A("__asyncDelegator",DIB),A("__asyncValues",ZIB),A("__makeTemplateObject",GIB),A("__importStar",FIB),A("__importDefault",IIB),A("__classPrivateFieldGet",YIB),A("__classPrivateFieldSet",WIB),A("__classPrivateFieldIn",JIB),A("__addDisposableResource",XIB),A("__disposeResources",VIB),A("__rewriteRelativeImportExtension",CIB)})});
var LH0=E((Ar5,sYB)=>{var{defineProperty:Ky1,getOwnPropertyDescriptor:Pu6,getOwnPropertyNames:Su6}=Object,ju6=Object.prototype.hasOwnProperty,Hy1=(A,B)=>Ky1(A,"name",{value:B,configurable:!0}),ku6=(A,B)=>{for(var Q in B)Ky1(A,Q,{get:B[Q],enumerable:!0})},yu6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Su6(B))if(!ju6.call(A,Z)&&Z!==Q)Ky1(A,Z,{get:()=>B[Z],enumerable:!(D=Pu6(B,Z))||D.enumerable})}return A},_u6=(A)=>yu6(Ky1({},"__esModule",{value:!0}),A),mYB={};ku6(mYB,{AlgorithmId:()=>pYB,EndpointURLScheme:()=>lYB,FieldPosition:()=>iYB,HttpApiKeyAuthLocation:()=>cYB,HttpAuthLocation:()=>dYB,IniSectionType:()=>nYB,RequestHandlerProtocol:()=>aYB,SMITHY_CONTEXT_KEY:()=>hu6,getDefaultClientConfiguration:()=>bu6,resolveDefaultRuntimeConfig:()=>fu6});sYB.exports=_u6(mYB);var dYB=((A)=>{return A.HEADER="header",A.QUERY="query",A})(dYB||{}),cYB=((A)=>{return A.HEADER="header",A.QUERY="query",A})(cYB||{}),lYB=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(lYB||{}),pYB=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(pYB||{}),xu6=Hy1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),vu6=Hy1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),bu6=Hy1((A)=>{return xu6(A)},"getDefaultClientConfiguration"),fu6=Hy1((A)=>{return vu6(A)},"resolveDefaultRuntimeConfig"),iYB=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(iYB||{}),hu6="__smithy_context",nYB=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(nYB||{}),aYB=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(aYB||{})});
var NW2=E(($W2)=>{Object.defineProperty($W2,"__esModule",{value:!0});$W2.defaultEndpointResolver=void 0;var wO4=Ts(),J30=$7(),$O4=wW2(),qO4=new J30.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),NO4=(A,B={})=>{return qO4.get(A,()=>J30.resolveEndpoint($O4.ruleSet,{endpointParams:A,logger:B.logger}))};$W2.defaultEndpointResolver=NO4;J30.customEndpointFunctions.aws=wO4.awsEndpointFunctions});
var NWB=E((Wr5,wy1)=>{var{defineProperty:$WB,getOwnPropertyDescriptor:Qd6,getOwnPropertyNames:Dd6}=Object,Zd6=Object.prototype.hasOwnProperty,bH0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Dd6(B))if(!Zd6.call(A,Z)&&Z!==Q)$WB(A,Z,{get:()=>B[Z],enumerable:!(D=Qd6(B,Z))||D.enumerable})}return A},qWB=(A,B,Q)=>(bH0(A,B,"default"),Q&&bH0(Q,B,"default")),Gd6=(A)=>bH0($WB({},"__esModule",{value:!0}),A),fH0={};wy1.exports=Gd6(fH0);qWB(fH0,zWB(),wy1.exports);qWB(fH0,wWB(),wy1.exports)});
var O80=E((R80)=>{Object.defineProperty(R80,"__esModule",{value:!0});R80.fromHttp=void 0;var tC4=l82();Object.defineProperty(R80,"fromHttp",{enumerable:!0,get:function(){return tC4.fromHttp}})});
var QZ=E((yH5,xF2)=>{var{defineProperty:jL1,getOwnPropertyDescriptor:dN4,getOwnPropertyNames:cN4}=Object,lN4=Object.prototype.hasOwnProperty,j2=(A,B)=>jL1(A,"name",{value:B,configurable:!0}),pN4=(A,B)=>{for(var Q in B)jL1(A,Q,{get:B[Q],enumerable:!0})},iN4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of cN4(B))if(!lN4.call(A,Z)&&Z!==Q)jL1(A,Z,{get:()=>B[Z],enumerable:!(D=dN4(B,Z))||D.enumerable})}return A},nN4=(A)=>iN4(jL1({},"__esModule",{value:!0}),A),UF2={};pN4(UF2,{Client:()=>aN4,Command:()=>$F2,LazyJsonString:()=>Sg,NoOpLogger:()=>lL4,SENSITIVE_STRING:()=>rN4,ServiceException:()=>kL4,_json:()=>b50,collectBody:()=>j50.collectBody,convertMap:()=>pL4,createAggregatedClient:()=>oN4,dateToUtcString:()=>OF2,decorateServiceException:()=>TF2,emitWarningIfUnsupportedVersion:()=>vL4,expectBoolean:()=>eN4,expectByte:()=>v50,expectFloat32:()=>PL1,expectInt:()=>BL4,expectInt32:()=>_50,expectLong:()=>Z81,expectNonNull:()=>DL4,expectNumber:()=>D81,expectObject:()=>qF2,expectShort:()=>x50,expectString:()=>ZL4,expectUnion:()=>GL4,extendedEncodeURIComponent:()=>j50.extendedEncodeURIComponent,getArrayIfSingleItem:()=>dL4,getDefaultClientConfiguration:()=>uL4,getDefaultExtensionConfiguration:()=>SF2,getValueFromTextNode:()=>jF2,handleFloat:()=>YL4,isSerializableHeaderValue:()=>cL4,limitedParseDouble:()=>g50,limitedParseFloat:()=>WL4,limitedParseFloat32:()=>JL4,loadConfigsForDefaultMode:()=>xL4,logger:()=>G81,map:()=>m50,parseBoolean:()=>tN4,parseEpochTimestamp:()=>NL4,parseRfc3339DateTime:()=>HL4,parseRfc3339DateTimeWithOffset:()=>EL4,parseRfc7231DateTime:()=>qL4,quoteHeader:()=>yF2,resolveDefaultRuntimeConfig:()=>mL4,resolvedPath:()=>j50.resolvedPath,serializeDateTime:()=>oL4,serializeFloat:()=>rL4,splitEvery:()=>_F2,splitHeader:()=>tL4,strictParseByte:()=>RF2,strictParseDouble:()=>h50,strictParseFloat:()=>FL4,strictParseFloat32:()=>NF2,strictParseInt:()=>XL4,strictParseInt32:()=>VL4,strictParseLong:()=>MF2,strictParseShort:()=>ls,take:()=>iL4,throwDefaultError:()=>PF2,withBaseException:()=>yL4});xF2.exports=nN4(UF2);var wF2=Uw(),aN4=class{constructor(A){this.config=A,this.middlewareStack=wF2.constructStack()}static{j2(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},j50=$6(),y50=S50(),$F2=class{constructor(){this.middlewareStack=wF2.constructStack()}static{j2(this,"Command")}static classBuilder(){return new sN4}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[y50.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},sN4=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{j2(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}build(){let A=this,B;return B=class extends $F2{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this)}static{j2(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},rN4="***SensitiveInformation***",oN4=j2((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=j2(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),tN4=j2((A)=>{switch(A){case"true":return!0;case"false":return!1;default:throw new Error(`Unable to parse boolean value "${A}"`)}},"parseBoolean"),eN4=j2((A)=>{if(A===null||A===void 0)return;if(typeof A==="number"){if(A===0||A===1)G81.warn(SL1(`Expected boolean, got ${typeof A}: ${A}`));if(A===0)return!1;if(A===1)return!0}if(typeof A==="string"){let B=A.toLowerCase();if(B==="false"||B==="true")G81.warn(SL1(`Expected boolean, got ${typeof A}: ${A}`));if(B==="false")return!1;if(B==="true")return!0}if(typeof A==="boolean")return A;throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)},"expectBoolean"),D81=j2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string"){let B=parseFloat(A);if(!Number.isNaN(B)){if(String(B)!==String(A))G81.warn(SL1(`Expected number but observed string: ${A}`));return B}}if(typeof A==="number")return A;throw new TypeError(`Expected number, got ${typeof A}: ${A}`)},"expectNumber"),AL4=Math.ceil(340282346638528860000000000000000000000),PL1=j2((A)=>{let B=D81(A);if(B!==void 0&&!Number.isNaN(B)&&B!==1/0&&B!==-1/0){if(Math.abs(B)>AL4)throw new TypeError(`Expected 32-bit float, got ${A}`)}return B},"expectFloat32"),Z81=j2((A)=>{if(A===null||A===void 0)return;if(Number.isInteger(A)&&!Number.isNaN(A))return A;throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)},"expectLong"),BL4=Z81,_50=j2((A)=>f50(A,32),"expectInt32"),x50=j2((A)=>f50(A,16),"expectShort"),v50=j2((A)=>f50(A,8),"expectByte"),f50=j2((A,B)=>{let Q=Z81(A);if(Q!==void 0&&QL4(Q,B)!==Q)throw new TypeError(`Expected ${B}-bit integer, got ${A}`);return Q},"expectSizedInt"),QL4=j2((A,B)=>{switch(B){case 32:return Int32Array.of(A)[0];case 16:return Int16Array.of(A)[0];case 8:return Int8Array.of(A)[0]}},"castInt"),DL4=j2((A,B)=>{if(A===null||A===void 0){if(B)throw new TypeError(`Expected a non-null value for ${B}`);throw new TypeError("Expected a non-null value")}return A},"expectNonNull"),qF2=j2((A)=>{if(A===null||A===void 0)return;if(typeof A==="object"&&!Array.isArray(A))return A;let B=Array.isArray(A)?"array":typeof A;throw new TypeError(`Expected object, got ${B}: ${A}`)},"expectObject"),ZL4=j2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string")return A;if(["boolean","number","bigint"].includes(typeof A))return G81.warn(SL1(`Expected string, got ${typeof A}: ${A}`)),String(A);throw new TypeError(`Expected string, got ${typeof A}: ${A}`)},"expectString"),GL4=j2((A)=>{if(A===null||A===void 0)return;let B=qF2(A),Q=Object.entries(B).filter(([,D])=>D!=null).map(([D])=>D);if(Q.length===0)throw new TypeError("Unions must have exactly one non-null member. None were found.");if(Q.length>1)throw new TypeError(`Unions must have exactly one non-null member. Keys ${Q} were not null.`);return B},"expectUnion"),h50=j2((A)=>{if(typeof A=="string")return D81(is(A));return D81(A)},"strictParseDouble"),FL4=h50,NF2=j2((A)=>{if(typeof A=="string")return PL1(is(A));return PL1(A)},"strictParseFloat32"),IL4=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,is=j2((A)=>{let B=A.match(IL4);if(B===null||B[0].length!==A.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(A)},"parseNumber"),g50=j2((A)=>{if(typeof A=="string")return LF2(A);return D81(A)},"limitedParseDouble"),YL4=g50,WL4=g50,JL4=j2((A)=>{if(typeof A=="string")return LF2(A);return PL1(A)},"limitedParseFloat32"),LF2=j2((A)=>{switch(A){case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error(`Unable to parse float value: ${A}`)}},"parseFloatString"),MF2=j2((A)=>{if(typeof A==="string")return Z81(is(A));return Z81(A)},"strictParseLong"),XL4=MF2,VL4=j2((A)=>{if(typeof A==="string")return _50(is(A));return _50(A)},"strictParseInt32"),ls=j2((A)=>{if(typeof A==="string")return x50(is(A));return x50(A)},"strictParseShort"),RF2=j2((A)=>{if(typeof A==="string")return v50(is(A));return v50(A)},"strictParseByte"),SL1=j2((A)=>{return String(new TypeError(A).stack||A).split(`
`).slice(0,5).filter((B)=>!B.includes("stackTraceWarning")).join(`
`)},"stackTraceWarning"),G81={warn:console.warn},CL4=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],u50=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function OF2(A){let B=A.getUTCFullYear(),Q=A.getUTCMonth(),D=A.getUTCDay(),Z=A.getUTCDate(),G=A.getUTCHours(),F=A.getUTCMinutes(),I=A.getUTCSeconds(),Y=Z<10?`0${Z}`:`${Z}`,W=G<10?`0${G}`:`${G}`,J=F<10?`0${F}`:`${F}`,X=I<10?`0${I}`:`${I}`;return`${CL4[D]}, ${Y} ${u50[Q]} ${B} ${W}:${J}:${X} GMT`}j2(OF2,"dateToUtcString");var KL4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),HL4=j2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=KL4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W]=B,J=ls(ps(D)),X=qL(Z,"month",1,12),V=qL(G,"day",1,31);return Q81(J,X,V,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})},"parseRfc3339DateTime"),zL4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),EL4=j2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=zL4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W,J]=B,X=ls(ps(D)),V=qL(Z,"month",1,12),C=qL(G,"day",1,31),K=Q81(X,V,C,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W});if(J.toUpperCase()!="Z")K.setTime(K.getTime()-jL4(J));return K},"parseRfc3339DateTimeWithOffset"),UL4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),wL4=new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),$L4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/),qL4=j2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-7231 date-times must be expressed as strings");let B=UL4.exec(A);if(B){let[Q,D,Z,G,F,I,Y,W]=B;return Q81(ls(ps(G)),k50(Z),qL(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})}if(B=wL4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return RL4(Q81(LL4(G),k50(Z),qL(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W}))}if(B=$L4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return Q81(ls(ps(W)),k50(D),qL(Z.trimLeft(),"day",1,31),{hours:G,minutes:F,seconds:I,fractionalMilliseconds:Y})}throw new TypeError("Invalid RFC-7231 date-time value")},"parseRfc7231DateTime"),NL4=j2((A)=>{if(A===null||A===void 0)return;let B;if(typeof A==="number")B=A;else if(typeof A==="string")B=h50(A);else if(typeof A==="object"&&A.tag===1)B=A.value;else throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(B)||B===1/0||B===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(B*1000))},"parseEpochTimestamp"),Q81=j2((A,B,Q,D)=>{let Z=B-1;return TL4(A,Z,Q),new Date(Date.UTC(A,Z,Q,qL(D.hours,"hour",0,23),qL(D.minutes,"minute",0,59),qL(D.seconds,"seconds",0,60),SL4(D.fractionalMilliseconds)))},"buildDate"),LL4=j2((A)=>{let B=new Date().getUTCFullYear(),Q=Math.floor(B/100)*100+ls(ps(A));if(Q<B)return Q+100;return Q},"parseTwoDigitYear"),ML4=1576800000000,RL4=j2((A)=>{if(A.getTime()-new Date().getTime()>ML4)return new Date(Date.UTC(A.getUTCFullYear()-100,A.getUTCMonth(),A.getUTCDate(),A.getUTCHours(),A.getUTCMinutes(),A.getUTCSeconds(),A.getUTCMilliseconds()));return A},"adjustRfc850Year"),k50=j2((A)=>{let B=u50.indexOf(A);if(B<0)throw new TypeError(`Invalid month: ${A}`);return B+1},"parseMonthByShortName"),OL4=[31,28,31,30,31,30,31,31,30,31,30,31],TL4=j2((A,B,Q)=>{let D=OL4[B];if(B===1&&PL4(A))D=29;if(Q>D)throw new TypeError(`Invalid day for ${u50[B]} in ${A}: ${Q}`)},"validateDayOfMonth"),PL4=j2((A)=>{return A%4===0&&(A%100!==0||A%400===0)},"isLeapYear"),qL=j2((A,B,Q,D)=>{let Z=RF2(ps(A));if(Z<Q||Z>D)throw new TypeError(`${B} must be between ${Q} and ${D}, inclusive`);return Z},"parseDateValue"),SL4=j2((A)=>{if(A===null||A===void 0)return 0;return NF2("0."+A)*1000},"parseMilliseconds"),jL4=j2((A)=>{let B=A[0],Q=1;if(B=="+")Q=1;else if(B=="-")Q=-1;else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);let D=Number(A.substring(1,3)),Z=Number(A.substring(4,6));return Q*(D*60+Z)*60*1000},"parseOffsetToMilliseconds"),ps=j2((A)=>{let B=0;while(B<A.length-1&&A.charAt(B)==="0")B++;if(B===0)return A;return A.slice(B)},"stripLeadingZeroes"),kL4=class A extends Error{static{j2(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},TF2=j2((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),PF2=j2(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=_L4(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw TF2(F,B)},"throwDefaultError"),yL4=j2((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{PF2({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),_L4=j2((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),xL4=j2((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),EF2=!1,vL4=j2((A)=>{if(A&&!EF2&&parseInt(A.substring(1,A.indexOf(".")))<16)EF2=!0},"emitWarningIfUnsupportedVersion"),bL4=j2((A)=>{let B=[];for(let Q in y50.AlgorithmId){let D=y50.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),fL4=j2((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),hL4=j2((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),gL4=j2((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),SF2=j2((A)=>{return Object.assign(bL4(A),hL4(A))},"getDefaultExtensionConfiguration"),uL4=SF2,mL4=j2((A)=>{return Object.assign(fL4(A),gL4(A))},"resolveDefaultRuntimeConfig"),dL4=j2((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),jF2=j2((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=jF2(A[Q]);return A},"getValueFromTextNode"),cL4=j2((A)=>{return A!=null},"isSerializableHeaderValue"),Sg=j2(function A(B){return Object.assign(new String(B),{deserializeJSON(){return JSON.parse(String(B))},toString(){return String(B)},toJSON(){return String(B)}})},"LazyJsonString");Sg.from=(A)=>{if(A&&typeof A==="object"&&(A instanceof Sg||("deserializeJSON"in A)))return A;else if(typeof A==="string"||Object.getPrototypeOf(A)===String.prototype)return Sg(String(A));return Sg(JSON.stringify(A))};Sg.fromObject=Sg.from;var lL4=class{static{j2(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function m50(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,nL4(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}kF2(D,null,G,F)}return D}j2(m50,"map");var pL4=j2((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),iL4=j2((A,B)=>{let Q={};for(let D in B)kF2(Q,A,B,D);return Q},"take"),nL4=j2((A,B,Q)=>{return m50(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),kF2=j2((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=aL4,Y=sL4,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),aL4=j2((A)=>A!=null,"nonNullish"),sL4=j2((A)=>A,"pass");function yF2(A){if(A.includes(",")||A.includes('"'))A=`"${A.replace(/"/g,"\\\"")}"`;return A}j2(yF2,"quoteHeader");var rL4=j2((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),oL4=j2((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),b50=j2((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(b50);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=b50(A[Q])}return B}return A},"_json");function _F2(A,B,Q){if(Q<=0||!Number.isInteger(Q))throw new Error("Invalid number of delimiters ("+Q+") for splitEvery.");let D=A.split(B);if(Q===1)return D;let Z=[],G="";for(let F=0;F<D.length;F++){if(G==="")G=D[F];else G+=B+D[F];if((F+1)%Q===0)Z.push(G),G=""}if(G!=="")Z.push(G);return Z}j2(_F2,"splitEvery");var tL4=j2((A)=>{let B=A.length,Q=[],D=!1,Z=void 0,G=0;for(let F=0;F<B;++F){let I=A[F];switch(I){case'"':if(Z!=="\\")D=!D;break;case",":if(!D)Q.push(A.slice(G,F)),G=F+1;break;default:}Z=I}return Q.push(A.slice(G)),Q.map((F)=>{F=F.trim();let I=F.length;if(I<2)return F;if(F[0]==='"'&&F[I-1]==='"')F=F.slice(1,I-1);return F.replace(/\\"/g,'"')})},"splitHeader")});
var R52=E((wK5,M52)=>{var{defineProperty:ON1,getOwnPropertyDescriptor:hK4,getOwnPropertyNames:gK4}=Object,uK4=Object.prototype.hasOwnProperty,TN1=(A,B)=>ON1(A,"name",{value:B,configurable:!0}),mK4=(A,B)=>{for(var Q in B)ON1(A,Q,{get:B[Q],enumerable:!0})},dK4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of gK4(B))if(!uK4.call(A,Z)&&Z!==Q)ON1(A,Z,{get:()=>B[Z],enumerable:!(D=hK4(B,Z))||D.enumerable})}return A},cK4=(A)=>dK4(ON1({},"__esModule",{value:!0}),A),z52={};mK4(z52,{AlgorithmId:()=>$52,EndpointURLScheme:()=>w52,FieldPosition:()=>q52,HttpApiKeyAuthLocation:()=>U52,HttpAuthLocation:()=>E52,IniSectionType:()=>N52,RequestHandlerProtocol:()=>L52,SMITHY_CONTEXT_KEY:()=>aK4,getDefaultClientConfiguration:()=>iK4,resolveDefaultRuntimeConfig:()=>nK4});M52.exports=cK4(z52);var E52=((A)=>{return A.HEADER="header",A.QUERY="query",A})(E52||{}),U52=((A)=>{return A.HEADER="header",A.QUERY="query",A})(U52||{}),w52=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(w52||{}),$52=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})($52||{}),lK4=TN1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),pK4=TN1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),iK4=TN1((A)=>{return lK4(A)},"getDefaultClientConfiguration"),nK4=TN1((A)=>{return pK4(A)},"resolveDefaultRuntimeConfig"),q52=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(q52||{}),aK4="__smithy_context",N52=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(N52||{}),L52=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(L52||{})});
var RFB=E((Es5,MFB)=>{var{defineProperty:ak1,getOwnPropertyDescriptor:Fh6,getOwnPropertyNames:Ih6}=Object,Yh6=Object.prototype.hasOwnProperty,yx=(A,B)=>ak1(A,"name",{value:B,configurable:!0}),Wh6=(A,B)=>{for(var Q in B)ak1(A,Q,{get:B[Q],enumerable:!0})},Jh6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Ih6(B))if(!Yh6.call(A,Z)&&Z!==Q)ak1(A,Z,{get:()=>B[Z],enumerable:!(D=Fh6(B,Z))||D.enumerable})}return A},Xh6=(A)=>Jh6(ak1({},"__esModule",{value:!0}),A),$FB={};Wh6($FB,{Field:()=>Kh6,Fields:()=>Hh6,HttpRequest:()=>zh6,HttpResponse:()=>Eh6,IHttpRequest:()=>qFB.HttpRequest,getHttpHandlerExtensionConfiguration:()=>Vh6,isValidHostname:()=>LFB,resolveHttpHandlerRuntimeConfig:()=>Ch6});MFB.exports=Xh6($FB);var Vh6=yx((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),Ch6=yx((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),qFB=wFB(),Kh6=class{static{yx(this,"Field")}constructor({name:A,kind:B=qFB.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},Hh6=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{yx(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},zh6=class A{static{yx(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=NFB(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function NFB(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}yx(NFB,"cloneQuery");var Eh6=class{static{yx(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function LFB(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}yx(LFB,"isValidHostname")});
var S50=E((kH5,zF2)=>{var{defineProperty:OL1,getOwnPropertyDescriptor:kN4,getOwnPropertyNames:yN4}=Object,_N4=Object.prototype.hasOwnProperty,TL1=(A,B)=>OL1(A,"name",{value:B,configurable:!0}),xN4=(A,B)=>{for(var Q in B)OL1(A,Q,{get:B[Q],enumerable:!0})},vN4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of yN4(B))if(!_N4.call(A,Z)&&Z!==Q)OL1(A,Z,{get:()=>B[Z],enumerable:!(D=kN4(B,Z))||D.enumerable})}return A},bN4=(A)=>vN4(OL1({},"__esModule",{value:!0}),A),YF2={};xN4(YF2,{AlgorithmId:()=>VF2,EndpointURLScheme:()=>XF2,FieldPosition:()=>CF2,HttpApiKeyAuthLocation:()=>JF2,HttpAuthLocation:()=>WF2,IniSectionType:()=>KF2,RequestHandlerProtocol:()=>HF2,SMITHY_CONTEXT_KEY:()=>mN4,getDefaultClientConfiguration:()=>gN4,resolveDefaultRuntimeConfig:()=>uN4});zF2.exports=bN4(YF2);var WF2=((A)=>{return A.HEADER="header",A.QUERY="query",A})(WF2||{}),JF2=((A)=>{return A.HEADER="header",A.QUERY="query",A})(JF2||{}),XF2=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(XF2||{}),VF2=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(VF2||{}),fN4=TL1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),hN4=TL1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),gN4=TL1((A)=>{return fN4(A)},"getDefaultClientConfiguration"),uN4=TL1((A)=>{return hN4(A)},"resolveDefaultRuntimeConfig"),CF2=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(CF2||{}),mN4="__smithy_context",KF2=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(KF2||{}),HF2=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(HF2||{})});
var SYB=E((ns5,PYB)=>{var{defineProperty:Jy1,getOwnPropertyDescriptor:Qu6,getOwnPropertyNames:Du6}=Object,Zu6=Object.prototype.hasOwnProperty,Xy1=(A,B)=>Jy1(A,"name",{value:B,configurable:!0}),Gu6=(A,B)=>{for(var Q in B)Jy1(A,Q,{get:B[Q],enumerable:!0})},Fu6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Du6(B))if(!Zu6.call(A,Z)&&Z!==Q)Jy1(A,Z,{get:()=>B[Z],enumerable:!(D=Qu6(B,Z))||D.enumerable})}return A},Iu6=(A)=>Fu6(Jy1({},"__esModule",{value:!0}),A),OYB={};Gu6(OYB,{eventStreamPayloadHandlerProvider:()=>Xu6});PYB.exports=Iu6(OYB);var Yu6=qH0(),Wy1=J1("stream"),Wu6=class extends Wy1.Transform{static{Xy1(this,"EventSigningStream")}priorSignature;messageSigner;eventStreamCodec;systemClockOffsetProvider;constructor(A){super({autoDestroy:!0,readableObjectMode:!0,writableObjectMode:!0,...A});this.priorSignature=A.priorSignature,this.eventStreamCodec=A.eventStreamCodec,this.messageSigner=A.messageSigner,this.systemClockOffsetProvider=A.systemClockOffsetProvider}async _transform(A,B,Q){try{let D=new Date(Date.now()+await this.systemClockOffsetProvider()),Z={":date":{type:"timestamp",value:D}},G=await this.messageSigner.sign({message:{body:A,headers:Z},priorSignature:this.priorSignature},{signingDate:D});this.priorSignature=G.signature;let F=this.eventStreamCodec.encode({headers:{...Z,":chunk-signature":{type:"binary",value:TYB(G.signature)}},body:A});return this.push(F),Q()}catch(D){Q(D)}}};function TYB(A){let B=Buffer.from(A,"hex");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength/Uint8Array.BYTES_PER_ELEMENT)}Xy1(TYB,"getSignatureBinary");var Ju6=class{static{Xy1(this,"EventStreamPayloadHandler")}messageSigner;eventStreamCodec;systemClockOffsetProvider;constructor(A){this.messageSigner=A.messageSigner,this.eventStreamCodec=new Yu6.EventStreamCodec(A.utf8Encoder,A.utf8Decoder),this.systemClockOffsetProvider=async()=>A.systemClockOffset??0}async handle(A,B,Q={}){let D=B.request,{body:Z,query:G}=D;if(!(Z instanceof Wy1.Readable))throw new Error("Eventstream payload must be a Readable stream.");let F=Z;D.body=new Wy1.PassThrough({objectMode:!0});let Y=D.headers?.authorization?.match(/Signature=([\w]+)$/)?.[1]??G?.["X-Amz-Signature"]??"",W=new Wu6({priorSignature:Y,eventStreamCodec:this.eventStreamCodec,messageSigner:await this.messageSigner(),systemClockOffsetProvider:this.systemClockOffsetProvider});Wy1.pipeline(F,W,D.body,(X)=>{if(X)throw X});let J;try{J=await A(B)}catch(X){throw D.body.end(),X}return J}},Xu6=Xy1((A)=>new Ju6(A),"eventStreamPayloadHandlerProvider")});
var TW2=E((RW2)=>{Object.defineProperty(RW2,"__esModule",{value:!0});RW2.getRuntimeConfig=void 0;var LO4=WI(),MO4=VB(),RO4=QZ(),OO4=BZ(),LW2=s50(),MW2=cB(),TO4=F30(),PO4=NW2(),SO4=(A)=>{return{apiVersion:"2011-06-15",base64Decoder:A?.base64Decoder??LW2.fromBase64,base64Encoder:A?.base64Encoder??LW2.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??PO4.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??TO4.defaultSTSHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new LO4.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new MO4.NoAuthSigner}],logger:A?.logger??new RO4.NoOpLogger,serviceId:A?.serviceId??"STS",urlParser:A?.urlParser??OO4.parseUrl,utf8Decoder:A?.utf8Decoder??MW2.fromUtf8,utf8Encoder:A?.utf8Encoder??MW2.toUtf8}};RW2.getRuntimeConfig=SO4});
var Ts=E((RK5,p52)=>{var{defineProperty:kN1,getOwnPropertyDescriptor:EH4,getOwnPropertyNames:UH4}=Object,wH4=Object.prototype.hasOwnProperty,Os=(A,B)=>kN1(A,"name",{value:B,configurable:!0}),$H4=(A,B)=>{for(var Q in B)kN1(A,Q,{get:B[Q],enumerable:!0})},qH4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of UH4(B))if(!wH4.call(A,Z)&&Z!==Q)kN1(A,Z,{get:()=>B[Z],enumerable:!(D=EH4(B,Z))||D.enumerable})}return A},NH4=(A)=>qH4(kN1({},"__esModule",{value:!0}),A),f52={};$H4(f52,{ConditionObject:()=>B7.ConditionObject,DeprecatedObject:()=>B7.DeprecatedObject,EndpointError:()=>B7.EndpointError,EndpointObject:()=>B7.EndpointObject,EndpointObjectHeaders:()=>B7.EndpointObjectHeaders,EndpointObjectProperties:()=>B7.EndpointObjectProperties,EndpointParams:()=>B7.EndpointParams,EndpointResolverOptions:()=>B7.EndpointResolverOptions,EndpointRuleObject:()=>B7.EndpointRuleObject,ErrorRuleObject:()=>B7.ErrorRuleObject,EvaluateOptions:()=>B7.EvaluateOptions,Expression:()=>B7.Expression,FunctionArgv:()=>B7.FunctionArgv,FunctionObject:()=>B7.FunctionObject,FunctionReturn:()=>B7.FunctionReturn,ParameterObject:()=>B7.ParameterObject,ReferenceObject:()=>B7.ReferenceObject,ReferenceRecord:()=>B7.ReferenceRecord,RuleSetObject:()=>B7.RuleSetObject,RuleSetRules:()=>B7.RuleSetRules,TreeRuleObject:()=>B7.TreeRuleObject,awsEndpointFunctions:()=>l52,getUserAgentPrefix:()=>OH4,isIpAddress:()=>B7.isIpAddress,partition:()=>d52,resolveEndpoint:()=>B7.resolveEndpoint,setPartitionInfo:()=>c52,useDefaultPartitionInfo:()=>RH4});p52.exports=NH4(f52);var B7=$7(),h52=Os((A,B=!1)=>{if(B){for(let Q of A.split("."))if(!h52(Q))return!1;return!0}if(!B7.isValidHostLabel(A))return!1;if(A.length<3||A.length>63)return!1;if(A!==A.toLowerCase())return!1;if(B7.isIpAddress(A))return!1;return!0},"isVirtualHostableS3Bucket"),b52=":",LH4="/",MH4=Os((A)=>{let B=A.split(b52);if(B.length<6)return null;let[Q,D,Z,G,F,...I]=B;if(Q!=="arn"||D===""||Z===""||I.join(b52)==="")return null;let Y=I.map((W)=>W.split(LH4)).flat();return{partition:D,service:Z,region:G,accountId:F,resourceId:Y}},"parseArn"),g52={partitions:[{id:"aws",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-east-1",name:"aws",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^(us|eu|ap|sa|ca|me|af|il|mx)\\-\\w+\\-\\d+$",regions:{"af-south-1":{description:"Africa (Cape Town)"},"ap-east-1":{description:"Asia Pacific (Hong Kong)"},"ap-northeast-1":{description:"Asia Pacific (Tokyo)"},"ap-northeast-2":{description:"Asia Pacific (Seoul)"},"ap-northeast-3":{description:"Asia Pacific (Osaka)"},"ap-south-1":{description:"Asia Pacific (Mumbai)"},"ap-south-2":{description:"Asia Pacific (Hyderabad)"},"ap-southeast-1":{description:"Asia Pacific (Singapore)"},"ap-southeast-2":{description:"Asia Pacific (Sydney)"},"ap-southeast-3":{description:"Asia Pacific (Jakarta)"},"ap-southeast-4":{description:"Asia Pacific (Melbourne)"},"ap-southeast-5":{description:"Asia Pacific (Malaysia)"},"ap-southeast-7":{description:"Asia Pacific (Thailand)"},"aws-global":{description:"AWS Standard global region"},"ca-central-1":{description:"Canada (Central)"},"ca-west-1":{description:"Canada West (Calgary)"},"eu-central-1":{description:"Europe (Frankfurt)"},"eu-central-2":{description:"Europe (Zurich)"},"eu-north-1":{description:"Europe (Stockholm)"},"eu-south-1":{description:"Europe (Milan)"},"eu-south-2":{description:"Europe (Spain)"},"eu-west-1":{description:"Europe (Ireland)"},"eu-west-2":{description:"Europe (London)"},"eu-west-3":{description:"Europe (Paris)"},"il-central-1":{description:"Israel (Tel Aviv)"},"me-central-1":{description:"Middle East (UAE)"},"me-south-1":{description:"Middle East (Bahrain)"},"mx-central-1":{description:"Mexico (Central)"},"sa-east-1":{description:"South America (Sao Paulo)"},"us-east-1":{description:"US East (N. Virginia)"},"us-east-2":{description:"US East (Ohio)"},"us-west-1":{description:"US West (N. California)"},"us-west-2":{description:"US West (Oregon)"}}},{id:"aws-cn",outputs:{dnsSuffix:"amazonaws.com.cn",dualStackDnsSuffix:"api.amazonwebservices.com.cn",implicitGlobalRegion:"cn-northwest-1",name:"aws-cn",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^cn\\-\\w+\\-\\d+$",regions:{"aws-cn-global":{description:"AWS China global region"},"cn-north-1":{description:"China (Beijing)"},"cn-northwest-1":{description:"China (Ningxia)"}}},{id:"aws-us-gov",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-gov-west-1",name:"aws-us-gov",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^us\\-gov\\-\\w+\\-\\d+$",regions:{"aws-us-gov-global":{description:"AWS GovCloud (US) global region"},"us-gov-east-1":{description:"AWS GovCloud (US-East)"},"us-gov-west-1":{description:"AWS GovCloud (US-West)"}}},{id:"aws-iso",outputs:{dnsSuffix:"c2s.ic.gov",dualStackDnsSuffix:"c2s.ic.gov",implicitGlobalRegion:"us-iso-east-1",name:"aws-iso",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-iso\\-\\w+\\-\\d+$",regions:{"aws-iso-global":{description:"AWS ISO (US) global region"},"us-iso-east-1":{description:"US ISO East"},"us-iso-west-1":{description:"US ISO WEST"}}},{id:"aws-iso-b",outputs:{dnsSuffix:"sc2s.sgov.gov",dualStackDnsSuffix:"sc2s.sgov.gov",implicitGlobalRegion:"us-isob-east-1",name:"aws-iso-b",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isob\\-\\w+\\-\\d+$",regions:{"aws-iso-b-global":{description:"AWS ISOB (US) global region"},"us-isob-east-1":{description:"US ISOB East (Ohio)"}}},{id:"aws-iso-e",outputs:{dnsSuffix:"cloud.adc-e.uk",dualStackDnsSuffix:"cloud.adc-e.uk",implicitGlobalRegion:"eu-isoe-west-1",name:"aws-iso-e",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eu\\-isoe\\-\\w+\\-\\d+$",regions:{"aws-iso-e-global":{description:"AWS ISOE (Europe) global region"},"eu-isoe-west-1":{description:"EU ISOE West"}}},{id:"aws-iso-f",outputs:{dnsSuffix:"csp.hci.ic.gov",dualStackDnsSuffix:"csp.hci.ic.gov",implicitGlobalRegion:"us-isof-south-1",name:"aws-iso-f",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isof\\-\\w+\\-\\d+$",regions:{"aws-iso-f-global":{description:"AWS ISOF global region"},"us-isof-east-1":{description:"US ISOF EAST"},"us-isof-south-1":{description:"US ISOF SOUTH"}}},{id:"aws-eusc",outputs:{dnsSuffix:"amazonaws.eu",dualStackDnsSuffix:"amazonaws.eu",implicitGlobalRegion:"eusc-de-east-1",name:"aws-eusc",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eusc\\-(de)\\-\\w+\\-\\d+$",regions:{"eusc-de-east-1":{description:"EU (Germany)"}}}],version:"1.1"},u52=g52,m52="",d52=Os((A)=>{let{partitions:B}=u52;for(let D of B){let{regions:Z,outputs:G}=D;for(let[F,I]of Object.entries(Z))if(F===A)return{...G,...I}}for(let D of B){let{regionRegex:Z,outputs:G}=D;if(new RegExp(Z).test(A))return{...G}}let Q=B.find((D)=>D.id==="aws");if(!Q)throw new Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");return{...Q.outputs}},"partition"),c52=Os((A,B="")=>{u52=A,m52=B},"setPartitionInfo"),RH4=Os(()=>{c52(g52,"")},"useDefaultPartitionInfo"),OH4=Os(()=>m52,"getUserAgentPrefix"),l52={isVirtualHostableS3Bucket:h52,parseArn:MH4,partition:d52};B7.customEndpointFunctions.aws=l52});
var UYB=E((zYB)=>{Object.defineProperty(zYB,"__esModule",{value:!0});zYB.AwsCrc32=void 0;var KYB=KH0(),UH0=EH0(),HYB=Fy1(),Lg6=function(){function A(){this.crc32=new HYB.Crc32}return A.prototype.update=function(B){if(UH0.isEmptyData(B))return;this.crc32.update(UH0.convertToBuffer(B))},A.prototype.digest=function(){return KYB.__awaiter(this,void 0,void 0,function(){return KYB.__generator(this,function(B){return[2,UH0.numToUint8(this.crc32.digest())]})})},A.prototype.reset=function(){this.crc32=new HYB.Crc32},A}();zYB.AwsCrc32=Lg6});
var W50=E((GH5,EZ2)=>{var{defineProperty:JL1,getOwnPropertyDescriptor:Pw4,getOwnPropertyNames:Sw4}=Object,jw4=Object.prototype.hasOwnProperty,XL1=(A,B)=>JL1(A,"name",{value:B,configurable:!0}),kw4=(A,B)=>{for(var Q in B)JL1(A,Q,{get:B[Q],enumerable:!0})},yw4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Sw4(B))if(!jw4.call(A,Z)&&Z!==Q)JL1(A,Z,{get:()=>B[Z],enumerable:!(D=Pw4(B,Z))||D.enumerable})}return A},_w4=(A)=>yw4(JL1({},"__esModule",{value:!0}),A),WZ2={};kw4(WZ2,{AlgorithmId:()=>CZ2,EndpointURLScheme:()=>VZ2,FieldPosition:()=>KZ2,HttpApiKeyAuthLocation:()=>XZ2,HttpAuthLocation:()=>JZ2,IniSectionType:()=>HZ2,RequestHandlerProtocol:()=>zZ2,SMITHY_CONTEXT_KEY:()=>hw4,getDefaultClientConfiguration:()=>bw4,resolveDefaultRuntimeConfig:()=>fw4});EZ2.exports=_w4(WZ2);var JZ2=((A)=>{return A.HEADER="header",A.QUERY="query",A})(JZ2||{}),XZ2=((A)=>{return A.HEADER="header",A.QUERY="query",A})(XZ2||{}),VZ2=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(VZ2||{}),CZ2=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(CZ2||{}),xw4=XL1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),vw4=XL1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),bw4=XL1((A)=>{return xw4(A)},"getDefaultClientConfiguration"),fw4=XL1((A)=>{return vw4(A)},"resolveDefaultRuntimeConfig"),KZ2=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(KZ2||{}),hw4="__smithy_context",HZ2=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(HZ2||{}),zZ2=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(zZ2||{})});
var W80=E((aC5,q62)=>{var{defineProperty:JN1,getOwnPropertyDescriptor:hX4,getOwnPropertyNames:gX4}=Object,uX4=Object.prototype.hasOwnProperty,mX4=(A,B)=>JN1(A,"name",{value:B,configurable:!0}),dX4=(A,B)=>{for(var Q in B)JN1(A,Q,{get:B[Q],enumerable:!0})},cX4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of gX4(B))if(!uX4.call(A,Z)&&Z!==Q)JN1(A,Z,{get:()=>B[Z],enumerable:!(D=hX4(B,Z))||D.enumerable})}return A},lX4=(A)=>cX4(JN1({},"__esModule",{value:!0}),A),K62={};dX4(K62,{ENV_ACCOUNT_ID:()=>$62,ENV_CREDENTIAL_SCOPE:()=>w62,ENV_EXPIRATION:()=>U62,ENV_KEY:()=>H62,ENV_SECRET:()=>z62,ENV_SESSION:()=>E62,fromEnv:()=>nX4});q62.exports=lX4(K62);var pX4=HL(),iX4=eB(),H62="AWS_ACCESS_KEY_ID",z62="AWS_SECRET_ACCESS_KEY",E62="AWS_SESSION_TOKEN",U62="AWS_CREDENTIAL_EXPIRATION",w62="AWS_CREDENTIAL_SCOPE",$62="AWS_ACCOUNT_ID",nX4=mX4((A)=>async()=>{A?.logger?.debug("@aws-sdk/credential-provider-env - fromEnv");let B=process.env[H62],Q=process.env[z62],D=process.env[E62],Z=process.env[U62],G=process.env[w62],F=process.env[$62];if(B&&Q){let I={accessKeyId:B,secretAccessKey:Q,...D&&{sessionToken:D},...Z&&{expiration:new Date(Z)},...G&&{credentialScope:G},...F&&{accountId:F}};return pX4.setCredentialFeature(I,"CREDENTIALS_ENV_VARS","g"),I}throw new iX4.CredentialsProviderError("Unable to find environment variable credentials.",{logger:A?.logger})},"fromEnv")});
var WI=E((p61)=>{Object.defineProperty(p61,"__esModule",{value:!0});var Q50=b32();Q50.__exportStar(HL(),p61);Q50.__exportStar(m72(),p61);Q50.__exportStar(XD2(),p61)});
var X80=E((oC5,Y82)=>{var{defineProperty:KN1,getOwnPropertyDescriptor:eX4,getOwnPropertyNames:AV4}=Object,BV4=Object.prototype.hasOwnProperty,HN1=(A,B)=>KN1(A,"name",{value:B,configurable:!0}),QV4=(A,B)=>{for(var Q in B)KN1(A,Q,{get:B[Q],enumerable:!0})},DV4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of AV4(B))if(!BV4.call(A,Z)&&Z!==Q)KN1(A,Z,{get:()=>B[Z],enumerable:!(D=eX4(B,Z))||D.enumerable})}return A},ZV4=(A)=>DV4(KN1({},"__esModule",{value:!0}),A),A82={};QV4(A82,{AlgorithmId:()=>Z82,EndpointURLScheme:()=>D82,FieldPosition:()=>G82,HttpApiKeyAuthLocation:()=>Q82,HttpAuthLocation:()=>B82,IniSectionType:()=>F82,RequestHandlerProtocol:()=>I82,SMITHY_CONTEXT_KEY:()=>WV4,getDefaultClientConfiguration:()=>IV4,resolveDefaultRuntimeConfig:()=>YV4});Y82.exports=ZV4(A82);var B82=((A)=>{return A.HEADER="header",A.QUERY="query",A})(B82||{}),Q82=((A)=>{return A.HEADER="header",A.QUERY="query",A})(Q82||{}),D82=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(D82||{}),Z82=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(Z82||{}),GV4=HN1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),FV4=HN1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),IV4=HN1((A)=>{return GV4(A)},"getDefaultClientConfiguration"),YV4=HN1((A)=>{return FV4(A)},"resolveDefaultRuntimeConfig"),G82=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(G82||{}),WV4="__smithy_context",F82=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(F82||{}),I82=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(I82||{})});
var X81=E((aY2)=>{Object.defineProperty(aY2,"__esModule",{value:!0});aY2.commonParams=aY2.resolveClientEndpointParameters=void 0;var zO4=(A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,useGlobalEndpoint:A.useGlobalEndpoint??!1,defaultSigningName:"sts"})};aY2.resolveClientEndpointParameters=zO4;aY2.commonParams={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}});
var XD2=E((tK5,JD2)=>{var{defineProperty:BL1,getOwnPropertyDescriptor:kU4,getOwnPropertyNames:yU4}=Object,_U4=Object.prototype.hasOwnProperty,UV=(A,B)=>BL1(A,"name",{value:B,configurable:!0}),xU4=(A,B)=>{for(var Q in B)BL1(A,Q,{get:B[Q],enumerable:!0})},vU4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of yU4(B))if(!_U4.call(A,Z)&&Z!==Q)BL1(A,Z,{get:()=>B[Z],enumerable:!(D=kU4(B,Z))||D.enumerable})}return A},bU4=(A)=>vU4(BL1({},"__esModule",{value:!0}),A),FD2={};xU4(FD2,{_toBool:()=>hU4,_toNum:()=>gU4,_toStr:()=>fU4,awsExpectUnion:()=>mU4,loadRestJsonErrorCode:()=>lU4,loadRestXmlErrorCode:()=>aU4,parseJsonBody:()=>YD2,parseJsonErrorBody:()=>cU4,parseXmlBody:()=>WD2,parseXmlErrorBody:()=>nU4});JD2.exports=bU4(FD2);var fU4=UV((A)=>{if(A==null)return A;if(typeof A==="number"||typeof A==="bigint"){let B=new Error(`Received number ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}if(typeof A==="boolean"){let B=new Error(`Received boolean ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}return A},"_toStr"),hU4=UV((A)=>{if(A==null)return A;if(typeof A==="string"){let B=A.toLowerCase();if(A!==""&&B!=="false"&&B!=="true"){let Q=new Error(`Received string "${A}" where a boolean was expected.`);Q.name="Warning",console.warn(Q)}return A!==""&&B!=="false"}return A},"_toBool"),gU4=UV((A)=>{if(A==null)return A;if(typeof A==="string"){let B=Number(A);if(B.toString()!==A){let Q=new Error(`Received string "${A}" where a number was expected.`);return Q.name="Warning",console.warn(Q),A}return B}return A},"_toNum"),uU4=AL1(),mU4=UV((A)=>{if(A==null)return;if(typeof A==="object"&&"__type"in A)delete A.__type;return uU4.expectUnion(A)},"awsExpectUnion"),dU4=AL1(),ID2=UV((A,B)=>dU4.collectBody(A,B).then((Q)=>B.utf8Encoder(Q)),"collectBodyString"),YD2=UV((A,B)=>ID2(A,B).then((Q)=>{if(Q.length)try{return JSON.parse(Q)}catch(D){if(D?.name==="SyntaxError")Object.defineProperty(D,"$responseBodyText",{value:Q});throw D}return{}}),"parseJsonBody"),cU4=UV(async(A,B)=>{let Q=await YD2(A,B);return Q.message=Q.message??Q.Message,Q},"parseJsonErrorBody"),lU4=UV((A,B)=>{let Q=UV((G,F)=>Object.keys(G).find((I)=>I.toLowerCase()===F.toLowerCase()),"findKey"),D=UV((G)=>{let F=G;if(typeof F==="number")F=F.toString();if(F.indexOf(",")>=0)F=F.split(",")[0];if(F.indexOf(":")>=0)F=F.split(":")[0];if(F.indexOf("#")>=0)F=F.split("#")[1];return F},"sanitizeErrorCode"),Z=Q(A.headers,"x-amzn-errortype");if(Z!==void 0)return D(A.headers[Z]);if(B.code!==void 0)return D(B.code);if(B.__type!==void 0)return D(B.__type)},"loadRestJsonErrorCode"),pU4=AL1(),iU4=gN(),WD2=UV((A,B)=>ID2(A,B).then((Q)=>{if(Q.length){let D=new iU4.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:UV((Y,W)=>W.trim()===""&&W.includes(`
`)?"":void 0,"tagValueProcessor")});D.addEntity("#xD","\r"),D.addEntity("#10",`
`);let Z;try{Z=D.parse(Q,!0)}catch(Y){if(Y&&typeof Y==="object")Object.defineProperty(Y,"$responseBodyText",{value:Q});throw Y}let G="#text",F=Object.keys(Z)[0],I=Z[F];if(I[G])I[F]=I[G],delete I[G];return pU4.getValueFromTextNode(I)}return{}}),"parseXmlBody"),nU4=UV(async(A,B)=>{let Q=await WD2(A,B);if(Q.Error)Q.Error.message=Q.Error.message??Q.Error.Message;return Q},"parseXmlErrorBody"),aU4=UV((A,B)=>{if(B?.Error?.Code!==void 0)return B.Error.Code;if(B?.Code!==void 0)return B.Code;if(A.statusCode==404)return"NotFound"},"loadRestXmlErrorCode")});
var XH0=E((hFB)=>{Object.defineProperty(hFB,"__esModule",{value:!0});hFB.resolveHttpAuthSchemeConfig=hFB.defaultBedrockRuntimeHttpAuthSchemeProvider=hFB.defaultBedrockRuntimeHttpAuthSchemeParametersProvider=void 0;var _h6=WI(),JH0=I5(),xh6=async(A,B,Q)=>{return{operation:JH0.getSmithyContext(B).operation,region:await JH0.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};hFB.defaultBedrockRuntimeHttpAuthSchemeParametersProvider=xh6;function vh6(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"bedrock",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}var bh6=(A)=>{let B=[];switch(A.operation){default:B.push(vh6(A))}return B};hFB.defaultBedrockRuntimeHttpAuthSchemeProvider=bh6;var fh6=(A)=>{let B=_h6.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:JH0.normalizeProvider(A.authSchemePreference??[])})};hFB.resolveHttpAuthSchemeConfig=fh6});
var Z30=E((Dz5,lY2)=>{var{defineProperty:uL1,getOwnPropertyDescriptor:tR4,getOwnPropertyNames:hY2}=Object,eR4=Object.prototype.hasOwnProperty,mL1=(A,B)=>uL1(A,"name",{value:B,configurable:!0}),AO4=(A,B)=>function Q(){return A&&(B=A[hY2(A)[0]](A=0)),B},gY2=(A,B)=>{for(var Q in B)uL1(A,Q,{get:B[Q],enumerable:!0})},BO4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of hY2(B))if(!eR4.call(A,Z)&&Z!==Q)uL1(A,Z,{get:()=>B[Z],enumerable:!(D=tR4(B,Z))||D.enumerable})}return A},QO4=(A)=>BO4(uL1({},"__esModule",{value:!0}),A),uY2={};gY2(uY2,{GetRoleCredentialsCommand:()=>D30.GetRoleCredentialsCommand,SSOClient:()=>D30.SSOClient});var D30,DO4=AO4({"src/loadSso.ts"(){D30=IF2()}}),mY2={};gY2(mY2,{fromSSO:()=>GO4,isSsoProfile:()=>dY2,validateSsoProfile:()=>cY2});lY2.exports=QO4(mY2);var dY2=mL1((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),bY2=HL(),ZO4=vY2(),_w=eB(),gL1=e5(),W81=!1,fY2=mL1(async({ssoStartUrl:A,ssoSession:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoClient:G,clientConfig:F,parentClientConfig:I,profile:Y,logger:W})=>{let J,X="To refresh this SSO session run aws sso login with the corresponding profile.";if(B)try{let f=await ZO4.fromSso({profile:Y})();J={accessToken:f.token,expiresAt:new Date(f.expiration).toISOString()}}catch(f){throw new _w.CredentialsProviderError(f.message,{tryNextLink:W81,logger:W})}else try{J=await gL1.getSSOTokenFromFile(A)}catch(f){throw new _w.CredentialsProviderError("The SSO session associated with this profile is invalid. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:W81,logger:W})}if(new Date(J.expiresAt).getTime()-Date.now()<=0)throw new _w.CredentialsProviderError("The SSO session associated with this profile has expired. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:W81,logger:W});let{accessToken:V}=J,{SSOClient:C,GetRoleCredentialsCommand:K}=await Promise.resolve().then(()=>(DO4(),uY2)),H=G||new C(Object.assign({},F??{},{logger:F?.logger??I?.logger,region:F?.region??D})),z;try{z=await H.send(new K({accountId:Q,roleName:Z,accessToken:V}))}catch(f){throw new _w.CredentialsProviderError(f,{tryNextLink:W81,logger:W})}let{roleCredentials:{accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:O,credentialScope:R,accountId:T}={}}=z;if(!$||!L||!N||!O)throw new _w.CredentialsProviderError("SSO returns an invalid temporary credential.",{tryNextLink:W81,logger:W});let j={accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:new Date(O),...R&&{credentialScope:R},...T&&{accountId:T}};if(B)bY2.setCredentialFeature(j,"CREDENTIALS_SSO","s");else bY2.setCredentialFeature(j,"CREDENTIALS_SSO_LEGACY","u");return j},"resolveSSOCredentials"),cY2=mL1((A,B)=>{let{sso_start_url:Q,sso_account_id:D,sso_region:Z,sso_role_name:G}=A;if(!Q||!D||!Z||!G)throw new _w.CredentialsProviderError(`Profile is configured with invalid SSO credentials. Required parameters "sso_account_id", "sso_region", "sso_role_name", "sso_start_url". Got ${Object.keys(A).join(", ")}
Reference: https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.html`,{tryNextLink:!1,logger:B});return A},"validateSsoProfile"),GO4=mL1((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-sso - fromSSO");let{ssoStartUrl:Q,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoSession:F}=A,{ssoClient:I}=A,Y=gL1.getProfileName({profile:A.profile??B?.profile});if(!Q&&!D&&!Z&&!G&&!F){let J=(await gL1.parseKnownFiles(A))[Y];if(!J)throw new _w.CredentialsProviderError(`Profile ${Y} was not found.`,{logger:A.logger});if(!dY2(J))throw new _w.CredentialsProviderError(`Profile ${Y} is not configured with SSO credentials.`,{logger:A.logger});if(J?.sso_session){let $=(await gL1.loadSsoSessionData(A))[J.sso_session],L=` configurations in profile ${Y} and sso-session ${J.sso_session}`;if(Z&&Z!==$.sso_region)throw new _w.CredentialsProviderError("Conflicting SSO region"+L,{tryNextLink:!1,logger:A.logger});if(Q&&Q!==$.sso_start_url)throw new _w.CredentialsProviderError("Conflicting SSO start_url"+L,{tryNextLink:!1,logger:A.logger});J.sso_region=$.sso_region,J.sso_start_url=$.sso_start_url}let{sso_start_url:X,sso_account_id:V,sso_region:C,sso_role_name:K,sso_session:H}=cY2(J,A.logger);return fY2({ssoStartUrl:X,ssoSession:H,ssoAccountId:V,ssoRegion:C,ssoRoleName:K,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})}else if(!Q||!D||!Z||!G)throw new _w.CredentialsProviderError('Incomplete configuration. The fromSSO() argument hash must include "ssoStartUrl", "ssoAccountId", "ssoRegion", "ssoRoleName"',{tryNextLink:!1,logger:A.logger});else return fY2({ssoStartUrl:Q,ssoSession:F,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})},"fromSSO")});
var ZJB=E((Kr5,DJB)=>{var{defineProperty:qy1,getOwnPropertyDescriptor:kd6,getOwnPropertyNames:yd6}=Object,_d6=Object.prototype.hasOwnProperty,xx=(A,B)=>qy1(A,"name",{value:B,configurable:!0}),xd6=(A,B)=>{for(var Q in B)qy1(A,Q,{get:B[Q],enumerable:!0})},vd6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of yd6(B))if(!_d6.call(A,Z)&&Z!==Q)qy1(A,Z,{get:()=>B[Z],enumerable:!(D=kd6(B,Z))||D.enumerable})}return A},bd6=(A)=>vd6(qy1({},"__esModule",{value:!0}),A),eWB={};xd6(eWB,{Field:()=>gd6,Fields:()=>ud6,HttpRequest:()=>md6,HttpResponse:()=>dd6,IHttpRequest:()=>AJB.HttpRequest,getHttpHandlerExtensionConfiguration:()=>fd6,isValidHostname:()=>QJB,resolveHttpHandlerRuntimeConfig:()=>hd6});DJB.exports=bd6(eWB);var fd6=xx((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),hd6=xx((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),AJB=LH0(),gd6=class{static{xx(this,"Field")}constructor({name:A,kind:B=AJB.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},ud6=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{xx(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},md6=class A{static{xx(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=BJB(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function BJB(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}xx(BJB,"cloneQuery");var dd6=class{static{xx(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function QJB(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}xx(QJB,"isValidHostname")});
var ZYB=E((QYB)=>{Object.defineProperty(QYB,"__esModule",{value:!0});QYB.convertToBuffer=void 0;var Vg6=BYB(),Cg6=typeof Buffer!=="undefined"&&Buffer.from?function(A){return Buffer.from(A,"utf8")}:Vg6.fromUtf8;function Kg6(A){if(A instanceof Uint8Array)return A;if(typeof A==="string")return Cg6(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)}QYB.convertToBuffer=Kg6});
var _82=E((QK5,y82)=>{var{defineProperty:wN1,getOwnPropertyDescriptor:NV4,getOwnPropertyNames:LV4}=Object,MV4=Object.prototype.hasOwnProperty,T2=(A,B)=>wN1(A,"name",{value:B,configurable:!0}),RV4=(A,B)=>{for(var Q in B)wN1(A,Q,{get:B[Q],enumerable:!0})},OV4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of LV4(B))if(!MV4.call(A,Z)&&Z!==Q)wN1(A,Z,{get:()=>B[Z],enumerable:!(D=NV4(B,Z))||D.enumerable})}return A},TV4=(A)=>OV4(wN1({},"__esModule",{value:!0}),A),z82={};RV4(z82,{Client:()=>PV4,Command:()=>U82,LazyJsonString:()=>Rg,NoOpLogger:()=>MC4,SENSITIVE_STRING:()=>jV4,ServiceException:()=>XC4,_json:()=>U80,collectBody:()=>V80.collectBody,convertMap:()=>RC4,createAggregatedClient:()=>kV4,dateToUtcString:()=>M82,decorateServiceException:()=>R82,emitWarningIfUnsupportedVersion:()=>HC4,expectBoolean:()=>_V4,expectByte:()=>E80,expectFloat32:()=>EN1,expectInt:()=>vV4,expectInt32:()=>H80,expectLong:()=>x61,expectNonNull:()=>fV4,expectNumber:()=>_61,expectObject:()=>w82,expectShort:()=>z80,expectString:()=>hV4,expectUnion:()=>gV4,extendedEncodeURIComponent:()=>V80.extendedEncodeURIComponent,getArrayIfSingleItem:()=>NC4,getDefaultClientConfiguration:()=>$C4,getDefaultExtensionConfiguration:()=>T82,getValueFromTextNode:()=>P82,handleFloat:()=>dV4,isSerializableHeaderValue:()=>LC4,limitedParseDouble:()=>q80,limitedParseFloat:()=>cV4,limitedParseFloat32:()=>lV4,loadConfigsForDefaultMode:()=>KC4,logger:()=>v61,map:()=>L80,parseBoolean:()=>yV4,parseEpochTimestamp:()=>QC4,parseRfc3339DateTime:()=>sV4,parseRfc3339DateTimeWithOffset:()=>oV4,parseRfc7231DateTime:()=>BC4,quoteHeader:()=>j82,resolveDefaultRuntimeConfig:()=>qC4,resolvedPath:()=>V80.resolvedPath,serializeDateTime:()=>kC4,serializeFloat:()=>jC4,splitEvery:()=>k82,splitHeader:()=>yC4,strictParseByte:()=>L82,strictParseDouble:()=>$80,strictParseFloat:()=>uV4,strictParseFloat32:()=>$82,strictParseInt:()=>pV4,strictParseInt32:()=>iV4,strictParseLong:()=>N82,strictParseShort:()=>Ls,take:()=>OC4,throwDefaultError:()=>O82,withBaseException:()=>VC4});y82.exports=TV4(z82);var E82=Uw(),PV4=class{constructor(A){this.config=A,this.middlewareStack=E82.constructStack()}static{T2(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},V80=$6(),K80=X80(),U82=class{constructor(){this.middlewareStack=E82.constructStack()}static{T2(this,"Command")}static classBuilder(){return new SV4}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[K80.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},SV4=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{T2(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}build(){let A=this,B;return B=class extends U82{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this)}static{T2(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},jV4="***SensitiveInformation***",kV4=T2((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=T2(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),yV4=T2((A)=>{switch(A){case"true":return!0;case"false":return!1;default:throw new Error(`Unable to parse boolean value "${A}"`)}},"parseBoolean"),_V4=T2((A)=>{if(A===null||A===void 0)return;if(typeof A==="number"){if(A===0||A===1)v61.warn(UN1(`Expected boolean, got ${typeof A}: ${A}`));if(A===0)return!1;if(A===1)return!0}if(typeof A==="string"){let B=A.toLowerCase();if(B==="false"||B==="true")v61.warn(UN1(`Expected boolean, got ${typeof A}: ${A}`));if(B==="false")return!1;if(B==="true")return!0}if(typeof A==="boolean")return A;throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)},"expectBoolean"),_61=T2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string"){let B=parseFloat(A);if(!Number.isNaN(B)){if(String(B)!==String(A))v61.warn(UN1(`Expected number but observed string: ${A}`));return B}}if(typeof A==="number")return A;throw new TypeError(`Expected number, got ${typeof A}: ${A}`)},"expectNumber"),xV4=Math.ceil(340282346638528860000000000000000000000),EN1=T2((A)=>{let B=_61(A);if(B!==void 0&&!Number.isNaN(B)&&B!==1/0&&B!==-1/0){if(Math.abs(B)>xV4)throw new TypeError(`Expected 32-bit float, got ${A}`)}return B},"expectFloat32"),x61=T2((A)=>{if(A===null||A===void 0)return;if(Number.isInteger(A)&&!Number.isNaN(A))return A;throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)},"expectLong"),vV4=x61,H80=T2((A)=>w80(A,32),"expectInt32"),z80=T2((A)=>w80(A,16),"expectShort"),E80=T2((A)=>w80(A,8),"expectByte"),w80=T2((A,B)=>{let Q=x61(A);if(Q!==void 0&&bV4(Q,B)!==Q)throw new TypeError(`Expected ${B}-bit integer, got ${A}`);return Q},"expectSizedInt"),bV4=T2((A,B)=>{switch(B){case 32:return Int32Array.of(A)[0];case 16:return Int16Array.of(A)[0];case 8:return Int8Array.of(A)[0]}},"castInt"),fV4=T2((A,B)=>{if(A===null||A===void 0){if(B)throw new TypeError(`Expected a non-null value for ${B}`);throw new TypeError("Expected a non-null value")}return A},"expectNonNull"),w82=T2((A)=>{if(A===null||A===void 0)return;if(typeof A==="object"&&!Array.isArray(A))return A;let B=Array.isArray(A)?"array":typeof A;throw new TypeError(`Expected object, got ${B}: ${A}`)},"expectObject"),hV4=T2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string")return A;if(["boolean","number","bigint"].includes(typeof A))return v61.warn(UN1(`Expected string, got ${typeof A}: ${A}`)),String(A);throw new TypeError(`Expected string, got ${typeof A}: ${A}`)},"expectString"),gV4=T2((A)=>{if(A===null||A===void 0)return;let B=w82(A),Q=Object.entries(B).filter(([,D])=>D!=null).map(([D])=>D);if(Q.length===0)throw new TypeError("Unions must have exactly one non-null member. None were found.");if(Q.length>1)throw new TypeError(`Unions must have exactly one non-null member. Keys ${Q} were not null.`);return B},"expectUnion"),$80=T2((A)=>{if(typeof A=="string")return _61(Rs(A));return _61(A)},"strictParseDouble"),uV4=$80,$82=T2((A)=>{if(typeof A=="string")return EN1(Rs(A));return EN1(A)},"strictParseFloat32"),mV4=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,Rs=T2((A)=>{let B=A.match(mV4);if(B===null||B[0].length!==A.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(A)},"parseNumber"),q80=T2((A)=>{if(typeof A=="string")return q82(A);return _61(A)},"limitedParseDouble"),dV4=q80,cV4=q80,lV4=T2((A)=>{if(typeof A=="string")return q82(A);return EN1(A)},"limitedParseFloat32"),q82=T2((A)=>{switch(A){case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error(`Unable to parse float value: ${A}`)}},"parseFloatString"),N82=T2((A)=>{if(typeof A==="string")return x61(Rs(A));return x61(A)},"strictParseLong"),pV4=N82,iV4=T2((A)=>{if(typeof A==="string")return H80(Rs(A));return H80(A)},"strictParseInt32"),Ls=T2((A)=>{if(typeof A==="string")return z80(Rs(A));return z80(A)},"strictParseShort"),L82=T2((A)=>{if(typeof A==="string")return E80(Rs(A));return E80(A)},"strictParseByte"),UN1=T2((A)=>{return String(new TypeError(A).stack||A).split(`
`).slice(0,5).filter((B)=>!B.includes("stackTraceWarning")).join(`
`)},"stackTraceWarning"),v61={warn:console.warn},nV4=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],N80=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function M82(A){let B=A.getUTCFullYear(),Q=A.getUTCMonth(),D=A.getUTCDay(),Z=A.getUTCDate(),G=A.getUTCHours(),F=A.getUTCMinutes(),I=A.getUTCSeconds(),Y=Z<10?`0${Z}`:`${Z}`,W=G<10?`0${G}`:`${G}`,J=F<10?`0${F}`:`${F}`,X=I<10?`0${I}`:`${I}`;return`${nV4[D]}, ${Y} ${N80[Q]} ${B} ${W}:${J}:${X} GMT`}T2(M82,"dateToUtcString");var aV4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),sV4=T2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=aV4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W]=B,J=Ls(Ms(D)),X=zL(Z,"month",1,12),V=zL(G,"day",1,31);return y61(J,X,V,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})},"parseRfc3339DateTime"),rV4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),oV4=T2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=rV4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W,J]=B,X=Ls(Ms(D)),V=zL(Z,"month",1,12),C=zL(G,"day",1,31),K=y61(X,V,C,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W});if(J.toUpperCase()!="Z")K.setTime(K.getTime()-JC4(J));return K},"parseRfc3339DateTimeWithOffset"),tV4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),eV4=new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),AC4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/),BC4=T2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-7231 date-times must be expressed as strings");let B=tV4.exec(A);if(B){let[Q,D,Z,G,F,I,Y,W]=B;return y61(Ls(Ms(G)),C80(Z),zL(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})}if(B=eV4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return GC4(y61(DC4(G),C80(Z),zL(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W}))}if(B=AC4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return y61(Ls(Ms(W)),C80(D),zL(Z.trimLeft(),"day",1,31),{hours:G,minutes:F,seconds:I,fractionalMilliseconds:Y})}throw new TypeError("Invalid RFC-7231 date-time value")},"parseRfc7231DateTime"),QC4=T2((A)=>{if(A===null||A===void 0)return;let B;if(typeof A==="number")B=A;else if(typeof A==="string")B=$80(A);else if(typeof A==="object"&&A.tag===1)B=A.value;else throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(B)||B===1/0||B===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(B*1000))},"parseEpochTimestamp"),y61=T2((A,B,Q,D)=>{let Z=B-1;return IC4(A,Z,Q),new Date(Date.UTC(A,Z,Q,zL(D.hours,"hour",0,23),zL(D.minutes,"minute",0,59),zL(D.seconds,"seconds",0,60),WC4(D.fractionalMilliseconds)))},"buildDate"),DC4=T2((A)=>{let B=new Date().getUTCFullYear(),Q=Math.floor(B/100)*100+Ls(Ms(A));if(Q<B)return Q+100;return Q},"parseTwoDigitYear"),ZC4=1576800000000,GC4=T2((A)=>{if(A.getTime()-new Date().getTime()>ZC4)return new Date(Date.UTC(A.getUTCFullYear()-100,A.getUTCMonth(),A.getUTCDate(),A.getUTCHours(),A.getUTCMinutes(),A.getUTCSeconds(),A.getUTCMilliseconds()));return A},"adjustRfc850Year"),C80=T2((A)=>{let B=N80.indexOf(A);if(B<0)throw new TypeError(`Invalid month: ${A}`);return B+1},"parseMonthByShortName"),FC4=[31,28,31,30,31,30,31,31,30,31,30,31],IC4=T2((A,B,Q)=>{let D=FC4[B];if(B===1&&YC4(A))D=29;if(Q>D)throw new TypeError(`Invalid day for ${N80[B]} in ${A}: ${Q}`)},"validateDayOfMonth"),YC4=T2((A)=>{return A%4===0&&(A%100!==0||A%400===0)},"isLeapYear"),zL=T2((A,B,Q,D)=>{let Z=L82(Ms(A));if(Z<Q||Z>D)throw new TypeError(`${B} must be between ${Q} and ${D}, inclusive`);return Z},"parseDateValue"),WC4=T2((A)=>{if(A===null||A===void 0)return 0;return $82("0."+A)*1000},"parseMilliseconds"),JC4=T2((A)=>{let B=A[0],Q=1;if(B=="+")Q=1;else if(B=="-")Q=-1;else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);let D=Number(A.substring(1,3)),Z=Number(A.substring(4,6));return Q*(D*60+Z)*60*1000},"parseOffsetToMilliseconds"),Ms=T2((A)=>{let B=0;while(B<A.length-1&&A.charAt(B)==="0")B++;if(B===0)return A;return A.slice(B)},"stripLeadingZeroes"),XC4=class A extends Error{static{T2(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},R82=T2((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),O82=T2(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=CC4(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw R82(F,B)},"throwDefaultError"),VC4=T2((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{O82({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),CC4=T2((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),KC4=T2((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),H82=!1,HC4=T2((A)=>{if(A&&!H82&&parseInt(A.substring(1,A.indexOf(".")))<16)H82=!0},"emitWarningIfUnsupportedVersion"),zC4=T2((A)=>{let B=[];for(let Q in K80.AlgorithmId){let D=K80.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),EC4=T2((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),UC4=T2((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),wC4=T2((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),T82=T2((A)=>{return Object.assign(zC4(A),UC4(A))},"getDefaultExtensionConfiguration"),$C4=T82,qC4=T2((A)=>{return Object.assign(EC4(A),wC4(A))},"resolveDefaultRuntimeConfig"),NC4=T2((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),P82=T2((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=P82(A[Q]);return A},"getValueFromTextNode"),LC4=T2((A)=>{return A!=null},"isSerializableHeaderValue"),Rg=T2(function A(B){return Object.assign(new String(B),{deserializeJSON(){return JSON.parse(String(B))},toString(){return String(B)},toJSON(){return String(B)}})},"LazyJsonString");Rg.from=(A)=>{if(A&&typeof A==="object"&&(A instanceof Rg||("deserializeJSON"in A)))return A;else if(typeof A==="string"||Object.getPrototypeOf(A)===String.prototype)return Rg(String(A));return Rg(JSON.stringify(A))};Rg.fromObject=Rg.from;var MC4=class{static{T2(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function L80(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,TC4(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}S82(D,null,G,F)}return D}T2(L80,"map");var RC4=T2((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),OC4=T2((A,B)=>{let Q={};for(let D in B)S82(Q,A,B,D);return Q},"take"),TC4=T2((A,B,Q)=>{return L80(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),S82=T2((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=PC4,Y=SC4,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),PC4=T2((A)=>A!=null,"nonNullish"),SC4=T2((A)=>A,"pass");function j82(A){if(A.includes(",")||A.includes('"'))A=`"${A.replace(/"/g,"\\\"")}"`;return A}T2(j82,"quoteHeader");var jC4=T2((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),kC4=T2((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),U80=T2((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(U80);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=U80(A[Q])}return B}return A},"_json");function k82(A,B,Q){if(Q<=0||!Number.isInteger(Q))throw new Error("Invalid number of delimiters ("+Q+") for splitEvery.");let D=A.split(B);if(Q===1)return D;let Z=[],G="";for(let F=0;F<D.length;F++){if(G==="")G=D[F];else G+=B+D[F];if((F+1)%Q===0)Z.push(G),G=""}if(G!=="")Z.push(G);return Z}T2(k82,"splitEvery");var yC4=T2((A)=>{let B=A.length,Q=[],D=!1,Z=void 0,G=0;for(let F=0;F<B;++F){let I=A[F];switch(I){case'"':if(Z!=="\\")D=!D;break;case",":if(!D)Q.push(A.slice(G,F)),G=F+1;break;default:}Z=I}return Q.push(A.slice(G)),Q.map((F)=>{F=F.trim();let I=F.length;if(I<2)return F;if(F[0]==='"'&&F[I-1]==='"')F=F.slice(1,I-1);return F.replace(/\\"/g,'"')})},"splitHeader")});
var _G2=E(($H5,yG2)=>{var{defineProperty:UL1,getOwnPropertyDescriptor:uq4,getOwnPropertyNames:mq4}=Object,dq4=Object.prototype.hasOwnProperty,B_=(A,B)=>UL1(A,"name",{value:B,configurable:!0}),cq4=(A,B)=>{for(var Q in B)UL1(A,Q,{get:B[Q],enumerable:!0})},lq4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of mq4(B))if(!dq4.call(A,Z)&&Z!==Q)UL1(A,Z,{get:()=>B[Z],enumerable:!(D=uq4(B,Z))||D.enumerable})}return A},pq4=(A)=>lq4(UL1({},"__esModule",{value:!0}),A),PG2={};cq4(PG2,{Field:()=>aq4,Fields:()=>sq4,HttpRequest:()=>rq4,HttpResponse:()=>oq4,IHttpRequest:()=>SG2.HttpRequest,getHttpHandlerExtensionConfiguration:()=>iq4,isValidHostname:()=>kG2,resolveHttpHandlerRuntimeConfig:()=>nq4});yG2.exports=pq4(PG2);var iq4=B_((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),nq4=B_((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),SG2=W50(),aq4=class{static{B_(this,"Field")}constructor({name:A,kind:B=SG2.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},sq4=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{B_(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},rq4=class A{static{B_(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=jG2(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function jG2(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}B_(jG2,"cloneQuery");var oq4=class{static{B_(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function kG2(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}B_(kG2,"isValidHostname")});
var _s=E((AH5,LD2)=>{var{defineProperty:DL1,getOwnPropertyDescriptor:sU4,getOwnPropertyNames:rU4}=Object,oU4=Object.prototype.hasOwnProperty,jT=(A,B)=>DL1(A,"name",{value:B,configurable:!0}),tU4=(A,B)=>{for(var Q in B)DL1(A,Q,{get:B[Q],enumerable:!0})},eU4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of rU4(B))if(!oU4.call(A,Z)&&Z!==Q)DL1(A,Z,{get:()=>B[Z],enumerable:!(D=sU4(B,Z))||D.enumerable})}return A},Aw4=(A)=>eU4(DL1({},"__esModule",{value:!0}),A),HD2={};tU4(HD2,{DEFAULT_UA_APP_ID:()=>zD2,getUserAgentMiddlewareOptions:()=>ND2,getUserAgentPlugin:()=>Yw4,resolveUserAgentConfig:()=>UD2,userAgentMiddleware:()=>qD2});LD2.exports=Aw4(HD2);var Bw4=VB(),zD2=void 0;function ED2(A){if(A===void 0)return!0;return typeof A==="string"&&A.length<=50}jT(ED2,"isValidUserAgentAppId");function UD2(A){let B=Bw4.normalizeProvider(A.userAgentAppId??zD2),{customUserAgent:Q}=A;return Object.assign(A,{customUserAgent:typeof Q==="string"?[[Q]]:Q,userAgentAppId:jT(async()=>{let D=await B();if(!ED2(D)){let Z=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console:A.logger;if(typeof D!=="string")Z?.warn("userAgentAppId must be a string or undefined.");else if(D.length>50)Z?.warn("The provided userAgentAppId exceeds the maximum length of 50 characters.")}return D},"userAgentAppId")})}jT(UD2,"resolveUserAgentConfig");var Qw4=Ts(),Dw4=I32(),UL=WI(),Zw4=/\d{12}\.ddb/;async function wD2(A,B,Q){if(Q.request?.headers?.["smithy-protocol"]==="rpc-v2-cbor")UL.setFeature(A,"PROTOCOL_RPC_V2_CBOR","M");if(typeof B.retryStrategy==="function"){let G=await B.retryStrategy();if(typeof G.acquireInitialRetryToken==="function")if(G.constructor?.name?.includes("Adaptive"))UL.setFeature(A,"RETRY_MODE_ADAPTIVE","F");else UL.setFeature(A,"RETRY_MODE_STANDARD","E");else UL.setFeature(A,"RETRY_MODE_LEGACY","D")}if(typeof B.accountIdEndpointMode==="function"){let G=A.endpointV2;if(String(G?.url?.hostname).match(Zw4))UL.setFeature(A,"ACCOUNT_ID_ENDPOINT","O");switch(await B.accountIdEndpointMode?.()){case"disabled":UL.setFeature(A,"ACCOUNT_ID_MODE_DISABLED","Q");break;case"preferred":UL.setFeature(A,"ACCOUNT_ID_MODE_PREFERRED","P");break;case"required":UL.setFeature(A,"ACCOUNT_ID_MODE_REQUIRED","R");break}}let Z=A.__smithy_context?.selectedHttpAuthScheme?.identity;if(Z?.$source){let G=Z;if(G.accountId)UL.setFeature(A,"RESOLVED_ACCOUNT_ID","T");for(let[F,I]of Object.entries(G.$source??{}))UL.setFeature(A,F,I)}}jT(wD2,"checkFeatures");var VD2="user-agent",D50="x-amz-user-agent",CD2=" ",Z50="/",Gw4=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g,Fw4=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g,KD2="-",Iw4=1024;function $D2(A){let B="";for(let Q in A){let D=A[Q];if(B.length+D.length+1<=Iw4){if(B.length)B+=","+D;else B+=D;continue}break}return B}jT($D2,"encodeFeatures");var qD2=jT((A)=>(B,Q)=>async(D)=>{let{request:Z}=D;if(!Dw4.HttpRequest.isInstance(Z))return B(D);let{headers:G}=Z,F=Q?.userAgent?.map(QL1)||[],I=(await A.defaultUserAgentProvider()).map(QL1);await wD2(Q,A,D);let Y=Q;I.push(`m/${$D2(Object.assign({},Q.__smithy_context?.features,Y.__aws_sdk_context?.features))}`);let W=A?.customUserAgent?.map(QL1)||[],J=await A.userAgentAppId();if(J)I.push(QL1([`app/${J}`]));let X=Qw4.getUserAgentPrefix(),V=(X?[X]:[]).concat([...I,...F,...W]).join(CD2),C=[...I.filter((K)=>K.startsWith("aws-sdk-")),...W].join(CD2);if(A.runtime!=="browser"){if(C)G[D50]=G[D50]?`${G[VD2]} ${C}`:C;G[VD2]=V}else G[D50]=V;return B({...D,request:Z})},"userAgentMiddleware"),QL1=jT((A)=>{let B=A[0].split(Z50).map((F)=>F.replace(Gw4,KD2)).join(Z50),Q=A[1]?.replace(Fw4,KD2),D=B.indexOf(Z50),Z=B.substring(0,D),G=B.substring(D+1);if(Z==="api")G=G.toLowerCase();return[Z,G,Q].filter((F)=>F&&F.length>0).reduce((F,I,Y)=>{switch(Y){case 0:return I;case 1:return`${F}/${I}`;default:return`${F}#${I}`}},"")},"escapeUserAgent"),ND2={name:"getUserAgentMiddleware",step:"build",priority:"low",tags:["SET_USER_AGENT","USER_AGENT"],override:!0},Yw4=jT((A)=>({applyToStack:jT((B)=>{B.add(qD2(A),ND2)},"applyToStack")}),"getUserAgentPlugin")});
var b32=E((kK5,fN1)=>{var Y32,W32,J32,X32,V32,C32,K32,H32,z32,E32,U32,w32,$32,vN1,S80,q32,N32,L32,Ps,M32,R32,O32,T32,P32,S32,j32,k32,y32,bN1,_32,x32,v32;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof fN1==="object"&&typeof kK5==="object")A(Q(B,Q(kK5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};Y32=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},W32=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},J32=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},X32=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},V32=function(G,F){return function(I,Y){F(I,Y,G)}},C32=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},K32=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},H32=function(G){return typeof G==="symbol"?G:"".concat(G)},z32=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},E32=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},U32=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},w32=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},$32=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))bN1(F,G,I)},bN1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},vN1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},S80=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},q32=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(S80(arguments[F]));return G},N32=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},L32=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},Ps=function(G){return this instanceof Ps?(this.v=G,this):new Ps(G)},M32=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof Ps?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},R32=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:Ps(G[W](X)),done:!1}:J?J(X):X}:J}},O32=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof vN1==="function"?vN1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},T32=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};P32=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")bN1(F,G,I[Y])}return Q(F,G),F},S32=function(G){return G&&G.__esModule?G:{default:G}},j32=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},k32=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},y32=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},_32=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};x32=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},v32=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",Y32),A("__assign",W32),A("__rest",J32),A("__decorate",X32),A("__param",V32),A("__esDecorate",C32),A("__runInitializers",K32),A("__propKey",H32),A("__setFunctionName",z32),A("__metadata",E32),A("__awaiter",U32),A("__generator",w32),A("__exportStar",$32),A("__createBinding",bN1),A("__values",vN1),A("__read",S80),A("__spread",q32),A("__spreadArrays",N32),A("__spreadArray",L32),A("__await",Ps),A("__asyncGenerator",M32),A("__asyncDelegator",R32),A("__asyncValues",O32),A("__makeTemplateObject",T32),A("__importStar",P32),A("__importDefault",S32),A("__classPrivateFieldGet",j32),A("__classPrivateFieldSet",k32),A("__classPrivateFieldIn",y32),A("__addDisposableResource",_32),A("__disposeResources",x32),A("__rewriteRelativeImportExtension",v32)})});
var b61=E((EK5,X52)=>{var{defineProperty:MN1,getOwnPropertyDescriptor:LK4,getOwnPropertyNames:MK4}=Object,RK4=Object.prototype.hasOwnProperty,LN1=(A,B)=>MN1(A,"name",{value:B,configurable:!0}),OK4=(A,B)=>{for(var Q in B)MN1(A,Q,{get:B[Q],enumerable:!0})},TK4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of MK4(B))if(!RK4.call(A,Z)&&Z!==Q)MN1(A,Z,{get:()=>B[Z],enumerable:!(D=LK4(B,Z))||D.enumerable})}return A},PK4=(A)=>TK4(MN1({},"__esModule",{value:!0}),A),I52={};OK4(I52,{getHostHeaderPlugin:()=>jK4,hostHeaderMiddleware:()=>W52,hostHeaderMiddlewareOptions:()=>J52,resolveHostHeaderConfig:()=>Y52});X52.exports=PK4(I52);var SK4=F52();function Y52(A){return A}LN1(Y52,"resolveHostHeaderConfig");var W52=LN1((A)=>(B)=>async(Q)=>{if(!SK4.HttpRequest.isInstance(Q.request))return B(Q);let{request:D}=Q,{handlerProtocol:Z=""}=A.requestHandler.metadata||{};if(Z.indexOf("h2")>=0&&!D.headers[":authority"])delete D.headers.host,D.headers[":authority"]=D.hostname+(D.port?":"+D.port:"");else if(!D.headers.host){let G=D.hostname;if(D.port!=null)G+=`:${D.port}`;D.headers.host=G}return B(Q)},"hostHeaderMiddleware"),J52={name:"hostHeaderMiddleware",step:"build",priority:"low",tags:["HOST"],override:!0},jK4=LN1((A)=>({applyToStack:LN1((B)=>{B.add(W52(A),J52)},"applyToStack")}),"getHostHeaderPlugin")});
var b82=E((x82)=>{Object.defineProperty(x82,"__esModule",{value:!0});x82.getCredentials=x82.createGetRequest=void 0;var M80=eB(),_C4=K82(),xC4=_82(),vC4=Fy();function bC4(A){return new _C4.HttpRequest({protocol:A.protocol,hostname:A.hostname,port:Number(A.port),path:A.pathname,query:Array.from(A.searchParams.entries()).reduce((B,[Q,D])=>{return B[Q]=D,B},{}),fragment:A.hash})}x82.createGetRequest=bC4;async function fC4(A,B){let D=await vC4.sdkStreamMixin(A.body).transformToString();if(A.statusCode===200){let Z=JSON.parse(D);if(typeof Z.AccessKeyId!=="string"||typeof Z.SecretAccessKey!=="string"||typeof Z.Token!=="string"||typeof Z.Expiration!=="string")throw new M80.CredentialsProviderError("HTTP credential provider response not of the required format, an object matching: { AccessKeyId: string, SecretAccessKey: string, Token: string, Expiration: string(rfc3339) }",{logger:B});return{accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.Token,expiration:xC4.parseRfc3339DateTime(Z.Expiration)}}if(A.statusCode>=400&&A.statusCode<500){let Z={};try{Z=JSON.parse(D)}catch(G){}throw Object.assign(new M80.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B}),{Code:Z.Code,Message:Z.Message})}throw new M80.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B})}x82.getCredentials=fC4});
var c30=E((Kz5,d30)=>{var{defineProperty:cL1,getOwnPropertyDescriptor:eO4,getOwnPropertyNames:AT4}=Object,BT4=Object.prototype.hasOwnProperty,Z9=(A,B)=>cL1(A,"name",{value:B,configurable:!0}),QT4=(A,B)=>{for(var Q in B)cL1(A,Q,{get:B[Q],enumerable:!0})},v30=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of AT4(B))if(!BT4.call(A,Z)&&Z!==Q)cL1(A,Z,{get:()=>B[Z],enumerable:!(D=eO4(B,Z))||D.enumerable})}return A},DT4=(A,B,Q)=>(v30(A,B,"default"),Q&&v30(Q,B,"default")),ZT4=(A)=>v30(cL1({},"__esModule",{value:!0}),A),f30={};QT4(f30,{AssumeRoleCommand:()=>u30,AssumeRoleResponseFilterSensitiveLog:()=>AJ2,AssumeRoleWithWebIdentityCommand:()=>m30,AssumeRoleWithWebIdentityRequestFilterSensitiveLog:()=>IJ2,AssumeRoleWithWebIdentityResponseFilterSensitiveLog:()=>YJ2,ClientInputEndpointParameters:()=>rT4.ClientInputEndpointParameters,CredentialsFilterSensitiveLog:()=>h30,ExpiredTokenException:()=>BJ2,IDPCommunicationErrorException:()=>WJ2,IDPRejectedClaimException:()=>GJ2,InvalidIdentityTokenException:()=>FJ2,MalformedPolicyDocumentException:()=>QJ2,PackedPolicyTooLargeException:()=>DJ2,RegionDisabledException:()=>ZJ2,STS:()=>$J2,STSServiceException:()=>vT,decorateDefaultCredentialProvider:()=>eT4,getDefaultRoleAssumer:()=>OJ2,getDefaultRoleAssumerWithWebIdentity:()=>TJ2});d30.exports=ZT4(f30);DT4(f30,J81(),d30.exports);var GT4=QZ(),FT4=q6(),IT4=T3(),YT4=QZ(),WT4=X81(),eW2=QZ(),JT4=QZ(),vT=class A extends JT4.ServiceException{static{Z9(this,"STSServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},h30=Z9((A)=>({...A,...A.SecretAccessKey&&{SecretAccessKey:eW2.SENSITIVE_STRING}}),"CredentialsFilterSensitiveLog"),AJ2=Z9((A)=>({...A,...A.Credentials&&{Credentials:h30(A.Credentials)}}),"AssumeRoleResponseFilterSensitiveLog"),BJ2=class A extends vT{static{Z9(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},QJ2=class A extends vT{static{Z9(this,"MalformedPolicyDocumentException")}name="MalformedPolicyDocumentException";$fault="client";constructor(B){super({name:"MalformedPolicyDocumentException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},DJ2=class A extends vT{static{Z9(this,"PackedPolicyTooLargeException")}name="PackedPolicyTooLargeException";$fault="client";constructor(B){super({name:"PackedPolicyTooLargeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},ZJ2=class A extends vT{static{Z9(this,"RegionDisabledException")}name="RegionDisabledException";$fault="client";constructor(B){super({name:"RegionDisabledException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},GJ2=class A extends vT{static{Z9(this,"IDPRejectedClaimException")}name="IDPRejectedClaimException";$fault="client";constructor(B){super({name:"IDPRejectedClaimException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},FJ2=class A extends vT{static{Z9(this,"InvalidIdentityTokenException")}name="InvalidIdentityTokenException";$fault="client";constructor(B){super({name:"InvalidIdentityTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},IJ2=Z9((A)=>({...A,...A.WebIdentityToken&&{WebIdentityToken:eW2.SENSITIVE_STRING}}),"AssumeRoleWithWebIdentityRequestFilterSensitiveLog"),YJ2=Z9((A)=>({...A,...A.Credentials&&{Credentials:h30(A.Credentials)}}),"AssumeRoleWithWebIdentityResponseFilterSensitiveLog"),WJ2=class A extends vT{static{Z9(this,"IDPCommunicationErrorException")}name="IDPCommunicationErrorException";$fault="client";constructor(B){super({name:"IDPCommunicationErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},g30=WI(),XT4=fL1(),V5=QZ(),VT4=Z9(async(A,B)=>{let Q=HJ2,D;return D=wJ2({...LT4(A,B),[EJ2]:mT4,[UJ2]:zJ2}),KJ2(B,Q,"/",void 0,D)},"se_AssumeRoleCommand"),CT4=Z9(async(A,B)=>{let Q=HJ2,D;return D=wJ2({...MT4(A,B),[EJ2]:dT4,[UJ2]:zJ2}),KJ2(B,Q,"/",void 0,D)},"se_AssumeRoleWithWebIdentityCommand"),KT4=Z9(async(A,B)=>{if(A.statusCode>=300)return JJ2(A,B);let Q=await g30.parseXmlBody(A.body,B),D={};return D=kT4(Q.AssumeRoleResult,B),{$metadata:bT(A),...D}},"de_AssumeRoleCommand"),HT4=Z9(async(A,B)=>{if(A.statusCode>=300)return JJ2(A,B);let Q=await g30.parseXmlBody(A.body,B),D={};return D=yT4(Q.AssumeRoleWithWebIdentityResult,B),{$metadata:bT(A),...D}},"de_AssumeRoleWithWebIdentityCommand"),JJ2=Z9(async(A,B)=>{let Q={...A,body:await g30.parseXmlErrorBody(A.body,B)},D=cT4(A,Q.body);switch(D){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await zT4(Q,B);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await $T4(Q,B);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await qT4(Q,B);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await NT4(Q,B);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await ET4(Q,B);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await UT4(Q,B);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await wT4(Q,B);default:let Z=Q.body;return uT4({output:A,parsedBody:Z.Error,errorCode:D})}},"de_CommandError"),zT4=Z9(async(A,B)=>{let Q=A.body,D=_T4(Q.Error,B),Z=new BJ2({$metadata:bT(A),...D});return V5.decorateServiceException(Z,Q)},"de_ExpiredTokenExceptionRes"),ET4=Z9(async(A,B)=>{let Q=A.body,D=xT4(Q.Error,B),Z=new WJ2({$metadata:bT(A),...D});return V5.decorateServiceException(Z,Q)},"de_IDPCommunicationErrorExceptionRes"),UT4=Z9(async(A,B)=>{let Q=A.body,D=vT4(Q.Error,B),Z=new GJ2({$metadata:bT(A),...D});return V5.decorateServiceException(Z,Q)},"de_IDPRejectedClaimExceptionRes"),wT4=Z9(async(A,B)=>{let Q=A.body,D=bT4(Q.Error,B),Z=new FJ2({$metadata:bT(A),...D});return V5.decorateServiceException(Z,Q)},"de_InvalidIdentityTokenExceptionRes"),$T4=Z9(async(A,B)=>{let Q=A.body,D=fT4(Q.Error,B),Z=new QJ2({$metadata:bT(A),...D});return V5.decorateServiceException(Z,Q)},"de_MalformedPolicyDocumentExceptionRes"),qT4=Z9(async(A,B)=>{let Q=A.body,D=hT4(Q.Error,B),Z=new DJ2({$metadata:bT(A),...D});return V5.decorateServiceException(Z,Q)},"de_PackedPolicyTooLargeExceptionRes"),NT4=Z9(async(A,B)=>{let Q=A.body,D=gT4(Q.Error,B),Z=new ZJ2({$metadata:bT(A),...D});return V5.decorateServiceException(Z,Q)},"de_RegionDisabledExceptionRes"),LT4=Z9((A,B)=>{let Q={};if(A[Ir]!=null)Q[Ir]=A[Ir];if(A[Yr]!=null)Q[Yr]=A[Yr];if(A[Gr]!=null){let D=XJ2(A[Gr],B);if(A[Gr]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[Zr]!=null)Q[Zr]=A[Zr];if(A[Dr]!=null)Q[Dr]=A[Dr];if(A[S30]!=null){let D=jT4(A[S30],B);if(A[S30]?.length===0)Q.Tags=[];Object.entries(D).forEach(([Z,G])=>{let F=`Tags.${Z}`;Q[F]=G})}if(A[k30]!=null){let D=ST4(A[k30],B);if(A[k30]?.length===0)Q.TransitiveTagKeys=[];Object.entries(D).forEach(([Z,G])=>{let F=`TransitiveTagKeys.${Z}`;Q[F]=G})}if(A[w30]!=null)Q[w30]=A[w30];if(A[T30]!=null)Q[T30]=A[T30];if(A[j30]!=null)Q[j30]=A[j30];if(A[xT]!=null)Q[xT]=A[xT];if(A[N30]!=null){let D=TT4(A[N30],B);if(A[N30]?.length===0)Q.ProvidedContexts=[];Object.entries(D).forEach(([Z,G])=>{let F=`ProvidedContexts.${Z}`;Q[F]=G})}return Q},"se_AssumeRoleRequest"),MT4=Z9((A,B)=>{let Q={};if(A[Ir]!=null)Q[Ir]=A[Ir];if(A[Yr]!=null)Q[Yr]=A[Yr];if(A[_30]!=null)Q[_30]=A[_30];if(A[L30]!=null)Q[L30]=A[L30];if(A[Gr]!=null){let D=XJ2(A[Gr],B);if(A[Gr]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[Zr]!=null)Q[Zr]=A[Zr];if(A[Dr]!=null)Q[Dr]=A[Dr];return Q},"se_AssumeRoleWithWebIdentityRequest"),XJ2=Z9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=RT4(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_policyDescriptorListType"),RT4=Z9((A,B)=>{let Q={};if(A[x30]!=null)Q[x30]=A[x30];return Q},"se_PolicyDescriptorType"),OT4=Z9((A,B)=>{let Q={};if(A[q30]!=null)Q[q30]=A[q30];if(A[E30]!=null)Q[E30]=A[E30];return Q},"se_ProvidedContext"),TT4=Z9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=OT4(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_ProvidedContextsListType"),PT4=Z9((A,B)=>{let Q={};if(A[$30]!=null)Q[$30]=A[$30];if(A[y30]!=null)Q[y30]=A[y30];return Q},"se_Tag"),ST4=Z9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;Q[`member.${D}`]=Z,D++}return Q},"se_tagKeyListType"),jT4=Z9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=PT4(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_tagListType"),VJ2=Z9((A,B)=>{let Q={};if(A[K30]!=null)Q[K30]=V5.expectString(A[K30]);if(A[H30]!=null)Q[H30]=V5.expectString(A[H30]);return Q},"de_AssumedRoleUser"),kT4=Z9((A,B)=>{let Q={};if(A[Qr]!=null)Q[Qr]=CJ2(A[Qr],B);if(A[Br]!=null)Q[Br]=VJ2(A[Br],B);if(A[Fr]!=null)Q[Fr]=V5.strictParseInt32(A[Fr]);if(A[xT]!=null)Q[xT]=V5.expectString(A[xT]);return Q},"de_AssumeRoleResponse"),yT4=Z9((A,B)=>{let Q={};if(A[Qr]!=null)Q[Qr]=CJ2(A[Qr],B);if(A[O30]!=null)Q[O30]=V5.expectString(A[O30]);if(A[Br]!=null)Q[Br]=VJ2(A[Br],B);if(A[Fr]!=null)Q[Fr]=V5.strictParseInt32(A[Fr]);if(A[M30]!=null)Q[M30]=V5.expectString(A[M30]);if(A[z30]!=null)Q[z30]=V5.expectString(A[z30]);if(A[xT]!=null)Q[xT]=V5.expectString(A[xT]);return Q},"de_AssumeRoleWithWebIdentityResponse"),CJ2=Z9((A,B)=>{let Q={};if(A[C30]!=null)Q[C30]=V5.expectString(A[C30]);if(A[R30]!=null)Q[R30]=V5.expectString(A[R30]);if(A[P30]!=null)Q[P30]=V5.expectString(A[P30]);if(A[U30]!=null)Q[U30]=V5.expectNonNull(V5.parseRfc3339DateTimeWithOffset(A[U30]));return Q},"de_Credentials"),_T4=Z9((A,B)=>{let Q={};if(A[nZ]!=null)Q[nZ]=V5.expectString(A[nZ]);return Q},"de_ExpiredTokenException"),xT4=Z9((A,B)=>{let Q={};if(A[nZ]!=null)Q[nZ]=V5.expectString(A[nZ]);return Q},"de_IDPCommunicationErrorException"),vT4=Z9((A,B)=>{let Q={};if(A[nZ]!=null)Q[nZ]=V5.expectString(A[nZ]);return Q},"de_IDPRejectedClaimException"),bT4=Z9((A,B)=>{let Q={};if(A[nZ]!=null)Q[nZ]=V5.expectString(A[nZ]);return Q},"de_InvalidIdentityTokenException"),fT4=Z9((A,B)=>{let Q={};if(A[nZ]!=null)Q[nZ]=V5.expectString(A[nZ]);return Q},"de_MalformedPolicyDocumentException"),hT4=Z9((A,B)=>{let Q={};if(A[nZ]!=null)Q[nZ]=V5.expectString(A[nZ]);return Q},"de_PackedPolicyTooLargeException"),gT4=Z9((A,B)=>{let Q={};if(A[nZ]!=null)Q[nZ]=V5.expectString(A[nZ]);return Q},"de_RegionDisabledException"),bT=Z9((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),uT4=V5.withBaseException(vT),KJ2=Z9(async(A,B,Q,D,Z)=>{let{hostname:G,protocol:F="https",port:I,path:Y}=await A.endpoint(),W={protocol:F,hostname:G,port:I,method:"POST",path:Y.endsWith("/")?Y.slice(0,-1)+Q:Y+Q,headers:B};if(D!==void 0)W.hostname=D;if(Z!==void 0)W.body=Z;return new XT4.HttpRequest(W)},"buildHttpRpcRequest"),HJ2={"content-type":"application/x-www-form-urlencoded"},zJ2="2011-06-15",EJ2="Action",C30="AccessKeyId",mT4="AssumeRole",K30="AssumedRoleId",Br="AssumedRoleUser",dT4="AssumeRoleWithWebIdentity",H30="Arn",z30="Audience",Qr="Credentials",E30="ContextAssertion",Dr="DurationSeconds",U30="Expiration",w30="ExternalId",$30="Key",Zr="Policy",Gr="PolicyArns",q30="ProviderArn",N30="ProvidedContexts",L30="ProviderId",Fr="PackedPolicySize",M30="Provider",Ir="RoleArn",Yr="RoleSessionName",R30="SecretAccessKey",O30="SubjectFromWebIdentityToken",xT="SourceIdentity",T30="SerialNumber",P30="SessionToken",S30="Tags",j30="TokenCode",k30="TransitiveTagKeys",UJ2="Version",y30="Value",_30="WebIdentityToken",x30="arn",nZ="message",wJ2=Z9((A)=>Object.entries(A).map(([B,Q])=>V5.extendedEncodeURIComponent(B)+"="+V5.extendedEncodeURIComponent(Q)).join("&"),"buildFormUrlencodedString"),cT4=Z9((A,B)=>{if(B.Error?.Code!==void 0)return B.Error.Code;if(A.statusCode==404)return"NotFound"},"loadQueryErrorCode"),u30=class extends YT4.Command.classBuilder().ep(WT4.commonParams).m(function(A,B,Q,D){return[IT4.getSerdePlugin(Q,this.serialize,this.deserialize),FT4.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRole",{}).n("STSClient","AssumeRoleCommand").f(void 0,AJ2).ser(VT4).de(KT4).build(){static{Z9(this,"AssumeRoleCommand")}},lT4=q6(),pT4=T3(),iT4=QZ(),nT4=X81(),m30=class extends iT4.Command.classBuilder().ep(nT4.commonParams).m(function(A,B,Q,D){return[pT4.getSerdePlugin(Q,this.serialize,this.deserialize),lT4.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithWebIdentity",{}).n("STSClient","AssumeRoleWithWebIdentityCommand").f(IJ2,YJ2).ser(CT4).de(HT4).build(){static{Z9(this,"AssumeRoleWithWebIdentityCommand")}},aT4=J81(),sT4={AssumeRoleCommand:u30,AssumeRoleWithWebIdentityCommand:m30},$J2=class extends aT4.STSClient{static{Z9(this,"STS")}};GT4.createAggregatedClient(sT4,$J2);var rT4=X81(),b30=HL(),tW2="us-east-1",qJ2=Z9((A)=>{if(typeof A?.Arn==="string"){let B=A.Arn.split(":");if(B.length>4&&B[4]!=="")return B[4]}return},"getAccountIdFromAssumedRoleUser"),NJ2=Z9(async(A,B,Q)=>{let D=typeof A==="function"?await A():A,Z=typeof B==="function"?await B():B;return Q?.debug?.("@aws-sdk/client-sts::resolveRegion","accepting first of:",`${D} (provider)`,`${Z} (parent client)`,`${tW2} (STS default)`),D??Z??tW2},"resolveRegion"),oT4=Z9((A,B)=>{let Q,D;return async(Z,G)=>{if(D=Z,!Q){let{logger:J=A?.parentClientConfig?.logger,region:X,requestHandler:V=A?.parentClientConfig?.requestHandler,credentialProviderLogger:C}=A,K=await NJ2(X,A?.parentClientConfig?.region,C),H=!LJ2(V);Q=new B({profile:A?.parentClientConfig?.profile,credentialDefaultProvider:Z9(()=>async()=>D,"credentialDefaultProvider"),region:K,requestHandler:H?V:void 0,logger:J})}let{Credentials:F,AssumedRoleUser:I}=await Q.send(new u30(G));if(!F||!F.AccessKeyId||!F.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRole call with role ${G.RoleArn}`);let Y=qJ2(I),W={accessKeyId:F.AccessKeyId,secretAccessKey:F.SecretAccessKey,sessionToken:F.SessionToken,expiration:F.Expiration,...F.CredentialScope&&{credentialScope:F.CredentialScope},...Y&&{accountId:Y}};return b30.setCredentialFeature(W,"CREDENTIALS_STS_ASSUME_ROLE","i"),W}},"getDefaultRoleAssumer"),tT4=Z9((A,B)=>{let Q;return async(D)=>{if(!Q){let{logger:Y=A?.parentClientConfig?.logger,region:W,requestHandler:J=A?.parentClientConfig?.requestHandler,credentialProviderLogger:X}=A,V=await NJ2(W,A?.parentClientConfig?.region,X),C=!LJ2(J);Q=new B({profile:A?.parentClientConfig?.profile,region:V,requestHandler:C?J:void 0,logger:Y})}let{Credentials:Z,AssumedRoleUser:G}=await Q.send(new m30(D));if(!Z||!Z.AccessKeyId||!Z.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${D.RoleArn}`);let F=qJ2(G),I={accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.SessionToken,expiration:Z.Expiration,...Z.CredentialScope&&{credentialScope:Z.CredentialScope},...F&&{accountId:F}};if(F)b30.setCredentialFeature(I,"RESOLVED_ACCOUNT_ID","T");return b30.setCredentialFeature(I,"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID","k"),I}},"getDefaultRoleAssumerWithWebIdentity"),LJ2=Z9((A)=>{return A?.metadata?.handlerProtocol==="h2"},"isH2"),MJ2=J81(),RJ2=Z9((A,B)=>{if(!B)return A;else return class Q extends A{static{Z9(this,"CustomizableSTSClient")}constructor(D){super(D);for(let Z of B)this.middlewareStack.use(Z)}}},"getCustomizableStsClientCtor"),OJ2=Z9((A={},B)=>oT4(A,RJ2(MJ2.STSClient,B)),"getDefaultRoleAssumer"),TJ2=Z9((A={},B)=>tT4(A,RJ2(MJ2.STSClient,B)),"getDefaultRoleAssumerWithWebIdentity"),eT4=Z9((A)=>(B)=>A({roleAssumer:OJ2(B),roleAssumerWithWebIdentity:TJ2(B),...B}),"decorateDefaultCredentialProvider")});
var c50=E((vF2)=>{Object.defineProperty(vF2,"__esModule",{value:!0});vF2.resolveHttpAuthSchemeConfig=vF2.defaultSSOOIDCHttpAuthSchemeProvider=vF2.defaultSSOOIDCHttpAuthSchemeParametersProvider=void 0;var eL4=WI(),d50=I5(),AM4=async(A,B,Q)=>{return{operation:d50.getSmithyContext(B).operation,region:await d50.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};vF2.defaultSSOOIDCHttpAuthSchemeParametersProvider=AM4;function BM4(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sso-oauth",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function QM4(A){return{schemeId:"smithy.api#noAuth"}}var DM4=(A)=>{let B=[];switch(A.operation){case"CreateToken":{B.push(QM4(A));break}default:B.push(BM4(A))}return B};vF2.defaultSSOOIDCHttpAuthSchemeProvider=DM4;var ZM4=(A)=>{let B=eL4.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:d50.normalizeProvider(A.authSchemePreference??[])})};vF2.resolveHttpAuthSchemeConfig=ZM4});
var cI2=E((mI2)=>{Object.defineProperty(mI2,"__esModule",{value:!0});mI2.getRuntimeConfig=void 0;var LM4=WI(),MM4=VB(),RM4=QZ(),OM4=BZ(),gI2=s50(),uI2=cB(),TM4=c50(),PM4=hI2(),SM4=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??gI2.fromBase64,base64Encoder:A?.base64Encoder??gI2.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??PM4.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??TM4.defaultSSOOIDCHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new LM4.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new MM4.NoAuthSigner}],logger:A?.logger??new RM4.NoOpLogger,serviceId:A?.serviceId??"SSO OIDC",urlParser:A?.urlParser??OM4.parseUrl,utf8Decoder:A?.utf8Decoder??uI2.fromUtf8,utf8Encoder:A?.utf8Encoder??uI2.toUtf8}};mI2.getRuntimeConfig=SM4});
var e32=E((fK5,t32)=>{var{defineProperty:mN1,getOwnPropertyDescriptor:wz4,getOwnPropertyNames:$z4}=Object,qz4=Object.prototype.hasOwnProperty,Nz4=(A,B)=>mN1(A,"name",{value:B,configurable:!0}),Lz4=(A,B)=>{for(var Q in B)mN1(A,Q,{get:B[Q],enumerable:!0})},Mz4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of $z4(B))if(!qz4.call(A,Z)&&Z!==Q)mN1(A,Z,{get:()=>B[Z],enumerable:!(D=wz4(B,Z))||D.enumerable})}return A},Rz4=(A)=>Mz4(mN1({},"__esModule",{value:!0}),A),o32={};Lz4(o32,{isArrayBuffer:()=>Oz4});t32.exports=Rz4(o32);var Oz4=Nz4((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var e61=E((wH5,TG2)=>{var{defineProperty:EL1,getOwnPropertyDescriptor:Sq4,getOwnPropertyNames:jq4}=Object,kq4=Object.prototype.hasOwnProperty,$L=(A,B)=>EL1(A,"name",{value:B,configurable:!0}),yq4=(A,B)=>{for(var Q in B)EL1(A,Q,{get:B[Q],enumerable:!0})},_q4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of jq4(B))if(!kq4.call(A,Z)&&Z!==Q)EL1(A,Z,{get:()=>B[Z],enumerable:!(D=Sq4(B,Z))||D.enumerable})}return A},xq4=(A)=>_q4(EL1({},"__esModule",{value:!0}),A),LG2={};yq4(LG2,{NODE_REGION_CONFIG_FILE_OPTIONS:()=>hq4,NODE_REGION_CONFIG_OPTIONS:()=>fq4,REGION_ENV_NAME:()=>MG2,REGION_INI_NAME:()=>RG2,getAwsRegionExtensionConfiguration:()=>vq4,resolveAwsRegionExtensionConfiguration:()=>bq4,resolveRegionConfig:()=>gq4});TG2.exports=xq4(LG2);var vq4=$L((A)=>{return{setRegion(B){A.region=B},region(){return A.region}}},"getAwsRegionExtensionConfiguration"),bq4=$L((A)=>{return{region:A.region()}},"resolveAwsRegionExtensionConfiguration"),MG2="AWS_REGION",RG2="region",fq4={environmentVariableSelector:$L((A)=>A[MG2],"environmentVariableSelector"),configFileSelector:$L((A)=>A[RG2],"configFileSelector"),default:$L(()=>{throw new Error("Region is missing")},"default")},hq4={preferredFile:"credentials"},OG2=$L((A)=>typeof A==="string"&&(A.startsWith("fips-")||A.endsWith("-fips")),"isFipsRegion"),NG2=$L((A)=>OG2(A)?["fips-aws-global","aws-fips"].includes(A)?"us-east-1":A.replace(/fips-(dkr-|prod-)?|-fips/,""):A,"getRealRegion"),gq4=$L((A)=>{let{region:B,useFipsEndpoint:Q}=A;if(!B)throw new Error("Region is missing");return Object.assign(A,{region:$L(async()=>{if(typeof B==="string")return NG2(B);let D=await B();return NG2(D)},"region"),useFipsEndpoint:$L(async()=>{let D=typeof B==="string"?B:await B();if(OG2(D))return!0;return typeof Q!=="function"?Promise.resolve(!!Q):Q()},"useFipsEndpoint")})},"resolveRegionConfig")});
var e62=E((o62)=>{Object.defineProperty(o62,"__esModule",{value:!0});o62.checkUrl=void 0;var aX4=eB(),sX4="*************",rX4="*************3",oX4="[fd00:ec2::23]",tX4=(A,B)=>{if(A.protocol==="https:")return;if(A.hostname===sX4||A.hostname===rX4||A.hostname===oX4)return;if(A.hostname.includes("[")){if(A.hostname==="[::1]"||A.hostname==="[0000:0000:0000:0000:0000:0000:0000:0001]")return}else{if(A.hostname==="localhost")return;let Q=A.hostname.split("."),D=(Z)=>{let G=parseInt(Z,10);return 0<=G&&G<=255};if(Q[0]==="127"&&D(Q[1])&&D(Q[2])&&D(Q[3])&&Q.length===4)return}throw new aX4.CredentialsProviderError(`URL not accepted. It must either be HTTPS or match one of the following:
  - loopback CIDR *********/8 or [::1/128]
  - ECS container host *************
  - EKS container host *************3 or [fd00:ec2::23]`,{logger:B})};o62.checkUrl=tX4});
var f61=E((UK5,H52)=>{var{defineProperty:RN1,getOwnPropertyDescriptor:kK4,getOwnPropertyNames:yK4}=Object,_K4=Object.prototype.hasOwnProperty,T80=(A,B)=>RN1(A,"name",{value:B,configurable:!0}),xK4=(A,B)=>{for(var Q in B)RN1(A,Q,{get:B[Q],enumerable:!0})},vK4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of yK4(B))if(!_K4.call(A,Z)&&Z!==Q)RN1(A,Z,{get:()=>B[Z],enumerable:!(D=kK4(B,Z))||D.enumerable})}return A},bK4=(A)=>vK4(RN1({},"__esModule",{value:!0}),A),V52={};xK4(V52,{getLoggerPlugin:()=>fK4,loggerMiddleware:()=>C52,loggerMiddlewareOptions:()=>K52});H52.exports=bK4(V52);var C52=T80(()=>(A,B)=>async(Q)=>{try{let D=await A(Q),{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y,overrideOutputFilterSensitiveLog:W}=I,J=Y??B.inputFilterSensitiveLog,X=W??B.outputFilterSensitiveLog,{$metadata:V,...C}=D.output;return F?.info?.({clientName:Z,commandName:G,input:J(Q.input),output:X(C),metadata:V}),D}catch(D){let{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y}=I,W=Y??B.inputFilterSensitiveLog;throw F?.error?.({clientName:Z,commandName:G,input:W(Q.input),error:D,metadata:D.$metadata}),D}},"loggerMiddleware"),K52={name:"loggerMiddleware",tags:["LOGGER"],step:"initialize",override:!0},fK4=T80((A)=>({applyToStack:T80((B)=>{B.add(C52(),K52)},"applyToStack")}),"getLoggerPlugin")});
var fFB=E((Ns5,bFB)=>{var{defineProperty:rk1,getOwnPropertyDescriptor:Rh6,getOwnPropertyNames:Oh6}=Object,Th6=Object.prototype.hasOwnProperty,Ph6=(A,B)=>rk1(A,"name",{value:B,configurable:!0}),Sh6=(A,B)=>{for(var Q in B)rk1(A,Q,{get:B[Q],enumerable:!0})},jh6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Oh6(B))if(!Th6.call(A,Z)&&Z!==Q)rk1(A,Z,{get:()=>B[Z],enumerable:!(D=Rh6(B,Z))||D.enumerable})}return A},kh6=(A)=>jh6(rk1({},"__esModule",{value:!0}),A),vFB={};Sh6(vFB,{resolveEventStreamSerdeConfig:()=>yh6});bFB.exports=kh6(vFB);var yh6=Ph6((A)=>Object.assign(A,{eventStreamMarshaller:A.eventStreamSerdeProvider(A)}),"resolveEventStreamSerdeConfig")});
var fL1=E((aH5,AY2)=>{var{defineProperty:bL1,getOwnPropertyDescriptor:mM4,getOwnPropertyNames:dM4}=Object,cM4=Object.prototype.hasOwnProperty,D_=(A,B)=>bL1(A,"name",{value:B,configurable:!0}),lM4=(A,B)=>{for(var Q in B)bL1(A,Q,{get:B[Q],enumerable:!0})},pM4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of dM4(B))if(!cM4.call(A,Z)&&Z!==Q)bL1(A,Z,{get:()=>B[Z],enumerable:!(D=mM4(B,Z))||D.enumerable})}return A},iM4=(A)=>pM4(bL1({},"__esModule",{value:!0}),A),rI2={};lM4(rI2,{Field:()=>sM4,Fields:()=>rM4,HttpRequest:()=>oM4,HttpResponse:()=>tM4,IHttpRequest:()=>oI2.HttpRequest,getHttpHandlerExtensionConfiguration:()=>nM4,isValidHostname:()=>eI2,resolveHttpHandlerRuntimeConfig:()=>aM4});AY2.exports=iM4(rI2);var nM4=D_((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),aM4=D_((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),oI2=S50(),sM4=class{static{D_(this,"Field")}constructor({name:A,kind:B=oI2.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},rM4=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{D_(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},oM4=class A{static{D_(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=tI2(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function tI2(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}D_(tI2,"cloneQuery");var tM4=class{static{D_(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function eI2(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}D_(eI2,"isValidHostname")});
var fW2=E((vW2)=>{Object.defineProperty(vW2,"__esModule",{value:!0});vW2.resolveHttpAuthRuntimeConfig=vW2.getHttpAuthExtensionConfiguration=void 0;var mO4=(A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}};vW2.getHttpAuthExtensionConfiguration=mO4;var dO4=(A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}};vW2.resolveHttpAuthRuntimeConfig=dO4});
var g61=E((_K5,r32)=>{var{defineProperty:uN1,getOwnPropertyDescriptor:Iz4,getOwnPropertyNames:Yz4}=Object,Wz4=Object.prototype.hasOwnProperty,ey=(A,B)=>uN1(A,"name",{value:B,configurable:!0}),Jz4=(A,B)=>{for(var Q in B)uN1(A,Q,{get:B[Q],enumerable:!0})},Xz4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Yz4(B))if(!Wz4.call(A,Z)&&Z!==Q)uN1(A,Z,{get:()=>B[Z],enumerable:!(D=Iz4(B,Z))||D.enumerable})}return A},Vz4=(A)=>Xz4(uN1({},"__esModule",{value:!0}),A),i32={};Jz4(i32,{Field:()=>Hz4,Fields:()=>zz4,HttpRequest:()=>Ez4,HttpResponse:()=>Uz4,IHttpRequest:()=>n32.HttpRequest,getHttpHandlerExtensionConfiguration:()=>Cz4,isValidHostname:()=>s32,resolveHttpHandlerRuntimeConfig:()=>Kz4});r32.exports=Vz4(i32);var Cz4=ey((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),Kz4=ey((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),n32=j80(),Hz4=class{static{ey(this,"Field")}constructor({name:A,kind:B=n32.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},zz4=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{ey(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},Ez4=class A{static{ey(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=a32(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function a32(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}ey(a32,"cloneQuery");var Uz4=class{static{ey(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function s32(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}ey(s32,"isValidHostname")});
var g82=E((f82)=>{Object.defineProperty(f82,"__esModule",{value:!0});f82.retryWrapper=void 0;var gC4=(A,B,Q)=>{return async()=>{for(let D=0;D<B;++D)try{return await A()}catch(Z){await new Promise((G)=>setTimeout(G,Q))}return await A()}};f82.retryWrapper=gC4});
var h61=E((MK5,v52)=>{var{defineProperty:jN1,getOwnPropertyDescriptor:IH4,getOwnPropertyNames:YH4}=Object,WH4=Object.prototype.hasOwnProperty,SN1=(A,B)=>jN1(A,"name",{value:B,configurable:!0}),JH4=(A,B)=>{for(var Q in B)jN1(A,Q,{get:B[Q],enumerable:!0})},XH4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of YH4(B))if(!WH4.call(A,Z)&&Z!==Q)jN1(A,Z,{get:()=>B[Z],enumerable:!(D=IH4(B,Z))||D.enumerable})}return A},VH4=(A)=>XH4(jN1({},"__esModule",{value:!0}),A),y52={};JH4(y52,{addRecursionDetectionMiddlewareOptions:()=>x52,getRecursionDetectionPlugin:()=>zH4,recursionDetectionMiddleware:()=>_52});v52.exports=VH4(y52);var CH4=k52(),P80="X-Amzn-Trace-Id",KH4="AWS_LAMBDA_FUNCTION_NAME",HH4="_X_AMZN_TRACE_ID",_52=SN1((A)=>(B)=>async(Q)=>{let{request:D}=Q;if(!CH4.HttpRequest.isInstance(D)||A.runtime!=="node")return B(Q);let Z=Object.keys(D.headers??{}).find((Y)=>Y.toLowerCase()===P80.toLowerCase())??P80;if(D.headers.hasOwnProperty(Z))return B(Q);let G=process.env[KH4],F=process.env[HH4],I=SN1((Y)=>typeof Y==="string"&&Y.length>0,"nonEmptyString");if(I(G)&&I(F))D.headers[P80]=F;return B({...Q,request:D})},"recursionDetectionMiddleware"),x52={step:"build",tags:["RECURSION_DETECTION"],name:"recursionDetectionMiddleware",override:!0,priority:"low"},zH4=SN1((A)=>({applyToStack:SN1((B)=>{B.add(_52(A),x52)},"applyToStack")}),"getRecursionDetectionPlugin")});
var hI2=E((bI2)=>{Object.defineProperty(bI2,"__esModule",{value:!0});bI2.defaultEndpointResolver=void 0;var wM4=Ts(),t50=$7(),$M4=vI2(),qM4=new t50.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),NM4=(A,B={})=>{return qM4.get(A,()=>t50.resolveEndpoint($M4.ruleSet,{endpointParams:A,logger:B.logger}))};bI2.defaultEndpointResolver=NM4;t50.customEndpointFunctions.aws=wM4.awsEndpointFunctions});
var hWB=E((bWB)=>{Object.defineProperty(bWB,"__esModule",{value:!0});bWB.ruleSet=void 0;var _WB="required",$M="fn",qM="argv",Me="ref",LWB=!0,MWB="isSet",bD1="booleanEquals",Le="error",vD1="endpoint",ZX="tree",hH0="PartitionResult",RWB={[_WB]:!1,type:"String"},OWB={[_WB]:!0,default:!1,type:"Boolean"},TWB={[Me]:"Endpoint"},xWB={[$M]:bD1,[qM]:[{[Me]:"UseFIPS"},!0]},vWB={[$M]:bD1,[qM]:[{[Me]:"UseDualStack"},!0]},wM={},PWB={[$M]:"getAttr",[qM]:[{[Me]:hH0},"supportsFIPS"]},SWB={[$M]:bD1,[qM]:[!0,{[$M]:"getAttr",[qM]:[{[Me]:hH0},"supportsDualStack"]}]},jWB=[xWB],kWB=[vWB],yWB=[{[Me]:"Region"}],Fd6={version:"1.0",parameters:{Region:RWB,UseDualStack:OWB,UseFIPS:OWB,Endpoint:RWB},rules:[{conditions:[{[$M]:MWB,[qM]:[TWB]}],rules:[{conditions:jWB,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:Le},{rules:[{conditions:kWB,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:Le},{endpoint:{url:TWB,properties:wM,headers:wM},type:vD1}],type:ZX}],type:ZX},{rules:[{conditions:[{[$M]:MWB,[qM]:yWB}],rules:[{conditions:[{[$M]:"aws.partition",[qM]:yWB,assign:hH0}],rules:[{conditions:[xWB,vWB],rules:[{conditions:[{[$M]:bD1,[qM]:[LWB,PWB]},SWB],rules:[{rules:[{endpoint:{url:"https://bedrock-runtime-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:wM,headers:wM},type:vD1}],type:ZX}],type:ZX},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:Le}],type:ZX},{conditions:jWB,rules:[{conditions:[{[$M]:bD1,[qM]:[PWB,LWB]}],rules:[{rules:[{endpoint:{url:"https://bedrock-runtime-fips.{Region}.{PartitionResult#dnsSuffix}",properties:wM,headers:wM},type:vD1}],type:ZX}],type:ZX},{error:"FIPS is enabled but this partition does not support FIPS",type:Le}],type:ZX},{conditions:kWB,rules:[{conditions:[SWB],rules:[{rules:[{endpoint:{url:"https://bedrock-runtime.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:wM,headers:wM},type:vD1}],type:ZX}],type:ZX},{error:"DualStack is enabled but this partition does not support DualStack",type:Le}],type:ZX},{rules:[{endpoint:{url:"https://bedrock-runtime.{Region}.{PartitionResult#dnsSuffix}",properties:wM,headers:wM},type:vD1}],type:ZX}],type:ZX}],type:ZX},{error:"Invalid Configuration: Missing Region",type:Le}],type:ZX}]};bWB.ruleSet=Fd6});
var hZ2=E((bZ2)=>{Object.defineProperty(bZ2,"__esModule",{value:!0});bZ2.fromBase64=void 0;var r$4=AD(),o$4=/^[A-Za-z0-9+/]*={0,2}$/,t$4=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!o$4.exec(A))throw new TypeError("Invalid base64 string.");let B=r$4.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};bZ2.fromBase64=t$4});
var i30=E((Uz5,jJ2)=>{var{defineProperty:lL1,getOwnPropertyDescriptor:AP4,getOwnPropertyNames:BP4}=Object,QP4=Object.prototype.hasOwnProperty,p30=(A,B)=>lL1(A,"name",{value:B,configurable:!0}),DP4=(A,B)=>{for(var Q in B)lL1(A,Q,{get:B[Q],enumerable:!0})},ZP4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of BP4(B))if(!QP4.call(A,Z)&&Z!==Q)lL1(A,Z,{get:()=>B[Z],enumerable:!(D=AP4(B,Z))||D.enumerable})}return A},GP4=(A)=>ZP4(lL1({},"__esModule",{value:!0}),A),SJ2={};DP4(SJ2,{fromProcess:()=>XP4});jJ2.exports=GP4(SJ2);var PJ2=e5(),l30=eB(),FP4=J1("child_process"),IP4=J1("util"),YP4=HL(),WP4=p30((A,B,Q)=>{if(B.Version!==1)throw Error(`Profile ${A} credential_process did not return Version 1.`);if(B.AccessKeyId===void 0||B.SecretAccessKey===void 0)throw Error(`Profile ${A} credential_process returned invalid credentials.`);if(B.Expiration){let G=new Date;if(new Date(B.Expiration)<G)throw Error(`Profile ${A} credential_process returned expired credentials.`)}let D=B.AccountId;if(!D&&Q?.[A]?.aws_account_id)D=Q[A].aws_account_id;let Z={accessKeyId:B.AccessKeyId,secretAccessKey:B.SecretAccessKey,...B.SessionToken&&{sessionToken:B.SessionToken},...B.Expiration&&{expiration:new Date(B.Expiration)},...B.CredentialScope&&{credentialScope:B.CredentialScope},...D&&{accountId:D}};return YP4.setCredentialFeature(Z,"CREDENTIALS_PROCESS","w"),Z},"getValidatedProcessCredentials"),JP4=p30(async(A,B,Q)=>{let D=B[A];if(B[A]){let Z=D.credential_process;if(Z!==void 0){let G=IP4.promisify(FP4.exec);try{let{stdout:F}=await G(Z),I;try{I=JSON.parse(F.trim())}catch{throw Error(`Profile ${A} credential_process returned invalid JSON.`)}return WP4(A,I,B)}catch(F){throw new l30.CredentialsProviderError(F.message,{logger:Q})}}else throw new l30.CredentialsProviderError(`Profile ${A} did not contain credential_process.`,{logger:Q})}else throw new l30.CredentialsProviderError(`Profile ${A} could not be found in shared credentials file.`,{logger:Q})},"resolveProcessCredentials"),XP4=p30((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-process - fromProcess");let Q=await PJ2.parseKnownFiles(A);return JP4(PJ2.getProfileName({profile:A.profile??B?.profile}),Q,A.logger)},"fromProcess")});
var i50=E((uH5,IM4)=>{IM4.exports={name:"@aws-sdk/nested-clients",version:"3.797.0",description:"Nested clients for AWS SDK packages.",main:"./dist-cjs/index.js",module:"./dist-es/index.js",types:"./dist-types/index.d.ts",scripts:{build:"yarn lint && concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline nested-clients","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo",lint:"node ../../scripts/validation/submodules-linter.js --pkg nested-clients",test:"yarn g:vitest run","test:watch":"yarn g:vitest watch"},engines:{node:">=18.0.0"},author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.796.0","@aws-sdk/middleware-host-header":"3.775.0","@aws-sdk/middleware-logger":"3.775.0","@aws-sdk/middleware-recursion-detection":"3.775.0","@aws-sdk/middleware-user-agent":"3.796.0","@aws-sdk/region-config-resolver":"3.775.0","@aws-sdk/types":"3.775.0","@aws-sdk/util-endpoints":"3.787.0","@aws-sdk/util-user-agent-browser":"3.775.0","@aws-sdk/util-user-agent-node":"3.796.0","@smithy/config-resolver":"^4.1.0","@smithy/core":"^3.2.0","@smithy/fetch-http-handler":"^5.0.2","@smithy/hash-node":"^4.0.2","@smithy/invalid-dependency":"^4.0.2","@smithy/middleware-content-length":"^4.0.2","@smithy/middleware-endpoint":"^4.1.0","@smithy/middleware-retry":"^4.1.0","@smithy/middleware-serde":"^4.0.3","@smithy/middleware-stack":"^4.0.2","@smithy/node-config-provider":"^4.0.2","@smithy/node-http-handler":"^4.0.4","@smithy/protocol-http":"^5.1.0","@smithy/smithy-client":"^4.2.0","@smithy/types":"^4.2.0","@smithy/url-parser":"^4.0.2","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.8","@smithy/util-defaults-mode-node":"^4.0.8","@smithy/util-endpoints":"^3.0.2","@smithy/util-middleware":"^4.0.2","@smithy/util-retry":"^4.0.2","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.2.2"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["./sso-oidc.d.ts","./sso-oidc.js","./sts.d.ts","./sts.js","dist-*/**"],browser:{"./dist-es/submodules/sso-oidc/runtimeConfig":"./dist-es/submodules/sso-oidc/runtimeConfig.browser","./dist-es/submodules/sts/runtimeConfig":"./dist-es/submodules/sts/runtimeConfig.browser"},"react-native":{},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/packages/nested-clients",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"packages/nested-clients"},exports:{"./sso-oidc":{types:"./dist-types/submodules/sso-oidc/index.d.ts",module:"./dist-es/submodules/sso-oidc/index.js",node:"./dist-cjs/submodules/sso-oidc/index.js",import:"./dist-es/submodules/sso-oidc/index.js",require:"./dist-cjs/submodules/sso-oidc/index.js"},"./sts":{types:"./dist-types/submodules/sts/index.d.ts",module:"./dist-es/submodules/sts/index.js",node:"./dist-cjs/submodules/sts/index.js",import:"./dist-es/submodules/sts/index.js",require:"./dist-cjs/submodules/sts/index.js"}}}});
var i61=E((ZH5,YZ2)=>{var{defineProperty:WL1,getOwnPropertyDescriptor:Ew4,getOwnPropertyNames:Uw4}=Object,ww4=Object.prototype.hasOwnProperty,YL1=(A,B)=>WL1(A,"name",{value:B,configurable:!0}),$w4=(A,B)=>{for(var Q in B)WL1(A,Q,{get:B[Q],enumerable:!0})},qw4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Uw4(B))if(!ww4.call(A,Z)&&Z!==Q)WL1(A,Z,{get:()=>B[Z],enumerable:!(D=Ew4(B,Z))||D.enumerable})}return A},Nw4=(A)=>qw4(WL1({},"__esModule",{value:!0}),A),DZ2={};$w4(DZ2,{NODE_APP_ID_CONFIG_OPTIONS:()=>Tw4,UA_APP_ID_ENV_NAME:()=>FZ2,UA_APP_ID_INI_NAME:()=>IZ2,createDefaultUserAgentProvider:()=>GZ2,crtAvailability:()=>ZZ2,defaultUserAgent:()=>Mw4});YZ2.exports=Nw4(DZ2);var QZ2=J1("os"),Y50=J1("process"),ZZ2={isCrtAvailable:!1},Lw4=YL1(()=>{if(ZZ2.isCrtAvailable)return["md/crt-avail"];return null},"isCrtAvailable"),GZ2=YL1(({serviceId:A,clientVersion:B})=>{return async(Q)=>{let D=[["aws-sdk-js",B],["ua","2.1"],[`os/${QZ2.platform()}`,QZ2.release()],["lang/js"],["md/nodejs",`${Y50.versions.node}`]],Z=Lw4();if(Z)D.push(Z);if(A)D.push([`api/${A}`,B]);if(Y50.env.AWS_EXECUTION_ENV)D.push([`exec-env/${Y50.env.AWS_EXECUTION_ENV}`]);let G=await Q?.userAgentAppId?.();return G?[...D,[`app/${G}`]]:[...D]}},"createDefaultUserAgentProvider"),Mw4=GZ2,Rw4=_s(),FZ2="AWS_SDK_UA_APP_ID",IZ2="sdk_ua_app_id",Ow4="sdk-ua-app-id",Tw4={environmentVariableSelector:YL1((A)=>A[FZ2],"environmentVariableSelector"),configFileSelector:YL1((A)=>A[IZ2]??A[Ow4],"configFileSelector"),default:Rw4.DEFAULT_UA_APP_ID}});
var iIB=E((Ts5,pIB)=>{var{defineProperty:Dy1,getOwnPropertyDescriptor:mh6,getOwnPropertyNames:dh6}=Object,ch6=Object.prototype.hasOwnProperty,lh6=(A,B)=>Dy1(A,"name",{value:B,configurable:!0}),ph6=(A,B)=>{for(var Q in B)Dy1(A,Q,{get:B[Q],enumerable:!0})},ih6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of dh6(B))if(!ch6.call(A,Z)&&Z!==Q)Dy1(A,Z,{get:()=>B[Z],enumerable:!(D=mh6(B,Z))||D.enumerable})}return A},nh6=(A)=>ih6(Dy1({},"__esModule",{value:!0}),A),lIB={};ph6(lIB,{isArrayBuffer:()=>ah6});pIB.exports=nh6(lIB);var ah6=lh6((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var iWB=E((lWB)=>{Object.defineProperty(lWB,"__esModule",{value:!0});lWB.getRuntimeConfig=void 0;var Xd6=WI(),Vd6=xD1(),Cd6=BZ(),dWB=NWB(),cWB=cB(),Kd6=XH0(),Hd6=mWB(),zd6=(A)=>{return{apiVersion:"2023-09-30",base64Decoder:A?.base64Decoder??dWB.fromBase64,base64Encoder:A?.base64Encoder??dWB.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??Hd6.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??Kd6.defaultBedrockRuntimeHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new Xd6.AwsSdkSigV4Signer}],logger:A?.logger??new Vd6.NoOpLogger,serviceId:A?.serviceId??"Bedrock Runtime",urlParser:A?.urlParser??Cd6.parseUrl,utf8Decoder:A?.utf8Decoder??cWB.fromUtf8,utf8Encoder:A?.utf8Encoder??cWB.toUtf8}};lWB.getRuntimeConfig=zd6});
var j80=E((yK5,p32)=>{var{defineProperty:hN1,getOwnPropertyDescriptor:rH4,getOwnPropertyNames:oH4}=Object,tH4=Object.prototype.hasOwnProperty,gN1=(A,B)=>hN1(A,"name",{value:B,configurable:!0}),eH4=(A,B)=>{for(var Q in B)hN1(A,Q,{get:B[Q],enumerable:!0})},Az4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of oH4(B))if(!tH4.call(A,Z)&&Z!==Q)hN1(A,Z,{get:()=>B[Z],enumerable:!(D=rH4(B,Z))||D.enumerable})}return A},Bz4=(A)=>Az4(hN1({},"__esModule",{value:!0}),A),f32={};eH4(f32,{AlgorithmId:()=>m32,EndpointURLScheme:()=>u32,FieldPosition:()=>d32,HttpApiKeyAuthLocation:()=>g32,HttpAuthLocation:()=>h32,IniSectionType:()=>c32,RequestHandlerProtocol:()=>l32,SMITHY_CONTEXT_KEY:()=>Fz4,getDefaultClientConfiguration:()=>Zz4,resolveDefaultRuntimeConfig:()=>Gz4});p32.exports=Bz4(f32);var h32=((A)=>{return A.HEADER="header",A.QUERY="query",A})(h32||{}),g32=((A)=>{return A.HEADER="header",A.QUERY="query",A})(g32||{}),u32=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(u32||{}),m32=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(m32||{}),Qz4=gN1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),Dz4=gN1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),Zz4=gN1((A)=>{return Qz4(A)},"getDefaultClientConfiguration"),Gz4=gN1((A)=>{return Dz4(A)},"resolveDefaultRuntimeConfig"),d32=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(d32||{}),Fz4="__smithy_context",c32=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(c32||{}),l32=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(l32||{})});
var k52=E(($K5,j52)=>{var{defineProperty:PN1,getOwnPropertyDescriptor:sK4,getOwnPropertyNames:rK4}=Object,oK4=Object.prototype.hasOwnProperty,oy=(A,B)=>PN1(A,"name",{value:B,configurable:!0}),tK4=(A,B)=>{for(var Q in B)PN1(A,Q,{get:B[Q],enumerable:!0})},eK4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of rK4(B))if(!oK4.call(A,Z)&&Z!==Q)PN1(A,Z,{get:()=>B[Z],enumerable:!(D=sK4(B,Z))||D.enumerable})}return A},AH4=(A)=>eK4(PN1({},"__esModule",{value:!0}),A),O52={};tK4(O52,{Field:()=>DH4,Fields:()=>ZH4,HttpRequest:()=>GH4,HttpResponse:()=>FH4,IHttpRequest:()=>T52.HttpRequest,getHttpHandlerExtensionConfiguration:()=>BH4,isValidHostname:()=>S52,resolveHttpHandlerRuntimeConfig:()=>QH4});j52.exports=AH4(O52);var BH4=oy((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),QH4=oy((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),T52=R52(),DH4=class{static{oy(this,"Field")}constructor({name:A,kind:B=T52.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},ZH4=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{oy(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},GH4=class A{static{oy(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=P52(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function P52(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}oy(P52,"cloneQuery");var FH4=class{static{oy(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function S52(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}oy(S52,"isValidHostname")});
var k72=E((gK5,j72)=>{var{defineProperty:sN1,getOwnPropertyDescriptor:vz4,getOwnPropertyNames:bz4}=Object,fz4=Object.prototype.hasOwnProperty,bY=(A,B)=>sN1(A,"name",{value:B,configurable:!0}),hz4=(A,B)=>{for(var Q in B)sN1(A,Q,{get:B[Q],enumerable:!0})},gz4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of bz4(B))if(!fz4.call(A,Z)&&Z!==Q)sN1(A,Z,{get:()=>B[Z],enumerable:!(D=vz4(B,Z))||D.enumerable})}return A},uz4=(A)=>gz4(sN1({},"__esModule",{value:!0}),A),Y72={};hz4(Y72,{ALGORITHM_IDENTIFIER:()=>cN1,ALGORITHM_IDENTIFIER_V4A:()=>lz4,ALGORITHM_QUERY_PARAM:()=>W72,ALWAYS_UNSIGNABLE_HEADERS:()=>E72,AMZ_DATE_HEADER:()=>g80,AMZ_DATE_QUERY_PARAM:()=>v80,AUTH_HEADER:()=>h80,CREDENTIAL_QUERY_PARAM:()=>J72,DATE_HEADER:()=>C72,EVENT_ALGORITHM_IDENTIFIER:()=>$72,EXPIRES_QUERY_PARAM:()=>V72,GENERATED_HEADERS:()=>K72,HOST_HEADER:()=>dz4,KEY_TYPE_IDENTIFIER:()=>u80,MAX_CACHE_SIZE:()=>N72,MAX_PRESIGNED_TTL:()=>L72,PROXY_HEADER_PATTERN:()=>U72,REGION_SET_PARAM:()=>mz4,SEC_HEADER_PATTERN:()=>w72,SHA256_HEADER:()=>aN1,SIGNATURE_HEADER:()=>H72,SIGNATURE_QUERY_PARAM:()=>b80,SIGNED_HEADERS_QUERY_PARAM:()=>X72,SignatureV4:()=>BE4,SignatureV4Base:()=>S72,TOKEN_HEADER:()=>z72,TOKEN_QUERY_PARAM:()=>f80,UNSIGNABLE_PATTERNS:()=>cz4,UNSIGNED_PAYLOAD:()=>q72,clearCredentialCache:()=>iz4,createScope:()=>pN1,getCanonicalHeaders:()=>y80,getCanonicalQuery:()=>P72,getPayloadHash:()=>iN1,getSigningKey:()=>M72,hasHeader:()=>R72,moveHeadersToQuery:()=>T72,prepareRequest:()=>x80,signatureV4aContainer:()=>QE4});j72.exports=uz4(Y72);var Z72=cB(),W72="X-Amz-Algorithm",J72="X-Amz-Credential",v80="X-Amz-Date",X72="X-Amz-SignedHeaders",V72="X-Amz-Expires",b80="X-Amz-Signature",f80="X-Amz-Security-Token",mz4="X-Amz-Region-Set",h80="authorization",g80=v80.toLowerCase(),C72="date",K72=[h80,g80,C72],H72=b80.toLowerCase(),aN1="x-amz-content-sha256",z72=f80.toLowerCase(),dz4="host",E72={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},U72=/^proxy-/,w72=/^sec-/,cz4=[/^proxy-/i,/^sec-/i],cN1="AWS4-HMAC-SHA256",lz4="AWS4-ECDSA-P256-SHA256",$72="AWS4-HMAC-SHA256-PAYLOAD",q72="UNSIGNED-PAYLOAD",N72=50,u80="aws4_request",L72=604800,A_=Zy(),pz4=cB(),Ss={},lN1=[],pN1=bY((A,B,Q)=>`${A}/${B}/${Q}/${u80}`,"createScope"),M72=bY(async(A,B,Q,D,Z)=>{let G=await G72(A,B.secretAccessKey,B.accessKeyId),F=`${Q}:${D}:${Z}:${A_.toHex(G)}:${B.sessionToken}`;if(F in Ss)return Ss[F];lN1.push(F);while(lN1.length>N72)delete Ss[lN1.shift()];let I=`AWS4${B.secretAccessKey}`;for(let Y of[Q,D,Z,u80])I=await G72(A,I,Y);return Ss[F]=I},"getSigningKey"),iz4=bY(()=>{lN1.length=0,Object.keys(Ss).forEach((A)=>{delete Ss[A]})},"clearCredentialCache"),G72=bY((A,B,Q)=>{let D=new A(B);return D.update(pz4.toUint8Array(Q)),D.digest()},"hmac"),y80=bY(({headers:A},B,Q)=>{let D={};for(let Z of Object.keys(A).sort()){if(A[Z]==null)continue;let G=Z.toLowerCase();if(G in E72||B?.has(G)||U72.test(G)||w72.test(G)){if(!Q||Q&&!Q.has(G))continue}D[G]=A[Z].trim().replace(/\s+/g," ")}return D},"getCanonicalHeaders"),nz4=e32(),az4=cB(),iN1=bY(async({headers:A,body:B},Q)=>{for(let D of Object.keys(A))if(D.toLowerCase()===aN1)return A[D];if(B==null)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";else if(typeof B==="string"||ArrayBuffer.isView(B)||nz4.isArrayBuffer(B)){let D=new Q;return D.update(az4.toUint8Array(B)),A_.toHex(await D.digest())}return q72},"getPayloadHash"),F72=cB(),sz4=class{static{bY(this,"HeaderFormatter")}format(A){let B=[];for(let Z of Object.keys(A)){let G=F72.fromUtf8(Z);B.push(Uint8Array.from([G.byteLength]),G,this.formatHeaderValue(A[Z]))}let Q=new Uint8Array(B.reduce((Z,G)=>Z+G.byteLength,0)),D=0;for(let Z of B)Q.set(Z,D),D+=Z.byteLength;return Q}formatHeaderValue(A){switch(A.type){case"boolean":return Uint8Array.from([A.value?0:1]);case"byte":return Uint8Array.from([2,A.value]);case"short":let B=new DataView(new ArrayBuffer(3));return B.setUint8(0,3),B.setInt16(1,A.value,!1),new Uint8Array(B.buffer);case"integer":let Q=new DataView(new ArrayBuffer(5));return Q.setUint8(0,4),Q.setInt32(1,A.value,!1),new Uint8Array(Q.buffer);case"long":let D=new Uint8Array(9);return D[0]=5,D.set(A.value.bytes,1),D;case"binary":let Z=new DataView(new ArrayBuffer(3+A.value.byteLength));Z.setUint8(0,6),Z.setUint16(1,A.value.byteLength,!1);let G=new Uint8Array(Z.buffer);return G.set(A.value,3),G;case"string":let F=F72.fromUtf8(A.value),I=new DataView(new ArrayBuffer(3+F.byteLength));I.setUint8(0,7),I.setUint16(1,F.byteLength,!1);let Y=new Uint8Array(I.buffer);return Y.set(F,3),Y;case"timestamp":let W=new Uint8Array(9);return W[0]=8,W.set(oz4.fromNumber(A.value.valueOf()).bytes,1),W;case"uuid":if(!rz4.test(A.value))throw new Error(`Invalid UUID received: ${A.value}`);let J=new Uint8Array(17);return J[0]=9,J.set(A_.fromHex(A.value.replace(/\-/g,"")),1),J}}},rz4=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,oz4=class A{constructor(B){if(this.bytes=B,B.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static{bY(this,"Int64")}static fromNumber(B){if(B>9223372036854776000||B<-9223372036854776000)throw new Error(`${B} is too large (or, if negative, too small) to represent as an Int64`);let Q=new Uint8Array(8);for(let D=7,Z=Math.abs(Math.round(B));D>-1&&Z>0;D--,Z/=256)Q[D]=Z;if(B<0)_80(Q);return new A(Q)}valueOf(){let B=this.bytes.slice(0),Q=B[0]&128;if(Q)_80(B);return parseInt(A_.toHex(B),16)*(Q?-1:1)}toString(){return String(this.valueOf())}};function _80(A){for(let B=0;B<8;B++)A[B]^=255;for(let B=7;B>-1;B--)if(A[B]++,A[B]!==0)break}bY(_80,"negate");var R72=bY((A,B)=>{A=A.toLowerCase();for(let Q of Object.keys(B))if(A===Q.toLowerCase())return!0;return!1},"hasHeader"),O72=g61(),T72=bY((A,B={})=>{let{headers:Q,query:D={}}=O72.HttpRequest.clone(A);for(let Z of Object.keys(Q)){let G=Z.toLowerCase();if(G.slice(0,6)==="x-amz-"&&!B.unhoistableHeaders?.has(G)||B.hoistableHeaders?.has(G))D[Z]=Q[Z],delete Q[Z]}return{...A,headers:Q,query:D}},"moveHeadersToQuery"),x80=bY((A)=>{A=O72.HttpRequest.clone(A);for(let B of Object.keys(A.headers))if(K72.indexOf(B.toLowerCase())>-1)delete A.headers[B];return A},"prepareRequest"),I72=I5(),tz4=cB(),nN1=D72(),P72=bY(({query:A={}})=>{let B=[],Q={};for(let D of Object.keys(A)){if(D.toLowerCase()===H72)continue;let Z=nN1.escapeUri(D);B.push(Z);let G=A[D];if(typeof G==="string")Q[Z]=`${Z}=${nN1.escapeUri(G)}`;else if(Array.isArray(G))Q[Z]=G.slice(0).reduce((F,I)=>F.concat([`${Z}=${nN1.escapeUri(I)}`]),[]).sort().join("&")}return B.sort().map((D)=>Q[D]).filter((D)=>D).join("&")},"getCanonicalQuery"),ez4=bY((A)=>AE4(A).toISOString().replace(/\.\d{3}Z$/,"Z"),"iso8601"),AE4=bY((A)=>{if(typeof A==="number")return new Date(A*1000);if(typeof A==="string"){if(Number(A))return new Date(Number(A)*1000);return new Date(A)}return A},"toDate"),S72=class{static{bY(this,"SignatureV4Base")}constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){this.service=D,this.sha256=Z,this.uriEscapePath=G,this.applyChecksum=typeof A==="boolean"?A:!0,this.regionProvider=I72.normalizeProvider(Q),this.credentialProvider=I72.normalizeProvider(B)}createCanonicalRequest(A,B,Q){let D=Object.keys(B).sort();return`${A.method}
${this.getCanonicalPath(A)}
${P72(A)}
${D.map((Z)=>`${Z}:${B[Z]}`).join(`
`)}

${D.join(";")}
${Q}`}async createStringToSign(A,B,Q,D){let Z=new this.sha256;Z.update(tz4.toUint8Array(Q));let G=await Z.digest();return`${D}
${A}
${B}
${A_.toHex(G)}`}getCanonicalPath({path:A}){if(this.uriEscapePath){let B=[];for(let Z of A.split("/")){if(Z?.length===0)continue;if(Z===".")continue;if(Z==="..")B.pop();else B.push(Z)}let Q=`${A?.startsWith("/")?"/":""}${B.join("/")}${B.length>0&&A?.endsWith("/")?"/":""}`;return nN1.escapeUri(Q).replace(/%2F/g,"/")}return A}validateResolvedCredentials(A){if(typeof A!=="object"||typeof A.accessKeyId!=="string"||typeof A.secretAccessKey!=="string")throw new Error("Resolved credential object is not valid")}formatDate(A){let B=ez4(A).replace(/[\-:]/g,"");return{longDate:B,shortDate:B.slice(0,8)}}getCanonicalHeaderList(A){return Object.keys(A).sort().join(";")}},BE4=class extends S72{constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){super({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G});this.headerFormatter=new sz4}static{bY(this,"SignatureV4")}async presign(A,B={}){let{signingDate:Q=new Date,expiresIn:D=3600,unsignableHeaders:Z,unhoistableHeaders:G,signableHeaders:F,hoistableHeaders:I,signingRegion:Y,signingService:W}=B,J=await this.credentialProvider();this.validateResolvedCredentials(J);let X=Y??await this.regionProvider(),{longDate:V,shortDate:C}=this.formatDate(Q);if(D>L72)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");let K=pN1(C,X,W??this.service),H=T72(x80(A),{unhoistableHeaders:G,hoistableHeaders:I});if(J.sessionToken)H.query[f80]=J.sessionToken;H.query[W72]=cN1,H.query[J72]=`${J.accessKeyId}/${K}`,H.query[v80]=V,H.query[V72]=D.toString(10);let z=y80(H,Z,F);return H.query[X72]=this.getCanonicalHeaderList(z),H.query[b80]=await this.getSignature(V,K,this.getSigningKey(J,X,C,W),this.createCanonicalRequest(H,z,await iN1(A,this.sha256))),H}async sign(A,B){if(typeof A==="string")return this.signString(A,B);else if(A.headers&&A.payload)return this.signEvent(A,B);else if(A.message)return this.signMessage(A,B);else return this.signRequest(A,B)}async signEvent({headers:A,payload:B},{signingDate:Q=new Date,priorSignature:D,signingRegion:Z,signingService:G}){let F=Z??await this.regionProvider(),{shortDate:I,longDate:Y}=this.formatDate(Q),W=pN1(I,F,G??this.service),J=await iN1({headers:{},body:B},this.sha256),X=new this.sha256;X.update(A);let V=A_.toHex(await X.digest()),C=[$72,Y,W,D,V,J].join(`
`);return this.signString(C,{signingDate:Q,signingRegion:F,signingService:G})}async signMessage(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}){return this.signEvent({headers:this.headerFormatter.format(A.message.headers),payload:A.message.body},{signingDate:B,signingRegion:Q,signingService:D,priorSignature:A.priorSignature}).then((G)=>{return{message:A.message,signature:G}})}async signString(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}={}){let Z=await this.credentialProvider();this.validateResolvedCredentials(Z);let G=Q??await this.regionProvider(),{shortDate:F}=this.formatDate(B),I=new this.sha256(await this.getSigningKey(Z,G,F,D));return I.update(Z72.toUint8Array(A)),A_.toHex(await I.digest())}async signRequest(A,{signingDate:B=new Date,signableHeaders:Q,unsignableHeaders:D,signingRegion:Z,signingService:G}={}){let F=await this.credentialProvider();this.validateResolvedCredentials(F);let I=Z??await this.regionProvider(),Y=x80(A),{longDate:W,shortDate:J}=this.formatDate(B),X=pN1(J,I,G??this.service);if(Y.headers[g80]=W,F.sessionToken)Y.headers[z72]=F.sessionToken;let V=await iN1(Y,this.sha256);if(!R72(aN1,Y.headers)&&this.applyChecksum)Y.headers[aN1]=V;let C=y80(Y,D,Q),K=await this.getSignature(W,X,this.getSigningKey(F,I,J,G),this.createCanonicalRequest(Y,C,V));return Y.headers[h80]=`${cN1} Credential=${F.accessKeyId}/${X}, SignedHeaders=${this.getCanonicalHeaderList(C)}, Signature=${K}`,Y}async getSignature(A,B,Q,D){let Z=await this.createStringToSign(A,B,D,cN1),G=new this.sha256(await Q);return G.update(Z72.toUint8Array(Z)),A_.toHex(await G.digest())}getSigningKey(A,B,Q,D){return M72(this.sha256,A,Q,B,D||this.service)}},QE4={SignatureV4a:null}});
var l82=E((d82)=>{Object.defineProperty(d82,"__esModule",{value:!0});d82.fromHttp=void 0;var uC4=r62(),mC4=HL(),dC4=S3(),u82=eB(),cC4=uC4.__importDefault(J1("fs/promises")),lC4=e62(),m82=b82(),pC4=g82(),iC4="AWS_CONTAINER_CREDENTIALS_RELATIVE_URI",nC4="http://*************",aC4="AWS_CONTAINER_CREDENTIALS_FULL_URI",sC4="AWS_CONTAINER_AUTHORIZATION_TOKEN_FILE",rC4="AWS_CONTAINER_AUTHORIZATION_TOKEN",oC4=(A={})=>{A.logger?.debug("@aws-sdk/credential-provider-http - fromHttp");let B,Q=A.awsContainerCredentialsRelativeUri??process.env[iC4],D=A.awsContainerCredentialsFullUri??process.env[aC4],Z=A.awsContainerAuthorizationToken??process.env[rC4],G=A.awsContainerAuthorizationTokenFile??process.env[sC4],F=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console.warn:A.logger.warn;if(Q&&D)F("@aws-sdk/credential-provider-http: you have set both awsContainerCredentialsRelativeUri and awsContainerCredentialsFullUri."),F("awsContainerCredentialsFullUri will take precedence.");if(Z&&G)F("@aws-sdk/credential-provider-http: you have set both awsContainerAuthorizationToken and awsContainerAuthorizationTokenFile."),F("awsContainerAuthorizationToken will take precedence.");if(D)B=D;else if(Q)B=`${nC4}${Q}`;else throw new u82.CredentialsProviderError(`No HTTP credential provider host provided.
Set AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI.`,{logger:A.logger});let I=new URL(B);lC4.checkUrl(I,A.logger);let Y=new dC4.NodeHttpHandler({requestTimeout:A.timeout??1000,connectionTimeout:A.timeout??1000});return pC4.retryWrapper(async()=>{let W=m82.createGetRequest(I);if(Z)W.headers.Authorization=Z;else if(G)W.headers.Authorization=(await cC4.default.readFile(G)).toString();try{let J=await Y.handle(W);return m82.getCredentials(J.response).then((X)=>mC4.setCredentialFeature(X,"CREDENTIALS_HTTP","z"))}catch(J){throw new u82.CredentialsProviderError(String(J),{logger:A.logger})}},A.maxRetries??3,A.timeout??1000)};d82.fromHttp=oC4});
var lJ2=E((Nz5,cJ2)=>{var{create:TP4,defineProperty:C81,getOwnPropertyDescriptor:PP4,getOwnPropertyNames:SP4,getPrototypeOf:jP4}=Object,kP4=Object.prototype.hasOwnProperty,hG=(A,B)=>C81(A,"name",{value:B,configurable:!0}),yP4=(A,B)=>{for(var Q in B)C81(A,Q,{get:B[Q],enumerable:!0})},uJ2=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of SP4(B))if(!kP4.call(A,Z)&&Z!==Q)C81(A,Z,{get:()=>B[Z],enumerable:!(D=PP4(B,Z))||D.enumerable})}return A},I_=(A,B,Q)=>(Q=A!=null?TP4(jP4(A)):{},uJ2(B||!A||!A.__esModule?C81(Q,"default",{value:A,enumerable:!0}):Q,A)),_P4=(A)=>uJ2(C81({},"__esModule",{value:!0}),A),mJ2={};yP4(mJ2,{fromIni:()=>pP4});cJ2.exports=_P4(mJ2);var t30=e5(),Y_=HL(),V81=eB(),xP4=hG((A,B,Q)=>{let D={EcsContainer:hG(async(Z)=>{let{fromHttp:G}=await Promise.resolve().then(()=>I_(O80())),{fromContainerMetadata:F}=await Promise.resolve().then(()=>I_($F()));return Q?.debug("@aws-sdk/credential-provider-ini - credential_source is EcsContainer"),async()=>V81.chain(G(Z??{}),F(Z))().then(o30)},"EcsContainer"),Ec2InstanceMetadata:hG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Ec2InstanceMetadata");let{fromInstanceMetadata:G}=await Promise.resolve().then(()=>I_($F()));return async()=>G(Z)().then(o30)},"Ec2InstanceMetadata"),Environment:hG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Environment");let{fromEnv:G}=await Promise.resolve().then(()=>I_(W80()));return async()=>G(Z)().then(o30)},"Environment")};if(A in D)return D[A];else throw new V81.CredentialsProviderError(`Unsupported credential source in profile ${B}. Got ${A}, expected EcsContainer or Ec2InstanceMetadata or Environment.`,{logger:Q})},"resolveCredentialSource"),o30=hG((A)=>Y_.setCredentialFeature(A,"CREDENTIALS_PROFILE_NAMED_PROVIDER","p"),"setNamedProvider"),vP4=hG((A,{profile:B="default",logger:Q}={})=>{return Boolean(A)&&typeof A==="object"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1&&["undefined","string"].indexOf(typeof A.external_id)>-1&&["undefined","string"].indexOf(typeof A.mfa_serial)>-1&&(bP4(A,{profile:B,logger:Q})||fP4(A,{profile:B,logger:Q}))},"isAssumeRoleProfile"),bP4=hG((A,{profile:B,logger:Q})=>{let D=typeof A.source_profile==="string"&&typeof A.credential_source==="undefined";if(D)Q?.debug?.(`    ${B} isAssumeRoleWithSourceProfile source_profile=${A.source_profile}`);return D},"isAssumeRoleWithSourceProfile"),fP4=hG((A,{profile:B,logger:Q})=>{let D=typeof A.credential_source==="string"&&typeof A.source_profile==="undefined";if(D)Q?.debug?.(`    ${B} isCredentialSourceProfile credential_source=${A.credential_source}`);return D},"isCredentialSourceProfile"),hP4=hG(async(A,B,Q,D={})=>{Q.logger?.debug("@aws-sdk/credential-provider-ini - resolveAssumeRoleCredentials (STS)");let Z=B[A],{source_profile:G,region:F}=Z;if(!Q.roleAssumer){let{getDefaultRoleAssumer:Y}=await Promise.resolve().then(()=>I_(c30()));Q.roleAssumer=Y({...Q.clientConfig,credentialProviderLogger:Q.logger,parentClientConfig:{...Q?.parentClientConfig,region:F??Q?.parentClientConfig?.region}},Q.clientPlugins)}if(G&&G in D)throw new V81.CredentialsProviderError(`Detected a cycle attempting to resolve credentials for profile ${t30.getProfileName(Q)}. Profiles visited: `+Object.keys(D).join(", "),{logger:Q.logger});Q.logger?.debug(`@aws-sdk/credential-provider-ini - finding credential resolver using ${G?`source_profile=[${G}]`:`profile=[${A}]`}`);let I=G?dJ2(G,B,Q,{...D,[G]:!0},fJ2(B[G]??{})):(await xP4(Z.credential_source,A,Q.logger)(Q))();if(fJ2(Z))return I.then((Y)=>Y_.setCredentialFeature(Y,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"));else{let Y={RoleArn:Z.role_arn,RoleSessionName:Z.role_session_name||`aws-sdk-js-${Date.now()}`,ExternalId:Z.external_id,DurationSeconds:parseInt(Z.duration_seconds||"3600",10)},{mfa_serial:W}=Z;if(W){if(!Q.mfaCodeProvider)throw new V81.CredentialsProviderError(`Profile ${A} requires multi-factor authentication, but no MFA code callback was provided.`,{logger:Q.logger,tryNextLink:!1});Y.SerialNumber=W,Y.TokenCode=await Q.mfaCodeProvider(W)}let J=await I;return Q.roleAssumer(J,Y).then((X)=>Y_.setCredentialFeature(X,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"))}},"resolveAssumeRoleCredentials"),fJ2=hG((A)=>{return!A.role_arn&&!!A.credential_source},"isCredentialSourceWithoutRoleArn"),gP4=hG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.credential_process==="string","isProcessProfile"),uP4=hG(async(A,B)=>Promise.resolve().then(()=>I_(i30())).then(({fromProcess:Q})=>Q({...A,profile:B})().then((D)=>Y_.setCredentialFeature(D,"CREDENTIALS_PROFILE_PROCESS","v"))),"resolveProcessCredentials"),mP4=hG(async(A,B,Q={})=>{let{fromSSO:D}=await Promise.resolve().then(()=>I_(Z30()));return D({profile:A,logger:Q.logger,parentClientConfig:Q.parentClientConfig,clientConfig:Q.clientConfig})().then((Z)=>{if(B.sso_session)return Y_.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO","r");else return Y_.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO_LEGACY","t")})},"resolveSsoCredentials"),dP4=hG((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),hJ2=hG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.aws_access_key_id==="string"&&typeof A.aws_secret_access_key==="string"&&["undefined","string"].indexOf(typeof A.aws_session_token)>-1&&["undefined","string"].indexOf(typeof A.aws_account_id)>-1,"isStaticCredsProfile"),gJ2=hG(async(A,B)=>{B?.logger?.debug("@aws-sdk/credential-provider-ini - resolveStaticCredentials");let Q={accessKeyId:A.aws_access_key_id,secretAccessKey:A.aws_secret_access_key,sessionToken:A.aws_session_token,...A.aws_credential_scope&&{credentialScope:A.aws_credential_scope},...A.aws_account_id&&{accountId:A.aws_account_id}};return Y_.setCredentialFeature(Q,"CREDENTIALS_PROFILE","n")},"resolveStaticCredentials"),cP4=hG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.web_identity_token_file==="string"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1,"isWebIdentityProfile"),lP4=hG(async(A,B)=>Promise.resolve().then(()=>I_(r30())).then(({fromTokenFile:Q})=>Q({webIdentityTokenFile:A.web_identity_token_file,roleArn:A.role_arn,roleSessionName:A.role_session_name,roleAssumerWithWebIdentity:B.roleAssumerWithWebIdentity,logger:B.logger,parentClientConfig:B.parentClientConfig})().then((D)=>Y_.setCredentialFeature(D,"CREDENTIALS_PROFILE_STS_WEB_ID_TOKEN","q"))),"resolveWebIdentityCredentials"),dJ2=hG(async(A,B,Q,D={},Z=!1)=>{let G=B[A];if(Object.keys(D).length>0&&hJ2(G))return gJ2(G,Q);if(Z||vP4(G,{profile:A,logger:Q.logger}))return hP4(A,B,Q,D);if(hJ2(G))return gJ2(G,Q);if(cP4(G))return lP4(G,Q);if(gP4(G))return uP4(Q,A);if(dP4(G))return await mP4(A,G,Q);throw new V81.CredentialsProviderError(`Could not resolve credentials using profile: [${A}] in configuration/credentials file(s).`,{logger:Q.logger})},"resolveProfileData"),pP4=hG((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/credential-provider-ini - fromIni");let D=await t30.parseKnownFiles(Q);return dJ2(t30.getProfileName({profile:A.profile??B?.profile}),D,Q)},"fromIni")});
var lW2=E((dW2)=>{Object.defineProperty(dW2,"__esModule",{value:!0});dW2.resolveRuntimeExtensions=void 0;var hW2=e61(),gW2=fL1(),uW2=QZ(),mW2=fW2(),lO4=(A,B)=>{let Q=Object.assign(hW2.getAwsRegionExtensionConfiguration(A),uW2.getDefaultExtensionConfiguration(A),gW2.getHttpHandlerExtensionConfiguration(A),mW2.getHttpAuthExtensionConfiguration(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,hW2.resolveAwsRegionExtensionConfiguration(Q),uW2.resolveDefaultRuntimeConfig(Q),gW2.resolveHttpHandlerRuntimeConfig(Q),mW2.resolveHttpAuthRuntimeConfig(Q))};dW2.resolveRuntimeExtensions=lO4});
var lZ2=E((KH5,HL1)=>{var{defineProperty:dZ2,getOwnPropertyDescriptor:Qq4,getOwnPropertyNames:Dq4}=Object,Zq4=Object.prototype.hasOwnProperty,N50=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Dq4(B))if(!Zq4.call(A,Z)&&Z!==Q)dZ2(A,Z,{get:()=>B[Z],enumerable:!(D=Qq4(B,Z))||D.enumerable})}return A},cZ2=(A,B,Q)=>(N50(A,B,"default"),Q&&N50(Q,B,"default")),Gq4=(A)=>N50(dZ2({},"__esModule",{value:!0}),A),L50={};HL1.exports=Gq4(L50);cZ2(L50,hZ2(),HL1.exports);cZ2(L50,mZ2(),HL1.exports)});
var m72=E((cK5,u72)=>{var{defineProperty:rN1,getOwnPropertyDescriptor:DE4,getOwnPropertyNames:ZE4}=Object,GE4=Object.prototype.hasOwnProperty,fY=(A,B)=>rN1(A,"name",{value:B,configurable:!0}),FE4=(A,B)=>{for(var Q in B)rN1(A,Q,{get:B[Q],enumerable:!0})},IE4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ZE4(B))if(!GE4.call(A,Z)&&Z!==Q)rN1(A,Z,{get:()=>B[Z],enumerable:!(D=DE4(B,Z))||D.enumerable})}return A},YE4=(A)=>IE4(rN1({},"__esModule",{value:!0}),A),b72={};FE4(b72,{AWSSDKSigV4Signer:()=>VE4,AwsSdkSigV4ASigner:()=>KE4,AwsSdkSigV4Signer:()=>c80,NODE_SIGV4A_CONFIG_OPTIONS:()=>EE4,resolveAWSSDKSigV4Config:()=>wE4,resolveAwsSdkSigV4AConfig:()=>zE4,resolveAwsSdkSigV4Config:()=>f72,validateSigningProperties:()=>d80});u72.exports=YE4(b72);var WE4=g61(),JE4=g61(),y72=fY((A)=>JE4.HttpResponse.isInstance(A)?A.headers?.date??A.headers?.Date:void 0,"getDateHeader"),m80=fY((A)=>new Date(Date.now()+A),"getSkewCorrectedDate"),XE4=fY((A,B)=>Math.abs(m80(B).getTime()-A)>=300000,"isClockSkewed"),_72=fY((A,B)=>{let Q=Date.parse(A);if(XE4(Q,B))return Q-Date.now();return B},"getUpdatedSystemClockOffset"),u61=fY((A,B)=>{if(!B)throw new Error(`Property \`${A}\` is not resolved for AWS SDK SigV4Auth`);return B},"throwSigningPropertyError"),d80=fY(async(A)=>{let B=u61("context",A.context),Q=u61("config",A.config),D=B.endpointV2?.properties?.authSchemes?.[0],G=await u61("signer",Q.signer)(D),F=A?.signingRegion,I=A?.signingRegionSet,Y=A?.signingName;return{config:Q,signer:G,signingRegion:F,signingRegionSet:I,signingName:Y}},"validateSigningProperties"),c80=class{static{fY(this,"AwsSdkSigV4Signer")}async sign(A,B,Q){if(!WE4.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let D=await d80(Q),{config:Z,signer:G}=D,{signingRegion:F,signingName:I}=D,Y=Q.context;if(Y?.authSchemes?.length??0>1){let[J,X]=Y.authSchemes;if(J?.name==="sigv4a"&&X?.name==="sigv4")F=X?.signingRegion??F,I=X?.signingName??I}return await G.sign(A,{signingDate:m80(Z.systemClockOffset),signingRegion:F,signingService:I})}errorHandler(A){return(B)=>{let Q=B.ServerTime??y72(B.$response);if(Q){let D=u61("config",A.config),Z=D.systemClockOffset;if(D.systemClockOffset=_72(Q,D.systemClockOffset),D.systemClockOffset!==Z&&B.$metadata)B.$metadata.clockSkewCorrected=!0}throw B}}successHandler(A,B){let Q=y72(A);if(Q){let D=u61("config",B.config);D.systemClockOffset=_72(Q,D.systemClockOffset)}}},VE4=c80,CE4=g61(),KE4=class extends c80{static{fY(this,"AwsSdkSigV4ASigner")}async sign(A,B,Q){if(!CE4.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let{config:D,signer:Z,signingRegion:G,signingRegionSet:F,signingName:I}=await d80(Q),W=(await D.sigv4aSigningRegionSet?.()??F??[G]).join(",");return await Z.sign(A,{signingDate:m80(D.systemClockOffset),signingRegion:W,signingService:I})}},HE4=VB(),x72=eB(),zE4=fY((A)=>{return A.sigv4aSigningRegionSet=HE4.normalizeProvider(A.sigv4aSigningRegionSet),A},"resolveAwsSdkSigV4AConfig"),EE4={environmentVariableSelector(A){if(A.AWS_SIGV4A_SIGNING_REGION_SET)return A.AWS_SIGV4A_SIGNING_REGION_SET.split(",").map((B)=>B.trim());throw new x72.ProviderError("AWS_SIGV4A_SIGNING_REGION_SET not set in env.",{tryNextLink:!0})},configFileSelector(A){if(A.sigv4a_signing_region_set)return(A.sigv4a_signing_region_set??"").split(",").map((B)=>B.trim());throw new x72.ProviderError("sigv4a_signing_region_set not set in profile.",{tryNextLink:!0})},default:void 0},UE4=HL(),Og=VB(),v72=k72(),f72=fY((A)=>{let B=A.credentials,Q=!!A.credentials,D=void 0;Object.defineProperty(A,"credentials",{set(W){if(W&&W!==B&&W!==D)Q=!0;B=W;let J=h72(A,{credentials:B,credentialDefaultProvider:A.credentialDefaultProvider}),X=g72(A,J);if(Q&&!X.attributed)D=fY(async(V)=>X(V).then((C)=>UE4.setCredentialFeature(C,"CREDENTIALS_CODE","e")),"resolvedCredentials"),D.memoized=X.memoized,D.configBound=X.configBound,D.attributed=!0;else D=X},get(){return D},enumerable:!0,configurable:!0}),A.credentials=B;let{signingEscapePath:Z=!0,systemClockOffset:G=A.systemClockOffset||0,sha256:F}=A,I;if(A.signer)I=Og.normalizeProvider(A.signer);else if(A.regionInfoProvider)I=fY(()=>Og.normalizeProvider(A.region)().then(async(W)=>[await A.regionInfoProvider(W,{useFipsEndpoint:await A.useFipsEndpoint(),useDualstackEndpoint:await A.useDualstackEndpoint()})||{},W]).then(([W,J])=>{let{signingRegion:X,signingService:V}=W;A.signingRegion=A.signingRegion||X||J,A.signingName=A.signingName||V||A.serviceId;let C={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||v72.SignatureV4)(C)}),"signer");else I=fY(async(W)=>{W=Object.assign({},{name:"sigv4",signingName:A.signingName||A.defaultSigningName,signingRegion:await Og.normalizeProvider(A.region)(),properties:{}},W);let{signingRegion:J,signingName:X}=W;A.signingRegion=A.signingRegion||J,A.signingName=A.signingName||X||A.serviceId;let V={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||v72.SignatureV4)(V)},"signer");return Object.assign(A,{systemClockOffset:G,signingEscapePath:Z,signer:I})},"resolveAwsSdkSigV4Config"),wE4=f72;function h72(A,{credentials:B,credentialDefaultProvider:Q}){let D;if(B)if(!B?.memoized)D=Og.memoizeIdentityProvider(B,Og.isIdentityExpired,Og.doesIdentityRequireRefresh);else D=B;else if(Q)D=Og.normalizeProvider(Q(Object.assign({},A,{parentClientConfig:A})));else D=fY(async()=>{throw new Error("@aws-sdk/core::resolveAwsSdkSigV4Config - `credentials` not provided and no credentialDefaultProvider was configured.")},"credentialsProvider");return D.memoized=!0,D}fY(h72,"normalizeCredentialProvider");function g72(A,B){if(B.configBound)return B;let Q=fY(async(D)=>B({...D,callerClientConfig:A}),"fn");return Q.memoized=B.memoized,Q.configBound=!0,Q}fY(g72,"bindCallerConfig")});
var mWB=E((gWB)=>{Object.defineProperty(gWB,"__esModule",{value:!0});gWB.defaultEndpointResolver=void 0;var Id6=Ts(),gH0=$7(),Yd6=hWB(),Wd6=new gH0.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),Jd6=(A,B={})=>{return Wd6.get(A,()=>gH0.resolveEndpoint(Yd6.ruleSet,{endpointParams:A,logger:B.logger}))};gWB.defaultEndpointResolver=Jd6;gH0.customEndpointFunctions.aws=Id6.awsEndpointFunctions});
var mZ2=E((gZ2)=>{Object.defineProperty(gZ2,"__esModule",{value:!0});gZ2.toBase64=void 0;var e$4=AD(),Aq4=cB(),Bq4=(A)=>{let B;if(typeof A==="string")B=Aq4.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return e$4.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};gZ2.toBase64=Bq4});
var n30=E((LL)=>{var VP4=LL&&LL.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),CP4=LL&&LL.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),KP4=LL&&LL.__importStar||function(A){if(A&&A.__esModule)return A;var B={};if(A!=null){for(var Q in A)if(Q!=="default"&&Object.prototype.hasOwnProperty.call(A,Q))VP4(B,A,Q)}return CP4(B,A),B};Object.defineProperty(LL,"__esModule",{value:!0});LL.fromWebToken=void 0;var HP4=(A)=>async(B)=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromWebToken");let{roleArn:Q,roleSessionName:D,webIdentityToken:Z,providerId:G,policyArns:F,policy:I,durationSeconds:Y}=A,{roleAssumerWithWebIdentity:W}=A;if(!W){let{getDefaultRoleAssumerWithWebIdentity:J}=await Promise.resolve().then(()=>KP4(c30()));W=J({...A.clientConfig,credentialProviderLogger:A.logger,parentClientConfig:{...B?.callerClientConfig,...A.parentClientConfig}},A.clientPlugins)}return W({RoleArn:Q,RoleSessionName:D??`aws-sdk-js-session-${Date.now()}`,WebIdentityToken:Z,ProviderId:G,PolicyArns:F,Policy:I,DurationSeconds:Y})};LL.fromWebToken=HP4});
var o61=E((FH5,vZ2)=>{var{defineProperty:KL1,getOwnPropertyDescriptor:gw4,getOwnPropertyNames:uw4}=Object,mw4=Object.prototype.hasOwnProperty,S2=(A,B)=>KL1(A,"name",{value:B,configurable:!0}),dw4=(A,B)=>{for(var Q in B)KL1(A,Q,{get:B[Q],enumerable:!0})},cw4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of uw4(B))if(!mw4.call(A,Z)&&Z!==Q)KL1(A,Z,{get:()=>B[Z],enumerable:!(D=gw4(B,Z))||D.enumerable})}return A},lw4=(A)=>cw4(KL1({},"__esModule",{value:!0}),A),wZ2={};dw4(wZ2,{Client:()=>pw4,Command:()=>qZ2,LazyJsonString:()=>Pg,NoOpLogger:()=>m$4,SENSITIVE_STRING:()=>nw4,ServiceException:()=>P$4,_json:()=>z50,collectBody:()=>J50.collectBody,convertMap:()=>d$4,createAggregatedClient:()=>aw4,dateToUtcString:()=>TZ2,decorateServiceException:()=>PZ2,emitWarningIfUnsupportedVersion:()=>y$4,expectBoolean:()=>rw4,expectByte:()=>H50,expectFloat32:()=>VL1,expectInt:()=>tw4,expectInt32:()=>C50,expectLong:()=>s61,expectNonNull:()=>A$4,expectNumber:()=>a61,expectObject:()=>NZ2,expectShort:()=>K50,expectString:()=>B$4,expectUnion:()=>Q$4,extendedEncodeURIComponent:()=>J50.extendedEncodeURIComponent,getArrayIfSingleItem:()=>g$4,getDefaultClientConfiguration:()=>f$4,getDefaultExtensionConfiguration:()=>jZ2,getValueFromTextNode:()=>kZ2,handleFloat:()=>G$4,isSerializableHeaderValue:()=>u$4,limitedParseDouble:()=>w50,limitedParseFloat:()=>F$4,limitedParseFloat32:()=>I$4,loadConfigsForDefaultMode:()=>k$4,logger:()=>r61,map:()=>q50,parseBoolean:()=>sw4,parseEpochTimestamp:()=>w$4,parseRfc3339DateTime:()=>V$4,parseRfc3339DateTimeWithOffset:()=>K$4,parseRfc7231DateTime:()=>U$4,quoteHeader:()=>_Z2,resolveDefaultRuntimeConfig:()=>h$4,resolvedPath:()=>J50.resolvedPath,serializeDateTime:()=>a$4,serializeFloat:()=>n$4,splitEvery:()=>xZ2,splitHeader:()=>s$4,strictParseByte:()=>OZ2,strictParseDouble:()=>U50,strictParseFloat:()=>D$4,strictParseFloat32:()=>LZ2,strictParseInt:()=>Y$4,strictParseInt32:()=>W$4,strictParseLong:()=>RZ2,strictParseShort:()=>vs,take:()=>c$4,throwDefaultError:()=>SZ2,withBaseException:()=>S$4});vZ2.exports=lw4(wZ2);var $Z2=Uw(),pw4=class{constructor(A){this.config=A,this.middlewareStack=$Z2.constructStack()}static{S2(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},J50=$6(),V50=W50(),qZ2=class{constructor(){this.middlewareStack=$Z2.constructStack()}static{S2(this,"Command")}static classBuilder(){return new iw4}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[V50.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},iw4=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{S2(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}build(){let A=this,B;return B=class extends qZ2{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this)}static{S2(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},nw4="***SensitiveInformation***",aw4=S2((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=S2(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),sw4=S2((A)=>{switch(A){case"true":return!0;case"false":return!1;default:throw new Error(`Unable to parse boolean value "${A}"`)}},"parseBoolean"),rw4=S2((A)=>{if(A===null||A===void 0)return;if(typeof A==="number"){if(A===0||A===1)r61.warn(CL1(`Expected boolean, got ${typeof A}: ${A}`));if(A===0)return!1;if(A===1)return!0}if(typeof A==="string"){let B=A.toLowerCase();if(B==="false"||B==="true")r61.warn(CL1(`Expected boolean, got ${typeof A}: ${A}`));if(B==="false")return!1;if(B==="true")return!0}if(typeof A==="boolean")return A;throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)},"expectBoolean"),a61=S2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string"){let B=parseFloat(A);if(!Number.isNaN(B)){if(String(B)!==String(A))r61.warn(CL1(`Expected number but observed string: ${A}`));return B}}if(typeof A==="number")return A;throw new TypeError(`Expected number, got ${typeof A}: ${A}`)},"expectNumber"),ow4=Math.ceil(340282346638528860000000000000000000000),VL1=S2((A)=>{let B=a61(A);if(B!==void 0&&!Number.isNaN(B)&&B!==1/0&&B!==-1/0){if(Math.abs(B)>ow4)throw new TypeError(`Expected 32-bit float, got ${A}`)}return B},"expectFloat32"),s61=S2((A)=>{if(A===null||A===void 0)return;if(Number.isInteger(A)&&!Number.isNaN(A))return A;throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)},"expectLong"),tw4=s61,C50=S2((A)=>E50(A,32),"expectInt32"),K50=S2((A)=>E50(A,16),"expectShort"),H50=S2((A)=>E50(A,8),"expectByte"),E50=S2((A,B)=>{let Q=s61(A);if(Q!==void 0&&ew4(Q,B)!==Q)throw new TypeError(`Expected ${B}-bit integer, got ${A}`);return Q},"expectSizedInt"),ew4=S2((A,B)=>{switch(B){case 32:return Int32Array.of(A)[0];case 16:return Int16Array.of(A)[0];case 8:return Int8Array.of(A)[0]}},"castInt"),A$4=S2((A,B)=>{if(A===null||A===void 0){if(B)throw new TypeError(`Expected a non-null value for ${B}`);throw new TypeError("Expected a non-null value")}return A},"expectNonNull"),NZ2=S2((A)=>{if(A===null||A===void 0)return;if(typeof A==="object"&&!Array.isArray(A))return A;let B=Array.isArray(A)?"array":typeof A;throw new TypeError(`Expected object, got ${B}: ${A}`)},"expectObject"),B$4=S2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string")return A;if(["boolean","number","bigint"].includes(typeof A))return r61.warn(CL1(`Expected string, got ${typeof A}: ${A}`)),String(A);throw new TypeError(`Expected string, got ${typeof A}: ${A}`)},"expectString"),Q$4=S2((A)=>{if(A===null||A===void 0)return;let B=NZ2(A),Q=Object.entries(B).filter(([,D])=>D!=null).map(([D])=>D);if(Q.length===0)throw new TypeError("Unions must have exactly one non-null member. None were found.");if(Q.length>1)throw new TypeError(`Unions must have exactly one non-null member. Keys ${Q} were not null.`);return B},"expectUnion"),U50=S2((A)=>{if(typeof A=="string")return a61(hs(A));return a61(A)},"strictParseDouble"),D$4=U50,LZ2=S2((A)=>{if(typeof A=="string")return VL1(hs(A));return VL1(A)},"strictParseFloat32"),Z$4=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,hs=S2((A)=>{let B=A.match(Z$4);if(B===null||B[0].length!==A.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(A)},"parseNumber"),w50=S2((A)=>{if(typeof A=="string")return MZ2(A);return a61(A)},"limitedParseDouble"),G$4=w50,F$4=w50,I$4=S2((A)=>{if(typeof A=="string")return MZ2(A);return VL1(A)},"limitedParseFloat32"),MZ2=S2((A)=>{switch(A){case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error(`Unable to parse float value: ${A}`)}},"parseFloatString"),RZ2=S2((A)=>{if(typeof A==="string")return s61(hs(A));return s61(A)},"strictParseLong"),Y$4=RZ2,W$4=S2((A)=>{if(typeof A==="string")return C50(hs(A));return C50(A)},"strictParseInt32"),vs=S2((A)=>{if(typeof A==="string")return K50(hs(A));return K50(A)},"strictParseShort"),OZ2=S2((A)=>{if(typeof A==="string")return H50(hs(A));return H50(A)},"strictParseByte"),CL1=S2((A)=>{return String(new TypeError(A).stack||A).split(`
`).slice(0,5).filter((B)=>!B.includes("stackTraceWarning")).join(`
`)},"stackTraceWarning"),r61={warn:console.warn},J$4=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],$50=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function TZ2(A){let B=A.getUTCFullYear(),Q=A.getUTCMonth(),D=A.getUTCDay(),Z=A.getUTCDate(),G=A.getUTCHours(),F=A.getUTCMinutes(),I=A.getUTCSeconds(),Y=Z<10?`0${Z}`:`${Z}`,W=G<10?`0${G}`:`${G}`,J=F<10?`0${F}`:`${F}`,X=I<10?`0${I}`:`${I}`;return`${J$4[D]}, ${Y} ${$50[Q]} ${B} ${W}:${J}:${X} GMT`}S2(TZ2,"dateToUtcString");var X$4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),V$4=S2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=X$4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W]=B,J=vs(bs(D)),X=wL(Z,"month",1,12),V=wL(G,"day",1,31);return n61(J,X,V,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})},"parseRfc3339DateTime"),C$4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),K$4=S2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=C$4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W,J]=B,X=vs(bs(D)),V=wL(Z,"month",1,12),C=wL(G,"day",1,31),K=n61(X,V,C,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W});if(J.toUpperCase()!="Z")K.setTime(K.getTime()-T$4(J));return K},"parseRfc3339DateTimeWithOffset"),H$4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),z$4=new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),E$4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/),U$4=S2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-7231 date-times must be expressed as strings");let B=H$4.exec(A);if(B){let[Q,D,Z,G,F,I,Y,W]=B;return n61(vs(bs(G)),X50(Z),wL(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})}if(B=z$4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return N$4(n61($$4(G),X50(Z),wL(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W}))}if(B=E$4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return n61(vs(bs(W)),X50(D),wL(Z.trimLeft(),"day",1,31),{hours:G,minutes:F,seconds:I,fractionalMilliseconds:Y})}throw new TypeError("Invalid RFC-7231 date-time value")},"parseRfc7231DateTime"),w$4=S2((A)=>{if(A===null||A===void 0)return;let B;if(typeof A==="number")B=A;else if(typeof A==="string")B=U50(A);else if(typeof A==="object"&&A.tag===1)B=A.value;else throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(B)||B===1/0||B===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(B*1000))},"parseEpochTimestamp"),n61=S2((A,B,Q,D)=>{let Z=B-1;return M$4(A,Z,Q),new Date(Date.UTC(A,Z,Q,wL(D.hours,"hour",0,23),wL(D.minutes,"minute",0,59),wL(D.seconds,"seconds",0,60),O$4(D.fractionalMilliseconds)))},"buildDate"),$$4=S2((A)=>{let B=new Date().getUTCFullYear(),Q=Math.floor(B/100)*100+vs(bs(A));if(Q<B)return Q+100;return Q},"parseTwoDigitYear"),q$4=1576800000000,N$4=S2((A)=>{if(A.getTime()-new Date().getTime()>q$4)return new Date(Date.UTC(A.getUTCFullYear()-100,A.getUTCMonth(),A.getUTCDate(),A.getUTCHours(),A.getUTCMinutes(),A.getUTCSeconds(),A.getUTCMilliseconds()));return A},"adjustRfc850Year"),X50=S2((A)=>{let B=$50.indexOf(A);if(B<0)throw new TypeError(`Invalid month: ${A}`);return B+1},"parseMonthByShortName"),L$4=[31,28,31,30,31,30,31,31,30,31,30,31],M$4=S2((A,B,Q)=>{let D=L$4[B];if(B===1&&R$4(A))D=29;if(Q>D)throw new TypeError(`Invalid day for ${$50[B]} in ${A}: ${Q}`)},"validateDayOfMonth"),R$4=S2((A)=>{return A%4===0&&(A%100!==0||A%400===0)},"isLeapYear"),wL=S2((A,B,Q,D)=>{let Z=OZ2(bs(A));if(Z<Q||Z>D)throw new TypeError(`${B} must be between ${Q} and ${D}, inclusive`);return Z},"parseDateValue"),O$4=S2((A)=>{if(A===null||A===void 0)return 0;return LZ2("0."+A)*1000},"parseMilliseconds"),T$4=S2((A)=>{let B=A[0],Q=1;if(B=="+")Q=1;else if(B=="-")Q=-1;else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);let D=Number(A.substring(1,3)),Z=Number(A.substring(4,6));return Q*(D*60+Z)*60*1000},"parseOffsetToMilliseconds"),bs=S2((A)=>{let B=0;while(B<A.length-1&&A.charAt(B)==="0")B++;if(B===0)return A;return A.slice(B)},"stripLeadingZeroes"),P$4=class A extends Error{static{S2(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},PZ2=S2((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),SZ2=S2(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=j$4(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw PZ2(F,B)},"throwDefaultError"),S$4=S2((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{SZ2({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),j$4=S2((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),k$4=S2((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),UZ2=!1,y$4=S2((A)=>{if(A&&!UZ2&&parseInt(A.substring(1,A.indexOf(".")))<16)UZ2=!0},"emitWarningIfUnsupportedVersion"),_$4=S2((A)=>{let B=[];for(let Q in V50.AlgorithmId){let D=V50.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),x$4=S2((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),v$4=S2((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),b$4=S2((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),jZ2=S2((A)=>{return Object.assign(_$4(A),v$4(A))},"getDefaultExtensionConfiguration"),f$4=jZ2,h$4=S2((A)=>{return Object.assign(x$4(A),b$4(A))},"resolveDefaultRuntimeConfig"),g$4=S2((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),kZ2=S2((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=kZ2(A[Q]);return A},"getValueFromTextNode"),u$4=S2((A)=>{return A!=null},"isSerializableHeaderValue"),Pg=S2(function A(B){return Object.assign(new String(B),{deserializeJSON(){return JSON.parse(String(B))},toString(){return String(B)},toJSON(){return String(B)}})},"LazyJsonString");Pg.from=(A)=>{if(A&&typeof A==="object"&&(A instanceof Pg||("deserializeJSON"in A)))return A;else if(typeof A==="string"||Object.getPrototypeOf(A)===String.prototype)return Pg(String(A));return Pg(JSON.stringify(A))};Pg.fromObject=Pg.from;var m$4=class{static{S2(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function q50(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,l$4(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}yZ2(D,null,G,F)}return D}S2(q50,"map");var d$4=S2((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),c$4=S2((A,B)=>{let Q={};for(let D in B)yZ2(Q,A,B,D);return Q},"take"),l$4=S2((A,B,Q)=>{return q50(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),yZ2=S2((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=p$4,Y=i$4,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),p$4=S2((A)=>A!=null,"nonNullish"),i$4=S2((A)=>A,"pass");function _Z2(A){if(A.includes(",")||A.includes('"'))A=`"${A.replace(/"/g,"\\\"")}"`;return A}S2(_Z2,"quoteHeader");var n$4=S2((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),a$4=S2((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),z50=S2((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(z50);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=z50(A[Q])}return B}return A},"_json");function xZ2(A,B,Q){if(Q<=0||!Number.isInteger(Q))throw new Error("Invalid number of delimiters ("+Q+") for splitEvery.");let D=A.split(B);if(Q===1)return D;let Z=[],G="";for(let F=0;F<D.length;F++){if(G==="")G=D[F];else G+=B+D[F];if((F+1)%Q===0)Z.push(G),G=""}if(G!=="")Z.push(G);return Z}S2(xZ2,"splitEvery");var s$4=S2((A)=>{let B=A.length,Q=[],D=!1,Z=void 0,G=0;for(let F=0;F<B;++F){let I=A[F];switch(I){case'"':if(Z!=="\\")D=!D;break;case",":if(!D)Q.push(A.slice(G,F)),G=F+1;break;default:}Z=I}return Q.push(A.slice(G)),Q.map((F)=>{F=F.trim();let I=F.length;if(I<2)return F;if(F[0]==='"'&&F[I-1]==='"')F=F.slice(1,I-1);return F.replace(/\\"/g,'"')})},"splitHeader")});
var p50=E((gH5,_L1)=>{var fF2,hF2,gF2,uF2,mF2,dF2,cF2,lF2,pF2,iF2,nF2,aF2,sF2,kL1,l50,rF2,oF2,tF2,ns,eF2,AI2,BI2,QI2,DI2,ZI2,GI2,FI2,II2,yL1,YI2,WI2,JI2;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof _L1==="object"&&typeof gH5==="object")A(Q(B,Q(gH5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};fF2=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},hF2=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},gF2=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},uF2=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},mF2=function(G,F){return function(I,Y){F(I,Y,G)}},dF2=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},cF2=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},lF2=function(G){return typeof G==="symbol"?G:"".concat(G)},pF2=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},iF2=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},nF2=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},aF2=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},sF2=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))yL1(F,G,I)},yL1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},kL1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},l50=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},rF2=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(l50(arguments[F]));return G},oF2=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},tF2=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},ns=function(G){return this instanceof ns?(this.v=G,this):new ns(G)},eF2=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof ns?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},AI2=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:ns(G[W](X)),done:!1}:J?J(X):X}:J}},BI2=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof kL1==="function"?kL1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},QI2=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};DI2=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")yL1(F,G,I[Y])}return Q(F,G),F},ZI2=function(G){return G&&G.__esModule?G:{default:G}},GI2=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},FI2=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},II2=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},YI2=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};WI2=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},JI2=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",fF2),A("__assign",hF2),A("__rest",gF2),A("__decorate",uF2),A("__param",mF2),A("__esDecorate",dF2),A("__runInitializers",cF2),A("__propKey",lF2),A("__setFunctionName",pF2),A("__metadata",iF2),A("__awaiter",nF2),A("__generator",aF2),A("__exportStar",sF2),A("__createBinding",yL1),A("__values",kL1),A("__read",l50),A("__spread",rF2),A("__spreadArrays",oF2),A("__spreadArray",tF2),A("__await",ns),A("__asyncGenerator",eF2),A("__asyncDelegator",AI2),A("__asyncValues",BI2),A("__makeTemplateObject",QI2),A("__importStar",DI2),A("__importDefault",ZI2),A("__classPrivateFieldGet",GI2),A("__classPrivateFieldSet",FI2),A("__classPrivateFieldIn",II2),A("__addDisposableResource",YI2),A("__disposeResources",WI2),A("__rewriteRelativeImportExtension",JI2)})});
var qG2=E((wG2)=>{Object.defineProperty(wG2,"__esModule",{value:!0});wG2.getRuntimeConfig=void 0;var Uq4=AZ2(),wq4=Uq4.__importDefault(BZ2()),$q4=WI(),zG2=i61(),zL1=V4(),qq4=jG(),EG2=v4(),ds=QD(),UG2=S3(),Nq4=kG(),Lq4=hZ(),Mq4=HG2(),Rq4=o61(),Oq4=yG(),Tq4=o61(),Pq4=(A)=>{Tq4.emitWarningIfUnsupportedVersion(process.version);let B=Oq4.resolveDefaultsModeConfig(A),Q=()=>B().then(Rq4.loadConfigsForDefaultMode),D=Mq4.getRuntimeConfig(A);$q4.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile};return{...D,...A,runtime:"node",defaultsMode:B,bodyLengthChecker:A?.bodyLengthChecker??Nq4.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??zG2.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:wq4.default.version}),maxAttempts:A?.maxAttempts??ds.loadConfig(EG2.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??ds.loadConfig(zL1.NODE_REGION_CONFIG_OPTIONS,{...zL1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:UG2.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??ds.loadConfig({...EG2.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||Lq4.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??qq4.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??UG2.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??ds.loadConfig(zL1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??ds.loadConfig(zL1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??ds.loadConfig(zG2.NODE_APP_ID_CONFIG_OPTIONS,Z)}};wG2.getRuntimeConfig=Pq4});
var qH0=E((us5,RYB)=>{var{defineProperty:Yy1,getOwnPropertyDescriptor:_g6,getOwnPropertyNames:xg6}=Object,vg6=Object.prototype.hasOwnProperty,xP=(A,B)=>Yy1(A,"name",{value:B,configurable:!0}),bg6=(A,B)=>{for(var Q in B)Yy1(A,Q,{get:B[Q],enumerable:!0})},fg6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of xg6(B))if(!vg6.call(A,Z)&&Z!==Q)Yy1(A,Z,{get:()=>B[Z],enumerable:!(D=_g6(B,Z))||D.enumerable})}return A},hg6=(A)=>fg6(Yy1({},"__esModule",{value:!0}),A),qYB={};bg6(qYB,{EventStreamCodec:()=>og6,HeaderMarshaller:()=>NYB,Int64:()=>Iy1,MessageDecoderStream:()=>tg6,MessageEncoderStream:()=>eg6,SmithyMessageDecoderStream:()=>Au6,SmithyMessageEncoderStream:()=>Bu6});RYB.exports=hg6(qYB);var gg6=Fy1(),Vm=Zy(),Iy1=class A{constructor(B){if(this.bytes=B,B.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static{xP(this,"Int64")}static fromNumber(B){if(B>9223372036854776000||B<-9223372036854776000)throw new Error(`${B} is too large (or, if negative, too small) to represent as an Int64`);let Q=new Uint8Array(8);for(let D=7,Z=Math.abs(Math.round(B));D>-1&&Z>0;D--,Z/=256)Q[D]=Z;if(B<0)$H0(Q);return new A(Q)}valueOf(){let B=this.bytes.slice(0),Q=B[0]&128;if(Q)$H0(B);return parseInt(Vm.toHex(B),16)*(Q?-1:1)}toString(){return String(this.valueOf())}};function $H0(A){for(let B=0;B<8;B++)A[B]^=255;for(let B=7;B>-1;B--)if(A[B]++,A[B]!==0)break}xP($H0,"negate");var NYB=class{constructor(A,B){this.toUtf8=A,this.fromUtf8=B}static{xP(this,"HeaderMarshaller")}format(A){let B=[];for(let Z of Object.keys(A)){let G=this.fromUtf8(Z);B.push(Uint8Array.from([G.byteLength]),G,this.formatHeaderValue(A[Z]))}let Q=new Uint8Array(B.reduce((Z,G)=>Z+G.byteLength,0)),D=0;for(let Z of B)Q.set(Z,D),D+=Z.byteLength;return Q}formatHeaderValue(A){switch(A.type){case"boolean":return Uint8Array.from([A.value?0:1]);case"byte":return Uint8Array.from([2,A.value]);case"short":let B=new DataView(new ArrayBuffer(3));return B.setUint8(0,3),B.setInt16(1,A.value,!1),new Uint8Array(B.buffer);case"integer":let Q=new DataView(new ArrayBuffer(5));return Q.setUint8(0,4),Q.setInt32(1,A.value,!1),new Uint8Array(Q.buffer);case"long":let D=new Uint8Array(9);return D[0]=5,D.set(A.value.bytes,1),D;case"binary":let Z=new DataView(new ArrayBuffer(3+A.value.byteLength));Z.setUint8(0,6),Z.setUint16(1,A.value.byteLength,!1);let G=new Uint8Array(Z.buffer);return G.set(A.value,3),G;case"string":let F=this.fromUtf8(A.value),I=new DataView(new ArrayBuffer(3+F.byteLength));I.setUint8(0,7),I.setUint16(1,F.byteLength,!1);let Y=new Uint8Array(I.buffer);return Y.set(F,3),Y;case"timestamp":let W=new Uint8Array(9);return W[0]=8,W.set(Iy1.fromNumber(A.value.valueOf()).bytes,1),W;case"uuid":if(!ag6.test(A.value))throw new Error(`Invalid UUID received: ${A.value}`);let J=new Uint8Array(17);return J[0]=9,J.set(Vm.fromHex(A.value.replace(/\-/g,"")),1),J}}parse(A){let B={},Q=0;while(Q<A.byteLength){let D=A.getUint8(Q++),Z=this.toUtf8(new Uint8Array(A.buffer,A.byteOffset+Q,D));switch(Q+=D,A.getUint8(Q++)){case 0:B[Z]={type:$YB,value:!0};break;case 1:B[Z]={type:$YB,value:!1};break;case 2:B[Z]={type:ug6,value:A.getInt8(Q++)};break;case 3:B[Z]={type:mg6,value:A.getInt16(Q,!1)},Q+=2;break;case 4:B[Z]={type:dg6,value:A.getInt32(Q,!1)},Q+=4;break;case 5:B[Z]={type:cg6,value:new Iy1(new Uint8Array(A.buffer,A.byteOffset+Q,8))},Q+=8;break;case 6:let G=A.getUint16(Q,!1);Q+=2,B[Z]={type:lg6,value:new Uint8Array(A.buffer,A.byteOffset+Q,G)},Q+=G;break;case 7:let F=A.getUint16(Q,!1);Q+=2,B[Z]={type:pg6,value:this.toUtf8(new Uint8Array(A.buffer,A.byteOffset+Q,F))},Q+=F;break;case 8:B[Z]={type:ig6,value:new Date(new Iy1(new Uint8Array(A.buffer,A.byteOffset+Q,8)).valueOf())},Q+=8;break;case 9:let I=new Uint8Array(A.buffer,A.byteOffset+Q,16);Q+=16,B[Z]={type:ng6,value:`${Vm.toHex(I.subarray(0,4))}-${Vm.toHex(I.subarray(4,6))}-${Vm.toHex(I.subarray(6,8))}-${Vm.toHex(I.subarray(8,10))}-${Vm.toHex(I.subarray(10))}`};break;default:throw new Error("Unrecognized header type tag")}}return B}},$YB="boolean",ug6="byte",mg6="short",dg6="integer",cg6="long",lg6="binary",pg6="string",ig6="timestamp",ng6="uuid",ag6=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,sg6=Fy1(),LYB=4,_x=LYB*2,Cm=4,rg6=_x+Cm*2;function MYB({byteLength:A,byteOffset:B,buffer:Q}){if(A<rg6)throw new Error("Provided message too short to accommodate event stream message overhead");let D=new DataView(Q,B,A),Z=D.getUint32(0,!1);if(A!==Z)throw new Error("Reported message length does not match received message length");let G=D.getUint32(LYB,!1),F=D.getUint32(_x,!1),I=D.getUint32(A-Cm,!1),Y=new sg6.Crc32().update(new Uint8Array(Q,B,_x));if(F!==Y.digest())throw new Error(`The prelude checksum specified in the message (${F}) does not match the calculated CRC32 checksum (${Y.digest()})`);if(Y.update(new Uint8Array(Q,B+_x,A-(_x+Cm))),I!==Y.digest())throw new Error(`The message checksum (${Y.digest()}) did not match the expected value of ${I}`);return{headers:new DataView(Q,B+_x+Cm,G),body:new Uint8Array(Q,B+_x+Cm+G,Z-G-(_x+Cm+Cm))}}xP(MYB,"splitMessage");var og6=class{static{xP(this,"EventStreamCodec")}constructor(A,B){this.headerMarshaller=new NYB(A,B),this.messageBuffer=[],this.isEndOfStream=!1}feed(A){this.messageBuffer.push(this.decode(A))}endOfStream(){this.isEndOfStream=!0}getMessage(){let A=this.messageBuffer.pop(),B=this.isEndOfStream;return{getMessage(){return A},isEndOfStream(){return B}}}getAvailableMessages(){let A=this.messageBuffer;this.messageBuffer=[];let B=this.isEndOfStream;return{getMessages(){return A},isEndOfStream(){return B}}}encode({headers:A,body:B}){let Q=this.headerMarshaller.format(A),D=Q.byteLength+B.byteLength+16,Z=new Uint8Array(D),G=new DataView(Z.buffer,Z.byteOffset,Z.byteLength),F=new gg6.Crc32;return G.setUint32(0,D,!1),G.setUint32(4,Q.byteLength,!1),G.setUint32(8,F.update(Z.subarray(0,8)).digest(),!1),Z.set(Q,12),Z.set(B,Q.byteLength+12),G.setUint32(D-4,F.update(Z.subarray(8,D-4)).digest(),!1),Z}decode(A){let{headers:B,body:Q}=MYB(A);return{headers:this.headerMarshaller.parse(B),body:Q}}formatHeaders(A){return this.headerMarshaller.format(A)}},tg6=class{constructor(A){this.options=A}static{xP(this,"MessageDecoderStream")}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(let A of this.options.inputStream)yield this.options.decoder.decode(A)}},eg6=class{constructor(A){this.options=A}static{xP(this,"MessageEncoderStream")}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(let A of this.options.messageStream)yield this.options.encoder.encode(A);if(this.options.includeEndFrame)yield new Uint8Array(0)}},Au6=class{constructor(A){this.options=A}static{xP(this,"SmithyMessageDecoderStream")}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(let A of this.options.messageStream){let B=await this.options.deserializer(A);if(B===void 0)continue;yield B}}},Bu6=class{constructor(A){this.options=A}static{xP(this,"SmithyMessageEncoderStream")}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(let A of this.options.inputStream)yield this.options.serializer(A)}}});
var r30=E((qz5,pL1)=>{var{defineProperty:vJ2,getOwnPropertyDescriptor:LP4,getOwnPropertyNames:MP4}=Object,RP4=Object.prototype.hasOwnProperty,a30=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of MP4(B))if(!RP4.call(A,Z)&&Z!==Q)vJ2(A,Z,{get:()=>B[Z],enumerable:!(D=LP4(B,Z))||D.enumerable})}return A},bJ2=(A,B,Q)=>(a30(A,B,"default"),Q&&a30(Q,B,"default")),OP4=(A)=>a30(vJ2({},"__esModule",{value:!0}),A),s30={};pL1.exports=OP4(s30);bJ2(s30,xJ2(),pL1.exports);bJ2(s30,n30(),pL1.exports)});
var r62=E((sC5,CN1)=>{var N62,L62,M62,R62,O62,T62,P62,S62,j62,k62,y62,_62,x62,XN1,J80,v62,b62,f62,Ns,h62,g62,u62,m62,d62,c62,l62,p62,i62,VN1,n62,a62,s62;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof CN1==="object"&&typeof sC5==="object")A(Q(B,Q(sC5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};N62=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},L62=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},M62=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},R62=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},O62=function(G,F){return function(I,Y){F(I,Y,G)}},T62=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},P62=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},S62=function(G){return typeof G==="symbol"?G:"".concat(G)},j62=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},k62=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},y62=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},_62=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},x62=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))VN1(F,G,I)},VN1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},XN1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},J80=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},v62=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(J80(arguments[F]));return G},b62=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},f62=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},Ns=function(G){return this instanceof Ns?(this.v=G,this):new Ns(G)},h62=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof Ns?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},g62=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:Ns(G[W](X)),done:!1}:J?J(X):X}:J}},u62=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof XN1==="function"?XN1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},m62=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};d62=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")VN1(F,G,I[Y])}return Q(F,G),F},c62=function(G){return G&&G.__esModule?G:{default:G}},l62=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},p62=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},i62=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},n62=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};a62=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},s62=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",N62),A("__assign",L62),A("__rest",M62),A("__decorate",R62),A("__param",O62),A("__esDecorate",T62),A("__runInitializers",P62),A("__propKey",S62),A("__setFunctionName",j62),A("__metadata",k62),A("__awaiter",y62),A("__generator",_62),A("__exportStar",x62),A("__createBinding",VN1),A("__values",XN1),A("__read",J80),A("__spread",v62),A("__spreadArrays",b62),A("__spreadArray",f62),A("__await",Ns),A("__asyncGenerator",h62),A("__asyncDelegator",g62),A("__asyncValues",u62),A("__makeTemplateObject",m62),A("__importStar",d62),A("__importDefault",c62),A("__classPrivateFieldGet",l62),A("__classPrivateFieldSet",p62),A("__classPrivateFieldIn",i62),A("__addDisposableResource",n62),A("__disposeResources",a62),A("__rewriteRelativeImportExtension",s62)})});
var rIB=E((Ps5,sIB)=>{var{defineProperty:Zy1,getOwnPropertyDescriptor:sh6,getOwnPropertyNames:rh6}=Object,oh6=Object.prototype.hasOwnProperty,nIB=(A,B)=>Zy1(A,"name",{value:B,configurable:!0}),th6=(A,B)=>{for(var Q in B)Zy1(A,Q,{get:B[Q],enumerable:!0})},eh6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of rh6(B))if(!oh6.call(A,Z)&&Z!==Q)Zy1(A,Z,{get:()=>B[Z],enumerable:!(D=sh6(B,Z))||D.enumerable})}return A},Ag6=(A)=>eh6(Zy1({},"__esModule",{value:!0}),A),aIB={};th6(aIB,{fromArrayBuffer:()=>Qg6,fromString:()=>Dg6});sIB.exports=Ag6(aIB);var Bg6=iIB(),HH0=J1("buffer"),Qg6=nIB((A,B=0,Q=A.byteLength-B)=>{if(!Bg6.isArrayBuffer(A))throw new TypeError(`The "input" argument must be ArrayBuffer. Received type ${typeof A} (${A})`);return HH0.Buffer.from(A,B,Q)},"fromArrayBuffer"),Dg6=nIB((A,B)=>{if(typeof A!=="string")throw new TypeError(`The "input" argument must be of type string. Received type ${typeof A} (${A})`);return B?HH0.Buffer.from(A,B):HH0.Buffer.from(A)},"fromString")});
var s50=E((cH5,xL1)=>{var{defineProperty:EI2,getOwnPropertyDescriptor:KM4,getOwnPropertyNames:HM4}=Object,zM4=Object.prototype.hasOwnProperty,n50=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of HM4(B))if(!zM4.call(A,Z)&&Z!==Q)EI2(A,Z,{get:()=>B[Z],enumerable:!(D=KM4(B,Z))||D.enumerable})}return A},UI2=(A,B,Q)=>(n50(A,B,"default"),Q&&n50(Q,B,"default")),EM4=(A)=>n50(EI2({},"__esModule",{value:!0}),A),a50={};xL1.exports=EM4(a50);UI2(a50,CI2(),xL1.exports);UI2(a50,zI2(),xL1.exports)});
var sI2=E((nI2)=>{Object.defineProperty(nI2,"__esModule",{value:!0});nI2.getRuntimeConfig=void 0;var jM4=p50(),kM4=jM4.__importDefault(i50()),yM4=WI(),lI2=i61(),vL1=V4(),_M4=jG(),pI2=v4(),os=QD(),iI2=S3(),xM4=kG(),vM4=hZ(),bM4=cI2(),fM4=QZ(),hM4=yG(),gM4=QZ(),uM4=(A)=>{gM4.emitWarningIfUnsupportedVersion(process.version);let B=hM4.resolveDefaultsModeConfig(A),Q=()=>B().then(fM4.loadConfigsForDefaultMode),D=bM4.getRuntimeConfig(A);yM4.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile};return{...D,...A,runtime:"node",defaultsMode:B,bodyLengthChecker:A?.bodyLengthChecker??xM4.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??lI2.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:kM4.default.version}),maxAttempts:A?.maxAttempts??os.loadConfig(pI2.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??os.loadConfig(vL1.NODE_REGION_CONFIG_OPTIONS,{...vL1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:iI2.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??os.loadConfig({...pI2.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||vM4.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??_M4.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??iI2.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??os.loadConfig(vL1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??os.loadConfig(vL1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??os.loadConfig(lI2.NODE_APP_ID_CONFIG_OPTIONS,Z)}};nI2.getRuntimeConfig=uM4});
var tWB=E((rWB)=>{Object.defineProperty(rWB,"__esModule",{value:!0});rWB.getRuntimeConfig=void 0;var Ed6=KIB(),Ud6=Ed6.__importDefault(HIB()),wd6=WI(),$d6=A70(),qd6=SYB(),nWB=i61(),$y1=V4(),Nd6=uYB(),Ld6=jG(),aWB=v4(),Re=QD(),sWB=S3(),Md6=kG(),Rd6=hZ(),Od6=iWB(),Td6=xD1(),Pd6=yG(),Sd6=xD1(),jd6=(A)=>{Sd6.emitWarningIfUnsupportedVersion(process.version);let B=Pd6.resolveDefaultsModeConfig(A),Q=()=>B().then(Td6.loadConfigsForDefaultMode),D=Od6.getRuntimeConfig(A);wd6.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile};return{...D,...A,runtime:"node",defaultsMode:B,bodyLengthChecker:A?.bodyLengthChecker??Md6.calculateBodyLength,credentialDefaultProvider:A?.credentialDefaultProvider??$d6.defaultProvider,defaultUserAgentProvider:A?.defaultUserAgentProvider??nWB.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:Ud6.default.version}),eventStreamPayloadHandlerProvider:A?.eventStreamPayloadHandlerProvider??qd6.eventStreamPayloadHandlerProvider,eventStreamSerdeProvider:A?.eventStreamSerdeProvider??Nd6.eventStreamSerdeProvider,maxAttempts:A?.maxAttempts??Re.loadConfig(aWB.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??Re.loadConfig($y1.NODE_REGION_CONFIG_OPTIONS,{...$y1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:sWB.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??Re.loadConfig({...aWB.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||Rd6.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??Ld6.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??sWB.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??Re.loadConfig($y1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??Re.loadConfig($y1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??Re.loadConfig(nWB.NODE_APP_ID_CONFIG_OPTIONS,Z)}};rWB.getRuntimeConfig=jd6});
var uYB=E((ts5,gYB)=>{var{defineProperty:Cy1,getOwnPropertyDescriptor:wu6,getOwnPropertyNames:$u6}=Object,qu6=Object.prototype.hasOwnProperty,NH0=(A,B)=>Cy1(A,"name",{value:B,configurable:!0}),Nu6=(A,B)=>{for(var Q in B)Cy1(A,Q,{get:B[Q],enumerable:!0})},Lu6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of $u6(B))if(!qu6.call(A,Z)&&Z!==Q)Cy1(A,Z,{get:()=>B[Z],enumerable:!(D=wu6(B,Z))||D.enumerable})}return A},Mu6=(A)=>Lu6(Cy1({},"__esModule",{value:!0}),A),bYB={};Nu6(bYB,{EventStreamMarshaller:()=>hYB,eventStreamSerdeProvider:()=>Tu6});gYB.exports=Mu6(bYB);var Ru6=vYB(),Ou6=J1("stream");async function*fYB(A){let B=!1,Q=!1,D=new Array;A.on("error",(Z)=>{if(!B)B=!0;if(Z)throw Z}),A.on("data",(Z)=>{D.push(Z)}),A.on("end",()=>{B=!0});while(!Q){let Z=await new Promise((G)=>setTimeout(()=>G(D.shift()),0));if(Z)yield Z;Q=B&&D.length===0}}NH0(fYB,"readabletoIterable");var hYB=class{static{NH0(this,"EventStreamMarshaller")}constructor({utf8Encoder:A,utf8Decoder:B}){this.universalMarshaller=new Ru6.EventStreamMarshaller({utf8Decoder:B,utf8Encoder:A})}deserialize(A,B){let Q=typeof A[Symbol.asyncIterator]==="function"?A:fYB(A);return this.universalMarshaller.deserialize(Q,B)}serialize(A,B){return Ou6.Readable.from(this.universalMarshaller.serialize(A,B))}},Tu6=NH0((A)=>new hYB(A),"eventStreamSerdeProvider")});
var vI2=E((_I2)=>{Object.defineProperty(_I2,"__esModule",{value:!0});_I2.ruleSet=void 0;var SI2="required",iz="fn",nz="argv",rs="ref",wI2=!0,$I2="isSet",F81="booleanEquals",as="error",ss="endpoint",_T="tree",r50="PartitionResult",o50="getAttr",qI2={[SI2]:!1,type:"String"},NI2={[SI2]:!0,default:!1,type:"Boolean"},LI2={[rs]:"Endpoint"},jI2={[iz]:F81,[nz]:[{[rs]:"UseFIPS"},!0]},kI2={[iz]:F81,[nz]:[{[rs]:"UseDualStack"},!0]},pz={},MI2={[iz]:o50,[nz]:[{[rs]:r50},"supportsFIPS"]},yI2={[rs]:r50},RI2={[iz]:F81,[nz]:[!0,{[iz]:o50,[nz]:[yI2,"supportsDualStack"]}]},OI2=[jI2],TI2=[kI2],PI2=[{[rs]:"Region"}],UM4={version:"1.0",parameters:{Region:qI2,UseDualStack:NI2,UseFIPS:NI2,Endpoint:qI2},rules:[{conditions:[{[iz]:$I2,[nz]:[LI2]}],rules:[{conditions:OI2,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:as},{conditions:TI2,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:as},{endpoint:{url:LI2,properties:pz,headers:pz},type:ss}],type:_T},{conditions:[{[iz]:$I2,[nz]:PI2}],rules:[{conditions:[{[iz]:"aws.partition",[nz]:PI2,assign:r50}],rules:[{conditions:[jI2,kI2],rules:[{conditions:[{[iz]:F81,[nz]:[wI2,MI2]},RI2],rules:[{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:pz,headers:pz},type:ss}],type:_T},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:as}],type:_T},{conditions:OI2,rules:[{conditions:[{[iz]:F81,[nz]:[MI2,wI2]}],rules:[{conditions:[{[iz]:"stringEquals",[nz]:[{[iz]:o50,[nz]:[yI2,"name"]},"aws-us-gov"]}],endpoint:{url:"https://oidc.{Region}.amazonaws.com",properties:pz,headers:pz},type:ss},{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dnsSuffix}",properties:pz,headers:pz},type:ss}],type:_T},{error:"FIPS is enabled but this partition does not support FIPS",type:as}],type:_T},{conditions:TI2,rules:[{conditions:[RI2],rules:[{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:pz,headers:pz},type:ss}],type:_T},{error:"DualStack is enabled but this partition does not support DualStack",type:as}],type:_T},{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dnsSuffix}",properties:pz,headers:pz},type:ss}],type:_T}],type:_T},{error:"Invalid Configuration: Missing Region",type:as}]};_I2.ruleSet=UM4});
var vXB=E((Ur5,xXB)=>{var{defineProperty:by1,getOwnPropertyDescriptor:cd6,getOwnPropertyNames:ld6}=Object,pd6=Object.prototype.hasOwnProperty,h1=(A,B)=>by1(A,"name",{value:B,configurable:!0}),id6=(A,B)=>{for(var Q in B)by1(A,Q,{get:B[Q],enumerable:!0})},nd6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ld6(B))if(!pd6.call(A,Z)&&Z!==Q)by1(A,Z,{get:()=>B[Z],enumerable:!(D=cd6(B,Z))||D.enumerable})}return A},ad6=(A)=>nd6(by1({},"__esModule",{value:!0}),A),UJB={};id6(UJB,{AccessDeniedException:()=>$JB,ApplyGuardrailCommand:()=>RXB,ApplyGuardrailRequestFilterSensitiveLog:()=>fJB,AsyncInvokeOutputDataConfig:()=>uH0,AsyncInvokeStatus:()=>Gc6,AsyncInvokeSummaryFilterSensitiveLog:()=>_JB,BedrockRuntime:()=>_XB,BedrockRuntimeClient:()=>oH0,BedrockRuntimeServiceException:()=>hV,BidirectionalInputPayloadPartFilterSensitiveLog:()=>ic6,BidirectionalOutputPayloadPartFilterSensitiveLog:()=>ac6,CachePointType:()=>Oc6,ConflictException:()=>MJB,ContentBlock:()=>ky1,ContentBlockDelta:()=>pH0,ContentBlockDeltaEventFilterSensitiveLog:()=>lJB,ContentBlockDeltaFilterSensitiveLog:()=>cJB,ContentBlockFilterSensitiveLog:()=>hJB,ContentBlockStart:()=>iH0,ConversationRole:()=>_c6,ConverseCommand:()=>OXB,ConverseOutput:()=>cH0,ConverseOutputFilterSensitiveLog:()=>uJB,ConverseRequestFilterSensitiveLog:()=>gJB,ConverseResponseFilterSensitiveLog:()=>mJB,ConverseStreamCommand:()=>TXB,ConverseStreamOutput:()=>nH0,ConverseStreamOutputFilterSensitiveLog:()=>pc6,ConverseStreamRequestFilterSensitiveLog:()=>dJB,ConverseStreamResponseFilterSensitiveLog:()=>pJB,DocumentFormat:()=>Tc6,DocumentSource:()=>My1,GetAsyncInvokeCommand:()=>PXB,GetAsyncInvokeResponseFilterSensitiveLog:()=>yJB,GuardrailAction:()=>Vc6,GuardrailContentBlock:()=>Ly1,GuardrailContentBlockFilterSensitiveLog:()=>bJB,GuardrailContentFilterConfidence:()=>Kc6,GuardrailContentFilterStrength:()=>Hc6,GuardrailContentFilterType:()=>zc6,GuardrailContentPolicyAction:()=>Cc6,GuardrailContentQualifier:()=>Wc6,GuardrailContentSource:()=>Xc6,GuardrailContextualGroundingFilterType:()=>Uc6,GuardrailContextualGroundingPolicyAction:()=>Ec6,GuardrailConverseContentBlock:()=>Oy1,GuardrailConverseContentBlockFilterSensitiveLog:()=>tH0,GuardrailConverseContentQualifier:()=>Sc6,GuardrailConverseImageBlockFilterSensitiveLog:()=>mc6,GuardrailConverseImageFormat:()=>Pc6,GuardrailConverseImageSource:()=>Ry1,GuardrailConverseImageSourceFilterSensitiveLog:()=>uc6,GuardrailImageBlockFilterSensitiveLog:()=>gc6,GuardrailImageFormat:()=>Yc6,GuardrailImageSource:()=>Ny1,GuardrailImageSourceFilterSensitiveLog:()=>hc6,GuardrailManagedWordType:()=>Mc6,GuardrailOutputScope:()=>Jc6,GuardrailPiiEntityType:()=>$c6,GuardrailSensitiveInformationPolicyAction:()=>wc6,GuardrailStreamProcessingMode:()=>bc6,GuardrailTopicPolicyAction:()=>qc6,GuardrailTopicType:()=>Nc6,GuardrailTrace:()=>Rc6,GuardrailWordPolicyAction:()=>Lc6,ImageFormat:()=>jc6,ImageSource:()=>Ty1,InternalServerException:()=>qJB,InvokeModelCommand:()=>SXB,InvokeModelRequestFilterSensitiveLog:()=>iJB,InvokeModelResponseFilterSensitiveLog:()=>nJB,InvokeModelWithBidirectionalStreamCommand:()=>jXB,InvokeModelWithBidirectionalStreamInput:()=>vy1,InvokeModelWithBidirectionalStreamInputFilterSensitiveLog:()=>nc6,InvokeModelWithBidirectionalStreamOutput:()=>aH0,InvokeModelWithBidirectionalStreamOutputFilterSensitiveLog:()=>sc6,InvokeModelWithBidirectionalStreamRequestFilterSensitiveLog:()=>aJB,InvokeModelWithBidirectionalStreamResponseFilterSensitiveLog:()=>sJB,InvokeModelWithResponseStreamCommand:()=>kXB,InvokeModelWithResponseStreamRequestFilterSensitiveLog:()=>rJB,InvokeModelWithResponseStreamResponseFilterSensitiveLog:()=>oJB,ListAsyncInvokesCommand:()=>Fz0,ListAsyncInvokesResponseFilterSensitiveLog:()=>xJB,MessageFilterSensitiveLog:()=>fy1,ModelErrorException:()=>PJB,ModelNotReadyException:()=>SJB,ModelStreamErrorException:()=>kJB,ModelTimeoutException:()=>jJB,PayloadPartFilterSensitiveLog:()=>rc6,PerformanceConfigLatency:()=>xc6,PromptVariableValues:()=>mH0,ReasoningContentBlock:()=>Py1,ReasoningContentBlockDelta:()=>lH0,ReasoningContentBlockDeltaFilterSensitiveLog:()=>lc6,ReasoningContentBlockFilterSensitiveLog:()=>cc6,ReasoningTextBlockFilterSensitiveLog:()=>dc6,ResourceNotFoundException:()=>RJB,ResponseStream:()=>sH0,ResponseStreamFilterSensitiveLog:()=>oc6,ServiceQuotaExceededException:()=>OJB,ServiceUnavailableException:()=>TJB,SortAsyncInvocationBy:()=>Fc6,SortOrder:()=>Ic6,StartAsyncInvokeCommand:()=>yXB,StartAsyncInvokeRequestFilterSensitiveLog:()=>vJB,StopReason:()=>vc6,SystemContentBlock:()=>yy1,SystemContentBlockFilterSensitiveLog:()=>eH0,ThrottlingException:()=>NJB,Tool:()=>xy1,ToolChoice:()=>dH0,ToolInputSchema:()=>_y1,ToolResultContentBlock:()=>jy1,ToolResultStatus:()=>yc6,Trace:()=>fc6,ValidationException:()=>LJB,VideoFormat:()=>kc6,VideoSource:()=>Sy1,__Client:()=>r1.Client,paginateListAsyncInvokes:()=>mp6});xXB.exports=ad6(UJB);var wJB=xFB(),GJB=b61(),sd6=f61(),rd6=h61(),FJB=_s(),od6=V4(),bK=VB(),td6=fFB(),ed6=TG(),NM=q6(),IJB=v4(),YJB=XH0(),Ac6=h1((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"bedrock"})},"resolveClientEndpointParameters"),vP={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},Bc6=tWB(),WJB=e61(),JJB=ZJB(),r1=xD1(),Qc6=h1((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),Dc6=h1((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),Zc6=h1((A,B)=>{let Q=Object.assign(WJB.getAwsRegionExtensionConfiguration(A),r1.getDefaultExtensionConfiguration(A),JJB.getHttpHandlerExtensionConfiguration(A),Qc6(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,WJB.resolveAwsRegionExtensionConfiguration(Q),r1.resolveDefaultRuntimeConfig(Q),JJB.resolveHttpHandlerRuntimeConfig(Q),Dc6(Q))},"resolveRuntimeExtensions"),oH0=class extends r1.Client{static{h1(this,"BedrockRuntimeClient")}config;constructor(...[A]){let B=Bc6.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=Ac6(B),D=FJB.resolveUserAgentConfig(Q),Z=IJB.resolveRetryConfig(D),G=od6.resolveRegionConfig(Z),F=GJB.resolveHostHeaderConfig(G),I=NM.resolveEndpointConfig(F),Y=td6.resolveEventStreamSerdeConfig(I),W=YJB.resolveHttpAuthSchemeConfig(Y),J=wJB.resolveEventStreamConfig(W),X=Zc6(J,A?.extensions||[]);this.config=X,this.middlewareStack.use(FJB.getUserAgentPlugin(this.config)),this.middlewareStack.use(IJB.getRetryPlugin(this.config)),this.middlewareStack.use(ed6.getContentLengthPlugin(this.config)),this.middlewareStack.use(GJB.getHostHeaderPlugin(this.config)),this.middlewareStack.use(sd6.getLoggerPlugin(this.config)),this.middlewareStack.use(rd6.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(bK.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:YJB.defaultBedrockRuntimeHttpAuthSchemeParametersProvider,identityProviderConfigProvider:h1(async(V)=>new bK.DefaultIdentityProviderConfig({"aws.auth#sigv4":V.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(bK.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},bP=T3(),hV=class A extends r1.ServiceException{static{h1(this,"BedrockRuntimeServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},$JB=class A extends hV{static{h1(this,"AccessDeniedException")}name="AccessDeniedException";$fault="client";constructor(B){super({name:"AccessDeniedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},uH0;((A)=>{A.visit=h1((B,Q)=>{if(B.s3OutputDataConfig!==void 0)return Q.s3OutputDataConfig(B.s3OutputDataConfig);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(uH0||(uH0={}));var Gc6={COMPLETED:"Completed",FAILED:"Failed",IN_PROGRESS:"InProgress"},qJB=class A extends hV{static{h1(this,"InternalServerException")}name="InternalServerException";$fault="server";constructor(B){super({name:"InternalServerException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype)}},NJB=class A extends hV{static{h1(this,"ThrottlingException")}name="ThrottlingException";$fault="client";constructor(B){super({name:"ThrottlingException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},LJB=class A extends hV{static{h1(this,"ValidationException")}name="ValidationException";$fault="client";constructor(B){super({name:"ValidationException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},Fc6={SUBMISSION_TIME:"SubmissionTime"},Ic6={ASCENDING:"Ascending",DESCENDING:"Descending"},MJB=class A extends hV{static{h1(this,"ConflictException")}name="ConflictException";$fault="client";constructor(B){super({name:"ConflictException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},RJB=class A extends hV{static{h1(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},OJB=class A extends hV{static{h1(this,"ServiceQuotaExceededException")}name="ServiceQuotaExceededException";$fault="client";constructor(B){super({name:"ServiceQuotaExceededException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},TJB=class A extends hV{static{h1(this,"ServiceUnavailableException")}name="ServiceUnavailableException";$fault="server";constructor(B){super({name:"ServiceUnavailableException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype)}},Yc6={JPEG:"jpeg",PNG:"png"},Ny1;((A)=>{A.visit=h1((B,Q)=>{if(B.bytes!==void 0)return Q.bytes(B.bytes);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(Ny1||(Ny1={}));var Wc6={GROUNDING_SOURCE:"grounding_source",GUARD_CONTENT:"guard_content",QUERY:"query"},Ly1;((A)=>{A.visit=h1((B,Q)=>{if(B.text!==void 0)return Q.text(B.text);if(B.image!==void 0)return Q.image(B.image);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(Ly1||(Ly1={}));var Jc6={FULL:"FULL",INTERVENTIONS:"INTERVENTIONS"},Xc6={INPUT:"INPUT",OUTPUT:"OUTPUT"},Vc6={GUARDRAIL_INTERVENED:"GUARDRAIL_INTERVENED",NONE:"NONE"},Cc6={BLOCKED:"BLOCKED",NONE:"NONE"},Kc6={HIGH:"HIGH",LOW:"LOW",MEDIUM:"MEDIUM",NONE:"NONE"},Hc6={HIGH:"HIGH",LOW:"LOW",MEDIUM:"MEDIUM",NONE:"NONE"},zc6={HATE:"HATE",INSULTS:"INSULTS",MISCONDUCT:"MISCONDUCT",PROMPT_ATTACK:"PROMPT_ATTACK",SEXUAL:"SEXUAL",VIOLENCE:"VIOLENCE"},Ec6={BLOCKED:"BLOCKED",NONE:"NONE"},Uc6={GROUNDING:"GROUNDING",RELEVANCE:"RELEVANCE"},wc6={ANONYMIZED:"ANONYMIZED",BLOCKED:"BLOCKED",NONE:"NONE"},$c6={ADDRESS:"ADDRESS",AGE:"AGE",AWS_ACCESS_KEY:"AWS_ACCESS_KEY",AWS_SECRET_KEY:"AWS_SECRET_KEY",CA_HEALTH_NUMBER:"CA_HEALTH_NUMBER",CA_SOCIAL_INSURANCE_NUMBER:"CA_SOCIAL_INSURANCE_NUMBER",CREDIT_DEBIT_CARD_CVV:"CREDIT_DEBIT_CARD_CVV",CREDIT_DEBIT_CARD_EXPIRY:"CREDIT_DEBIT_CARD_EXPIRY",CREDIT_DEBIT_CARD_NUMBER:"CREDIT_DEBIT_CARD_NUMBER",DRIVER_ID:"DRIVER_ID",EMAIL:"EMAIL",INTERNATIONAL_BANK_ACCOUNT_NUMBER:"INTERNATIONAL_BANK_ACCOUNT_NUMBER",IP_ADDRESS:"IP_ADDRESS",LICENSE_PLATE:"LICENSE_PLATE",MAC_ADDRESS:"MAC_ADDRESS",NAME:"NAME",PASSWORD:"PASSWORD",PHONE:"PHONE",PIN:"PIN",SWIFT_CODE:"SWIFT_CODE",UK_NATIONAL_HEALTH_SERVICE_NUMBER:"UK_NATIONAL_HEALTH_SERVICE_NUMBER",UK_NATIONAL_INSURANCE_NUMBER:"UK_NATIONAL_INSURANCE_NUMBER",UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER:"UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER",URL:"URL",USERNAME:"USERNAME",US_BANK_ACCOUNT_NUMBER:"US_BANK_ACCOUNT_NUMBER",US_BANK_ROUTING_NUMBER:"US_BANK_ROUTING_NUMBER",US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER:"US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER",US_PASSPORT_NUMBER:"US_PASSPORT_NUMBER",US_SOCIAL_SECURITY_NUMBER:"US_SOCIAL_SECURITY_NUMBER",VEHICLE_IDENTIFICATION_NUMBER:"VEHICLE_IDENTIFICATION_NUMBER"},qc6={BLOCKED:"BLOCKED",NONE:"NONE"},Nc6={DENY:"DENY"},Lc6={BLOCKED:"BLOCKED",NONE:"NONE"},Mc6={PROFANITY:"PROFANITY"},Rc6={DISABLED:"disabled",ENABLED:"enabled",ENABLED_FULL:"enabled_full"},Oc6={DEFAULT:"default"},Tc6={CSV:"csv",DOC:"doc",DOCX:"docx",HTML:"html",MD:"md",PDF:"pdf",TXT:"txt",XLS:"xls",XLSX:"xlsx"},My1;((A)=>{A.visit=h1((B,Q)=>{if(B.bytes!==void 0)return Q.bytes(B.bytes);if(B.s3Location!==void 0)return Q.s3Location(B.s3Location);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(My1||(My1={}));var Pc6={JPEG:"jpeg",PNG:"png"},Ry1;((A)=>{A.visit=h1((B,Q)=>{if(B.bytes!==void 0)return Q.bytes(B.bytes);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(Ry1||(Ry1={}));var Sc6={GROUNDING_SOURCE:"grounding_source",GUARD_CONTENT:"guard_content",QUERY:"query"},Oy1;((A)=>{A.visit=h1((B,Q)=>{if(B.text!==void 0)return Q.text(B.text);if(B.image!==void 0)return Q.image(B.image);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(Oy1||(Oy1={}));var jc6={GIF:"gif",JPEG:"jpeg",PNG:"png",WEBP:"webp"},Ty1;((A)=>{A.visit=h1((B,Q)=>{if(B.bytes!==void 0)return Q.bytes(B.bytes);if(B.s3Location!==void 0)return Q.s3Location(B.s3Location);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(Ty1||(Ty1={}));var Py1;((A)=>{A.visit=h1((B,Q)=>{if(B.reasoningText!==void 0)return Q.reasoningText(B.reasoningText);if(B.redactedContent!==void 0)return Q.redactedContent(B.redactedContent);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(Py1||(Py1={}));var kc6={FLV:"flv",MKV:"mkv",MOV:"mov",MP4:"mp4",MPEG:"mpeg",MPG:"mpg",THREE_GP:"three_gp",WEBM:"webm",WMV:"wmv"},Sy1;((A)=>{A.visit=h1((B,Q)=>{if(B.bytes!==void 0)return Q.bytes(B.bytes);if(B.s3Location!==void 0)return Q.s3Location(B.s3Location);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(Sy1||(Sy1={}));var jy1;((A)=>{A.visit=h1((B,Q)=>{if(B.json!==void 0)return Q.json(B.json);if(B.text!==void 0)return Q.text(B.text);if(B.image!==void 0)return Q.image(B.image);if(B.document!==void 0)return Q.document(B.document);if(B.video!==void 0)return Q.video(B.video);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(jy1||(jy1={}));var yc6={ERROR:"error",SUCCESS:"success"},ky1;((A)=>{A.visit=h1((B,Q)=>{if(B.text!==void 0)return Q.text(B.text);if(B.image!==void 0)return Q.image(B.image);if(B.document!==void 0)return Q.document(B.document);if(B.video!==void 0)return Q.video(B.video);if(B.toolUse!==void 0)return Q.toolUse(B.toolUse);if(B.toolResult!==void 0)return Q.toolResult(B.toolResult);if(B.guardContent!==void 0)return Q.guardContent(B.guardContent);if(B.cachePoint!==void 0)return Q.cachePoint(B.cachePoint);if(B.reasoningContent!==void 0)return Q.reasoningContent(B.reasoningContent);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(ky1||(ky1={}));var _c6={ASSISTANT:"assistant",USER:"user"},xc6={OPTIMIZED:"optimized",STANDARD:"standard"},mH0;((A)=>{A.visit=h1((B,Q)=>{if(B.text!==void 0)return Q.text(B.text);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(mH0||(mH0={}));var yy1;((A)=>{A.visit=h1((B,Q)=>{if(B.text!==void 0)return Q.text(B.text);if(B.guardContent!==void 0)return Q.guardContent(B.guardContent);if(B.cachePoint!==void 0)return Q.cachePoint(B.cachePoint);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(yy1||(yy1={}));var dH0;((A)=>{A.visit=h1((B,Q)=>{if(B.auto!==void 0)return Q.auto(B.auto);if(B.any!==void 0)return Q.any(B.any);if(B.tool!==void 0)return Q.tool(B.tool);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(dH0||(dH0={}));var _y1;((A)=>{A.visit=h1((B,Q)=>{if(B.json!==void 0)return Q.json(B.json);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(_y1||(_y1={}));var xy1;((A)=>{A.visit=h1((B,Q)=>{if(B.toolSpec!==void 0)return Q.toolSpec(B.toolSpec);if(B.cachePoint!==void 0)return Q.cachePoint(B.cachePoint);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(xy1||(xy1={}));var cH0;((A)=>{A.visit=h1((B,Q)=>{if(B.message!==void 0)return Q.message(B.message);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(cH0||(cH0={}));var vc6={CONTENT_FILTERED:"content_filtered",END_TURN:"end_turn",GUARDRAIL_INTERVENED:"guardrail_intervened",MAX_TOKENS:"max_tokens",STOP_SEQUENCE:"stop_sequence",TOOL_USE:"tool_use"},PJB=class A extends hV{static{h1(this,"ModelErrorException")}name="ModelErrorException";$fault="client";originalStatusCode;resourceName;constructor(B){super({name:"ModelErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.originalStatusCode=B.originalStatusCode,this.resourceName=B.resourceName}},SJB=class A extends hV{static{h1(this,"ModelNotReadyException")}name="ModelNotReadyException";$fault="client";$retryable={};constructor(B){super({name:"ModelNotReadyException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},jJB=class A extends hV{static{h1(this,"ModelTimeoutException")}name="ModelTimeoutException";$fault="client";constructor(B){super({name:"ModelTimeoutException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},bc6={ASYNC:"async",SYNC:"sync"},lH0;((A)=>{A.visit=h1((B,Q)=>{if(B.text!==void 0)return Q.text(B.text);if(B.redactedContent!==void 0)return Q.redactedContent(B.redactedContent);if(B.signature!==void 0)return Q.signature(B.signature);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(lH0||(lH0={}));var pH0;((A)=>{A.visit=h1((B,Q)=>{if(B.text!==void 0)return Q.text(B.text);if(B.toolUse!==void 0)return Q.toolUse(B.toolUse);if(B.reasoningContent!==void 0)return Q.reasoningContent(B.reasoningContent);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(pH0||(pH0={}));var iH0;((A)=>{A.visit=h1((B,Q)=>{if(B.toolUse!==void 0)return Q.toolUse(B.toolUse);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(iH0||(iH0={}));var kJB=class A extends hV{static{h1(this,"ModelStreamErrorException")}name="ModelStreamErrorException";$fault="client";originalStatusCode;originalMessage;constructor(B){super({name:"ModelStreamErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.originalStatusCode=B.originalStatusCode,this.originalMessage=B.originalMessage}},nH0;((A)=>{A.visit=h1((B,Q)=>{if(B.messageStart!==void 0)return Q.messageStart(B.messageStart);if(B.contentBlockStart!==void 0)return Q.contentBlockStart(B.contentBlockStart);if(B.contentBlockDelta!==void 0)return Q.contentBlockDelta(B.contentBlockDelta);if(B.contentBlockStop!==void 0)return Q.contentBlockStop(B.contentBlockStop);if(B.messageStop!==void 0)return Q.messageStop(B.messageStop);if(B.metadata!==void 0)return Q.metadata(B.metadata);if(B.internalServerException!==void 0)return Q.internalServerException(B.internalServerException);if(B.modelStreamErrorException!==void 0)return Q.modelStreamErrorException(B.modelStreamErrorException);if(B.validationException!==void 0)return Q.validationException(B.validationException);if(B.throttlingException!==void 0)return Q.throttlingException(B.throttlingException);if(B.serviceUnavailableException!==void 0)return Q.serviceUnavailableException(B.serviceUnavailableException);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(nH0||(nH0={}));var fc6={DISABLED:"DISABLED",ENABLED:"ENABLED",ENABLED_FULL:"ENABLED_FULL"},vy1;((A)=>{A.visit=h1((B,Q)=>{if(B.chunk!==void 0)return Q.chunk(B.chunk);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(vy1||(vy1={}));var aH0;((A)=>{A.visit=h1((B,Q)=>{if(B.chunk!==void 0)return Q.chunk(B.chunk);if(B.internalServerException!==void 0)return Q.internalServerException(B.internalServerException);if(B.modelStreamErrorException!==void 0)return Q.modelStreamErrorException(B.modelStreamErrorException);if(B.validationException!==void 0)return Q.validationException(B.validationException);if(B.throttlingException!==void 0)return Q.throttlingException(B.throttlingException);if(B.modelTimeoutException!==void 0)return Q.modelTimeoutException(B.modelTimeoutException);if(B.serviceUnavailableException!==void 0)return Q.serviceUnavailableException(B.serviceUnavailableException);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(aH0||(aH0={}));var sH0;((A)=>{A.visit=h1((B,Q)=>{if(B.chunk!==void 0)return Q.chunk(B.chunk);if(B.internalServerException!==void 0)return Q.internalServerException(B.internalServerException);if(B.modelStreamErrorException!==void 0)return Q.modelStreamErrorException(B.modelStreamErrorException);if(B.validationException!==void 0)return Q.validationException(B.validationException);if(B.throttlingException!==void 0)return Q.throttlingException(B.throttlingException);if(B.modelTimeoutException!==void 0)return Q.modelTimeoutException(B.modelTimeoutException);if(B.serviceUnavailableException!==void 0)return Q.serviceUnavailableException(B.serviceUnavailableException);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(sH0||(sH0={}));var yJB=h1((A)=>({...A,...A.failureMessage&&{failureMessage:r1.SENSITIVE_STRING},...A.outputDataConfig&&{outputDataConfig:A.outputDataConfig}}),"GetAsyncInvokeResponseFilterSensitiveLog"),_JB=h1((A)=>({...A,...A.failureMessage&&{failureMessage:r1.SENSITIVE_STRING},...A.outputDataConfig&&{outputDataConfig:A.outputDataConfig}}),"AsyncInvokeSummaryFilterSensitiveLog"),xJB=h1((A)=>({...A,...A.asyncInvokeSummaries&&{asyncInvokeSummaries:A.asyncInvokeSummaries.map((B)=>_JB(B))}}),"ListAsyncInvokesResponseFilterSensitiveLog"),vJB=h1((A)=>({...A,...A.modelInput&&{modelInput:r1.SENSITIVE_STRING},...A.outputDataConfig&&{outputDataConfig:A.outputDataConfig}}),"StartAsyncInvokeRequestFilterSensitiveLog"),hc6=h1((A)=>{if(A.bytes!==void 0)return{bytes:A.bytes};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"GuardrailImageSourceFilterSensitiveLog"),gc6=h1((A)=>({...A,...A.source&&{source:r1.SENSITIVE_STRING}}),"GuardrailImageBlockFilterSensitiveLog"),bJB=h1((A)=>{if(A.text!==void 0)return{text:A.text};if(A.image!==void 0)return{image:r1.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"GuardrailContentBlockFilterSensitiveLog"),fJB=h1((A)=>({...A,...A.content&&{content:A.content.map((B)=>bJB(B))}}),"ApplyGuardrailRequestFilterSensitiveLog"),uc6=h1((A)=>{if(A.bytes!==void 0)return{bytes:A.bytes};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"GuardrailConverseImageSourceFilterSensitiveLog"),mc6=h1((A)=>({...A,...A.source&&{source:r1.SENSITIVE_STRING}}),"GuardrailConverseImageBlockFilterSensitiveLog"),tH0=h1((A)=>{if(A.text!==void 0)return{text:A.text};if(A.image!==void 0)return{image:r1.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"GuardrailConverseContentBlockFilterSensitiveLog"),dc6=h1((A)=>({...A}),"ReasoningTextBlockFilterSensitiveLog"),cc6=h1((A)=>{if(A.reasoningText!==void 0)return{reasoningText:r1.SENSITIVE_STRING};if(A.redactedContent!==void 0)return{redactedContent:A.redactedContent};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"ReasoningContentBlockFilterSensitiveLog"),hJB=h1((A)=>{if(A.text!==void 0)return{text:A.text};if(A.image!==void 0)return{image:A.image};if(A.document!==void 0)return{document:A.document};if(A.video!==void 0)return{video:A.video};if(A.toolUse!==void 0)return{toolUse:A.toolUse};if(A.toolResult!==void 0)return{toolResult:A.toolResult};if(A.guardContent!==void 0)return{guardContent:tH0(A.guardContent)};if(A.cachePoint!==void 0)return{cachePoint:A.cachePoint};if(A.reasoningContent!==void 0)return{reasoningContent:r1.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"ContentBlockFilterSensitiveLog"),fy1=h1((A)=>({...A,...A.content&&{content:A.content.map((B)=>hJB(B))}}),"MessageFilterSensitiveLog"),eH0=h1((A)=>{if(A.text!==void 0)return{text:A.text};if(A.guardContent!==void 0)return{guardContent:tH0(A.guardContent)};if(A.cachePoint!==void 0)return{cachePoint:A.cachePoint};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"SystemContentBlockFilterSensitiveLog"),gJB=h1((A)=>({...A,...A.messages&&{messages:A.messages.map((B)=>fy1(B))},...A.system&&{system:A.system.map((B)=>eH0(B))},...A.toolConfig&&{toolConfig:A.toolConfig},...A.promptVariables&&{promptVariables:r1.SENSITIVE_STRING},...A.requestMetadata&&{requestMetadata:r1.SENSITIVE_STRING}}),"ConverseRequestFilterSensitiveLog"),uJB=h1((A)=>{if(A.message!==void 0)return{message:fy1(A.message)};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"ConverseOutputFilterSensitiveLog"),mJB=h1((A)=>({...A,...A.output&&{output:uJB(A.output)}}),"ConverseResponseFilterSensitiveLog"),dJB=h1((A)=>({...A,...A.messages&&{messages:A.messages.map((B)=>fy1(B))},...A.system&&{system:A.system.map((B)=>eH0(B))},...A.toolConfig&&{toolConfig:A.toolConfig},...A.promptVariables&&{promptVariables:r1.SENSITIVE_STRING},...A.requestMetadata&&{requestMetadata:r1.SENSITIVE_STRING}}),"ConverseStreamRequestFilterSensitiveLog"),lc6=h1((A)=>{if(A.text!==void 0)return{text:A.text};if(A.redactedContent!==void 0)return{redactedContent:A.redactedContent};if(A.signature!==void 0)return{signature:A.signature};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"ReasoningContentBlockDeltaFilterSensitiveLog"),cJB=h1((A)=>{if(A.text!==void 0)return{text:A.text};if(A.toolUse!==void 0)return{toolUse:A.toolUse};if(A.reasoningContent!==void 0)return{reasoningContent:r1.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"ContentBlockDeltaFilterSensitiveLog"),lJB=h1((A)=>({...A,...A.delta&&{delta:cJB(A.delta)}}),"ContentBlockDeltaEventFilterSensitiveLog"),pc6=h1((A)=>{if(A.messageStart!==void 0)return{messageStart:A.messageStart};if(A.contentBlockStart!==void 0)return{contentBlockStart:A.contentBlockStart};if(A.contentBlockDelta!==void 0)return{contentBlockDelta:lJB(A.contentBlockDelta)};if(A.contentBlockStop!==void 0)return{contentBlockStop:A.contentBlockStop};if(A.messageStop!==void 0)return{messageStop:A.messageStop};if(A.metadata!==void 0)return{metadata:A.metadata};if(A.internalServerException!==void 0)return{internalServerException:A.internalServerException};if(A.modelStreamErrorException!==void 0)return{modelStreamErrorException:A.modelStreamErrorException};if(A.validationException!==void 0)return{validationException:A.validationException};if(A.throttlingException!==void 0)return{throttlingException:A.throttlingException};if(A.serviceUnavailableException!==void 0)return{serviceUnavailableException:A.serviceUnavailableException};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"ConverseStreamOutputFilterSensitiveLog"),pJB=h1((A)=>({...A,...A.stream&&{stream:"STREAMING_CONTENT"}}),"ConverseStreamResponseFilterSensitiveLog"),iJB=h1((A)=>({...A,...A.body&&{body:r1.SENSITIVE_STRING}}),"InvokeModelRequestFilterSensitiveLog"),nJB=h1((A)=>({...A,...A.body&&{body:r1.SENSITIVE_STRING}}),"InvokeModelResponseFilterSensitiveLog"),ic6=h1((A)=>({...A,...A.bytes&&{bytes:r1.SENSITIVE_STRING}}),"BidirectionalInputPayloadPartFilterSensitiveLog"),nc6=h1((A)=>{if(A.chunk!==void 0)return{chunk:r1.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"InvokeModelWithBidirectionalStreamInputFilterSensitiveLog"),aJB=h1((A)=>({...A,...A.body&&{body:"STREAMING_CONTENT"}}),"InvokeModelWithBidirectionalStreamRequestFilterSensitiveLog"),ac6=h1((A)=>({...A,...A.bytes&&{bytes:r1.SENSITIVE_STRING}}),"BidirectionalOutputPayloadPartFilterSensitiveLog"),sc6=h1((A)=>{if(A.chunk!==void 0)return{chunk:r1.SENSITIVE_STRING};if(A.internalServerException!==void 0)return{internalServerException:A.internalServerException};if(A.modelStreamErrorException!==void 0)return{modelStreamErrorException:A.modelStreamErrorException};if(A.validationException!==void 0)return{validationException:A.validationException};if(A.throttlingException!==void 0)return{throttlingException:A.throttlingException};if(A.modelTimeoutException!==void 0)return{modelTimeoutException:A.modelTimeoutException};if(A.serviceUnavailableException!==void 0)return{serviceUnavailableException:A.serviceUnavailableException};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"InvokeModelWithBidirectionalStreamOutputFilterSensitiveLog"),sJB=h1((A)=>({...A,...A.body&&{body:"STREAMING_CONTENT"}}),"InvokeModelWithBidirectionalStreamResponseFilterSensitiveLog"),rJB=h1((A)=>({...A,...A.body&&{body:r1.SENSITIVE_STRING}}),"InvokeModelWithResponseStreamRequestFilterSensitiveLog"),rc6=h1((A)=>({...A,...A.bytes&&{bytes:r1.SENSITIVE_STRING}}),"PayloadPartFilterSensitiveLog"),oc6=h1((A)=>{if(A.chunk!==void 0)return{chunk:r1.SENSITIVE_STRING};if(A.internalServerException!==void 0)return{internalServerException:A.internalServerException};if(A.modelStreamErrorException!==void 0)return{modelStreamErrorException:A.modelStreamErrorException};if(A.validationException!==void 0)return{validationException:A.validationException};if(A.throttlingException!==void 0)return{throttlingException:A.throttlingException};if(A.modelTimeoutException!==void 0)return{modelTimeoutException:A.modelTimeoutException};if(A.serviceUnavailableException!==void 0)return{serviceUnavailableException:A.serviceUnavailableException};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"ResponseStreamFilterSensitiveLog"),oJB=h1((A)=>({...A,...A.body&&{body:"STREAMING_CONTENT"}}),"InvokeModelWithResponseStreamResponseFilterSensitiveLog"),Z8=WI(),tc6=sQ1(),ec6=h1(async(A,B)=>{let Q=bK.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/guardrail/{guardrailIdentifier}/version/{guardrailVersion}/apply"),Q.p("guardrailIdentifier",()=>A.guardrailIdentifier,"{guardrailIdentifier}",!1),Q.p("guardrailVersion",()=>A.guardrailVersion,"{guardrailVersion}",!1);let Z;return Z=JSON.stringify(r1.take(A,{content:h1((G)=>dl6(G,B),"content"),outputScope:[],source:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_ApplyGuardrailCommand"),Al6=h1(async(A,B)=>{let Q=bK.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/model/{modelId}/converse"),Q.p("modelId",()=>A.modelId,"{modelId}",!1);let Z;return Z=JSON.stringify(r1.take(A,{additionalModelRequestFields:h1((G)=>fD1(G,B),"additionalModelRequestFields"),additionalModelResponseFieldPaths:h1((G)=>r1._json(G),"additionalModelResponseFieldPaths"),guardrailConfig:h1((G)=>r1._json(G),"guardrailConfig"),inferenceConfig:h1((G)=>YXB(G,B),"inferenceConfig"),messages:h1((G)=>WXB(G,B),"messages"),performanceConfig:h1((G)=>r1._json(G),"performanceConfig"),promptVariables:h1((G)=>r1._json(G),"promptVariables"),requestMetadata:h1((G)=>r1._json(G),"requestMetadata"),system:h1((G)=>JXB(G,B),"system"),toolConfig:h1((G)=>XXB(G,B),"toolConfig")})),Q.m("POST").h(D).b(Z),Q.build()},"se_ConverseCommand"),Bl6=h1(async(A,B)=>{let Q=bK.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/model/{modelId}/converse-stream"),Q.p("modelId",()=>A.modelId,"{modelId}",!1);let Z;return Z=JSON.stringify(r1.take(A,{additionalModelRequestFields:h1((G)=>fD1(G,B),"additionalModelRequestFields"),additionalModelResponseFieldPaths:h1((G)=>r1._json(G),"additionalModelResponseFieldPaths"),guardrailConfig:h1((G)=>r1._json(G),"guardrailConfig"),inferenceConfig:h1((G)=>YXB(G,B),"inferenceConfig"),messages:h1((G)=>WXB(G,B),"messages"),performanceConfig:h1((G)=>r1._json(G),"performanceConfig"),promptVariables:h1((G)=>r1._json(G),"promptVariables"),requestMetadata:h1((G)=>r1._json(G),"requestMetadata"),system:h1((G)=>JXB(G,B),"system"),toolConfig:h1((G)=>XXB(G,B),"toolConfig")})),Q.m("POST").h(D).b(Z),Q.build()},"se_ConverseStreamCommand"),Ql6=h1(async(A,B)=>{let Q=bK.requestBuilder(A,B),D={};Q.bp("/async-invoke/{invocationArn}"),Q.p("invocationArn",()=>A.invocationArn,"{invocationArn}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetAsyncInvokeCommand"),Dl6=h1(async(A,B)=>{let Q=bK.requestBuilder(A,B),D=r1.map({},r1.isSerializableHeaderValue,{[Gz0]:A[gy1]||"application/octet-stream",[rH0]:A[rH0],[MXB]:A[qXB],[NXB]:A[wXB],[LXB]:A[$XB],[my1]:A[uy1]});Q.bp("/model/{modelId}/invoke"),Q.p("modelId",()=>A.modelId,"{modelId}",!1);let Z;if(A.body!==void 0)Z=A.body;return Q.m("POST").h(D).b(Z),Q.build()},"se_InvokeModelCommand"),Zl6=h1(async(A,B)=>{let Q=bK.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/model/{modelId}/invoke-with-bidirectional-stream"),Q.p("modelId",()=>A.modelId,"{modelId}",!1);let Z;if(A.body!==void 0)Z=Ml6(A.body,B);return Q.m("POST").h(D).b(Z),Q.build()},"se_InvokeModelWithBidirectionalStreamCommand"),Gl6=h1(async(A,B)=>{let Q=bK.requestBuilder(A,B),D=r1.map({},r1.isSerializableHeaderValue,{[Gz0]:A[gy1]||"application/octet-stream",[hp6]:A[rH0],[MXB]:A[qXB],[NXB]:A[wXB],[LXB]:A[$XB],[my1]:A[uy1]});Q.bp("/model/{modelId}/invoke-with-response-stream"),Q.p("modelId",()=>A.modelId,"{modelId}",!1);let Z;if(A.body!==void 0)Z=A.body;return Q.m("POST").h(D).b(Z),Q.build()},"se_InvokeModelWithResponseStreamCommand"),Fl6=h1(async(A,B)=>{let Q=bK.requestBuilder(A,B),D={};Q.bp("/async-invoke");let Z=r1.map({[zJB]:[()=>A.submitTimeAfter!==void 0,()=>r1.serializeDateTime(A[zJB]).toString()],[EJB]:[()=>A.submitTimeBefore!==void 0,()=>r1.serializeDateTime(A[EJB]).toString()],[KJB]:[,A[KJB]],[XJB]:[()=>A.maxResults!==void 0,()=>A[XJB].toString()],[VJB]:[,A[VJB]],[CJB]:[,A[CJB]],[HJB]:[,A[HJB]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAsyncInvokesCommand"),Il6=h1(async(A,B)=>{let Q=bK.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/async-invoke");let Z;return Z=JSON.stringify(r1.take(A,{clientRequestToken:[!0,(G)=>G??tc6.v4()],modelId:[],modelInput:h1((G)=>sl6(G,B),"modelInput"),outputDataConfig:h1((G)=>r1._json(G),"outputDataConfig"),tags:h1((G)=>r1._json(G),"tags")})),Q.m("POST").h(D).b(Z),Q.build()},"se_StartAsyncInvokeCommand"),Yl6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return fP(A,B);let Q=r1.map({$metadata:BG(A)}),D=r1.expectNonNull(r1.expectObject(await Z8.parseJsonBody(A.body,B)),"body"),Z=r1.take(D,{action:r1.expectString,actionReason:r1.expectString,assessments:h1((G)=>HXB(G,B),"assessments"),guardrailCoverage:r1._json,outputs:r1._json,usage:r1._json});return Object.assign(Q,Z),Q},"de_ApplyGuardrailCommand"),Wl6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return fP(A,B);let Q=r1.map({$metadata:BG(A)}),D=r1.expectNonNull(r1.expectObject(await Z8.parseJsonBody(A.body,B)),"body"),Z=r1.take(D,{additionalModelResponseFields:h1((G)=>hy1(G,B),"additionalModelResponseFields"),metrics:r1._json,output:h1((G)=>Kp6(Z8.awsExpectUnion(G),B),"output"),performanceConfig:r1._json,stopReason:r1.expectString,trace:h1((G)=>Ep6(G,B),"trace"),usage:r1._json});return Object.assign(Q,Z),Q},"de_ConverseCommand"),Jl6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return fP(A,B);let Q=r1.map({$metadata:BG(A)}),D=A.body;return Q.stream=Ol6(D,B),Q},"de_ConverseStreamCommand"),Xl6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return fP(A,B);let Q=r1.map({$metadata:BG(A)}),D=r1.expectNonNull(r1.expectObject(await Z8.parseJsonBody(A.body,B)),"body"),Z=r1.take(D,{clientRequestToken:r1.expectString,endTime:h1((G)=>r1.expectNonNull(r1.parseRfc3339DateTimeWithOffset(G)),"endTime"),failureMessage:r1.expectString,invocationArn:r1.expectString,lastModifiedTime:h1((G)=>r1.expectNonNull(r1.parseRfc3339DateTimeWithOffset(G)),"lastModifiedTime"),modelArn:r1.expectString,outputDataConfig:h1((G)=>r1._json(Z8.awsExpectUnion(G)),"outputDataConfig"),status:r1.expectString,submitTime:h1((G)=>r1.expectNonNull(r1.parseRfc3339DateTimeWithOffset(G)),"submitTime")});return Object.assign(Q,Z),Q},"de_GetAsyncInvokeCommand"),Vl6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return fP(A,B);let Q=r1.map({$metadata:BG(A),[gy1]:[,A.headers[Gz0]],[uy1]:[,A.headers[my1]]}),D=await r1.collectBody(A.body,B);return Q.body=D,Q},"de_InvokeModelCommand"),Cl6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return fP(A,B);let Q=r1.map({$metadata:BG(A)}),D=A.body;return Q.body=Tl6(D,B),Q},"de_InvokeModelWithBidirectionalStreamCommand"),Kl6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return fP(A,B);let Q=r1.map({$metadata:BG(A),[gy1]:[,A.headers[gp6]],[uy1]:[,A.headers[my1]]}),D=A.body;return Q.body=Pl6(D,B),Q},"de_InvokeModelWithResponseStreamCommand"),Hl6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return fP(A,B);let Q=r1.map({$metadata:BG(A)}),D=r1.expectNonNull(r1.expectObject(await Z8.parseJsonBody(A.body,B)),"body"),Z=r1.take(D,{asyncInvokeSummaries:h1((G)=>Ip6(G,B),"asyncInvokeSummaries"),nextToken:r1.expectString});return Object.assign(Q,Z),Q},"de_ListAsyncInvokesCommand"),zl6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return fP(A,B);let Q=r1.map({$metadata:BG(A)}),D=r1.expectNonNull(r1.expectObject(await Z8.parseJsonBody(A.body,B)),"body"),Z=r1.take(D,{invocationArn:r1.expectString});return Object.assign(Q,Z),Q},"de_StartAsyncInvokeCommand"),fP=h1(async(A,B)=>{let Q={...A,body:await Z8.parseJsonErrorBody(A.body,B)},D=Z8.loadRestJsonErrorCode(A,Q.body);switch(D){case"AccessDeniedException":case"com.amazonaws.bedrockruntime#AccessDeniedException":throw await Ul6(Q,B);case"InternalServerException":case"com.amazonaws.bedrockruntime#InternalServerException":throw await tJB(Q,B);case"ResourceNotFoundException":case"com.amazonaws.bedrockruntime#ResourceNotFoundException":throw await Nl6(Q,B);case"ServiceQuotaExceededException":case"com.amazonaws.bedrockruntime#ServiceQuotaExceededException":throw await Ll6(Q,B);case"ThrottlingException":case"com.amazonaws.bedrockruntime#ThrottlingException":throw await QXB(Q,B);case"ValidationException":case"com.amazonaws.bedrockruntime#ValidationException":throw await DXB(Q,B);case"ModelErrorException":case"com.amazonaws.bedrockruntime#ModelErrorException":throw await $l6(Q,B);case"ModelNotReadyException":case"com.amazonaws.bedrockruntime#ModelNotReadyException":throw await ql6(Q,B);case"ModelTimeoutException":case"com.amazonaws.bedrockruntime#ModelTimeoutException":throw await AXB(Q,B);case"ServiceUnavailableException":case"com.amazonaws.bedrockruntime#ServiceUnavailableException":throw await BXB(Q,B);case"ModelStreamErrorException":case"com.amazonaws.bedrockruntime#ModelStreamErrorException":throw await eJB(Q,B);case"ConflictException":case"com.amazonaws.bedrockruntime#ConflictException":throw await wl6(Q,B);default:let Z=Q.body;return El6({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),El6=r1.withBaseException(hV),Ul6=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new $JB({$metadata:BG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_AccessDeniedExceptionRes"),wl6=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new MJB({$metadata:BG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ConflictExceptionRes"),tJB=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new qJB({$metadata:BG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_InternalServerExceptionRes"),$l6=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString,originalStatusCode:r1.expectInt32,resourceName:r1.expectString});Object.assign(Q,Z);let G=new PJB({$metadata:BG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ModelErrorExceptionRes"),ql6=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new SJB({$metadata:BG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ModelNotReadyExceptionRes"),eJB=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString,originalMessage:r1.expectString,originalStatusCode:r1.expectInt32});Object.assign(Q,Z);let G=new kJB({$metadata:BG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ModelStreamErrorExceptionRes"),AXB=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new jJB({$metadata:BG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ModelTimeoutExceptionRes"),Nl6=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new RJB({$metadata:BG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ResourceNotFoundExceptionRes"),Ll6=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new OJB({$metadata:BG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ServiceQuotaExceededExceptionRes"),BXB=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new TJB({$metadata:BG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ServiceUnavailableExceptionRes"),QXB=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new NJB({$metadata:BG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ThrottlingExceptionRes"),DXB=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new LJB({$metadata:BG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ValidationExceptionRes"),Ml6=h1((A,B)=>{let Q=h1((D)=>vy1.visit(D,{chunk:h1((Z)=>Rl6(Z,B),"chunk"),_:h1((Z)=>Z,"_")}),"eventMarshallingVisitor");return B.eventStreamMarshaller.serialize(A,Q)},"se_InvokeModelWithBidirectionalStreamInput"),Rl6=h1((A,B)=>{let Q={":event-type":{type:"string",value:"chunk"},":message-type":{type:"string",value:"event"},":content-type":{type:"string",value:"application/json"}},D=new Uint8Array;return D=fl6(A,B),D=B.utf8Decoder(JSON.stringify(D)),{headers:Q,body:D}},"se_BidirectionalInputPayloadPart_event"),Ol6=h1((A,B)=>{return B.eventStreamMarshaller.deserialize(A,async(Q)=>{if(Q.messageStart!=null)return{messageStart:await xl6(Q.messageStart,B)};if(Q.contentBlockStart!=null)return{contentBlockStart:await kl6(Q.contentBlockStart,B)};if(Q.contentBlockDelta!=null)return{contentBlockDelta:await jl6(Q.contentBlockDelta,B)};if(Q.contentBlockStop!=null)return{contentBlockStop:await yl6(Q.contentBlockStop,B)};if(Q.messageStop!=null)return{messageStop:await vl6(Q.messageStop,B)};if(Q.metadata!=null)return{metadata:await _l6(Q.metadata,B)};if(Q.internalServerException!=null)return{internalServerException:await Az0(Q.internalServerException,B)};if(Q.modelStreamErrorException!=null)return{modelStreamErrorException:await Bz0(Q.modelStreamErrorException,B)};if(Q.validationException!=null)return{validationException:await Zz0(Q.validationException,B)};if(Q.throttlingException!=null)return{throttlingException:await Dz0(Q.throttlingException,B)};if(Q.serviceUnavailableException!=null)return{serviceUnavailableException:await Qz0(Q.serviceUnavailableException,B)};return{$unknown:A}})},"de_ConverseStreamOutput"),Tl6=h1((A,B)=>{return B.eventStreamMarshaller.deserialize(A,async(Q)=>{if(Q.chunk!=null)return{chunk:await Sl6(Q.chunk,B)};if(Q.internalServerException!=null)return{internalServerException:await Az0(Q.internalServerException,B)};if(Q.modelStreamErrorException!=null)return{modelStreamErrorException:await Bz0(Q.modelStreamErrorException,B)};if(Q.validationException!=null)return{validationException:await Zz0(Q.validationException,B)};if(Q.throttlingException!=null)return{throttlingException:await Dz0(Q.throttlingException,B)};if(Q.modelTimeoutException!=null)return{modelTimeoutException:await ZXB(Q.modelTimeoutException,B)};if(Q.serviceUnavailableException!=null)return{serviceUnavailableException:await Qz0(Q.serviceUnavailableException,B)};return{$unknown:A}})},"de_InvokeModelWithBidirectionalStreamOutput"),Pl6=h1((A,B)=>{return B.eventStreamMarshaller.deserialize(A,async(Q)=>{if(Q.chunk!=null)return{chunk:await bl6(Q.chunk,B)};if(Q.internalServerException!=null)return{internalServerException:await Az0(Q.internalServerException,B)};if(Q.modelStreamErrorException!=null)return{modelStreamErrorException:await Bz0(Q.modelStreamErrorException,B)};if(Q.validationException!=null)return{validationException:await Zz0(Q.validationException,B)};if(Q.throttlingException!=null)return{throttlingException:await Dz0(Q.throttlingException,B)};if(Q.modelTimeoutException!=null)return{modelTimeoutException:await ZXB(Q.modelTimeoutException,B)};if(Q.serviceUnavailableException!=null)return{serviceUnavailableException:await Qz0(Q.serviceUnavailableException,B)};return{$unknown:A}})},"de_ResponseStream"),Sl6=h1(async(A,B)=>{let Q={},D=await Z8.parseJsonBody(A.body,B);return Object.assign(Q,Wp6(D,B)),Q},"de_BidirectionalOutputPayloadPart_event"),jl6=h1(async(A,B)=>{let Q={},D=await Z8.parseJsonBody(A.body,B);return Object.assign(Q,Vp6(D,B)),Q},"de_ContentBlockDeltaEvent_event"),kl6=h1(async(A,B)=>{let Q={},D=await Z8.parseJsonBody(A.body,B);return Object.assign(Q,r1._json(D)),Q},"de_ContentBlockStartEvent_event"),yl6=h1(async(A,B)=>{let Q={},D=await Z8.parseJsonBody(A.body,B);return Object.assign(Q,r1._json(D)),Q},"de_ContentBlockStopEvent_event"),_l6=h1(async(A,B)=>{let Q={},D=await Z8.parseJsonBody(A.body,B);return Object.assign(Q,Hp6(D,B)),Q},"de_ConverseStreamMetadataEvent_event"),Az0=h1(async(A,B)=>{let Q={...A,body:await Z8.parseJsonBody(A.body,B)};return tJB(Q,B)},"de_InternalServerException_event"),xl6=h1(async(A,B)=>{let Q={},D=await Z8.parseJsonBody(A.body,B);return Object.assign(Q,r1._json(D)),Q},"de_MessageStartEvent_event"),vl6=h1(async(A,B)=>{let Q={},D=await Z8.parseJsonBody(A.body,B);return Object.assign(Q,Sp6(D,B)),Q},"de_MessageStopEvent_event"),Bz0=h1(async(A,B)=>{let Q={...A,body:await Z8.parseJsonBody(A.body,B)};return eJB(Q,B)},"de_ModelStreamErrorException_event"),ZXB=h1(async(A,B)=>{let Q={...A,body:await Z8.parseJsonBody(A.body,B)};return AXB(Q,B)},"de_ModelTimeoutException_event"),bl6=h1(async(A,B)=>{let Q={},D=await Z8.parseJsonBody(A.body,B);return Object.assign(Q,jp6(D,B)),Q},"de_PayloadPart_event"),Qz0=h1(async(A,B)=>{let Q={...A,body:await Z8.parseJsonBody(A.body,B)};return BXB(Q,B)},"de_ServiceUnavailableException_event"),Dz0=h1(async(A,B)=>{let Q={...A,body:await Z8.parseJsonBody(A.body,B)};return QXB(Q,B)},"de_ThrottlingException_event"),Zz0=h1(async(A,B)=>{let Q={...A,body:await Z8.parseJsonBody(A.body,B)};return DXB(Q,B)},"de_ValidationException_event"),fl6=h1((A,B)=>{return r1.take(A,{bytes:B.base64Encoder})},"se_BidirectionalInputPayloadPart"),hl6=h1((A,B)=>{return ky1.visit(A,{cachePoint:h1((Q)=>({cachePoint:r1._json(Q)}),"cachePoint"),document:h1((Q)=>({document:GXB(Q,B)}),"document"),guardContent:h1((Q)=>({guardContent:FXB(Q,B)}),"guardContent"),image:h1((Q)=>({image:IXB(Q,B)}),"image"),reasoningContent:h1((Q)=>({reasoningContent:rl6(Q,B)}),"reasoningContent"),text:h1((Q)=>({text:Q}),"text"),toolResult:h1((Q)=>({toolResult:Ap6(Q,B)}),"toolResult"),toolUse:h1((Q)=>({toolUse:Gp6(Q,B)}),"toolUse"),video:h1((Q)=>({video:VXB(Q,B)}),"video"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_ContentBlock"),gl6=h1((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return hl6(Q,B)})},"se_ContentBlocks"),GXB=h1((A,B)=>{return r1.take(A,{format:[],name:[],source:h1((Q)=>ul6(Q,B),"source")})},"se_DocumentBlock"),ul6=h1((A,B)=>{return My1.visit(A,{bytes:h1((Q)=>({bytes:B.base64Encoder(Q)}),"bytes"),s3Location:h1((Q)=>({s3Location:r1._json(Q)}),"s3Location"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_DocumentSource"),ml6=h1((A,B)=>{return Ly1.visit(A,{image:h1((Q)=>({image:pl6(Q,B)}),"image"),text:h1((Q)=>({text:r1._json(Q)}),"text"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_GuardrailContentBlock"),dl6=h1((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return ml6(Q,B)})},"se_GuardrailContentBlockList"),FXB=h1((A,B)=>{return Oy1.visit(A,{image:h1((Q)=>({image:cl6(Q,B)}),"image"),text:h1((Q)=>({text:r1._json(Q)}),"text"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_GuardrailConverseContentBlock"),cl6=h1((A,B)=>{return r1.take(A,{format:[],source:h1((Q)=>ll6(Q,B),"source")})},"se_GuardrailConverseImageBlock"),ll6=h1((A,B)=>{return Ry1.visit(A,{bytes:h1((Q)=>({bytes:B.base64Encoder(Q)}),"bytes"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_GuardrailConverseImageSource"),pl6=h1((A,B)=>{return r1.take(A,{format:[],source:h1((Q)=>il6(Q,B),"source")})},"se_GuardrailImageBlock"),il6=h1((A,B)=>{return Ny1.visit(A,{bytes:h1((Q)=>({bytes:B.base64Encoder(Q)}),"bytes"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_GuardrailImageSource"),IXB=h1((A,B)=>{return r1.take(A,{format:[],source:h1((Q)=>nl6(Q,B),"source")})},"se_ImageBlock"),nl6=h1((A,B)=>{return Ty1.visit(A,{bytes:h1((Q)=>({bytes:B.base64Encoder(Q)}),"bytes"),s3Location:h1((Q)=>({s3Location:r1._json(Q)}),"s3Location"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_ImageSource"),YXB=h1((A,B)=>{return r1.take(A,{maxTokens:[],stopSequences:r1._json,temperature:r1.serializeFloat,topP:r1.serializeFloat})},"se_InferenceConfiguration"),al6=h1((A,B)=>{return r1.take(A,{content:h1((Q)=>gl6(Q,B),"content"),role:[]})},"se_Message"),WXB=h1((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return al6(Q,B)})},"se_Messages"),sl6=h1((A,B)=>{return A},"se_ModelInputPayload"),rl6=h1((A,B)=>{return Py1.visit(A,{reasoningText:h1((Q)=>({reasoningText:r1._json(Q)}),"reasoningText"),redactedContent:h1((Q)=>({redactedContent:B.base64Encoder(Q)}),"redactedContent"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_ReasoningContentBlock"),ol6=h1((A,B)=>{return yy1.visit(A,{cachePoint:h1((Q)=>({cachePoint:r1._json(Q)}),"cachePoint"),guardContent:h1((Q)=>({guardContent:FXB(Q,B)}),"guardContent"),text:h1((Q)=>({text:Q}),"text"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_SystemContentBlock"),JXB=h1((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return ol6(Q,B)})},"se_SystemContentBlocks"),tl6=h1((A,B)=>{return xy1.visit(A,{cachePoint:h1((Q)=>({cachePoint:r1._json(Q)}),"cachePoint"),toolSpec:h1((Q)=>({toolSpec:Zp6(Q,B)}),"toolSpec"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_Tool"),XXB=h1((A,B)=>{return r1.take(A,{toolChoice:r1._json,tools:h1((Q)=>Dp6(Q,B),"tools")})},"se_ToolConfiguration"),el6=h1((A,B)=>{return _y1.visit(A,{json:h1((Q)=>({json:fD1(Q,B)}),"json"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_ToolInputSchema"),Ap6=h1((A,B)=>{return r1.take(A,{content:h1((Q)=>Qp6(Q,B),"content"),status:[],toolUseId:[]})},"se_ToolResultBlock"),Bp6=h1((A,B)=>{return jy1.visit(A,{document:h1((Q)=>({document:GXB(Q,B)}),"document"),image:h1((Q)=>({image:IXB(Q,B)}),"image"),json:h1((Q)=>({json:fD1(Q,B)}),"json"),text:h1((Q)=>({text:Q}),"text"),video:h1((Q)=>({video:VXB(Q,B)}),"video"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_ToolResultContentBlock"),Qp6=h1((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return Bp6(Q,B)})},"se_ToolResultContentBlocks"),Dp6=h1((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return tl6(Q,B)})},"se_Tools"),Zp6=h1((A,B)=>{return r1.take(A,{description:[],inputSchema:h1((Q)=>el6(Q,B),"inputSchema"),name:[]})},"se_ToolSpecification"),Gp6=h1((A,B)=>{return r1.take(A,{input:h1((Q)=>fD1(Q,B),"input"),name:[],toolUseId:[]})},"se_ToolUseBlock"),VXB=h1((A,B)=>{return r1.take(A,{format:[],source:h1((Q)=>Fp6(Q,B),"source")})},"se_VideoBlock"),Fp6=h1((A,B)=>{return Sy1.visit(A,{bytes:h1((Q)=>({bytes:B.base64Encoder(Q)}),"bytes"),s3Location:h1((Q)=>({s3Location:r1._json(Q)}),"s3Location"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_VideoSource"),fD1=h1((A,B)=>{return A},"se_Document"),Ip6=h1((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return Yp6(D,B)})},"de_AsyncInvokeSummaries"),Yp6=h1((A,B)=>{return r1.take(A,{clientRequestToken:r1.expectString,endTime:h1((Q)=>r1.expectNonNull(r1.parseRfc3339DateTimeWithOffset(Q)),"endTime"),failureMessage:r1.expectString,invocationArn:r1.expectString,lastModifiedTime:h1((Q)=>r1.expectNonNull(r1.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),modelArn:r1.expectString,outputDataConfig:h1((Q)=>r1._json(Z8.awsExpectUnion(Q)),"outputDataConfig"),status:r1.expectString,submitTime:h1((Q)=>r1.expectNonNull(r1.parseRfc3339DateTimeWithOffset(Q)),"submitTime")})},"de_AsyncInvokeSummary"),Wp6=h1((A,B)=>{return r1.take(A,{bytes:B.base64Decoder})},"de_BidirectionalOutputPayloadPart"),Jp6=h1((A,B)=>{if(A.cachePoint!=null)return{cachePoint:r1._json(A.cachePoint)};if(A.document!=null)return{document:CXB(A.document,B)};if(A.guardContent!=null)return{guardContent:Mp6(Z8.awsExpectUnion(A.guardContent),B)};if(A.image!=null)return{image:EXB(A.image,B)};if(A.reasoningContent!=null)return{reasoningContent:kp6(Z8.awsExpectUnion(A.reasoningContent),B)};if(r1.expectString(A.text)!==void 0)return{text:r1.expectString(A.text)};if(A.toolResult!=null)return{toolResult:_p6(A.toolResult,B)};if(A.toolUse!=null)return{toolUse:bp6(A.toolUse,B)};if(A.video!=null)return{video:UXB(A.video,B)};return{$unknown:Object.entries(A)[0]}},"de_ContentBlock"),Xp6=h1((A,B)=>{if(A.reasoningContent!=null)return{reasoningContent:yp6(Z8.awsExpectUnion(A.reasoningContent),B)};if(r1.expectString(A.text)!==void 0)return{text:r1.expectString(A.text)};if(A.toolUse!=null)return{toolUse:r1._json(A.toolUse)};return{$unknown:Object.entries(A)[0]}},"de_ContentBlockDelta"),Vp6=h1((A,B)=>{return r1.take(A,{contentBlockIndex:r1.expectInt32,delta:h1((Q)=>Xp6(Z8.awsExpectUnion(Q),B),"delta")})},"de_ContentBlockDeltaEvent"),Cp6=h1((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return Jp6(Z8.awsExpectUnion(D),B)})},"de_ContentBlocks"),Kp6=h1((A,B)=>{if(A.message!=null)return{message:Pp6(A.message,B)};return{$unknown:Object.entries(A)[0]}},"de_ConverseOutput"),Hp6=h1((A,B)=>{return r1.take(A,{metrics:r1._json,performanceConfig:r1._json,trace:h1((Q)=>zp6(Q,B),"trace"),usage:r1._json})},"de_ConverseStreamMetadataEvent"),zp6=h1((A,B)=>{return r1.take(A,{guardrail:h1((Q)=>zXB(Q,B),"guardrail"),promptRouter:r1._json})},"de_ConverseStreamTrace"),Ep6=h1((A,B)=>{return r1.take(A,{guardrail:h1((Q)=>zXB(Q,B),"guardrail"),promptRouter:r1._json})},"de_ConverseTrace"),CXB=h1((A,B)=>{return r1.take(A,{format:r1.expectString,name:r1.expectString,source:h1((Q)=>Up6(Z8.awsExpectUnion(Q),B),"source")})},"de_DocumentBlock"),Up6=h1((A,B)=>{if(A.bytes!=null)return{bytes:B.base64Decoder(A.bytes)};if(A.s3Location!=null)return{s3Location:r1._json(A.s3Location)};return{$unknown:Object.entries(A)[0]}},"de_DocumentSource"),KXB=h1((A,B)=>{return r1.take(A,{contentPolicy:r1._json,contextualGroundingPolicy:h1((Q)=>Lp6(Q,B),"contextualGroundingPolicy"),invocationMetrics:r1._json,sensitiveInformationPolicy:r1._json,topicPolicy:r1._json,wordPolicy:r1._json})},"de_GuardrailAssessment"),HXB=h1((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return KXB(D,B)})},"de_GuardrailAssessmentList"),wp6=h1((A,B)=>{return Object.entries(A).reduce((Q,[D,Z])=>{if(Z===null)return Q;return Q[D]=HXB(Z,B),Q},{})},"de_GuardrailAssessmentListMap"),$p6=h1((A,B)=>{return Object.entries(A).reduce((Q,[D,Z])=>{if(Z===null)return Q;return Q[D]=KXB(Z,B),Q},{})},"de_GuardrailAssessmentMap"),qp6=h1((A,B)=>{return r1.take(A,{action:r1.expectString,detected:r1.expectBoolean,score:r1.limitedParseDouble,threshold:r1.limitedParseDouble,type:r1.expectString})},"de_GuardrailContextualGroundingFilter"),Np6=h1((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return qp6(D,B)})},"de_GuardrailContextualGroundingFilters"),Lp6=h1((A,B)=>{return r1.take(A,{filters:h1((Q)=>Np6(Q,B),"filters")})},"de_GuardrailContextualGroundingPolicyAssessment"),Mp6=h1((A,B)=>{if(A.image!=null)return{image:Rp6(A.image,B)};if(A.text!=null)return{text:r1._json(A.text)};return{$unknown:Object.entries(A)[0]}},"de_GuardrailConverseContentBlock"),Rp6=h1((A,B)=>{return r1.take(A,{format:r1.expectString,source:h1((Q)=>Op6(Z8.awsExpectUnion(Q),B),"source")})},"de_GuardrailConverseImageBlock"),Op6=h1((A,B)=>{if(A.bytes!=null)return{bytes:B.base64Decoder(A.bytes)};return{$unknown:Object.entries(A)[0]}},"de_GuardrailConverseImageSource"),zXB=h1((A,B)=>{return r1.take(A,{actionReason:r1.expectString,inputAssessment:h1((Q)=>$p6(Q,B),"inputAssessment"),modelOutput:r1._json,outputAssessments:h1((Q)=>wp6(Q,B),"outputAssessments")})},"de_GuardrailTraceAssessment"),EXB=h1((A,B)=>{return r1.take(A,{format:r1.expectString,source:h1((Q)=>Tp6(Z8.awsExpectUnion(Q),B),"source")})},"de_ImageBlock"),Tp6=h1((A,B)=>{if(A.bytes!=null)return{bytes:B.base64Decoder(A.bytes)};if(A.s3Location!=null)return{s3Location:r1._json(A.s3Location)};return{$unknown:Object.entries(A)[0]}},"de_ImageSource"),Pp6=h1((A,B)=>{return r1.take(A,{content:h1((Q)=>Cp6(Q,B),"content"),role:r1.expectString})},"de_Message"),Sp6=h1((A,B)=>{return r1.take(A,{additionalModelResponseFields:h1((Q)=>hy1(Q,B),"additionalModelResponseFields"),stopReason:r1.expectString})},"de_MessageStopEvent"),jp6=h1((A,B)=>{return r1.take(A,{bytes:B.base64Decoder})},"de_PayloadPart"),kp6=h1((A,B)=>{if(A.reasoningText!=null)return{reasoningText:r1._json(A.reasoningText)};if(A.redactedContent!=null)return{redactedContent:B.base64Decoder(A.redactedContent)};return{$unknown:Object.entries(A)[0]}},"de_ReasoningContentBlock"),yp6=h1((A,B)=>{if(A.redactedContent!=null)return{redactedContent:B.base64Decoder(A.redactedContent)};if(r1.expectString(A.signature)!==void 0)return{signature:r1.expectString(A.signature)};if(r1.expectString(A.text)!==void 0)return{text:r1.expectString(A.text)};return{$unknown:Object.entries(A)[0]}},"de_ReasoningContentBlockDelta"),_p6=h1((A,B)=>{return r1.take(A,{content:h1((Q)=>vp6(Q,B),"content"),status:r1.expectString,toolUseId:r1.expectString})},"de_ToolResultBlock"),xp6=h1((A,B)=>{if(A.document!=null)return{document:CXB(A.document,B)};if(A.image!=null)return{image:EXB(A.image,B)};if(A.json!=null)return{json:hy1(A.json,B)};if(r1.expectString(A.text)!==void 0)return{text:r1.expectString(A.text)};if(A.video!=null)return{video:UXB(A.video,B)};return{$unknown:Object.entries(A)[0]}},"de_ToolResultContentBlock"),vp6=h1((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return xp6(Z8.awsExpectUnion(D),B)})},"de_ToolResultContentBlocks"),bp6=h1((A,B)=>{return r1.take(A,{input:h1((Q)=>hy1(Q,B),"input"),name:r1.expectString,toolUseId:r1.expectString})},"de_ToolUseBlock"),UXB=h1((A,B)=>{return r1.take(A,{format:r1.expectString,source:h1((Q)=>fp6(Z8.awsExpectUnion(Q),B),"source")})},"de_VideoBlock"),fp6=h1((A,B)=>{if(A.bytes!=null)return{bytes:B.base64Decoder(A.bytes)};if(A.s3Location!=null)return{s3Location:r1._json(A.s3Location)};return{$unknown:Object.entries(A)[0]}},"de_VideoSource"),hy1=h1((A,B)=>{return A},"de_Document"),BG=h1((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),rH0="accept",gy1="contentType",Gz0="content-type",wXB="guardrailIdentifier",$XB="guardrailVersion",XJB="maxResults",VJB="nextToken",uy1="performanceConfigLatency",CJB="sortBy",KJB="statusEquals",HJB="sortOrder",zJB="submitTimeAfter",EJB="submitTimeBefore",qXB="trace",hp6="x-amzn-bedrock-accept",gp6="x-amzn-bedrock-content-type",NXB="x-amzn-bedrock-guardrailidentifier",LXB="x-amzn-bedrock-guardrailversion",my1="x-amzn-bedrock-performanceconfig-latency",MXB="x-amzn-bedrock-trace",RXB=class extends r1.Command.classBuilder().ep(vP).m(function(A,B,Q,D){return[bP.getSerdePlugin(Q,this.serialize,this.deserialize),NM.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","ApplyGuardrail",{}).n("BedrockRuntimeClient","ApplyGuardrailCommand").f(fJB,void 0).ser(ec6).de(Yl6).build(){static{h1(this,"ApplyGuardrailCommand")}},OXB=class extends r1.Command.classBuilder().ep(vP).m(function(A,B,Q,D){return[bP.getSerdePlugin(Q,this.serialize,this.deserialize),NM.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","Converse",{}).n("BedrockRuntimeClient","ConverseCommand").f(gJB,mJB).ser(Al6).de(Wl6).build(){static{h1(this,"ConverseCommand")}},TXB=class extends r1.Command.classBuilder().ep(vP).m(function(A,B,Q,D){return[bP.getSerdePlugin(Q,this.serialize,this.deserialize),NM.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","ConverseStream",{eventStream:{output:!0}}).n("BedrockRuntimeClient","ConverseStreamCommand").f(dJB,pJB).ser(Bl6).de(Jl6).build(){static{h1(this,"ConverseStreamCommand")}},PXB=class extends r1.Command.classBuilder().ep(vP).m(function(A,B,Q,D){return[bP.getSerdePlugin(Q,this.serialize,this.deserialize),NM.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","GetAsyncInvoke",{}).n("BedrockRuntimeClient","GetAsyncInvokeCommand").f(void 0,yJB).ser(Ql6).de(Xl6).build(){static{h1(this,"GetAsyncInvokeCommand")}},SXB=class extends r1.Command.classBuilder().ep(vP).m(function(A,B,Q,D){return[bP.getSerdePlugin(Q,this.serialize,this.deserialize),NM.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","InvokeModel",{}).n("BedrockRuntimeClient","InvokeModelCommand").f(iJB,nJB).ser(Dl6).de(Vl6).build(){static{h1(this,"InvokeModelCommand")}},jXB=class extends r1.Command.classBuilder().ep(vP).m(function(A,B,Q,D){return[bP.getSerdePlugin(Q,this.serialize,this.deserialize),NM.getEndpointPlugin(Q,A.getEndpointParameterInstructions()),wJB.getEventStreamPlugin(Q)]}).s("AmazonBedrockFrontendService","InvokeModelWithBidirectionalStream",{eventStream:{input:!0,output:!0}}).n("BedrockRuntimeClient","InvokeModelWithBidirectionalStreamCommand").f(aJB,sJB).ser(Zl6).de(Cl6).build(){static{h1(this,"InvokeModelWithBidirectionalStreamCommand")}},kXB=class extends r1.Command.classBuilder().ep(vP).m(function(A,B,Q,D){return[bP.getSerdePlugin(Q,this.serialize,this.deserialize),NM.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","InvokeModelWithResponseStream",{eventStream:{output:!0}}).n("BedrockRuntimeClient","InvokeModelWithResponseStreamCommand").f(rJB,oJB).ser(Gl6).de(Kl6).build(){static{h1(this,"InvokeModelWithResponseStreamCommand")}},Fz0=class extends r1.Command.classBuilder().ep(vP).m(function(A,B,Q,D){return[bP.getSerdePlugin(Q,this.serialize,this.deserialize),NM.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","ListAsyncInvokes",{}).n("BedrockRuntimeClient","ListAsyncInvokesCommand").f(void 0,xJB).ser(Fl6).de(Hl6).build(){static{h1(this,"ListAsyncInvokesCommand")}},yXB=class extends r1.Command.classBuilder().ep(vP).m(function(A,B,Q,D){return[bP.getSerdePlugin(Q,this.serialize,this.deserialize),NM.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","StartAsyncInvoke",{}).n("BedrockRuntimeClient","StartAsyncInvokeCommand").f(vJB,void 0).ser(Il6).de(zl6).build(){static{h1(this,"StartAsyncInvokeCommand")}},up6={ApplyGuardrailCommand:RXB,ConverseCommand:OXB,ConverseStreamCommand:TXB,GetAsyncInvokeCommand:PXB,InvokeModelCommand:SXB,InvokeModelWithBidirectionalStreamCommand:jXB,InvokeModelWithResponseStreamCommand:kXB,ListAsyncInvokesCommand:Fz0,StartAsyncInvokeCommand:yXB},_XB=class extends oH0{static{h1(this,"BedrockRuntime")}};r1.createAggregatedClient(up6,_XB);var mp6=bK.createPaginator(oH0,Fz0,"nextToken","nextToken","maxResults")});
var vY2=E((Qz5,xY2)=>{var{create:fR4,defineProperty:Y81,getOwnPropertyDescriptor:hR4,getOwnPropertyNames:gR4,getPrototypeOf:uR4}=Object,mR4=Object.prototype.hasOwnProperty,Z_=(A,B)=>Y81(A,"name",{value:B,configurable:!0}),dR4=(A,B)=>{for(var Q in B)Y81(A,Q,{get:B[Q],enumerable:!0})},jY2=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of gR4(B))if(!mR4.call(A,Z)&&Z!==Q)Y81(A,Z,{get:()=>B[Z],enumerable:!(D=hR4(B,Z))||D.enumerable})}return A},kY2=(A,B,Q)=>(Q=A!=null?fR4(uR4(A)):{},jY2(B||!A||!A.__esModule?Y81(Q,"default",{value:A,enumerable:!0}):Q,A)),cR4=(A)=>jY2(Y81({},"__esModule",{value:!0}),A),yY2={};dR4(yY2,{fromSso:()=>_Y2,fromStatic:()=>rR4,nodeProvider:()=>oR4});xY2.exports=cR4(yY2);var lR4=300000,Q30="To refresh this SSO session run 'aws sso login' with the corresponding profile.",pR4=Z_(async(A,B={})=>{let{SSOOIDCClient:Q}=await Promise.resolve().then(()=>kY2(B30()));return new Q(Object.assign({},B.clientConfig??{},{region:A??B.clientConfig?.region,logger:B.clientConfig?.logger??B.parentClientConfig?.logger}))},"getSsoOidcClient"),iR4=Z_(async(A,B,Q={})=>{let{CreateTokenCommand:D}=await Promise.resolve().then(()=>kY2(B30()));return(await pR4(B,Q)).send(new D({clientId:A.clientId,clientSecret:A.clientSecret,refreshToken:A.refreshToken,grantType:"refresh_token"}))},"getNewSsoOidcToken"),yw=eB(),PY2=Z_((A)=>{if(A.expiration&&A.expiration.getTime()<Date.now())throw new yw.TokenProviderError(`Token is expired. ${Q30}`,!1)},"validateTokenExpiry"),jg=Z_((A,B,Q=!1)=>{if(typeof B==="undefined")throw new yw.TokenProviderError(`Value not present for '${A}' in SSO Token${Q?". Cannot refresh":""}. ${Q30}`,!1)},"validateTokenKey"),I81=e5(),nR4=J1("fs"),{writeFile:aR4}=nR4.promises,sR4=Z_((A,B)=>{let Q=I81.getSSOTokenFilepath(A),D=JSON.stringify(B,null,2);return aR4(Q,D)},"writeSSOTokenToFile"),SY2=new Date(0),_Y2=Z_((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/token-providers - fromSso");let D=await I81.parseKnownFiles(Q),Z=I81.getProfileName({profile:Q.profile??B?.profile}),G=D[Z];if(!G)throw new yw.TokenProviderError(`Profile '${Z}' could not be found in shared credentials file.`,!1);else if(!G.sso_session)throw new yw.TokenProviderError(`Profile '${Z}' is missing required property 'sso_session'.`);let F=G.sso_session,Y=(await I81.loadSsoSessionData(Q))[F];if(!Y)throw new yw.TokenProviderError(`Sso session '${F}' could not be found in shared credentials file.`,!1);for(let H of["sso_start_url","sso_region"])if(!Y[H])throw new yw.TokenProviderError(`Sso session '${F}' is missing required property '${H}'.`,!1);let{sso_start_url:W,sso_region:J}=Y,X;try{X=await I81.getSSOTokenFromFile(F)}catch(H){throw new yw.TokenProviderError(`The SSO session token associated with profile=${Z} was not found or is invalid. ${Q30}`,!1)}jg("accessToken",X.accessToken),jg("expiresAt",X.expiresAt);let{accessToken:V,expiresAt:C}=X,K={token:V,expiration:new Date(C)};if(K.expiration.getTime()-Date.now()>lR4)return K;if(Date.now()-SY2.getTime()<30000)return PY2(K),K;jg("clientId",X.clientId,!0),jg("clientSecret",X.clientSecret,!0),jg("refreshToken",X.refreshToken,!0);try{SY2.setTime(Date.now());let H=await iR4(X,J,Q);jg("accessToken",H.accessToken),jg("expiresIn",H.expiresIn);let z=new Date(Date.now()+H.expiresIn*1000);try{await sR4(F,{...X,accessToken:H.accessToken,expiresAt:z.toISOString(),refreshToken:H.refreshToken})}catch($){}return{token:H.accessToken,expiration:z}}catch(H){return PY2(K),K}},"fromSso"),rR4=Z_(({token:A,logger:B})=>async()=>{if(B?.debug("@aws-sdk/token-providers - fromStatic"),!A||!A.token)throw new yw.TokenProviderError("Please pass a valid token to fromStatic",!1);return A},"fromStatic"),oR4=Z_((A={})=>yw.memoize(yw.chain(_Y2(A),async()=>{throw new yw.TokenProviderError("Could not load token from any providers",!1)}),(B)=>B.expiration!==void 0&&B.expiration.getTime()-Date.now()<300000,(B)=>B.expiration!==void 0),"nodeProvider")});
var vYB=E((rs5,xYB)=>{var{defineProperty:Vy1,getOwnPropertyDescriptor:Vu6,getOwnPropertyNames:Cu6}=Object,Ku6=Object.prototype.hasOwnProperty,we=(A,B)=>Vy1(A,"name",{value:B,configurable:!0}),Hu6=(A,B)=>{for(var Q in B)Vy1(A,Q,{get:B[Q],enumerable:!0})},zu6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Cu6(B))if(!Ku6.call(A,Z)&&Z!==Q)Vy1(A,Z,{get:()=>B[Z],enumerable:!(D=Vu6(B,Z))||D.enumerable})}return A},Eu6=(A)=>zu6(Vy1({},"__esModule",{value:!0}),A),jYB={};Hu6(jYB,{EventStreamMarshaller:()=>_YB,eventStreamSerdeProvider:()=>Uu6});xYB.exports=Eu6(jYB);var SD1=qH0();function kYB(A){let B=0,Q=0,D=null,Z=null,G=we((I)=>{if(typeof I!=="number")throw new Error("Attempted to allocate an event message where size was not a number: "+I);B=I,Q=4,D=new Uint8Array(I),new DataView(D.buffer).setUint32(0,I,!1)},"allocateMessage"),F=we(async function*(){let I=A[Symbol.asyncIterator]();while(!0){let{value:Y,done:W}=await I.next();if(W){if(!B)return;else if(B===Q)yield D;else throw new Error("Truncated event message received.");return}let J=Y.length,X=0;while(X<J){if(!D){let C=J-X;if(!Z)Z=new Uint8Array(4);let K=Math.min(4-Q,C);if(Z.set(Y.slice(X,X+K),Q),Q+=K,X+=K,Q<4)break;G(new DataView(Z.buffer).getUint32(0,!1)),Z=null}let V=Math.min(B-Q,J-X);if(D.set(Y.slice(X,X+V),Q),Q+=V,X+=V,B&&B===Q)yield D,D=null,B=0,Q=0}}},"iterator");return{[Symbol.asyncIterator]:F}}we(kYB,"getChunkedStream");function yYB(A,B){return async function(Q){let{value:D}=Q.headers[":message-type"];if(D==="error"){let Z=new Error(Q.headers[":error-message"].value||"UnknownError");throw Z.name=Q.headers[":error-code"].value,Z}else if(D==="exception"){let Z=Q.headers[":exception-type"].value,G={[Z]:Q},F=await A(G);if(F.$unknown){let I=new Error(B(Q.body));throw I.name=Z,I}throw F[Z]}else if(D==="event"){let Z={[Q.headers[":event-type"].value]:Q},G=await A(Z);if(G.$unknown)return;return G}else throw Error(`Unrecognizable event type: ${Q.headers[":event-type"].value}`)}}we(yYB,"getMessageUnmarshaller");var _YB=class{static{we(this,"EventStreamMarshaller")}constructor({utf8Encoder:A,utf8Decoder:B}){this.eventStreamCodec=new SD1.EventStreamCodec(A,B),this.utfEncoder=A}deserialize(A,B){let Q=kYB(A);return new SD1.SmithyMessageDecoderStream({messageStream:new SD1.MessageDecoderStream({inputStream:Q,decoder:this.eventStreamCodec}),deserializer:yYB(B,this.utfEncoder)})}serialize(A,B){return new SD1.MessageEncoderStream({messageStream:new SD1.SmithyMessageEncoderStream({inputStream:A,serializer:B}),encoder:this.eventStreamCodec,includeEndFrame:!0})}},Uu6=we((A)=>new _YB(A),"eventStreamSerdeProvider")});
var wFB=E((zs5,UFB)=>{var{defineProperty:ik1,getOwnPropertyDescriptor:sf6,getOwnPropertyNames:rf6}=Object,of6=Object.prototype.hasOwnProperty,nk1=(A,B)=>ik1(A,"name",{value:B,configurable:!0}),tf6=(A,B)=>{for(var Q in B)ik1(A,Q,{get:B[Q],enumerable:!0})},ef6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of rf6(B))if(!of6.call(A,Z)&&Z!==Q)ik1(A,Z,{get:()=>B[Z],enumerable:!(D=sf6(B,Z))||D.enumerable})}return A},Ah6=(A)=>ef6(ik1({},"__esModule",{value:!0}),A),JFB={};tf6(JFB,{AlgorithmId:()=>KFB,EndpointURLScheme:()=>CFB,FieldPosition:()=>HFB,HttpApiKeyAuthLocation:()=>VFB,HttpAuthLocation:()=>XFB,IniSectionType:()=>zFB,RequestHandlerProtocol:()=>EFB,SMITHY_CONTEXT_KEY:()=>Gh6,getDefaultClientConfiguration:()=>Dh6,resolveDefaultRuntimeConfig:()=>Zh6});UFB.exports=Ah6(JFB);var XFB=((A)=>{return A.HEADER="header",A.QUERY="query",A})(XFB||{}),VFB=((A)=>{return A.HEADER="header",A.QUERY="query",A})(VFB||{}),CFB=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(CFB||{}),KFB=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(KFB||{}),Bh6=nk1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),Qh6=nk1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),Dh6=nk1((A)=>{return Bh6(A)},"getDefaultClientConfiguration"),Zh6=nk1((A)=>{return Qh6(A)},"resolveDefaultRuntimeConfig"),HFB=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(HFB||{}),Gh6="__smithy_context",zFB=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(zFB||{}),EFB=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(EFB||{})});
var wW2=E((EW2)=>{Object.defineProperty(EW2,"__esModule",{value:!0});EW2.ruleSet=void 0;var IW2="required",U4="type",c8="fn",l8="argv",F_="ref",rY2=!1,I30=!0,G_="booleanEquals",hY="stringEquals",YW2="sigv4",WW2="sts",JW2="us-east-1",CD="endpoint",oY2="https://sts.{Region}.{PartitionResult#dnsSuffix}",NL="tree",es="error",W30="getAttr",tY2={[IW2]:!1,[U4]:"String"},Y30={[IW2]:!0,default:!1,[U4]:"Boolean"},XW2={[F_]:"Endpoint"},eY2={[c8]:"isSet",[l8]:[{[F_]:"Region"}]},gY={[F_]:"Region"},AW2={[c8]:"aws.partition",[l8]:[gY],assign:"PartitionResult"},VW2={[F_]:"UseFIPS"},CW2={[F_]:"UseDualStack"},lW={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:YW2,signingName:WW2,signingRegion:JW2}]},headers:{}},CK={},BW2={conditions:[{[c8]:hY,[l8]:[gY,"aws-global"]}],[CD]:lW,[U4]:CD},KW2={[c8]:G_,[l8]:[VW2,!0]},HW2={[c8]:G_,[l8]:[CW2,!0]},QW2={[c8]:W30,[l8]:[{[F_]:"PartitionResult"},"supportsFIPS"]},zW2={[F_]:"PartitionResult"},DW2={[c8]:G_,[l8]:[!0,{[c8]:W30,[l8]:[zW2,"supportsDualStack"]}]},ZW2=[{[c8]:"isSet",[l8]:[XW2]}],GW2=[KW2],FW2=[HW2],UO4={version:"1.0",parameters:{Region:tY2,UseDualStack:Y30,UseFIPS:Y30,Endpoint:tY2,UseGlobalEndpoint:Y30},rules:[{conditions:[{[c8]:G_,[l8]:[{[F_]:"UseGlobalEndpoint"},I30]},{[c8]:"not",[l8]:ZW2},eY2,AW2,{[c8]:G_,[l8]:[VW2,rY2]},{[c8]:G_,[l8]:[CW2,rY2]}],rules:[{conditions:[{[c8]:hY,[l8]:[gY,"ap-northeast-1"]}],endpoint:lW,[U4]:CD},{conditions:[{[c8]:hY,[l8]:[gY,"ap-south-1"]}],endpoint:lW,[U4]:CD},{conditions:[{[c8]:hY,[l8]:[gY,"ap-southeast-1"]}],endpoint:lW,[U4]:CD},{conditions:[{[c8]:hY,[l8]:[gY,"ap-southeast-2"]}],endpoint:lW,[U4]:CD},BW2,{conditions:[{[c8]:hY,[l8]:[gY,"ca-central-1"]}],endpoint:lW,[U4]:CD},{conditions:[{[c8]:hY,[l8]:[gY,"eu-central-1"]}],endpoint:lW,[U4]:CD},{conditions:[{[c8]:hY,[l8]:[gY,"eu-north-1"]}],endpoint:lW,[U4]:CD},{conditions:[{[c8]:hY,[l8]:[gY,"eu-west-1"]}],endpoint:lW,[U4]:CD},{conditions:[{[c8]:hY,[l8]:[gY,"eu-west-2"]}],endpoint:lW,[U4]:CD},{conditions:[{[c8]:hY,[l8]:[gY,"eu-west-3"]}],endpoint:lW,[U4]:CD},{conditions:[{[c8]:hY,[l8]:[gY,"sa-east-1"]}],endpoint:lW,[U4]:CD},{conditions:[{[c8]:hY,[l8]:[gY,JW2]}],endpoint:lW,[U4]:CD},{conditions:[{[c8]:hY,[l8]:[gY,"us-east-2"]}],endpoint:lW,[U4]:CD},{conditions:[{[c8]:hY,[l8]:[gY,"us-west-1"]}],endpoint:lW,[U4]:CD},{conditions:[{[c8]:hY,[l8]:[gY,"us-west-2"]}],endpoint:lW,[U4]:CD},{endpoint:{url:oY2,properties:{authSchemes:[{name:YW2,signingName:WW2,signingRegion:"{Region}"}]},headers:CK},[U4]:CD}],[U4]:NL},{conditions:ZW2,rules:[{conditions:GW2,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[U4]:es},{conditions:FW2,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[U4]:es},{endpoint:{url:XW2,properties:CK,headers:CK},[U4]:CD}],[U4]:NL},{conditions:[eY2],rules:[{conditions:[AW2],rules:[{conditions:[KW2,HW2],rules:[{conditions:[{[c8]:G_,[l8]:[I30,QW2]},DW2],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:CK,headers:CK},[U4]:CD}],[U4]:NL},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[U4]:es}],[U4]:NL},{conditions:GW2,rules:[{conditions:[{[c8]:G_,[l8]:[QW2,I30]}],rules:[{conditions:[{[c8]:hY,[l8]:[{[c8]:W30,[l8]:[zW2,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:CK,headers:CK},[U4]:CD},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:CK,headers:CK},[U4]:CD}],[U4]:NL},{error:"FIPS is enabled but this partition does not support FIPS",[U4]:es}],[U4]:NL},{conditions:FW2,rules:[{conditions:[DW2],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:CK,headers:CK},[U4]:CD}],[U4]:NL},{error:"DualStack is enabled but this partition does not support DualStack",[U4]:es}],[U4]:NL},BW2,{endpoint:{url:oY2,properties:CK,headers:CK},[U4]:CD}],[U4]:NL}],[U4]:NL},{error:"Invalid Configuration: Missing Region",[U4]:es}]};EW2.ruleSet=UO4});
var wWB=E((EWB)=>{Object.defineProperty(EWB,"__esModule",{value:!0});EWB.toBase64=void 0;var em6=AD(),Ad6=cB(),Bd6=(A)=>{let B;if(typeof A==="string")B=Ad6.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return em6.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};EWB.toBase64=Bd6});
var xD1=E((Br5,CWB)=>{var{defineProperty:Uy1,getOwnPropertyDescriptor:gu6,getOwnPropertyNames:uu6}=Object,mu6=Object.prototype.hasOwnProperty,y2=(A,B)=>Uy1(A,"name",{value:B,configurable:!0}),du6=(A,B)=>{for(var Q in B)Uy1(A,Q,{get:B[Q],enumerable:!0})},cu6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of uu6(B))if(!mu6.call(A,Z)&&Z!==Q)Uy1(A,Z,{get:()=>B[Z],enumerable:!(D=gu6(B,Z))||D.enumerable})}return A},lu6=(A)=>cu6(Uy1({},"__esModule",{value:!0}),A),oYB={};du6(oYB,{Client:()=>pu6,Command:()=>eYB,LazyJsonString:()=>Km,NoOpLogger:()=>mm6,SENSITIVE_STRING:()=>nu6,ServiceException:()=>Pm6,_json:()=>jH0,collectBody:()=>MH0.collectBody,convertMap:()=>dm6,createAggregatedClient:()=>au6,dateToUtcString:()=>GWB,decorateServiceException:()=>FWB,emitWarningIfUnsupportedVersion:()=>ym6,expectBoolean:()=>ru6,expectByte:()=>SH0,expectFloat32:()=>zy1,expectInt:()=>tu6,expectInt32:()=>TH0,expectLong:()=>yD1,expectNonNull:()=>Am6,expectNumber:()=>kD1,expectObject:()=>AWB,expectShort:()=>PH0,expectString:()=>Bm6,expectUnion:()=>Qm6,extendedEncodeURIComponent:()=>MH0.extendedEncodeURIComponent,getArrayIfSingleItem:()=>gm6,getDefaultClientConfiguration:()=>fm6,getDefaultExtensionConfiguration:()=>YWB,getValueFromTextNode:()=>WWB,handleFloat:()=>Gm6,isSerializableHeaderValue:()=>um6,limitedParseDouble:()=>_H0,limitedParseFloat:()=>Fm6,limitedParseFloat32:()=>Im6,loadConfigsForDefaultMode:()=>km6,logger:()=>_D1,map:()=>vH0,parseBoolean:()=>su6,parseEpochTimestamp:()=>wm6,parseRfc3339DateTime:()=>Vm6,parseRfc3339DateTimeWithOffset:()=>Km6,parseRfc7231DateTime:()=>Um6,quoteHeader:()=>XWB,resolveDefaultRuntimeConfig:()=>hm6,resolvedPath:()=>MH0.resolvedPath,serializeDateTime:()=>am6,serializeFloat:()=>nm6,splitEvery:()=>VWB,splitHeader:()=>sm6,strictParseByte:()=>ZWB,strictParseDouble:()=>yH0,strictParseFloat:()=>Dm6,strictParseFloat32:()=>BWB,strictParseInt:()=>Ym6,strictParseInt32:()=>Wm6,strictParseLong:()=>DWB,strictParseShort:()=>$e,take:()=>cm6,throwDefaultError:()=>IWB,withBaseException:()=>Sm6});CWB.exports=lu6(oYB);var tYB=Uw(),pu6=class{constructor(A){this.config=A,this.middlewareStack=tYB.constructStack()}static{y2(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},MH0=$6(),OH0=LH0(),eYB=class{constructor(){this.middlewareStack=tYB.constructStack()}static{y2(this,"Command")}static classBuilder(){return new iu6}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[OH0.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},iu6=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{y2(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}build(){let A=this,B;return B=class extends eYB{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this)}static{y2(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},nu6="***SensitiveInformation***",au6=y2((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=y2(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),su6=y2((A)=>{switch(A){case"true":return!0;case"false":return!1;default:throw new Error(`Unable to parse boolean value "${A}"`)}},"parseBoolean"),ru6=y2((A)=>{if(A===null||A===void 0)return;if(typeof A==="number"){if(A===0||A===1)_D1.warn(Ey1(`Expected boolean, got ${typeof A}: ${A}`));if(A===0)return!1;if(A===1)return!0}if(typeof A==="string"){let B=A.toLowerCase();if(B==="false"||B==="true")_D1.warn(Ey1(`Expected boolean, got ${typeof A}: ${A}`));if(B==="false")return!1;if(B==="true")return!0}if(typeof A==="boolean")return A;throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)},"expectBoolean"),kD1=y2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string"){let B=parseFloat(A);if(!Number.isNaN(B)){if(String(B)!==String(A))_D1.warn(Ey1(`Expected number but observed string: ${A}`));return B}}if(typeof A==="number")return A;throw new TypeError(`Expected number, got ${typeof A}: ${A}`)},"expectNumber"),ou6=Math.ceil(340282346638528860000000000000000000000),zy1=y2((A)=>{let B=kD1(A);if(B!==void 0&&!Number.isNaN(B)&&B!==1/0&&B!==-1/0){if(Math.abs(B)>ou6)throw new TypeError(`Expected 32-bit float, got ${A}`)}return B},"expectFloat32"),yD1=y2((A)=>{if(A===null||A===void 0)return;if(Number.isInteger(A)&&!Number.isNaN(A))return A;throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)},"expectLong"),tu6=yD1,TH0=y2((A)=>kH0(A,32),"expectInt32"),PH0=y2((A)=>kH0(A,16),"expectShort"),SH0=y2((A)=>kH0(A,8),"expectByte"),kH0=y2((A,B)=>{let Q=yD1(A);if(Q!==void 0&&eu6(Q,B)!==Q)throw new TypeError(`Expected ${B}-bit integer, got ${A}`);return Q},"expectSizedInt"),eu6=y2((A,B)=>{switch(B){case 32:return Int32Array.of(A)[0];case 16:return Int16Array.of(A)[0];case 8:return Int8Array.of(A)[0]}},"castInt"),Am6=y2((A,B)=>{if(A===null||A===void 0){if(B)throw new TypeError(`Expected a non-null value for ${B}`);throw new TypeError("Expected a non-null value")}return A},"expectNonNull"),AWB=y2((A)=>{if(A===null||A===void 0)return;if(typeof A==="object"&&!Array.isArray(A))return A;let B=Array.isArray(A)?"array":typeof A;throw new TypeError(`Expected object, got ${B}: ${A}`)},"expectObject"),Bm6=y2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string")return A;if(["boolean","number","bigint"].includes(typeof A))return _D1.warn(Ey1(`Expected string, got ${typeof A}: ${A}`)),String(A);throw new TypeError(`Expected string, got ${typeof A}: ${A}`)},"expectString"),Qm6=y2((A)=>{if(A===null||A===void 0)return;let B=AWB(A),Q=Object.entries(B).filter(([,D])=>D!=null).map(([D])=>D);if(Q.length===0)throw new TypeError("Unions must have exactly one non-null member. None were found.");if(Q.length>1)throw new TypeError(`Unions must have exactly one non-null member. Keys ${Q} were not null.`);return B},"expectUnion"),yH0=y2((A)=>{if(typeof A=="string")return kD1(Ne(A));return kD1(A)},"strictParseDouble"),Dm6=yH0,BWB=y2((A)=>{if(typeof A=="string")return zy1(Ne(A));return zy1(A)},"strictParseFloat32"),Zm6=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,Ne=y2((A)=>{let B=A.match(Zm6);if(B===null||B[0].length!==A.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(A)},"parseNumber"),_H0=y2((A)=>{if(typeof A=="string")return QWB(A);return kD1(A)},"limitedParseDouble"),Gm6=_H0,Fm6=_H0,Im6=y2((A)=>{if(typeof A=="string")return QWB(A);return zy1(A)},"limitedParseFloat32"),QWB=y2((A)=>{switch(A){case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error(`Unable to parse float value: ${A}`)}},"parseFloatString"),DWB=y2((A)=>{if(typeof A==="string")return yD1(Ne(A));return yD1(A)},"strictParseLong"),Ym6=DWB,Wm6=y2((A)=>{if(typeof A==="string")return TH0(Ne(A));return TH0(A)},"strictParseInt32"),$e=y2((A)=>{if(typeof A==="string")return PH0(Ne(A));return PH0(A)},"strictParseShort"),ZWB=y2((A)=>{if(typeof A==="string")return SH0(Ne(A));return SH0(A)},"strictParseByte"),Ey1=y2((A)=>{return String(new TypeError(A).stack||A).split(`
`).slice(0,5).filter((B)=>!B.includes("stackTraceWarning")).join(`
`)},"stackTraceWarning"),_D1={warn:console.warn},Jm6=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],xH0=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function GWB(A){let B=A.getUTCFullYear(),Q=A.getUTCMonth(),D=A.getUTCDay(),Z=A.getUTCDate(),G=A.getUTCHours(),F=A.getUTCMinutes(),I=A.getUTCSeconds(),Y=Z<10?`0${Z}`:`${Z}`,W=G<10?`0${G}`:`${G}`,J=F<10?`0${F}`:`${F}`,X=I<10?`0${I}`:`${I}`;return`${Jm6[D]}, ${Y} ${xH0[Q]} ${B} ${W}:${J}:${X} GMT`}y2(GWB,"dateToUtcString");var Xm6=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),Vm6=y2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=Xm6.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W]=B,J=$e(qe(D)),X=UM(Z,"month",1,12),V=UM(G,"day",1,31);return jD1(J,X,V,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})},"parseRfc3339DateTime"),Cm6=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),Km6=y2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=Cm6.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W,J]=B,X=$e(qe(D)),V=UM(Z,"month",1,12),C=UM(G,"day",1,31),K=jD1(X,V,C,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W});if(J.toUpperCase()!="Z")K.setTime(K.getTime()-Tm6(J));return K},"parseRfc3339DateTimeWithOffset"),Hm6=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),zm6=new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),Em6=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/),Um6=y2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-7231 date-times must be expressed as strings");let B=Hm6.exec(A);if(B){let[Q,D,Z,G,F,I,Y,W]=B;return jD1($e(qe(G)),RH0(Z),UM(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})}if(B=zm6.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return Nm6(jD1($m6(G),RH0(Z),UM(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W}))}if(B=Em6.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return jD1($e(qe(W)),RH0(D),UM(Z.trimLeft(),"day",1,31),{hours:G,minutes:F,seconds:I,fractionalMilliseconds:Y})}throw new TypeError("Invalid RFC-7231 date-time value")},"parseRfc7231DateTime"),wm6=y2((A)=>{if(A===null||A===void 0)return;let B;if(typeof A==="number")B=A;else if(typeof A==="string")B=yH0(A);else if(typeof A==="object"&&A.tag===1)B=A.value;else throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(B)||B===1/0||B===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(B*1000))},"parseEpochTimestamp"),jD1=y2((A,B,Q,D)=>{let Z=B-1;return Mm6(A,Z,Q),new Date(Date.UTC(A,Z,Q,UM(D.hours,"hour",0,23),UM(D.minutes,"minute",0,59),UM(D.seconds,"seconds",0,60),Om6(D.fractionalMilliseconds)))},"buildDate"),$m6=y2((A)=>{let B=new Date().getUTCFullYear(),Q=Math.floor(B/100)*100+$e(qe(A));if(Q<B)return Q+100;return Q},"parseTwoDigitYear"),qm6=1576800000000,Nm6=y2((A)=>{if(A.getTime()-new Date().getTime()>qm6)return new Date(Date.UTC(A.getUTCFullYear()-100,A.getUTCMonth(),A.getUTCDate(),A.getUTCHours(),A.getUTCMinutes(),A.getUTCSeconds(),A.getUTCMilliseconds()));return A},"adjustRfc850Year"),RH0=y2((A)=>{let B=xH0.indexOf(A);if(B<0)throw new TypeError(`Invalid month: ${A}`);return B+1},"parseMonthByShortName"),Lm6=[31,28,31,30,31,30,31,31,30,31,30,31],Mm6=y2((A,B,Q)=>{let D=Lm6[B];if(B===1&&Rm6(A))D=29;if(Q>D)throw new TypeError(`Invalid day for ${xH0[B]} in ${A}: ${Q}`)},"validateDayOfMonth"),Rm6=y2((A)=>{return A%4===0&&(A%100!==0||A%400===0)},"isLeapYear"),UM=y2((A,B,Q,D)=>{let Z=ZWB(qe(A));if(Z<Q||Z>D)throw new TypeError(`${B} must be between ${Q} and ${D}, inclusive`);return Z},"parseDateValue"),Om6=y2((A)=>{if(A===null||A===void 0)return 0;return BWB("0."+A)*1000},"parseMilliseconds"),Tm6=y2((A)=>{let B=A[0],Q=1;if(B=="+")Q=1;else if(B=="-")Q=-1;else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);let D=Number(A.substring(1,3)),Z=Number(A.substring(4,6));return Q*(D*60+Z)*60*1000},"parseOffsetToMilliseconds"),qe=y2((A)=>{let B=0;while(B<A.length-1&&A.charAt(B)==="0")B++;if(B===0)return A;return A.slice(B)},"stripLeadingZeroes"),Pm6=class A extends Error{static{y2(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},FWB=y2((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),IWB=y2(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=jm6(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw FWB(F,B)},"throwDefaultError"),Sm6=y2((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{IWB({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),jm6=y2((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),km6=y2((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),rYB=!1,ym6=y2((A)=>{if(A&&!rYB&&parseInt(A.substring(1,A.indexOf(".")))<16)rYB=!0},"emitWarningIfUnsupportedVersion"),_m6=y2((A)=>{let B=[];for(let Q in OH0.AlgorithmId){let D=OH0.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),xm6=y2((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),vm6=y2((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),bm6=y2((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),YWB=y2((A)=>{return Object.assign(_m6(A),vm6(A))},"getDefaultExtensionConfiguration"),fm6=YWB,hm6=y2((A)=>{return Object.assign(xm6(A),bm6(A))},"resolveDefaultRuntimeConfig"),gm6=y2((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),WWB=y2((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=WWB(A[Q]);return A},"getValueFromTextNode"),um6=y2((A)=>{return A!=null},"isSerializableHeaderValue"),Km=y2(function A(B){return Object.assign(new String(B),{deserializeJSON(){return JSON.parse(String(B))},toString(){return String(B)},toJSON(){return String(B)}})},"LazyJsonString");Km.from=(A)=>{if(A&&typeof A==="object"&&(A instanceof Km||("deserializeJSON"in A)))return A;else if(typeof A==="string"||Object.getPrototypeOf(A)===String.prototype)return Km(String(A));return Km(JSON.stringify(A))};Km.fromObject=Km.from;var mm6=class{static{y2(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function vH0(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,lm6(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}JWB(D,null,G,F)}return D}y2(vH0,"map");var dm6=y2((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),cm6=y2((A,B)=>{let Q={};for(let D in B)JWB(Q,A,B,D);return Q},"take"),lm6=y2((A,B,Q)=>{return vH0(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),JWB=y2((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=pm6,Y=im6,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),pm6=y2((A)=>A!=null,"nonNullish"),im6=y2((A)=>A,"pass");function XWB(A){if(A.includes(",")||A.includes('"'))A=`"${A.replace(/"/g,"\\\"")}"`;return A}y2(XWB,"quoteHeader");var nm6=y2((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),am6=y2((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),jH0=y2((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(jH0);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=jH0(A[Q])}return B}return A},"_json");function VWB(A,B,Q){if(Q<=0||!Number.isInteger(Q))throw new Error("Invalid number of delimiters ("+Q+") for splitEvery.");let D=A.split(B);if(Q===1)return D;let Z=[],G="";for(let F=0;F<D.length;F++){if(G==="")G=D[F];else G+=B+D[F];if((F+1)%Q===0)Z.push(G),G=""}if(G!=="")Z.push(G);return Z}y2(VWB,"splitEvery");var sm6=y2((A)=>{let B=A.length,Q=[],D=!1,Z=void 0,G=0;for(let F=0;F<B;++F){let I=A[F];switch(I){case'"':if(Z!=="\\")D=!D;break;case",":if(!D)Q.push(A.slice(G,F)),G=F+1;break;default:}Z=I}return Q.push(A.slice(G)),Q.map((F)=>{F=F.trim();let I=F.length;if(I<2)return F;if(F[0]==='"'&&F[I-1]==='"')F=F.slice(1,I-1);return F.replace(/\\"/g,'"')})},"splitHeader")});
var xFB=E((qs5,_FB)=>{var{defineProperty:sk1,getOwnPropertyDescriptor:Uh6,getOwnPropertyNames:wh6}=Object,$h6=Object.prototype.hasOwnProperty,PD1=(A,B)=>sk1(A,"name",{value:B,configurable:!0}),qh6=(A,B)=>{for(var Q in B)sk1(A,Q,{get:B[Q],enumerable:!0})},Nh6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of wh6(B))if(!$h6.call(A,Z)&&Z!==Q)sk1(A,Z,{get:()=>B[Z],enumerable:!(D=Uh6(B,Z))||D.enumerable})}return A},Lh6=(A)=>Nh6(sk1({},"__esModule",{value:!0}),A),OFB={};qh6(OFB,{eventStreamHandlingMiddleware:()=>SFB,eventStreamHandlingMiddlewareOptions:()=>jFB,eventStreamHeaderMiddleware:()=>kFB,eventStreamHeaderMiddlewareOptions:()=>yFB,getEventStreamPlugin:()=>Mh6,resolveEventStreamConfig:()=>TFB});_FB.exports=Lh6(OFB);function TFB(A){let{signer:B,signer:Q}=A,D=Object.assign(A,{eventSigner:B,messageSigner:Q}),Z=D.eventStreamPayloadHandlerProvider(D);return Object.assign(D,{eventStreamPayloadHandler:Z})}PD1(TFB,"resolveEventStreamConfig");var PFB=RFB(),SFB=PD1((A)=>(B,Q)=>async(D)=>{let{request:Z}=D;if(!PFB.HttpRequest.isInstance(Z))return B(D);return A.eventStreamPayloadHandler.handle(B,D,Q)},"eventStreamHandlingMiddleware"),jFB={tags:["EVENT_STREAM","SIGNATURE","HANDLE"],name:"eventStreamHandlingMiddleware",relation:"after",toMiddleware:"awsAuthMiddleware",override:!0},kFB=PD1((A)=>async(B)=>{let{request:Q}=B;if(!PFB.HttpRequest.isInstance(Q))return A(B);return Q.headers={...Q.headers,"content-type":"application/vnd.amazon.eventstream","x-amz-content-sha256":"STREAMING-AWS4-HMAC-SHA256-EVENTS"},A({...B,request:Q})},"eventStreamHeaderMiddleware"),yFB={step:"build",tags:["EVENT_STREAM","HEADER","CONTENT_TYPE","CONTENT_SHA256"],name:"eventStreamHeaderMiddleware",override:!0},Mh6=PD1((A)=>({applyToStack:PD1((B)=>{B.addRelativeTo(SFB(A),jFB),B.add(kFB,yFB)},"applyToStack")}),"getEventStreamPlugin")});
var xJ2=E((yJ2)=>{Object.defineProperty(yJ2,"__esModule",{value:!0});yJ2.fromTokenFile=void 0;var zP4=HL(),EP4=eB(),UP4=J1("fs"),wP4=n30(),kJ2="AWS_WEB_IDENTITY_TOKEN_FILE",$P4="AWS_ROLE_ARN",qP4="AWS_ROLE_SESSION_NAME",NP4=(A={})=>async()=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromTokenFile");let B=A?.webIdentityTokenFile??process.env[kJ2],Q=A?.roleArn??process.env[$P4],D=A?.roleSessionName??process.env[qP4];if(!B||!Q)throw new EP4.CredentialsProviderError("Web identity configuration not specified",{logger:A.logger});let Z=await wP4.fromWebToken({...A,webIdentityToken:UP4.readFileSync(B,{encoding:"ascii"}),roleArn:Q,roleSessionName:D})();if(B===process.env[kJ2])zP4.setCredentialFeature(Z,"CREDENTIALS_ENV_VARS_STS_WEB_ID_TOKEN","h");return Z};yJ2.fromTokenFile=NP4});
var xW2=E((yW2)=>{Object.defineProperty(yW2,"__esModule",{value:!0});yW2.getRuntimeConfig=void 0;var jO4=p50(),kO4=jO4.__importDefault(i50()),PW2=WI(),SW2=i61(),dL1=V4(),yO4=VB(),_O4=jG(),jW2=v4(),Ar=QD(),kW2=S3(),xO4=kG(),vO4=hZ(),bO4=TW2(),fO4=QZ(),hO4=yG(),gO4=QZ(),uO4=(A)=>{gO4.emitWarningIfUnsupportedVersion(process.version);let B=hO4.resolveDefaultsModeConfig(A),Q=()=>B().then(fO4.loadConfigsForDefaultMode),D=bO4.getRuntimeConfig(A);PW2.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile};return{...D,...A,runtime:"node",defaultsMode:B,bodyLengthChecker:A?.bodyLengthChecker??xO4.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??SW2.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:kO4.default.version}),httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(G)=>G.getIdentityProvider("aws.auth#sigv4")||(async(F)=>await A.credentialDefaultProvider(F?.__config||{})()),signer:new PW2.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(G)=>G.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new yO4.NoAuthSigner}],maxAttempts:A?.maxAttempts??Ar.loadConfig(jW2.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??Ar.loadConfig(dL1.NODE_REGION_CONFIG_OPTIONS,{...dL1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:kW2.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??Ar.loadConfig({...jW2.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||vO4.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??_O4.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??kW2.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??Ar.loadConfig(dL1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??Ar.loadConfig(dL1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??Ar.loadConfig(SW2.NODE_APP_ID_CONFIG_OPTIONS,Z)}};yW2.getRuntimeConfig=uO4});
var zI2=E((KI2)=>{Object.defineProperty(KI2,"__esModule",{value:!0});KI2.toBase64=void 0;var XM4=AD(),VM4=cB(),CM4=(A)=>{let B;if(typeof A==="string")B=VM4.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return XM4.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};KI2.toBase64=CM4});
var zWB=E((KWB)=>{Object.defineProperty(KWB,"__esModule",{value:!0});KWB.fromBase64=void 0;var rm6=AD(),om6=/^[A-Za-z0-9+/]*={0,2}$/,tm6=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!om6.exec(A))throw new TypeError("Invalid base64 string.");let B=rm6.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};KWB.fromBase64=tm6});

module.exports = vXB;
