// dependency_chain package extracted with entry point: SR1

var AX2=E((wV)=>{var QS4=wV&&wV.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),DS4=wV&&wV.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),tJ2=wV&&wV.__importStar||function(A){if(A&&A.__esModule)return A;var B={};if(A!=null){for(var Q in A)if(Q!=="default"&&Object.prototype.hasOwnProperty.call(A,Q))QS4(B,A,Q)}return DS4(B,A),B};Object.defineProperty(wV,"__esModule",{value:!0});wV.req=wV.json=wV.toBuffer=void 0;var ZS4=tJ2(J1("http")),GS4=tJ2(J1("https"));async function eJ2(A){let B=0,Q=[];for await(let D of A)B+=D.length,Q.push(D);return Buffer.concat(Q,B)}wV.toBuffer=eJ2;async function FS4(A){let Q=(await eJ2(A)).toString("utf8");try{return JSON.parse(Q)}catch(D){let Z=D;throw Z.message+=` (input: ${Q})`,Z}}wV.json=FS4;function IS4(A,B={}){let D=((typeof A==="string"?A:A.href).startsWith("https:")?GS4:ZS4).request(A,B),Z=new Promise((G,F)=>{D.once("response",G).once("error",F).end()});return D.then=Z.then.bind(Z),D}wV.req=IS4});
var FX2=E((Jr)=>{var VS4=Jr&&Jr.__importDefault||function(A){return A&&A.__esModule?A:{default:A}};Object.defineProperty(Jr,"__esModule",{value:!0});Jr.parseProxyResponse=void 0;var CS4=VS4(GB1()),nL1=CS4.default("https-proxy-agent:parse-proxy-response");function KS4(A){return new Promise((B,Q)=>{let D=0,Z=[];function G(){let J=A.read();if(J)W(J);else A.once("readable",G)}function F(){A.removeListener("end",I),A.removeListener("error",Y),A.removeListener("readable",G)}function I(){F(),nL1("onend"),Q(new Error("Proxy connection ended before receiving CONNECT response"))}function Y(J){F(),nL1("onerror %o",J),Q(J)}function W(J){Z.push(J),D+=J.length;let X=Buffer.concat(Z,D),V=X.indexOf(`\r
\r
`);if(V===-1){nL1("have not received end of HTTP headers yet..."),G();return}let C=X.slice(0,V).toString("ascii").split(`\r
`),K=C.shift();if(!K)return A.destroy(),Q(new Error("No header received from proxy CONNECT response"));let H=K.split(" "),z=+H[1],$=H.slice(2).join(" "),L={};for(let N of C){if(!N)continue;let O=N.indexOf(":");if(O===-1)return A.destroy(),Q(new Error(`Invalid header from proxy CONNECT response: "${N}"`));let R=N.slice(0,O).toLowerCase(),T=N.slice(O+1).trimStart(),j=L[R];if(typeof j==="string")L[R]=[j,T];else if(Array.isArray(j))j.push(T);else L[R]=T}nL1("got proxy server response: %o %o",K,L),F(),B({connect:{statusCode:z,statusText:$,headers:L},buffered:X})}A.on("error",Y),A.on("end",I),G()})}Jr.parseProxyResponse=KS4});
var Fp0=E((Zp0,RX1)=>{var LM9=J1("tty"),MX1=J1("util");Zp0.init=jM9;Zp0.log=TM9;Zp0.formatArgs=RM9;Zp0.save=PM9;Zp0.load=SM9;Zp0.useColors=MM9;Zp0.destroy=MX1.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");Zp0.colors=[6,2,3,4,5,1];try{let A=Qp0();if(A&&(A.stderr||A).level>=2)Zp0.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221]}catch(A){}Zp0.inspectOpts=Object.keys(process.env).filter((A)=>{return/^debug_/i.test(A)}).reduce((A,B)=>{let Q=B.substring(6).toLowerCase().replace(/_([a-z])/g,(Z,G)=>{return G.toUpperCase()}),D=process.env[B];if(/^(yes|on|true|enabled)$/i.test(D))D=!0;else if(/^(no|off|false|disabled)$/i.test(D))D=!1;else if(D==="null")D=null;else D=Number(D);return A[Q]=D,A},{});function MM9(){return"colors"in Zp0.inspectOpts?Boolean(Zp0.inspectOpts.colors):LM9.isatty(process.stderr.fd)}function RM9(A){let{namespace:B,useColors:Q}=this;if(Q){let D=this.color,Z="\x1B[3"+(D<8?D:"8;5;"+D),G=`  ${Z};1m${B} \x1B[0m`;A[0]=G+A[0].split(`
`).join(`
`+G),A.push(Z+"m+"+RX1.exports.humanize(this.diff)+"\x1B[0m")}else A[0]=OM9()+B+" "+A[0]}function OM9(){if(Zp0.inspectOpts.hideDate)return"";return new Date().toISOString()+" "}function TM9(...A){return process.stderr.write(MX1.formatWithOptions(Zp0.inspectOpts,...A)+`
`)}function PM9(A){if(A)process.env.DEBUG=A;else delete process.env.DEBUG}function SM9(){return process.env.DEBUG}function jM9(A){A.inspectOpts={};let B=Object.keys(Zp0.inspectOpts);for(let Q=0;Q<B.length;Q++)A.inspectOpts[B[Q]]=Zp0.inspectOpts[B[Q]]}RX1.exports=mc1()(Zp0);var{formatters:Dp0}=RX1.exports;Dp0.o=function(A){return this.inspectOpts.colors=this.useColors,MX1.inspect(A,this.inspectOpts).split(`
`).map((B)=>B.trim()).join(" ")};Dp0.O=function(A){return this.inspectOpts.colors=this.useColors,MX1.inspect(A,this.inspectOpts)}});
var GB1=E((fh8,cc1)=>{if(typeof process==="undefined"||process.type==="renderer"||!1||process.__nwjs)cc1.exports=tl0();else cc1.exports=Fp0()});
var GX2=E((KK)=>{var QX2=KK&&KK.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),YS4=KK&&KK.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),DX2=KK&&KK.__importStar||function(A){if(A&&A.__esModule)return A;var B={};if(A!=null){for(var Q in A)if(Q!=="default"&&Object.prototype.hasOwnProperty.call(A,Q))QX2(B,A,Q)}return YS4(B,A),B},WS4=KK&&KK.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))QX2(B,A,Q)};Object.defineProperty(KK,"__esModule",{value:!0});KK.Agent=void 0;var JS4=DX2(J1("net")),BX2=DX2(J1("http")),XS4=J1("https");WS4(AX2(),KK);var ML=Symbol("AgentBaseInternalState");class ZX2 extends BX2.Agent{constructor(A){super(A);this[ML]={}}isSecureEndpoint(A){if(A){if(typeof A.secureEndpoint==="boolean")return A.secureEndpoint;if(typeof A.protocol==="string")return A.protocol==="https:"}let{stack:B}=new Error;if(typeof B!=="string")return!1;return B.split(`
`).some((Q)=>Q.indexOf("(https.js:")!==-1||Q.indexOf("node:https:")!==-1)}incrementSockets(A){if(this.maxSockets===1/0&&this.maxTotalSockets===1/0)return null;if(!this.sockets[A])this.sockets[A]=[];let B=new JS4.Socket({writable:!1});return this.sockets[A].push(B),this.totalSocketCount++,B}decrementSockets(A,B){if(!this.sockets[A]||B===null)return;let Q=this.sockets[A],D=Q.indexOf(B);if(D!==-1){if(Q.splice(D,1),this.totalSocketCount--,Q.length===0)delete this.sockets[A]}}getName(A){if(typeof A.secureEndpoint==="boolean"?A.secureEndpoint:this.isSecureEndpoint(A))return XS4.Agent.prototype.getName.call(this,A);return super.getName(A)}createSocket(A,B,Q){let D={...B,secureEndpoint:this.isSecureEndpoint(B)},Z=this.getName(D),G=this.incrementSockets(Z);Promise.resolve().then(()=>this.connect(A,D)).then((F)=>{if(this.decrementSockets(Z,G),F instanceof BX2.Agent)try{return F.addRequest(A,D)}catch(I){return Q(I)}this[ML].currentSocket=F,super.createSocket(A,B,Q)},(F)=>{this.decrementSockets(Z,G),Q(F)})}createConnection(){let A=this[ML].currentSocket;if(this[ML].currentSocket=void 0,!A)throw new Error("No socket was returned in the `connect()` function");return A}get defaultPort(){return this[ML].defaultPort??(this.protocol==="https:"?443:80)}set defaultPort(A){if(this[ML])this[ML].defaultPort=A}get protocol(){return this[ML].protocol??(this.isSecureEndpoint()?"https:":"http:")}set protocol(A){if(this[ML])this[ML].protocol=A}}KK.Agent=ZX2});
var Q70=E((sz)=>{var HS4=sz&&sz.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),zS4=sz&&sz.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),JX2=sz&&sz.__importStar||function(A){if(A&&A.__esModule)return A;var B={};if(A!=null){for(var Q in A)if(Q!=="default"&&Object.prototype.hasOwnProperty.call(A,Q))HS4(B,A,Q)}return zS4(B,A),B},XX2=sz&&sz.__importDefault||function(A){return A&&A.__esModule?A:{default:A}};Object.defineProperty(sz,"__esModule",{value:!0});sz.HttpsProxyAgent=void 0;var aL1=JX2(J1("net")),IX2=JX2(J1("tls")),ES4=XX2(J1("assert")),US4=XX2(GB1()),wS4=GX2(),$S4=J1("url"),qS4=FX2(),H81=US4.default("https-proxy-agent"),YX2=(A)=>{if(A.servername===void 0&&A.host&&!aL1.isIP(A.host))return{...A,servername:A.host};return A};class B70 extends wS4.Agent{constructor(A,B){super(B);this.options={path:void 0},this.proxy=typeof A==="string"?new $S4.URL(A):A,this.proxyHeaders=B?.headers??{},H81("Creating new HttpsProxyAgent instance: %o",this.proxy.href);let Q=(this.proxy.hostname||this.proxy.host).replace(/^\[|\]$/g,""),D=this.proxy.port?parseInt(this.proxy.port,10):this.proxy.protocol==="https:"?443:80;this.connectOpts={ALPNProtocols:["http/1.1"],...B?WX2(B,"headers"):null,host:Q,port:D}}async connect(A,B){let{proxy:Q}=this;if(!B.host)throw new TypeError('No "host" provided');let D;if(Q.protocol==="https:")H81("Creating `tls.Socket`: %o",this.connectOpts),D=IX2.connect(YX2(this.connectOpts));else H81("Creating `net.Socket`: %o",this.connectOpts),D=aL1.connect(this.connectOpts);let Z=typeof this.proxyHeaders==="function"?this.proxyHeaders():{...this.proxyHeaders},G=aL1.isIPv6(B.host)?`[${B.host}]`:B.host,F=`CONNECT ${G}:${B.port} HTTP/1.1\r
`;if(Q.username||Q.password){let X=`${decodeURIComponent(Q.username)}:${decodeURIComponent(Q.password)}`;Z["Proxy-Authorization"]=`Basic ${Buffer.from(X).toString("base64")}`}if(Z.Host=`${G}:${B.port}`,!Z["Proxy-Connection"])Z["Proxy-Connection"]=this.keepAlive?"Keep-Alive":"close";for(let X of Object.keys(Z))F+=`${X}: ${Z[X]}\r
`;let I=qS4.parseProxyResponse(D);D.write(`${F}\r
`);let{connect:Y,buffered:W}=await I;if(A.emit("proxyConnect",Y),this.emit("proxyConnect",Y,A),Y.statusCode===200){if(A.once("socket",NS4),B.secureEndpoint)return H81("Upgrading socket connection to TLS"),IX2.connect({...WX2(YX2(B),"host","path","port"),socket:D});return D}D.destroy();let J=new aL1.Socket({writable:!1});return J.readable=!0,A.once("socket",(X)=>{H81("Replaying proxy buffer for failed request"),ES4.default(X.listenerCount("data")>0),X.push(W),X.push(null)}),J}}B70.protocols=["http","https"];sz.HttpsProxyAgent=B70;function NS4(A){A.resume()}function WX2(A,...B){let Q={},D;for(D in A)if(!B.includes(D))Q[D]=A[D];return Q}});
var Qp0=E((vh8,Bp0)=>{var wM9=J1("os"),Ap0=J1("tty"),dH=ZB1(),{env:tF}=process,LX1;if(dH("no-color")||dH("no-colors")||dH("color=false")||dH("color=never"))LX1=0;else if(dH("color")||dH("colors")||dH("color=true")||dH("color=always"))LX1=1;function $M9(){if("FORCE_COLOR"in tF){if(tF.FORCE_COLOR==="true")return 1;if(tF.FORCE_COLOR==="false")return 0;return tF.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(tF.FORCE_COLOR,10),3)}}function qM9(A){if(A===0)return!1;return{level:A,hasBasic:!0,has256:A>=2,has16m:A>=3}}function NM9(A,{streamIsTTY:B,sniffFlags:Q=!0}={}){let D=$M9();if(D!==void 0)LX1=D;let Z=Q?LX1:D;if(Z===0)return 0;if(Q){if(dH("color=16m")||dH("color=full")||dH("color=truecolor"))return 3;if(dH("color=256"))return 2}if(A&&!B&&Z===void 0)return 0;let G=Z||0;if(tF.TERM==="dumb")return G;if(process.platform==="win32"){let F=wM9.release().split(".");if(Number(F[0])>=10&&Number(F[2])>=10586)return Number(F[2])>=14931?3:2;return 1}if("CI"in tF){if(["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some((F)=>(F in tF))||tF.CI_NAME==="codeship")return 1;return G}if("TEAMCITY_VERSION"in tF)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(tF.TEAMCITY_VERSION)?1:0;if(tF.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in tF){let F=Number.parseInt((tF.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(tF.TERM_PROGRAM){case"iTerm.app":return F>=3?3:2;case"Apple_Terminal":return 2}}if(/-256(color)?$/i.test(tF.TERM))return 2;if(/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(tF.TERM))return 1;if("COLORTERM"in tF)return 1;return G}function dc1(A,B={}){let Q=NM9(A,{streamIsTTY:A&&A.isTTY,...B});return qM9(Q)}Bp0.exports={supportsColor:dc1,stdout:dc1({isTTY:Ap0.isatty(1)}),stderr:dc1({isTTY:Ap0.isatty(2)})}});
var ZB1=E((xh8,el0)=>{el0.exports=(A,B=process.argv)=>{let Q=A.startsWith("-")?"":A.length===1?"-":"--",D=B.indexOf(Q+A),Z=B.indexOf("--");return D!==-1&&(Z===-1||D<Z)}});
var mc1=E((yh8,rl0)=>{function GM9(A){Q.debug=Q,Q.default=Q,Q.coerce=Y,Q.disable=F,Q.enable=Z,Q.enabled=I,Q.humanize=sl0(),Q.destroy=W,Object.keys(A).forEach((J)=>{Q[J]=A[J]}),Q.names=[],Q.skips=[],Q.formatters={};function B(J){let X=0;for(let V=0;V<J.length;V++)X=(X<<5)-X+J.charCodeAt(V),X|=0;return Q.colors[Math.abs(X)%Q.colors.length]}Q.selectColor=B;function Q(J){let X,V=null,C,K;function H(...z){if(!H.enabled)return;let $=H,L=Number(new Date),N=L-(X||L);if($.diff=N,$.prev=X,$.curr=L,X=L,z[0]=Q.coerce(z[0]),typeof z[0]!=="string")z.unshift("%O");let O=0;z[0]=z[0].replace(/%([a-zA-Z%])/g,(T,j)=>{if(T==="%%")return"%";O++;let f=Q.formatters[j];if(typeof f==="function"){let k=z[O];T=f.call($,k),z.splice(O,1),O--}return T}),Q.formatArgs.call($,z),($.log||Q.log).apply($,z)}if(H.namespace=J,H.useColors=Q.useColors(),H.color=Q.selectColor(J),H.extend=D,H.destroy=Q.destroy,Object.defineProperty(H,"enabled",{enumerable:!0,configurable:!1,get:()=>{if(V!==null)return V;if(C!==Q.namespaces)C=Q.namespaces,K=Q.enabled(J);return K},set:(z)=>{V=z}}),typeof Q.init==="function")Q.init(H);return H}function D(J,X){let V=Q(this.namespace+(typeof X==="undefined"?":":X)+J);return V.log=this.log,V}function Z(J){Q.save(J),Q.namespaces=J,Q.names=[],Q.skips=[];let X=(typeof J==="string"?J:"").trim().replace(" ",",").split(",").filter(Boolean);for(let V of X)if(V[0]==="-")Q.skips.push(V.slice(1));else Q.names.push(V)}function G(J,X){let V=0,C=0,K=-1,H=0;while(V<J.length)if(C<X.length&&(X[C]===J[V]||X[C]==="*"))if(X[C]==="*")K=C,H=V,C++;else V++,C++;else if(K!==-1)C=K+1,H++,V=H;else return!1;while(C<X.length&&X[C]==="*")C++;return C===X.length}function F(){let J=[...Q.names,...Q.skips.map((X)=>"-"+X)].join(",");return Q.enable(""),J}function I(J){for(let X of Q.skips)if(G(J,X))return!1;for(let X of Q.names)if(G(J,X))return!0;return!1}function Y(J){if(J instanceof Error)return J.stack||J.message;return J}function W(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return Q.enable(Q.load()),Q}rl0.exports=GM9});
var sl0=E((kh8,al0)=>{var hl=1000,gl=hl*60,ul=gl*60,Tf=ul*24,AM9=Tf*7,BM9=Tf*365.25;al0.exports=function(A,B){B=B||{};var Q=typeof A;if(Q==="string"&&A.length>0)return QM9(A);else if(Q==="number"&&isFinite(A))return B.long?ZM9(A):DM9(A);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(A))};function QM9(A){if(A=String(A),A.length>100)return;var B=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(A);if(!B)return;var Q=parseFloat(B[1]),D=(B[2]||"ms").toLowerCase();switch(D){case"years":case"year":case"yrs":case"yr":case"y":return Q*BM9;case"weeks":case"week":case"w":return Q*AM9;case"days":case"day":case"d":return Q*Tf;case"hours":case"hour":case"hrs":case"hr":case"h":return Q*ul;case"minutes":case"minute":case"mins":case"min":case"m":return Q*gl;case"seconds":case"second":case"secs":case"sec":case"s":return Q*hl;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return Q;default:return}}function DM9(A){var B=Math.abs(A);if(B>=Tf)return Math.round(A/Tf)+"d";if(B>=ul)return Math.round(A/ul)+"h";if(B>=gl)return Math.round(A/gl)+"m";if(B>=hl)return Math.round(A/hl)+"s";return A+"ms"}function ZM9(A){var B=Math.abs(A);if(B>=Tf)return $X1(A,B,Tf,"day");if(B>=ul)return $X1(A,B,ul,"hour");if(B>=gl)return $X1(A,B,gl,"minute");if(B>=hl)return $X1(A,B,hl,"second");return A+" ms"}function $X1(A,B,Q,D){var Z=B>=Q*1.5;return Math.round(A/Q)+" "+D+(Z?"s":"")}});
var tl0=E((ol0,NX1)=>{ol0.formatArgs=IM9;ol0.save=YM9;ol0.load=WM9;ol0.useColors=FM9;ol0.storage=JM9();ol0.destroy=(()=>{let A=!1;return()=>{if(!A)A=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}})();ol0.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function FM9(){if(typeof window!=="undefined"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator!=="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let A;return typeof document!=="undefined"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window!=="undefined"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator!=="undefined"&&navigator.userAgent&&(A=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(A[1],10)>=31||typeof navigator!=="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function IM9(A){if(A[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+A[0]+(this.useColors?"%c ":" ")+"+"+NX1.exports.humanize(this.diff),!this.useColors)return;let B="color: "+this.color;A.splice(1,0,B,"color: inherit");let Q=0,D=0;A[0].replace(/%[a-zA-Z%]/g,(Z)=>{if(Z==="%%")return;if(Q++,Z==="%c")D=Q}),A.splice(D,0,B)}ol0.log=console.debug||console.log||(()=>{});function YM9(A){try{if(A)ol0.storage.setItem("debug",A);else ol0.storage.removeItem("debug")}catch(B){}}function WM9(){let A;try{A=ol0.storage.getItem("debug")}catch(B){}if(!A&&typeof process!=="undefined"&&"env"in process)A=process.env.DEBUG;return A}function JM9(){try{return localStorage}catch(A){}}NX1.exports=mc1()(ol0);var{formatters:XM9}=NX1.exports;XM9.j=function(A){try{return JSON.stringify(A)}catch(B){return"[UnexpectedJSONParseError]: "+B.message}}});

module.exports = SR1;
