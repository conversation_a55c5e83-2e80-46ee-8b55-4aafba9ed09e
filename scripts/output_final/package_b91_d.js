// E_module package extracted with entry point: b91

var b91=E((VA5,cK1)=>{function _8A(A){return Array.isArray(A)?A:[A]}var W0Q=void 0,Ns1="",k8A=" ",qs1="\\",J0Q=/^\s+$/,X0Q=/(?:[^\\]|^)\\$/,V0Q=/^\\!/,C0Q=/^\\#/,K0Q=/\r?\n/g,H0Q=/^\.{0,2}\/|^\.{1,2}$/,z0Q=/\/$/,Vi="/",x8A="node-ignore";if(typeof Symbol!=="undefined")x8A=Symbol.for("node-ignore");var v8A=x8A,Ci=(A,B,Q)=>{return Object.defineProperty(A,B,{value:Q}),Q},E0Q=/([0-z])-([0-z])/g,b8A=()=>!1,U0Q=(A)=>A.replace(E0Q,(B,Q,D)=>Q.charCodeAt(0)<=D.charCodeAt(0)?B:Ns1),w0Q=(A)=>{let{length:B}=A;return A.slice(0,B-B%2)},$0Q=[[/^\uFEFF/,()=>Ns1],[/((?:\\\\)*?)(\\?\s+)$/,(A,B,Q)=>B+(Q.indexOf("\\")===0?k8A:Ns1)],[/(\\+?)\s/g,(A,B)=>{let{length:Q}=B;return B.slice(0,Q-Q%2)+k8A}],[/[\\$.|*+(){^]/g,(A)=>`\\${A}`],[/(?!\\)\?/g,()=>"[^/]"],[/^\//,()=>"^"],[/\//g,()=>"\\/"],[/^\^*\\\*\\\*\\\//,()=>"^(?:.*\\/)?"],[/^(?=[^^])/,function A(){return!/\/(?!$)/.test(this)?"(?:^|\\/)":"^"}],[/\\\/\\\*\\\*(?=\\\/|$)/g,(A,B,Q)=>B+6<Q.length?"(?:\\/[^\\/]+)*":"\\/.+"],[/(^|[^\\]+)(\\\*)+(?=.+)/g,(A,B,Q)=>{let D=Q.replace(/\\\*/g,"[^\\/]*");return B+D}],[/\\\\\\(?=[$.|*+(){^])/g,()=>qs1],[/\\\\/g,()=>qs1],[/(\\)?\[([^\]/]*?)(\\*)($|\])/g,(A,B,Q,D,Z)=>B===qs1?`\\[${Q}${w0Q(D)}${Z}`:Z==="]"?D.length%2===0?`[${U0Q(Q)}${D}]`:"[]":"[]"],[/(?:[^*])$/,(A)=>/\/$/.test(A)?`${A}$`:`${A}(?=$|\\/$)`]],q0Q=/(^|\\\/)?\\\*$/,v91="regex",mK1="checkRegex",y8A="_",N0Q={[v91](A,B){return`${B?`${B}[^/]+`:"[^/]*"}(?=$|\\/$)`},[mK1](A,B){return`${B?`${B}[^/]*`:"[^/]*"}(?=$|\\/$)`}},L0Q=(A)=>$0Q.reduce((B,[Q,D])=>B.replace(Q,D.bind(A)),A),dK1=(A)=>typeof A==="string",M0Q=(A)=>A&&dK1(A)&&!J0Q.test(A)&&!X0Q.test(A)&&A.indexOf("#")!==0,R0Q=(A)=>A.split(K0Q).filter(Boolean);class f8A{constructor(A,B,Q,D,Z,G){this.pattern=A,this.mark=B,this.negative=Z,Ci(this,"body",Q),Ci(this,"ignoreCase",D),Ci(this,"regexPrefix",G)}get regex(){let A=y8A+v91;if(this[A])return this[A];return this._make(v91,A)}get checkRegex(){let A=y8A+mK1;if(this[A])return this[A];return this._make(mK1,A)}_make(A,B){let Q=this.regexPrefix.replace(q0Q,N0Q[A]),D=this.ignoreCase?new RegExp(Q,"i"):new RegExp(Q);return Ci(this,B,D)}}var O0Q=({pattern:A,mark:B},Q)=>{let D=!1,Z=A;if(Z.indexOf("!")===0)D=!0,Z=Z.substr(1);Z=Z.replace(V0Q,"!").replace(C0Q,"#");let G=L0Q(Z);return new f8A(A,B,Z,Q,D,G)};class h8A{constructor(A){this._ignoreCase=A,this._rules=[]}_add(A){if(A&&A[v8A]){this._rules=this._rules.concat(A._rules._rules),this._added=!0;return}if(dK1(A))A={pattern:A};if(M0Q(A.pattern)){let B=O0Q(A,this._ignoreCase);this._added=!0,this._rules.push(B)}}add(A){return this._added=!1,_8A(dK1(A)?R0Q(A):A).forEach(this._add,this),this._added}test(A,B,Q){let D=!1,Z=!1,G;this._rules.forEach((I)=>{let{negative:Y}=I;if(Z===Y&&D!==Z||Y&&!D&&!Z&&!B)return;if(!I[Q].test(A))return;D=!Y,Z=Y,G=Y?W0Q:I});let F={ignored:D,unignored:Z};if(G)F.rule=G;return F}}var T0Q=(A,B)=>{throw new B(A)},RO=(A,B,Q)=>{if(!dK1(A))return Q(`path must be a string, but got \`${B}\``,TypeError);if(!A)return Q("path must not be empty",TypeError);if(RO.isNotRelative(A))return Q(`path should be a \`path.relative()\`d string, but got "${B}"`,RangeError);return!0},g8A=(A)=>H0Q.test(A);RO.isNotRelative=g8A;RO.convert=(A)=>A;class u8A{constructor({ignorecase:A=!0,ignoreCase:B=A,allowRelativePaths:Q=!1}={}){Ci(this,v8A,!0),this._rules=new h8A(B),this._strictPathCheck=!Q,this._initCache()}_initCache(){this._ignoreCache=Object.create(null),this._testCache=Object.create(null)}add(A){if(this._rules.add(A))this._initCache();return this}addPattern(A){return this.add(A)}_test(A,B,Q,D){let Z=A&&RO.convert(A);return RO(Z,A,this._strictPathCheck?T0Q:b8A),this._t(Z,B,Q,D)}checkIgnore(A){if(!z0Q.test(A))return this.test(A);let B=A.split(Vi).filter(Boolean);if(B.pop(),B.length){let Q=this._t(B.join(Vi)+Vi,this._testCache,!0,B);if(Q.ignored)return Q}return this._rules.test(A,!1,mK1)}_t(A,B,Q,D){if(A in B)return B[A];if(!D)D=A.split(Vi).filter(Boolean);if(D.pop(),!D.length)return B[A]=this._rules.test(A,Q,v91);let Z=this._t(D.join(Vi)+Vi,B,Q,D);return B[A]=Z.ignored?Z:this._rules.test(A,Q,v91)}ignores(A){return this._test(A,this._ignoreCache,!1).ignored}createFilter(){return(A)=>!this.ignores(A)}filter(A){return _8A(A).filter(this.createFilter())}test(A){return this._test(A,this._testCache,!0)}}var Ls1=(A)=>new u8A(A),P0Q=(A)=>RO(A&&RO.convert(A),A,b8A),m8A=()=>{let A=(Q)=>/^\\\\\?\\/.test(Q)||/["<>|\u0000-\u001F]+/u.test(Q)?Q:Q.replace(/\\/g,"/");RO.convert=A;let B=/^[a-z]:\//i;RO.isNotRelative=(Q)=>B.test(Q)||g8A(Q)};if(typeof process!=="undefined"&&process.platform==="win32")m8A();cK1.exports=Ls1;Ls1.default=Ls1;cK1.exports.isPathValid=P0Q;Ci(cK1.exports,Symbol.for("setupWindows"),m8A)});

module.exports = b91;
