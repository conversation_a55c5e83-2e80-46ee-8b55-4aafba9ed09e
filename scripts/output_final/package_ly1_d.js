// dependency_chain package extracted with entry point: ly1

var LZB=E((ta5,NZB)=>{var{defineProperty:Pk1,getOwnPropertyDescriptor:xx6,getOwnPropertyNames:vx6}=Object,bx6=Object.prototype.hasOwnProperty,fx6=(A,B)=>Pk1(A,"name",{value:B,configurable:!0}),hx6=(A,B)=>{for(var Q in B)Pk1(A,Q,{get:B[Q],enumerable:!0})},gx6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of vx6(B))if(!bx6.call(A,Z)&&Z!==Q)Pk1(A,Z,{get:()=>B[Z],enumerable:!(D=xx6(B,Z))||D.enumerable})}return A},ux6=(A)=>gx6(Pk1({},"__esModule",{value:!0}),A),qZB={};hx6(qZB,{isArrayBuffer:()=>mx6});NZB.exports=ux6(qZB);var mx6=fx6((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var SZB=E((TZB)=>{Object.defineProperty(TZB,"__esModule",{value:!0});TZB.fromBase64=void 0;var ox6=jk1(),tx6=/^[A-Za-z0-9+/]*={0,2}$/,ex6=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!tx6.exec(A))throw new TypeError("Invalid base64 string.");let B=ox6.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};TZB.fromBase64=ex6});
var fZB=E((vZB)=>{Object.defineProperty(vZB,"__esModule",{value:!0});vZB.toBase64=void 0;var Yv6=jk1(),Wv6=xZB(),Jv6=(A)=>{let B;if(typeof A==="string")B=Wv6.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return Yv6.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};vZB.toBase64=Jv6});
var hK0=E((Ds5,yk1)=>{var{defineProperty:hZB,getOwnPropertyDescriptor:Xv6,getOwnPropertyNames:Vv6}=Object,Cv6=Object.prototype.hasOwnProperty,bK0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Vv6(B))if(!Cv6.call(A,Z)&&Z!==Q)hZB(A,Z,{get:()=>B[Z],enumerable:!(D=Xv6(B,Z))||D.enumerable})}return A},gZB=(A,B,Q)=>(bK0(A,B,"default"),Q&&bK0(Q,B,"default")),Kv6=(A)=>bK0(hZB({},"__esModule",{value:!0}),A),fK0={};yk1.exports=Kv6(fK0);gZB(fK0,SZB(),yk1.exports);gZB(fK0,fZB(),yk1.exports)});
var jk1=E((ea5,OZB)=>{var{defineProperty:Sk1,getOwnPropertyDescriptor:dx6,getOwnPropertyNames:cx6}=Object,lx6=Object.prototype.hasOwnProperty,MZB=(A,B)=>Sk1(A,"name",{value:B,configurable:!0}),px6=(A,B)=>{for(var Q in B)Sk1(A,Q,{get:B[Q],enumerable:!0})},ix6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of cx6(B))if(!lx6.call(A,Z)&&Z!==Q)Sk1(A,Z,{get:()=>B[Z],enumerable:!(D=dx6(B,Z))||D.enumerable})}return A},nx6=(A)=>ix6(Sk1({},"__esModule",{value:!0}),A),RZB={};px6(RZB,{fromArrayBuffer:()=>sx6,fromString:()=>rx6});OZB.exports=nx6(RZB);var ax6=LZB(),xK0=J1("buffer"),sx6=MZB((A,B=0,Q=A.byteLength-B)=>{if(!ax6.isArrayBuffer(A))throw new TypeError(`The "input" argument must be ArrayBuffer. Received type ${typeof A} (${A})`);return xK0.Buffer.from(A,B,Q)},"fromArrayBuffer"),rx6=MZB((A,B)=>{if(typeof A!=="string")throw new TypeError(`The "input" argument must be of type string. Received type ${typeof A} (${A})`);return B?xK0.Buffer.from(A,B):xK0.Buffer.from(A)},"fromString")});
var xZB=E((Bs5,_ZB)=>{var{defineProperty:kk1,getOwnPropertyDescriptor:Av6,getOwnPropertyNames:Bv6}=Object,Qv6=Object.prototype.hasOwnProperty,vK0=(A,B)=>kk1(A,"name",{value:B,configurable:!0}),Dv6=(A,B)=>{for(var Q in B)kk1(A,Q,{get:B[Q],enumerable:!0})},Zv6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Bv6(B))if(!Qv6.call(A,Z)&&Z!==Q)kk1(A,Z,{get:()=>B[Z],enumerable:!(D=Av6(B,Z))||D.enumerable})}return A},Gv6=(A)=>Zv6(kk1({},"__esModule",{value:!0}),A),jZB={};Dv6(jZB,{fromUtf8:()=>yZB,toUint8Array:()=>Fv6,toUtf8:()=>Iv6});_ZB.exports=Gv6(jZB);var kZB=jk1(),yZB=vK0((A)=>{let B=kZB.fromString(A,"utf8");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength/Uint8Array.BYTES_PER_ELEMENT)},"fromUtf8"),Fv6=vK0((A)=>{if(typeof A==="string")return yZB(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)},"toUint8Array"),Iv6=vK0((A)=>{if(typeof A==="string")return A;if(typeof A!=="object"||typeof A.byteOffset!=="number"||typeof A.byteLength!=="number")throw new Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return kZB.fromArrayBuffer(A.buffer,A.byteOffset,A.byteLength).toString("utf8")},"toUtf8")});

module.exports = ly1;
