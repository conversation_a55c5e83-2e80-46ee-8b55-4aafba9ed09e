// dependency_chain package extracted with entry point: uXB

var $5B=E((U5B)=>{Object.defineProperty(U5B,"__esModule",{value:!0});U5B.toUtf8=U5B.fromUtf8=void 0;var uS6=(A)=>{let B=[];for(let Q=0,D=A.length;Q<D;Q++){let Z=A.charCodeAt(Q);if(Z<128)B.push(Z);else if(Z<2048)B.push(Z>>6|192,Z&63|128);else if(Q+1<A.length&&(Z&64512)===55296&&(<PERSON><PERSON>charCode<PERSON>t(Q+1)&64512)===56320){let G=65536+((Z&1023)<<10)+(A.charCodeAt(++Q)&1023);B.push(G>>18|240,G>>12&63|128,G>>6&63|128,G&63|128)}else B.push(Z>>12|224,Z>>6&63|128,Z&63|128)}return Uint8Array.from(B)};U5B.fromUtf8=uS6;var mS6=(A)=>{let B="";for(let Q=0,D=A.length;Q<D;Q++){let Z=A[Q];if(Z<128)B+=String.fromCharCode(Z);else if(192<=Z&&Z<224){let G=A[++Q];B+=String.fromCharCode((Z&31)<<6|G&63)}else if(240<=Z&&Z<365){let F="%"+[Z,A[++Q],A[++Q],A[++Q]].map((I)=>I.toString(16)).join("%");B+=decodeURIComponent(F)}else B+=String.fromCharCode((Z&15)<<12|(A[++Q]&63)<<6|A[++Q]&63)}return B};U5B.toUtf8=mS6});
var $ZB=E((oa5,wZB)=>{var{defineProperty:Tk1,getOwnPropertyDescriptor:Rx6,getOwnPropertyNames:Ox6}=Object,Tx6=Object.prototype.hasOwnProperty,_K0=(A,B)=>Tk1(A,"name",{value:B,configurable:!0}),Px6=(A,B)=>{for(var Q in B)Tk1(A,Q,{get:B[Q],enumerable:!0})},Sx6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Ox6(B))if(!Tx6.call(A,Z)&&Z!==Q)Tk1(A,Z,{get:()=>B[Z],enumerable:!(D=Rx6(B,Z))||D.enumerable})}return A},jx6=(A)=>Sx6(Tk1({},"__esModule",{value:!0}),A),HZB={};Px6(HZB,{EventStreamMarshaller:()=>UZB,eventStreamSerdeProvider:()=>_x6});wZB.exports=jx6(HZB);var kx6=KZB(),yx6=J1("stream");async function*zZB(A){let B=!1,Q=!1,D=new Array;A.on("error",(Z)=>{if(!B)B=!0;if(Z)throw Z}),A.on("data",(Z)=>{D.push(Z)}),A.on("end",()=>{B=!0});while(!Q){let Z=await new Promise((G)=>setTimeout(()=>G(D.shift()),0));if(Z)yield Z;Q=B&&D.length===0}}_K0(zZB,"readabletoIterable");var EZB=class A{constructor({utf8Encoder:B,utf8Decoder:Q}){this.universalMarshaller=new kx6.EventStreamMarshaller({utf8Decoder:Q,utf8Encoder:B})}deserialize(B,Q){let D=typeof B[Symbol.asyncIterator]==="function"?B:zZB(B);return this.universalMarshaller.deserialize(D,Q)}serialize(B,Q){return yx6.Readable.from(this.universalMarshaller.serialize(B,Q))}};_K0(EZB,"EventStreamMarshaller");var UZB=EZB,_x6=_K0((A)=>new UZB(A),"eventStreamSerdeProvider")});
var IZB=E((sa5,FZB)=>{var{defineProperty:Rk1,getOwnPropertyDescriptor:s_6,getOwnPropertyNames:r_6}=Object,o_6=Object.prototype.hasOwnProperty,_P=(A,B)=>Rk1(A,"name",{value:B,configurable:!0}),t_6=(A,B)=>{for(var Q in B)Rk1(A,Q,{get:B[Q],enumerable:!0})},e_6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of r_6(B))if(!o_6.call(A,Z)&&Z!==Q)Rk1(A,Z,{get:()=>B[Z],enumerable:!(D=s_6(B,Z))||D.enumerable})}return A},Ax6=(A)=>e_6(Rk1({},"__esModule",{value:!0}),A),sDB={};t_6(sDB,{EventStreamCodec:()=>Cx6,HeaderMarshaller:()=>tDB,Int64:()=>Mk1,MessageDecoderStream:()=>Kx6,MessageEncoderStream:()=>Hx6,SmithyMessageDecoderStream:()=>zx6,SmithyMessageEncoderStream:()=>Ex6});FZB.exports=Ax6(sDB);var Bx6=Nk1(),Wm=nDB(),rDB=class A{constructor(B){if(this.bytes=B,B.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static fromNumber(B){if(B>9223372036854776000||B<-9223372036854776000)throw new Error(`${B} is too large (or, if negative, too small) to represent as an Int64`);let Q=new Uint8Array(8);for(let D=7,Z=Math.abs(Math.round(B));D>-1&&Z>0;D--,Z/=256)Q[D]=Z;if(B<0)yK0(Q);return new A(Q)}valueOf(){let B=this.bytes.slice(0),Q=B[0]&128;if(Q)yK0(B);return parseInt(Wm.toHex(B),16)*(Q?-1:1)}toString(){return String(this.valueOf())}};_P(rDB,"Int64");var Mk1=rDB;function yK0(A){for(let B=0;B<8;B++)A[B]^=255;for(let B=7;B>-1;B--)if(A[B]++,A[B]!==0)break}_P(yK0,"negate");var oDB=class A{constructor(B,Q){this.toUtf8=B,this.fromUtf8=Q}format(B){let Q=[];for(let G of Object.keys(B)){let F=this.fromUtf8(G);Q.push(Uint8Array.from([F.byteLength]),F,this.formatHeaderValue(B[G]))}let D=new Uint8Array(Q.reduce((G,F)=>G+F.byteLength,0)),Z=0;for(let G of Q)D.set(G,Z),Z+=G.byteLength;return D}formatHeaderValue(B){switch(B.type){case"boolean":return Uint8Array.from([B.value?0:1]);case"byte":return Uint8Array.from([2,B.value]);case"short":let Q=new DataView(new ArrayBuffer(3));return Q.setUint8(0,3),Q.setInt16(1,B.value,!1),new Uint8Array(Q.buffer);case"integer":let D=new DataView(new ArrayBuffer(5));return D.setUint8(0,4),D.setInt32(1,B.value,!1),new Uint8Array(D.buffer);case"long":let Z=new Uint8Array(9);return Z[0]=5,Z.set(B.value.bytes,1),Z;case"binary":let G=new DataView(new ArrayBuffer(3+B.value.byteLength));G.setUint8(0,6),G.setUint16(1,B.value.byteLength,!1);let F=new Uint8Array(G.buffer);return F.set(B.value,3),F;case"string":let I=this.fromUtf8(B.value),Y=new DataView(new ArrayBuffer(3+I.byteLength));Y.setUint8(0,7),Y.setUint16(1,I.byteLength,!1);let W=new Uint8Array(Y.buffer);return W.set(I,3),W;case"timestamp":let J=new Uint8Array(9);return J[0]=8,J.set(Mk1.fromNumber(B.value.valueOf()).bytes,1),J;case"uuid":if(!Jx6.test(B.value))throw new Error(`Invalid UUID received: ${B.value}`);let X=new Uint8Array(17);return X[0]=9,X.set(Wm.fromHex(B.value.replace(/\-/g,"")),1),X}}parse(B){let Q={},D=0;while(D<B.byteLength){let Z=B.getUint8(D++),G=this.toUtf8(new Uint8Array(B.buffer,B.byteOffset+D,Z));switch(D+=Z,B.getUint8(D++)){case 0:Q[G]={type:aDB,value:!0};break;case 1:Q[G]={type:aDB,value:!1};break;case 2:Q[G]={type:Qx6,value:B.getInt8(D++)};break;case 3:Q[G]={type:Dx6,value:B.getInt16(D,!1)},D+=2;break;case 4:Q[G]={type:Zx6,value:B.getInt32(D,!1)},D+=4;break;case 5:Q[G]={type:Gx6,value:new Mk1(new Uint8Array(B.buffer,B.byteOffset+D,8))},D+=8;break;case 6:let F=B.getUint16(D,!1);D+=2,Q[G]={type:Fx6,value:new Uint8Array(B.buffer,B.byteOffset+D,F)},D+=F;break;case 7:let I=B.getUint16(D,!1);D+=2,Q[G]={type:Ix6,value:this.toUtf8(new Uint8Array(B.buffer,B.byteOffset+D,I))},D+=I;break;case 8:Q[G]={type:Yx6,value:new Date(new Mk1(new Uint8Array(B.buffer,B.byteOffset+D,8)).valueOf())},D+=8;break;case 9:let Y=new Uint8Array(B.buffer,B.byteOffset+D,16);D+=16,Q[G]={type:Wx6,value:`${Wm.toHex(Y.subarray(0,4))}-${Wm.toHex(Y.subarray(4,6))}-${Wm.toHex(Y.subarray(6,8))}-${Wm.toHex(Y.subarray(8,10))}-${Wm.toHex(Y.subarray(10))}`};break;default:throw new Error("Unrecognized header type tag")}}return Q}};_P(oDB,"HeaderMarshaller");var tDB=oDB,aDB="boolean",Qx6="byte",Dx6="short",Zx6="integer",Gx6="long",Fx6="binary",Ix6="string",Yx6="timestamp",Wx6="uuid",Jx6=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,Xx6=Nk1(),eDB=4,jx=eDB*2,Jm=4,Vx6=jx+Jm*2;function AZB({byteLength:A,byteOffset:B,buffer:Q}){if(A<Vx6)throw new Error("Provided message too short to accommodate event stream message overhead");let D=new DataView(Q,B,A),Z=D.getUint32(0,!1);if(A!==Z)throw new Error("Reported message length does not match received message length");let G=D.getUint32(eDB,!1),F=D.getUint32(jx,!1),I=D.getUint32(A-Jm,!1),Y=new Xx6.Crc32().update(new Uint8Array(Q,B,jx));if(F!==Y.digest())throw new Error(`The prelude checksum specified in the message (${F}) does not match the calculated CRC32 checksum (${Y.digest()})`);if(Y.update(new Uint8Array(Q,B+jx,A-(jx+Jm))),I!==Y.digest())throw new Error(`The message checksum (${Y.digest()}) did not match the expected value of ${I}`);return{headers:new DataView(Q,B+jx+Jm,G),body:new Uint8Array(Q,B+jx+Jm+G,Z-G-(jx+Jm+Jm))}}_P(AZB,"splitMessage");var BZB=class A{constructor(B,Q){this.headerMarshaller=new tDB(B,Q),this.messageBuffer=[],this.isEndOfStream=!1}feed(B){this.messageBuffer.push(this.decode(B))}endOfStream(){this.isEndOfStream=!0}getMessage(){let B=this.messageBuffer.pop(),Q=this.isEndOfStream;return{getMessage(){return B},isEndOfStream(){return Q}}}getAvailableMessages(){let B=this.messageBuffer;this.messageBuffer=[];let Q=this.isEndOfStream;return{getMessages(){return B},isEndOfStream(){return Q}}}encode({headers:B,body:Q}){let D=this.headerMarshaller.format(B),Z=D.byteLength+Q.byteLength+16,G=new Uint8Array(Z),F=new DataView(G.buffer,G.byteOffset,G.byteLength),I=new Bx6.Crc32;return F.setUint32(0,Z,!1),F.setUint32(4,D.byteLength,!1),F.setUint32(8,I.update(G.subarray(0,8)).digest(),!1),G.set(D,12),G.set(Q,D.byteLength+12),F.setUint32(Z-4,I.update(G.subarray(8,Z-4)).digest(),!1),G}decode(B){let{headers:Q,body:D}=AZB(B);return{headers:this.headerMarshaller.parse(Q),body:D}}formatHeaders(B){return this.headerMarshaller.format(B)}};_P(BZB,"EventStreamCodec");var Cx6=BZB,QZB=class A{constructor(B){this.options=B}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(let B of this.options.inputStream)yield this.options.decoder.decode(B)}};_P(QZB,"MessageDecoderStream");var Kx6=QZB,DZB=class A{constructor(B){this.options=B}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(let B of this.options.messageStream)yield this.options.encoder.encode(B);if(this.options.includeEndFrame)yield new Uint8Array(0)}};_P(DZB,"MessageEncoderStream");var Hx6=DZB,ZZB=class A{constructor(B){this.options=B}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(let B of this.options.messageStream){let Q=await this.options.deserializer(B);if(Q===void 0)continue;yield Q}}};_P(ZZB,"SmithyMessageDecoderStream");var zx6=ZZB,GZB=class A{constructor(B){this.options=B}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(let B of this.options.inputStream)yield this.options.serializer(B)}};_P(GZB,"SmithyMessageEncoderStream");var Ex6=GZB});
var KZB=E((ra5,CZB)=>{var{defineProperty:Ok1,getOwnPropertyDescriptor:Ux6,getOwnPropertyNames:wx6}=Object,$x6=Object.prototype.hasOwnProperty,Ve=(A,B)=>Ok1(A,"name",{value:B,configurable:!0}),qx6=(A,B)=>{for(var Q in B)Ok1(A,Q,{get:B[Q],enumerable:!0})},Nx6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of wx6(B))if(!$x6.call(A,Z)&&Z!==Q)Ok1(A,Z,{get:()=>B[Z],enumerable:!(D=Ux6(B,Z))||D.enumerable})}return A},Lx6=(A)=>Nx6(Ok1({},"__esModule",{value:!0}),A),YZB={};qx6(YZB,{EventStreamMarshaller:()=>VZB,eventStreamSerdeProvider:()=>Mx6});CZB.exports=Lx6(YZB);var qD1=IZB();function WZB(A){let B=0,Q=0,D=null,Z=null,G=Ve((I)=>{if(typeof I!=="number")throw new Error("Attempted to allocate an event message where size was not a number: "+I);B=I,Q=4,D=new Uint8Array(I),new DataView(D.buffer).setUint32(0,I,!1)},"allocateMessage"),F=Ve(async function*(){let I=A[Symbol.asyncIterator]();while(!0){let{value:Y,done:W}=await I.next();if(W){if(!B)return;else if(B===Q)yield D;else throw new Error("Truncated event message received.");return}let J=Y.length,X=0;while(X<J){if(!D){let C=J-X;if(!Z)Z=new Uint8Array(4);let K=Math.min(4-Q,C);if(Z.set(Y.slice(X,X+K),Q),Q+=K,X+=K,Q<4)break;G(new DataView(Z.buffer).getUint32(0,!1)),Z=null}let V=Math.min(B-Q,J-X);if(D.set(Y.slice(X,X+V),Q),Q+=V,X+=V,B&&B===Q)yield D,D=null,B=0,Q=0}}},"iterator");return{[Symbol.asyncIterator]:F}}Ve(WZB,"getChunkedStream");function JZB(A,B){return async function(Q){let{value:D}=Q.headers[":message-type"];if(D==="error"){let Z=new Error(Q.headers[":error-message"].value||"UnknownError");throw Z.name=Q.headers[":error-code"].value,Z}else if(D==="exception"){let Z=Q.headers[":exception-type"].value,G={[Z]:Q},F=await A(G);if(F.$unknown){let I=new Error(B(Q.body));throw I.name=Z,I}throw F[Z]}else if(D==="event"){let Z={[Q.headers[":event-type"].value]:Q},G=await A(Z);if(G.$unknown)return;return G}else throw Error(`Unrecognizable event type: ${Q.headers[":event-type"].value}`)}}Ve(JZB,"getMessageUnmarshaller");var XZB=class A{constructor({utf8Encoder:B,utf8Decoder:Q}){this.eventStreamCodec=new qD1.EventStreamCodec(B,Q),this.utfEncoder=B}deserialize(B,Q){let D=WZB(B);return new qD1.SmithyMessageDecoderStream({messageStream:new qD1.MessageDecoderStream({inputStream:D,decoder:this.eventStreamCodec}),deserializer:JZB(Q,this.utfEncoder)})}serialize(B,Q){return new qD1.MessageEncoderStream({messageStream:new qD1.SmithyMessageEncoderStream({inputStream:B,serializer:Q}),encoder:this.eventStreamCodec,includeEndFrame:!0})}};Ve(XZB,"EventStreamMarshaller");var VZB=XZB,Mx6=Ve((A)=>new VZB(A),"eventStreamSerdeProvider")});
var L5B=E((q5B)=>{Object.defineProperty(q5B,"__esModule",{value:!0});q5B.toUtf8=q5B.fromUtf8=void 0;function cS6(A){return new TextEncoder().encode(A)}q5B.fromUtf8=cS6;function lS6(A){return new TextDecoder("utf-8").decode(A)}q5B.toUtf8=lS6});
var Nk1=E((jK0)=>{Object.defineProperty(jK0,"__esModule",{value:!0});jK0.AwsCrc32=jK0.Crc32=jK0.crc32=void 0;var x_6=TK0(),v_6=PK0();function b_6(A){return new uDB().update(A).digest()}jK0.crc32=b_6;var uDB=function(){function A(){this.checksum=**********}return A.prototype.update=function(B){var Q,D;try{for(var Z=x_6.__values(B),G=Z.next();!G.done;G=Z.next()){var F=G.value;this.checksum=this.checksum>>>8^h_6[(this.checksum^F)&255]}}catch(I){Q={error:I}}finally{try{if(G&&!G.done&&(D=Z.return))D.call(Z)}finally{if(Q)throw Q.error}}return this},A.prototype.digest=function(){return(this.checksum^**********)>>>0},A}();jK0.Crc32=uDB;var f_6=[0,**********,**********,**********,124634137,**********,**********,**********,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918000,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],h_6=v_6.uint32ArrayFrom(f_6),g_6=gDB();Object.defineProperty(jK0,"AwsCrc32",{enumerable:!0,get:function(){return g_6.AwsCrc32}})});
var PDB=E((ODB)=>{Object.defineProperty(ODB,"__esModule",{value:!0});ODB.isEmptyData=void 0;function R_6(A){if(typeof A==="string")return A.length===0;return A.byteLength===0}ODB.isEmptyData=R_6});
var PK0=E((Xe)=>{Object.defineProperty(Xe,"__esModule",{value:!0});Xe.uint32ArrayFrom=Xe.numToUint8=Xe.isEmptyData=Xe.convertToBuffer=void 0;var P_6=RDB();Object.defineProperty(Xe,"convertToBuffer",{enumerable:!0,get:function(){return P_6.convertToBuffer}});var S_6=PDB();Object.defineProperty(Xe,"isEmptyData",{enumerable:!0,get:function(){return S_6.isEmptyData}});var j_6=kDB();Object.defineProperty(Xe,"numToUint8",{enumerable:!0,get:function(){return j_6.numToUint8}});var k_6=xDB();Object.defineProperty(Xe,"uint32ArrayFrom",{enumerable:!0,get:function(){return k_6.uint32ArrayFrom}})});
var RDB=E((LDB)=>{Object.defineProperty(LDB,"__esModule",{value:!0});LDB.convertToBuffer=void 0;var N_6=ZK0(),L_6=typeof Buffer!=="undefined"&&Buffer.from?function(A){return Buffer.from(A,"utf8")}:N_6.fromUtf8;function M_6(A){if(A instanceof Uint8Array)return A;if(typeof A==="string")return L_6(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)}LDB.convertToBuffer=M_6});
var TK0=E((fa5,qk1)=>{/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var DDB,ZDB,GDB,FDB,IDB,YDB,WDB,JDB,XDB,$k1,OK0,VDB,CDB,Je,KDB,HDB,zDB,EDB,UDB,wDB,$DB,qDB,NDB;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof qk1==="object"&&typeof fa5==="object")A(Q(B,Q(fa5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(Q,D){Q.__proto__=D}||function(Q,D){for(var Z in D)if(D.hasOwnProperty(Z))Q[Z]=D[Z]};DDB=function(Q,D){B(Q,D);function Z(){this.constructor=Q}Q.prototype=D===null?Object.create(D):(Z.prototype=D.prototype,new Z)},ZDB=Object.assign||function(Q){for(var D,Z=1,G=arguments.length;Z<G;Z++){D=arguments[Z];for(var F in D)if(Object.prototype.hasOwnProperty.call(D,F))Q[F]=D[F]}return Q},GDB=function(Q,D){var Z={};for(var G in Q)if(Object.prototype.hasOwnProperty.call(Q,G)&&D.indexOf(G)<0)Z[G]=Q[G];if(Q!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var F=0,G=Object.getOwnPropertySymbols(Q);F<G.length;F++)if(D.indexOf(G[F])<0&&Object.prototype.propertyIsEnumerable.call(Q,G[F]))Z[G[F]]=Q[G[F]]}return Z},FDB=function(Q,D,Z,G){var F=arguments.length,I=F<3?D:G===null?G=Object.getOwnPropertyDescriptor(D,Z):G,Y;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")I=Reflect.decorate(Q,D,Z,G);else for(var W=Q.length-1;W>=0;W--)if(Y=Q[W])I=(F<3?Y(I):F>3?Y(D,Z,I):Y(D,Z))||I;return F>3&&I&&Object.defineProperty(D,Z,I),I},IDB=function(Q,D){return function(Z,G){D(Z,G,Q)}},YDB=function(Q,D){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(Q,D)},WDB=function(Q,D,Z,G){function F(I){return I instanceof Z?I:new Z(function(Y){Y(I)})}return new(Z||(Z=Promise))(function(I,Y){function W(V){try{X(G.next(V))}catch(C){Y(C)}}function J(V){try{X(G.throw(V))}catch(C){Y(C)}}function X(V){V.done?I(V.value):F(V.value).then(W,J)}X((G=G.apply(Q,D||[])).next())})},JDB=function(Q,D){var Z={label:0,sent:function(){if(I[0]&1)throw I[1];return I[1]},trys:[],ops:[]},G,F,I,Y;return Y={next:W(0),throw:W(1),return:W(2)},typeof Symbol==="function"&&(Y[Symbol.iterator]=function(){return this}),Y;function W(X){return function(V){return J([X,V])}}function J(X){if(G)throw new TypeError("Generator is already executing.");while(Z)try{if(G=1,F&&(I=X[0]&2?F.return:X[0]?F.throw||((I=F.return)&&I.call(F),0):F.next)&&!(I=I.call(F,X[1])).done)return I;if(F=0,I)X=[X[0]&2,I.value];switch(X[0]){case 0:case 1:I=X;break;case 4:return Z.label++,{value:X[1],done:!1};case 5:Z.label++,F=X[1],X=[0];continue;case 7:X=Z.ops.pop(),Z.trys.pop();continue;default:if((I=Z.trys,!(I=I.length>0&&I[I.length-1]))&&(X[0]===6||X[0]===2)){Z=0;continue}if(X[0]===3&&(!I||X[1]>I[0]&&X[1]<I[3])){Z.label=X[1];break}if(X[0]===6&&Z.label<I[1]){Z.label=I[1],I=X;break}if(I&&Z.label<I[2]){Z.label=I[2],Z.ops.push(X);break}if(I[2])Z.ops.pop();Z.trys.pop();continue}X=D.call(Q,Z)}catch(V){X=[6,V],F=0}finally{G=I=0}if(X[0]&5)throw X[1];return{value:X[0]?X[1]:void 0,done:!0}}},NDB=function(Q,D,Z,G){if(G===void 0)G=Z;Q[G]=D[Z]},XDB=function(Q,D){for(var Z in Q)if(Z!=="default"&&!D.hasOwnProperty(Z))D[Z]=Q[Z]},$k1=function(Q){var D=typeof Symbol==="function"&&Symbol.iterator,Z=D&&Q[D],G=0;if(Z)return Z.call(Q);if(Q&&typeof Q.length==="number")return{next:function(){if(Q&&G>=Q.length)Q=void 0;return{value:Q&&Q[G++],done:!Q}}};throw new TypeError(D?"Object is not iterable.":"Symbol.iterator is not defined.")},OK0=function(Q,D){var Z=typeof Symbol==="function"&&Q[Symbol.iterator];if(!Z)return Q;var G=Z.call(Q),F,I=[],Y;try{while((D===void 0||D-- >0)&&!(F=G.next()).done)I.push(F.value)}catch(W){Y={error:W}}finally{try{if(F&&!F.done&&(Z=G.return))Z.call(G)}finally{if(Y)throw Y.error}}return I},VDB=function(){for(var Q=[],D=0;D<arguments.length;D++)Q=Q.concat(OK0(arguments[D]));return Q},CDB=function(){for(var Q=0,D=0,Z=arguments.length;D<Z;D++)Q+=arguments[D].length;for(var G=Array(Q),F=0,D=0;D<Z;D++)for(var I=arguments[D],Y=0,W=I.length;Y<W;Y++,F++)G[F]=I[Y];return G},Je=function(Q){return this instanceof Je?(this.v=Q,this):new Je(Q)},KDB=function(Q,D,Z){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var G=Z.apply(Q,D||[]),F,I=[];return F={},Y("next"),Y("throw"),Y("return"),F[Symbol.asyncIterator]=function(){return this},F;function Y(K){if(G[K])F[K]=function(H){return new Promise(function(z,$){I.push([K,H,z,$])>1||W(K,H)})}}function W(K,H){try{J(G[K](H))}catch(z){C(I[0][3],z)}}function J(K){K.value instanceof Je?Promise.resolve(K.value.v).then(X,V):C(I[0][2],K)}function X(K){W("next",K)}function V(K){W("throw",K)}function C(K,H){if(K(H),I.shift(),I.length)W(I[0][0],I[0][1])}},HDB=function(Q){var D,Z;return D={},G("next"),G("throw",function(F){throw F}),G("return"),D[Symbol.iterator]=function(){return this},D;function G(F,I){D[F]=Q[F]?function(Y){return(Z=!Z)?{value:Je(Q[F](Y)),done:F==="return"}:I?I(Y):Y}:I}},zDB=function(Q){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var D=Q[Symbol.asyncIterator],Z;return D?D.call(Q):(Q=typeof $k1==="function"?$k1(Q):Q[Symbol.iterator](),Z={},G("next"),G("throw"),G("return"),Z[Symbol.asyncIterator]=function(){return this},Z);function G(I){Z[I]=Q[I]&&function(Y){return new Promise(function(W,J){Y=Q[I](Y),F(W,J,Y.done,Y.value)})}}function F(I,Y,W,J){Promise.resolve(J).then(function(X){I({value:X,done:W})},Y)}},EDB=function(Q,D){if(Object.defineProperty)Object.defineProperty(Q,"raw",{value:D});else Q.raw=D;return Q},UDB=function(Q){if(Q&&Q.__esModule)return Q;var D={};if(Q!=null){for(var Z in Q)if(Object.hasOwnProperty.call(Q,Z))D[Z]=Q[Z]}return D.default=Q,D},wDB=function(Q){return Q&&Q.__esModule?Q:{default:Q}},$DB=function(Q,D){if(!D.has(Q))throw new TypeError("attempted to get private field on non-instance");return D.get(Q)},qDB=function(Q,D,Z){if(!D.has(Q))throw new TypeError("attempted to set private field on non-instance");return D.set(Q,Z),Z},A("__extends",DDB),A("__assign",ZDB),A("__rest",GDB),A("__decorate",FDB),A("__param",IDB),A("__metadata",YDB),A("__awaiter",WDB),A("__generator",JDB),A("__exportStar",XDB),A("__createBinding",NDB),A("__values",$k1),A("__read",OK0),A("__spread",VDB),A("__spreadArrays",CDB),A("__await",Je),A("__asyncGenerator",KDB),A("__asyncDelegator",HDB),A("__asyncValues",zDB),A("__makeTemplateObject",EDB),A("__importStar",UDB),A("__importDefault",wDB),A("__classPrivateFieldGet",$DB),A("__classPrivateFieldSet",qDB)})});
var ZK0=E((O5B)=>{Object.defineProperty(O5B,"__esModule",{value:!0});O5B.toUtf8=O5B.fromUtf8=void 0;var M5B=$5B(),R5B=L5B(),iS6=(A)=>typeof TextEncoder==="function"?R5B.fromUtf8(A):M5B.fromUtf8(A);O5B.fromUtf8=iS6;var nS6=(A)=>typeof TextDecoder==="function"?R5B.toUtf8(A):M5B.toUtf8(A);O5B.toUtf8=nS6});
var gDB=E((fDB)=>{Object.defineProperty(fDB,"__esModule",{value:!0});fDB.AwsCrc32=void 0;var vDB=TK0(),SK0=PK0(),bDB=Nk1(),__6=function(){function A(){this.crc32=new bDB.Crc32}return A.prototype.update=function(B){if(SK0.isEmptyData(B))return;this.crc32.update(SK0.convertToBuffer(B))},A.prototype.digest=function(){return vDB.__awaiter(this,void 0,void 0,function(){return vDB.__generator(this,function(B){return[2,SK0.numToUint8(this.crc32.digest())]})})},A.prototype.reset=function(){this.crc32=new bDB.Crc32},A}();fDB.AwsCrc32=__6});
var kDB=E((SDB)=>{Object.defineProperty(SDB,"__esModule",{value:!0});SDB.numToUint8=void 0;function O_6(A){return new Uint8Array([(A&4278190080)>>24,(A&16711680)>>16,(A&65280)>>8,A&255])}SDB.numToUint8=O_6});
var nDB=E((aa5,iDB)=>{var{defineProperty:Lk1,getOwnPropertyDescriptor:c_6,getOwnPropertyNames:l_6}=Object,p_6=Object.prototype.hasOwnProperty,mDB=(A,B)=>Lk1(A,"name",{value:B,configurable:!0}),i_6=(A,B)=>{for(var Q in B)Lk1(A,Q,{get:B[Q],enumerable:!0})},n_6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of l_6(B))if(!p_6.call(A,Z)&&Z!==Q)Lk1(A,Z,{get:()=>B[Z],enumerable:!(D=c_6(B,Z))||D.enumerable})}return A},a_6=(A)=>n_6(Lk1({},"__esModule",{value:!0}),A),dDB={};i_6(dDB,{fromHex:()=>lDB,toHex:()=>pDB});iDB.exports=a_6(dDB);var cDB={},kK0={};for(let A=0;A<256;A++){let B=A.toString(16).toLowerCase();if(B.length===1)B=`0${B}`;cDB[A]=B,kK0[B]=A}function lDB(A){if(A.length%2!==0)throw new Error("Hex encoded strings must have an even number length");let B=new Uint8Array(A.length/2);for(let Q=0;Q<A.length;Q+=2){let D=A.slice(Q,Q+2).toLowerCase();if(D in kK0)B[Q/2]=kK0[D];else throw new Error(`Cannot decode unrecognized sequence ${D} as hexadecimal`)}return B}mDB(lDB,"fromHex");function pDB(A){let B="";for(let Q=0;Q<A.byteLength;Q++)B+=cDB[A[Q]];return B}mDB(pDB,"toHex")});
var xDB=E((yDB)=>{Object.defineProperty(yDB,"__esModule",{value:!0});yDB.uint32ArrayFrom=void 0;function T_6(A){if(!Uint32Array.from){var B=new Uint32Array(A.length),Q=0;while(Q<A.length)B[Q]=A[Q],Q+=1;return B}return Uint32Array.from(A)}yDB.uint32ArrayFrom=T_6});

module.exports = uXB;
