// dependency_chain package extracted with entry point: _o0

var So0=E((wp8,Ey9)=>{Ey9.exports={single:{topLeft:"┌",top:"─",topRight:"┐",right:"│",bottomRight:"┘",bottom:"─",bottomLeft:"└",left:"│"},double:{topLeft:"╔",top:"═",topRight:"╗",right:"║",bottomRight:"╝",bottom:"═",bottomLeft:"╚",left:"║"},round:{topLeft:"╭",top:"─",topRight:"╮",right:"│",bottomRight:"╯",bottom:"─",bottomLeft:"╰",left:"│"},bold:{topLeft:"┏",top:"━",topRight:"┓",right:"┃",bottomRight:"┛",bottom:"━",bottomLeft:"┗",left:"┃"},singleDouble:{topLeft:"╓",top:"─",topRight:"╖",right:"║",bottomRight:"╜",bottom:"─",bottomLeft:"╙",left:"║"},doubleSingle:{topLeft:"╒",top:"═",topRight:"╕",right:"│",bottomRight:"╛",bottom:"═",bottomLeft:"╘",left:"│"},classic:{topLeft:"+",top:"-",topRight:"+",right:"|",bottomRight:"+",bottom:"-",bottomLeft:"+",left:"|"},arrow:{topLeft:"↘",top:"↓",topRight:"↙",right:"←",bottomRight:"↖",bottom:"↑",bottomLeft:"↗",left:"→"}}});
var ko0=E(($p8,ap1)=>{var jo0=So0();ap1.exports=jo0;ap1.exports.default=jo0});

module.exports = _o0;
