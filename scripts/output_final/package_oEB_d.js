// dependency_chain package extracted with entry point: oEB

var $CB=E((Is6)=>{var DJ=sy1(),uV=oy1(),$8=uV.implSymbol,Gs6=uV.ctorRegistrySymbol;Is6.is=(A)=>{return uV.isObject(A)&&uV.hasOwn(A,$8)&&A[$8]instanceof MM.implementation};Is6.isImpl=(A)=>{return uV.isObject(A)&&A instanceof MM.implementation};Is6.convert=(A,B,{context:Q="The provided value"}={})=>{if(Is6.is(B))return uV.implForWrapper(B);throw new A.TypeError(`${Q} is not of type 'URL'.`)};function ECB(A,B){let Q;if(B!==void 0)Q=B.prototype;if(!uV.isObject(Q))Q=A[Gs6].URL.prototype;return Object.create(Q)}Is6.create=(A,B,Q)=>{let D=ECB(A);return Is6.setup(D,A,B,Q)};Is6.createImpl=(A,B,Q)=>{let D=Is6.create(A,B,Q);return uV.implForWrapper(D)};Is6._internalSetup=(A,B)=>{};Is6.setup=(A,B,Q=[],D={})=>{if(D.wrapper=A,Is6._internalSetup(A,B),Object.defineProperty(A,$8,{value:new MM.implementation(B,Q,D),configurable:!0}),A[$8][uV.wrapperSymbol]=A,MM.init)MM.init(A[$8]);return A};Is6.new=(A,B)=>{let Q=ECB(A,B);if(Is6._internalSetup(Q,A),Object.defineProperty(Q,$8,{value:Object.create(MM.implementation.prototype),configurable:!0}),Q[$8][uV.wrapperSymbol]=Q,MM.init)MM.init(Q[$8]);return Q[$8]};var Fs6=new Set(["Window","Worker"]);Is6.install=(A,B)=>{if(!B.some((Z)=>Fs6.has(Z)))return;let Q=uV.initCtorRegistry(A);class D{constructor(Z){if(arguments.length<1)throw new A.TypeError(`Failed to construct 'URL': 1 argument required, but only ${arguments.length} present.`);let G=[];{let F=arguments[0];F=DJ.USVString(F,{context:"Failed to construct 'URL': parameter 1",globals:A}),G.push(F)}{let F=arguments[1];if(F!==void 0)F=DJ.USVString(F,{context:"Failed to construct 'URL': parameter 2",globals:A});G.push(F)}return Is6.setup(Object.create(new.target.prototype),A,G)}toJSON(){let Z=this!==null&&this!==void 0?this:A;if(!Is6.is(Z))throw new A.TypeError("'toJSON' called on an object that is not a valid instance of URL.");return Z[$8].toJSON()}get href(){let Z=this!==null&&this!==void 0?this:A;if(!Is6.is(Z))throw new A.TypeError("'get href' called on an object that is not a valid instance of URL.");return Z[$8].href}set href(Z){let G=this!==null&&this!==void 0?this:A;if(!Is6.is(G))throw new A.TypeError("'set href' called on an object that is not a valid instance of URL.");Z=DJ.USVString(Z,{context:"Failed to set the 'href' property on 'URL': The provided value",globals:A}),G[$8].href=Z}toString(){let Z=this;if(!Is6.is(Z))throw new A.TypeError("'toString' called on an object that is not a valid instance of URL.");return Z[$8].href}get origin(){let Z=this!==null&&this!==void 0?this:A;if(!Is6.is(Z))throw new A.TypeError("'get origin' called on an object that is not a valid instance of URL.");return Z[$8].origin}get protocol(){let Z=this!==null&&this!==void 0?this:A;if(!Is6.is(Z))throw new A.TypeError("'get protocol' called on an object that is not a valid instance of URL.");return Z[$8].protocol}set protocol(Z){let G=this!==null&&this!==void 0?this:A;if(!Is6.is(G))throw new A.TypeError("'set protocol' called on an object that is not a valid instance of URL.");Z=DJ.USVString(Z,{context:"Failed to set the 'protocol' property on 'URL': The provided value",globals:A}),G[$8].protocol=Z}get username(){let Z=this!==null&&this!==void 0?this:A;if(!Is6.is(Z))throw new A.TypeError("'get username' called on an object that is not a valid instance of URL.");return Z[$8].username}set username(Z){let G=this!==null&&this!==void 0?this:A;if(!Is6.is(G))throw new A.TypeError("'set username' called on an object that is not a valid instance of URL.");Z=DJ.USVString(Z,{context:"Failed to set the 'username' property on 'URL': The provided value",globals:A}),G[$8].username=Z}get password(){let Z=this!==null&&this!==void 0?this:A;if(!Is6.is(Z))throw new A.TypeError("'get password' called on an object that is not a valid instance of URL.");return Z[$8].password}set password(Z){let G=this!==null&&this!==void 0?this:A;if(!Is6.is(G))throw new A.TypeError("'set password' called on an object that is not a valid instance of URL.");Z=DJ.USVString(Z,{context:"Failed to set the 'password' property on 'URL': The provided value",globals:A}),G[$8].password=Z}get host(){let Z=this!==null&&this!==void 0?this:A;if(!Is6.is(Z))throw new A.TypeError("'get host' called on an object that is not a valid instance of URL.");return Z[$8].host}set host(Z){let G=this!==null&&this!==void 0?this:A;if(!Is6.is(G))throw new A.TypeError("'set host' called on an object that is not a valid instance of URL.");Z=DJ.USVString(Z,{context:"Failed to set the 'host' property on 'URL': The provided value",globals:A}),G[$8].host=Z}get hostname(){let Z=this!==null&&this!==void 0?this:A;if(!Is6.is(Z))throw new A.TypeError("'get hostname' called on an object that is not a valid instance of URL.");return Z[$8].hostname}set hostname(Z){let G=this!==null&&this!==void 0?this:A;if(!Is6.is(G))throw new A.TypeError("'set hostname' called on an object that is not a valid instance of URL.");Z=DJ.USVString(Z,{context:"Failed to set the 'hostname' property on 'URL': The provided value",globals:A}),G[$8].hostname=Z}get port(){let Z=this!==null&&this!==void 0?this:A;if(!Is6.is(Z))throw new A.TypeError("'get port' called on an object that is not a valid instance of URL.");return Z[$8].port}set port(Z){let G=this!==null&&this!==void 0?this:A;if(!Is6.is(G))throw new A.TypeError("'set port' called on an object that is not a valid instance of URL.");Z=DJ.USVString(Z,{context:"Failed to set the 'port' property on 'URL': The provided value",globals:A}),G[$8].port=Z}get pathname(){let Z=this!==null&&this!==void 0?this:A;if(!Is6.is(Z))throw new A.TypeError("'get pathname' called on an object that is not a valid instance of URL.");return Z[$8].pathname}set pathname(Z){let G=this!==null&&this!==void 0?this:A;if(!Is6.is(G))throw new A.TypeError("'set pathname' called on an object that is not a valid instance of URL.");Z=DJ.USVString(Z,{context:"Failed to set the 'pathname' property on 'URL': The provided value",globals:A}),G[$8].pathname=Z}get search(){let Z=this!==null&&this!==void 0?this:A;if(!Is6.is(Z))throw new A.TypeError("'get search' called on an object that is not a valid instance of URL.");return Z[$8].search}set search(Z){let G=this!==null&&this!==void 0?this:A;if(!Is6.is(G))throw new A.TypeError("'set search' called on an object that is not a valid instance of URL.");Z=DJ.USVString(Z,{context:"Failed to set the 'search' property on 'URL': The provided value",globals:A}),G[$8].search=Z}get searchParams(){let Z=this!==null&&this!==void 0?this:A;if(!Is6.is(Z))throw new A.TypeError("'get searchParams' called on an object that is not a valid instance of URL.");return uV.getSameObject(this,"searchParams",()=>{return uV.tryWrapperForImpl(Z[$8].searchParams)})}get hash(){let Z=this!==null&&this!==void 0?this:A;if(!Is6.is(Z))throw new A.TypeError("'get hash' called on an object that is not a valid instance of URL.");return Z[$8].hash}set hash(Z){let G=this!==null&&this!==void 0?this:A;if(!Is6.is(G))throw new A.TypeError("'set hash' called on an object that is not a valid instance of URL.");Z=DJ.USVString(Z,{context:"Failed to set the 'hash' property on 'URL': The provided value",globals:A}),G[$8].hash=Z}static parse(Z){if(arguments.length<1)throw new A.TypeError(`Failed to execute 'parse' on 'URL': 1 argument required, but only ${arguments.length} present.`);let G=[];{let F=arguments[0];F=DJ.USVString(F,{context:"Failed to execute 'parse' on 'URL': parameter 1",globals:A}),G.push(F)}{let F=arguments[1];if(F!==void 0)F=DJ.USVString(F,{context:"Failed to execute 'parse' on 'URL': parameter 2",globals:A});G.push(F)}return uV.tryWrapperForImpl(MM.implementation.parse(A,...G))}static canParse(Z){if(arguments.length<1)throw new A.TypeError(`Failed to execute 'canParse' on 'URL': 1 argument required, but only ${arguments.length} present.`);let G=[];{let F=arguments[0];F=DJ.USVString(F,{context:"Failed to execute 'canParse' on 'URL': parameter 1",globals:A}),G.push(F)}{let F=arguments[1];if(F!==void 0)F=DJ.USVString(F,{context:"Failed to execute 'canParse' on 'URL': parameter 2",globals:A});G.push(F)}return MM.implementation.canParse(...G)}}if(Object.defineProperties(D.prototype,{toJSON:{enumerable:!0},href:{enumerable:!0},toString:{enumerable:!0},origin:{enumerable:!0},protocol:{enumerable:!0},username:{enumerable:!0},password:{enumerable:!0},host:{enumerable:!0},hostname:{enumerable:!0},port:{enumerable:!0},pathname:{enumerable:!0},search:{enumerable:!0},searchParams:{enumerable:!0},hash:{enumerable:!0},[Symbol.toStringTag]:{value:"URL",configurable:!0}}),Object.defineProperties(D,{parse:{enumerable:!0},canParse:{enumerable:!0}}),Q.URL=D,Object.defineProperty(A,"URL",{configurable:!0,writable:!0,value:D}),B.includes("Window"))Object.defineProperty(A,"webkitURL",{configurable:!0,writable:!0,value:D})};var MM=zCB()});
var $m=E((NHB)=>{Object.defineProperty(NHB,"__esModule",{value:!0});NHB.OAuth2Client=NHB.ClientAuthentication=NHB.CertificateFormat=NHB.CodeChallengeMethod=void 0;var mo6=X$(),CE0=J1("querystring"),do6=J1("stream"),co6=YE0(),KE0=he(),lo6=PM(),po6=VE0(),qHB;(function(A){A.Plain="plain",A.S256="S256"})(qHB||(NHB.CodeChallengeMethod=qHB={}));var dP;(function(A){A.PEM="PEM",A.JWK="JWK"})(dP||(NHB.CertificateFormat=dP={}));var rD1;(function(A){A.ClientSecretPost="ClientSecretPost",A.ClientSecretBasic="ClientSecretBasic",A.None="None"})(rD1||(NHB.ClientAuthentication=rD1={}));class YX extends lo6.AuthClient{constructor(A,B,Q){let D=A&&typeof A==="object"?A:{clientId:A,clientSecret:B,redirectUri:Q};super(D);this.certificateCache={},this.certificateExpiry=null,this.certificateCacheFormat=dP.PEM,this.refreshTokenPromises=new Map,this._clientId=D.clientId,this._clientSecret=D.clientSecret,this.redirectUri=D.redirectUri,this.endpoints={tokenInfoUrl:"https://oauth2.googleapis.com/tokeninfo",oauth2AuthBaseUrl:"https://accounts.google.com/o/oauth2/v2/auth",oauth2TokenUrl:"https://oauth2.googleapis.com/token",oauth2RevokeUrl:"https://oauth2.googleapis.com/revoke",oauth2FederatedSignonPemCertsUrl:"https://www.googleapis.com/oauth2/v1/certs",oauth2FederatedSignonJwkCertsUrl:"https://www.googleapis.com/oauth2/v3/certs",oauth2IapPublicKeyUrl:"https://www.gstatic.com/iap/verify/public_key",...D.endpoints},this.clientAuthentication=D.clientAuthentication||rD1.ClientSecretPost,this.issuers=D.issuers||["accounts.google.com","https://accounts.google.com",this.universeDomain]}generateAuthUrl(A={}){if(A.code_challenge_method&&!A.code_challenge)throw new Error("If a code_challenge_method is provided, code_challenge must be included.");if(A.response_type=A.response_type||"code",A.client_id=A.client_id||this._clientId,A.redirect_uri=A.redirect_uri||this.redirectUri,Array.isArray(A.scope))A.scope=A.scope.join(" ");return this.endpoints.oauth2AuthBaseUrl.toString()+"?"+CE0.stringify(A)}generateCodeVerifier(){throw new Error("generateCodeVerifier is removed, please use generateCodeVerifierAsync instead.")}async generateCodeVerifierAsync(){let A=KE0.createCrypto(),Q=A.randomBytesBase64(96).replace(/\+/g,"~").replace(/=/g,"_").replace(/\//g,"-"),Z=(await A.sha256DigestBase64(Q)).split("=")[0].replace(/\+/g,"-").replace(/\//g,"_");return{codeVerifier:Q,codeChallenge:Z}}getToken(A,B){let Q=typeof A==="string"?{code:A}:A;if(B)this.getTokenAsync(Q).then((D)=>B(null,D.tokens,D.res),(D)=>B(D,null,D.response));else return this.getTokenAsync(Q)}async getTokenAsync(A){let B=this.endpoints.oauth2TokenUrl.toString(),Q={"Content-Type":"application/x-www-form-urlencoded"},D={client_id:A.client_id||this._clientId,code_verifier:A.codeVerifier,code:A.code,grant_type:"authorization_code",redirect_uri:A.redirect_uri||this.redirectUri};if(this.clientAuthentication===rD1.ClientSecretBasic){let F=Buffer.from(`${this._clientId}:${this._clientSecret}`);Q.Authorization=`Basic ${F.toString("base64")}`}if(this.clientAuthentication===rD1.ClientSecretPost)D.client_secret=this._clientSecret;let Z=await this.transporter.request({...YX.RETRY_CONFIG,method:"POST",url:B,data:CE0.stringify(D),headers:Q}),G=Z.data;if(Z.data&&Z.data.expires_in)G.expiry_date=new Date().getTime()+Z.data.expires_in*1000,delete G.expires_in;return this.emit("tokens",G),{tokens:G,res:Z}}async refreshToken(A){if(!A)return this.refreshTokenNoCache(A);if(this.refreshTokenPromises.has(A))return this.refreshTokenPromises.get(A);let B=this.refreshTokenNoCache(A).then((Q)=>{return this.refreshTokenPromises.delete(A),Q},(Q)=>{throw this.refreshTokenPromises.delete(A),Q});return this.refreshTokenPromises.set(A,B),B}async refreshTokenNoCache(A){var B;if(!A)throw new Error("No refresh token is set.");let Q=this.endpoints.oauth2TokenUrl.toString(),D={refresh_token:A,client_id:this._clientId,client_secret:this._clientSecret,grant_type:"refresh_token"},Z;try{Z=await this.transporter.request({...YX.RETRY_CONFIG,method:"POST",url:Q,data:CE0.stringify(D),headers:{"Content-Type":"application/x-www-form-urlencoded"}})}catch(F){if(F instanceof mo6.GaxiosError&&F.message==="invalid_grant"&&((B=F.response)===null||B===void 0?void 0:B.data)&&/ReAuth/i.test(F.response.data.error_description))F.message=JSON.stringify(F.response.data);throw F}let G=Z.data;if(Z.data&&Z.data.expires_in)G.expiry_date=new Date().getTime()+Z.data.expires_in*1000,delete G.expires_in;return this.emit("tokens",G),{tokens:G,res:Z}}refreshAccessToken(A){if(A)this.refreshAccessTokenAsync().then((B)=>A(null,B.credentials,B.res),A);else return this.refreshAccessTokenAsync()}async refreshAccessTokenAsync(){let A=await this.refreshToken(this.credentials.refresh_token),B=A.tokens;return B.refresh_token=this.credentials.refresh_token,this.credentials=B,{credentials:this.credentials,res:A.res}}getAccessToken(A){if(A)this.getAccessTokenAsync().then((B)=>A(null,B.token,B.res),A);else return this.getAccessTokenAsync()}async getAccessTokenAsync(){if(!this.credentials.access_token||this.isTokenExpiring()){if(!this.credentials.refresh_token)if(this.refreshHandler){let Q=await this.processAndValidateRefreshHandler();if(Q===null||Q===void 0?void 0:Q.access_token)return this.setCredentials(Q),{token:this.credentials.access_token}}else throw new Error("No refresh token or refresh handler callback is set.");let B=await this.refreshAccessTokenAsync();if(!B.credentials||B.credentials&&!B.credentials.access_token)throw new Error("Could not refresh access token.");return{token:B.credentials.access_token,res:B.res}}else return{token:this.credentials.access_token}}async getRequestHeaders(A){return(await this.getRequestMetadataAsync(A)).headers}async getRequestMetadataAsync(A){let B=this.credentials;if(!B.access_token&&!B.refresh_token&&!this.apiKey&&!this.refreshHandler)throw new Error("No access, refresh token, API key or refresh handler callback is set.");if(B.access_token&&!this.isTokenExpiring()){B.token_type=B.token_type||"Bearer";let F={Authorization:B.token_type+" "+B.access_token};return{headers:this.addSharedMetadataHeaders(F)}}if(this.refreshHandler){let F=await this.processAndValidateRefreshHandler();if(F===null||F===void 0?void 0:F.access_token){this.setCredentials(F);let I={Authorization:"Bearer "+this.credentials.access_token};return{headers:this.addSharedMetadataHeaders(I)}}}if(this.apiKey)return{headers:{"X-Goog-Api-Key":this.apiKey}};let Q=null,D=null;try{Q=await this.refreshToken(B.refresh_token),D=Q.tokens}catch(F){let I=F;if(I.response&&(I.response.status===403||I.response.status===404))I.message=`Could not refresh access token: ${I.message}`;throw I}let Z=this.credentials;Z.token_type=Z.token_type||"Bearer",D.refresh_token=Z.refresh_token,this.credentials=D;let G={Authorization:Z.token_type+" "+D.access_token};return{headers:this.addSharedMetadataHeaders(G),res:Q.res}}static getRevokeTokenUrl(A){return new YX().getRevokeTokenURL(A).toString()}getRevokeTokenURL(A){let B=new URL(this.endpoints.oauth2RevokeUrl);return B.searchParams.append("token",A),B}revokeToken(A,B){let Q={...YX.RETRY_CONFIG,url:this.getRevokeTokenURL(A).toString(),method:"POST"};if(B)this.transporter.request(Q).then((D)=>B(null,D),B);else return this.transporter.request(Q)}revokeCredentials(A){if(A)this.revokeCredentialsAsync().then((B)=>A(null,B),A);else return this.revokeCredentialsAsync()}async revokeCredentialsAsync(){let A=this.credentials.access_token;if(this.credentials={},A)return this.revokeToken(A);else throw new Error("No access token to revoke.")}request(A,B){if(B)this.requestAsync(A).then((Q)=>B(null,Q),(Q)=>{return B(Q,Q.response)});else return this.requestAsync(A)}async requestAsync(A,B=!1){let Q;try{let D=await this.getRequestMetadataAsync(A.url);if(A.headers=A.headers||{},D.headers&&D.headers["x-goog-user-project"])A.headers["x-goog-user-project"]=D.headers["x-goog-user-project"];if(D.headers&&D.headers.Authorization)A.headers.Authorization=D.headers.Authorization;if(this.apiKey)A.headers["X-Goog-Api-Key"]=this.apiKey;Q=await this.transporter.request(A)}catch(D){let Z=D.response;if(Z){let G=Z.status,F=this.credentials&&this.credentials.access_token&&this.credentials.refresh_token&&(!this.credentials.expiry_date||this.forceRefreshOnFailure),I=this.credentials&&this.credentials.access_token&&!this.credentials.refresh_token&&(!this.credentials.expiry_date||this.forceRefreshOnFailure)&&this.refreshHandler,Y=Z.config.data instanceof do6.Readable,W=G===401||G===403;if(!B&&W&&!Y&&F)return await this.refreshAccessTokenAsync(),this.requestAsync(A,!0);else if(!B&&W&&!Y&&I){let J=await this.processAndValidateRefreshHandler();if(J===null||J===void 0?void 0:J.access_token)this.setCredentials(J);return this.requestAsync(A,!0)}}throw D}return Q}verifyIdToken(A,B){if(B&&typeof B!=="function")throw new Error("This method accepts an options object as the first parameter, which includes the idToken, audience, and maxExpiry.");if(B)this.verifyIdTokenAsync(A).then((Q)=>B(null,Q),B);else return this.verifyIdTokenAsync(A)}async verifyIdTokenAsync(A){if(!A.idToken)throw new Error("The verifyIdToken method requires an ID Token");let B=await this.getFederatedSignonCertsAsync();return await this.verifySignedJwtWithCertsAsync(A.idToken,B.certs,A.audience,this.issuers,A.maxExpiry)}async getTokenInfo(A){let{data:B}=await this.transporter.request({...YX.RETRY_CONFIG,method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",Authorization:`Bearer ${A}`},url:this.endpoints.tokenInfoUrl.toString()}),Q=Object.assign({expiry_date:new Date().getTime()+B.expires_in*1000,scopes:B.scope.split(" ")},B);return delete Q.expires_in,delete Q.scope,Q}getFederatedSignonCerts(A){if(A)this.getFederatedSignonCertsAsync().then((B)=>A(null,B.certs,B.res),A);else return this.getFederatedSignonCertsAsync()}async getFederatedSignonCertsAsync(){let A=new Date().getTime(),B=KE0.hasBrowserCrypto()?dP.JWK:dP.PEM;if(this.certificateExpiry&&A<this.certificateExpiry.getTime()&&this.certificateCacheFormat===B)return{certs:this.certificateCache,format:B};let Q,D;switch(B){case dP.PEM:D=this.endpoints.oauth2FederatedSignonPemCertsUrl.toString();break;case dP.JWK:D=this.endpoints.oauth2FederatedSignonJwkCertsUrl.toString();break;default:throw new Error(`Unsupported certificate format ${B}`)}try{Q=await this.transporter.request({...YX.RETRY_CONFIG,url:D})}catch(Y){if(Y instanceof Error)Y.message=`Failed to retrieve verification certificates: ${Y.message}`;throw Y}let Z=Q?Q.headers["cache-control"]:void 0,G=-1;if(Z){let W=new RegExp("max-age=([0-9]*)").exec(Z);if(W&&W.length===2)G=Number(W[1])*1000}let F={};switch(B){case dP.PEM:F=Q.data;break;case dP.JWK:for(let Y of Q.data.keys)F[Y.kid]=Y;break;default:throw new Error(`Unsupported certificate format ${B}`)}let I=new Date;return this.certificateExpiry=G===-1?null:new Date(I.getTime()+G),this.certificateCache=F,this.certificateCacheFormat=B,{certs:F,format:B,res:Q}}getIapPublicKeys(A){if(A)this.getIapPublicKeysAsync().then((B)=>A(null,B.pubkeys,B.res),A);else return this.getIapPublicKeysAsync()}async getIapPublicKeysAsync(){let A,B=this.endpoints.oauth2IapPublicKeyUrl.toString();try{A=await this.transporter.request({...YX.RETRY_CONFIG,url:B})}catch(Q){if(Q instanceof Error)Q.message=`Failed to retrieve verification certificates: ${Q.message}`;throw Q}return{pubkeys:A.data,res:A}}verifySignedJwtWithCerts(){throw new Error("verifySignedJwtWithCerts is removed, please use verifySignedJwtWithCertsAsync instead.")}async verifySignedJwtWithCertsAsync(A,B,Q,D,Z){let G=KE0.createCrypto();if(!Z)Z=YX.DEFAULT_MAX_TOKEN_LIFETIME_SECS_;let F=A.split(".");if(F.length!==3)throw new Error("Wrong number of segments in token: "+A);let I=F[0]+"."+F[1],Y=F[2],W,J;try{W=JSON.parse(G.decodeBase64StringUtf8(F[0]))}catch(L){if(L instanceof Error)L.message=`Can't parse token envelope: ${F[0]}': ${L.message}`;throw L}if(!W)throw new Error("Can't parse token envelope: "+F[0]);try{J=JSON.parse(G.decodeBase64StringUtf8(F[1]))}catch(L){if(L instanceof Error)L.message=`Can't parse token payload '${F[0]}`;throw L}if(!J)throw new Error("Can't parse token payload: "+F[1]);if(!Object.prototype.hasOwnProperty.call(B,W.kid))throw new Error("No pem found for envelope: "+JSON.stringify(W));let X=B[W.kid];if(W.alg==="ES256")Y=co6.joseToDer(Y,"ES256").toString("base64");if(!await G.verify(X,I,Y))throw new Error("Invalid token signature: "+A);if(!J.iat)throw new Error("No issue time in token: "+JSON.stringify(J));if(!J.exp)throw new Error("No expiration time in token: "+JSON.stringify(J));let C=Number(J.iat);if(isNaN(C))throw new Error("iat field using invalid format");let K=Number(J.exp);if(isNaN(K))throw new Error("exp field using invalid format");let H=new Date().getTime()/1000;if(K>=H+Z)throw new Error("Expiration time too far in future: "+JSON.stringify(J));let z=C-YX.CLOCK_SKEW_SECS_,$=K+YX.CLOCK_SKEW_SECS_;if(H<z)throw new Error("Token used too early, "+H+" < "+z+": "+JSON.stringify(J));if(H>$)throw new Error("Token used too late, "+H+" > "+$+": "+JSON.stringify(J));if(D&&D.indexOf(J.iss)<0)throw new Error("Invalid issuer, expected one of ["+D+"], but got "+J.iss);if(typeof Q!=="undefined"&&Q!==null){let L=J.aud,N=!1;if(Q.constructor===Array)N=Q.indexOf(L)>-1;else N=L===Q;if(!N)throw new Error("Wrong recipient, payload audience != requiredAudience")}return new po6.LoginTicket(W,J)}async processAndValidateRefreshHandler(){if(this.refreshHandler){let A=await this.refreshHandler();if(!A.access_token)throw new Error("No access token is returned by the refreshHandler callback.");return A}return}isTokenExpiring(){let A=this.credentials.expiry_date;return A?A<=new Date().getTime()+this.eagerRefreshThresholdMillis:!1}}NHB.OAuth2Client=YX;YX.GOOGLE_TOKEN_INFO_URL="https://oauth2.googleapis.com/tokeninfo";YX.CLOCK_SKEW_SECS_=300;YX.DEFAULT_MAX_TOKEN_LIFETIME_SECS_=86400});
var $z0=E((Vo5,kVB)=>{function wz0(A){return A>=48&&A<=57}function jVB(A){return A>=65&&A<=90||A>=97&&A<=122}function fn6(A){return jVB(A)||wz0(A)}function hn6(A){return wz0(A)||A>=65&&A<=70||A>=97&&A<=102}kVB.exports={isASCIIDigit:wz0,isASCIIAlpha:jVB,isASCIIAlphanumeric:fn6,isASCIIHex:hn6}});
var AX2=E((wV)=>{var QS4=wV&&wV.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),DS4=wV&&wV.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),tJ2=wV&&wV.__importStar||function(A){if(A&&A.__esModule)return A;var B={};if(A!=null){for(var Q in A)if(Q!=="default"&&Object.prototype.hasOwnProperty.call(A,Q))QS4(B,A,Q)}return DS4(B,A),B};Object.defineProperty(wV,"__esModule",{value:!0});wV.req=wV.json=wV.toBuffer=void 0;var ZS4=tJ2(J1("http")),GS4=tJ2(J1("https"));async function eJ2(A){let B=0,Q=[];for await(let D of A)B+=D.length,Q.push(D);return Buffer.concat(Q,B)}wV.toBuffer=eJ2;async function FS4(A){let Q=(await eJ2(A)).toString("utf8");try{return JSON.parse(Q)}catch(D){let Z=D;throw Z.message+=` (input: ${Q})`,Z}}wV.json=FS4;function IS4(A,B={}){let D=((typeof A==="string"?A:A.href).startsWith("https:")?GS4:ZS4).request(A,B),Z=new Promise((G,F)=>{D.once("response",G).once("error",F).end()});return D.then=Z.then.bind(Z),D}wV.req=IS4});
var Ae1=E((ZqA)=>{Object.defineProperty(ZqA,"__esModule",{value:!0});ZqA.default=wwQ;var EwQ=UwQ(J1("crypto"));function UwQ(A){return A&&A.__esModule?A:{default:A}}var rE1=new Uint8Array(256),sE1=rE1.length;function wwQ(){if(sE1>rE1.length-16)EwQ.default.randomFillSync(rE1),sE1=0;return rE1.slice(sE1,sE1+=16)}});
var CEB=E((XEB)=>{Object.defineProperty(XEB,"__esModule",{value:!0});XEB.PluggableAuthHandler=void 0;var pe6=x_1(),qm=FU0(),ie6=J1("child_process"),IU0=J1("fs");class YU0{constructor(A){if(!A.command)throw new Error("No command provided.");if(this.commandComponents=YU0.parseCommand(A.command),this.timeoutMillis=A.timeoutMillis,!this.timeoutMillis)throw new Error("No timeoutMillis provided.");this.outputFile=A.outputFile}retrieveResponseFromExecutable(A){return new Promise((B,Q)=>{let D=ie6.spawn(this.commandComponents[0],this.commandComponents.slice(1),{env:{...process.env,...Object.fromEntries(A)}}),Z="";D.stdout.on("data",(F)=>{Z+=F}),D.stderr.on("data",(F)=>{Z+=F});let G=setTimeout(()=>{return D.removeAllListeners(),D.kill(),Q(new Error("The executable failed to finish within the timeout specified."))},this.timeoutMillis);D.on("close",(F)=>{if(clearTimeout(G),F===0)try{let I=JSON.parse(Z),Y=new qm.ExecutableResponse(I);return B(Y)}catch(I){if(I instanceof qm.ExecutableResponseError)return Q(I);return Q(new qm.ExecutableResponseError(`The executable returned an invalid response: ${Z}`))}else return Q(new pe6.ExecutableError(Z,F.toString()))})})}async retrieveCachedResponse(){if(!this.outputFile||this.outputFile.length===0)return;let A;try{A=await IU0.promises.realpath(this.outputFile)}catch(Q){return}if(!(await IU0.promises.lstat(A)).isFile())return;let B=await IU0.promises.readFile(A,{encoding:"utf8"});if(B==="")return;try{let Q=JSON.parse(B);if(new qm.ExecutableResponse(Q).isValid())return new qm.ExecutableResponse(Q);return}catch(Q){if(Q instanceof qm.ExecutableResponseError)throw Q;throw new qm.ExecutableResponseError(`The output file contained an invalid response: ${B}`)}}static parseCommand(A){let B=A.match(/(?:[^\s"]+|"[^"]*")+/g);if(!B)throw new Error(`Provided command: "${A}" could not be parsed.`);for(let Q=0;Q<B.length;Q++)if(B[Q][0]==='"'&&B[Q].slice(-1)==='"')B[Q]=B[Q].slice(1,-1);return B}}XEB.PluggableAuthHandler=YU0});
var CKB=E((ko5,VKB)=>{var JKB=az0(),XKB=ko5;(function(){function A(W){return W<10?"0"+W:W}var B=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,Q=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,D,Z,G={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':"\\\"","\\":"\\\\"},F;function I(W){return Q.lastIndex=0,Q.test(W)?'"'+W.replace(Q,function(J){var X=G[J];return typeof X==="string"?X:"\\u"+("0000"+J.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+W+'"'}function Y(W,J){var X,V,C,K,H=D,z,$=J[W],L=$!=null&&($ instanceof JKB||JKB.isBigNumber($));if($&&typeof $==="object"&&typeof $.toJSON==="function")$=$.toJSON(W);if(typeof F==="function")$=F.call(J,W,$);switch(typeof $){case"string":if(L)return $;else return I($);case"number":return isFinite($)?String($):"null";case"boolean":case"null":case"bigint":return String($);case"object":if(!$)return"null";if(D+=Z,z=[],Object.prototype.toString.apply($)==="[object Array]"){K=$.length;for(X=0;X<K;X+=1)z[X]=Y(X,$)||"null";return C=z.length===0?"[]":D?`[
`+D+z.join(`,
`+D)+`
`+H+"]":"["+z.join(",")+"]",D=H,C}if(F&&typeof F==="object"){K=F.length;for(X=0;X<K;X+=1)if(typeof F[X]==="string"){if(V=F[X],C=Y(V,$),C)z.push(I(V)+(D?": ":":")+C)}}else Object.keys($).forEach(function(N){var O=Y(N,$);if(O)z.push(I(N)+(D?": ":":")+O)});return C=z.length===0?"{}":D?`{
`+D+z.join(`,
`+D)+`
`+H+"}":"{"+z.join(",")+"}",D=H,C}}if(typeof XKB.stringify!=="function")XKB.stringify=function(W,J,X){var V;if(D="",Z="",typeof X==="number")for(V=0;V<X;V+=1)Z+=" ";else if(typeof X==="string")Z=X;if(F=J,J&&typeof J!=="function"&&(typeof J!=="object"||typeof J.length!=="number"))throw new Error("JSON.stringify");return Y("",{"":W})}})()});
var Cz0=E((Fo5,tXB)=>{var ny1=Object.prototype.hasOwnProperty,oXB=Object.prototype.toString,pXB=Object.defineProperty,iXB=Object.getOwnPropertyDescriptor,nXB=function A(B){if(typeof Array.isArray==="function")return Array.isArray(B);return oXB.call(B)==="[object Array]"},aXB=function A(B){if(!B||oXB.call(B)!=="[object Object]")return!1;var Q=ny1.call(B,"constructor"),D=B.constructor&&B.constructor.prototype&&ny1.call(B.constructor.prototype,"isPrototypeOf");if(B.constructor&&!Q&&!D)return!1;var Z;for(Z in B);return typeof Z==="undefined"||ny1.call(B,Z)},sXB=function A(B,Q){if(pXB&&Q.name==="__proto__")pXB(B,Q.name,{enumerable:!0,configurable:!0,value:Q.newValue,writable:!0});else B[Q.name]=Q.newValue},rXB=function A(B,Q){if(Q==="__proto__"){if(!ny1.call(B,Q))return;else if(iXB)return iXB(B,Q).value}return B[Q]};tXB.exports=function A(){var B,Q,D,Z,G,F,I=arguments[0],Y=1,W=arguments.length,J=!1;if(typeof I==="boolean")J=I,I=arguments[1]||{},Y=2;if(I==null||typeof I!=="object"&&typeof I!=="function")I={};for(;Y<W;++Y)if(B=arguments[Y],B!=null){for(Q in B)if(D=rXB(I,Q),Z=rXB(B,Q),I!==Z){if(J&&Z&&(aXB(Z)||(G=nXB(Z)))){if(G)G=!1,F=D&&nXB(D)?D:[];else F=D&&aXB(D)?D:{};sXB(I,{name:Q,newValue:A(J,F,Z)})}else if(typeof Z!=="undefined")sXB(I,{name:Q,newValue:Z})}}return I}});
var DHB=E((io5,QHB)=>{function IE0(A){var B=(A/8|0)+(A%8===0?0:1);return B}var jo6={ES256:IE0(256),ES384:IE0(384),ES512:IE0(521)};function ko6(A){var B=jo6[A];if(B)return B;throw new Error('Unknown algorithm "'+A+'"')}QHB.exports=ko6});
var EE0=E((yHB)=>{Object.defineProperty(yHB,"__esModule",{value:!0});yHB.GCPEnv=void 0;yHB.clear=to6;yHB.getEnv=eo6;var kHB=nD1(),cP;(function(A){A.APP_ENGINE="APP_ENGINE",A.KUBERNETES_ENGINE="KUBERNETES_ENGINE",A.CLOUD_FUNCTIONS="CLOUD_FUNCTIONS",A.COMPUTE_ENGINE="COMPUTE_ENGINE",A.CLOUD_RUN="CLOUD_RUN",A.NONE="NONE"})(cP||(yHB.GCPEnv=cP={}));var oD1;function to6(){oD1=void 0}async function eo6(){if(oD1)return oD1;return oD1=At6(),oD1}async function At6(){let A=cP.NONE;if(Bt6())A=cP.APP_ENGINE;else if(Qt6())A=cP.CLOUD_FUNCTIONS;else if(await Gt6())if(await Zt6())A=cP.KUBERNETES_ENGINE;else if(Dt6())A=cP.CLOUD_RUN;else A=cP.COMPUTE_ENGINE;else A=cP.NONE;return A}function Bt6(){return!!(process.env.GAE_SERVICE||process.env.GAE_MODULE_NAME)}function Qt6(){return!!(process.env.FUNCTION_NAME||process.env.FUNCTION_TARGET)}function Dt6(){return!!process.env.K_CONFIGURATION}async function Zt6(){try{return await kHB.instance("attributes/cluster-name"),!0}catch(A){return!1}}async function Gt6(){return kHB.isAvailable()}});
var FU0=E((WEB)=>{Object.defineProperty(WEB,"__esModule",{value:!0});WEB.InvalidSubjectTokenError=WEB.InvalidMessageFieldError=WEB.InvalidCodeFieldError=WEB.InvalidTokenTypeFieldError=WEB.InvalidExpirationTimeFieldError=WEB.InvalidSuccessFieldError=WEB.InvalidVersionFieldError=WEB.ExecutableResponseError=WEB.ExecutableResponse=void 0;var y_1="urn:ietf:params:oauth:token-type:saml2",eE0="urn:ietf:params:oauth:token-type:id_token",AU0="urn:ietf:params:oauth:token-type:jwt";class IEB{constructor(A){if(!A.version)throw new BU0("Executable response must contain a 'version' field.");if(A.success===void 0)throw new QU0("Executable response must contain a 'success' field.");if(this.version=A.version,this.success=A.success,this.success){if(this.expirationTime=A.expiration_time,this.tokenType=A.token_type,this.tokenType!==y_1&&this.tokenType!==eE0&&this.tokenType!==AU0)throw new DU0(`Executable response must contain a 'token_type' field when successful and it must be one of ${eE0}, ${AU0}, or ${y_1}.`);if(this.tokenType===y_1){if(!A.saml_response)throw new __1(`Executable response must contain a 'saml_response' field when token_type=${y_1}.`);this.subjectToken=A.saml_response}else{if(!A.id_token)throw new __1(`Executable response must contain a 'id_token' field when token_type=${eE0} or ${AU0}.`);this.subjectToken=A.id_token}}else{if(!A.code)throw new ZU0("Executable response must contain a 'code' field when unsuccessful.");if(!A.message)throw new GU0("Executable response must contain a 'message' field when unsuccessful.");this.errorCode=A.code,this.errorMessage=A.message}}isValid(){return!this.isExpired()&&this.success}isExpired(){return this.expirationTime!==void 0&&this.expirationTime<Math.round(Date.now()/1000)}}WEB.ExecutableResponse=IEB;class pP extends Error{constructor(A){super(A);Object.setPrototypeOf(this,new.target.prototype)}}WEB.ExecutableResponseError=pP;class BU0 extends pP{}WEB.InvalidVersionFieldError=BU0;class QU0 extends pP{}WEB.InvalidSuccessFieldError=QU0;class YEB extends pP{}WEB.InvalidExpirationTimeFieldError=YEB;class DU0 extends pP{}WEB.InvalidTokenTypeFieldError=DU0;class ZU0 extends pP{}WEB.InvalidCodeFieldError=ZU0;class GU0 extends pP{}WEB.InvalidMessageFieldError=GU0;class __1 extends pP{}WEB.InvalidSubjectTokenError=__1});
var FX2=E((Jr)=>{var VS4=Jr&&Jr.__importDefault||function(A){return A&&A.__esModule?A:{default:A}};Object.defineProperty(Jr,"__esModule",{value:!0});Jr.parseProxyResponse=void 0;var CS4=VS4(GB1()),nL1=CS4.default("https-proxy-agent:parse-proxy-response");function KS4(A){return new Promise((B,Q)=>{let D=0,Z=[];function G(){let J=A.read();if(J)W(J);else A.once("readable",G)}function F(){A.removeListener("end",I),A.removeListener("error",Y),A.removeListener("readable",G)}function I(){F(),nL1("onend"),Q(new Error("Proxy connection ended before receiving CONNECT response"))}function Y(J){F(),nL1("onerror %o",J),Q(J)}function W(J){Z.push(J),D+=J.length;let X=Buffer.concat(Z,D),V=X.indexOf(`\r
\r
`);if(V===-1){nL1("have not received end of HTTP headers yet..."),G();return}let C=X.slice(0,V).toString("ascii").split(`\r
`),K=C.shift();if(!K)return A.destroy(),Q(new Error("No header received from proxy CONNECT response"));let H=K.split(" "),z=+H[1],$=H.slice(2).join(" "),L={};for(let N of C){if(!N)continue;let O=N.indexOf(":");if(O===-1)return A.destroy(),Q(new Error(`Invalid header from proxy CONNECT response: "${N}"`));let R=N.slice(0,O).toLowerCase(),T=N.slice(O+1).trimStart(),j=L[R];if(typeof j==="string")L[R]=[j,T];else if(Array.isArray(j))j.push(T);else L[R]=T}nL1("got proxy server response: %o %o",K,L),F(),B({connect:{statusCode:z,statusText:$,headers:L},buffered:X})}A.on("error",Y),A.on("end",I),G()})}Jr.parseProxyResponse=KS4});
var Fp0=E((Zp0,RX1)=>{var LM9=J1("tty"),MX1=J1("util");Zp0.init=jM9;Zp0.log=TM9;Zp0.formatArgs=RM9;Zp0.save=PM9;Zp0.load=SM9;Zp0.useColors=MM9;Zp0.destroy=MX1.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");Zp0.colors=[6,2,3,4,5,1];try{let A=Qp0();if(A&&(A.stderr||A).level>=2)Zp0.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221]}catch(A){}Zp0.inspectOpts=Object.keys(process.env).filter((A)=>{return/^debug_/i.test(A)}).reduce((A,B)=>{let Q=B.substring(6).toLowerCase().replace(/_([a-z])/g,(Z,G)=>{return G.toUpperCase()}),D=process.env[B];if(/^(yes|on|true|enabled)$/i.test(D))D=!0;else if(/^(no|off|false|disabled)$/i.test(D))D=!1;else if(D==="null")D=null;else D=Number(D);return A[Q]=D,A},{});function MM9(){return"colors"in Zp0.inspectOpts?Boolean(Zp0.inspectOpts.colors):LM9.isatty(process.stderr.fd)}function RM9(A){let{namespace:B,useColors:Q}=this;if(Q){let D=this.color,Z="\x1B[3"+(D<8?D:"8;5;"+D),G=`  ${Z};1m${B} \x1B[0m`;A[0]=G+A[0].split(`
`).join(`
`+G),A.push(Z+"m+"+RX1.exports.humanize(this.diff)+"\x1B[0m")}else A[0]=OM9()+B+" "+A[0]}function OM9(){if(Zp0.inspectOpts.hideDate)return"";return new Date().toISOString()+" "}function TM9(...A){return process.stderr.write(MX1.formatWithOptions(Zp0.inspectOpts,...A)+`
`)}function PM9(A){if(A)process.env.DEBUG=A;else delete process.env.DEBUG}function SM9(){return process.env.DEBUG}function jM9(A){A.inspectOpts={};let B=Object.keys(Zp0.inspectOpts);for(let Q=0;Q<B.length;Q++)A.inspectOpts[B[Q]]=Zp0.inspectOpts[B[Q]]}RX1.exports=mc1()(Zp0);var{formatters:Dp0}=RX1.exports;Dp0.o=function(A){return this.inspectOpts.colors=this.useColors,MX1.inspect(A,this.inspectOpts).split(`
`).map((B)=>B.trim()).join(" ")};Dp0.O=function(A){return this.inspectOpts.colors=this.useColors,MX1.inspect(A,this.inspectOpts)}});
var GB1=E((fh8,cc1)=>{if(typeof process==="undefined"||process.type==="renderer"||!1||process.__nwjs)cc1.exports=tl0();else cc1.exports=Fp0()});
var GE0=E((lo5,Oo6)=>{Oo6.exports={name:"google-auth-library",version:"9.15.1",author:"Google Inc.",description:"Google APIs Authentication Client Library for Node.js",engines:{node:">=14"},main:"./build/src/index.js",types:"./build/src/index.d.ts",repository:"googleapis/google-auth-library-nodejs.git",keywords:["google","api","google apis","client","client library"],dependencies:{"base64-js":"^1.3.0","ecdsa-sig-formatter":"^1.0.11",gaxios:"^6.1.1","gcp-metadata":"^6.1.0",gtoken:"^7.0.0",jws:"^4.0.0"},devDependencies:{"@types/base64-js":"^1.2.5","@types/chai":"^4.1.7","@types/jws":"^3.1.0","@types/mocha":"^9.0.0","@types/mv":"^2.1.0","@types/ncp":"^2.0.1","@types/node":"^20.4.2","@types/sinon":"^17.0.0","assert-rejects":"^1.0.0",c8:"^8.0.0",chai:"^4.2.0",cheerio:"1.0.0-rc.12",codecov:"^3.0.2","engine.io":"6.6.2",gts:"^5.0.0","is-docker":"^2.0.0",jsdoc:"^4.0.0","jsdoc-fresh":"^3.0.0","jsdoc-region-tag":"^3.0.0",karma:"^6.0.0","karma-chrome-launcher":"^3.0.0","karma-coverage":"^2.0.0","karma-firefox-launcher":"^2.0.0","karma-mocha":"^2.0.0","karma-sourcemap-loader":"^0.4.0","karma-webpack":"5.0.0",keypair:"^1.0.4",linkinator:"^4.0.0",mocha:"^9.2.2",mv:"^2.1.1",ncp:"^2.0.0",nock:"^13.0.0","null-loader":"^4.0.0",pdfmake:"0.2.12",puppeteer:"^21.0.0",sinon:"^18.0.0","ts-loader":"^8.0.0",typescript:"^5.1.6",webpack:"^5.21.2","webpack-cli":"^4.0.0"},files:["build/src","!build/src/**/*.map"],scripts:{test:"c8 mocha build/test",clean:"gts clean",prepare:"npm run compile",lint:"gts check",compile:"tsc -p .",fix:"gts fix",pretest:"npm run compile -- --sourceMap",docs:"jsdoc -c .jsdoc.json","samples-setup":"cd samples/ && npm link ../ && npm run setup && cd ../","samples-test":"cd samples/ && npm link ../ && npm test && cd ../","system-test":"mocha build/system-test --timeout 60000","presystem-test":"npm run compile -- --sourceMap",webpack:"webpack","browser-test":"karma start","docs-test":"linkinator docs","predocs-test":"npm run docs",prelint:"cd samples; npm link ../; npm install",precompile:"gts clean"},license:"Apache-2.0"}});
var GX2=E((KK)=>{var QX2=KK&&KK.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),YS4=KK&&KK.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),DX2=KK&&KK.__importStar||function(A){if(A&&A.__esModule)return A;var B={};if(A!=null){for(var Q in A)if(Q!=="default"&&Object.prototype.hasOwnProperty.call(A,Q))QX2(B,A,Q)}return YS4(B,A),B},WS4=KK&&KK.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))QX2(B,A,Q)};Object.defineProperty(KK,"__esModule",{value:!0});KK.Agent=void 0;var JS4=DX2(J1("net")),BX2=DX2(J1("http")),XS4=J1("https");WS4(AX2(),KK);var ML=Symbol("AgentBaseInternalState");class ZX2 extends BX2.Agent{constructor(A){super(A);this[ML]={}}isSecureEndpoint(A){if(A){if(typeof A.secureEndpoint==="boolean")return A.secureEndpoint;if(typeof A.protocol==="string")return A.protocol==="https:"}let{stack:B}=new Error;if(typeof B!=="string")return!1;return B.split(`
`).some((Q)=>Q.indexOf("(https.js:")!==-1||Q.indexOf("node:https:")!==-1)}incrementSockets(A){if(this.maxSockets===1/0&&this.maxTotalSockets===1/0)return null;if(!this.sockets[A])this.sockets[A]=[];let B=new JS4.Socket({writable:!1});return this.sockets[A].push(B),this.totalSocketCount++,B}decrementSockets(A,B){if(!this.sockets[A]||B===null)return;let Q=this.sockets[A],D=Q.indexOf(B);if(D!==-1){if(Q.splice(D,1),this.totalSocketCount--,Q.length===0)delete this.sockets[A]}}getName(A){if(typeof A.secureEndpoint==="boolean"?A.secureEndpoint:this.isSecureEndpoint(A))return XS4.Agent.prototype.getName.call(this,A);return super.getName(A)}createSocket(A,B,Q){let D={...B,secureEndpoint:this.isSecureEndpoint(B)},Z=this.getName(D),G=this.incrementSockets(Z);Promise.resolve().then(()=>this.connect(A,D)).then((F)=>{if(this.decrementSockets(Z,G),F instanceof BX2.Agent)try{return F.addRequest(A,D)}catch(I){return Q(I)}this[ML].currentSocket=F,super.createSocket(A,B,Q)},(F)=>{this.decrementSockets(Z,G),Q(F)})}createConnection(){let A=this[ML].currentSocket;if(this[ML].currentSocket=void 0,!A)throw new Error("No socket was returned in the `connect()` function");return A}get defaultPort(){return this[ML].defaultPort??(this.protocol==="https:"?443:80)}set defaultPort(A){if(this[ML])this[ML].defaultPort=A}get protocol(){return this[ML].protocol??(this.isSecureEndpoint()?"https:":"http:")}set protocol(A){if(this[ML])this[ML].protocol=A}}KK.Agent=ZX2});
var Ge1=E((qqA)=>{Object.defineProperty(qqA,"__esModule",{value:!0});qqA.URL=qqA.DNS=void 0;qqA.default=lwQ;var uwQ=aQ1(),mwQ=dwQ(Ze1());function dwQ(A){return A&&A.__esModule?A:{default:A}}function cwQ(A){A=unescape(encodeURIComponent(A));let B=[];for(let Q=0;Q<A.length;++Q)B.push(A.charCodeAt(Q));return B}var wqA="6ba7b810-9dad-11d1-80b4-00c04fd430c8";qqA.DNS=wqA;var $qA="6ba7b811-9dad-11d1-80b4-00c04fd430c8";qqA.URL=$qA;function lwQ(A,B,Q){function D(Z,G,F,I){var Y;if(typeof Z==="string")Z=cwQ(Z);if(typeof G==="string")G=mwQ.default(G);if(((Y=G)===null||Y===void 0?void 0:Y.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let W=new Uint8Array(16+Z.length);if(W.set(G),W.set(Z,G.length),W=Q(W),W[6]=W[6]&15|B,W[8]=W[8]&63|128,F){I=I||0;for(let J=0;J<16;++J)F[I+J]=W[J];return F}return uwQ.unsafeStringify(W)}try{D.name=A}catch(Z){}return D.DNS=wqA,D.URL=$qA,D}});
var HE0=E((OHB)=>{Object.defineProperty(OHB,"__esModule",{value:!0});OHB.Compute=void 0;var so6=X$(),MHB=nD1(),ro6=$m();class RHB extends ro6.OAuth2Client{constructor(A={}){super(A);this.credentials={expiry_date:1,refresh_token:"compute-placeholder"},this.serviceAccountEmail=A.serviceAccountEmail||"default",this.scopes=Array.isArray(A.scopes)?A.scopes:A.scopes?[A.scopes]:[]}async refreshTokenNoCache(A){let B=`service-accounts/${this.serviceAccountEmail}/token`,Q;try{let Z={property:B};if(this.scopes.length>0)Z.params={scopes:this.scopes.join(",")};Q=await MHB.instance(Z)}catch(Z){if(Z instanceof so6.GaxiosError)Z.message=`Could not refresh access token: ${Z.message}`,this.wrapError(Z);throw Z}let D=Q;if(Q&&Q.expires_in)D.expiry_date=new Date().getTime()+Q.expires_in*1000,delete D.expires_in;return this.emit("tokens",D),{tokens:D,res:null}}async fetchIdToken(A){let B=`service-accounts/${this.serviceAccountEmail}/identity?format=full&audience=${A}`,Q;try{let D={property:B};Q=await MHB.instance(D)}catch(D){if(D instanceof Error)D.message=`Could not fetch ID token: ${D.message}`;throw D}return Q}wrapError(A){let B=A.response;if(B&&B.status){if(A.status=B.status,B.status===403)A.message="A Forbidden error was returned while attempting to retrieve an access token for the Compute Engine built-in service account. This may be because the Compute Engine instance does not have the correct permission scopes specified: "+A.message;else if(B.status===404)A.message="A Not Found error was returned while attempting to retrieve an accesstoken for the Compute Engine built-in service account. This may be because the Compute Engine instance does not have any permission scopes specified: "+A.message}}}OHB.Compute=RHB});
var HKB=E((yo5,KKB)=>{var V_1=null,Rr6=/(?:_|\\u005[Ff])(?:_|\\u005[Ff])(?:p|\\u0070)(?:r|\\u0072)(?:o|\\u006[Ff])(?:t|\\u0074)(?:o|\\u006[Ff])(?:_|\\u005[Ff])(?:_|\\u005[Ff])/,Or6=/(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)/,Tr6=function(A){var B={strict:!1,storeAsString:!1,alwaysParseAsBig:!1,useNativeBigInt:!1,protoAction:"error",constructorAction:"error"};if(A!==void 0&&A!==null){if(A.strict===!0)B.strict=!0;if(A.storeAsString===!0)B.storeAsString=!0;if(B.alwaysParseAsBig=A.alwaysParseAsBig===!0?A.alwaysParseAsBig:!1,B.useNativeBigInt=A.useNativeBigInt===!0?A.useNativeBigInt:!1,typeof A.constructorAction!=="undefined")if(A.constructorAction==="error"||A.constructorAction==="ignore"||A.constructorAction==="preserve")B.constructorAction=A.constructorAction;else throw new Error(`Incorrect value for constructorAction option, must be "error", "ignore" or undefined but passed ${A.constructorAction}`);if(typeof A.protoAction!=="undefined")if(A.protoAction==="error"||A.protoAction==="ignore"||A.protoAction==="preserve")B.protoAction=A.protoAction;else throw new Error(`Incorrect value for protoAction option, must be "error", "ignore" or undefined but passed ${A.protoAction}`)}var Q,D,Z={'"':'"',"\\":"\\","/":"/",b:"\b",f:"\f",n:`
`,r:"\r",t:"\t"},G,F=function(H){throw{name:"SyntaxError",message:H,at:Q,text:G}},I=function(H){if(H&&H!==D)F("Expected '"+H+"' instead of '"+D+"'");return D=G.charAt(Q),Q+=1,D},Y=function(){var H,z="";if(D==="-")z="-",I("-");while(D>="0"&&D<="9")z+=D,I();if(D==="."){z+=".";while(I()&&D>="0"&&D<="9")z+=D}if(D==="e"||D==="E"){if(z+=D,I(),D==="-"||D==="+")z+=D,I();while(D>="0"&&D<="9")z+=D,I()}if(H=+z,!isFinite(H))F("Bad number");else{if(V_1==null)V_1=az0();if(z.length>15)return B.storeAsString?z:B.useNativeBigInt?BigInt(z):new V_1(z);else return!B.alwaysParseAsBig?H:B.useNativeBigInt?BigInt(H):new V_1(H)}},W=function(){var H,z,$="",L;if(D==='"'){var N=Q;while(I()){if(D==='"'){if(Q-1>N)$+=G.substring(N,Q-1);return I(),$}if(D==="\\"){if(Q-1>N)$+=G.substring(N,Q-1);if(I(),D==="u"){L=0;for(z=0;z<4;z+=1){if(H=parseInt(I(),16),!isFinite(H))break;L=L*16+H}$+=String.fromCharCode(L)}else if(typeof Z[D]==="string")$+=Z[D];else break;N=Q}}}F("Bad string")},J=function(){while(D&&D<=" ")I()},X=function(){switch(D){case"t":return I("t"),I("r"),I("u"),I("e"),!0;case"f":return I("f"),I("a"),I("l"),I("s"),I("e"),!1;case"n":return I("n"),I("u"),I("l"),I("l"),null}F("Unexpected '"+D+"'")},V,C=function(){var H=[];if(D==="["){if(I("["),J(),D==="]")return I("]"),H;while(D){if(H.push(V()),J(),D==="]")return I("]"),H;I(","),J()}}F("Bad array")},K=function(){var H,z=Object.create(null);if(D==="{"){if(I("{"),J(),D==="}")return I("}"),z;while(D){if(H=W(),J(),I(":"),B.strict===!0&&Object.hasOwnProperty.call(z,H))F('Duplicate key "'+H+'"');if(Rr6.test(H)===!0)if(B.protoAction==="error")F("Object contains forbidden prototype property");else if(B.protoAction==="ignore")V();else z[H]=V();else if(Or6.test(H)===!0)if(B.constructorAction==="error")F("Object contains forbidden constructor property");else if(B.constructorAction==="ignore")V();else z[H]=V();else z[H]=V();if(J(),D==="}")return I("}"),z;I(","),J()}}F("Bad object")};return V=function(){switch(J(),D){case"{":return K();case"[":return C();case'"':return W();case"-":return Y();default:return D>="0"&&D<="9"?Y():X()}},function(H,z){var $;if(G=H+"",Q=0,D=" ",$=V(),J(),D)F("Syntax error");return typeof z==="function"?function L(N,O){var R,T,j=N[O];if(j&&typeof j==="object")Object.keys(j).forEach(function(f){if(T=L(j,f),T!==void 0)j[f]=T;else delete j[f]});return z.call(N,O,j)}({"":$},""):$}};KKB.exports=Tr6});
var IKB=E((ZJ)=>{var Fr6=ZJ&&ZJ.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),Ir6=ZJ&&ZJ.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),Yr6=ZJ&&ZJ.__importStar||function(A){if(A&&A.__esModule)return A;var B={};if(A!=null){for(var Q in A)if(Q!=="default"&&Object.prototype.hasOwnProperty.call(A,Q))Fr6(B,A,Q)}return Ir6(B,A),B},Em=ZJ&&ZJ.__classPrivateFieldGet||function(A,B,Q,D){if(Q==="a"&&!D)throw new TypeError("Private accessor was defined without a getter");if(typeof B==="function"?A!==B||!D:!B.has(A))throw new TypeError("Cannot read private member from an object whose class did not declare it");return Q==="m"?D:Q==="a"?D.call(A):D?D.value:B.get(A)},Wr6=ZJ&&ZJ.__classPrivateFieldSet||function(A,B,Q,D,Z){if(D==="m")throw new TypeError("Private method is not writable");if(D==="a"&&!Z)throw new TypeError("Private accessor was defined without a setter");if(typeof B==="function"?A!==B||!Z:!B.has(A))throw new TypeError("Cannot write private member to an object whose class did not declare it");return D==="a"?Z.call(A,Q):Z?Z.value=Q:B.set(A,Q),Q},J_1=ZJ&&ZJ.__importDefault||function(A){return A&&A.__esModule?A:{default:A}},be,zm,oCB,DKB,ZKB,GKB,Y_1,tCB;Object.defineProperty(ZJ,"__esModule",{value:!0});ZJ.Gaxios=void 0;var Jr6=J_1(Cz0()),Xr6=J1("https"),Vr6=J_1(vCB()),Cr6=J_1(J1("querystring")),Kr6=J_1(fCB()),eCB=J1("url"),W_1=pz0(),Hr6=nCB(),AKB=J1("stream"),zr6=sQ1(),BKB=iz0(),Er6=wr6()?window.fetch:Vr6.default;function Ur6(){return typeof window!=="undefined"&&!!window}function wr6(){return Ur6()&&!!window.fetch}function $r6(){return typeof Buffer!=="undefined"}function QKB(A,B){return!!FKB(A,B)}function FKB(A,B){B=B.toLowerCase();for(let Q of Object.keys((A===null||A===void 0?void 0:A.headers)||{}))if(B===Q.toLowerCase())return A.headers[Q];return}class nz0{constructor(A){be.add(this),this.agentCache=new Map,this.defaults=A||{},this.interceptors={request:new BKB.GaxiosInterceptorManager,response:new BKB.GaxiosInterceptorManager}}async request(A={}){return A=await Em(this,be,"m",GKB).call(this,A),A=await Em(this,be,"m",DKB).call(this,A),Em(this,be,"m",ZKB).call(this,this._request(A))}async _defaultAdapter(A){let Q=await(A.fetchImplementation||Er6)(A.url,A),D=await this.getResponseData(A,Q);return this.translateResponse(A,Q,D)}async _request(A={}){var B;try{let Q;if(A.adapter)Q=await A.adapter(A,this._defaultAdapter.bind(this));else Q=await this._defaultAdapter(A);if(!A.validateStatus(Q.status)){if(A.responseType==="stream"){let D="";await new Promise((Z)=>{(Q===null||Q===void 0?void 0:Q.data).on("data",(G)=>{D+=G}),(Q===null||Q===void 0?void 0:Q.data).on("end",Z)}),Q.data=D}throw new W_1.GaxiosError(`Request failed with status code ${Q.status}`,A,Q)}return Q}catch(Q){let D=Q instanceof W_1.GaxiosError?Q:new W_1.GaxiosError(Q.message,A,void 0,Q),{shouldRetry:Z,config:G}=await Hr6.getRetryConfig(D);if(Z&&G)return D.config.retryConfig.currentRetryAttempt=G.retryConfig.currentRetryAttempt,A.retryConfig=(B=D.config)===null||B===void 0?void 0:B.retryConfig,this._request(A);throw D}}async getResponseData(A,B){switch(A.responseType){case"stream":return B.body;case"json":{let Q=await B.text();try{Q=JSON.parse(Q)}catch(D){}return Q}case"arraybuffer":return B.arrayBuffer();case"blob":return B.blob();case"text":return B.text();default:return this.getResponseDataFromContentType(B)}}validateStatus(A){return A>=200&&A<300}paramsSerializer(A){return Cr6.default.stringify(A)}translateResponse(A,B,Q){let D={};return B.headers.forEach((Z,G)=>{D[G]=Z}),{config:A,data:Q,headers:D,status:B.status,statusText:B.statusText,request:{responseURL:B.url}}}async getResponseDataFromContentType(A){let B=A.headers.get("Content-Type");if(B===null)return A.text();if(B=B.toLowerCase(),B.includes("application/json")){let Q=await A.text();try{Q=JSON.parse(Q)}catch(D){}return Q}else if(B.match(/^text\//))return A.text();else return A.blob()}async*getMultipartRequest(A,B){let Q=`--${B}--`;for(let D of A){let Z=D.headers["Content-Type"]||"application/octet-stream";if(yield`--${B}\r
Content-Type: ${Z}\r
\r
`,typeof D.content==="string")yield D.content;else yield*D.content;yield`\r
`}yield Q}}ZJ.Gaxios=nz0;zm=nz0,be=new WeakSet,oCB=function A(B,Q=[]){var D,Z;let G=new eCB.URL(B),F=[...Q],I=((Z=(D=process.env.NO_PROXY)!==null&&D!==void 0?D:process.env.no_proxy)===null||Z===void 0?void 0:Z.split(","))||[];for(let Y of I)F.push(Y.trim());for(let Y of F)if(Y instanceof RegExp){if(Y.test(G.toString()))return!1}else if(Y instanceof eCB.URL){if(Y.origin===G.origin)return!1}else if(Y.startsWith("*.")||Y.startsWith(".")){let W=Y.replace(/^\*\./,".");if(G.hostname.endsWith(W))return!1}else if(Y===G.origin||Y===G.hostname||Y===G.href)return!1;return!0},DKB=async function A(B){let Q=Promise.resolve(B);for(let D of this.interceptors.request.values())if(D)Q=Q.then(D.resolved,D.rejected);return Q},ZKB=async function A(B){let Q=Promise.resolve(B);for(let D of this.interceptors.response.values())if(D)Q=Q.then(D.resolved,D.rejected);return Q},GKB=async function A(B){var Q,D,Z,G;let F=Jr6.default(!0,{},this.defaults,B);if(!F.url)throw new Error("URL is required.");let I=F.baseUrl||F.baseURL;if(I)F.url=I.toString()+F.url;if(F.paramsSerializer=F.paramsSerializer||this.paramsSerializer,F.params&&Object.keys(F.params).length>0){let J=F.paramsSerializer(F.params);if(J.startsWith("?"))J=J.slice(1);let X=F.url.toString().includes("?")?"&":"?";F.url=F.url+X+J}if(typeof B.maxContentLength==="number")F.size=B.maxContentLength;if(typeof B.maxRedirects==="number")F.follow=B.maxRedirects;if(F.headers=F.headers||{},F.multipart===void 0&&F.data){let J=typeof FormData==="undefined"?!1:(F===null||F===void 0?void 0:F.data)instanceof FormData;if(Kr6.default.readable(F.data))F.body=F.data;else if($r6()&&Buffer.isBuffer(F.data)){if(F.body=F.data,!QKB(F,"Content-Type"))F.headers["Content-Type"]="application/json"}else if(typeof F.data==="object"){if(!J)if(FKB(F,"content-type")==="application/x-www-form-urlencoded")F.body=F.paramsSerializer(F.data);else{if(!QKB(F,"Content-Type"))F.headers["Content-Type"]="application/json";F.body=JSON.stringify(F.data)}}else F.body=F.data}else if(F.multipart&&F.multipart.length>0){let J=zr6.v4();F.headers["Content-Type"]=`multipart/related; boundary=${J}`;let X=new AKB.PassThrough;F.body=X,AKB.pipeline(this.getMultipartRequest(F.multipart,J),X,()=>{})}if(F.validateStatus=F.validateStatus||this.validateStatus,F.responseType=F.responseType||"unknown",!F.headers.Accept&&F.responseType==="json")F.headers.Accept="application/json";F.method=F.method||"GET";let Y=F.proxy||((Q=process===null||process===void 0?void 0:process.env)===null||Q===void 0?void 0:Q.HTTPS_PROXY)||((D=process===null||process===void 0?void 0:process.env)===null||D===void 0?void 0:D.https_proxy)||((Z=process===null||process===void 0?void 0:process.env)===null||Z===void 0?void 0:Z.HTTP_PROXY)||((G=process===null||process===void 0?void 0:process.env)===null||G===void 0?void 0:G.http_proxy),W=Em(this,be,"m",oCB).call(this,F.url,F.noProxy);if(F.agent);else if(Y&&W){let J=await Em(zm,zm,"m",tCB).call(zm);if(this.agentCache.has(Y))F.agent=this.agentCache.get(Y);else F.agent=new J(Y,{cert:F.cert,key:F.key}),this.agentCache.set(Y,F.agent)}else if(F.cert&&F.key)if(this.agentCache.has(F.key))F.agent=this.agentCache.get(F.key);else F.agent=new Xr6.Agent({cert:F.cert,key:F.key}),this.agentCache.set(F.key,F.agent);if(typeof F.errorRedactor!=="function"&&F.errorRedactor!==!1)F.errorRedactor=W_1.defaultErrorRedactor;return F},tCB=async function A(){return Wr6(this,zm,Em(this,zm,"f",Y_1)||(await Promise.resolve().then(()=>Yr6(Q70()))).HttpsProxyAgent,"f",Y_1),Em(this,zm,"f",Y_1)};Y_1={value:void 0}});
var IqA=E((GqA)=>{Object.defineProperty(GqA,"__esModule",{value:!0});GqA.default=void 0;var qwQ=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|********-0000-0000-0000-********0000)$/i;GqA.default=qwQ});
var JU0=E((qEB)=>{Object.defineProperty(qEB,"__esModule",{value:!0});qEB.ExternalAccountClient=void 0;var ee6=ix(),A18=aE0(),B18=tE0(),Q18=x_1();class $EB{constructor(){throw new Error("ExternalAccountClients should be initialized via: ExternalAccountClient.fromJSON(), directly via explicit constructors, eg. new AwsClient(options), new IdentityPoolClient(options), newPluggableAuthClientOptions, or via new GoogleAuth(options).getClient()")}static fromJSON(A,B){var Q,D;if(A&&A.type===ee6.EXTERNAL_ACCOUNT_TYPE)if((Q=A.credential_source)===null||Q===void 0?void 0:Q.environment_id)return new B18.AwsClient(A,B);else if((D=A.credential_source)===null||D===void 0?void 0:D.executable)return new Q18.PluggableAuthClient(A,B);else return new A18.IdentityPoolClient(A,B);else return null}}qEB.ExternalAccountClient=$EB});
var LCB=E(($s6)=>{var{URL:Us6,URLSearchParams:ws6}=qCB(),Y$=Pz0(),NCB=ey1(),G_1={Array,Object,Promise,String,TypeError};Us6.install(G_1,["Window"]);ws6.install(G_1,["Window"]);$s6.URL=G_1.URL;$s6.URLSearchParams=G_1.URLSearchParams;$s6.parseURL=Y$.parseURL;$s6.basicURLParse=Y$.basicURLParse;$s6.serializeURL=Y$.serializeURL;$s6.serializePath=Y$.serializePath;$s6.serializeHost=Y$.serializeHost;$s6.serializeInteger=Y$.serializeInteger;$s6.serializeURLOrigin=Y$.serializeURLOrigin;$s6.setTheUsername=Y$.setTheUsername;$s6.setThePassword=Y$.setThePassword;$s6.cannotHaveAUsernamePasswordPort=Y$.cannotHaveAUsernamePasswordPort;$s6.hasAnOpaquePath=Y$.hasAnOpaquePath;$s6.percentDecodeString=NCB.percentDecodeString;$s6.percentDecodeBytes=NCB.percentDecodeBytes});
var LE0=E((Zt5,iHB)=>{var Mt6=J1("buffer").Buffer;iHB.exports=function A(B){if(typeof B==="string")return B;if(typeof B==="number"||Mt6.isBuffer(B))return B.toString();return JSON.stringify(B)}});
var MVB=E((jn6,LVB)=>{jn6.STATUS_MAPPING={mapped:1,valid:2,disallowed:3,deviation:6,ignored:7}});
var NE0=E((Dt5,pHB)=>{var Vt6=bHB(),ce=ue().Buffer,SM=J1("crypto"),hHB=YE0(),fHB=J1("util"),Ct6=`"%s" is not a valid algorithm.
  Supported algorithms are:
  "HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "PS256", "PS384", "PS512", "ES256", "ES384", "ES512" and "none".`,eD1="secret must be a string or buffer",de="key must be a string or a buffer",Kt6="key must be a string, a buffer or an object",$E0=typeof SM.createPublicKey==="function";if($E0)de+=" or a KeyObject",eD1+="or a KeyObject";function gHB(A){if(ce.isBuffer(A))return;if(typeof A==="string")return;if(!$E0)throw K$(de);if(typeof A!=="object")throw K$(de);if(typeof A.type!=="string")throw K$(de);if(typeof A.asymmetricKeyType!=="string")throw K$(de);if(typeof A.export!=="function")throw K$(de)}function uHB(A){if(ce.isBuffer(A))return;if(typeof A==="string")return;if(typeof A==="object")return;throw K$(Kt6)}function Ht6(A){if(ce.isBuffer(A))return;if(typeof A==="string")return A;if(!$E0)throw K$(eD1);if(typeof A!=="object")throw K$(eD1);if(A.type!=="secret")throw K$(eD1);if(typeof A.export!=="function")throw K$(eD1)}function qE0(A){return A.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function mHB(A){A=A.toString();var B=4-A.length%4;if(B!==4)for(var Q=0;Q<B;++Q)A+="=";return A.replace(/\-/g,"+").replace(/_/g,"/")}function K$(A){var B=[].slice.call(arguments,1),Q=fHB.format.bind(fHB,A).apply(null,B);return new TypeError(Q)}function zt6(A){return ce.isBuffer(A)||typeof A==="string"}function AZ1(A){if(!zt6(A))A=JSON.stringify(A);return A}function dHB(A){return function B(Q,D){Ht6(D),Q=AZ1(Q);var Z=SM.createHmac("sha"+A,D),G=(Z.update(Q),Z.digest("base64"));return qE0(G)}}function Et6(A){return function B(Q,D,Z){var G=dHB(A)(Q,Z);return Vt6(ce.from(D),ce.from(G))}}function cHB(A){return function B(Q,D){uHB(D),Q=AZ1(Q);var Z=SM.createSign("RSA-SHA"+A),G=(Z.update(Q),Z.sign(D,"base64"));return qE0(G)}}function lHB(A){return function B(Q,D,Z){gHB(Z),Q=AZ1(Q),D=mHB(D);var G=SM.createVerify("RSA-SHA"+A);return G.update(Q),G.verify(Z,D,"base64")}}function Ut6(A){return function B(Q,D){uHB(D),Q=AZ1(Q);var Z=SM.createSign("RSA-SHA"+A),G=(Z.update(Q),Z.sign({key:D,padding:SM.constants.RSA_PKCS1_PSS_PADDING,saltLength:SM.constants.RSA_PSS_SALTLEN_DIGEST},"base64"));return qE0(G)}}function wt6(A){return function B(Q,D,Z){gHB(Z),Q=AZ1(Q),D=mHB(D);var G=SM.createVerify("RSA-SHA"+A);return G.update(Q),G.verify({key:Z,padding:SM.constants.RSA_PKCS1_PSS_PADDING,saltLength:SM.constants.RSA_PSS_SALTLEN_DIGEST},D,"base64")}}function $t6(A){var B=cHB(A);return function Q(){var D=B.apply(null,arguments);return D=hHB.derToJose(D,"ES"+A),D}}function qt6(A){var B=lHB(A);return function Q(D,Z,G){Z=hHB.joseToDer(Z,"ES"+A).toString("base64");var F=B(D,Z,G);return F}}function Nt6(){return function A(){return""}}function Lt6(){return function A(B,Q){return Q===""}}pHB.exports=function A(B){var Q={hs:dHB,rs:cHB,ps:Ut6,es:$t6,none:Nt6},D={hs:Et6,rs:lHB,ps:wt6,es:qt6,none:Lt6},Z=B.match(/^(RS|PS|ES|HS)(256|384|512)$|^(none)$/);if(!Z)throw K$(Ct6,B);var G=(Z[1]||Z[3]).toLowerCase(),F=Z[2];return{sign:Q[G](F),verify:D[G](F)}}});
var NVB=E((Jo5,Sn6)=>{Sn6.exports=[[[0,44],2],[[45,46],2],[47,2],[[48,57],2],[[58,64],2],[65,1,"a"],[66,1,"b"],[67,1,"c"],[68,1,"d"],[69,1,"e"],[70,1,"f"],[71,1,"g"],[72,1,"h"],[73,1,"i"],[74,1,"j"],[75,1,"k"],[76,1,"l"],[77,1,"m"],[78,1,"n"],[79,1,"o"],[80,1,"p"],[81,1,"q"],[82,1,"r"],[83,1,"s"],[84,1,"t"],[85,1,"u"],[86,1,"v"],[87,1,"w"],[88,1,"x"],[89,1,"y"],[90,1,"z"],[[91,96],2],[[97,122],2],[[123,127],2],[[128,159],3],[160,1," "],[[161,167],2],[168,1," ̈"],[169,2],[170,1,"a"],[[171,172],2],[173,7],[174,2],[175,1," ̄"],[[176,177],2],[178,1,"2"],[179,1,"3"],[180,1," ́"],[181,1,"μ"],[182,2],[183,2],[184,1," ̧"],[185,1,"1"],[186,1,"o"],[187,2],[188,1,"1⁄4"],[189,1,"1⁄2"],[190,1,"3⁄4"],[191,2],[192,1,"à"],[193,1,"á"],[194,1,"â"],[195,1,"ã"],[196,1,"ä"],[197,1,"å"],[198,1,"æ"],[199,1,"ç"],[200,1,"è"],[201,1,"é"],[202,1,"ê"],[203,1,"ë"],[204,1,"ì"],[205,1,"í"],[206,1,"î"],[207,1,"ï"],[208,1,"ð"],[209,1,"ñ"],[210,1,"ò"],[211,1,"ó"],[212,1,"ô"],[213,1,"õ"],[214,1,"ö"],[215,2],[216,1,"ø"],[217,1,"ù"],[218,1,"ú"],[219,1,"û"],[220,1,"ü"],[221,1,"ý"],[222,1,"þ"],[223,6,"ss"],[[224,246],2],[247,2],[[248,255],2],[256,1,"ā"],[257,2],[258,1,"ă"],[259,2],[260,1,"ą"],[261,2],[262,1,"ć"],[263,2],[264,1,"ĉ"],[265,2],[266,1,"ċ"],[267,2],[268,1,"č"],[269,2],[270,1,"ď"],[271,2],[272,1,"đ"],[273,2],[274,1,"ē"],[275,2],[276,1,"ĕ"],[277,2],[278,1,"ė"],[279,2],[280,1,"ę"],[281,2],[282,1,"ě"],[283,2],[284,1,"ĝ"],[285,2],[286,1,"ğ"],[287,2],[288,1,"ġ"],[289,2],[290,1,"ģ"],[291,2],[292,1,"ĥ"],[293,2],[294,1,"ħ"],[295,2],[296,1,"ĩ"],[297,2],[298,1,"ī"],[299,2],[300,1,"ĭ"],[301,2],[302,1,"į"],[303,2],[304,1,"i̇"],[305,2],[[306,307],1,"ij"],[308,1,"ĵ"],[309,2],[310,1,"ķ"],[[311,312],2],[313,1,"ĺ"],[314,2],[315,1,"ļ"],[316,2],[317,1,"ľ"],[318,2],[[319,320],1,"l·"],[321,1,"ł"],[322,2],[323,1,"ń"],[324,2],[325,1,"ņ"],[326,2],[327,1,"ň"],[328,2],[329,1,"ʼn"],[330,1,"ŋ"],[331,2],[332,1,"ō"],[333,2],[334,1,"ŏ"],[335,2],[336,1,"ő"],[337,2],[338,1,"œ"],[339,2],[340,1,"ŕ"],[341,2],[342,1,"ŗ"],[343,2],[344,1,"ř"],[345,2],[346,1,"ś"],[347,2],[348,1,"ŝ"],[349,2],[350,1,"ş"],[351,2],[352,1,"š"],[353,2],[354,1,"ţ"],[355,2],[356,1,"ť"],[357,2],[358,1,"ŧ"],[359,2],[360,1,"ũ"],[361,2],[362,1,"ū"],[363,2],[364,1,"ŭ"],[365,2],[366,1,"ů"],[367,2],[368,1,"ű"],[369,2],[370,1,"ų"],[371,2],[372,1,"ŵ"],[373,2],[374,1,"ŷ"],[375,2],[376,1,"ÿ"],[377,1,"ź"],[378,2],[379,1,"ż"],[380,2],[381,1,"ž"],[382,2],[383,1,"s"],[384,2],[385,1,"ɓ"],[386,1,"ƃ"],[387,2],[388,1,"ƅ"],[389,2],[390,1,"ɔ"],[391,1,"ƈ"],[392,2],[393,1,"ɖ"],[394,1,"ɗ"],[395,1,"ƌ"],[[396,397],2],[398,1,"ǝ"],[399,1,"ə"],[400,1,"ɛ"],[401,1,"ƒ"],[402,2],[403,1,"ɠ"],[404,1,"ɣ"],[405,2],[406,1,"ɩ"],[407,1,"ɨ"],[408,1,"ƙ"],[[409,411],2],[412,1,"ɯ"],[413,1,"ɲ"],[414,2],[415,1,"ɵ"],[416,1,"ơ"],[417,2],[418,1,"ƣ"],[419,2],[420,1,"ƥ"],[421,2],[422,1,"ʀ"],[423,1,"ƨ"],[424,2],[425,1,"ʃ"],[[426,427],2],[428,1,"ƭ"],[429,2],[430,1,"ʈ"],[431,1,"ư"],[432,2],[433,1,"ʊ"],[434,1,"ʋ"],[435,1,"ƴ"],[436,2],[437,1,"ƶ"],[438,2],[439,1,"ʒ"],[440,1,"ƹ"],[[441,443],2],[444,1,"ƽ"],[[445,451],2],[[452,454],1,"dž"],[[455,457],1,"lj"],[[458,460],1,"nj"],[461,1,"ǎ"],[462,2],[463,1,"ǐ"],[464,2],[465,1,"ǒ"],[466,2],[467,1,"ǔ"],[468,2],[469,1,"ǖ"],[470,2],[471,1,"ǘ"],[472,2],[473,1,"ǚ"],[474,2],[475,1,"ǜ"],[[476,477],2],[478,1,"ǟ"],[479,2],[480,1,"ǡ"],[481,2],[482,1,"ǣ"],[483,2],[484,1,"ǥ"],[485,2],[486,1,"ǧ"],[487,2],[488,1,"ǩ"],[489,2],[490,1,"ǫ"],[491,2],[492,1,"ǭ"],[493,2],[494,1,"ǯ"],[[495,496],2],[[497,499],1,"dz"],[500,1,"ǵ"],[501,2],[502,1,"ƕ"],[503,1,"ƿ"],[504,1,"ǹ"],[505,2],[506,1,"ǻ"],[507,2],[508,1,"ǽ"],[509,2],[510,1,"ǿ"],[511,2],[512,1,"ȁ"],[513,2],[514,1,"ȃ"],[515,2],[516,1,"ȅ"],[517,2],[518,1,"ȇ"],[519,2],[520,1,"ȉ"],[521,2],[522,1,"ȋ"],[523,2],[524,1,"ȍ"],[525,2],[526,1,"ȏ"],[527,2],[528,1,"ȑ"],[529,2],[530,1,"ȓ"],[531,2],[532,1,"ȕ"],[533,2],[534,1,"ȗ"],[535,2],[536,1,"ș"],[537,2],[538,1,"ț"],[539,2],[540,1,"ȝ"],[541,2],[542,1,"ȟ"],[543,2],[544,1,"ƞ"],[545,2],[546,1,"ȣ"],[547,2],[548,1,"ȥ"],[549,2],[550,1,"ȧ"],[551,2],[552,1,"ȩ"],[553,2],[554,1,"ȫ"],[555,2],[556,1,"ȭ"],[557,2],[558,1,"ȯ"],[559,2],[560,1,"ȱ"],[561,2],[562,1,"ȳ"],[563,2],[[564,566],2],[[567,569],2],[570,1,"ⱥ"],[571,1,"ȼ"],[572,2],[573,1,"ƚ"],[574,1,"ⱦ"],[[575,576],2],[577,1,"ɂ"],[578,2],[579,1,"ƀ"],[580,1,"ʉ"],[581,1,"ʌ"],[582,1,"ɇ"],[583,2],[584,1,"ɉ"],[585,2],[586,1,"ɋ"],[587,2],[588,1,"ɍ"],[589,2],[590,1,"ɏ"],[591,2],[[592,680],2],[[681,685],2],[[686,687],2],[688,1,"h"],[689,1,"ɦ"],[690,1,"j"],[691,1,"r"],[692,1,"ɹ"],[693,1,"ɻ"],[694,1,"ʁ"],[695,1,"w"],[696,1,"y"],[[697,705],2],[[706,709],2],[[710,721],2],[[722,727],2],[728,1," ̆"],[729,1," ̇"],[730,1," ̊"],[731,1," ̨"],[732,1," ̃"],[733,1," ̋"],[734,2],[735,2],[736,1,"ɣ"],[737,1,"l"],[738,1,"s"],[739,1,"x"],[740,1,"ʕ"],[[741,745],2],[[746,747],2],[748,2],[749,2],[750,2],[[751,767],2],[[768,831],2],[832,1,"̀"],[833,1,"́"],[834,2],[835,1,"̓"],[836,1,"̈́"],[837,1,"ι"],[[838,846],2],[847,7],[[848,855],2],[[856,860],2],[[861,863],2],[[864,865],2],[866,2],[[867,879],2],[880,1,"ͱ"],[881,2],[882,1,"ͳ"],[883,2],[884,1,"ʹ"],[885,2],[886,1,"ͷ"],[887,2],[[888,889],3],[890,1," ι"],[[891,893],2],[894,1,";"],[895,1,"ϳ"],[[896,899],3],[900,1," ́"],[901,1," ̈́"],[902,1,"ά"],[903,1,"·"],[904,1,"έ"],[905,1,"ή"],[906,1,"ί"],[907,3],[908,1,"ό"],[909,3],[910,1,"ύ"],[911,1,"ώ"],[912,2],[913,1,"α"],[914,1,"β"],[915,1,"γ"],[916,1,"δ"],[917,1,"ε"],[918,1,"ζ"],[919,1,"η"],[920,1,"θ"],[921,1,"ι"],[922,1,"κ"],[923,1,"λ"],[924,1,"μ"],[925,1,"ν"],[926,1,"ξ"],[927,1,"ο"],[928,1,"π"],[929,1,"ρ"],[930,3],[931,1,"σ"],[932,1,"τ"],[933,1,"υ"],[934,1,"φ"],[935,1,"χ"],[936,1,"ψ"],[937,1,"ω"],[938,1,"ϊ"],[939,1,"ϋ"],[[940,961],2],[962,6,"σ"],[[963,974],2],[975,1,"ϗ"],[976,1,"β"],[977,1,"θ"],[978,1,"υ"],[979,1,"ύ"],[980,1,"ϋ"],[981,1,"φ"],[982,1,"π"],[983,2],[984,1,"ϙ"],[985,2],[986,1,"ϛ"],[987,2],[988,1,"ϝ"],[989,2],[990,1,"ϟ"],[991,2],[992,1,"ϡ"],[993,2],[994,1,"ϣ"],[995,2],[996,1,"ϥ"],[997,2],[998,1,"ϧ"],[999,2],[1000,1,"ϩ"],[1001,2],[1002,1,"ϫ"],[1003,2],[1004,1,"ϭ"],[1005,2],[1006,1,"ϯ"],[1007,2],[1008,1,"κ"],[1009,1,"ρ"],[1010,1,"σ"],[1011,2],[1012,1,"θ"],[1013,1,"ε"],[1014,2],[1015,1,"ϸ"],[1016,2],[1017,1,"σ"],[1018,1,"ϻ"],[1019,2],[1020,2],[1021,1,"ͻ"],[1022,1,"ͼ"],[1023,1,"ͽ"],[1024,1,"ѐ"],[1025,1,"ё"],[1026,1,"ђ"],[1027,1,"ѓ"],[1028,1,"є"],[1029,1,"ѕ"],[1030,1,"і"],[1031,1,"ї"],[1032,1,"ј"],[1033,1,"љ"],[1034,1,"њ"],[1035,1,"ћ"],[1036,1,"ќ"],[1037,1,"ѝ"],[1038,1,"ў"],[1039,1,"џ"],[1040,1,"а"],[1041,1,"б"],[1042,1,"в"],[1043,1,"г"],[1044,1,"д"],[1045,1,"е"],[1046,1,"ж"],[1047,1,"з"],[1048,1,"и"],[1049,1,"й"],[1050,1,"к"],[1051,1,"л"],[1052,1,"м"],[1053,1,"н"],[1054,1,"о"],[1055,1,"п"],[1056,1,"р"],[1057,1,"с"],[1058,1,"т"],[1059,1,"у"],[1060,1,"ф"],[1061,1,"х"],[1062,1,"ц"],[1063,1,"ч"],[1064,1,"ш"],[1065,1,"щ"],[1066,1,"ъ"],[1067,1,"ы"],[1068,1,"ь"],[1069,1,"э"],[1070,1,"ю"],[1071,1,"я"],[[1072,1103],2],[1104,2],[[1105,1116],2],[1117,2],[[1118,1119],2],[1120,1,"ѡ"],[1121,2],[1122,1,"ѣ"],[1123,2],[1124,1,"ѥ"],[1125,2],[1126,1,"ѧ"],[1127,2],[1128,1,"ѩ"],[1129,2],[1130,1,"ѫ"],[1131,2],[1132,1,"ѭ"],[1133,2],[1134,1,"ѯ"],[1135,2],[1136,1,"ѱ"],[1137,2],[1138,1,"ѳ"],[1139,2],[1140,1,"ѵ"],[1141,2],[1142,1,"ѷ"],[1143,2],[1144,1,"ѹ"],[1145,2],[1146,1,"ѻ"],[1147,2],[1148,1,"ѽ"],[1149,2],[1150,1,"ѿ"],[1151,2],[1152,1,"ҁ"],[1153,2],[1154,2],[[1155,1158],2],[1159,2],[[1160,1161],2],[1162,1,"ҋ"],[1163,2],[1164,1,"ҍ"],[1165,2],[1166,1,"ҏ"],[1167,2],[1168,1,"ґ"],[1169,2],[1170,1,"ғ"],[1171,2],[1172,1,"ҕ"],[1173,2],[1174,1,"җ"],[1175,2],[1176,1,"ҙ"],[1177,2],[1178,1,"қ"],[1179,2],[1180,1,"ҝ"],[1181,2],[1182,1,"ҟ"],[1183,2],[1184,1,"ҡ"],[1185,2],[1186,1,"ң"],[1187,2],[1188,1,"ҥ"],[1189,2],[1190,1,"ҧ"],[1191,2],[1192,1,"ҩ"],[1193,2],[1194,1,"ҫ"],[1195,2],[1196,1,"ҭ"],[1197,2],[1198,1,"ү"],[1199,2],[1200,1,"ұ"],[1201,2],[1202,1,"ҳ"],[1203,2],[1204,1,"ҵ"],[1205,2],[1206,1,"ҷ"],[1207,2],[1208,1,"ҹ"],[1209,2],[1210,1,"һ"],[1211,2],[1212,1,"ҽ"],[1213,2],[1214,1,"ҿ"],[1215,2],[1216,1,"ӏ"],[1217,1,"ӂ"],[1218,2],[1219,1,"ӄ"],[1220,2],[1221,1,"ӆ"],[1222,2],[1223,1,"ӈ"],[1224,2],[1225,1,"ӊ"],[1226,2],[1227,1,"ӌ"],[1228,2],[1229,1,"ӎ"],[1230,2],[1231,2],[1232,1,"ӑ"],[1233,2],[1234,1,"ӓ"],[1235,2],[1236,1,"ӕ"],[1237,2],[1238,1,"ӗ"],[1239,2],[1240,1,"ә"],[1241,2],[1242,1,"ӛ"],[1243,2],[1244,1,"ӝ"],[1245,2],[1246,1,"ӟ"],[1247,2],[1248,1,"ӡ"],[1249,2],[1250,1,"ӣ"],[1251,2],[1252,1,"ӥ"],[1253,2],[1254,1,"ӧ"],[1255,2],[1256,1,"ө"],[1257,2],[1258,1,"ӫ"],[1259,2],[1260,1,"ӭ"],[1261,2],[1262,1,"ӯ"],[1263,2],[1264,1,"ӱ"],[1265,2],[1266,1,"ӳ"],[1267,2],[1268,1,"ӵ"],[1269,2],[1270,1,"ӷ"],[1271,2],[1272,1,"ӹ"],[1273,2],[1274,1,"ӻ"],[1275,2],[1276,1,"ӽ"],[1277,2],[1278,1,"ӿ"],[1279,2],[1280,1,"ԁ"],[1281,2],[1282,1,"ԃ"],[1283,2],[1284,1,"ԅ"],[1285,2],[1286,1,"ԇ"],[1287,2],[1288,1,"ԉ"],[1289,2],[1290,1,"ԋ"],[1291,2],[1292,1,"ԍ"],[1293,2],[1294,1,"ԏ"],[1295,2],[1296,1,"ԑ"],[1297,2],[1298,1,"ԓ"],[1299,2],[1300,1,"ԕ"],[1301,2],[1302,1,"ԗ"],[1303,2],[1304,1,"ԙ"],[1305,2],[1306,1,"ԛ"],[1307,2],[1308,1,"ԝ"],[1309,2],[1310,1,"ԟ"],[1311,2],[1312,1,"ԡ"],[1313,2],[1314,1,"ԣ"],[1315,2],[1316,1,"ԥ"],[1317,2],[1318,1,"ԧ"],[1319,2],[1320,1,"ԩ"],[1321,2],[1322,1,"ԫ"],[1323,2],[1324,1,"ԭ"],[1325,2],[1326,1,"ԯ"],[1327,2],[1328,3],[1329,1,"ա"],[1330,1,"բ"],[1331,1,"գ"],[1332,1,"դ"],[1333,1,"ե"],[1334,1,"զ"],[1335,1,"է"],[1336,1,"ը"],[1337,1,"թ"],[1338,1,"ժ"],[1339,1,"ի"],[1340,1,"լ"],[1341,1,"խ"],[1342,1,"ծ"],[1343,1,"կ"],[1344,1,"հ"],[1345,1,"ձ"],[1346,1,"ղ"],[1347,1,"ճ"],[1348,1,"մ"],[1349,1,"յ"],[1350,1,"ն"],[1351,1,"շ"],[1352,1,"ո"],[1353,1,"չ"],[1354,1,"պ"],[1355,1,"ջ"],[1356,1,"ռ"],[1357,1,"ս"],[1358,1,"վ"],[1359,1,"տ"],[1360,1,"ր"],[1361,1,"ց"],[1362,1,"ւ"],[1363,1,"փ"],[1364,1,"ք"],[1365,1,"օ"],[1366,1,"ֆ"],[[1367,1368],3],[1369,2],[[1370,1375],2],[1376,2],[[1377,1414],2],[1415,1,"եւ"],[1416,2],[1417,2],[1418,2],[[1419,1420],3],[[1421,1422],2],[1423,2],[1424,3],[[1425,1441],2],[1442,2],[[1443,1455],2],[[1456,1465],2],[1466,2],[[1467,1469],2],[1470,2],[1471,2],[1472,2],[[1473,1474],2],[1475,2],[1476,2],[1477,2],[1478,2],[1479,2],[[1480,1487],3],[[1488,1514],2],[[1515,1518],3],[1519,2],[[1520,1524],2],[[1525,1535],3],[[1536,1539],3],[1540,3],[1541,3],[[1542,1546],2],[1547,2],[1548,2],[[1549,1551],2],[[1552,1557],2],[[1558,1562],2],[1563,2],[1564,3],[1565,2],[1566,2],[1567,2],[1568,2],[[1569,1594],2],[[1595,1599],2],[1600,2],[[1601,1618],2],[[1619,1621],2],[[1622,1624],2],[[1625,1630],2],[1631,2],[[1632,1641],2],[[1642,1645],2],[[1646,1647],2],[[1648,1652],2],[1653,1,"اٴ"],[1654,1,"وٴ"],[1655,1,"ۇٴ"],[1656,1,"يٴ"],[[1657,1719],2],[[1720,1721],2],[[1722,1726],2],[1727,2],[[1728,1742],2],[1743,2],[[1744,1747],2],[1748,2],[[1749,1756],2],[1757,3],[1758,2],[[1759,1768],2],[1769,2],[[1770,1773],2],[[1774,1775],2],[[1776,1785],2],[[1786,1790],2],[1791,2],[[1792,1805],2],[1806,3],[1807,3],[[1808,1836],2],[[1837,1839],2],[[1840,1866],2],[[1867,1868],3],[[1869,1871],2],[[1872,1901],2],[[1902,1919],2],[[1920,1968],2],[1969,2],[[1970,1983],3],[[1984,2037],2],[[2038,2042],2],[[2043,2044],3],[2045,2],[[2046,2047],2],[[2048,2093],2],[[2094,2095],3],[[2096,2110],2],[2111,3],[[2112,2139],2],[[2140,2141],3],[2142,2],[2143,3],[[2144,2154],2],[[2155,2159],3],[[2160,2183],2],[2184,2],[[2185,2190],2],[2191,3],[[2192,2193],3],[[2194,2198],3],[2199,2],[[2200,2207],2],[2208,2],[2209,2],[[2210,2220],2],[[2221,2226],2],[[2227,2228],2],[2229,2],[[2230,2237],2],[[2238,2247],2],[[2248,2258],2],[2259,2],[[2260,2273],2],[2274,3],[2275,2],[[2276,2302],2],[2303,2],[2304,2],[[2305,2307],2],[2308,2],[[2309,2361],2],[[2362,2363],2],[[2364,2381],2],[2382,2],[2383,2],[[2384,2388],2],[2389,2],[[2390,2391],2],[2392,1,"क़"],[2393,1,"ख़"],[2394,1,"ग़"],[2395,1,"ज़"],[2396,1,"ड़"],[2397,1,"ढ़"],[2398,1,"फ़"],[2399,1,"य़"],[[2400,2403],2],[[2404,2405],2],[[2406,2415],2],[2416,2],[[2417,2418],2],[[2419,2423],2],[2424,2],[[2425,2426],2],[[2427,2428],2],[2429,2],[[2430,2431],2],[2432,2],[[2433,2435],2],[2436,3],[[2437,2444],2],[[2445,2446],3],[[2447,2448],2],[[2449,2450],3],[[2451,2472],2],[2473,3],[[2474,2480],2],[2481,3],[2482,2],[[2483,2485],3],[[2486,2489],2],[[2490,2491],3],[2492,2],[2493,2],[[2494,2500],2],[[2501,2502],3],[[2503,2504],2],[[2505,2506],3],[[2507,2509],2],[2510,2],[[2511,2518],3],[2519,2],[[2520,2523],3],[2524,1,"ড়"],[2525,1,"ঢ়"],[2526,3],[2527,1,"য়"],[[2528,2531],2],[[2532,2533],3],[[2534,2545],2],[[2546,2554],2],[2555,2],[2556,2],[2557,2],[2558,2],[[2559,2560],3],[2561,2],[2562,2],[2563,2],[2564,3],[[2565,2570],2],[[2571,2574],3],[[2575,2576],2],[[2577,2578],3],[[2579,2600],2],[2601,3],[[2602,2608],2],[2609,3],[2610,2],[2611,1,"ਲ਼"],[2612,3],[2613,2],[2614,1,"ਸ਼"],[2615,3],[[2616,2617],2],[[2618,2619],3],[2620,2],[2621,3],[[2622,2626],2],[[2627,2630],3],[[2631,2632],2],[[2633,2634],3],[[2635,2637],2],[[2638,2640],3],[2641,2],[[2642,2648],3],[2649,1,"ਖ਼"],[2650,1,"ਗ਼"],[2651,1,"ਜ਼"],[2652,2],[2653,3],[2654,1,"ਫ਼"],[[2655,2661],3],[[2662,2676],2],[2677,2],[2678,2],[[2679,2688],3],[[2689,2691],2],[2692,3],[[2693,2699],2],[2700,2],[2701,2],[2702,3],[[2703,2705],2],[2706,3],[[2707,2728],2],[2729,3],[[2730,2736],2],[2737,3],[[2738,2739],2],[2740,3],[[2741,2745],2],[[2746,2747],3],[[2748,2757],2],[2758,3],[[2759,2761],2],[2762,3],[[2763,2765],2],[[2766,2767],3],[2768,2],[[2769,2783],3],[2784,2],[[2785,2787],2],[[2788,2789],3],[[2790,2799],2],[2800,2],[2801,2],[[2802,2808],3],[2809,2],[[2810,2815],2],[2816,3],[[2817,2819],2],[2820,3],[[2821,2828],2],[[2829,2830],3],[[2831,2832],2],[[2833,2834],3],[[2835,2856],2],[2857,3],[[2858,2864],2],[2865,3],[[2866,2867],2],[2868,3],[2869,2],[[2870,2873],2],[[2874,2875],3],[[2876,2883],2],[2884,2],[[2885,2886],3],[[2887,2888],2],[[2889,2890],3],[[2891,2893],2],[[2894,2900],3],[2901,2],[[2902,2903],2],[[2904,2907],3],[2908,1,"ଡ଼"],[2909,1,"ଢ଼"],[2910,3],[[2911,2913],2],[[2914,2915],2],[[2916,2917],3],[[2918,2927],2],[2928,2],[2929,2],[[2930,2935],2],[[2936,2945],3],[[2946,2947],2],[2948,3],[[2949,2954],2],[[2955,2957],3],[[2958,2960],2],[2961,3],[[2962,2965],2],[[2966,2968],3],[[2969,2970],2],[2971,3],[2972,2],[2973,3],[[2974,2975],2],[[2976,2978],3],[[2979,2980],2],[[2981,2983],3],[[2984,2986],2],[[2987,2989],3],[[2990,2997],2],[2998,2],[[2999,3001],2],[[3002,3005],3],[[3006,3010],2],[[3011,3013],3],[[3014,3016],2],[3017,3],[[3018,3021],2],[[3022,3023],3],[3024,2],[[3025,3030],3],[3031,2],[[3032,3045],3],[3046,2],[[3047,3055],2],[[3056,3058],2],[[3059,3066],2],[[3067,3071],3],[3072,2],[[3073,3075],2],[3076,2],[[3077,3084],2],[3085,3],[[3086,3088],2],[3089,3],[[3090,3112],2],[3113,3],[[3114,3123],2],[3124,2],[[3125,3129],2],[[3130,3131],3],[3132,2],[3133,2],[[3134,3140],2],[3141,3],[[3142,3144],2],[3145,3],[[3146,3149],2],[[3150,3156],3],[[3157,3158],2],[3159,3],[[3160,3161],2],[3162,2],[[3163,3164],3],[3165,2],[[3166,3167],3],[[3168,3169],2],[[3170,3171],2],[[3172,3173],3],[[3174,3183],2],[[3184,3190],3],[3191,2],[[3192,3199],2],[3200,2],[3201,2],[[3202,3203],2],[3204,2],[[3205,3212],2],[3213,3],[[3214,3216],2],[3217,3],[[3218,3240],2],[3241,3],[[3242,3251],2],[3252,3],[[3253,3257],2],[[3258,3259],3],[[3260,3261],2],[[3262,3268],2],[3269,3],[[3270,3272],2],[3273,3],[[3274,3277],2],[[3278,3284],3],[[3285,3286],2],[[3287,3292],3],[3293,2],[3294,2],[3295,3],[[3296,3297],2],[[3298,3299],2],[[3300,3301],3],[[3302,3311],2],[3312,3],[[3313,3314],2],[3315,2],[[3316,3327],3],[3328,2],[3329,2],[[3330,3331],2],[3332,2],[[3333,3340],2],[3341,3],[[3342,3344],2],[3345,3],[[3346,3368],2],[3369,2],[[3370,3385],2],[3386,2],[[3387,3388],2],[3389,2],[[3390,3395],2],[3396,2],[3397,3],[[3398,3400],2],[3401,3],[[3402,3405],2],[3406,2],[3407,2],[[3408,3411],3],[[3412,3414],2],[3415,2],[[3416,3422],2],[3423,2],[[3424,3425],2],[[3426,3427],2],[[3428,3429],3],[[3430,3439],2],[[3440,3445],2],[[3446,3448],2],[3449,2],[[3450,3455],2],[3456,3],[3457,2],[[3458,3459],2],[3460,3],[[3461,3478],2],[[3479,3481],3],[[3482,3505],2],[3506,3],[[3507,3515],2],[3516,3],[3517,2],[[3518,3519],3],[[3520,3526],2],[[3527,3529],3],[3530,2],[[3531,3534],3],[[3535,3540],2],[3541,3],[3542,2],[3543,3],[[3544,3551],2],[[3552,3557],3],[[3558,3567],2],[[3568,3569],3],[[3570,3571],2],[3572,2],[[3573,3584],3],[[3585,3634],2],[3635,1,"ํา"],[[3636,3642],2],[[3643,3646],3],[3647,2],[[3648,3662],2],[3663,2],[[3664,3673],2],[[3674,3675],2],[[3676,3712],3],[[3713,3714],2],[3715,3],[3716,2],[3717,3],[3718,2],[[3719,3720],2],[3721,2],[3722,2],[3723,3],[3724,2],[3725,2],[[3726,3731],2],[[3732,3735],2],[3736,2],[[3737,3743],2],[3744,2],[[3745,3747],2],[3748,3],[3749,2],[3750,3],[3751,2],[[3752,3753],2],[[3754,3755],2],[3756,2],[[3757,3762],2],[3763,1,"ໍາ"],[[3764,3769],2],[3770,2],[[3771,3773],2],[[3774,3775],3],[[3776,3780],2],[3781,3],[3782,2],[3783,3],[[3784,3789],2],[3790,2],[3791,3],[[3792,3801],2],[[3802,3803],3],[3804,1,"ຫນ"],[3805,1,"ຫມ"],[[3806,3807],2],[[3808,3839],3],[3840,2],[[3841,3850],2],[3851,2],[3852,1,"་"],[[3853,3863],2],[[3864,3865],2],[[3866,3871],2],[[3872,3881],2],[[3882,3892],2],[3893,2],[3894,2],[3895,2],[3896,2],[3897,2],[[3898,3901],2],[[3902,3906],2],[3907,1,"གྷ"],[[3908,3911],2],[3912,3],[[3913,3916],2],[3917,1,"ཌྷ"],[[3918,3921],2],[3922,1,"དྷ"],[[3923,3926],2],[3927,1,"བྷ"],[[3928,3931],2],[3932,1,"ཛྷ"],[[3933,3944],2],[3945,1,"ཀྵ"],[3946,2],[[3947,3948],2],[[3949,3952],3],[[3953,3954],2],[3955,1,"ཱི"],[3956,2],[3957,1,"ཱུ"],[3958,1,"ྲྀ"],[3959,1,"ྲཱྀ"],[3960,1,"ླྀ"],[3961,1,"ླཱྀ"],[[3962,3968],2],[3969,1,"ཱྀ"],[[3970,3972],2],[3973,2],[[3974,3979],2],[[3980,3983],2],[[3984,3986],2],[3987,1,"ྒྷ"],[[3988,3989],2],[3990,2],[3991,2],[3992,3],[[3993,3996],2],[3997,1,"ྜྷ"],[[3998,4001],2],[4002,1,"ྡྷ"],[[4003,4006],2],[4007,1,"ྦྷ"],[[4008,4011],2],[4012,1,"ྫྷ"],[4013,2],[[4014,4016],2],[[4017,4023],2],[4024,2],[4025,1,"ྐྵ"],[[4026,4028],2],[4029,3],[[4030,4037],2],[4038,2],[[4039,4044],2],[4045,3],[4046,2],[4047,2],[[4048,4049],2],[[4050,4052],2],[[4053,4056],2],[[4057,4058],2],[[4059,4095],3],[[4096,4129],2],[4130,2],[[4131,4135],2],[4136,2],[[4137,4138],2],[4139,2],[[4140,4146],2],[[4147,4149],2],[[4150,4153],2],[[4154,4159],2],[[4160,4169],2],[[4170,4175],2],[[4176,4185],2],[[4186,4249],2],[[4250,4253],2],[[4254,4255],2],[4256,1,"ⴀ"],[4257,1,"ⴁ"],[4258,1,"ⴂ"],[4259,1,"ⴃ"],[4260,1,"ⴄ"],[4261,1,"ⴅ"],[4262,1,"ⴆ"],[4263,1,"ⴇ"],[4264,1,"ⴈ"],[4265,1,"ⴉ"],[4266,1,"ⴊ"],[4267,1,"ⴋ"],[4268,1,"ⴌ"],[4269,1,"ⴍ"],[4270,1,"ⴎ"],[4271,1,"ⴏ"],[4272,1,"ⴐ"],[4273,1,"ⴑ"],[4274,1,"ⴒ"],[4275,1,"ⴓ"],[4276,1,"ⴔ"],[4277,1,"ⴕ"],[4278,1,"ⴖ"],[4279,1,"ⴗ"],[4280,1,"ⴘ"],[4281,1,"ⴙ"],[4282,1,"ⴚ"],[4283,1,"ⴛ"],[4284,1,"ⴜ"],[4285,1,"ⴝ"],[4286,1,"ⴞ"],[4287,1,"ⴟ"],[4288,1,"ⴠ"],[4289,1,"ⴡ"],[4290,1,"ⴢ"],[4291,1,"ⴣ"],[4292,1,"ⴤ"],[4293,1,"ⴥ"],[4294,3],[4295,1,"ⴧ"],[[4296,4300],3],[4301,1,"ⴭ"],[[4302,4303],3],[[4304,4342],2],[[4343,4344],2],[[4345,4346],2],[4347,2],[4348,1,"ნ"],[[4349,4351],2],[[4352,4441],2],[[4442,4446],2],[[4447,4448],7],[[4449,4514],2],[[4515,4519],2],[[4520,4601],2],[[4602,4607],2],[[4608,4614],2],[4615,2],[[4616,4678],2],[4679,2],[4680,2],[4681,3],[[4682,4685],2],[[4686,4687],3],[[4688,4694],2],[4695,3],[4696,2],[4697,3],[[4698,4701],2],[[4702,4703],3],[[4704,4742],2],[4743,2],[4744,2],[4745,3],[[4746,4749],2],[[4750,4751],3],[[4752,4782],2],[4783,2],[4784,2],[4785,3],[[4786,4789],2],[[4790,4791],3],[[4792,4798],2],[4799,3],[4800,2],[4801,3],[[4802,4805],2],[[4806,4807],3],[[4808,4814],2],[4815,2],[[4816,4822],2],[4823,3],[[4824,4846],2],[4847,2],[[4848,4878],2],[4879,2],[4880,2],[4881,3],[[4882,4885],2],[[4886,4887],3],[[4888,4894],2],[4895,2],[[4896,4934],2],[4935,2],[[4936,4954],2],[[4955,4956],3],[[4957,4958],2],[4959,2],[4960,2],[[4961,4988],2],[[4989,4991],3],[[4992,5007],2],[[5008,5017],2],[[5018,5023],3],[[5024,5108],2],[5109,2],[[5110,5111],3],[5112,1,"Ᏸ"],[5113,1,"Ᏹ"],[5114,1,"Ᏺ"],[5115,1,"Ᏻ"],[5116,1,"Ᏼ"],[5117,1,"Ᏽ"],[[5118,5119],3],[5120,2],[[5121,5740],2],[[5741,5742],2],[[5743,5750],2],[[5751,5759],2],[5760,3],[[5761,5786],2],[[5787,5788],2],[[5789,5791],3],[[5792,5866],2],[[5867,5872],2],[[5873,5880],2],[[5881,5887],3],[[5888,5900],2],[5901,2],[[5902,5908],2],[5909,2],[[5910,5918],3],[5919,2],[[5920,5940],2],[[5941,5942],2],[[5943,5951],3],[[5952,5971],2],[[5972,5983],3],[[5984,5996],2],[5997,3],[[5998,6000],2],[6001,3],[[6002,6003],2],[[6004,6015],3],[[6016,6067],2],[[6068,6069],7],[[6070,6099],2],[[6100,6102],2],[6103,2],[[6104,6107],2],[6108,2],[6109,2],[[6110,6111],3],[[6112,6121],2],[[6122,6127],3],[[6128,6137],2],[[6138,6143],3],[[6144,6154],2],[[6155,6158],7],[6159,7],[[6160,6169],2],[[6170,6175],3],[[6176,6263],2],[6264,2],[[6265,6271],3],[[6272,6313],2],[6314,2],[[6315,6319],3],[[6320,6389],2],[[6390,6399],3],[[6400,6428],2],[[6429,6430],2],[6431,3],[[6432,6443],2],[[6444,6447],3],[[6448,6459],2],[[6460,6463],3],[6464,2],[[6465,6467],3],[[6468,6469],2],[[6470,6509],2],[[6510,6511],3],[[6512,6516],2],[[6517,6527],3],[[6528,6569],2],[[6570,6571],2],[[6572,6575],3],[[6576,6601],2],[[6602,6607],3],[[6608,6617],2],[6618,2],[[6619,6621],3],[[6622,6623],2],[[6624,6655],2],[[6656,6683],2],[[6684,6685],3],[[6686,6687],2],[[6688,6750],2],[6751,3],[[6752,6780],2],[[6781,6782],3],[[6783,6793],2],[[6794,6799],3],[[6800,6809],2],[[6810,6815],3],[[6816,6822],2],[6823,2],[[6824,6829],2],[[6830,6831],3],[[6832,6845],2],[6846,2],[[6847,6848],2],[[6849,6862],2],[[6863,6911],3],[[6912,6987],2],[6988,2],[6989,3],[[6990,6991],2],[[6992,7001],2],[[7002,7018],2],[[7019,7027],2],[[7028,7036],2],[[7037,7038],2],[7039,2],[[7040,7082],2],[[7083,7085],2],[[7086,7097],2],[[7098,7103],2],[[7104,7155],2],[[7156,7163],3],[[7164,7167],2],[[7168,7223],2],[[7224,7226],3],[[7227,7231],2],[[7232,7241],2],[[7242,7244],3],[[7245,7293],2],[[7294,7295],2],[7296,1,"в"],[7297,1,"д"],[7298,1,"о"],[7299,1,"с"],[[7300,7301],1,"т"],[7302,1,"ъ"],[7303,1,"ѣ"],[7304,1,"ꙋ"],[7305,1,"ᲊ"],[7306,2],[[7307,7311],3],[7312,1,"ა"],[7313,1,"ბ"],[7314,1,"გ"],[7315,1,"დ"],[7316,1,"ე"],[7317,1,"ვ"],[7318,1,"ზ"],[7319,1,"თ"],[7320,1,"ი"],[7321,1,"კ"],[7322,1,"ლ"],[7323,1,"მ"],[7324,1,"ნ"],[7325,1,"ო"],[7326,1,"პ"],[7327,1,"ჟ"],[7328,1,"რ"],[7329,1,"ს"],[7330,1,"ტ"],[7331,1,"უ"],[7332,1,"ფ"],[7333,1,"ქ"],[7334,1,"ღ"],[7335,1,"ყ"],[7336,1,"შ"],[7337,1,"ჩ"],[7338,1,"ც"],[7339,1,"ძ"],[7340,1,"წ"],[7341,1,"ჭ"],[7342,1,"ხ"],[7343,1,"ჯ"],[7344,1,"ჰ"],[7345,1,"ჱ"],[7346,1,"ჲ"],[7347,1,"ჳ"],[7348,1,"ჴ"],[7349,1,"ჵ"],[7350,1,"ჶ"],[7351,1,"ჷ"],[7352,1,"ჸ"],[7353,1,"ჹ"],[7354,1,"ჺ"],[[7355,7356],3],[7357,1,"ჽ"],[7358,1,"ჾ"],[7359,1,"ჿ"],[[7360,7367],2],[[7368,7375],3],[[7376,7378],2],[7379,2],[[7380,7410],2],[[7411,7414],2],[7415,2],[[7416,7417],2],[7418,2],[[7419,7423],3],[[7424,7467],2],[7468,1,"a"],[7469,1,"æ"],[7470,1,"b"],[7471,2],[7472,1,"d"],[7473,1,"e"],[7474,1,"ǝ"],[7475,1,"g"],[7476,1,"h"],[7477,1,"i"],[7478,1,"j"],[7479,1,"k"],[7480,1,"l"],[7481,1,"m"],[7482,1,"n"],[7483,2],[7484,1,"o"],[7485,1,"ȣ"],[7486,1,"p"],[7487,1,"r"],[7488,1,"t"],[7489,1,"u"],[7490,1,"w"],[7491,1,"a"],[7492,1,"ɐ"],[7493,1,"ɑ"],[7494,1,"ᴂ"],[7495,1,"b"],[7496,1,"d"],[7497,1,"e"],[7498,1,"ə"],[7499,1,"ɛ"],[7500,1,"ɜ"],[7501,1,"g"],[7502,2],[7503,1,"k"],[7504,1,"m"],[7505,1,"ŋ"],[7506,1,"o"],[7507,1,"ɔ"],[7508,1,"ᴖ"],[7509,1,"ᴗ"],[7510,1,"p"],[7511,1,"t"],[7512,1,"u"],[7513,1,"ᴝ"],[7514,1,"ɯ"],[7515,1,"v"],[7516,1,"ᴥ"],[7517,1,"β"],[7518,1,"γ"],[7519,1,"δ"],[7520,1,"φ"],[7521,1,"χ"],[7522,1,"i"],[7523,1,"r"],[7524,1,"u"],[7525,1,"v"],[7526,1,"β"],[7527,1,"γ"],[7528,1,"ρ"],[7529,1,"φ"],[7530,1,"χ"],[7531,2],[[7532,7543],2],[7544,1,"н"],[[7545,7578],2],[7579,1,"ɒ"],[7580,1,"c"],[7581,1,"ɕ"],[7582,1,"ð"],[7583,1,"ɜ"],[7584,1,"f"],[7585,1,"ɟ"],[7586,1,"ɡ"],[7587,1,"ɥ"],[7588,1,"ɨ"],[7589,1,"ɩ"],[7590,1,"ɪ"],[7591,1,"ᵻ"],[7592,1,"ʝ"],[7593,1,"ɭ"],[7594,1,"ᶅ"],[7595,1,"ʟ"],[7596,1,"ɱ"],[7597,1,"ɰ"],[7598,1,"ɲ"],[7599,1,"ɳ"],[7600,1,"ɴ"],[7601,1,"ɵ"],[7602,1,"ɸ"],[7603,1,"ʂ"],[7604,1,"ʃ"],[7605,1,"ƫ"],[7606,1,"ʉ"],[7607,1,"ʊ"],[7608,1,"ᴜ"],[7609,1,"ʋ"],[7610,1,"ʌ"],[7611,1,"z"],[7612,1,"ʐ"],[7613,1,"ʑ"],[7614,1,"ʒ"],[7615,1,"θ"],[[7616,7619],2],[[7620,7626],2],[[7627,7654],2],[[7655,7669],2],[[7670,7673],2],[7674,2],[7675,2],[7676,2],[7677,2],[[7678,7679],2],[7680,1,"ḁ"],[7681,2],[7682,1,"ḃ"],[7683,2],[7684,1,"ḅ"],[7685,2],[7686,1,"ḇ"],[7687,2],[7688,1,"ḉ"],[7689,2],[7690,1,"ḋ"],[7691,2],[7692,1,"ḍ"],[7693,2],[7694,1,"ḏ"],[7695,2],[7696,1,"ḑ"],[7697,2],[7698,1,"ḓ"],[7699,2],[7700,1,"ḕ"],[7701,2],[7702,1,"ḗ"],[7703,2],[7704,1,"ḙ"],[7705,2],[7706,1,"ḛ"],[7707,2],[7708,1,"ḝ"],[7709,2],[7710,1,"ḟ"],[7711,2],[7712,1,"ḡ"],[7713,2],[7714,1,"ḣ"],[7715,2],[7716,1,"ḥ"],[7717,2],[7718,1,"ḧ"],[7719,2],[7720,1,"ḩ"],[7721,2],[7722,1,"ḫ"],[7723,2],[7724,1,"ḭ"],[7725,2],[7726,1,"ḯ"],[7727,2],[7728,1,"ḱ"],[7729,2],[7730,1,"ḳ"],[7731,2],[7732,1,"ḵ"],[7733,2],[7734,1,"ḷ"],[7735,2],[7736,1,"ḹ"],[7737,2],[7738,1,"ḻ"],[7739,2],[7740,1,"ḽ"],[7741,2],[7742,1,"ḿ"],[7743,2],[7744,1,"ṁ"],[7745,2],[7746,1,"ṃ"],[7747,2],[7748,1,"ṅ"],[7749,2],[7750,1,"ṇ"],[7751,2],[7752,1,"ṉ"],[7753,2],[7754,1,"ṋ"],[7755,2],[7756,1,"ṍ"],[7757,2],[7758,1,"ṏ"],[7759,2],[7760,1,"ṑ"],[7761,2],[7762,1,"ṓ"],[7763,2],[7764,1,"ṕ"],[7765,2],[7766,1,"ṗ"],[7767,2],[7768,1,"ṙ"],[7769,2],[7770,1,"ṛ"],[7771,2],[7772,1,"ṝ"],[7773,2],[7774,1,"ṟ"],[7775,2],[7776,1,"ṡ"],[7777,2],[7778,1,"ṣ"],[7779,2],[7780,1,"ṥ"],[7781,2],[7782,1,"ṧ"],[7783,2],[7784,1,"ṩ"],[7785,2],[7786,1,"ṫ"],[7787,2],[7788,1,"ṭ"],[7789,2],[7790,1,"ṯ"],[7791,2],[7792,1,"ṱ"],[7793,2],[7794,1,"ṳ"],[7795,2],[7796,1,"ṵ"],[7797,2],[7798,1,"ṷ"],[7799,2],[7800,1,"ṹ"],[7801,2],[7802,1,"ṻ"],[7803,2],[7804,1,"ṽ"],[7805,2],[7806,1,"ṿ"],[7807,2],[7808,1,"ẁ"],[7809,2],[7810,1,"ẃ"],[7811,2],[7812,1,"ẅ"],[7813,2],[7814,1,"ẇ"],[7815,2],[7816,1,"ẉ"],[7817,2],[7818,1,"ẋ"],[7819,2],[7820,1,"ẍ"],[7821,2],[7822,1,"ẏ"],[7823,2],[7824,1,"ẑ"],[7825,2],[7826,1,"ẓ"],[7827,2],[7828,1,"ẕ"],[[7829,7833],2],[7834,1,"aʾ"],[7835,1,"ṡ"],[[7836,7837],2],[7838,1,"ß"],[7839,2],[7840,1,"ạ"],[7841,2],[7842,1,"ả"],[7843,2],[7844,1,"ấ"],[7845,2],[7846,1,"ầ"],[7847,2],[7848,1,"ẩ"],[7849,2],[7850,1,"ẫ"],[7851,2],[7852,1,"ậ"],[7853,2],[7854,1,"ắ"],[7855,2],[7856,1,"ằ"],[7857,2],[7858,1,"ẳ"],[7859,2],[7860,1,"ẵ"],[7861,2],[7862,1,"ặ"],[7863,2],[7864,1,"ẹ"],[7865,2],[7866,1,"ẻ"],[7867,2],[7868,1,"ẽ"],[7869,2],[7870,1,"ế"],[7871,2],[7872,1,"ề"],[7873,2],[7874,1,"ể"],[7875,2],[7876,1,"ễ"],[7877,2],[7878,1,"ệ"],[7879,2],[7880,1,"ỉ"],[7881,2],[7882,1,"ị"],[7883,2],[7884,1,"ọ"],[7885,2],[7886,1,"ỏ"],[7887,2],[7888,1,"ố"],[7889,2],[7890,1,"ồ"],[7891,2],[7892,1,"ổ"],[7893,2],[7894,1,"ỗ"],[7895,2],[7896,1,"ộ"],[7897,2],[7898,1,"ớ"],[7899,2],[7900,1,"ờ"],[7901,2],[7902,1,"ở"],[7903,2],[7904,1,"ỡ"],[7905,2],[7906,1,"ợ"],[7907,2],[7908,1,"ụ"],[7909,2],[7910,1,"ủ"],[7911,2],[7912,1,"ứ"],[7913,2],[7914,1,"ừ"],[7915,2],[7916,1,"ử"],[7917,2],[7918,1,"ữ"],[7919,2],[7920,1,"ự"],[7921,2],[7922,1,"ỳ"],[7923,2],[7924,1,"ỵ"],[7925,2],[7926,1,"ỷ"],[7927,2],[7928,1,"ỹ"],[7929,2],[7930,1,"ỻ"],[7931,2],[7932,1,"ỽ"],[7933,2],[7934,1,"ỿ"],[7935,2],[[7936,7943],2],[7944,1,"ἀ"],[7945,1,"ἁ"],[7946,1,"ἂ"],[7947,1,"ἃ"],[7948,1,"ἄ"],[7949,1,"ἅ"],[7950,1,"ἆ"],[7951,1,"ἇ"],[[7952,7957],2],[[7958,7959],3],[7960,1,"ἐ"],[7961,1,"ἑ"],[7962,1,"ἒ"],[7963,1,"ἓ"],[7964,1,"ἔ"],[7965,1,"ἕ"],[[7966,7967],3],[[7968,7975],2],[7976,1,"ἠ"],[7977,1,"ἡ"],[7978,1,"ἢ"],[7979,1,"ἣ"],[7980,1,"ἤ"],[7981,1,"ἥ"],[7982,1,"ἦ"],[7983,1,"ἧ"],[[7984,7991],2],[7992,1,"ἰ"],[7993,1,"ἱ"],[7994,1,"ἲ"],[7995,1,"ἳ"],[7996,1,"ἴ"],[7997,1,"ἵ"],[7998,1,"ἶ"],[7999,1,"ἷ"],[[8000,8005],2],[[8006,8007],3],[8008,1,"ὀ"],[8009,1,"ὁ"],[8010,1,"ὂ"],[8011,1,"ὃ"],[8012,1,"ὄ"],[8013,1,"ὅ"],[[8014,8015],3],[[8016,8023],2],[8024,3],[8025,1,"ὑ"],[8026,3],[8027,1,"ὓ"],[8028,3],[8029,1,"ὕ"],[8030,3],[8031,1,"ὗ"],[[8032,8039],2],[8040,1,"ὠ"],[8041,1,"ὡ"],[8042,1,"ὢ"],[8043,1,"ὣ"],[8044,1,"ὤ"],[8045,1,"ὥ"],[8046,1,"ὦ"],[8047,1,"ὧ"],[8048,2],[8049,1,"ά"],[8050,2],[8051,1,"έ"],[8052,2],[8053,1,"ή"],[8054,2],[8055,1,"ί"],[8056,2],[8057,1,"ό"],[8058,2],[8059,1,"ύ"],[8060,2],[8061,1,"ώ"],[[8062,8063],3],[8064,1,"ἀι"],[8065,1,"ἁι"],[8066,1,"ἂι"],[8067,1,"ἃι"],[8068,1,"ἄι"],[8069,1,"ἅι"],[8070,1,"ἆι"],[8071,1,"ἇι"],[8072,1,"ἀι"],[8073,1,"ἁι"],[8074,1,"ἂι"],[8075,1,"ἃι"],[8076,1,"ἄι"],[8077,1,"ἅι"],[8078,1,"ἆι"],[8079,1,"ἇι"],[8080,1,"ἠι"],[8081,1,"ἡι"],[8082,1,"ἢι"],[8083,1,"ἣι"],[8084,1,"ἤι"],[8085,1,"ἥι"],[8086,1,"ἦι"],[8087,1,"ἧι"],[8088,1,"ἠι"],[8089,1,"ἡι"],[8090,1,"ἢι"],[8091,1,"ἣι"],[8092,1,"ἤι"],[8093,1,"ἥι"],[8094,1,"ἦι"],[8095,1,"ἧι"],[8096,1,"ὠι"],[8097,1,"ὡι"],[8098,1,"ὢι"],[8099,1,"ὣι"],[8100,1,"ὤι"],[8101,1,"ὥι"],[8102,1,"ὦι"],[8103,1,"ὧι"],[8104,1,"ὠι"],[8105,1,"ὡι"],[8106,1,"ὢι"],[8107,1,"ὣι"],[8108,1,"ὤι"],[8109,1,"ὥι"],[8110,1,"ὦι"],[8111,1,"ὧι"],[[8112,8113],2],[8114,1,"ὰι"],[8115,1,"αι"],[8116,1,"άι"],[8117,3],[8118,2],[8119,1,"ᾶι"],[8120,1,"ᾰ"],[8121,1,"ᾱ"],[8122,1,"ὰ"],[8123,1,"ά"],[8124,1,"αι"],[8125,1," ̓"],[8126,1,"ι"],[8127,1," ̓"],[8128,1," ͂"],[8129,1," ̈͂"],[8130,1,"ὴι"],[8131,1,"ηι"],[8132,1,"ήι"],[8133,3],[8134,2],[8135,1,"ῆι"],[8136,1,"ὲ"],[8137,1,"έ"],[8138,1,"ὴ"],[8139,1,"ή"],[8140,1,"ηι"],[8141,1," ̓̀"],[8142,1," ̓́"],[8143,1," ̓͂"],[[8144,8146],2],[8147,1,"ΐ"],[[8148,8149],3],[[8150,8151],2],[8152,1,"ῐ"],[8153,1,"ῑ"],[8154,1,"ὶ"],[8155,1,"ί"],[8156,3],[8157,1," ̔̀"],[8158,1," ̔́"],[8159,1," ̔͂"],[[8160,8162],2],[8163,1,"ΰ"],[[8164,8167],2],[8168,1,"ῠ"],[8169,1,"ῡ"],[8170,1,"ὺ"],[8171,1,"ύ"],[8172,1,"ῥ"],[8173,1," ̈̀"],[8174,1," ̈́"],[8175,1,"`"],[[8176,8177],3],[8178,1,"ὼι"],[8179,1,"ωι"],[8180,1,"ώι"],[8181,3],[8182,2],[8183,1,"ῶι"],[8184,1,"ὸ"],[8185,1,"ό"],[8186,1,"ὼ"],[8187,1,"ώ"],[8188,1,"ωι"],[8189,1," ́"],[8190,1," ̔"],[8191,3],[[8192,8202],1," "],[8203,7],[[8204,8205],6,""],[[8206,8207],3],[8208,2],[8209,1,"‐"],[[8210,8214],2],[8215,1," ̳"],[[8216,8227],2],[[8228,8230],3],[8231,2],[[8232,8238],3],[8239,1," "],[[8240,8242],2],[8243,1,"′′"],[8244,1,"′′′"],[8245,2],[8246,1,"‵‵"],[8247,1,"‵‵‵"],[[8248,8251],2],[8252,1,"!!"],[8253,2],[8254,1," ̅"],[[8255,8262],2],[8263,1,"??"],[8264,1,"?!"],[8265,1,"!?"],[[8266,8269],2],[[8270,8274],2],[[8275,8276],2],[[8277,8278],2],[8279,1,"′′′′"],[[8280,8286],2],[8287,1," "],[[8288,8291],7],[8292,7],[8293,3],[[8294,8297],3],[[8298,8303],7],[8304,1,"0"],[8305,1,"i"],[[8306,8307],3],[8308,1,"4"],[8309,1,"5"],[8310,1,"6"],[8311,1,"7"],[8312,1,"8"],[8313,1,"9"],[8314,1,"+"],[8315,1,"−"],[8316,1,"="],[8317,1,"("],[8318,1,")"],[8319,1,"n"],[8320,1,"0"],[8321,1,"1"],[8322,1,"2"],[8323,1,"3"],[8324,1,"4"],[8325,1,"5"],[8326,1,"6"],[8327,1,"7"],[8328,1,"8"],[8329,1,"9"],[8330,1,"+"],[8331,1,"−"],[8332,1,"="],[8333,1,"("],[8334,1,")"],[8335,3],[8336,1,"a"],[8337,1,"e"],[8338,1,"o"],[8339,1,"x"],[8340,1,"ə"],[8341,1,"h"],[8342,1,"k"],[8343,1,"l"],[8344,1,"m"],[8345,1,"n"],[8346,1,"p"],[8347,1,"s"],[8348,1,"t"],[[8349,8351],3],[[8352,8359],2],[8360,1,"rs"],[[8361,8362],2],[8363,2],[8364,2],[[8365,8367],2],[[8368,8369],2],[[8370,8373],2],[[8374,8376],2],[8377,2],[8378,2],[[8379,8381],2],[8382,2],[8383,2],[8384,2],[[8385,8399],3],[[8400,8417],2],[[8418,8419],2],[[8420,8426],2],[8427,2],[[8428,8431],2],[8432,2],[[8433,8447],3],[8448,1,"a/c"],[8449,1,"a/s"],[8450,1,"c"],[8451,1,"°c"],[8452,2],[8453,1,"c/o"],[8454,1,"c/u"],[8455,1,"ɛ"],[8456,2],[8457,1,"°f"],[8458,1,"g"],[[8459,8462],1,"h"],[8463,1,"ħ"],[[8464,8465],1,"i"],[[8466,8467],1,"l"],[8468,2],[8469,1,"n"],[8470,1,"no"],[[8471,8472],2],[8473,1,"p"],[8474,1,"q"],[[8475,8477],1,"r"],[[8478,8479],2],[8480,1,"sm"],[8481,1,"tel"],[8482,1,"tm"],[8483,2],[8484,1,"z"],[8485,2],[8486,1,"ω"],[8487,2],[8488,1,"z"],[8489,2],[8490,1,"k"],[8491,1,"å"],[8492,1,"b"],[8493,1,"c"],[8494,2],[[8495,8496],1,"e"],[8497,1,"f"],[8498,1,"ⅎ"],[8499,1,"m"],[8500,1,"o"],[8501,1,"א"],[8502,1,"ב"],[8503,1,"ג"],[8504,1,"ד"],[8505,1,"i"],[8506,2],[8507,1,"fax"],[8508,1,"π"],[[8509,8510],1,"γ"],[8511,1,"π"],[8512,1,"∑"],[[8513,8516],2],[[8517,8518],1,"d"],[8519,1,"e"],[8520,1,"i"],[8521,1,"j"],[[8522,8523],2],[8524,2],[8525,2],[8526,2],[8527,2],[8528,1,"1⁄7"],[8529,1,"1⁄9"],[8530,1,"1⁄10"],[8531,1,"1⁄3"],[8532,1,"2⁄3"],[8533,1,"1⁄5"],[8534,1,"2⁄5"],[8535,1,"3⁄5"],[8536,1,"4⁄5"],[8537,1,"1⁄6"],[8538,1,"5⁄6"],[8539,1,"1⁄8"],[8540,1,"3⁄8"],[8541,1,"5⁄8"],[8542,1,"7⁄8"],[8543,1,"1⁄"],[8544,1,"i"],[8545,1,"ii"],[8546,1,"iii"],[8547,1,"iv"],[8548,1,"v"],[8549,1,"vi"],[8550,1,"vii"],[8551,1,"viii"],[8552,1,"ix"],[8553,1,"x"],[8554,1,"xi"],[8555,1,"xii"],[8556,1,"l"],[8557,1,"c"],[8558,1,"d"],[8559,1,"m"],[8560,1,"i"],[8561,1,"ii"],[8562,1,"iii"],[8563,1,"iv"],[8564,1,"v"],[8565,1,"vi"],[8566,1,"vii"],[8567,1,"viii"],[8568,1,"ix"],[8569,1,"x"],[8570,1,"xi"],[8571,1,"xii"],[8572,1,"l"],[8573,1,"c"],[8574,1,"d"],[8575,1,"m"],[[8576,8578],2],[8579,1,"ↄ"],[8580,2],[[8581,8584],2],[8585,1,"0⁄3"],[[8586,8587],2],[[8588,8591],3],[[8592,8682],2],[[8683,8691],2],[[8692,8703],2],[[8704,8747],2],[8748,1,"∫∫"],[8749,1,"∫∫∫"],[8750,2],[8751,1,"∮∮"],[8752,1,"∮∮∮"],[[8753,8945],2],[[8946,8959],2],[8960,2],[8961,2],[[8962,9000],2],[9001,1,"〈"],[9002,1,"〉"],[[9003,9082],2],[9083,2],[9084,2],[[9085,9114],2],[[9115,9166],2],[[9167,9168],2],[[9169,9179],2],[[9180,9191],2],[9192,2],[[9193,9203],2],[[9204,9210],2],[[9211,9214],2],[9215,2],[[9216,9252],2],[[9253,9254],2],[[9255,9257],2],[[9258,9279],3],[[9280,9290],2],[[9291,9311],3],[9312,1,"1"],[9313,1,"2"],[9314,1,"3"],[9315,1,"4"],[9316,1,"5"],[9317,1,"6"],[9318,1,"7"],[9319,1,"8"],[9320,1,"9"],[9321,1,"10"],[9322,1,"11"],[9323,1,"12"],[9324,1,"13"],[9325,1,"14"],[9326,1,"15"],[9327,1,"16"],[9328,1,"17"],[9329,1,"18"],[9330,1,"19"],[9331,1,"20"],[9332,1,"(1)"],[9333,1,"(2)"],[9334,1,"(3)"],[9335,1,"(4)"],[9336,1,"(5)"],[9337,1,"(6)"],[9338,1,"(7)"],[9339,1,"(8)"],[9340,1,"(9)"],[9341,1,"(10)"],[9342,1,"(11)"],[9343,1,"(12)"],[9344,1,"(13)"],[9345,1,"(14)"],[9346,1,"(15)"],[9347,1,"(16)"],[9348,1,"(17)"],[9349,1,"(18)"],[9350,1,"(19)"],[9351,1,"(20)"],[[9352,9371],3],[9372,1,"(a)"],[9373,1,"(b)"],[9374,1,"(c)"],[9375,1,"(d)"],[9376,1,"(e)"],[9377,1,"(f)"],[9378,1,"(g)"],[9379,1,"(h)"],[9380,1,"(i)"],[9381,1,"(j)"],[9382,1,"(k)"],[9383,1,"(l)"],[9384,1,"(m)"],[9385,1,"(n)"],[9386,1,"(o)"],[9387,1,"(p)"],[9388,1,"(q)"],[9389,1,"(r)"],[9390,1,"(s)"],[9391,1,"(t)"],[9392,1,"(u)"],[9393,1,"(v)"],[9394,1,"(w)"],[9395,1,"(x)"],[9396,1,"(y)"],[9397,1,"(z)"],[9398,1,"a"],[9399,1,"b"],[9400,1,"c"],[9401,1,"d"],[9402,1,"e"],[9403,1,"f"],[9404,1,"g"],[9405,1,"h"],[9406,1,"i"],[9407,1,"j"],[9408,1,"k"],[9409,1,"l"],[9410,1,"m"],[9411,1,"n"],[9412,1,"o"],[9413,1,"p"],[9414,1,"q"],[9415,1,"r"],[9416,1,"s"],[9417,1,"t"],[9418,1,"u"],[9419,1,"v"],[9420,1,"w"],[9421,1,"x"],[9422,1,"y"],[9423,1,"z"],[9424,1,"a"],[9425,1,"b"],[9426,1,"c"],[9427,1,"d"],[9428,1,"e"],[9429,1,"f"],[9430,1,"g"],[9431,1,"h"],[9432,1,"i"],[9433,1,"j"],[9434,1,"k"],[9435,1,"l"],[9436,1,"m"],[9437,1,"n"],[9438,1,"o"],[9439,1,"p"],[9440,1,"q"],[9441,1,"r"],[9442,1,"s"],[9443,1,"t"],[9444,1,"u"],[9445,1,"v"],[9446,1,"w"],[9447,1,"x"],[9448,1,"y"],[9449,1,"z"],[9450,1,"0"],[[9451,9470],2],[9471,2],[[9472,9621],2],[[9622,9631],2],[[9632,9711],2],[[9712,9719],2],[[9720,9727],2],[[9728,9747],2],[[9748,9749],2],[[9750,9751],2],[9752,2],[9753,2],[[9754,9839],2],[[9840,9841],2],[[9842,9853],2],[[9854,9855],2],[[9856,9865],2],[[9866,9873],2],[[9874,9884],2],[9885,2],[[9886,9887],2],[[9888,9889],2],[[9890,9905],2],[9906,2],[[9907,9916],2],[[9917,9919],2],[[9920,9923],2],[[9924,9933],2],[9934,2],[[9935,9953],2],[9954,2],[9955,2],[[9956,9959],2],[[9960,9983],2],[9984,2],[[9985,9988],2],[9989,2],[[9990,9993],2],[[9994,9995],2],[[9996,10023],2],[10024,2],[[10025,10059],2],[10060,2],[10061,2],[10062,2],[[10063,10066],2],[[10067,10069],2],[10070,2],[10071,2],[[10072,10078],2],[[10079,10080],2],[[10081,10087],2],[[10088,10101],2],[[10102,10132],2],[[10133,10135],2],[[10136,10159],2],[10160,2],[[10161,10174],2],[10175,2],[[10176,10182],2],[[10183,10186],2],[10187,2],[10188,2],[10189,2],[[10190,10191],2],[[10192,10219],2],[[10220,10223],2],[[10224,10239],2],[[10240,10495],2],[[10496,10763],2],[10764,1,"∫∫∫∫"],[[10765,10867],2],[10868,1,"::="],[10869,1,"=="],[10870,1,"==="],[[10871,10971],2],[10972,1,"⫝̸"],[[10973,11007],2],[[11008,11021],2],[[11022,11027],2],[[11028,11034],2],[[11035,11039],2],[[11040,11043],2],[[11044,11084],2],[[11085,11087],2],[[11088,11092],2],[[11093,11097],2],[[11098,11123],2],[[11124,11125],3],[[11126,11157],2],[11158,3],[11159,2],[[11160,11193],2],[[11194,11196],2],[[11197,11208],2],[11209,2],[[11210,11217],2],[11218,2],[[11219,11243],2],[[11244,11247],2],[[11248,11262],2],[11263,2],[11264,1,"ⰰ"],[11265,1,"ⰱ"],[11266,1,"ⰲ"],[11267,1,"ⰳ"],[11268,1,"ⰴ"],[11269,1,"ⰵ"],[11270,1,"ⰶ"],[11271,1,"ⰷ"],[11272,1,"ⰸ"],[11273,1,"ⰹ"],[11274,1,"ⰺ"],[11275,1,"ⰻ"],[11276,1,"ⰼ"],[11277,1,"ⰽ"],[11278,1,"ⰾ"],[11279,1,"ⰿ"],[11280,1,"ⱀ"],[11281,1,"ⱁ"],[11282,1,"ⱂ"],[11283,1,"ⱃ"],[11284,1,"ⱄ"],[11285,1,"ⱅ"],[11286,1,"ⱆ"],[11287,1,"ⱇ"],[11288,1,"ⱈ"],[11289,1,"ⱉ"],[11290,1,"ⱊ"],[11291,1,"ⱋ"],[11292,1,"ⱌ"],[11293,1,"ⱍ"],[11294,1,"ⱎ"],[11295,1,"ⱏ"],[11296,1,"ⱐ"],[11297,1,"ⱑ"],[11298,1,"ⱒ"],[11299,1,"ⱓ"],[11300,1,"ⱔ"],[11301,1,"ⱕ"],[11302,1,"ⱖ"],[11303,1,"ⱗ"],[11304,1,"ⱘ"],[11305,1,"ⱙ"],[11306,1,"ⱚ"],[11307,1,"ⱛ"],[11308,1,"ⱜ"],[11309,1,"ⱝ"],[11310,1,"ⱞ"],[11311,1,"ⱟ"],[[11312,11358],2],[11359,2],[11360,1,"ⱡ"],[11361,2],[11362,1,"ɫ"],[11363,1,"ᵽ"],[11364,1,"ɽ"],[[11365,11366],2],[11367,1,"ⱨ"],[11368,2],[11369,1,"ⱪ"],[11370,2],[11371,1,"ⱬ"],[11372,2],[11373,1,"ɑ"],[11374,1,"ɱ"],[11375,1,"ɐ"],[11376,1,"ɒ"],[11377,2],[11378,1,"ⱳ"],[11379,2],[11380,2],[11381,1,"ⱶ"],[[11382,11383],2],[[11384,11387],2],[11388,1,"j"],[11389,1,"v"],[11390,1,"ȿ"],[11391,1,"ɀ"],[11392,1,"ⲁ"],[11393,2],[11394,1,"ⲃ"],[11395,2],[11396,1,"ⲅ"],[11397,2],[11398,1,"ⲇ"],[11399,2],[11400,1,"ⲉ"],[11401,2],[11402,1,"ⲋ"],[11403,2],[11404,1,"ⲍ"],[11405,2],[11406,1,"ⲏ"],[11407,2],[11408,1,"ⲑ"],[11409,2],[11410,1,"ⲓ"],[11411,2],[11412,1,"ⲕ"],[11413,2],[11414,1,"ⲗ"],[11415,2],[11416,1,"ⲙ"],[11417,2],[11418,1,"ⲛ"],[11419,2],[11420,1,"ⲝ"],[11421,2],[11422,1,"ⲟ"],[11423,2],[11424,1,"ⲡ"],[11425,2],[11426,1,"ⲣ"],[11427,2],[11428,1,"ⲥ"],[11429,2],[11430,1,"ⲧ"],[11431,2],[11432,1,"ⲩ"],[11433,2],[11434,1,"ⲫ"],[11435,2],[11436,1,"ⲭ"],[11437,2],[11438,1,"ⲯ"],[11439,2],[11440,1,"ⲱ"],[11441,2],[11442,1,"ⲳ"],[11443,2],[11444,1,"ⲵ"],[11445,2],[11446,1,"ⲷ"],[11447,2],[11448,1,"ⲹ"],[11449,2],[11450,1,"ⲻ"],[11451,2],[11452,1,"ⲽ"],[11453,2],[11454,1,"ⲿ"],[11455,2],[11456,1,"ⳁ"],[11457,2],[11458,1,"ⳃ"],[11459,2],[11460,1,"ⳅ"],[11461,2],[11462,1,"ⳇ"],[11463,2],[11464,1,"ⳉ"],[11465,2],[11466,1,"ⳋ"],[11467,2],[11468,1,"ⳍ"],[11469,2],[11470,1,"ⳏ"],[11471,2],[11472,1,"ⳑ"],[11473,2],[11474,1,"ⳓ"],[11475,2],[11476,1,"ⳕ"],[11477,2],[11478,1,"ⳗ"],[11479,2],[11480,1,"ⳙ"],[11481,2],[11482,1,"ⳛ"],[11483,2],[11484,1,"ⳝ"],[11485,2],[11486,1,"ⳟ"],[11487,2],[11488,1,"ⳡ"],[11489,2],[11490,1,"ⳣ"],[[11491,11492],2],[[11493,11498],2],[11499,1,"ⳬ"],[11500,2],[11501,1,"ⳮ"],[[11502,11505],2],[11506,1,"ⳳ"],[11507,2],[[11508,11512],3],[[11513,11519],2],[[11520,11557],2],[11558,3],[11559,2],[[11560,11564],3],[11565,2],[[11566,11567],3],[[11568,11621],2],[[11622,11623],2],[[11624,11630],3],[11631,1,"ⵡ"],[11632,2],[[11633,11646],3],[11647,2],[[11648,11670],2],[[11671,11679],3],[[11680,11686],2],[11687,3],[[11688,11694],2],[11695,3],[[11696,11702],2],[11703,3],[[11704,11710],2],[11711,3],[[11712,11718],2],[11719,3],[[11720,11726],2],[11727,3],[[11728,11734],2],[11735,3],[[11736,11742],2],[11743,3],[[11744,11775],2],[[11776,11799],2],[[11800,11803],2],[[11804,11805],2],[[11806,11822],2],[11823,2],[11824,2],[11825,2],[[11826,11835],2],[[11836,11842],2],[[11843,11844],2],[[11845,11849],2],[[11850,11854],2],[11855,2],[[11856,11858],2],[[11859,11869],2],[[11870,11903],3],[[11904,11929],2],[11930,3],[[11931,11934],2],[11935,1,"母"],[[11936,12018],2],[12019,1,"龟"],[[12020,12031],3],[12032,1,"一"],[12033,1,"丨"],[12034,1,"丶"],[12035,1,"丿"],[12036,1,"乙"],[12037,1,"亅"],[12038,1,"二"],[12039,1,"亠"],[12040,1,"人"],[12041,1,"儿"],[12042,1,"入"],[12043,1,"八"],[12044,1,"冂"],[12045,1,"冖"],[12046,1,"冫"],[12047,1,"几"],[12048,1,"凵"],[12049,1,"刀"],[12050,1,"力"],[12051,1,"勹"],[12052,1,"匕"],[12053,1,"匚"],[12054,1,"匸"],[12055,1,"十"],[12056,1,"卜"],[12057,1,"卩"],[12058,1,"厂"],[12059,1,"厶"],[12060,1,"又"],[12061,1,"口"],[12062,1,"囗"],[12063,1,"土"],[12064,1,"士"],[12065,1,"夂"],[12066,1,"夊"],[12067,1,"夕"],[12068,1,"大"],[12069,1,"女"],[12070,1,"子"],[12071,1,"宀"],[12072,1,"寸"],[12073,1,"小"],[12074,1,"尢"],[12075,1,"尸"],[12076,1,"屮"],[12077,1,"山"],[12078,1,"巛"],[12079,1,"工"],[12080,1,"己"],[12081,1,"巾"],[12082,1,"干"],[12083,1,"幺"],[12084,1,"广"],[12085,1,"廴"],[12086,1,"廾"],[12087,1,"弋"],[12088,1,"弓"],[12089,1,"彐"],[12090,1,"彡"],[12091,1,"彳"],[12092,1,"心"],[12093,1,"戈"],[12094,1,"戶"],[12095,1,"手"],[12096,1,"支"],[12097,1,"攴"],[12098,1,"文"],[12099,1,"斗"],[12100,1,"斤"],[12101,1,"方"],[12102,1,"无"],[12103,1,"日"],[12104,1,"曰"],[12105,1,"月"],[12106,1,"木"],[12107,1,"欠"],[12108,1,"止"],[12109,1,"歹"],[12110,1,"殳"],[12111,1,"毋"],[12112,1,"比"],[12113,1,"毛"],[12114,1,"氏"],[12115,1,"气"],[12116,1,"水"],[12117,1,"火"],[12118,1,"爪"],[12119,1,"父"],[12120,1,"爻"],[12121,1,"爿"],[12122,1,"片"],[12123,1,"牙"],[12124,1,"牛"],[12125,1,"犬"],[12126,1,"玄"],[12127,1,"玉"],[12128,1,"瓜"],[12129,1,"瓦"],[12130,1,"甘"],[12131,1,"生"],[12132,1,"用"],[12133,1,"田"],[12134,1,"疋"],[12135,1,"疒"],[12136,1,"癶"],[12137,1,"白"],[12138,1,"皮"],[12139,1,"皿"],[12140,1,"目"],[12141,1,"矛"],[12142,1,"矢"],[12143,1,"石"],[12144,1,"示"],[12145,1,"禸"],[12146,1,"禾"],[12147,1,"穴"],[12148,1,"立"],[12149,1,"竹"],[12150,1,"米"],[12151,1,"糸"],[12152,1,"缶"],[12153,1,"网"],[12154,1,"羊"],[12155,1,"羽"],[12156,1,"老"],[12157,1,"而"],[12158,1,"耒"],[12159,1,"耳"],[12160,1,"聿"],[12161,1,"肉"],[12162,1,"臣"],[12163,1,"自"],[12164,1,"至"],[12165,1,"臼"],[12166,1,"舌"],[12167,1,"舛"],[12168,1,"舟"],[12169,1,"艮"],[12170,1,"色"],[12171,1,"艸"],[12172,1,"虍"],[12173,1,"虫"],[12174,1,"血"],[12175,1,"行"],[12176,1,"衣"],[12177,1,"襾"],[12178,1,"見"],[12179,1,"角"],[12180,1,"言"],[12181,1,"谷"],[12182,1,"豆"],[12183,1,"豕"],[12184,1,"豸"],[12185,1,"貝"],[12186,1,"赤"],[12187,1,"走"],[12188,1,"足"],[12189,1,"身"],[12190,1,"車"],[12191,1,"辛"],[12192,1,"辰"],[12193,1,"辵"],[12194,1,"邑"],[12195,1,"酉"],[12196,1,"釆"],[12197,1,"里"],[12198,1,"金"],[12199,1,"長"],[12200,1,"門"],[12201,1,"阜"],[12202,1,"隶"],[12203,1,"隹"],[12204,1,"雨"],[12205,1,"靑"],[12206,1,"非"],[12207,1,"面"],[12208,1,"革"],[12209,1,"韋"],[12210,1,"韭"],[12211,1,"音"],[12212,1,"頁"],[12213,1,"風"],[12214,1,"飛"],[12215,1,"食"],[12216,1,"首"],[12217,1,"香"],[12218,1,"馬"],[12219,1,"骨"],[12220,1,"高"],[12221,1,"髟"],[12222,1,"鬥"],[12223,1,"鬯"],[12224,1,"鬲"],[12225,1,"鬼"],[12226,1,"魚"],[12227,1,"鳥"],[12228,1,"鹵"],[12229,1,"鹿"],[12230,1,"麥"],[12231,1,"麻"],[12232,1,"黃"],[12233,1,"黍"],[12234,1,"黑"],[12235,1,"黹"],[12236,1,"黽"],[12237,1,"鼎"],[12238,1,"鼓"],[12239,1,"鼠"],[12240,1,"鼻"],[12241,1,"齊"],[12242,1,"齒"],[12243,1,"龍"],[12244,1,"龜"],[12245,1,"龠"],[[12246,12271],3],[[12272,12283],3],[[12284,12287],3],[12288,1," "],[12289,2],[12290,1,"."],[[12291,12292],2],[[12293,12295],2],[[12296,12329],2],[[12330,12333],2],[[12334,12341],2],[12342,1,"〒"],[12343,2],[12344,1,"十"],[12345,1,"卄"],[12346,1,"卅"],[12347,2],[12348,2],[12349,2],[12350,2],[12351,2],[12352,3],[[12353,12436],2],[[12437,12438],2],[[12439,12440],3],[[12441,12442],2],[12443,1," ゙"],[12444,1," ゚"],[[12445,12446],2],[12447,1,"より"],[12448,2],[[12449,12542],2],[12543,1,"コト"],[[12544,12548],3],[[12549,12588],2],[12589,2],[12590,2],[12591,2],[12592,3],[12593,1,"ᄀ"],[12594,1,"ᄁ"],[12595,1,"ᆪ"],[12596,1,"ᄂ"],[12597,1,"ᆬ"],[12598,1,"ᆭ"],[12599,1,"ᄃ"],[12600,1,"ᄄ"],[12601,1,"ᄅ"],[12602,1,"ᆰ"],[12603,1,"ᆱ"],[12604,1,"ᆲ"],[12605,1,"ᆳ"],[12606,1,"ᆴ"],[12607,1,"ᆵ"],[12608,1,"ᄚ"],[12609,1,"ᄆ"],[12610,1,"ᄇ"],[12611,1,"ᄈ"],[12612,1,"ᄡ"],[12613,1,"ᄉ"],[12614,1,"ᄊ"],[12615,1,"ᄋ"],[12616,1,"ᄌ"],[12617,1,"ᄍ"],[12618,1,"ᄎ"],[12619,1,"ᄏ"],[12620,1,"ᄐ"],[12621,1,"ᄑ"],[12622,1,"ᄒ"],[12623,1,"ᅡ"],[12624,1,"ᅢ"],[12625,1,"ᅣ"],[12626,1,"ᅤ"],[12627,1,"ᅥ"],[12628,1,"ᅦ"],[12629,1,"ᅧ"],[12630,1,"ᅨ"],[12631,1,"ᅩ"],[12632,1,"ᅪ"],[12633,1,"ᅫ"],[12634,1,"ᅬ"],[12635,1,"ᅭ"],[12636,1,"ᅮ"],[12637,1,"ᅯ"],[12638,1,"ᅰ"],[12639,1,"ᅱ"],[12640,1,"ᅲ"],[12641,1,"ᅳ"],[12642,1,"ᅴ"],[12643,1,"ᅵ"],[12644,7],[12645,1,"ᄔ"],[12646,1,"ᄕ"],[12647,1,"ᇇ"],[12648,1,"ᇈ"],[12649,1,"ᇌ"],[12650,1,"ᇎ"],[12651,1,"ᇓ"],[12652,1,"ᇗ"],[12653,1,"ᇙ"],[12654,1,"ᄜ"],[12655,1,"ᇝ"],[12656,1,"ᇟ"],[12657,1,"ᄝ"],[12658,1,"ᄞ"],[12659,1,"ᄠ"],[12660,1,"ᄢ"],[12661,1,"ᄣ"],[12662,1,"ᄧ"],[12663,1,"ᄩ"],[12664,1,"ᄫ"],[12665,1,"ᄬ"],[12666,1,"ᄭ"],[12667,1,"ᄮ"],[12668,1,"ᄯ"],[12669,1,"ᄲ"],[12670,1,"ᄶ"],[12671,1,"ᅀ"],[12672,1,"ᅇ"],[12673,1,"ᅌ"],[12674,1,"ᇱ"],[12675,1,"ᇲ"],[12676,1,"ᅗ"],[12677,1,"ᅘ"],[12678,1,"ᅙ"],[12679,1,"ᆄ"],[12680,1,"ᆅ"],[12681,1,"ᆈ"],[12682,1,"ᆑ"],[12683,1,"ᆒ"],[12684,1,"ᆔ"],[12685,1,"ᆞ"],[12686,1,"ᆡ"],[12687,3],[[12688,12689],2],[12690,1,"一"],[12691,1,"二"],[12692,1,"三"],[12693,1,"四"],[12694,1,"上"],[12695,1,"中"],[12696,1,"下"],[12697,1,"甲"],[12698,1,"乙"],[12699,1,"丙"],[12700,1,"丁"],[12701,1,"天"],[12702,1,"地"],[12703,1,"人"],[[12704,12727],2],[[12728,12730],2],[[12731,12735],2],[[12736,12751],2],[[12752,12771],2],[[12772,12773],2],[[12774,12782],3],[12783,3],[[12784,12799],2],[12800,1,"(ᄀ)"],[12801,1,"(ᄂ)"],[12802,1,"(ᄃ)"],[12803,1,"(ᄅ)"],[12804,1,"(ᄆ)"],[12805,1,"(ᄇ)"],[12806,1,"(ᄉ)"],[12807,1,"(ᄋ)"],[12808,1,"(ᄌ)"],[12809,1,"(ᄎ)"],[12810,1,"(ᄏ)"],[12811,1,"(ᄐ)"],[12812,1,"(ᄑ)"],[12813,1,"(ᄒ)"],[12814,1,"(가)"],[12815,1,"(나)"],[12816,1,"(다)"],[12817,1,"(라)"],[12818,1,"(마)"],[12819,1,"(바)"],[12820,1,"(사)"],[12821,1,"(아)"],[12822,1,"(자)"],[12823,1,"(차)"],[12824,1,"(카)"],[12825,1,"(타)"],[12826,1,"(파)"],[12827,1,"(하)"],[12828,1,"(주)"],[12829,1,"(오전)"],[12830,1,"(오후)"],[12831,3],[12832,1,"(一)"],[12833,1,"(二)"],[12834,1,"(三)"],[12835,1,"(四)"],[12836,1,"(五)"],[12837,1,"(六)"],[12838,1,"(七)"],[12839,1,"(八)"],[12840,1,"(九)"],[12841,1,"(十)"],[12842,1,"(月)"],[12843,1,"(火)"],[12844,1,"(水)"],[12845,1,"(木)"],[12846,1,"(金)"],[12847,1,"(土)"],[12848,1,"(日)"],[12849,1,"(株)"],[12850,1,"(有)"],[12851,1,"(社)"],[12852,1,"(名)"],[12853,1,"(特)"],[12854,1,"(財)"],[12855,1,"(祝)"],[12856,1,"(労)"],[12857,1,"(代)"],[12858,1,"(呼)"],[12859,1,"(学)"],[12860,1,"(監)"],[12861,1,"(企)"],[12862,1,"(資)"],[12863,1,"(協)"],[12864,1,"(祭)"],[12865,1,"(休)"],[12866,1,"(自)"],[12867,1,"(至)"],[12868,1,"問"],[12869,1,"幼"],[12870,1,"文"],[12871,1,"箏"],[[12872,12879],2],[12880,1,"pte"],[12881,1,"21"],[12882,1,"22"],[12883,1,"23"],[12884,1,"24"],[12885,1,"25"],[12886,1,"26"],[12887,1,"27"],[12888,1,"28"],[12889,1,"29"],[12890,1,"30"],[12891,1,"31"],[12892,1,"32"],[12893,1,"33"],[12894,1,"34"],[12895,1,"35"],[12896,1,"ᄀ"],[12897,1,"ᄂ"],[12898,1,"ᄃ"],[12899,1,"ᄅ"],[12900,1,"ᄆ"],[12901,1,"ᄇ"],[12902,1,"ᄉ"],[12903,1,"ᄋ"],[12904,1,"ᄌ"],[12905,1,"ᄎ"],[12906,1,"ᄏ"],[12907,1,"ᄐ"],[12908,1,"ᄑ"],[12909,1,"ᄒ"],[12910,1,"가"],[12911,1,"나"],[12912,1,"다"],[12913,1,"라"],[12914,1,"마"],[12915,1,"바"],[12916,1,"사"],[12917,1,"아"],[12918,1,"자"],[12919,1,"차"],[12920,1,"카"],[12921,1,"타"],[12922,1,"파"],[12923,1,"하"],[12924,1,"참고"],[12925,1,"주의"],[12926,1,"우"],[12927,2],[12928,1,"一"],[12929,1,"二"],[12930,1,"三"],[12931,1,"四"],[12932,1,"五"],[12933,1,"六"],[12934,1,"七"],[12935,1,"八"],[12936,1,"九"],[12937,1,"十"],[12938,1,"月"],[12939,1,"火"],[12940,1,"水"],[12941,1,"木"],[12942,1,"金"],[12943,1,"土"],[12944,1,"日"],[12945,1,"株"],[12946,1,"有"],[12947,1,"社"],[12948,1,"名"],[12949,1,"特"],[12950,1,"財"],[12951,1,"祝"],[12952,1,"労"],[12953,1,"秘"],[12954,1,"男"],[12955,1,"女"],[12956,1,"適"],[12957,1,"優"],[12958,1,"印"],[12959,1,"注"],[12960,1,"項"],[12961,1,"休"],[12962,1,"写"],[12963,1,"正"],[12964,1,"上"],[12965,1,"中"],[12966,1,"下"],[12967,1,"左"],[12968,1,"右"],[12969,1,"医"],[12970,1,"宗"],[12971,1,"学"],[12972,1,"監"],[12973,1,"企"],[12974,1,"資"],[12975,1,"協"],[12976,1,"夜"],[12977,1,"36"],[12978,1,"37"],[12979,1,"38"],[12980,1,"39"],[12981,1,"40"],[12982,1,"41"],[12983,1,"42"],[12984,1,"43"],[12985,1,"44"],[12986,1,"45"],[12987,1,"46"],[12988,1,"47"],[12989,1,"48"],[12990,1,"49"],[12991,1,"50"],[12992,1,"1月"],[12993,1,"2月"],[12994,1,"3月"],[12995,1,"4月"],[12996,1,"5月"],[12997,1,"6月"],[12998,1,"7月"],[12999,1,"8月"],[13000,1,"9月"],[13001,1,"10月"],[13002,1,"11月"],[13003,1,"12月"],[13004,1,"hg"],[13005,1,"erg"],[13006,1,"ev"],[13007,1,"ltd"],[13008,1,"ア"],[13009,1,"イ"],[13010,1,"ウ"],[13011,1,"エ"],[13012,1,"オ"],[13013,1,"カ"],[13014,1,"キ"],[13015,1,"ク"],[13016,1,"ケ"],[13017,1,"コ"],[13018,1,"サ"],[13019,1,"シ"],[13020,1,"ス"],[13021,1,"セ"],[13022,1,"ソ"],[13023,1,"タ"],[13024,1,"チ"],[13025,1,"ツ"],[13026,1,"テ"],[13027,1,"ト"],[13028,1,"ナ"],[13029,1,"ニ"],[13030,1,"ヌ"],[13031,1,"ネ"],[13032,1,"ノ"],[13033,1,"ハ"],[13034,1,"ヒ"],[13035,1,"フ"],[13036,1,"ヘ"],[13037,1,"ホ"],[13038,1,"マ"],[13039,1,"ミ"],[13040,1,"ム"],[13041,1,"メ"],[13042,1,"モ"],[13043,1,"ヤ"],[13044,1,"ユ"],[13045,1,"ヨ"],[13046,1,"ラ"],[13047,1,"リ"],[13048,1,"ル"],[13049,1,"レ"],[13050,1,"ロ"],[13051,1,"ワ"],[13052,1,"ヰ"],[13053,1,"ヱ"],[13054,1,"ヲ"],[13055,1,"令和"],[13056,1,"アパート"],[13057,1,"アルファ"],[13058,1,"アンペア"],[13059,1,"アール"],[13060,1,"イニング"],[13061,1,"インチ"],[13062,1,"ウォン"],[13063,1,"エスクード"],[13064,1,"エーカー"],[13065,1,"オンス"],[13066,1,"オーム"],[13067,1,"カイリ"],[13068,1,"カラット"],[13069,1,"カロリー"],[13070,1,"ガロン"],[13071,1,"ガンマ"],[13072,1,"ギガ"],[13073,1,"ギニー"],[13074,1,"キュリー"],[13075,1,"ギルダー"],[13076,1,"キロ"],[13077,1,"キログラム"],[13078,1,"キロメートル"],[13079,1,"キロワット"],[13080,1,"グラム"],[13081,1,"グラムトン"],[13082,1,"クルゼイロ"],[13083,1,"クローネ"],[13084,1,"ケース"],[13085,1,"コルナ"],[13086,1,"コーポ"],[13087,1,"サイクル"],[13088,1,"サンチーム"],[13089,1,"シリング"],[13090,1,"センチ"],[13091,1,"セント"],[13092,1,"ダース"],[13093,1,"デシ"],[13094,1,"ドル"],[13095,1,"トン"],[13096,1,"ナノ"],[13097,1,"ノット"],[13098,1,"ハイツ"],[13099,1,"パーセント"],[13100,1,"パーツ"],[13101,1,"バーレル"],[13102,1,"ピアストル"],[13103,1,"ピクル"],[13104,1,"ピコ"],[13105,1,"ビル"],[13106,1,"ファラッド"],[13107,1,"フィート"],[13108,1,"ブッシェル"],[13109,1,"フラン"],[13110,1,"ヘクタール"],[13111,1,"ペソ"],[13112,1,"ペニヒ"],[13113,1,"ヘルツ"],[13114,1,"ペンス"],[13115,1,"ページ"],[13116,1,"ベータ"],[13117,1,"ポイント"],[13118,1,"ボルト"],[13119,1,"ホン"],[13120,1,"ポンド"],[13121,1,"ホール"],[13122,1,"ホーン"],[13123,1,"マイクロ"],[13124,1,"マイル"],[13125,1,"マッハ"],[13126,1,"マルク"],[13127,1,"マンション"],[13128,1,"ミクロン"],[13129,1,"ミリ"],[13130,1,"ミリバール"],[13131,1,"メガ"],[13132,1,"メガトン"],[13133,1,"メートル"],[13134,1,"ヤード"],[13135,1,"ヤール"],[13136,1,"ユアン"],[13137,1,"リットル"],[13138,1,"リラ"],[13139,1,"ルピー"],[13140,1,"ルーブル"],[13141,1,"レム"],[13142,1,"レントゲン"],[13143,1,"ワット"],[13144,1,"0点"],[13145,1,"1点"],[13146,1,"2点"],[13147,1,"3点"],[13148,1,"4点"],[13149,1,"5点"],[13150,1,"6点"],[13151,1,"7点"],[13152,1,"8点"],[13153,1,"9点"],[13154,1,"10点"],[13155,1,"11点"],[13156,1,"12点"],[13157,1,"13点"],[13158,1,"14点"],[13159,1,"15点"],[13160,1,"16点"],[13161,1,"17点"],[13162,1,"18点"],[13163,1,"19点"],[13164,1,"20点"],[13165,1,"21点"],[13166,1,"22点"],[13167,1,"23点"],[13168,1,"24点"],[13169,1,"hpa"],[13170,1,"da"],[13171,1,"au"],[13172,1,"bar"],[13173,1,"ov"],[13174,1,"pc"],[13175,1,"dm"],[13176,1,"dm2"],[13177,1,"dm3"],[13178,1,"iu"],[13179,1,"平成"],[13180,1,"昭和"],[13181,1,"大正"],[13182,1,"明治"],[13183,1,"株式会社"],[13184,1,"pa"],[13185,1,"na"],[13186,1,"μa"],[13187,1,"ma"],[13188,1,"ka"],[13189,1,"kb"],[13190,1,"mb"],[13191,1,"gb"],[13192,1,"cal"],[13193,1,"kcal"],[13194,1,"pf"],[13195,1,"nf"],[13196,1,"μf"],[13197,1,"μg"],[13198,1,"mg"],[13199,1,"kg"],[13200,1,"hz"],[13201,1,"khz"],[13202,1,"mhz"],[13203,1,"ghz"],[13204,1,"thz"],[13205,1,"μl"],[13206,1,"ml"],[13207,1,"dl"],[13208,1,"kl"],[13209,1,"fm"],[13210,1,"nm"],[13211,1,"μm"],[13212,1,"mm"],[13213,1,"cm"],[13214,1,"km"],[13215,1,"mm2"],[13216,1,"cm2"],[13217,1,"m2"],[13218,1,"km2"],[13219,1,"mm3"],[13220,1,"cm3"],[13221,1,"m3"],[13222,1,"km3"],[13223,1,"m∕s"],[13224,1,"m∕s2"],[13225,1,"pa"],[13226,1,"kpa"],[13227,1,"mpa"],[13228,1,"gpa"],[13229,1,"rad"],[13230,1,"rad∕s"],[13231,1,"rad∕s2"],[13232,1,"ps"],[13233,1,"ns"],[13234,1,"μs"],[13235,1,"ms"],[13236,1,"pv"],[13237,1,"nv"],[13238,1,"μv"],[13239,1,"mv"],[13240,1,"kv"],[13241,1,"mv"],[13242,1,"pw"],[13243,1,"nw"],[13244,1,"μw"],[13245,1,"mw"],[13246,1,"kw"],[13247,1,"mw"],[13248,1,"kω"],[13249,1,"mω"],[13250,3],[13251,1,"bq"],[13252,1,"cc"],[13253,1,"cd"],[13254,1,"c∕kg"],[13255,3],[13256,1,"db"],[13257,1,"gy"],[13258,1,"ha"],[13259,1,"hp"],[13260,1,"in"],[13261,1,"kk"],[13262,1,"km"],[13263,1,"kt"],[13264,1,"lm"],[13265,1,"ln"],[13266,1,"log"],[13267,1,"lx"],[13268,1,"mb"],[13269,1,"mil"],[13270,1,"mol"],[13271,1,"ph"],[13272,3],[13273,1,"ppm"],[13274,1,"pr"],[13275,1,"sr"],[13276,1,"sv"],[13277,1,"wb"],[13278,1,"v∕m"],[13279,1,"a∕m"],[13280,1,"1日"],[13281,1,"2日"],[13282,1,"3日"],[13283,1,"4日"],[13284,1,"5日"],[13285,1,"6日"],[13286,1,"7日"],[13287,1,"8日"],[13288,1,"9日"],[13289,1,"10日"],[13290,1,"11日"],[13291,1,"12日"],[13292,1,"13日"],[13293,1,"14日"],[13294,1,"15日"],[13295,1,"16日"],[13296,1,"17日"],[13297,1,"18日"],[13298,1,"19日"],[13299,1,"20日"],[13300,1,"21日"],[13301,1,"22日"],[13302,1,"23日"],[13303,1,"24日"],[13304,1,"25日"],[13305,1,"26日"],[13306,1,"27日"],[13307,1,"28日"],[13308,1,"29日"],[13309,1,"30日"],[13310,1,"31日"],[13311,1,"gal"],[[13312,19893],2],[[19894,19903],2],[[19904,19967],2],[[19968,40869],2],[[40870,40891],2],[[40892,40899],2],[[40900,40907],2],[40908,2],[[40909,40917],2],[[40918,40938],2],[[40939,40943],2],[[40944,40956],2],[[40957,40959],2],[[40960,42124],2],[[42125,42127],3],[[42128,42145],2],[[42146,42147],2],[[42148,42163],2],[42164,2],[[42165,42176],2],[42177,2],[[42178,42180],2],[42181,2],[42182,2],[[42183,42191],3],[[42192,42237],2],[[42238,42239],2],[[42240,42508],2],[[42509,42511],2],[[42512,42539],2],[[42540,42559],3],[42560,1,"ꙁ"],[42561,2],[42562,1,"ꙃ"],[42563,2],[42564,1,"ꙅ"],[42565,2],[42566,1,"ꙇ"],[42567,2],[42568,1,"ꙉ"],[42569,2],[42570,1,"ꙋ"],[42571,2],[42572,1,"ꙍ"],[42573,2],[42574,1,"ꙏ"],[42575,2],[42576,1,"ꙑ"],[42577,2],[42578,1,"ꙓ"],[42579,2],[42580,1,"ꙕ"],[42581,2],[42582,1,"ꙗ"],[42583,2],[42584,1,"ꙙ"],[42585,2],[42586,1,"ꙛ"],[42587,2],[42588,1,"ꙝ"],[42589,2],[42590,1,"ꙟ"],[42591,2],[42592,1,"ꙡ"],[42593,2],[42594,1,"ꙣ"],[42595,2],[42596,1,"ꙥ"],[42597,2],[42598,1,"ꙧ"],[42599,2],[42600,1,"ꙩ"],[42601,2],[42602,1,"ꙫ"],[42603,2],[42604,1,"ꙭ"],[[42605,42607],2],[[42608,42611],2],[[42612,42619],2],[[42620,42621],2],[42622,2],[42623,2],[42624,1,"ꚁ"],[42625,2],[42626,1,"ꚃ"],[42627,2],[42628,1,"ꚅ"],[42629,2],[42630,1,"ꚇ"],[42631,2],[42632,1,"ꚉ"],[42633,2],[42634,1,"ꚋ"],[42635,2],[42636,1,"ꚍ"],[42637,2],[42638,1,"ꚏ"],[42639,2],[42640,1,"ꚑ"],[42641,2],[42642,1,"ꚓ"],[42643,2],[42644,1,"ꚕ"],[42645,2],[42646,1,"ꚗ"],[42647,2],[42648,1,"ꚙ"],[42649,2],[42650,1,"ꚛ"],[42651,2],[42652,1,"ъ"],[42653,1,"ь"],[42654,2],[42655,2],[[42656,42725],2],[[42726,42735],2],[[42736,42737],2],[[42738,42743],2],[[42744,42751],3],[[42752,42774],2],[[42775,42778],2],[[42779,42783],2],[[42784,42785],2],[42786,1,"ꜣ"],[42787,2],[42788,1,"ꜥ"],[42789,2],[42790,1,"ꜧ"],[42791,2],[42792,1,"ꜩ"],[42793,2],[42794,1,"ꜫ"],[42795,2],[42796,1,"ꜭ"],[42797,2],[42798,1,"ꜯ"],[[42799,42801],2],[42802,1,"ꜳ"],[42803,2],[42804,1,"ꜵ"],[42805,2],[42806,1,"ꜷ"],[42807,2],[42808,1,"ꜹ"],[42809,2],[42810,1,"ꜻ"],[42811,2],[42812,1,"ꜽ"],[42813,2],[42814,1,"ꜿ"],[42815,2],[42816,1,"ꝁ"],[42817,2],[42818,1,"ꝃ"],[42819,2],[42820,1,"ꝅ"],[42821,2],[42822,1,"ꝇ"],[42823,2],[42824,1,"ꝉ"],[42825,2],[42826,1,"ꝋ"],[42827,2],[42828,1,"ꝍ"],[42829,2],[42830,1,"ꝏ"],[42831,2],[42832,1,"ꝑ"],[42833,2],[42834,1,"ꝓ"],[42835,2],[42836,1,"ꝕ"],[42837,2],[42838,1,"ꝗ"],[42839,2],[42840,1,"ꝙ"],[42841,2],[42842,1,"ꝛ"],[42843,2],[42844,1,"ꝝ"],[42845,2],[42846,1,"ꝟ"],[42847,2],[42848,1,"ꝡ"],[42849,2],[42850,1,"ꝣ"],[42851,2],[42852,1,"ꝥ"],[42853,2],[42854,1,"ꝧ"],[42855,2],[42856,1,"ꝩ"],[42857,2],[42858,1,"ꝫ"],[42859,2],[42860,1,"ꝭ"],[42861,2],[42862,1,"ꝯ"],[42863,2],[42864,1,"ꝯ"],[[42865,42872],2],[42873,1,"ꝺ"],[42874,2],[42875,1,"ꝼ"],[42876,2],[42877,1,"ᵹ"],[42878,1,"ꝿ"],[42879,2],[42880,1,"ꞁ"],[42881,2],[42882,1,"ꞃ"],[42883,2],[42884,1,"ꞅ"],[42885,2],[42886,1,"ꞇ"],[[42887,42888],2],[[42889,42890],2],[42891,1,"ꞌ"],[42892,2],[42893,1,"ɥ"],[42894,2],[42895,2],[42896,1,"ꞑ"],[42897,2],[42898,1,"ꞓ"],[42899,2],[[42900,42901],2],[42902,1,"ꞗ"],[42903,2],[42904,1,"ꞙ"],[42905,2],[42906,1,"ꞛ"],[42907,2],[42908,1,"ꞝ"],[42909,2],[42910,1,"ꞟ"],[42911,2],[42912,1,"ꞡ"],[42913,2],[42914,1,"ꞣ"],[42915,2],[42916,1,"ꞥ"],[42917,2],[42918,1,"ꞧ"],[42919,2],[42920,1,"ꞩ"],[42921,2],[42922,1,"ɦ"],[42923,1,"ɜ"],[42924,1,"ɡ"],[42925,1,"ɬ"],[42926,1,"ɪ"],[42927,2],[42928,1,"ʞ"],[42929,1,"ʇ"],[42930,1,"ʝ"],[42931,1,"ꭓ"],[42932,1,"ꞵ"],[42933,2],[42934,1,"ꞷ"],[42935,2],[42936,1,"ꞹ"],[42937,2],[42938,1,"ꞻ"],[42939,2],[42940,1,"ꞽ"],[42941,2],[42942,1,"ꞿ"],[42943,2],[42944,1,"ꟁ"],[42945,2],[42946,1,"ꟃ"],[42947,2],[42948,1,"ꞔ"],[42949,1,"ʂ"],[42950,1,"ᶎ"],[42951,1,"ꟈ"],[42952,2],[42953,1,"ꟊ"],[42954,2],[42955,1,"ɤ"],[42956,1,"ꟍ"],[42957,2],[[42958,42959],3],[42960,1,"ꟑ"],[42961,2],[42962,3],[42963,2],[42964,3],[42965,2],[42966,1,"ꟗ"],[42967,2],[42968,1,"ꟙ"],[42969,2],[42970,1,"ꟛ"],[42971,2],[42972,1,"ƛ"],[[42973,42993],3],[42994,1,"c"],[42995,1,"f"],[42996,1,"q"],[42997,1,"ꟶ"],[42998,2],[42999,2],[43000,1,"ħ"],[43001,1,"œ"],[43002,2],[[43003,43007],2],[[43008,43047],2],[[43048,43051],2],[43052,2],[[43053,43055],3],[[43056,43065],2],[[43066,43071],3],[[43072,43123],2],[[43124,43127],2],[[43128,43135],3],[[43136,43204],2],[43205,2],[[43206,43213],3],[[43214,43215],2],[[43216,43225],2],[[43226,43231],3],[[43232,43255],2],[[43256,43258],2],[43259,2],[43260,2],[43261,2],[[43262,43263],2],[[43264,43309],2],[[43310,43311],2],[[43312,43347],2],[[43348,43358],3],[43359,2],[[43360,43388],2],[[43389,43391],3],[[43392,43456],2],[[43457,43469],2],[43470,3],[[43471,43481],2],[[43482,43485],3],[[43486,43487],2],[[43488,43518],2],[43519,3],[[43520,43574],2],[[43575,43583],3],[[43584,43597],2],[[43598,43599],3],[[43600,43609],2],[[43610,43611],3],[[43612,43615],2],[[43616,43638],2],[[43639,43641],2],[[43642,43643],2],[[43644,43647],2],[[43648,43714],2],[[43715,43738],3],[[43739,43741],2],[[43742,43743],2],[[43744,43759],2],[[43760,43761],2],[[43762,43766],2],[[43767,43776],3],[[43777,43782],2],[[43783,43784],3],[[43785,43790],2],[[43791,43792],3],[[43793,43798],2],[[43799,43807],3],[[43808,43814],2],[43815,3],[[43816,43822],2],[43823,3],[[43824,43866],2],[43867,2],[43868,1,"ꜧ"],[43869,1,"ꬷ"],[43870,1,"ɫ"],[43871,1,"ꭒ"],[[43872,43875],2],[[43876,43877],2],[[43878,43879],2],[43880,2],[43881,1,"ʍ"],[[43882,43883],2],[[43884,43887],3],[43888,1,"Ꭰ"],[43889,1,"Ꭱ"],[43890,1,"Ꭲ"],[43891,1,"Ꭳ"],[43892,1,"Ꭴ"],[43893,1,"Ꭵ"],[43894,1,"Ꭶ"],[43895,1,"Ꭷ"],[43896,1,"Ꭸ"],[43897,1,"Ꭹ"],[43898,1,"Ꭺ"],[43899,1,"Ꭻ"],[43900,1,"Ꭼ"],[43901,1,"Ꭽ"],[43902,1,"Ꭾ"],[43903,1,"Ꭿ"],[43904,1,"Ꮀ"],[43905,1,"Ꮁ"],[43906,1,"Ꮂ"],[43907,1,"Ꮃ"],[43908,1,"Ꮄ"],[43909,1,"Ꮅ"],[43910,1,"Ꮆ"],[43911,1,"Ꮇ"],[43912,1,"Ꮈ"],[43913,1,"Ꮉ"],[43914,1,"Ꮊ"],[43915,1,"Ꮋ"],[43916,1,"Ꮌ"],[43917,1,"Ꮍ"],[43918,1,"Ꮎ"],[43919,1,"Ꮏ"],[43920,1,"Ꮐ"],[43921,1,"Ꮑ"],[43922,1,"Ꮒ"],[43923,1,"Ꮓ"],[43924,1,"Ꮔ"],[43925,1,"Ꮕ"],[43926,1,"Ꮖ"],[43927,1,"Ꮗ"],[43928,1,"Ꮘ"],[43929,1,"Ꮙ"],[43930,1,"Ꮚ"],[43931,1,"Ꮛ"],[43932,1,"Ꮜ"],[43933,1,"Ꮝ"],[43934,1,"Ꮞ"],[43935,1,"Ꮟ"],[43936,1,"Ꮠ"],[43937,1,"Ꮡ"],[43938,1,"Ꮢ"],[43939,1,"Ꮣ"],[43940,1,"Ꮤ"],[43941,1,"Ꮥ"],[43942,1,"Ꮦ"],[43943,1,"Ꮧ"],[43944,1,"Ꮨ"],[43945,1,"Ꮩ"],[43946,1,"Ꮪ"],[43947,1,"Ꮫ"],[43948,1,"Ꮬ"],[43949,1,"Ꮭ"],[43950,1,"Ꮮ"],[43951,1,"Ꮯ"],[43952,1,"Ꮰ"],[43953,1,"Ꮱ"],[43954,1,"Ꮲ"],[43955,1,"Ꮳ"],[43956,1,"Ꮴ"],[43957,1,"Ꮵ"],[43958,1,"Ꮶ"],[43959,1,"Ꮷ"],[43960,1,"Ꮸ"],[43961,1,"Ꮹ"],[43962,1,"Ꮺ"],[43963,1,"Ꮻ"],[43964,1,"Ꮼ"],[43965,1,"Ꮽ"],[43966,1,"Ꮾ"],[43967,1,"Ꮿ"],[[43968,44010],2],[44011,2],[[44012,44013],2],[[44014,44015],3],[[44016,44025],2],[[44026,44031],3],[[44032,55203],2],[[55204,55215],3],[[55216,55238],2],[[55239,55242],3],[[55243,55291],2],[[55292,55295],3],[[55296,57343],3],[[57344,63743],3],[63744,1,"豈"],[63745,1,"更"],[63746,1,"車"],[63747,1,"賈"],[63748,1,"滑"],[63749,1,"串"],[63750,1,"句"],[[63751,63752],1,"龜"],[63753,1,"契"],[63754,1,"金"],[63755,1,"喇"],[63756,1,"奈"],[63757,1,"懶"],[63758,1,"癩"],[63759,1,"羅"],[63760,1,"蘿"],[63761,1,"螺"],[63762,1,"裸"],[63763,1,"邏"],[63764,1,"樂"],[63765,1,"洛"],[63766,1,"烙"],[63767,1,"珞"],[63768,1,"落"],[63769,1,"酪"],[63770,1,"駱"],[63771,1,"亂"],[63772,1,"卵"],[63773,1,"欄"],[63774,1,"爛"],[63775,1,"蘭"],[63776,1,"鸞"],[63777,1,"嵐"],[63778,1,"濫"],[63779,1,"藍"],[63780,1,"襤"],[63781,1,"拉"],[63782,1,"臘"],[63783,1,"蠟"],[63784,1,"廊"],[63785,1,"朗"],[63786,1,"浪"],[63787,1,"狼"],[63788,1,"郎"],[63789,1,"來"],[63790,1,"冷"],[63791,1,"勞"],[63792,1,"擄"],[63793,1,"櫓"],[63794,1,"爐"],[63795,1,"盧"],[63796,1,"老"],[63797,1,"蘆"],[63798,1,"虜"],[63799,1,"路"],[63800,1,"露"],[63801,1,"魯"],[63802,1,"鷺"],[63803,1,"碌"],[63804,1,"祿"],[63805,1,"綠"],[63806,1,"菉"],[63807,1,"錄"],[63808,1,"鹿"],[63809,1,"論"],[63810,1,"壟"],[63811,1,"弄"],[63812,1,"籠"],[63813,1,"聾"],[63814,1,"牢"],[63815,1,"磊"],[63816,1,"賂"],[63817,1,"雷"],[63818,1,"壘"],[63819,1,"屢"],[63820,1,"樓"],[63821,1,"淚"],[63822,1,"漏"],[63823,1,"累"],[63824,1,"縷"],[63825,1,"陋"],[63826,1,"勒"],[63827,1,"肋"],[63828,1,"凜"],[63829,1,"凌"],[63830,1,"稜"],[63831,1,"綾"],[63832,1,"菱"],[63833,1,"陵"],[63834,1,"讀"],[63835,1,"拏"],[63836,1,"樂"],[63837,1,"諾"],[63838,1,"丹"],[63839,1,"寧"],[63840,1,"怒"],[63841,1,"率"],[63842,1,"異"],[63843,1,"北"],[63844,1,"磻"],[63845,1,"便"],[63846,1,"復"],[63847,1,"不"],[63848,1,"泌"],[63849,1,"數"],[63850,1,"索"],[63851,1,"參"],[63852,1,"塞"],[63853,1,"省"],[63854,1,"葉"],[63855,1,"說"],[63856,1,"殺"],[63857,1,"辰"],[63858,1,"沈"],[63859,1,"拾"],[63860,1,"若"],[63861,1,"掠"],[63862,1,"略"],[63863,1,"亮"],[63864,1,"兩"],[63865,1,"凉"],[63866,1,"梁"],[63867,1,"糧"],[63868,1,"良"],[63869,1,"諒"],[63870,1,"量"],[63871,1,"勵"],[63872,1,"呂"],[63873,1,"女"],[63874,1,"廬"],[63875,1,"旅"],[63876,1,"濾"],[63877,1,"礪"],[63878,1,"閭"],[63879,1,"驪"],[63880,1,"麗"],[63881,1,"黎"],[63882,1,"力"],[63883,1,"曆"],[63884,1,"歷"],[63885,1,"轢"],[63886,1,"年"],[63887,1,"憐"],[63888,1,"戀"],[63889,1,"撚"],[63890,1,"漣"],[63891,1,"煉"],[63892,1,"璉"],[63893,1,"秊"],[63894,1,"練"],[63895,1,"聯"],[63896,1,"輦"],[63897,1,"蓮"],[63898,1,"連"],[63899,1,"鍊"],[63900,1,"列"],[63901,1,"劣"],[63902,1,"咽"],[63903,1,"烈"],[63904,1,"裂"],[63905,1,"說"],[63906,1,"廉"],[63907,1,"念"],[63908,1,"捻"],[63909,1,"殮"],[63910,1,"簾"],[63911,1,"獵"],[63912,1,"令"],[63913,1,"囹"],[63914,1,"寧"],[63915,1,"嶺"],[63916,1,"怜"],[63917,1,"玲"],[63918,1,"瑩"],[63919,1,"羚"],[63920,1,"聆"],[63921,1,"鈴"],[63922,1,"零"],[63923,1,"靈"],[63924,1,"領"],[63925,1,"例"],[63926,1,"禮"],[63927,1,"醴"],[63928,1,"隸"],[63929,1,"惡"],[63930,1,"了"],[63931,1,"僚"],[63932,1,"寮"],[63933,1,"尿"],[63934,1,"料"],[63935,1,"樂"],[63936,1,"燎"],[63937,1,"療"],[63938,1,"蓼"],[63939,1,"遼"],[63940,1,"龍"],[63941,1,"暈"],[63942,1,"阮"],[63943,1,"劉"],[63944,1,"杻"],[63945,1,"柳"],[63946,1,"流"],[63947,1,"溜"],[63948,1,"琉"],[63949,1,"留"],[63950,1,"硫"],[63951,1,"紐"],[63952,1,"類"],[63953,1,"六"],[63954,1,"戮"],[63955,1,"陸"],[63956,1,"倫"],[63957,1,"崙"],[63958,1,"淪"],[63959,1,"輪"],[63960,1,"律"],[63961,1,"慄"],[63962,1,"栗"],[63963,1,"率"],[63964,1,"隆"],[63965,1,"利"],[63966,1,"吏"],[63967,1,"履"],[63968,1,"易"],[63969,1,"李"],[63970,1,"梨"],[63971,1,"泥"],[63972,1,"理"],[63973,1,"痢"],[63974,1,"罹"],[63975,1,"裏"],[63976,1,"裡"],[63977,1,"里"],[63978,1,"離"],[63979,1,"匿"],[63980,1,"溺"],[63981,1,"吝"],[63982,1,"燐"],[63983,1,"璘"],[63984,1,"藺"],[63985,1,"隣"],[63986,1,"鱗"],[63987,1,"麟"],[63988,1,"林"],[63989,1,"淋"],[63990,1,"臨"],[63991,1,"立"],[63992,1,"笠"],[63993,1,"粒"],[63994,1,"狀"],[63995,1,"炙"],[63996,1,"識"],[63997,1,"什"],[63998,1,"茶"],[63999,1,"刺"],[64000,1,"切"],[64001,1,"度"],[64002,1,"拓"],[64003,1,"糖"],[64004,1,"宅"],[64005,1,"洞"],[64006,1,"暴"],[64007,1,"輻"],[64008,1,"行"],[64009,1,"降"],[64010,1,"見"],[64011,1,"廓"],[64012,1,"兀"],[64013,1,"嗀"],[[64014,64015],2],[64016,1,"塚"],[64017,2],[64018,1,"晴"],[[64019,64020],2],[64021,1,"凞"],[64022,1,"猪"],[64023,1,"益"],[64024,1,"礼"],[64025,1,"神"],[64026,1,"祥"],[64027,1,"福"],[64028,1,"靖"],[64029,1,"精"],[64030,1,"羽"],[64031,2],[64032,1,"蘒"],[64033,2],[64034,1,"諸"],[[64035,64036],2],[64037,1,"逸"],[64038,1,"都"],[[64039,64041],2],[64042,1,"飯"],[64043,1,"飼"],[64044,1,"館"],[64045,1,"鶴"],[64046,1,"郞"],[64047,1,"隷"],[64048,1,"侮"],[64049,1,"僧"],[64050,1,"免"],[64051,1,"勉"],[64052,1,"勤"],[64053,1,"卑"],[64054,1,"喝"],[64055,1,"嘆"],[64056,1,"器"],[64057,1,"塀"],[64058,1,"墨"],[64059,1,"層"],[64060,1,"屮"],[64061,1,"悔"],[64062,1,"慨"],[64063,1,"憎"],[64064,1,"懲"],[64065,1,"敏"],[64066,1,"既"],[64067,1,"暑"],[64068,1,"梅"],[64069,1,"海"],[64070,1,"渚"],[64071,1,"漢"],[64072,1,"煮"],[64073,1,"爫"],[64074,1,"琢"],[64075,1,"碑"],[64076,1,"社"],[64077,1,"祉"],[64078,1,"祈"],[64079,1,"祐"],[64080,1,"祖"],[64081,1,"祝"],[64082,1,"禍"],[64083,1,"禎"],[64084,1,"穀"],[64085,1,"突"],[64086,1,"節"],[64087,1,"練"],[64088,1,"縉"],[64089,1,"繁"],[64090,1,"署"],[64091,1,"者"],[64092,1,"臭"],[[64093,64094],1,"艹"],[64095,1,"著"],[64096,1,"褐"],[64097,1,"視"],[64098,1,"謁"],[64099,1,"謹"],[64100,1,"賓"],[64101,1,"贈"],[64102,1,"辶"],[64103,1,"逸"],[64104,1,"難"],[64105,1,"響"],[64106,1,"頻"],[64107,1,"恵"],[64108,1,"𤋮"],[64109,1,"舘"],[[64110,64111],3],[64112,1,"並"],[64113,1,"况"],[64114,1,"全"],[64115,1,"侀"],[64116,1,"充"],[64117,1,"冀"],[64118,1,"勇"],[64119,1,"勺"],[64120,1,"喝"],[64121,1,"啕"],[64122,1,"喙"],[64123,1,"嗢"],[64124,1,"塚"],[64125,1,"墳"],[64126,1,"奄"],[64127,1,"奔"],[64128,1,"婢"],[64129,1,"嬨"],[64130,1,"廒"],[64131,1,"廙"],[64132,1,"彩"],[64133,1,"徭"],[64134,1,"惘"],[64135,1,"慎"],[64136,1,"愈"],[64137,1,"憎"],[64138,1,"慠"],[64139,1,"懲"],[64140,1,"戴"],[64141,1,"揄"],[64142,1,"搜"],[64143,1,"摒"],[64144,1,"敖"],[64145,1,"晴"],[64146,1,"朗"],[64147,1,"望"],[64148,1,"杖"],[64149,1,"歹"],[64150,1,"殺"],[64151,1,"流"],[64152,1,"滛"],[64153,1,"滋"],[64154,1,"漢"],[64155,1,"瀞"],[64156,1,"煮"],[64157,1,"瞧"],[64158,1,"爵"],[64159,1,"犯"],[64160,1,"猪"],[64161,1,"瑱"],[64162,1,"甆"],[64163,1,"画"],[64164,1,"瘝"],[64165,1,"瘟"],[64166,1,"益"],[64167,1,"盛"],[64168,1,"直"],[64169,1,"睊"],[64170,1,"着"],[64171,1,"磌"],[64172,1,"窱"],[64173,1,"節"],[64174,1,"类"],[64175,1,"絛"],[64176,1,"練"],[64177,1,"缾"],[64178,1,"者"],[64179,1,"荒"],[64180,1,"華"],[64181,1,"蝹"],[64182,1,"襁"],[64183,1,"覆"],[64184,1,"視"],[64185,1,"調"],[64186,1,"諸"],[64187,1,"請"],[64188,1,"謁"],[64189,1,"諾"],[64190,1,"諭"],[64191,1,"謹"],[64192,1,"變"],[64193,1,"贈"],[64194,1,"輸"],[64195,1,"遲"],[64196,1,"醙"],[64197,1,"鉶"],[64198,1,"陼"],[64199,1,"難"],[64200,1,"靖"],[64201,1,"韛"],[64202,1,"響"],[64203,1,"頋"],[64204,1,"頻"],[64205,1,"鬒"],[64206,1,"龜"],[64207,1,"𢡊"],[64208,1,"𢡄"],[64209,1,"𣏕"],[64210,1,"㮝"],[64211,1,"䀘"],[64212,1,"䀹"],[64213,1,"𥉉"],[64214,1,"𥳐"],[64215,1,"𧻓"],[64216,1,"齃"],[64217,1,"龎"],[[64218,64255],3],[64256,1,"ff"],[64257,1,"fi"],[64258,1,"fl"],[64259,1,"ffi"],[64260,1,"ffl"],[[64261,64262],1,"st"],[[64263,64274],3],[64275,1,"մն"],[64276,1,"մե"],[64277,1,"մի"],[64278,1,"վն"],[64279,1,"մխ"],[[64280,64284],3],[64285,1,"יִ"],[64286,2],[64287,1,"ײַ"],[64288,1,"ע"],[64289,1,"א"],[64290,1,"ד"],[64291,1,"ה"],[64292,1,"כ"],[64293,1,"ל"],[64294,1,"ם"],[64295,1,"ר"],[64296,1,"ת"],[64297,1,"+"],[64298,1,"שׁ"],[64299,1,"שׂ"],[64300,1,"שּׁ"],[64301,1,"שּׂ"],[64302,1,"אַ"],[64303,1,"אָ"],[64304,1,"אּ"],[64305,1,"בּ"],[64306,1,"גּ"],[64307,1,"דּ"],[64308,1,"הּ"],[64309,1,"וּ"],[64310,1,"זּ"],[64311,3],[64312,1,"טּ"],[64313,1,"יּ"],[64314,1,"ךּ"],[64315,1,"כּ"],[64316,1,"לּ"],[64317,3],[64318,1,"מּ"],[64319,3],[64320,1,"נּ"],[64321,1,"סּ"],[64322,3],[64323,1,"ףּ"],[64324,1,"פּ"],[64325,3],[64326,1,"צּ"],[64327,1,"קּ"],[64328,1,"רּ"],[64329,1,"שּ"],[64330,1,"תּ"],[64331,1,"וֹ"],[64332,1,"בֿ"],[64333,1,"כֿ"],[64334,1,"פֿ"],[64335,1,"אל"],[[64336,64337],1,"ٱ"],[[64338,64341],1,"ٻ"],[[64342,64345],1,"پ"],[[64346,64349],1,"ڀ"],[[64350,64353],1,"ٺ"],[[64354,64357],1,"ٿ"],[[64358,64361],1,"ٹ"],[[64362,64365],1,"ڤ"],[[64366,64369],1,"ڦ"],[[64370,64373],1,"ڄ"],[[64374,64377],1,"ڃ"],[[64378,64381],1,"چ"],[[64382,64385],1,"ڇ"],[[64386,64387],1,"ڍ"],[[64388,64389],1,"ڌ"],[[64390,64391],1,"ڎ"],[[64392,64393],1,"ڈ"],[[64394,64395],1,"ژ"],[[64396,64397],1,"ڑ"],[[64398,64401],1,"ک"],[[64402,64405],1,"گ"],[[64406,64409],1,"ڳ"],[[64410,64413],1,"ڱ"],[[64414,64415],1,"ں"],[[64416,64419],1,"ڻ"],[[64420,64421],1,"ۀ"],[[64422,64425],1,"ہ"],[[64426,64429],1,"ھ"],[[64430,64431],1,"ے"],[[64432,64433],1,"ۓ"],[[64434,64449],2],[64450,2],[[64451,64466],3],[[64467,64470],1,"ڭ"],[[64471,64472],1,"ۇ"],[[64473,64474],1,"ۆ"],[[64475,64476],1,"ۈ"],[64477,1,"ۇٴ"],[[64478,64479],1,"ۋ"],[[64480,64481],1,"ۅ"],[[64482,64483],1,"ۉ"],[[64484,64487],1,"ې"],[[64488,64489],1,"ى"],[[64490,64491],1,"ئا"],[[64492,64493],1,"ئە"],[[64494,64495],1,"ئو"],[[64496,64497],1,"ئۇ"],[[64498,64499],1,"ئۆ"],[[64500,64501],1,"ئۈ"],[[64502,64504],1,"ئې"],[[64505,64507],1,"ئى"],[[64508,64511],1,"ی"],[64512,1,"ئج"],[64513,1,"ئح"],[64514,1,"ئم"],[64515,1,"ئى"],[64516,1,"ئي"],[64517,1,"بج"],[64518,1,"بح"],[64519,1,"بخ"],[64520,1,"بم"],[64521,1,"بى"],[64522,1,"بي"],[64523,1,"تج"],[64524,1,"تح"],[64525,1,"تخ"],[64526,1,"تم"],[64527,1,"تى"],[64528,1,"تي"],[64529,1,"ثج"],[64530,1,"ثم"],[64531,1,"ثى"],[64532,1,"ثي"],[64533,1,"جح"],[64534,1,"جم"],[64535,1,"حج"],[64536,1,"حم"],[64537,1,"خج"],[64538,1,"خح"],[64539,1,"خم"],[64540,1,"سج"],[64541,1,"سح"],[64542,1,"سخ"],[64543,1,"سم"],[64544,1,"صح"],[64545,1,"صم"],[64546,1,"ضج"],[64547,1,"ضح"],[64548,1,"ضخ"],[64549,1,"ضم"],[64550,1,"طح"],[64551,1,"طم"],[64552,1,"ظم"],[64553,1,"عج"],[64554,1,"عم"],[64555,1,"غج"],[64556,1,"غم"],[64557,1,"فج"],[64558,1,"فح"],[64559,1,"فخ"],[64560,1,"فم"],[64561,1,"فى"],[64562,1,"في"],[64563,1,"قح"],[64564,1,"قم"],[64565,1,"قى"],[64566,1,"قي"],[64567,1,"كا"],[64568,1,"كج"],[64569,1,"كح"],[64570,1,"كخ"],[64571,1,"كل"],[64572,1,"كم"],[64573,1,"كى"],[64574,1,"كي"],[64575,1,"لج"],[64576,1,"لح"],[64577,1,"لخ"],[64578,1,"لم"],[64579,1,"لى"],[64580,1,"لي"],[64581,1,"مج"],[64582,1,"مح"],[64583,1,"مخ"],[64584,1,"مم"],[64585,1,"مى"],[64586,1,"مي"],[64587,1,"نج"],[64588,1,"نح"],[64589,1,"نخ"],[64590,1,"نم"],[64591,1,"نى"],[64592,1,"ني"],[64593,1,"هج"],[64594,1,"هم"],[64595,1,"هى"],[64596,1,"هي"],[64597,1,"يج"],[64598,1,"يح"],[64599,1,"يخ"],[64600,1,"يم"],[64601,1,"يى"],[64602,1,"يي"],[64603,1,"ذٰ"],[64604,1,"رٰ"],[64605,1,"ىٰ"],[64606,1," ٌّ"],[64607,1," ٍّ"],[64608,1," َّ"],[64609,1," ُّ"],[64610,1," ِّ"],[64611,1," ّٰ"],[64612,1,"ئر"],[64613,1,"ئز"],[64614,1,"ئم"],[64615,1,"ئن"],[64616,1,"ئى"],[64617,1,"ئي"],[64618,1,"بر"],[64619,1,"بز"],[64620,1,"بم"],[64621,1,"بن"],[64622,1,"بى"],[64623,1,"بي"],[64624,1,"تر"],[64625,1,"تز"],[64626,1,"تم"],[64627,1,"تن"],[64628,1,"تى"],[64629,1,"تي"],[64630,1,"ثر"],[64631,1,"ثز"],[64632,1,"ثم"],[64633,1,"ثن"],[64634,1,"ثى"],[64635,1,"ثي"],[64636,1,"فى"],[64637,1,"في"],[64638,1,"قى"],[64639,1,"قي"],[64640,1,"كا"],[64641,1,"كل"],[64642,1,"كم"],[64643,1,"كى"],[64644,1,"كي"],[64645,1,"لم"],[64646,1,"لى"],[64647,1,"لي"],[64648,1,"ما"],[64649,1,"مم"],[64650,1,"نر"],[64651,1,"نز"],[64652,1,"نم"],[64653,1,"نن"],[64654,1,"نى"],[64655,1,"ني"],[64656,1,"ىٰ"],[64657,1,"ير"],[64658,1,"يز"],[64659,1,"يم"],[64660,1,"ين"],[64661,1,"يى"],[64662,1,"يي"],[64663,1,"ئج"],[64664,1,"ئح"],[64665,1,"ئخ"],[64666,1,"ئم"],[64667,1,"ئه"],[64668,1,"بج"],[64669,1,"بح"],[64670,1,"بخ"],[64671,1,"بم"],[64672,1,"به"],[64673,1,"تج"],[64674,1,"تح"],[64675,1,"تخ"],[64676,1,"تم"],[64677,1,"ته"],[64678,1,"ثم"],[64679,1,"جح"],[64680,1,"جم"],[64681,1,"حج"],[64682,1,"حم"],[64683,1,"خج"],[64684,1,"خم"],[64685,1,"سج"],[64686,1,"سح"],[64687,1,"سخ"],[64688,1,"سم"],[64689,1,"صح"],[64690,1,"صخ"],[64691,1,"صم"],[64692,1,"ضج"],[64693,1,"ضح"],[64694,1,"ضخ"],[64695,1,"ضم"],[64696,1,"طح"],[64697,1,"ظم"],[64698,1,"عج"],[64699,1,"عم"],[64700,1,"غج"],[64701,1,"غم"],[64702,1,"فج"],[64703,1,"فح"],[64704,1,"فخ"],[64705,1,"فم"],[64706,1,"قح"],[64707,1,"قم"],[64708,1,"كج"],[64709,1,"كح"],[64710,1,"كخ"],[64711,1,"كل"],[64712,1,"كم"],[64713,1,"لج"],[64714,1,"لح"],[64715,1,"لخ"],[64716,1,"لم"],[64717,1,"له"],[64718,1,"مج"],[64719,1,"مح"],[64720,1,"مخ"],[64721,1,"مم"],[64722,1,"نج"],[64723,1,"نح"],[64724,1,"نخ"],[64725,1,"نم"],[64726,1,"نه"],[64727,1,"هج"],[64728,1,"هم"],[64729,1,"هٰ"],[64730,1,"يج"],[64731,1,"يح"],[64732,1,"يخ"],[64733,1,"يم"],[64734,1,"يه"],[64735,1,"ئم"],[64736,1,"ئه"],[64737,1,"بم"],[64738,1,"به"],[64739,1,"تم"],[64740,1,"ته"],[64741,1,"ثم"],[64742,1,"ثه"],[64743,1,"سم"],[64744,1,"سه"],[64745,1,"شم"],[64746,1,"شه"],[64747,1,"كل"],[64748,1,"كم"],[64749,1,"لم"],[64750,1,"نم"],[64751,1,"نه"],[64752,1,"يم"],[64753,1,"يه"],[64754,1,"ـَّ"],[64755,1,"ـُّ"],[64756,1,"ـِّ"],[64757,1,"طى"],[64758,1,"طي"],[64759,1,"عى"],[64760,1,"عي"],[64761,1,"غى"],[64762,1,"غي"],[64763,1,"سى"],[64764,1,"سي"],[64765,1,"شى"],[64766,1,"شي"],[64767,1,"حى"],[64768,1,"حي"],[64769,1,"جى"],[64770,1,"جي"],[64771,1,"خى"],[64772,1,"خي"],[64773,1,"صى"],[64774,1,"صي"],[64775,1,"ضى"],[64776,1,"ضي"],[64777,1,"شج"],[64778,1,"شح"],[64779,1,"شخ"],[64780,1,"شم"],[64781,1,"شر"],[64782,1,"سر"],[64783,1,"صر"],[64784,1,"ضر"],[64785,1,"طى"],[64786,1,"طي"],[64787,1,"عى"],[64788,1,"عي"],[64789,1,"غى"],[64790,1,"غي"],[64791,1,"سى"],[64792,1,"سي"],[64793,1,"شى"],[64794,1,"شي"],[64795,1,"حى"],[64796,1,"حي"],[64797,1,"جى"],[64798,1,"جي"],[64799,1,"خى"],[64800,1,"خي"],[64801,1,"صى"],[64802,1,"صي"],[64803,1,"ضى"],[64804,1,"ضي"],[64805,1,"شج"],[64806,1,"شح"],[64807,1,"شخ"],[64808,1,"شم"],[64809,1,"شر"],[64810,1,"سر"],[64811,1,"صر"],[64812,1,"ضر"],[64813,1,"شج"],[64814,1,"شح"],[64815,1,"شخ"],[64816,1,"شم"],[64817,1,"سه"],[64818,1,"شه"],[64819,1,"طم"],[64820,1,"سج"],[64821,1,"سح"],[64822,1,"سخ"],[64823,1,"شج"],[64824,1,"شح"],[64825,1,"شخ"],[64826,1,"طم"],[64827,1,"ظم"],[[64828,64829],1,"اً"],[[64830,64831],2],[[64832,64847],2],[64848,1,"تجم"],[[64849,64850],1,"تحج"],[64851,1,"تحم"],[64852,1,"تخم"],[64853,1,"تمج"],[64854,1,"تمح"],[64855,1,"تمخ"],[[64856,64857],1,"جمح"],[64858,1,"حمي"],[64859,1,"حمى"],[64860,1,"سحج"],[64861,1,"سجح"],[64862,1,"سجى"],[[64863,64864],1,"سمح"],[64865,1,"سمج"],[[64866,64867],1,"سمم"],[[64868,64869],1,"صحح"],[64870,1,"صمم"],[[64871,64872],1,"شحم"],[64873,1,"شجي"],[[64874,64875],1,"شمخ"],[[64876,64877],1,"شمم"],[64878,1,"ضحى"],[[64879,64880],1,"ضخم"],[[64881,64882],1,"طمح"],[64883,1,"طمم"],[64884,1,"طمي"],[64885,1,"عجم"],[[64886,64887],1,"عمم"],[64888,1,"عمى"],[64889,1,"غمم"],[64890,1,"غمي"],[64891,1,"غمى"],[[64892,64893],1,"فخم"],[64894,1,"قمح"],[64895,1,"قمم"],[64896,1,"لحم"],[64897,1,"لحي"],[64898,1,"لحى"],[[64899,64900],1,"لجج"],[[64901,64902],1,"لخم"],[[64903,64904],1,"لمح"],[64905,1,"محج"],[64906,1,"محم"],[64907,1,"محي"],[64908,1,"مجح"],[64909,1,"مجم"],[64910,1,"مخج"],[64911,1,"مخم"],[[64912,64913],3],[64914,1,"مجخ"],[64915,1,"همج"],[64916,1,"همم"],[64917,1,"نحم"],[64918,1,"نحى"],[[64919,64920],1,"نجم"],[64921,1,"نجى"],[64922,1,"نمي"],[64923,1,"نمى"],[[64924,64925],1,"يمم"],[64926,1,"بخي"],[64927,1,"تجي"],[64928,1,"تجى"],[64929,1,"تخي"],[64930,1,"تخى"],[64931,1,"تمي"],[64932,1,"تمى"],[64933,1,"جمي"],[64934,1,"جحى"],[64935,1,"جمى"],[64936,1,"سخى"],[64937,1,"صحي"],[64938,1,"شحي"],[64939,1,"ضحي"],[64940,1,"لجي"],[64941,1,"لمي"],[64942,1,"يحي"],[64943,1,"يجي"],[64944,1,"يمي"],[64945,1,"ممي"],[64946,1,"قمي"],[64947,1,"نحي"],[64948,1,"قمح"],[64949,1,"لحم"],[64950,1,"عمي"],[64951,1,"كمي"],[64952,1,"نجح"],[64953,1,"مخي"],[64954,1,"لجم"],[64955,1,"كمم"],[64956,1,"لجم"],[64957,1,"نجح"],[64958,1,"جحي"],[64959,1,"حجي"],[64960,1,"مجي"],[64961,1,"فمي"],[64962,1,"بحي"],[64963,1,"كمم"],[64964,1,"عجم"],[64965,1,"صمم"],[64966,1,"سخي"],[64967,1,"نجي"],[[64968,64974],3],[64975,2],[[64976,65007],3],[65008,1,"صلے"],[65009,1,"قلے"],[65010,1,"الله"],[65011,1,"اكبر"],[65012,1,"محمد"],[65013,1,"صلعم"],[65014,1,"رسول"],[65015,1,"عليه"],[65016,1,"وسلم"],[65017,1,"صلى"],[65018,1,"صلى الله عليه وسلم"],[65019,1,"جل جلاله"],[65020,1,"ریال"],[65021,2],[[65022,65023],2],[[65024,65039],7],[65040,1,","],[65041,1,"、"],[65042,3],[65043,1,":"],[65044,1,";"],[65045,1,"!"],[65046,1,"?"],[65047,1,"〖"],[65048,1,"〗"],[65049,3],[[65050,65055],3],[[65056,65059],2],[[65060,65062],2],[[65063,65069],2],[[65070,65071],2],[65072,3],[65073,1,"—"],[65074,1,"–"],[[65075,65076],1,"_"],[65077,1,"("],[65078,1,")"],[65079,1,"{"],[65080,1,"}"],[65081,1,"〔"],[65082,1,"〕"],[65083,1,"【"],[65084,1,"】"],[65085,1,"《"],[65086,1,"》"],[65087,1,"〈"],[65088,1,"〉"],[65089,1,"「"],[65090,1,"」"],[65091,1,"『"],[65092,1,"』"],[[65093,65094],2],[65095,1,"["],[65096,1,"]"],[[65097,65100],1," ̅"],[[65101,65103],1,"_"],[65104,1,","],[65105,1,"、"],[65106,3],[65107,3],[65108,1,";"],[65109,1,":"],[65110,1,"?"],[65111,1,"!"],[65112,1,"—"],[65113,1,"("],[65114,1,")"],[65115,1,"{"],[65116,1,"}"],[65117,1,"〔"],[65118,1,"〕"],[65119,1,"#"],[65120,1,"&"],[65121,1,"*"],[65122,1,"+"],[65123,1,"-"],[65124,1,"<"],[65125,1,">"],[65126,1,"="],[65127,3],[65128,1,"\\"],[65129,1,"$"],[65130,1,"%"],[65131,1,"@"],[[65132,65135],3],[65136,1," ً"],[65137,1,"ـً"],[65138,1," ٌ"],[65139,2],[65140,1," ٍ"],[65141,3],[65142,1," َ"],[65143,1,"ـَ"],[65144,1," ُ"],[65145,1,"ـُ"],[65146,1," ِ"],[65147,1,"ـِ"],[65148,1," ّ"],[65149,1,"ـّ"],[65150,1," ْ"],[65151,1,"ـْ"],[65152,1,"ء"],[[65153,65154],1,"آ"],[[65155,65156],1,"أ"],[[65157,65158],1,"ؤ"],[[65159,65160],1,"إ"],[[65161,65164],1,"ئ"],[[65165,65166],1,"ا"],[[65167,65170],1,"ب"],[[65171,65172],1,"ة"],[[65173,65176],1,"ت"],[[65177,65180],1,"ث"],[[65181,65184],1,"ج"],[[65185,65188],1,"ح"],[[65189,65192],1,"خ"],[[65193,65194],1,"د"],[[65195,65196],1,"ذ"],[[65197,65198],1,"ر"],[[65199,65200],1,"ز"],[[65201,65204],1,"س"],[[65205,65208],1,"ش"],[[65209,65212],1,"ص"],[[65213,65216],1,"ض"],[[65217,65220],1,"ط"],[[65221,65224],1,"ظ"],[[65225,65228],1,"ع"],[[65229,65232],1,"غ"],[[65233,65236],1,"ف"],[[65237,65240],1,"ق"],[[65241,65244],1,"ك"],[[65245,65248],1,"ل"],[[65249,65252],1,"م"],[[65253,65256],1,"ن"],[[65257,65260],1,"ه"],[[65261,65262],1,"و"],[[65263,65264],1,"ى"],[[65265,65268],1,"ي"],[[65269,65270],1,"لآ"],[[65271,65272],1,"لأ"],[[65273,65274],1,"لإ"],[[65275,65276],1,"لا"],[[65277,65278],3],[65279,7],[65280,3],[65281,1,"!"],[65282,1,'"'],[65283,1,"#"],[65284,1,"$"],[65285,1,"%"],[65286,1,"&"],[65287,1,"'"],[65288,1,"("],[65289,1,")"],[65290,1,"*"],[65291,1,"+"],[65292,1,","],[65293,1,"-"],[65294,1,"."],[65295,1,"/"],[65296,1,"0"],[65297,1,"1"],[65298,1,"2"],[65299,1,"3"],[65300,1,"4"],[65301,1,"5"],[65302,1,"6"],[65303,1,"7"],[65304,1,"8"],[65305,1,"9"],[65306,1,":"],[65307,1,";"],[65308,1,"<"],[65309,1,"="],[65310,1,">"],[65311,1,"?"],[65312,1,"@"],[65313,1,"a"],[65314,1,"b"],[65315,1,"c"],[65316,1,"d"],[65317,1,"e"],[65318,1,"f"],[65319,1,"g"],[65320,1,"h"],[65321,1,"i"],[65322,1,"j"],[65323,1,"k"],[65324,1,"l"],[65325,1,"m"],[65326,1,"n"],[65327,1,"o"],[65328,1,"p"],[65329,1,"q"],[65330,1,"r"],[65331,1,"s"],[65332,1,"t"],[65333,1,"u"],[65334,1,"v"],[65335,1,"w"],[65336,1,"x"],[65337,1,"y"],[65338,1,"z"],[65339,1,"["],[65340,1,"\\"],[65341,1,"]"],[65342,1,"^"],[65343,1,"_"],[65344,1,"`"],[65345,1,"a"],[65346,1,"b"],[65347,1,"c"],[65348,1,"d"],[65349,1,"e"],[65350,1,"f"],[65351,1,"g"],[65352,1,"h"],[65353,1,"i"],[65354,1,"j"],[65355,1,"k"],[65356,1,"l"],[65357,1,"m"],[65358,1,"n"],[65359,1,"o"],[65360,1,"p"],[65361,1,"q"],[65362,1,"r"],[65363,1,"s"],[65364,1,"t"],[65365,1,"u"],[65366,1,"v"],[65367,1,"w"],[65368,1,"x"],[65369,1,"y"],[65370,1,"z"],[65371,1,"{"],[65372,1,"|"],[65373,1,"}"],[65374,1,"~"],[65375,1,"⦅"],[65376,1,"⦆"],[65377,1,"."],[65378,1,"「"],[65379,1,"」"],[65380,1,"、"],[65381,1,"・"],[65382,1,"ヲ"],[65383,1,"ァ"],[65384,1,"ィ"],[65385,1,"ゥ"],[65386,1,"ェ"],[65387,1,"ォ"],[65388,1,"ャ"],[65389,1,"ュ"],[65390,1,"ョ"],[65391,1,"ッ"],[65392,1,"ー"],[65393,1,"ア"],[65394,1,"イ"],[65395,1,"ウ"],[65396,1,"エ"],[65397,1,"オ"],[65398,1,"カ"],[65399,1,"キ"],[65400,1,"ク"],[65401,1,"ケ"],[65402,1,"コ"],[65403,1,"サ"],[65404,1,"シ"],[65405,1,"ス"],[65406,1,"セ"],[65407,1,"ソ"],[65408,1,"タ"],[65409,1,"チ"],[65410,1,"ツ"],[65411,1,"テ"],[65412,1,"ト"],[65413,1,"ナ"],[65414,1,"ニ"],[65415,1,"ヌ"],[65416,1,"ネ"],[65417,1,"ノ"],[65418,1,"ハ"],[65419,1,"ヒ"],[65420,1,"フ"],[65421,1,"ヘ"],[65422,1,"ホ"],[65423,1,"マ"],[65424,1,"ミ"],[65425,1,"ム"],[65426,1,"メ"],[65427,1,"モ"],[65428,1,"ヤ"],[65429,1,"ユ"],[65430,1,"ヨ"],[65431,1,"ラ"],[65432,1,"リ"],[65433,1,"ル"],[65434,1,"レ"],[65435,1,"ロ"],[65436,1,"ワ"],[65437,1,"ン"],[65438,1,"゙"],[65439,1,"゚"],[65440,7],[65441,1,"ᄀ"],[65442,1,"ᄁ"],[65443,1,"ᆪ"],[65444,1,"ᄂ"],[65445,1,"ᆬ"],[65446,1,"ᆭ"],[65447,1,"ᄃ"],[65448,1,"ᄄ"],[65449,1,"ᄅ"],[65450,1,"ᆰ"],[65451,1,"ᆱ"],[65452,1,"ᆲ"],[65453,1,"ᆳ"],[65454,1,"ᆴ"],[65455,1,"ᆵ"],[65456,1,"ᄚ"],[65457,1,"ᄆ"],[65458,1,"ᄇ"],[65459,1,"ᄈ"],[65460,1,"ᄡ"],[65461,1,"ᄉ"],[65462,1,"ᄊ"],[65463,1,"ᄋ"],[65464,1,"ᄌ"],[65465,1,"ᄍ"],[65466,1,"ᄎ"],[65467,1,"ᄏ"],[65468,1,"ᄐ"],[65469,1,"ᄑ"],[65470,1,"ᄒ"],[[65471,65473],3],[65474,1,"ᅡ"],[65475,1,"ᅢ"],[65476,1,"ᅣ"],[65477,1,"ᅤ"],[65478,1,"ᅥ"],[65479,1,"ᅦ"],[[65480,65481],3],[65482,1,"ᅧ"],[65483,1,"ᅨ"],[65484,1,"ᅩ"],[65485,1,"ᅪ"],[65486,1,"ᅫ"],[65487,1,"ᅬ"],[[65488,65489],3],[65490,1,"ᅭ"],[65491,1,"ᅮ"],[65492,1,"ᅯ"],[65493,1,"ᅰ"],[65494,1,"ᅱ"],[65495,1,"ᅲ"],[[65496,65497],3],[65498,1,"ᅳ"],[65499,1,"ᅴ"],[65500,1,"ᅵ"],[[65501,65503],3],[65504,1,"¢"],[65505,1,"£"],[65506,1,"¬"],[65507,1," ̄"],[65508,1,"¦"],[65509,1,"¥"],[65510,1,"₩"],[65511,3],[65512,1,"│"],[65513,1,"←"],[65514,1,"↑"],[65515,1,"→"],[65516,1,"↓"],[65517,1,"■"],[65518,1,"○"],[[65519,65528],3],[[65529,65531],3],[65532,3],[65533,3],[[65534,65535],3],[[65536,65547],2],[65548,3],[[65549,65574],2],[65575,3],[[65576,65594],2],[65595,3],[[65596,65597],2],[65598,3],[[65599,65613],2],[[65614,65615],3],[[65616,65629],2],[[65630,65663],3],[[65664,65786],2],[[65787,65791],3],[[65792,65794],2],[[65795,65798],3],[[65799,65843],2],[[65844,65846],3],[[65847,65855],2],[[65856,65930],2],[[65931,65932],2],[[65933,65934],2],[65935,3],[[65936,65947],2],[65948,2],[[65949,65951],3],[65952,2],[[65953,65999],3],[[66000,66044],2],[66045,2],[[66046,66175],3],[[66176,66204],2],[[66205,66207],3],[[66208,66256],2],[[66257,66271],3],[66272,2],[[66273,66299],2],[[66300,66303],3],[[66304,66334],2],[66335,2],[[66336,66339],2],[[66340,66348],3],[[66349,66351],2],[[66352,66368],2],[66369,2],[[66370,66377],2],[66378,2],[[66379,66383],3],[[66384,66426],2],[[66427,66431],3],[[66432,66461],2],[66462,3],[66463,2],[[66464,66499],2],[[66500,66503],3],[[66504,66511],2],[[66512,66517],2],[[66518,66559],3],[66560,1,"𐐨"],[66561,1,"𐐩"],[66562,1,"𐐪"],[66563,1,"𐐫"],[66564,1,"𐐬"],[66565,1,"𐐭"],[66566,1,"𐐮"],[66567,1,"𐐯"],[66568,1,"𐐰"],[66569,1,"𐐱"],[66570,1,"𐐲"],[66571,1,"𐐳"],[66572,1,"𐐴"],[66573,1,"𐐵"],[66574,1,"𐐶"],[66575,1,"𐐷"],[66576,1,"𐐸"],[66577,1,"𐐹"],[66578,1,"𐐺"],[66579,1,"𐐻"],[66580,1,"𐐼"],[66581,1,"𐐽"],[66582,1,"𐐾"],[66583,1,"𐐿"],[66584,1,"𐑀"],[66585,1,"𐑁"],[66586,1,"𐑂"],[66587,1,"𐑃"],[66588,1,"𐑄"],[66589,1,"𐑅"],[66590,1,"𐑆"],[66591,1,"𐑇"],[66592,1,"𐑈"],[66593,1,"𐑉"],[66594,1,"𐑊"],[66595,1,"𐑋"],[66596,1,"𐑌"],[66597,1,"𐑍"],[66598,1,"𐑎"],[66599,1,"𐑏"],[[66600,66637],2],[[66638,66717],2],[[66718,66719],3],[[66720,66729],2],[[66730,66735],3],[66736,1,"𐓘"],[66737,1,"𐓙"],[66738,1,"𐓚"],[66739,1,"𐓛"],[66740,1,"𐓜"],[66741,1,"𐓝"],[66742,1,"𐓞"],[66743,1,"𐓟"],[66744,1,"𐓠"],[66745,1,"𐓡"],[66746,1,"𐓢"],[66747,1,"𐓣"],[66748,1,"𐓤"],[66749,1,"𐓥"],[66750,1,"𐓦"],[66751,1,"𐓧"],[66752,1,"𐓨"],[66753,1,"𐓩"],[66754,1,"𐓪"],[66755,1,"𐓫"],[66756,1,"𐓬"],[66757,1,"𐓭"],[66758,1,"𐓮"],[66759,1,"𐓯"],[66760,1,"𐓰"],[66761,1,"𐓱"],[66762,1,"𐓲"],[66763,1,"𐓳"],[66764,1,"𐓴"],[66765,1,"𐓵"],[66766,1,"𐓶"],[66767,1,"𐓷"],[66768,1,"𐓸"],[66769,1,"𐓹"],[66770,1,"𐓺"],[66771,1,"𐓻"],[[66772,66775],3],[[66776,66811],2],[[66812,66815],3],[[66816,66855],2],[[66856,66863],3],[[66864,66915],2],[[66916,66926],3],[66927,2],[66928,1,"𐖗"],[66929,1,"𐖘"],[66930,1,"𐖙"],[66931,1,"𐖚"],[66932,1,"𐖛"],[66933,1,"𐖜"],[66934,1,"𐖝"],[66935,1,"𐖞"],[66936,1,"𐖟"],[66937,1,"𐖠"],[66938,1,"𐖡"],[66939,3],[66940,1,"𐖣"],[66941,1,"𐖤"],[66942,1,"𐖥"],[66943,1,"𐖦"],[66944,1,"𐖧"],[66945,1,"𐖨"],[66946,1,"𐖩"],[66947,1,"𐖪"],[66948,1,"𐖫"],[66949,1,"𐖬"],[66950,1,"𐖭"],[66951,1,"𐖮"],[66952,1,"𐖯"],[66953,1,"𐖰"],[66954,1,"𐖱"],[66955,3],[66956,1,"𐖳"],[66957,1,"𐖴"],[66958,1,"𐖵"],[66959,1,"𐖶"],[66960,1,"𐖷"],[66961,1,"𐖸"],[66962,1,"𐖹"],[66963,3],[66964,1,"𐖻"],[66965,1,"𐖼"],[66966,3],[[66967,66977],2],[66978,3],[[66979,66993],2],[66994,3],[[66995,67001],2],[67002,3],[[67003,67004],2],[[67005,67007],3],[[67008,67059],2],[[67060,67071],3],[[67072,67382],2],[[67383,67391],3],[[67392,67413],2],[[67414,67423],3],[[67424,67431],2],[[67432,67455],3],[67456,2],[67457,1,"ː"],[67458,1,"ˑ"],[67459,1,"æ"],[67460,1,"ʙ"],[67461,1,"ɓ"],[67462,3],[67463,1,"ʣ"],[67464,1,"ꭦ"],[67465,1,"ʥ"],[67466,1,"ʤ"],[67467,1,"ɖ"],[67468,1,"ɗ"],[67469,1,"ᶑ"],[67470,1,"ɘ"],[67471,1,"ɞ"],[67472,1,"ʩ"],[67473,1,"ɤ"],[67474,1,"ɢ"],[67475,1,"ɠ"],[67476,1,"ʛ"],[67477,1,"ħ"],[67478,1,"ʜ"],[67479,1,"ɧ"],[67480,1,"ʄ"],[67481,1,"ʪ"],[67482,1,"ʫ"],[67483,1,"ɬ"],[67484,1,"𝼄"],[67485,1,"ꞎ"],[67486,1,"ɮ"],[67487,1,"𝼅"],[67488,1,"ʎ"],[67489,1,"𝼆"],[67490,1,"ø"],[67491,1,"ɶ"],[67492,1,"ɷ"],[67493,1,"q"],[67494,1,"ɺ"],[67495,1,"𝼈"],[67496,1,"ɽ"],[67497,1,"ɾ"],[67498,1,"ʀ"],[67499,1,"ʨ"],[67500,1,"ʦ"],[67501,1,"ꭧ"],[67502,1,"ʧ"],[67503,1,"ʈ"],[67504,1,"ⱱ"],[67505,3],[67506,1,"ʏ"],[67507,1,"ʡ"],[67508,1,"ʢ"],[67509,1,"ʘ"],[67510,1,"ǀ"],[67511,1,"ǁ"],[67512,1,"ǂ"],[67513,1,"𝼊"],[67514,1,"𝼞"],[[67515,67583],3],[[67584,67589],2],[[67590,67591],3],[67592,2],[67593,3],[[67594,67637],2],[67638,3],[[67639,67640],2],[[67641,67643],3],[67644,2],[[67645,67646],3],[67647,2],[[67648,67669],2],[67670,3],[[67671,67679],2],[[67680,67702],2],[[67703,67711],2],[[67712,67742],2],[[67743,67750],3],[[67751,67759],2],[[67760,67807],3],[[67808,67826],2],[67827,3],[[67828,67829],2],[[67830,67834],3],[[67835,67839],2],[[67840,67861],2],[[67862,67865],2],[[67866,67867],2],[[67868,67870],3],[67871,2],[[67872,67897],2],[[67898,67902],3],[67903,2],[[67904,67967],3],[[67968,68023],2],[[68024,68027],3],[[68028,68029],2],[[68030,68031],2],[[68032,68047],2],[[68048,68049],3],[[68050,68095],2],[[68096,68099],2],[68100,3],[[68101,68102],2],[[68103,68107],3],[[68108,68115],2],[68116,3],[[68117,68119],2],[68120,3],[[68121,68147],2],[[68148,68149],2],[[68150,68151],3],[[68152,68154],2],[[68155,68158],3],[68159,2],[[68160,68167],2],[68168,2],[[68169,68175],3],[[68176,68184],2],[[68185,68191],3],[[68192,68220],2],[[68221,68223],2],[[68224,68252],2],[[68253,68255],2],[[68256,68287],3],[[68288,68295],2],[68296,2],[[68297,68326],2],[[68327,68330],3],[[68331,68342],2],[[68343,68351],3],[[68352,68405],2],[[68406,68408],3],[[68409,68415],2],[[68416,68437],2],[[68438,68439],3],[[68440,68447],2],[[68448,68466],2],[[68467,68471],3],[[68472,68479],2],[[68480,68497],2],[[68498,68504],3],[[68505,68508],2],[[68509,68520],3],[[68521,68527],2],[[68528,68607],3],[[68608,68680],2],[[68681,68735],3],[68736,1,"𐳀"],[68737,1,"𐳁"],[68738,1,"𐳂"],[68739,1,"𐳃"],[68740,1,"𐳄"],[68741,1,"𐳅"],[68742,1,"𐳆"],[68743,1,"𐳇"],[68744,1,"𐳈"],[68745,1,"𐳉"],[68746,1,"𐳊"],[68747,1,"𐳋"],[68748,1,"𐳌"],[68749,1,"𐳍"],[68750,1,"𐳎"],[68751,1,"𐳏"],[68752,1,"𐳐"],[68753,1,"𐳑"],[68754,1,"𐳒"],[68755,1,"𐳓"],[68756,1,"𐳔"],[68757,1,"𐳕"],[68758,1,"𐳖"],[68759,1,"𐳗"],[68760,1,"𐳘"],[68761,1,"𐳙"],[68762,1,"𐳚"],[68763,1,"𐳛"],[68764,1,"𐳜"],[68765,1,"𐳝"],[68766,1,"𐳞"],[68767,1,"𐳟"],[68768,1,"𐳠"],[68769,1,"𐳡"],[68770,1,"𐳢"],[68771,1,"𐳣"],[68772,1,"𐳤"],[68773,1,"𐳥"],[68774,1,"𐳦"],[68775,1,"𐳧"],[68776,1,"𐳨"],[68777,1,"𐳩"],[68778,1,"𐳪"],[68779,1,"𐳫"],[68780,1,"𐳬"],[68781,1,"𐳭"],[68782,1,"𐳮"],[68783,1,"𐳯"],[68784,1,"𐳰"],[68785,1,"𐳱"],[68786,1,"𐳲"],[[68787,68799],3],[[68800,68850],2],[[68851,68857],3],[[68858,68863],2],[[68864,68903],2],[[68904,68911],3],[[68912,68921],2],[[68922,68927],3],[[68928,68943],2],[68944,1,"𐵰"],[68945,1,"𐵱"],[68946,1,"𐵲"],[68947,1,"𐵳"],[68948,1,"𐵴"],[68949,1,"𐵵"],[68950,1,"𐵶"],[68951,1,"𐵷"],[68952,1,"𐵸"],[68953,1,"𐵹"],[68954,1,"𐵺"],[68955,1,"𐵻"],[68956,1,"𐵼"],[68957,1,"𐵽"],[68958,1,"𐵾"],[68959,1,"𐵿"],[68960,1,"𐶀"],[68961,1,"𐶁"],[68962,1,"𐶂"],[68963,1,"𐶃"],[68964,1,"𐶄"],[68965,1,"𐶅"],[[68966,68968],3],[[68969,68973],2],[68974,2],[[68975,68997],2],[[68998,69005],3],[[69006,69007],2],[[69008,69215],3],[[69216,69246],2],[69247,3],[[69248,69289],2],[69290,3],[[69291,69292],2],[69293,2],[[69294,69295],3],[[69296,69297],2],[[69298,69313],3],[[69314,69316],2],[[69317,69371],3],[69372,2],[[69373,69375],2],[[69376,69404],2],[[69405,69414],2],[69415,2],[[69416,69423],3],[[69424,69456],2],[[69457,69465],2],[[69466,69487],3],[[69488,69509],2],[[69510,69513],2],[[69514,69551],3],[[69552,69572],2],[[69573,69579],2],[[69580,69599],3],[[69600,69622],2],[[69623,69631],3],[[69632,69702],2],[[69703,69709],2],[[69710,69713],3],[[69714,69733],2],[[69734,69743],2],[[69744,69749],2],[[69750,69758],3],[69759,2],[[69760,69818],2],[[69819,69820],2],[69821,3],[[69822,69825],2],[69826,2],[[69827,69836],3],[69837,3],[[69838,69839],3],[[69840,69864],2],[[69865,69871],3],[[69872,69881],2],[[69882,69887],3],[[69888,69940],2],[69941,3],[[69942,69951],2],[[69952,69955],2],[[69956,69958],2],[69959,2],[[69960,69967],3],[[69968,70003],2],[[70004,70005],2],[70006,2],[[70007,70015],3],[[70016,70084],2],[[70085,70088],2],[[70089,70092],2],[70093,2],[[70094,70095],2],[[70096,70105],2],[70106,2],[70107,2],[70108,2],[[70109,70111],2],[70112,3],[[70113,70132],2],[[70133,70143],3],[[70144,70161],2],[70162,3],[[70163,70199],2],[[70200,70205],2],[70206,2],[[70207,70209],2],[[70210,70271],3],[[70272,70278],2],[70279,3],[70280,2],[70281,3],[[70282,70285],2],[70286,3],[[70287,70301],2],[70302,3],[[70303,70312],2],[70313,2],[[70314,70319],3],[[70320,70378],2],[[70379,70383],3],[[70384,70393],2],[[70394,70399],3],[70400,2],[[70401,70403],2],[70404,3],[[70405,70412],2],[[70413,70414],3],[[70415,70416],2],[[70417,70418],3],[[70419,70440],2],[70441,3],[[70442,70448],2],[70449,3],[[70450,70451],2],[70452,3],[[70453,70457],2],[70458,3],[70459,2],[[70460,70468],2],[[70469,70470],3],[[70471,70472],2],[[70473,70474],3],[[70475,70477],2],[[70478,70479],3],[70480,2],[[70481,70486],3],[70487,2],[[70488,70492],3],[[70493,70499],2],[[70500,70501],3],[[70502,70508],2],[[70509,70511],3],[[70512,70516],2],[[70517,70527],3],[[70528,70537],2],[70538,3],[70539,2],[[70540,70541],3],[70542,2],[70543,3],[[70544,70581],2],[70582,3],[[70583,70592],2],[70593,3],[70594,2],[[70595,70596],3],[70597,2],[70598,3],[[70599,70602],2],[70603,3],[[70604,70611],2],[[70612,70613],2],[70614,3],[[70615,70616],2],[[70617,70624],3],[[70625,70626],2],[[70627,70655],3],[[70656,70730],2],[[70731,70735],2],[[70736,70745],2],[70746,2],[70747,2],[70748,3],[70749,2],[70750,2],[70751,2],[[70752,70753],2],[[70754,70783],3],[[70784,70853],2],[70854,2],[70855,2],[[70856,70863],3],[[70864,70873],2],[[70874,71039],3],[[71040,71093],2],[[71094,71095],3],[[71096,71104],2],[[71105,71113],2],[[71114,71127],2],[[71128,71133],2],[[71134,71167],3],[[71168,71232],2],[[71233,71235],2],[71236,2],[[71237,71247],3],[[71248,71257],2],[[71258,71263],3],[[71264,71276],2],[[71277,71295],3],[[71296,71351],2],[71352,2],[71353,2],[[71354,71359],3],[[71360,71369],2],[[71370,71375],3],[[71376,71395],2],[[71396,71423],3],[[71424,71449],2],[71450,2],[[71451,71452],3],[[71453,71467],2],[[71468,71471],3],[[71472,71481],2],[[71482,71487],2],[[71488,71494],2],[[71495,71679],3],[[71680,71738],2],[71739,2],[[71740,71839],3],[71840,1,"𑣀"],[71841,1,"𑣁"],[71842,1,"𑣂"],[71843,1,"𑣃"],[71844,1,"𑣄"],[71845,1,"𑣅"],[71846,1,"𑣆"],[71847,1,"𑣇"],[71848,1,"𑣈"],[71849,1,"𑣉"],[71850,1,"𑣊"],[71851,1,"𑣋"],[71852,1,"𑣌"],[71853,1,"𑣍"],[71854,1,"𑣎"],[71855,1,"𑣏"],[71856,1,"𑣐"],[71857,1,"𑣑"],[71858,1,"𑣒"],[71859,1,"𑣓"],[71860,1,"𑣔"],[71861,1,"𑣕"],[71862,1,"𑣖"],[71863,1,"𑣗"],[71864,1,"𑣘"],[71865,1,"𑣙"],[71866,1,"𑣚"],[71867,1,"𑣛"],[71868,1,"𑣜"],[71869,1,"𑣝"],[71870,1,"𑣞"],[71871,1,"𑣟"],[[71872,71913],2],[[71914,71922],2],[[71923,71934],3],[71935,2],[[71936,71942],2],[[71943,71944],3],[71945,2],[[71946,71947],3],[[71948,71955],2],[71956,3],[[71957,71958],2],[71959,3],[[71960,71989],2],[71990,3],[[71991,71992],2],[[71993,71994],3],[[71995,72003],2],[[72004,72006],2],[[72007,72015],3],[[72016,72025],2],[[72026,72095],3],[[72096,72103],2],[[72104,72105],3],[[72106,72151],2],[[72152,72153],3],[[72154,72161],2],[72162,2],[[72163,72164],2],[[72165,72191],3],[[72192,72254],2],[[72255,72262],2],[72263,2],[[72264,72271],3],[[72272,72323],2],[[72324,72325],2],[[72326,72345],2],[[72346,72348],2],[72349,2],[[72350,72354],2],[[72355,72367],3],[[72368,72383],2],[[72384,72440],2],[[72441,72447],3],[[72448,72457],2],[[72458,72639],3],[[72640,72672],2],[72673,2],[[72674,72687],3],[[72688,72697],2],[[72698,72703],3],[[72704,72712],2],[72713,3],[[72714,72758],2],[72759,3],[[72760,72768],2],[[72769,72773],2],[[72774,72783],3],[[72784,72793],2],[[72794,72812],2],[[72813,72815],3],[[72816,72817],2],[[72818,72847],2],[[72848,72849],3],[[72850,72871],2],[72872,3],[[72873,72886],2],[[72887,72959],3],[[72960,72966],2],[72967,3],[[72968,72969],2],[72970,3],[[72971,73014],2],[[73015,73017],3],[73018,2],[73019,3],[[73020,73021],2],[73022,3],[[73023,73031],2],[[73032,73039],3],[[73040,73049],2],[[73050,73055],3],[[73056,73061],2],[73062,3],[[73063,73064],2],[73065,3],[[73066,73102],2],[73103,3],[[73104,73105],2],[73106,3],[[73107,73112],2],[[73113,73119],3],[[73120,73129],2],[[73130,73439],3],[[73440,73462],2],[[73463,73464],2],[[73465,73471],3],[[73472,73488],2],[73489,3],[[73490,73530],2],[[73531,73533],3],[[73534,73538],2],[[73539,73551],2],[[73552,73561],2],[73562,2],[[73563,73647],3],[73648,2],[[73649,73663],3],[[73664,73713],2],[[73714,73726],3],[73727,2],[[73728,74606],2],[[74607,74648],2],[74649,2],[[74650,74751],3],[[74752,74850],2],[[74851,74862],2],[74863,3],[[74864,74867],2],[74868,2],[[74869,74879],3],[[74880,75075],2],[[75076,77711],3],[[77712,77808],2],[[77809,77810],2],[[77811,77823],3],[[77824,78894],2],[78895,2],[[78896,78904],3],[[78905,78911],3],[[78912,78933],2],[[78934,78943],3],[[78944,82938],2],[[82939,82943],3],[[82944,83526],2],[[83527,90367],3],[[90368,90425],2],[[90426,92159],3],[[92160,92728],2],[[92729,92735],3],[[92736,92766],2],[92767,3],[[92768,92777],2],[[92778,92781],3],[[92782,92783],2],[[92784,92862],2],[92863,3],[[92864,92873],2],[[92874,92879],3],[[92880,92909],2],[[92910,92911],3],[[92912,92916],2],[92917,2],[[92918,92927],3],[[92928,92982],2],[[92983,92991],2],[[92992,92995],2],[[92996,92997],2],[[92998,93007],3],[[93008,93017],2],[93018,3],[[93019,93025],2],[93026,3],[[93027,93047],2],[[93048,93052],3],[[93053,93071],2],[[93072,93503],3],[[93504,93548],2],[[93549,93551],2],[[93552,93561],2],[[93562,93759],3],[93760,1,"𖹠"],[93761,1,"𖹡"],[93762,1,"𖹢"],[93763,1,"𖹣"],[93764,1,"𖹤"],[93765,1,"𖹥"],[93766,1,"𖹦"],[93767,1,"𖹧"],[93768,1,"𖹨"],[93769,1,"𖹩"],[93770,1,"𖹪"],[93771,1,"𖹫"],[93772,1,"𖹬"],[93773,1,"𖹭"],[93774,1,"𖹮"],[93775,1,"𖹯"],[93776,1,"𖹰"],[93777,1,"𖹱"],[93778,1,"𖹲"],[93779,1,"𖹳"],[93780,1,"𖹴"],[93781,1,"𖹵"],[93782,1,"𖹶"],[93783,1,"𖹷"],[93784,1,"𖹸"],[93785,1,"𖹹"],[93786,1,"𖹺"],[93787,1,"𖹻"],[93788,1,"𖹼"],[93789,1,"𖹽"],[93790,1,"𖹾"],[93791,1,"𖹿"],[[93792,93823],2],[[93824,93850],2],[[93851,93951],3],[[93952,94020],2],[[94021,94026],2],[[94027,94030],3],[94031,2],[[94032,94078],2],[[94079,94087],2],[[94088,94094],3],[[94095,94111],2],[[94112,94175],3],[94176,2],[94177,2],[94178,2],[94179,2],[94180,2],[[94181,94191],3],[[94192,94193],2],[[94194,94207],3],[[94208,100332],2],[[100333,100337],2],[[100338,100343],2],[[100344,100351],3],[[100352,101106],2],[[101107,101589],2],[[101590,101630],3],[101631,2],[[101632,101640],2],[[101641,110575],3],[[110576,110579],2],[110580,3],[[110581,110587],2],[110588,3],[[110589,110590],2],[110591,3],[[110592,110593],2],[[110594,110878],2],[[110879,110882],2],[[110883,110897],3],[110898,2],[[110899,110927],3],[[110928,110930],2],[[110931,110932],3],[110933,2],[[110934,110947],3],[[110948,110951],2],[[110952,110959],3],[[110960,111355],2],[[111356,113663],3],[[113664,113770],2],[[113771,113775],3],[[113776,113788],2],[[113789,113791],3],[[113792,113800],2],[[113801,113807],3],[[113808,113817],2],[[113818,113819],3],[113820,2],[[113821,113822],2],[113823,2],[[113824,113827],7],[[113828,117759],3],[[117760,117973],2],[117974,1,"a"],[117975,1,"b"],[117976,1,"c"],[117977,1,"d"],[117978,1,"e"],[117979,1,"f"],[117980,1,"g"],[117981,1,"h"],[117982,1,"i"],[117983,1,"j"],[117984,1,"k"],[117985,1,"l"],[117986,1,"m"],[117987,1,"n"],[117988,1,"o"],[117989,1,"p"],[117990,1,"q"],[117991,1,"r"],[117992,1,"s"],[117993,1,"t"],[117994,1,"u"],[117995,1,"v"],[117996,1,"w"],[117997,1,"x"],[117998,1,"y"],[117999,1,"z"],[118000,1,"0"],[118001,1,"1"],[118002,1,"2"],[118003,1,"3"],[118004,1,"4"],[118005,1,"5"],[118006,1,"6"],[118007,1,"7"],[118008,1,"8"],[118009,1,"9"],[[118010,118015],3],[[118016,118451],2],[[118452,118527],3],[[118528,118573],2],[[118574,118575],3],[[118576,118598],2],[[118599,118607],3],[[118608,118723],2],[[118724,118783],3],[[118784,119029],2],[[119030,119039],3],[[119040,119078],2],[[119079,119080],3],[119081,2],[[119082,119133],2],[119134,1,"𝅗𝅥"],[119135,1,"𝅘𝅥"],[119136,1,"𝅘𝅥𝅮"],[119137,1,"𝅘𝅥𝅯"],[119138,1,"𝅘𝅥𝅰"],[119139,1,"𝅘𝅥𝅱"],[119140,1,"𝅘𝅥𝅲"],[[119141,119154],2],[[119155,119162],7],[[119163,119226],2],[119227,1,"𝆹𝅥"],[119228,1,"𝆺𝅥"],[119229,1,"𝆹𝅥𝅮"],[119230,1,"𝆺𝅥𝅮"],[119231,1,"𝆹𝅥𝅯"],[119232,1,"𝆺𝅥𝅯"],[[119233,119261],2],[[119262,119272],2],[[119273,119274],2],[[119275,119295],3],[[119296,119365],2],[[119366,119487],3],[[119488,119507],2],[[119508,119519],3],[[119520,119539],2],[[119540,119551],3],[[119552,119638],2],[[119639,119647],3],[[119648,119665],2],[[119666,119672],2],[[119673,119807],3],[119808,1,"a"],[119809,1,"b"],[119810,1,"c"],[119811,1,"d"],[119812,1,"e"],[119813,1,"f"],[119814,1,"g"],[119815,1,"h"],[119816,1,"i"],[119817,1,"j"],[119818,1,"k"],[119819,1,"l"],[119820,1,"m"],[119821,1,"n"],[119822,1,"o"],[119823,1,"p"],[119824,1,"q"],[119825,1,"r"],[119826,1,"s"],[119827,1,"t"],[119828,1,"u"],[119829,1,"v"],[119830,1,"w"],[119831,1,"x"],[119832,1,"y"],[119833,1,"z"],[119834,1,"a"],[119835,1,"b"],[119836,1,"c"],[119837,1,"d"],[119838,1,"e"],[119839,1,"f"],[119840,1,"g"],[119841,1,"h"],[119842,1,"i"],[119843,1,"j"],[119844,1,"k"],[119845,1,"l"],[119846,1,"m"],[119847,1,"n"],[119848,1,"o"],[119849,1,"p"],[119850,1,"q"],[119851,1,"r"],[119852,1,"s"],[119853,1,"t"],[119854,1,"u"],[119855,1,"v"],[119856,1,"w"],[119857,1,"x"],[119858,1,"y"],[119859,1,"z"],[119860,1,"a"],[119861,1,"b"],[119862,1,"c"],[119863,1,"d"],[119864,1,"e"],[119865,1,"f"],[119866,1,"g"],[119867,1,"h"],[119868,1,"i"],[119869,1,"j"],[119870,1,"k"],[119871,1,"l"],[119872,1,"m"],[119873,1,"n"],[119874,1,"o"],[119875,1,"p"],[119876,1,"q"],[119877,1,"r"],[119878,1,"s"],[119879,1,"t"],[119880,1,"u"],[119881,1,"v"],[119882,1,"w"],[119883,1,"x"],[119884,1,"y"],[119885,1,"z"],[119886,1,"a"],[119887,1,"b"],[119888,1,"c"],[119889,1,"d"],[119890,1,"e"],[119891,1,"f"],[119892,1,"g"],[119893,3],[119894,1,"i"],[119895,1,"j"],[119896,1,"k"],[119897,1,"l"],[119898,1,"m"],[119899,1,"n"],[119900,1,"o"],[119901,1,"p"],[119902,1,"q"],[119903,1,"r"],[119904,1,"s"],[119905,1,"t"],[119906,1,"u"],[119907,1,"v"],[119908,1,"w"],[119909,1,"x"],[119910,1,"y"],[119911,1,"z"],[119912,1,"a"],[119913,1,"b"],[119914,1,"c"],[119915,1,"d"],[119916,1,"e"],[119917,1,"f"],[119918,1,"g"],[119919,1,"h"],[119920,1,"i"],[119921,1,"j"],[119922,1,"k"],[119923,1,"l"],[119924,1,"m"],[119925,1,"n"],[119926,1,"o"],[119927,1,"p"],[119928,1,"q"],[119929,1,"r"],[119930,1,"s"],[119931,1,"t"],[119932,1,"u"],[119933,1,"v"],[119934,1,"w"],[119935,1,"x"],[119936,1,"y"],[119937,1,"z"],[119938,1,"a"],[119939,1,"b"],[119940,1,"c"],[119941,1,"d"],[119942,1,"e"],[119943,1,"f"],[119944,1,"g"],[119945,1,"h"],[119946,1,"i"],[119947,1,"j"],[119948,1,"k"],[119949,1,"l"],[119950,1,"m"],[119951,1,"n"],[119952,1,"o"],[119953,1,"p"],[119954,1,"q"],[119955,1,"r"],[119956,1,"s"],[119957,1,"t"],[119958,1,"u"],[119959,1,"v"],[119960,1,"w"],[119961,1,"x"],[119962,1,"y"],[119963,1,"z"],[119964,1,"a"],[119965,3],[119966,1,"c"],[119967,1,"d"],[[119968,119969],3],[119970,1,"g"],[[119971,119972],3],[119973,1,"j"],[119974,1,"k"],[[119975,119976],3],[119977,1,"n"],[119978,1,"o"],[119979,1,"p"],[119980,1,"q"],[119981,3],[119982,1,"s"],[119983,1,"t"],[119984,1,"u"],[119985,1,"v"],[119986,1,"w"],[119987,1,"x"],[119988,1,"y"],[119989,1,"z"],[119990,1,"a"],[119991,1,"b"],[119992,1,"c"],[119993,1,"d"],[119994,3],[119995,1,"f"],[119996,3],[119997,1,"h"],[119998,1,"i"],[119999,1,"j"],[120000,1,"k"],[120001,1,"l"],[120002,1,"m"],[120003,1,"n"],[120004,3],[120005,1,"p"],[120006,1,"q"],[120007,1,"r"],[120008,1,"s"],[120009,1,"t"],[120010,1,"u"],[120011,1,"v"],[120012,1,"w"],[120013,1,"x"],[120014,1,"y"],[120015,1,"z"],[120016,1,"a"],[120017,1,"b"],[120018,1,"c"],[120019,1,"d"],[120020,1,"e"],[120021,1,"f"],[120022,1,"g"],[120023,1,"h"],[120024,1,"i"],[120025,1,"j"],[120026,1,"k"],[120027,1,"l"],[120028,1,"m"],[120029,1,"n"],[120030,1,"o"],[120031,1,"p"],[120032,1,"q"],[120033,1,"r"],[120034,1,"s"],[120035,1,"t"],[120036,1,"u"],[120037,1,"v"],[120038,1,"w"],[120039,1,"x"],[120040,1,"y"],[120041,1,"z"],[120042,1,"a"],[120043,1,"b"],[120044,1,"c"],[120045,1,"d"],[120046,1,"e"],[120047,1,"f"],[120048,1,"g"],[120049,1,"h"],[120050,1,"i"],[120051,1,"j"],[120052,1,"k"],[120053,1,"l"],[120054,1,"m"],[120055,1,"n"],[120056,1,"o"],[120057,1,"p"],[120058,1,"q"],[120059,1,"r"],[120060,1,"s"],[120061,1,"t"],[120062,1,"u"],[120063,1,"v"],[120064,1,"w"],[120065,1,"x"],[120066,1,"y"],[120067,1,"z"],[120068,1,"a"],[120069,1,"b"],[120070,3],[120071,1,"d"],[120072,1,"e"],[120073,1,"f"],[120074,1,"g"],[[120075,120076],3],[120077,1,"j"],[120078,1,"k"],[120079,1,"l"],[120080,1,"m"],[120081,1,"n"],[120082,1,"o"],[120083,1,"p"],[120084,1,"q"],[120085,3],[120086,1,"s"],[120087,1,"t"],[120088,1,"u"],[120089,1,"v"],[120090,1,"w"],[120091,1,"x"],[120092,1,"y"],[120093,3],[120094,1,"a"],[120095,1,"b"],[120096,1,"c"],[120097,1,"d"],[120098,1,"e"],[120099,1,"f"],[120100,1,"g"],[120101,1,"h"],[120102,1,"i"],[120103,1,"j"],[120104,1,"k"],[120105,1,"l"],[120106,1,"m"],[120107,1,"n"],[120108,1,"o"],[120109,1,"p"],[120110,1,"q"],[120111,1,"r"],[120112,1,"s"],[120113,1,"t"],[120114,1,"u"],[120115,1,"v"],[120116,1,"w"],[120117,1,"x"],[120118,1,"y"],[120119,1,"z"],[120120,1,"a"],[120121,1,"b"],[120122,3],[120123,1,"d"],[120124,1,"e"],[120125,1,"f"],[120126,1,"g"],[120127,3],[120128,1,"i"],[120129,1,"j"],[120130,1,"k"],[120131,1,"l"],[120132,1,"m"],[120133,3],[120134,1,"o"],[[120135,120137],3],[120138,1,"s"],[120139,1,"t"],[120140,1,"u"],[120141,1,"v"],[120142,1,"w"],[120143,1,"x"],[120144,1,"y"],[120145,3],[120146,1,"a"],[120147,1,"b"],[120148,1,"c"],[120149,1,"d"],[120150,1,"e"],[120151,1,"f"],[120152,1,"g"],[120153,1,"h"],[120154,1,"i"],[120155,1,"j"],[120156,1,"k"],[120157,1,"l"],[120158,1,"m"],[120159,1,"n"],[120160,1,"o"],[120161,1,"p"],[120162,1,"q"],[120163,1,"r"],[120164,1,"s"],[120165,1,"t"],[120166,1,"u"],[120167,1,"v"],[120168,1,"w"],[120169,1,"x"],[120170,1,"y"],[120171,1,"z"],[120172,1,"a"],[120173,1,"b"],[120174,1,"c"],[120175,1,"d"],[120176,1,"e"],[120177,1,"f"],[120178,1,"g"],[120179,1,"h"],[120180,1,"i"],[120181,1,"j"],[120182,1,"k"],[120183,1,"l"],[120184,1,"m"],[120185,1,"n"],[120186,1,"o"],[120187,1,"p"],[120188,1,"q"],[120189,1,"r"],[120190,1,"s"],[120191,1,"t"],[120192,1,"u"],[120193,1,"v"],[120194,1,"w"],[120195,1,"x"],[120196,1,"y"],[120197,1,"z"],[120198,1,"a"],[120199,1,"b"],[120200,1,"c"],[120201,1,"d"],[120202,1,"e"],[120203,1,"f"],[120204,1,"g"],[120205,1,"h"],[120206,1,"i"],[120207,1,"j"],[120208,1,"k"],[120209,1,"l"],[120210,1,"m"],[120211,1,"n"],[120212,1,"o"],[120213,1,"p"],[120214,1,"q"],[120215,1,"r"],[120216,1,"s"],[120217,1,"t"],[120218,1,"u"],[120219,1,"v"],[120220,1,"w"],[120221,1,"x"],[120222,1,"y"],[120223,1,"z"],[120224,1,"a"],[120225,1,"b"],[120226,1,"c"],[120227,1,"d"],[120228,1,"e"],[120229,1,"f"],[120230,1,"g"],[120231,1,"h"],[120232,1,"i"],[120233,1,"j"],[120234,1,"k"],[120235,1,"l"],[120236,1,"m"],[120237,1,"n"],[120238,1,"o"],[120239,1,"p"],[120240,1,"q"],[120241,1,"r"],[120242,1,"s"],[120243,1,"t"],[120244,1,"u"],[120245,1,"v"],[120246,1,"w"],[120247,1,"x"],[120248,1,"y"],[120249,1,"z"],[120250,1,"a"],[120251,1,"b"],[120252,1,"c"],[120253,1,"d"],[120254,1,"e"],[120255,1,"f"],[120256,1,"g"],[120257,1,"h"],[120258,1,"i"],[120259,1,"j"],[120260,1,"k"],[120261,1,"l"],[120262,1,"m"],[120263,1,"n"],[120264,1,"o"],[120265,1,"p"],[120266,1,"q"],[120267,1,"r"],[120268,1,"s"],[120269,1,"t"],[120270,1,"u"],[120271,1,"v"],[120272,1,"w"],[120273,1,"x"],[120274,1,"y"],[120275,1,"z"],[120276,1,"a"],[120277,1,"b"],[120278,1,"c"],[120279,1,"d"],[120280,1,"e"],[120281,1,"f"],[120282,1,"g"],[120283,1,"h"],[120284,1,"i"],[120285,1,"j"],[120286,1,"k"],[120287,1,"l"],[120288,1,"m"],[120289,1,"n"],[120290,1,"o"],[120291,1,"p"],[120292,1,"q"],[120293,1,"r"],[120294,1,"s"],[120295,1,"t"],[120296,1,"u"],[120297,1,"v"],[120298,1,"w"],[120299,1,"x"],[120300,1,"y"],[120301,1,"z"],[120302,1,"a"],[120303,1,"b"],[120304,1,"c"],[120305,1,"d"],[120306,1,"e"],[120307,1,"f"],[120308,1,"g"],[120309,1,"h"],[120310,1,"i"],[120311,1,"j"],[120312,1,"k"],[120313,1,"l"],[120314,1,"m"],[120315,1,"n"],[120316,1,"o"],[120317,1,"p"],[120318,1,"q"],[120319,1,"r"],[120320,1,"s"],[120321,1,"t"],[120322,1,"u"],[120323,1,"v"],[120324,1,"w"],[120325,1,"x"],[120326,1,"y"],[120327,1,"z"],[120328,1,"a"],[120329,1,"b"],[120330,1,"c"],[120331,1,"d"],[120332,1,"e"],[120333,1,"f"],[120334,1,"g"],[120335,1,"h"],[120336,1,"i"],[120337,1,"j"],[120338,1,"k"],[120339,1,"l"],[120340,1,"m"],[120341,1,"n"],[120342,1,"o"],[120343,1,"p"],[120344,1,"q"],[120345,1,"r"],[120346,1,"s"],[120347,1,"t"],[120348,1,"u"],[120349,1,"v"],[120350,1,"w"],[120351,1,"x"],[120352,1,"y"],[120353,1,"z"],[120354,1,"a"],[120355,1,"b"],[120356,1,"c"],[120357,1,"d"],[120358,1,"e"],[120359,1,"f"],[120360,1,"g"],[120361,1,"h"],[120362,1,"i"],[120363,1,"j"],[120364,1,"k"],[120365,1,"l"],[120366,1,"m"],[120367,1,"n"],[120368,1,"o"],[120369,1,"p"],[120370,1,"q"],[120371,1,"r"],[120372,1,"s"],[120373,1,"t"],[120374,1,"u"],[120375,1,"v"],[120376,1,"w"],[120377,1,"x"],[120378,1,"y"],[120379,1,"z"],[120380,1,"a"],[120381,1,"b"],[120382,1,"c"],[120383,1,"d"],[120384,1,"e"],[120385,1,"f"],[120386,1,"g"],[120387,1,"h"],[120388,1,"i"],[120389,1,"j"],[120390,1,"k"],[120391,1,"l"],[120392,1,"m"],[120393,1,"n"],[120394,1,"o"],[120395,1,"p"],[120396,1,"q"],[120397,1,"r"],[120398,1,"s"],[120399,1,"t"],[120400,1,"u"],[120401,1,"v"],[120402,1,"w"],[120403,1,"x"],[120404,1,"y"],[120405,1,"z"],[120406,1,"a"],[120407,1,"b"],[120408,1,"c"],[120409,1,"d"],[120410,1,"e"],[120411,1,"f"],[120412,1,"g"],[120413,1,"h"],[120414,1,"i"],[120415,1,"j"],[120416,1,"k"],[120417,1,"l"],[120418,1,"m"],[120419,1,"n"],[120420,1,"o"],[120421,1,"p"],[120422,1,"q"],[120423,1,"r"],[120424,1,"s"],[120425,1,"t"],[120426,1,"u"],[120427,1,"v"],[120428,1,"w"],[120429,1,"x"],[120430,1,"y"],[120431,1,"z"],[120432,1,"a"],[120433,1,"b"],[120434,1,"c"],[120435,1,"d"],[120436,1,"e"],[120437,1,"f"],[120438,1,"g"],[120439,1,"h"],[120440,1,"i"],[120441,1,"j"],[120442,1,"k"],[120443,1,"l"],[120444,1,"m"],[120445,1,"n"],[120446,1,"o"],[120447,1,"p"],[120448,1,"q"],[120449,1,"r"],[120450,1,"s"],[120451,1,"t"],[120452,1,"u"],[120453,1,"v"],[120454,1,"w"],[120455,1,"x"],[120456,1,"y"],[120457,1,"z"],[120458,1,"a"],[120459,1,"b"],[120460,1,"c"],[120461,1,"d"],[120462,1,"e"],[120463,1,"f"],[120464,1,"g"],[120465,1,"h"],[120466,1,"i"],[120467,1,"j"],[120468,1,"k"],[120469,1,"l"],[120470,1,"m"],[120471,1,"n"],[120472,1,"o"],[120473,1,"p"],[120474,1,"q"],[120475,1,"r"],[120476,1,"s"],[120477,1,"t"],[120478,1,"u"],[120479,1,"v"],[120480,1,"w"],[120481,1,"x"],[120482,1,"y"],[120483,1,"z"],[120484,1,"ı"],[120485,1,"ȷ"],[[120486,120487],3],[120488,1,"α"],[120489,1,"β"],[120490,1,"γ"],[120491,1,"δ"],[120492,1,"ε"],[120493,1,"ζ"],[120494,1,"η"],[120495,1,"θ"],[120496,1,"ι"],[120497,1,"κ"],[120498,1,"λ"],[120499,1,"μ"],[120500,1,"ν"],[120501,1,"ξ"],[120502,1,"ο"],[120503,1,"π"],[120504,1,"ρ"],[120505,1,"θ"],[120506,1,"σ"],[120507,1,"τ"],[120508,1,"υ"],[120509,1,"φ"],[120510,1,"χ"],[120511,1,"ψ"],[120512,1,"ω"],[120513,1,"∇"],[120514,1,"α"],[120515,1,"β"],[120516,1,"γ"],[120517,1,"δ"],[120518,1,"ε"],[120519,1,"ζ"],[120520,1,"η"],[120521,1,"θ"],[120522,1,"ι"],[120523,1,"κ"],[120524,1,"λ"],[120525,1,"μ"],[120526,1,"ν"],[120527,1,"ξ"],[120528,1,"ο"],[120529,1,"π"],[120530,1,"ρ"],[[120531,120532],1,"σ"],[120533,1,"τ"],[120534,1,"υ"],[120535,1,"φ"],[120536,1,"χ"],[120537,1,"ψ"],[120538,1,"ω"],[120539,1,"∂"],[120540,1,"ε"],[120541,1,"θ"],[120542,1,"κ"],[120543,1,"φ"],[120544,1,"ρ"],[120545,1,"π"],[120546,1,"α"],[120547,1,"β"],[120548,1,"γ"],[120549,1,"δ"],[120550,1,"ε"],[120551,1,"ζ"],[120552,1,"η"],[120553,1,"θ"],[120554,1,"ι"],[120555,1,"κ"],[120556,1,"λ"],[120557,1,"μ"],[120558,1,"ν"],[120559,1,"ξ"],[120560,1,"ο"],[120561,1,"π"],[120562,1,"ρ"],[120563,1,"θ"],[120564,1,"σ"],[120565,1,"τ"],[120566,1,"υ"],[120567,1,"φ"],[120568,1,"χ"],[120569,1,"ψ"],[120570,1,"ω"],[120571,1,"∇"],[120572,1,"α"],[120573,1,"β"],[120574,1,"γ"],[120575,1,"δ"],[120576,1,"ε"],[120577,1,"ζ"],[120578,1,"η"],[120579,1,"θ"],[120580,1,"ι"],[120581,1,"κ"],[120582,1,"λ"],[120583,1,"μ"],[120584,1,"ν"],[120585,1,"ξ"],[120586,1,"ο"],[120587,1,"π"],[120588,1,"ρ"],[[120589,120590],1,"σ"],[120591,1,"τ"],[120592,1,"υ"],[120593,1,"φ"],[120594,1,"χ"],[120595,1,"ψ"],[120596,1,"ω"],[120597,1,"∂"],[120598,1,"ε"],[120599,1,"θ"],[120600,1,"κ"],[120601,1,"φ"],[120602,1,"ρ"],[120603,1,"π"],[120604,1,"α"],[120605,1,"β"],[120606,1,"γ"],[120607,1,"δ"],[120608,1,"ε"],[120609,1,"ζ"],[120610,1,"η"],[120611,1,"θ"],[120612,1,"ι"],[120613,1,"κ"],[120614,1,"λ"],[120615,1,"μ"],[120616,1,"ν"],[120617,1,"ξ"],[120618,1,"ο"],[120619,1,"π"],[120620,1,"ρ"],[120621,1,"θ"],[120622,1,"σ"],[120623,1,"τ"],[120624,1,"υ"],[120625,1,"φ"],[120626,1,"χ"],[120627,1,"ψ"],[120628,1,"ω"],[120629,1,"∇"],[120630,1,"α"],[120631,1,"β"],[120632,1,"γ"],[120633,1,"δ"],[120634,1,"ε"],[120635,1,"ζ"],[120636,1,"η"],[120637,1,"θ"],[120638,1,"ι"],[120639,1,"κ"],[120640,1,"λ"],[120641,1,"μ"],[120642,1,"ν"],[120643,1,"ξ"],[120644,1,"ο"],[120645,1,"π"],[120646,1,"ρ"],[[120647,120648],1,"σ"],[120649,1,"τ"],[120650,1,"υ"],[120651,1,"φ"],[120652,1,"χ"],[120653,1,"ψ"],[120654,1,"ω"],[120655,1,"∂"],[120656,1,"ε"],[120657,1,"θ"],[120658,1,"κ"],[120659,1,"φ"],[120660,1,"ρ"],[120661,1,"π"],[120662,1,"α"],[120663,1,"β"],[120664,1,"γ"],[120665,1,"δ"],[120666,1,"ε"],[120667,1,"ζ"],[120668,1,"η"],[120669,1,"θ"],[120670,1,"ι"],[120671,1,"κ"],[120672,1,"λ"],[120673,1,"μ"],[120674,1,"ν"],[120675,1,"ξ"],[120676,1,"ο"],[120677,1,"π"],[120678,1,"ρ"],[120679,1,"θ"],[120680,1,"σ"],[120681,1,"τ"],[120682,1,"υ"],[120683,1,"φ"],[120684,1,"χ"],[120685,1,"ψ"],[120686,1,"ω"],[120687,1,"∇"],[120688,1,"α"],[120689,1,"β"],[120690,1,"γ"],[120691,1,"δ"],[120692,1,"ε"],[120693,1,"ζ"],[120694,1,"η"],[120695,1,"θ"],[120696,1,"ι"],[120697,1,"κ"],[120698,1,"λ"],[120699,1,"μ"],[120700,1,"ν"],[120701,1,"ξ"],[120702,1,"ο"],[120703,1,"π"],[120704,1,"ρ"],[[120705,120706],1,"σ"],[120707,1,"τ"],[120708,1,"υ"],[120709,1,"φ"],[120710,1,"χ"],[120711,1,"ψ"],[120712,1,"ω"],[120713,1,"∂"],[120714,1,"ε"],[120715,1,"θ"],[120716,1,"κ"],[120717,1,"φ"],[120718,1,"ρ"],[120719,1,"π"],[120720,1,"α"],[120721,1,"β"],[120722,1,"γ"],[120723,1,"δ"],[120724,1,"ε"],[120725,1,"ζ"],[120726,1,"η"],[120727,1,"θ"],[120728,1,"ι"],[120729,1,"κ"],[120730,1,"λ"],[120731,1,"μ"],[120732,1,"ν"],[120733,1,"ξ"],[120734,1,"ο"],[120735,1,"π"],[120736,1,"ρ"],[120737,1,"θ"],[120738,1,"σ"],[120739,1,"τ"],[120740,1,"υ"],[120741,1,"φ"],[120742,1,"χ"],[120743,1,"ψ"],[120744,1,"ω"],[120745,1,"∇"],[120746,1,"α"],[120747,1,"β"],[120748,1,"γ"],[120749,1,"δ"],[120750,1,"ε"],[120751,1,"ζ"],[120752,1,"η"],[120753,1,"θ"],[120754,1,"ι"],[120755,1,"κ"],[120756,1,"λ"],[120757,1,"μ"],[120758,1,"ν"],[120759,1,"ξ"],[120760,1,"ο"],[120761,1,"π"],[120762,1,"ρ"],[[120763,120764],1,"σ"],[120765,1,"τ"],[120766,1,"υ"],[120767,1,"φ"],[120768,1,"χ"],[120769,1,"ψ"],[120770,1,"ω"],[120771,1,"∂"],[120772,1,"ε"],[120773,1,"θ"],[120774,1,"κ"],[120775,1,"φ"],[120776,1,"ρ"],[120777,1,"π"],[[120778,120779],1,"ϝ"],[[120780,120781],3],[120782,1,"0"],[120783,1,"1"],[120784,1,"2"],[120785,1,"3"],[120786,1,"4"],[120787,1,"5"],[120788,1,"6"],[120789,1,"7"],[120790,1,"8"],[120791,1,"9"],[120792,1,"0"],[120793,1,"1"],[120794,1,"2"],[120795,1,"3"],[120796,1,"4"],[120797,1,"5"],[120798,1,"6"],[120799,1,"7"],[120800,1,"8"],[120801,1,"9"],[120802,1,"0"],[120803,1,"1"],[120804,1,"2"],[120805,1,"3"],[120806,1,"4"],[120807,1,"5"],[120808,1,"6"],[120809,1,"7"],[120810,1,"8"],[120811,1,"9"],[120812,1,"0"],[120813,1,"1"],[120814,1,"2"],[120815,1,"3"],[120816,1,"4"],[120817,1,"5"],[120818,1,"6"],[120819,1,"7"],[120820,1,"8"],[120821,1,"9"],[120822,1,"0"],[120823,1,"1"],[120824,1,"2"],[120825,1,"3"],[120826,1,"4"],[120827,1,"5"],[120828,1,"6"],[120829,1,"7"],[120830,1,"8"],[120831,1,"9"],[[120832,121343],2],[[121344,121398],2],[[121399,121402],2],[[121403,121452],2],[[121453,121460],2],[121461,2],[[121462,121475],2],[121476,2],[[121477,121483],2],[[121484,121498],3],[[121499,121503],2],[121504,3],[[121505,121519],2],[[121520,122623],3],[[122624,122654],2],[[122655,122660],3],[[122661,122666],2],[[122667,122879],3],[[122880,122886],2],[122887,3],[[122888,122904],2],[[122905,122906],3],[[122907,122913],2],[122914,3],[[122915,122916],2],[122917,3],[[122918,122922],2],[[122923,122927],3],[122928,1,"а"],[122929,1,"б"],[122930,1,"в"],[122931,1,"г"],[122932,1,"д"],[122933,1,"е"],[122934,1,"ж"],[122935,1,"з"],[122936,1,"и"],[122937,1,"к"],[122938,1,"л"],[122939,1,"м"],[122940,1,"о"],[122941,1,"п"],[122942,1,"р"],[122943,1,"с"],[122944,1,"т"],[122945,1,"у"],[122946,1,"ф"],[122947,1,"х"],[122948,1,"ц"],[122949,1,"ч"],[122950,1,"ш"],[122951,1,"ы"],[122952,1,"э"],[122953,1,"ю"],[122954,1,"ꚉ"],[122955,1,"ә"],[122956,1,"і"],[122957,1,"ј"],[122958,1,"ө"],[122959,1,"ү"],[122960,1,"ӏ"],[122961,1,"а"],[122962,1,"б"],[122963,1,"в"],[122964,1,"г"],[122965,1,"д"],[122966,1,"е"],[122967,1,"ж"],[122968,1,"з"],[122969,1,"и"],[122970,1,"к"],[122971,1,"л"],[122972,1,"о"],[122973,1,"п"],[122974,1,"с"],[122975,1,"у"],[122976,1,"ф"],[122977,1,"х"],[122978,1,"ц"],[122979,1,"ч"],[122980,1,"ш"],[122981,1,"ъ"],[122982,1,"ы"],[122983,1,"ґ"],[122984,1,"і"],[122985,1,"ѕ"],[122986,1,"џ"],[122987,1,"ҫ"],[122988,1,"ꙑ"],[122989,1,"ұ"],[[122990,123022],3],[123023,2],[[123024,123135],3],[[123136,123180],2],[[123181,123183],3],[[123184,123197],2],[[123198,123199],3],[[123200,123209],2],[[123210,123213],3],[123214,2],[123215,2],[[123216,123535],3],[[123536,123566],2],[[123567,123583],3],[[123584,123641],2],[[123642,123646],3],[123647,2],[[123648,124111],3],[[124112,124153],2],[[124154,124367],3],[[124368,124410],2],[[124411,124414],3],[124415,2],[[124416,124895],3],[[124896,124902],2],[124903,3],[[124904,124907],2],[124908,3],[[124909,124910],2],[124911,3],[[124912,124926],2],[124927,3],[[124928,125124],2],[[125125,125126],3],[[125127,125135],2],[[125136,125142],2],[[125143,125183],3],[125184,1,"𞤢"],[125185,1,"𞤣"],[125186,1,"𞤤"],[125187,1,"𞤥"],[125188,1,"𞤦"],[125189,1,"𞤧"],[125190,1,"𞤨"],[125191,1,"𞤩"],[125192,1,"𞤪"],[125193,1,"𞤫"],[125194,1,"𞤬"],[125195,1,"𞤭"],[125196,1,"𞤮"],[125197,1,"𞤯"],[125198,1,"𞤰"],[125199,1,"𞤱"],[125200,1,"𞤲"],[125201,1,"𞤳"],[125202,1,"𞤴"],[125203,1,"𞤵"],[125204,1,"𞤶"],[125205,1,"𞤷"],[125206,1,"𞤸"],[125207,1,"𞤹"],[125208,1,"𞤺"],[125209,1,"𞤻"],[125210,1,"𞤼"],[125211,1,"𞤽"],[125212,1,"𞤾"],[125213,1,"𞤿"],[125214,1,"𞥀"],[125215,1,"𞥁"],[125216,1,"𞥂"],[125217,1,"𞥃"],[[125218,125258],2],[125259,2],[[125260,125263],3],[[125264,125273],2],[[125274,125277],3],[[125278,125279],2],[[125280,126064],3],[[126065,126132],2],[[126133,126208],3],[[126209,126269],2],[[126270,126463],3],[126464,1,"ا"],[126465,1,"ب"],[126466,1,"ج"],[126467,1,"د"],[126468,3],[126469,1,"و"],[126470,1,"ز"],[126471,1,"ح"],[126472,1,"ط"],[126473,1,"ي"],[126474,1,"ك"],[126475,1,"ل"],[126476,1,"م"],[126477,1,"ن"],[126478,1,"س"],[126479,1,"ع"],[126480,1,"ف"],[126481,1,"ص"],[126482,1,"ق"],[126483,1,"ر"],[126484,1,"ش"],[126485,1,"ت"],[126486,1,"ث"],[126487,1,"خ"],[126488,1,"ذ"],[126489,1,"ض"],[126490,1,"ظ"],[126491,1,"غ"],[126492,1,"ٮ"],[126493,1,"ں"],[126494,1,"ڡ"],[126495,1,"ٯ"],[126496,3],[126497,1,"ب"],[126498,1,"ج"],[126499,3],[126500,1,"ه"],[[126501,126502],3],[126503,1,"ح"],[126504,3],[126505,1,"ي"],[126506,1,"ك"],[126507,1,"ل"],[126508,1,"م"],[126509,1,"ن"],[126510,1,"س"],[126511,1,"ع"],[126512,1,"ف"],[126513,1,"ص"],[126514,1,"ق"],[126515,3],[126516,1,"ش"],[126517,1,"ت"],[126518,1,"ث"],[126519,1,"خ"],[126520,3],[126521,1,"ض"],[126522,3],[126523,1,"غ"],[[126524,126529],3],[126530,1,"ج"],[[126531,126534],3],[126535,1,"ح"],[126536,3],[126537,1,"ي"],[126538,3],[126539,1,"ل"],[126540,3],[126541,1,"ن"],[126542,1,"س"],[126543,1,"ع"],[126544,3],[126545,1,"ص"],[126546,1,"ق"],[126547,3],[126548,1,"ش"],[[126549,126550],3],[126551,1,"خ"],[126552,3],[126553,1,"ض"],[126554,3],[126555,1,"غ"],[126556,3],[126557,1,"ں"],[126558,3],[126559,1,"ٯ"],[126560,3],[126561,1,"ب"],[126562,1,"ج"],[126563,3],[126564,1,"ه"],[[126565,126566],3],[126567,1,"ح"],[126568,1,"ط"],[126569,1,"ي"],[126570,1,"ك"],[126571,3],[126572,1,"م"],[126573,1,"ن"],[126574,1,"س"],[126575,1,"ع"],[126576,1,"ف"],[126577,1,"ص"],[126578,1,"ق"],[126579,3],[126580,1,"ش"],[126581,1,"ت"],[126582,1,"ث"],[126583,1,"خ"],[126584,3],[126585,1,"ض"],[126586,1,"ظ"],[126587,1,"غ"],[126588,1,"ٮ"],[126589,3],[126590,1,"ڡ"],[126591,3],[126592,1,"ا"],[126593,1,"ب"],[126594,1,"ج"],[126595,1,"د"],[126596,1,"ه"],[126597,1,"و"],[126598,1,"ز"],[126599,1,"ح"],[126600,1,"ط"],[126601,1,"ي"],[126602,3],[126603,1,"ل"],[126604,1,"م"],[126605,1,"ن"],[126606,1,"س"],[126607,1,"ع"],[126608,1,"ف"],[126609,1,"ص"],[126610,1,"ق"],[126611,1,"ر"],[126612,1,"ش"],[126613,1,"ت"],[126614,1,"ث"],[126615,1,"خ"],[126616,1,"ذ"],[126617,1,"ض"],[126618,1,"ظ"],[126619,1,"غ"],[[126620,126624],3],[126625,1,"ب"],[126626,1,"ج"],[126627,1,"د"],[126628,3],[126629,1,"و"],[126630,1,"ز"],[126631,1,"ح"],[126632,1,"ط"],[126633,1,"ي"],[126634,3],[126635,1,"ل"],[126636,1,"م"],[126637,1,"ن"],[126638,1,"س"],[126639,1,"ع"],[126640,1,"ف"],[126641,1,"ص"],[126642,1,"ق"],[126643,1,"ر"],[126644,1,"ش"],[126645,1,"ت"],[126646,1,"ث"],[126647,1,"خ"],[126648,1,"ذ"],[126649,1,"ض"],[126650,1,"ظ"],[126651,1,"غ"],[[126652,126703],3],[[126704,126705],2],[[126706,126975],3],[[126976,127019],2],[[127020,127023],3],[[127024,127123],2],[[127124,127135],3],[[127136,127150],2],[[127151,127152],3],[[127153,127166],2],[127167,2],[127168,3],[[127169,127183],2],[127184,3],[[127185,127199],2],[[127200,127221],2],[[127222,127231],3],[127232,3],[127233,1,"0,"],[127234,1,"1,"],[127235,1,"2,"],[127236,1,"3,"],[127237,1,"4,"],[127238,1,"5,"],[127239,1,"6,"],[127240,1,"7,"],[127241,1,"8,"],[127242,1,"9,"],[[127243,127244],2],[[127245,127247],2],[127248,1,"(a)"],[127249,1,"(b)"],[127250,1,"(c)"],[127251,1,"(d)"],[127252,1,"(e)"],[127253,1,"(f)"],[127254,1,"(g)"],[127255,1,"(h)"],[127256,1,"(i)"],[127257,1,"(j)"],[127258,1,"(k)"],[127259,1,"(l)"],[127260,1,"(m)"],[127261,1,"(n)"],[127262,1,"(o)"],[127263,1,"(p)"],[127264,1,"(q)"],[127265,1,"(r)"],[127266,1,"(s)"],[127267,1,"(t)"],[127268,1,"(u)"],[127269,1,"(v)"],[127270,1,"(w)"],[127271,1,"(x)"],[127272,1,"(y)"],[127273,1,"(z)"],[127274,1,"〔s〕"],[127275,1,"c"],[127276,1,"r"],[127277,1,"cd"],[127278,1,"wz"],[127279,2],[127280,1,"a"],[127281,1,"b"],[127282,1,"c"],[127283,1,"d"],[127284,1,"e"],[127285,1,"f"],[127286,1,"g"],[127287,1,"h"],[127288,1,"i"],[127289,1,"j"],[127290,1,"k"],[127291,1,"l"],[127292,1,"m"],[127293,1,"n"],[127294,1,"o"],[127295,1,"p"],[127296,1,"q"],[127297,1,"r"],[127298,1,"s"],[127299,1,"t"],[127300,1,"u"],[127301,1,"v"],[127302,1,"w"],[127303,1,"x"],[127304,1,"y"],[127305,1,"z"],[127306,1,"hv"],[127307,1,"mv"],[127308,1,"sd"],[127309,1,"ss"],[127310,1,"ppv"],[127311,1,"wc"],[[127312,127318],2],[127319,2],[[127320,127326],2],[127327,2],[[127328,127337],2],[127338,1,"mc"],[127339,1,"md"],[127340,1,"mr"],[[127341,127343],2],[[127344,127352],2],[127353,2],[127354,2],[[127355,127356],2],[[127357,127358],2],[127359,2],[[127360,127369],2],[[127370,127373],2],[[127374,127375],2],[127376,1,"dj"],[[127377,127386],2],[[127387,127404],2],[127405,2],[[127406,127461],3],[[127462,127487],2],[127488,1,"ほか"],[127489,1,"ココ"],[127490,1,"サ"],[[127491,127503],3],[127504,1,"手"],[127505,1,"字"],[127506,1,"双"],[127507,1,"デ"],[127508,1,"二"],[127509,1,"多"],[127510,1,"解"],[127511,1,"天"],[127512,1,"交"],[127513,1,"映"],[127514,1,"無"],[127515,1,"料"],[127516,1,"前"],[127517,1,"後"],[127518,1,"再"],[127519,1,"新"],[127520,1,"初"],[127521,1,"終"],[127522,1,"生"],[127523,1,"販"],[127524,1,"声"],[127525,1,"吹"],[127526,1,"演"],[127527,1,"投"],[127528,1,"捕"],[127529,1,"一"],[127530,1,"三"],[127531,1,"遊"],[127532,1,"左"],[127533,1,"中"],[127534,1,"右"],[127535,1,"指"],[127536,1,"走"],[127537,1,"打"],[127538,1,"禁"],[127539,1,"空"],[127540,1,"合"],[127541,1,"満"],[127542,1,"有"],[127543,1,"月"],[127544,1,"申"],[127545,1,"割"],[127546,1,"営"],[127547,1,"配"],[[127548,127551],3],[127552,1,"〔本〕"],[127553,1,"〔三〕"],[127554,1,"〔二〕"],[127555,1,"〔安〕"],[127556,1,"〔点〕"],[127557,1,"〔打〕"],[127558,1,"〔盗〕"],[127559,1,"〔勝〕"],[127560,1,"〔敗〕"],[[127561,127567],3],[127568,1,"得"],[127569,1,"可"],[[127570,127583],3],[[127584,127589],2],[[127590,127743],3],[[127744,127776],2],[[127777,127788],2],[[127789,127791],2],[[127792,127797],2],[127798,2],[[127799,127868],2],[127869,2],[[127870,127871],2],[[127872,127891],2],[[127892,127903],2],[[127904,127940],2],[127941,2],[[127942,127946],2],[[127947,127950],2],[[127951,127955],2],[[127956,127967],2],[[127968,127984],2],[[127985,127991],2],[[127992,127999],2],[[128000,128062],2],[128063,2],[128064,2],[128065,2],[[128066,128247],2],[128248,2],[[128249,128252],2],[[128253,128254],2],[128255,2],[[128256,128317],2],[[128318,128319],2],[[128320,128323],2],[[128324,128330],2],[[128331,128335],2],[[128336,128359],2],[[128360,128377],2],[128378,2],[[128379,128419],2],[128420,2],[[128421,128506],2],[[128507,128511],2],[128512,2],[[128513,128528],2],[128529,2],[[128530,128532],2],[128533,2],[128534,2],[128535,2],[128536,2],[128537,2],[128538,2],[128539,2],[[128540,128542],2],[128543,2],[[128544,128549],2],[[128550,128551],2],[[128552,128555],2],[128556,2],[128557,2],[[128558,128559],2],[[128560,128563],2],[128564,2],[[128565,128576],2],[[128577,128578],2],[[128579,128580],2],[[128581,128591],2],[[128592,128639],2],[[128640,128709],2],[[128710,128719],2],[128720,2],[[128721,128722],2],[[128723,128724],2],[128725,2],[[128726,128727],2],[[128728,128731],3],[128732,2],[[128733,128735],2],[[128736,128748],2],[[128749,128751],3],[[128752,128755],2],[[128756,128758],2],[[128759,128760],2],[128761,2],[128762,2],[[128763,128764],2],[[128765,128767],3],[[128768,128883],2],[[128884,128886],2],[[128887,128890],3],[[128891,128895],2],[[128896,128980],2],[[128981,128984],2],[128985,2],[[128986,128991],3],[[128992,129003],2],[[129004,129007],3],[129008,2],[[129009,129023],3],[[129024,129035],2],[[129036,129039],3],[[129040,129095],2],[[129096,129103],3],[[129104,129113],2],[[129114,129119],3],[[129120,129159],2],[[129160,129167],3],[[129168,129197],2],[[129198,129199],3],[[129200,129201],2],[[129202,129211],2],[[129212,129215],3],[[129216,129217],2],[[129218,129279],3],[[129280,129291],2],[129292,2],[[129293,129295],2],[[129296,129304],2],[[129305,129310],2],[129311,2],[[129312,129319],2],[[129320,129327],2],[129328,2],[[129329,129330],2],[[129331,129342],2],[129343,2],[[129344,129355],2],[129356,2],[[129357,129359],2],[[129360,129374],2],[[129375,129387],2],[[129388,129392],2],[129393,2],[129394,2],[[129395,129398],2],[[129399,129400],2],[129401,2],[129402,2],[129403,2],[[129404,129407],2],[[129408,129412],2],[[129413,129425],2],[[129426,129431],2],[[129432,129442],2],[[129443,129444],2],[[129445,129450],2],[[129451,129453],2],[[129454,129455],2],[[129456,129465],2],[[129466,129471],2],[129472,2],[[129473,129474],2],[[129475,129482],2],[129483,2],[129484,2],[[129485,129487],2],[[129488,129510],2],[[129511,129535],2],[[129536,129619],2],[[129620,129631],3],[[129632,129645],2],[[129646,129647],3],[[129648,129651],2],[129652,2],[[129653,129655],2],[[129656,129658],2],[[129659,129660],2],[[129661,129663],3],[[129664,129666],2],[[129667,129670],2],[[129671,129672],2],[129673,2],[[129674,129678],3],[129679,2],[[129680,129685],2],[[129686,129704],2],[[129705,129708],2],[[129709,129711],2],[[129712,129718],2],[[129719,129722],2],[[129723,129725],2],[129726,2],[129727,2],[[129728,129730],2],[[129731,129733],2],[129734,2],[[129735,129741],3],[[129742,129743],2],[[129744,129750],2],[[129751,129753],2],[[129754,129755],2],[129756,2],[[129757,129758],3],[129759,2],[[129760,129767],2],[129768,2],[129769,2],[[129770,129775],3],[[129776,129782],2],[[129783,129784],2],[[129785,129791],3],[[129792,129938],2],[129939,3],[[129940,129994],2],[[129995,130031],2],[130032,1,"0"],[130033,1,"1"],[130034,1,"2"],[130035,1,"3"],[130036,1,"4"],[130037,1,"5"],[130038,1,"6"],[130039,1,"7"],[130040,1,"8"],[130041,1,"9"],[[130042,131069],3],[[131070,131071],3],[[131072,173782],2],[[173783,173789],2],[[173790,173791],2],[[173792,173823],3],[[173824,177972],2],[[177973,177976],2],[177977,2],[[177978,177983],3],[[177984,178205],2],[[178206,178207],3],[[178208,183969],2],[[183970,183983],3],[[183984,191456],2],[[191457,191471],3],[[191472,192093],2],[[192094,194559],3],[194560,1,"丽"],[194561,1,"丸"],[194562,1,"乁"],[194563,1,"𠄢"],[194564,1,"你"],[194565,1,"侮"],[194566,1,"侻"],[194567,1,"倂"],[194568,1,"偺"],[194569,1,"備"],[194570,1,"僧"],[194571,1,"像"],[194572,1,"㒞"],[194573,1,"𠘺"],[194574,1,"免"],[194575,1,"兔"],[194576,1,"兤"],[194577,1,"具"],[194578,1,"𠔜"],[194579,1,"㒹"],[194580,1,"內"],[194581,1,"再"],[194582,1,"𠕋"],[194583,1,"冗"],[194584,1,"冤"],[194585,1,"仌"],[194586,1,"冬"],[194587,1,"况"],[194588,1,"𩇟"],[194589,1,"凵"],[194590,1,"刃"],[194591,1,"㓟"],[194592,1,"刻"],[194593,1,"剆"],[194594,1,"割"],[194595,1,"剷"],[194596,1,"㔕"],[194597,1,"勇"],[194598,1,"勉"],[194599,1,"勤"],[194600,1,"勺"],[194601,1,"包"],[194602,1,"匆"],[194603,1,"北"],[194604,1,"卉"],[194605,1,"卑"],[194606,1,"博"],[194607,1,"即"],[194608,1,"卽"],[[194609,194611],1,"卿"],[194612,1,"𠨬"],[194613,1,"灰"],[194614,1,"及"],[194615,1,"叟"],[194616,1,"𠭣"],[194617,1,"叫"],[194618,1,"叱"],[194619,1,"吆"],[194620,1,"咞"],[194621,1,"吸"],[194622,1,"呈"],[194623,1,"周"],[194624,1,"咢"],[194625,1,"哶"],[194626,1,"唐"],[194627,1,"啓"],[194628,1,"啣"],[[194629,194630],1,"善"],[194631,1,"喙"],[194632,1,"喫"],[194633,1,"喳"],[194634,1,"嗂"],[194635,1,"圖"],[194636,1,"嘆"],[194637,1,"圗"],[194638,1,"噑"],[194639,1,"噴"],[194640,1,"切"],[194641,1,"壮"],[194642,1,"城"],[194643,1,"埴"],[194644,1,"堍"],[194645,1,"型"],[194646,1,"堲"],[194647,1,"報"],[194648,1,"墬"],[194649,1,"𡓤"],[194650,1,"売"],[194651,1,"壷"],[194652,1,"夆"],[194653,1,"多"],[194654,1,"夢"],[194655,1,"奢"],[194656,1,"𡚨"],[194657,1,"𡛪"],[194658,1,"姬"],[194659,1,"娛"],[194660,1,"娧"],[194661,1,"姘"],[194662,1,"婦"],[194663,1,"㛮"],[194664,1,"㛼"],[194665,1,"嬈"],[[194666,194667],1,"嬾"],[194668,1,"𡧈"],[194669,1,"寃"],[194670,1,"寘"],[194671,1,"寧"],[194672,1,"寳"],[194673,1,"𡬘"],[194674,1,"寿"],[194675,1,"将"],[194676,1,"当"],[194677,1,"尢"],[194678,1,"㞁"],[194679,1,"屠"],[194680,1,"屮"],[194681,1,"峀"],[194682,1,"岍"],[194683,1,"𡷤"],[194684,1,"嵃"],[194685,1,"𡷦"],[194686,1,"嵮"],[194687,1,"嵫"],[194688,1,"嵼"],[194689,1,"巡"],[194690,1,"巢"],[194691,1,"㠯"],[194692,1,"巽"],[194693,1,"帨"],[194694,1,"帽"],[194695,1,"幩"],[194696,1,"㡢"],[194697,1,"𢆃"],[194698,1,"㡼"],[194699,1,"庰"],[194700,1,"庳"],[194701,1,"庶"],[194702,1,"廊"],[194703,1,"𪎒"],[194704,1,"廾"],[[194705,194706],1,"𢌱"],[194707,1,"舁"],[[194708,194709],1,"弢"],[194710,1,"㣇"],[194711,1,"𣊸"],[194712,1,"𦇚"],[194713,1,"形"],[194714,1,"彫"],[194715,1,"㣣"],[194716,1,"徚"],[194717,1,"忍"],[194718,1,"志"],[194719,1,"忹"],[194720,1,"悁"],[194721,1,"㤺"],[194722,1,"㤜"],[194723,1,"悔"],[194724,1,"𢛔"],[194725,1,"惇"],[194726,1,"慈"],[194727,1,"慌"],[194728,1,"慎"],[194729,1,"慌"],[194730,1,"慺"],[194731,1,"憎"],[194732,1,"憲"],[194733,1,"憤"],[194734,1,"憯"],[194735,1,"懞"],[194736,1,"懲"],[194737,1,"懶"],[194738,1,"成"],[194739,1,"戛"],[194740,1,"扝"],[194741,1,"抱"],[194742,1,"拔"],[194743,1,"捐"],[194744,1,"𢬌"],[194745,1,"挽"],[194746,1,"拼"],[194747,1,"捨"],[194748,1,"掃"],[194749,1,"揤"],[194750,1,"𢯱"],[194751,1,"搢"],[194752,1,"揅"],[194753,1,"掩"],[194754,1,"㨮"],[194755,1,"摩"],[194756,1,"摾"],[194757,1,"撝"],[194758,1,"摷"],[194759,1,"㩬"],[194760,1,"敏"],[194761,1,"敬"],[194762,1,"𣀊"],[194763,1,"旣"],[194764,1,"書"],[194765,1,"晉"],[194766,1,"㬙"],[194767,1,"暑"],[194768,1,"㬈"],[194769,1,"㫤"],[194770,1,"冒"],[194771,1,"冕"],[194772,1,"最"],[194773,1,"暜"],[194774,1,"肭"],[194775,1,"䏙"],[194776,1,"朗"],[194777,1,"望"],[194778,1,"朡"],[194779,1,"杞"],[194780,1,"杓"],[194781,1,"𣏃"],[194782,1,"㭉"],[194783,1,"柺"],[194784,1,"枅"],[194785,1,"桒"],[194786,1,"梅"],[194787,1,"𣑭"],[194788,1,"梎"],[194789,1,"栟"],[194790,1,"椔"],[194791,1,"㮝"],[194792,1,"楂"],[194793,1,"榣"],[194794,1,"槪"],[194795,1,"檨"],[194796,1,"𣚣"],[194797,1,"櫛"],[194798,1,"㰘"],[194799,1,"次"],[194800,1,"𣢧"],[194801,1,"歔"],[194802,1,"㱎"],[194803,1,"歲"],[194804,1,"殟"],[194805,1,"殺"],[194806,1,"殻"],[194807,1,"𣪍"],[194808,1,"𡴋"],[194809,1,"𣫺"],[194810,1,"汎"],[194811,1,"𣲼"],[194812,1,"沿"],[194813,1,"泍"],[194814,1,"汧"],[194815,1,"洖"],[194816,1,"派"],[194817,1,"海"],[194818,1,"流"],[194819,1,"浩"],[194820,1,"浸"],[194821,1,"涅"],[194822,1,"𣴞"],[194823,1,"洴"],[194824,1,"港"],[194825,1,"湮"],[194826,1,"㴳"],[194827,1,"滋"],[194828,1,"滇"],[194829,1,"𣻑"],[194830,1,"淹"],[194831,1,"潮"],[194832,1,"𣽞"],[194833,1,"𣾎"],[194834,1,"濆"],[194835,1,"瀹"],[194836,1,"瀞"],[194837,1,"瀛"],[194838,1,"㶖"],[194839,1,"灊"],[194840,1,"災"],[194841,1,"灷"],[194842,1,"炭"],[194843,1,"𠔥"],[194844,1,"煅"],[194845,1,"𤉣"],[194846,1,"熜"],[194847,1,"𤎫"],[194848,1,"爨"],[194849,1,"爵"],[194850,1,"牐"],[194851,1,"𤘈"],[194852,1,"犀"],[194853,1,"犕"],[194854,1,"𤜵"],[194855,1,"𤠔"],[194856,1,"獺"],[194857,1,"王"],[194858,1,"㺬"],[194859,1,"玥"],[[194860,194861],1,"㺸"],[194862,1,"瑇"],[194863,1,"瑜"],[194864,1,"瑱"],[194865,1,"璅"],[194866,1,"瓊"],[194867,1,"㼛"],[194868,1,"甤"],[194869,1,"𤰶"],[194870,1,"甾"],[194871,1,"𤲒"],[194872,1,"異"],[194873,1,"𢆟"],[194874,1,"瘐"],[194875,1,"𤾡"],[194876,1,"𤾸"],[194877,1,"𥁄"],[194878,1,"㿼"],[194879,1,"䀈"],[194880,1,"直"],[194881,1,"𥃳"],[194882,1,"𥃲"],[194883,1,"𥄙"],[194884,1,"𥄳"],[194885,1,"眞"],[[194886,194887],1,"真"],[194888,1,"睊"],[194889,1,"䀹"],[194890,1,"瞋"],[194891,1,"䁆"],[194892,1,"䂖"],[194893,1,"𥐝"],[194894,1,"硎"],[194895,1,"碌"],[194896,1,"磌"],[194897,1,"䃣"],[194898,1,"𥘦"],[194899,1,"祖"],[194900,1,"𥚚"],[194901,1,"𥛅"],[194902,1,"福"],[194903,1,"秫"],[194904,1,"䄯"],[194905,1,"穀"],[194906,1,"穊"],[194907,1,"穏"],[194908,1,"𥥼"],[[194909,194910],1,"𥪧"],[194911,1,"竮"],[194912,1,"䈂"],[194913,1,"𥮫"],[194914,1,"篆"],[194915,1,"築"],[194916,1,"䈧"],[194917,1,"𥲀"],[194918,1,"糒"],[194919,1,"䊠"],[194920,1,"糨"],[194921,1,"糣"],[194922,1,"紀"],[194923,1,"𥾆"],[194924,1,"絣"],[194925,1,"䌁"],[194926,1,"緇"],[194927,1,"縂"],[194928,1,"繅"],[194929,1,"䌴"],[194930,1,"𦈨"],[194931,1,"𦉇"],[194932,1,"䍙"],[194933,1,"𦋙"],[194934,1,"罺"],[194935,1,"𦌾"],[194936,1,"羕"],[194937,1,"翺"],[194938,1,"者"],[194939,1,"𦓚"],[194940,1,"𦔣"],[194941,1,"聠"],[194942,1,"𦖨"],[194943,1,"聰"],[194944,1,"𣍟"],[194945,1,"䏕"],[194946,1,"育"],[194947,1,"脃"],[194948,1,"䐋"],[194949,1,"脾"],[194950,1,"媵"],[194951,1,"𦞧"],[194952,1,"𦞵"],[194953,1,"𣎓"],[194954,1,"𣎜"],[194955,1,"舁"],[194956,1,"舄"],[194957,1,"辞"],[194958,1,"䑫"],[194959,1,"芑"],[194960,1,"芋"],[194961,1,"芝"],[194962,1,"劳"],[194963,1,"花"],[194964,1,"芳"],[194965,1,"芽"],[194966,1,"苦"],[194967,1,"𦬼"],[194968,1,"若"],[194969,1,"茝"],[194970,1,"荣"],[194971,1,"莭"],[194972,1,"茣"],[194973,1,"莽"],[194974,1,"菧"],[194975,1,"著"],[194976,1,"荓"],[194977,1,"菊"],[194978,1,"菌"],[194979,1,"菜"],[194980,1,"𦰶"],[194981,1,"𦵫"],[194982,1,"𦳕"],[194983,1,"䔫"],[194984,1,"蓱"],[194985,1,"蓳"],[194986,1,"蔖"],[194987,1,"𧏊"],[194988,1,"蕤"],[194989,1,"𦼬"],[194990,1,"䕝"],[194991,1,"䕡"],[194992,1,"𦾱"],[194993,1,"𧃒"],[194994,1,"䕫"],[194995,1,"虐"],[194996,1,"虜"],[194997,1,"虧"],[194998,1,"虩"],[194999,1,"蚩"],[195000,1,"蚈"],[195001,1,"蜎"],[195002,1,"蛢"],[195003,1,"蝹"],[195004,1,"蜨"],[195005,1,"蝫"],[195006,1,"螆"],[195007,1,"䗗"],[195008,1,"蟡"],[195009,1,"蠁"],[195010,1,"䗹"],[195011,1,"衠"],[195012,1,"衣"],[195013,1,"𧙧"],[195014,1,"裗"],[195015,1,"裞"],[195016,1,"䘵"],[195017,1,"裺"],[195018,1,"㒻"],[195019,1,"𧢮"],[195020,1,"𧥦"],[195021,1,"䚾"],[195022,1,"䛇"],[195023,1,"誠"],[195024,1,"諭"],[195025,1,"變"],[195026,1,"豕"],[195027,1,"𧲨"],[195028,1,"貫"],[195029,1,"賁"],[195030,1,"贛"],[195031,1,"起"],[195032,1,"𧼯"],[195033,1,"𠠄"],[195034,1,"跋"],[195035,1,"趼"],[195036,1,"跰"],[195037,1,"𠣞"],[195038,1,"軔"],[195039,1,"輸"],[195040,1,"𨗒"],[195041,1,"𨗭"],[195042,1,"邔"],[195043,1,"郱"],[195044,1,"鄑"],[195045,1,"𨜮"],[195046,1,"鄛"],[195047,1,"鈸"],[195048,1,"鋗"],[195049,1,"鋘"],[195050,1,"鉼"],[195051,1,"鏹"],[195052,1,"鐕"],[195053,1,"𨯺"],[195054,1,"開"],[195055,1,"䦕"],[195056,1,"閷"],[195057,1,"𨵷"],[195058,1,"䧦"],[195059,1,"雃"],[195060,1,"嶲"],[195061,1,"霣"],[195062,1,"𩅅"],[195063,1,"𩈚"],[195064,1,"䩮"],[195065,1,"䩶"],[195066,1,"韠"],[195067,1,"𩐊"],[195068,1,"䪲"],[195069,1,"𩒖"],[[195070,195071],1,"頋"],[195072,1,"頩"],[195073,1,"𩖶"],[195074,1,"飢"],[195075,1,"䬳"],[195076,1,"餩"],[195077,1,"馧"],[195078,1,"駂"],[195079,1,"駾"],[195080,1,"䯎"],[195081,1,"𩬰"],[195082,1,"鬒"],[195083,1,"鱀"],[195084,1,"鳽"],[195085,1,"䳎"],[195086,1,"䳭"],[195087,1,"鵧"],[195088,1,"𪃎"],[195089,1,"䳸"],[195090,1,"𪄅"],[195091,1,"𪈎"],[195092,1,"𪊑"],[195093,1,"麻"],[195094,1,"䵖"],[195095,1,"黹"],[195096,1,"黾"],[195097,1,"鼅"],[195098,1,"鼏"],[195099,1,"鼖"],[195100,1,"鼻"],[195101,1,"𪘀"],[[195102,196605],3],[[196606,196607],3],[[196608,201546],2],[[201547,201551],3],[[201552,205743],2],[[205744,262141],3],[[262142,262143],3],[[262144,327677],3],[[327678,327679],3],[[327680,393213],3],[[393214,393215],3],[[393216,458749],3],[[458750,458751],3],[[458752,524285],3],[[524286,524287],3],[[524288,589821],3],[[589822,589823],3],[[589824,655357],3],[[655358,655359],3],[[655360,720893],3],[[720894,720895],3],[[720896,786429],3],[[786430,786431],3],[[786432,851965],3],[[851966,851967],3],[[851968,917501],3],[[917502,917503],3],[917504,3],[917505,3],[[917506,917535],3],[[917536,917631],3],[[917632,917759],3],[[917760,917999],7],[[918000,983037],3],[[983038,983039],3],[[983040,1048573],3],[[1048574,1048575],3],[[1048576,1114109],3],[[1114110,1114111],3]]});
var PKB=E((OKB)=>{Object.defineProperty(OKB,"__esModule",{value:!0});OKB.Colours=void 0;class f4{static isEnabled(A){return A.isTTY&&(typeof A.getColorDepth==="function"?A.getColorDepth()>2:!0)}static refresh(){if(f4.enabled=f4.isEnabled(process.stderr),!this.enabled)f4.reset="",f4.bright="",f4.dim="",f4.red="",f4.green="",f4.yellow="",f4.blue="",f4.magenta="",f4.cyan="",f4.white="",f4.grey="";else f4.reset="\x1B[0m",f4.bright="\x1B[1m",f4.dim="\x1B[2m",f4.red="\x1B[31m",f4.green="\x1B[32m",f4.yellow="\x1B[33m",f4.blue="\x1B[34m",f4.magenta="\x1B[35m",f4.cyan="\x1B[36m",f4.white="\x1B[37m",f4.grey="\x1B[90m"}}OKB.Colours=f4;f4.enabled=!1;f4.reset="";f4.bright="";f4.dim="";f4.red="";f4.green="";f4.yellow="";f4.blue="";f4.magenta="";f4.cyan="";f4.white="";f4.grey="";f4.refresh()});
var PM=E((HHB)=>{Object.defineProperty(HHB,"__esModule",{value:!0});HHB.AuthClient=HHB.DEFAULT_EAGER_REFRESH_THRESHOLD_MILLIS=HHB.DEFAULT_UNIVERSE=void 0;var go6=J1("events"),VHB=X$(),CHB=sD1(),uo6=lx();HHB.DEFAULT_UNIVERSE="googleapis.com";HHB.DEFAULT_EAGER_REFRESH_THRESHOLD_MILLIS=300000;class KHB extends go6.EventEmitter{constructor(A={}){var B,Q,D,Z,G;super();this.credentials={},this.eagerRefreshThresholdMillis=HHB.DEFAULT_EAGER_REFRESH_THRESHOLD_MILLIS,this.forceRefreshOnFailure=!1,this.universeDomain=HHB.DEFAULT_UNIVERSE;let F=uo6.originalOrCamelOptions(A);if(this.apiKey=A.apiKey,this.projectId=(B=F.get("project_id"))!==null&&B!==void 0?B:null,this.quotaProjectId=F.get("quota_project_id"),this.credentials=(Q=F.get("credentials"))!==null&&Q!==void 0?Q:{},this.universeDomain=(D=F.get("universe_domain"))!==null&&D!==void 0?D:HHB.DEFAULT_UNIVERSE,this.transporter=(Z=A.transporter)!==null&&Z!==void 0?Z:new CHB.DefaultTransporter,A.transporterOptions)this.transporter.defaults=A.transporterOptions;if(A.eagerRefreshThresholdMillis)this.eagerRefreshThresholdMillis=A.eagerRefreshThresholdMillis;this.forceRefreshOnFailure=(G=A.forceRefreshOnFailure)!==null&&G!==void 0?G:!1}get gaxios(){if(this.transporter instanceof VHB.Gaxios)return this.transporter;else if(this.transporter instanceof CHB.DefaultTransporter)return this.transporter.instance;else if("instance"in this.transporter&&this.transporter.instance instanceof VHB.Gaxios)return this.transporter.instance;return null}setCredentials(A){this.credentials=A}addSharedMetadataHeaders(A){if(!A["x-goog-user-project"]&&this.quotaProjectId)A["x-goog-user-project"]=this.quotaProjectId;return A}static get RETRY_CONFIG(){return{retry:!0,retryConfig:{httpMethodsToRetry:["GET","PUT","POST","HEAD","OPTIONS","DELETE"]}}}}HHB.AuthClient=KHB});
var Pz0=E((Sa6,GX)=>{var Da6=SVB(),cY=$z0(),{utf8DecodeWithoutBOM:Za6}=ty1(),{percentDecodeString:Ga6,utf8PercentEncodeCodePoint:B_1,utf8PercentEncodeString:Q_1,isC0ControlPercentEncode:lVB,isFragmentPercentEncode:Fa6,isQueryPercentEncode:Ia6,isSpecialQueryPercentEncode:Ya6,isPathPercentEncode:Wa6,isUserinfoPercentEncode:Rz0}=ey1();function B2(A){return A.codePointAt(0)}var pVB={ftp:21,file:null,http:80,https:443,ws:80,wss:443},nQ=Symbol("failure");function uVB(A){return[...A].length}function mVB(A,B){let Q=A[B];return isNaN(Q)?void 0:String.fromCodePoint(Q)}function dVB(A){return A==="."||A.toLowerCase()==="%2e"}function Ja6(A){return A=A.toLowerCase(),A===".."||A==="%2e."||A===".%2e"||A==="%2e%2e"}function Xa6(A,B){return cY.isASCIIAlpha(A)&&(B===B2(":")||B===B2("|"))}function iVB(A){return A.length===2&&cY.isASCIIAlpha(A.codePointAt(0))&&(A[1]===":"||A[1]==="|")}function Va6(A){return A.length===2&&cY.isASCIIAlpha(A.codePointAt(0))&&A[1]===":"}function nVB(A){return A.search(/\u0000|\u0009|\u000A|\u000D|\u0020|#|\/|:|<|>|\?|@|\[|\\|\]|\^|\|/u)!==-1}function Ca6(A){return nVB(A)||A.search(/[\u0000-\u001F]|%|\u007F/u)!==-1}function A_1(A){return pVB[A]!==void 0}function dY(A){return A_1(A.scheme)}function Lz0(A){return!A_1(A.scheme)}function aVB(A){return pVB[A]}function sVB(A){if(A==="")return nQ;let B=10;if(A.length>=2&&A.charAt(0)==="0"&&A.charAt(1).toLowerCase()==="x")A=A.substring(2),B=16;else if(A.length>=2&&A.charAt(0)==="0")A=A.substring(1),B=8;if(A==="")return 0;let Q=/[^0-7]/u;if(B===10)Q=/[^0-9]/u;if(B===16)Q=/[^0-9A-Fa-f]/u;if(Q.test(A))return nQ;return parseInt(A,B)}function Ka6(A){let B=A.split(".");if(B[B.length-1]===""){if(B.length>1)B.pop()}if(B.length>4)return nQ;let Q=[];for(let G of B){let F=sVB(G);if(F===nQ)return nQ;Q.push(F)}for(let G=0;G<Q.length-1;++G)if(Q[G]>255)return nQ;if(Q[Q.length-1]>=256**(5-Q.length))return nQ;let D=Q.pop(),Z=0;for(let G of Q)D+=G*256**(3-Z),++Z;return D}function Ha6(A){let B="",Q=A;for(let D=1;D<=4;++D){if(B=String(Q%256)+B,D!==4)B=`.${B}`;Q=Math.floor(Q/256)}return B}function za6(A){let B=[0,0,0,0,0,0,0,0],Q=0,D=null,Z=0;if(A=Array.from(A,(G)=>G.codePointAt(0)),A[Z]===B2(":")){if(A[Z+1]!==B2(":"))return nQ;Z+=2,++Q,D=Q}while(Z<A.length){if(Q===8)return nQ;if(A[Z]===B2(":")){if(D!==null)return nQ;++Z,++Q,D=Q;continue}let G=0,F=0;while(F<4&&cY.isASCIIHex(A[Z]))G=G*16+parseInt(mVB(A,Z),16),++Z,++F;if(A[Z]===B2(".")){if(F===0)return nQ;if(Z-=F,Q>6)return nQ;let I=0;while(A[Z]!==void 0){let Y=null;if(I>0)if(A[Z]===B2(".")&&I<4)++Z;else return nQ;if(!cY.isASCIIDigit(A[Z]))return nQ;while(cY.isASCIIDigit(A[Z])){let W=parseInt(mVB(A,Z));if(Y===null)Y=W;else if(Y===0)return nQ;else Y=Y*10+W;if(Y>255)return nQ;++Z}if(B[Q]=B[Q]*256+Y,++I,I===2||I===4)++Q}if(I!==4)return nQ;break}else if(A[Z]===B2(":")){if(++Z,A[Z]===void 0)return nQ}else if(A[Z]!==void 0)return nQ;B[Q]=G,++Q}if(D!==null){let G=Q-D;Q=7;while(Q!==0&&G>0){let F=B[D+G-1];B[D+G-1]=B[Q],B[Q]=F,--Q,--G}}else if(D===null&&Q!==8)return nQ;return B}function Ea6(A){let B="",Q=$a6(A),D=!1;for(let Z=0;Z<=7;++Z){if(D&&A[Z]===0)continue;else if(D)D=!1;if(Q===Z){B+=Z===0?"::":":",D=!0;continue}if(B+=A[Z].toString(16),Z!==7)B+=":"}return B}function Mz0(A,B=!1){if(A[0]==="["){if(A[A.length-1]!=="]")return nQ;return za6(A.substring(1,A.length-1))}if(B)return wa6(A);let Q=Za6(Ga6(A)),D=qa6(Q);if(D===nQ)return nQ;if(Ua6(D))return Ka6(D);return D}function Ua6(A){let B=A.split(".");if(B[B.length-1]===""){if(B.length===1)return!1;B.pop()}let Q=B[B.length-1];if(sVB(Q)!==nQ)return!0;if(/^[0-9]+$/u.test(Q))return!0;return!1}function wa6(A){if(nVB(A))return nQ;return Q_1(A,lVB)}function $a6(A){let B=null,Q=1,D=null,Z=0;for(let G=0;G<A.length;++G)if(A[G]!==0){if(Z>Q)B=D,Q=Z;D=null,Z=0}else{if(D===null)D=G;++Z}if(Z>Q)return D;return B}function Oz0(A){if(typeof A==="number")return Ha6(A);if(A instanceof Array)return`[${Ea6(A)}]`;return A}function qa6(A,B=!1){let Q=Da6.toASCII(A,{checkHyphens:B,checkBidi:!0,checkJoiners:!0,useSTD3ASCIIRules:B,transitionalProcessing:!1,verifyDNSLength:B,ignoreInvalidPunycode:!1});if(Q===null)return nQ;if(!B){if(Q==="")return nQ;if(Ca6(Q))return nQ}return Q}function Na6(A){let B=0,Q=A.length;for(;B<Q;++B)if(A.charCodeAt(B)>32)break;for(;Q>B;--Q)if(A.charCodeAt(Q-1)>32)break;return A.substring(B,Q)}function La6(A){return A.replace(/\u0009|\u000A|\u000D/ug,"")}function rVB(A){let{path:B}=A;if(B.length===0)return;if(A.scheme==="file"&&B.length===1&&Ra6(B[0]))return;B.pop()}function oVB(A){return A.username!==""||A.password!==""}function Ma6(A){return A.host===null||A.host===""||A.scheme==="file"}function uD1(A){return typeof A.path==="string"}function Ra6(A){return/^[A-Za-z]:$/u.test(A)}function WZ(A,B,Q,D,Z){if(this.pointer=0,this.input=A,this.base=B||null,this.encodingOverride=Q||"utf-8",this.stateOverride=Z,this.url=D,this.failure=!1,this.parseError=!1,!this.url){this.url={scheme:"",username:"",password:"",host:null,port:null,path:[],query:null,fragment:null};let F=Na6(this.input);if(F!==this.input)this.parseError=!0;this.input=F}let G=La6(this.input);if(G!==this.input)this.parseError=!0;this.input=G,this.state=Z||"scheme start",this.buffer="",this.atFlag=!1,this.arrFlag=!1,this.passwordTokenSeenFlag=!1,this.input=Array.from(this.input,(F)=>F.codePointAt(0));for(;this.pointer<=this.input.length;++this.pointer){let F=this.input[this.pointer],I=isNaN(F)?void 0:String.fromCodePoint(F),Y=this[`parse ${this.state}`](F,I);if(!Y)break;else if(Y===nQ){this.failure=!0;break}}}WZ.prototype["parse scheme start"]=function A(B,Q){if(cY.isASCIIAlpha(B))this.buffer+=Q.toLowerCase(),this.state="scheme";else if(!this.stateOverride)this.state="no scheme",--this.pointer;else return this.parseError=!0,nQ;return!0};WZ.prototype["parse scheme"]=function A(B,Q){if(cY.isASCIIAlphanumeric(B)||B===B2("+")||B===B2("-")||B===B2("."))this.buffer+=Q.toLowerCase();else if(B===B2(":")){if(this.stateOverride){if(dY(this.url)&&!A_1(this.buffer))return!1;if(!dY(this.url)&&A_1(this.buffer))return!1;if((oVB(this.url)||this.url.port!==null)&&this.buffer==="file")return!1;if(this.url.scheme==="file"&&this.url.host==="")return!1}if(this.url.scheme=this.buffer,this.stateOverride){if(this.url.port===aVB(this.url.scheme))this.url.port=null;return!1}if(this.buffer="",this.url.scheme==="file"){if(this.input[this.pointer+1]!==B2("/")||this.input[this.pointer+2]!==B2("/"))this.parseError=!0;this.state="file"}else if(dY(this.url)&&this.base!==null&&this.base.scheme===this.url.scheme)this.state="special relative or authority";else if(dY(this.url))this.state="special authority slashes";else if(this.input[this.pointer+1]===B2("/"))this.state="path or authority",++this.pointer;else this.url.path="",this.state="opaque path"}else if(!this.stateOverride)this.buffer="",this.state="no scheme",this.pointer=-1;else return this.parseError=!0,nQ;return!0};WZ.prototype["parse no scheme"]=function A(B){if(this.base===null||uD1(this.base)&&B!==B2("#"))return nQ;else if(uD1(this.base)&&B===B2("#"))this.url.scheme=this.base.scheme,this.url.path=this.base.path,this.url.query=this.base.query,this.url.fragment="",this.state="fragment";else if(this.base.scheme==="file")this.state="file",--this.pointer;else this.state="relative",--this.pointer;return!0};WZ.prototype["parse special relative or authority"]=function A(B){if(B===B2("/")&&this.input[this.pointer+1]===B2("/"))this.state="special authority ignore slashes",++this.pointer;else this.parseError=!0,this.state="relative",--this.pointer;return!0};WZ.prototype["parse path or authority"]=function A(B){if(B===B2("/"))this.state="authority";else this.state="path",--this.pointer;return!0};WZ.prototype["parse relative"]=function A(B){if(this.url.scheme=this.base.scheme,B===B2("/"))this.state="relative slash";else if(dY(this.url)&&B===B2("\\"))this.parseError=!0,this.state="relative slash";else if(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.url.path=this.base.path.slice(),this.url.query=this.base.query,B===B2("?"))this.url.query="",this.state="query";else if(B===B2("#"))this.url.fragment="",this.state="fragment";else if(!isNaN(B))this.url.query=null,this.url.path.pop(),this.state="path",--this.pointer;return!0};WZ.prototype["parse relative slash"]=function A(B){if(dY(this.url)&&(B===B2("/")||B===B2("\\"))){if(B===B2("\\"))this.parseError=!0;this.state="special authority ignore slashes"}else if(B===B2("/"))this.state="authority";else this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.state="path",--this.pointer;return!0};WZ.prototype["parse special authority slashes"]=function A(B){if(B===B2("/")&&this.input[this.pointer+1]===B2("/"))this.state="special authority ignore slashes",++this.pointer;else this.parseError=!0,this.state="special authority ignore slashes",--this.pointer;return!0};WZ.prototype["parse special authority ignore slashes"]=function A(B){if(B!==B2("/")&&B!==B2("\\"))this.state="authority",--this.pointer;else this.parseError=!0;return!0};WZ.prototype["parse authority"]=function A(B,Q){if(B===B2("@")){if(this.parseError=!0,this.atFlag)this.buffer=`%40${this.buffer}`;this.atFlag=!0;let D=uVB(this.buffer);for(let Z=0;Z<D;++Z){let G=this.buffer.codePointAt(Z);if(G===B2(":")&&!this.passwordTokenSeenFlag){this.passwordTokenSeenFlag=!0;continue}let F=B_1(G,Rz0);if(this.passwordTokenSeenFlag)this.url.password+=F;else this.url.username+=F}this.buffer=""}else if(isNaN(B)||B===B2("/")||B===B2("?")||B===B2("#")||dY(this.url)&&B===B2("\\")){if(this.atFlag&&this.buffer==="")return this.parseError=!0,nQ;this.pointer-=uVB(this.buffer)+1,this.buffer="",this.state="host"}else this.buffer+=Q;return!0};WZ.prototype["parse hostname"]=WZ.prototype["parse host"]=function A(B,Q){if(this.stateOverride&&this.url.scheme==="file")--this.pointer,this.state="file host";else if(B===B2(":")&&!this.arrFlag){if(this.buffer==="")return this.parseError=!0,nQ;if(this.stateOverride==="hostname")return!1;let D=Mz0(this.buffer,Lz0(this.url));if(D===nQ)return nQ;this.url.host=D,this.buffer="",this.state="port"}else if(isNaN(B)||B===B2("/")||B===B2("?")||B===B2("#")||dY(this.url)&&B===B2("\\")){if(--this.pointer,dY(this.url)&&this.buffer==="")return this.parseError=!0,nQ;else if(this.stateOverride&&this.buffer===""&&(oVB(this.url)||this.url.port!==null))return this.parseError=!0,!1;let D=Mz0(this.buffer,Lz0(this.url));if(D===nQ)return nQ;if(this.url.host=D,this.buffer="",this.state="path start",this.stateOverride)return!1}else{if(B===B2("["))this.arrFlag=!0;else if(B===B2("]"))this.arrFlag=!1;this.buffer+=Q}return!0};WZ.prototype["parse port"]=function A(B,Q){if(cY.isASCIIDigit(B))this.buffer+=Q;else if(isNaN(B)||B===B2("/")||B===B2("?")||B===B2("#")||dY(this.url)&&B===B2("\\")||this.stateOverride){if(this.buffer!==""){let D=parseInt(this.buffer);if(D>65535)return this.parseError=!0,nQ;this.url.port=D===aVB(this.url.scheme)?null:D,this.buffer=""}if(this.stateOverride)return!1;this.state="path start",--this.pointer}else return this.parseError=!0,nQ;return!0};var Oa6=new Set([B2("/"),B2("\\"),B2("?"),B2("#")]);function tVB(A,B){let Q=A.length-B;return Q>=2&&Xa6(A[B],A[B+1])&&(Q===2||Oa6.has(A[B+2]))}WZ.prototype["parse file"]=function A(B){if(this.url.scheme="file",this.url.host="",B===B2("/")||B===B2("\\")){if(B===B2("\\"))this.parseError=!0;this.state="file slash"}else if(this.base!==null&&this.base.scheme==="file"){if(this.url.host=this.base.host,this.url.path=this.base.path.slice(),this.url.query=this.base.query,B===B2("?"))this.url.query="",this.state="query";else if(B===B2("#"))this.url.fragment="",this.state="fragment";else if(!isNaN(B)){if(this.url.query=null,!tVB(this.input,this.pointer))rVB(this.url);else this.parseError=!0,this.url.path=[];this.state="path",--this.pointer}}else this.state="path",--this.pointer;return!0};WZ.prototype["parse file slash"]=function A(B){if(B===B2("/")||B===B2("\\")){if(B===B2("\\"))this.parseError=!0;this.state="file host"}else{if(this.base!==null&&this.base.scheme==="file"){if(!tVB(this.input,this.pointer)&&Va6(this.base.path[0]))this.url.path.push(this.base.path[0]);this.url.host=this.base.host}this.state="path",--this.pointer}return!0};WZ.prototype["parse file host"]=function A(B,Q){if(isNaN(B)||B===B2("/")||B===B2("\\")||B===B2("?")||B===B2("#"))if(--this.pointer,!this.stateOverride&&iVB(this.buffer))this.parseError=!0,this.state="path";else if(this.buffer===""){if(this.url.host="",this.stateOverride)return!1;this.state="path start"}else{let D=Mz0(this.buffer,Lz0(this.url));if(D===nQ)return nQ;if(D==="localhost")D="";if(this.url.host=D,this.stateOverride)return!1;this.buffer="",this.state="path start"}else this.buffer+=Q;return!0};WZ.prototype["parse path start"]=function A(B){if(dY(this.url)){if(B===B2("\\"))this.parseError=!0;if(this.state="path",B!==B2("/")&&B!==B2("\\"))--this.pointer}else if(!this.stateOverride&&B===B2("?"))this.url.query="",this.state="query";else if(!this.stateOverride&&B===B2("#"))this.url.fragment="",this.state="fragment";else if(B!==void 0){if(this.state="path",B!==B2("/"))--this.pointer}else if(this.stateOverride&&this.url.host===null)this.url.path.push("");return!0};WZ.prototype["parse path"]=function A(B){if(isNaN(B)||B===B2("/")||dY(this.url)&&B===B2("\\")||!this.stateOverride&&(B===B2("?")||B===B2("#"))){if(dY(this.url)&&B===B2("\\"))this.parseError=!0;if(Ja6(this.buffer)){if(rVB(this.url),B!==B2("/")&&!(dY(this.url)&&B===B2("\\")))this.url.path.push("")}else if(dVB(this.buffer)&&B!==B2("/")&&!(dY(this.url)&&B===B2("\\")))this.url.path.push("");else if(!dVB(this.buffer)){if(this.url.scheme==="file"&&this.url.path.length===0&&iVB(this.buffer))this.buffer=`${this.buffer[0]}:`;this.url.path.push(this.buffer)}if(this.buffer="",B===B2("?"))this.url.query="",this.state="query";if(B===B2("#"))this.url.fragment="",this.state="fragment"}else{if(B===B2("%")&&(!cY.isASCIIHex(this.input[this.pointer+1])||!cY.isASCIIHex(this.input[this.pointer+2])))this.parseError=!0;this.buffer+=B_1(B,Wa6)}return!0};WZ.prototype["parse opaque path"]=function A(B){if(B===B2("?"))this.url.query="",this.state="query";else if(B===B2("#"))this.url.fragment="",this.state="fragment";else if(B===B2(" ")){let Q=this.input[this.pointer+1];if(Q===B2("?")||Q===B2("#"))this.url.path+="%20";else this.url.path+=" "}else{if(!isNaN(B)&&B!==B2("%"))this.parseError=!0;if(B===B2("%")&&(!cY.isASCIIHex(this.input[this.pointer+1])||!cY.isASCIIHex(this.input[this.pointer+2])))this.parseError=!0;if(!isNaN(B))this.url.path+=B_1(B,lVB)}return!0};WZ.prototype["parse query"]=function A(B,Q){if(!dY(this.url)||this.url.scheme==="ws"||this.url.scheme==="wss")this.encodingOverride="utf-8";if(!this.stateOverride&&B===B2("#")||isNaN(B)){let D=dY(this.url)?Ya6:Ia6;if(this.url.query+=Q_1(this.buffer,D),this.buffer="",B===B2("#"))this.url.fragment="",this.state="fragment"}else if(!isNaN(B)){if(B===B2("%")&&(!cY.isASCIIHex(this.input[this.pointer+1])||!cY.isASCIIHex(this.input[this.pointer+2])))this.parseError=!0;this.buffer+=Q}return!0};WZ.prototype["parse fragment"]=function A(B){if(!isNaN(B)){if(B===B2("%")&&(!cY.isASCIIHex(this.input[this.pointer+1])||!cY.isASCIIHex(this.input[this.pointer+2])))this.parseError=!0;this.url.fragment+=B_1(B,Fa6)}return!0};function Ta6(A,B){let Q=`${A.scheme}:`;if(A.host!==null){if(Q+="//",A.username!==""||A.password!==""){if(Q+=A.username,A.password!=="")Q+=`:${A.password}`;Q+="@"}if(Q+=Oz0(A.host),A.port!==null)Q+=`:${A.port}`}if(A.host===null&&!uD1(A)&&A.path.length>1&&A.path[0]==="")Q+="/.";if(Q+=Tz0(A),A.query!==null)Q+=`?${A.query}`;if(!B&&A.fragment!==null)Q+=`#${A.fragment}`;return Q}function Pa6(A){let B=`${A.scheme}://`;if(B+=Oz0(A.host),A.port!==null)B+=`:${A.port}`;return B}function Tz0(A){if(uD1(A))return A.path;let B="";for(let Q of A.path)B+=`/${Q}`;return B}Sa6.serializeURL=Ta6;Sa6.serializePath=Tz0;Sa6.serializeURLOrigin=function(A){switch(A.scheme){case"blob":{let B=Sa6.parseURL(Tz0(A));if(B===null)return"null";if(B.scheme!=="http"&&B.scheme!=="https")return"null";return Sa6.serializeURLOrigin(B)}case"ftp":case"http":case"https":case"ws":case"wss":return Pa6({scheme:A.scheme,host:A.host,port:A.port});case"file":return"null";default:return"null"}};Sa6.basicURLParse=function(A,B){if(B===void 0)B={};let Q=new WZ(A,B.baseURL,B.encodingOverride,B.url,B.stateOverride);if(Q.failure)return null;return Q.url};Sa6.setTheUsername=function(A,B){A.username=Q_1(B,Rz0)};Sa6.setThePassword=function(A,B){A.password=Q_1(B,Rz0)};Sa6.serializeHost=Oz0;Sa6.cannotHaveAUsernamePasswordPort=Ma6;Sa6.hasAnOpaquePath=uD1;Sa6.serializeInteger=function(A){return String(A)};Sa6.parseURL=function(A,B){if(B===void 0)B={};return Sa6.basicURLParse(A,{baseURL:B.baseURL,encodingOverride:B.encodingOverride})}});
var Q70=E((sz)=>{var HS4=sz&&sz.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),zS4=sz&&sz.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),JX2=sz&&sz.__importStar||function(A){if(A&&A.__esModule)return A;var B={};if(A!=null){for(var Q in A)if(Q!=="default"&&Object.prototype.hasOwnProperty.call(A,Q))HS4(B,A,Q)}return zS4(B,A),B},XX2=sz&&sz.__importDefault||function(A){return A&&A.__esModule?A:{default:A}};Object.defineProperty(sz,"__esModule",{value:!0});sz.HttpsProxyAgent=void 0;var aL1=JX2(J1("net")),IX2=JX2(J1("tls")),ES4=XX2(J1("assert")),US4=XX2(GB1()),wS4=GX2(),$S4=J1("url"),qS4=FX2(),H81=US4.default("https-proxy-agent"),YX2=(A)=>{if(A.servername===void 0&&A.host&&!aL1.isIP(A.host))return{...A,servername:A.host};return A};class B70 extends wS4.Agent{constructor(A,B){super(B);this.options={path:void 0},this.proxy=typeof A==="string"?new $S4.URL(A):A,this.proxyHeaders=B?.headers??{},H81("Creating new HttpsProxyAgent instance: %o",this.proxy.href);let Q=(this.proxy.hostname||this.proxy.host).replace(/^\[|\]$/g,""),D=this.proxy.port?parseInt(this.proxy.port,10):this.proxy.protocol==="https:"?443:80;this.connectOpts={ALPNProtocols:["http/1.1"],...B?WX2(B,"headers"):null,host:Q,port:D}}async connect(A,B){let{proxy:Q}=this;if(!B.host)throw new TypeError('No "host" provided');let D;if(Q.protocol==="https:")H81("Creating `tls.Socket`: %o",this.connectOpts),D=IX2.connect(YX2(this.connectOpts));else H81("Creating `net.Socket`: %o",this.connectOpts),D=aL1.connect(this.connectOpts);let Z=typeof this.proxyHeaders==="function"?this.proxyHeaders():{...this.proxyHeaders},G=aL1.isIPv6(B.host)?`[${B.host}]`:B.host,F=`CONNECT ${G}:${B.port} HTTP/1.1\r
`;if(Q.username||Q.password){let X=`${decodeURIComponent(Q.username)}:${decodeURIComponent(Q.password)}`;Z["Proxy-Authorization"]=`Basic ${Buffer.from(X).toString("base64")}`}if(Z.Host=`${G}:${B.port}`,!Z["Proxy-Connection"])Z["Proxy-Connection"]=this.keepAlive?"Keep-Alive":"close";for(let X of Object.keys(Z))F+=`${X}: ${Z[X]}\r
`;let I=qS4.parseProxyResponse(D);D.write(`${F}\r
`);let{connect:Y,buffered:W}=await I;if(A.emit("proxyConnect",Y),this.emit("proxyConnect",Y,A),Y.statusCode===200){if(A.once("socket",NS4),B.secureEndpoint)return H81("Upgrading socket connection to TLS"),IX2.connect({...WX2(YX2(B),"host","path","port"),socket:D});return D}D.destroy();let J=new aL1.Socket({writable:!1});return J.readable=!0,A.once("socket",(X)=>{H81("Replaying proxy buffer for failed request"),ES4.default(X.listenerCount("data")>0),X.push(W),X.push(null)}),J}}B70.protocols=["http","https"];sz.HttpsProxyAgent=B70;function NS4(A){A.resume()}function WX2(A,...B){let Q={},D;for(D in A)if(!B.includes(D))Q[D]=A[D];return Q}});
var Qp0=E((vh8,Bp0)=>{var wM9=J1("os"),Ap0=J1("tty"),dH=ZB1(),{env:tF}=process,LX1;if(dH("no-color")||dH("no-colors")||dH("color=false")||dH("color=never"))LX1=0;else if(dH("color")||dH("colors")||dH("color=true")||dH("color=always"))LX1=1;function $M9(){if("FORCE_COLOR"in tF){if(tF.FORCE_COLOR==="true")return 1;if(tF.FORCE_COLOR==="false")return 0;return tF.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(tF.FORCE_COLOR,10),3)}}function qM9(A){if(A===0)return!1;return{level:A,hasBasic:!0,has256:A>=2,has16m:A>=3}}function NM9(A,{streamIsTTY:B,sniffFlags:Q=!0}={}){let D=$M9();if(D!==void 0)LX1=D;let Z=Q?LX1:D;if(Z===0)return 0;if(Q){if(dH("color=16m")||dH("color=full")||dH("color=truecolor"))return 3;if(dH("color=256"))return 2}if(A&&!B&&Z===void 0)return 0;let G=Z||0;if(tF.TERM==="dumb")return G;if(process.platform==="win32"){let F=wM9.release().split(".");if(Number(F[0])>=10&&Number(F[2])>=10586)return Number(F[2])>=14931?3:2;return 1}if("CI"in tF){if(["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some((F)=>(F in tF))||tF.CI_NAME==="codeship")return 1;return G}if("TEAMCITY_VERSION"in tF)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(tF.TEAMCITY_VERSION)?1:0;if(tF.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in tF){let F=Number.parseInt((tF.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(tF.TERM_PROGRAM){case"iTerm.app":return F>=3?3:2;case"Apple_Terminal":return 2}}if(/-256(color)?$/i.test(tF.TERM))return 2;if(/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(tF.TERM))return 1;if("COLORTERM"in tF)return 1;return G}function dc1(A,B={}){let Q=NM9(A,{streamIsTTY:A&&A.isTTY,...B});return qM9(Q)}Bp0.exports={supportsColor:dc1,stdout:dc1({isTTY:Ap0.isatty(1)}),stderr:dc1({isTTY:Ap0.isatty(2)})}});
var RE0=E((ht6)=>{var WzB=tHB(),O_1=YzB(),ft6=["HS256","HS384","HS512","RS256","RS384","RS512","PS256","PS384","PS512","ES256","ES384","ES512"];ht6.ALGORITHMS=ft6;ht6.sign=WzB.sign;ht6.verify=O_1.verify;ht6.decode=O_1.decode;ht6.isValid=O_1.isValid;ht6.createSign=function A(B){return new WzB(B)};ht6.createVerify=function A(B){return new O_1(B)}});
var RqA=E((LqA)=>{Object.defineProperty(LqA,"__esModule",{value:!0});LqA.default=void 0;var nwQ=awQ(J1("crypto"));function awQ(A){return A&&A.__esModule?A:{default:A}}function swQ(A){if(Array.isArray(A))A=Buffer.from(A);else if(typeof A==="string")A=Buffer.from(A,"utf8");return nwQ.default.createHash("md5").update(A).digest()}var rwQ=swQ;LqA.default=rwQ});
var SVB=E((Xo5,PVB)=>{var Uz0=wVB(),yE=qVB(),RVB=NVB(),{STATUS_MAPPING:fx}=MVB();function Ez0(A){return/[^\x00-\x7F]/u.test(A)}function OVB(A){let B=0,Q=RVB.length-1;while(B<=Q){let D=Math.floor((B+Q)/2),Z=RVB[D],G=Array.isArray(Z[0])?Z[0][0]:Z[0],F=Array.isArray(Z[0])?Z[0][1]:Z[0];if(G<=A&&F>=A)return Z.slice(1);else if(G>A)Q=D-1;else B=D+1}return null}function yn6(A,{transitionalProcessing:B}){let Q="";for(let D of A){let[Z,G]=OVB(D.codePointAt(0));switch(Z){case fx.disallowed:Q+=D;break;case fx.ignored:break;case fx.mapped:if(B&&D==="ẞ")Q+="ss";else Q+=G;break;case fx.deviation:if(B)Q+=G;else Q+=D;break;case fx.valid:Q+=D;break}}return Q}function _n6(A,{checkHyphens:B,checkBidi:Q,checkJoiners:D,transitionalProcessing:Z,useSTD3ASCIIRules:G,isBidi:F}){if(A.length===0)return!0;if(A.normalize("NFC")!==A)return!1;let I=Array.from(A);if(B){if(I[2]==="-"&&I[3]==="-"||(A.startsWith("-")||A.endsWith("-")))return!1}if(!B){if(A.startsWith("xn--"))return!1}if(A.includes("."))return!1;if(yE.combiningMarks.test(I[0]))return!1;for(let Y of I){let W=Y.codePointAt(0),[J]=OVB(W);if(Z){if(J!==fx.valid)return!1}else if(J!==fx.valid&&J!==fx.deviation)return!1;if(G&&W<=127){if(!/^(?:[a-z]|[0-9]|-)$/u.test(Y))return!1}}if(D){let Y=0;for(let[W,J]of I.entries())if(J==="‌"||J==="‍"){if(W>0){if(yE.combiningClassVirama.test(I[W-1]))continue;if(J==="‌"){let X=I.indexOf("‌",W+1),V=X<0?I.slice(Y):I.slice(Y,X);if(yE.validZWNJ.test(V.join(""))){Y=W+1;continue}}}return!1}}if(Q&&F){let Y;if(yE.bidiS1LTR.test(I[0]))Y=!1;else if(yE.bidiS1RTL.test(I[0]))Y=!0;else return!1;if(Y){if(!yE.bidiS2.test(A)||!yE.bidiS3.test(A)||yE.bidiS4EN.test(A)&&yE.bidiS4AN.test(A))return!1}else if(!yE.bidiS5.test(A)||!yE.bidiS6.test(A))return!1}return!0}function xn6(A){let B=A.map((Q)=>{if(Q.startsWith("xn--"))try{return Uz0.decode(Q.substring(4))}catch{return""}return Q}).join(".");return yE.bidiDomain.test(B)}function TVB(A,B){let Q=yn6(A,B);Q=Q.normalize("NFC");let D=Q.split("."),Z=xn6(D),G=!1;for(let[F,I]of D.entries()){let Y=I,W=B.transitionalProcessing;if(Y.startsWith("xn--")){if(Ez0(Y)){G=!0;continue}try{Y=Uz0.decode(Y.substring(4))}catch{if(!B.ignoreInvalidPunycode){G=!0;continue}}if(D[F]=Y,Y===""||!Ez0(Y))G=!0;W=!1}if(G)continue;if(!_n6(Y,{...B,transitionalProcessing:W,isBidi:Z}))G=!0}return{string:D.join("."),error:G}}function vn6(A,{checkHyphens:B=!1,checkBidi:Q=!1,checkJoiners:D=!1,useSTD3ASCIIRules:Z=!1,verifyDNSLength:G=!1,transitionalProcessing:F=!1,ignoreInvalidPunycode:I=!1}={}){let Y=TVB(A,{checkHyphens:B,checkBidi:Q,checkJoiners:D,useSTD3ASCIIRules:Z,transitionalProcessing:F,ignoreInvalidPunycode:I}),W=Y.string.split(".");if(W=W.map((J)=>{if(Ez0(J))try{return`xn--${Uz0.encode(J)}`}catch{Y.error=!0}return J}),G){let J=W.join(".").length;if(J>253||J===0)Y.error=!0;for(let X=0;X<W.length;++X)if(W[X].length>63||W[X].length===0){Y.error=!0;break}}if(Y.error)return null;return W.join(".")}function bn6(A,{checkHyphens:B=!1,checkBidi:Q=!1,checkJoiners:D=!1,useSTD3ASCIIRules:Z=!1,transitionalProcessing:G=!1,ignoreInvalidPunycode:F=!1}={}){let I=TVB(A,{checkHyphens:B,checkBidi:Q,checkJoiners:D,useSTD3ASCIIRules:Z,transitionalProcessing:G,ignoreInvalidPunycode:F});return{domain:I.string,error:I.error}}PVB.exports={toASCII:vn6,toUnicode:bn6}});
var SqA=E((TqA)=>{Object.defineProperty(TqA,"__esModule",{value:!0});TqA.default=void 0;var owQ=OqA(Ge1()),twQ=OqA(RqA());function OqA(A){return A&&A.__esModule?A:{default:A}}var ewQ=owQ.default("v3",48,twQ.default),A$Q=ewQ;TqA.default=A$Q});
var Sz0=E((Ho5,FCB)=>{var{utf8Encode:ga6,utf8DecodeWithoutBOM:ACB}=ty1(),{percentDecodeBytes:BCB,utf8PercentEncodeString:QCB,isURLEncodedPercentEncode:DCB}=ey1();function ZCB(A){return A.codePointAt(0)}function ua6(A){let B=ca6(A,ZCB("&")),Q=[];for(let D of B){if(D.length===0)continue;let Z,G,F=D.indexOf(ZCB("="));if(F>=0)Z=D.slice(0,F),G=D.slice(F+1);else Z=D,G=new Uint8Array(0);Z=GCB(Z,43,32),G=GCB(G,43,32);let I=ACB(BCB(Z)),Y=ACB(BCB(G));Q.push([I,Y])}return Q}function ma6(A){return ua6(ga6(A))}function da6(A){let B="";for(let[Q,D]of A.entries()){let Z=QCB(D[0],DCB,!0),G=QCB(D[1],DCB,!0);if(Q!==0)B+="&";B+=`${Z}=${G}`}return B}function ca6(A,B){let Q=[],D=0,Z=A.indexOf(B);while(Z>=0)Q.push(A.slice(D,Z)),D=Z+1,Z=A.indexOf(B,D);if(D!==A.length)Q.push(A.slice(D));return Q}function GCB(A,B,Q){let D=A.indexOf(B);while(D>=0)A[D]=Q,D=A.indexOf(B,D+1);return A}FCB.exports={parseUrlencodedString:ma6,serializeUrlencoded:da6}});
var TEB=E((REB)=>{Object.defineProperty(REB,"__esModule",{value:!0});REB.ExternalAccountAuthorizedUserClient=REB.EXTERNAL_ACCOUNT_AUTHORIZED_USER_TYPE=void 0;var D18=PM(),LEB=vE0(),Z18=X$(),G18=J1("stream"),F18=ix();REB.EXTERNAL_ACCOUNT_AUTHORIZED_USER_TYPE="external_account_authorized_user";var I18="https://sts.{universeDomain}/v1/oauthtoken";class XU0 extends LEB.OAuthClientAuthHandler{constructor(A,B,Q){super(Q);this.url=A,this.transporter=B}async refreshToken(A,B){let Q=new URLSearchParams({grant_type:"refresh_token",refresh_token:A}),D={"Content-Type":"application/x-www-form-urlencoded",...B},Z={...XU0.RETRY_CONFIG,url:this.url,method:"POST",headers:D,data:Q.toString(),responseType:"json"};this.applyClientAuthenticationOptions(Z);try{let G=await this.transporter.request(Z),F=G.data;return F.res=G,F}catch(G){if(G instanceof Z18.GaxiosError&&G.response)throw LEB.getErrorFromOAuthErrorResponse(G.response.data,G);throw G}}}class MEB extends D18.AuthClient{constructor(A,B){var Q;super({...A,...B});if(A.universe_domain)this.universeDomain=A.universe_domain;this.refreshToken=A.refresh_token;let D={confidentialClientType:"basic",clientId:A.client_id,clientSecret:A.client_secret};if(this.externalAccountAuthorizedUserHandler=new XU0((Q=A.token_url)!==null&&Q!==void 0?Q:I18.replace("{universeDomain}",this.universeDomain),this.transporter,D),this.cachedAccessToken=null,this.quotaProjectId=A.quota_project_id,typeof(B===null||B===void 0?void 0:B.eagerRefreshThresholdMillis)!=="number")this.eagerRefreshThresholdMillis=F18.EXPIRATION_TIME_OFFSET;else this.eagerRefreshThresholdMillis=B.eagerRefreshThresholdMillis;this.forceRefreshOnFailure=!!(B===null||B===void 0?void 0:B.forceRefreshOnFailure)}async getAccessToken(){if(!this.cachedAccessToken||this.isExpired(this.cachedAccessToken))await this.refreshAccessTokenAsync();return{token:this.cachedAccessToken.access_token,res:this.cachedAccessToken.res}}async getRequestHeaders(){let B={Authorization:`Bearer ${(await this.getAccessToken()).token}`};return this.addSharedMetadataHeaders(B)}request(A,B){if(B)this.requestAsync(A).then((Q)=>B(null,Q),(Q)=>{return B(Q,Q.response)});else return this.requestAsync(A)}async requestAsync(A,B=!1){let Q;try{let D=await this.getRequestHeaders();if(A.headers=A.headers||{},D&&D["x-goog-user-project"])A.headers["x-goog-user-project"]=D["x-goog-user-project"];if(D&&D.Authorization)A.headers.Authorization=D.Authorization;Q=await this.transporter.request(A)}catch(D){let Z=D.response;if(Z){let G=Z.status,F=Z.config.data instanceof G18.Readable;if(!B&&(G===401||G===403)&&!F&&this.forceRefreshOnFailure)return await this.refreshAccessTokenAsync(),await this.requestAsync(A,!0)}throw D}return Q}async refreshAccessTokenAsync(){let A=await this.externalAccountAuthorizedUserHandler.refreshToken(this.refreshToken);if(this.cachedAccessToken={access_token:A.access_token,expiry_date:new Date().getTime()+A.expires_in*1000,res:A.res},A.refresh_token!==void 0)this.refreshToken=A.refresh_token;return this.cachedAccessToken}isExpired(A){let B=new Date().getTime();return A.expiry_date?B>=A.expiry_date-this.eagerRefreshThresholdMillis:!1}}REB.ExternalAccountAuthorizedUserClient=MEB});
var UE0=E((Bt5,xHB)=>{var N_1=ue().Buffer,Yt6=J1("stream"),Wt6=J1("util");function L_1(A){if(this.buffer=null,this.writable=!0,this.readable=!0,!A)return this.buffer=N_1.alloc(0),this;if(typeof A.pipe==="function")return this.buffer=N_1.alloc(0),A.pipe(this),this;if(A.length||typeof A==="object")return this.buffer=A,this.writable=!1,process.nextTick(function(){this.emit("end",A),this.readable=!1,this.emit("close")}.bind(this)),this;throw new TypeError("Unexpected data type ("+typeof A+")")}Wt6.inherits(L_1,Yt6);L_1.prototype.write=function A(B){this.buffer=N_1.concat([this.buffer,N_1.from(B)]),this.emit("data",B)};L_1.prototype.end=function A(B){if(B)this.write(B);this.emit("end",B),this.emit("close"),this.writable=!1,this.readable=!1};xHB.exports=L_1});
var UKB=E((_o5,C_1)=>{var zKB=CKB().stringify,EKB=HKB();C_1.exports=function(A){return{parse:EKB(A),stringify:zKB}};C_1.exports.parse=EKB();C_1.exports.stringify=zKB});
var UzB=E((px)=>{var H$=px&&px.__classPrivateFieldGet||function(A,B,Q,D){if(Q==="a"&&!D)throw new TypeError("Private accessor was defined without a getter");if(typeof B==="function"?A!==B||!D:!B.has(A))throw new TypeError("Cannot read private member from an object whose class did not declare it");return Q==="m"?D:Q==="a"?D.call(A):D?D.value:B.get(A)},JzB=px&&px.__classPrivateFieldSet||function(A,B,Q,D,Z){if(D==="m")throw new TypeError("Private method is not writable");if(D==="a"&&!Z)throw new TypeError("Private accessor was defined without a setter");if(typeof B==="function"?A!==B||!Z:!B.has(A))throw new TypeError("Cannot write private member to an object whose class did not declare it");return D==="a"?Z.call(A,Q):Z?Z.value=Q:B.set(A,Q),Q},z$,pe,OE0,XzB,VzB,TE0,PE0,CzB;Object.defineProperty(px,"__esModule",{value:!0});px.GoogleToken=void 0;var KzB=J1("fs"),it6=X$(),nt6=RE0(),at6=J1("path"),st6=J1("util"),HzB=KzB.readFile?st6.promisify(KzB.readFile):async()=>{throw new ie("use key rather than keyFile.","MISSING_CREDENTIALS")},zzB="https://www.googleapis.com/oauth2/v4/token",rt6="https://accounts.google.com/o/oauth2/revoke?token=";class ie extends Error{constructor(A,B){super(A);this.code=B}}class EzB{get accessToken(){return this.rawToken?this.rawToken.access_token:void 0}get idToken(){return this.rawToken?this.rawToken.id_token:void 0}get tokenType(){return this.rawToken?this.rawToken.token_type:void 0}get refreshToken(){return this.rawToken?this.rawToken.refresh_token:void 0}constructor(A){z$.add(this),this.transporter={request:(B)=>it6.request(B)},pe.set(this,void 0),H$(this,z$,"m",PE0).call(this,A)}hasExpired(){let A=new Date().getTime();if(this.rawToken&&this.expiresAt)return A>=this.expiresAt;else return!0}isTokenExpiring(){var A;let B=new Date().getTime(),Q=(A=this.eagerRefreshThresholdMillis)!==null&&A!==void 0?A:0;if(this.rawToken&&this.expiresAt)return this.expiresAt<=B+Q;else return!0}getToken(A,B={}){if(typeof A==="object")B=A,A=void 0;if(B=Object.assign({forceRefresh:!1},B),A){let Q=A;H$(this,z$,"m",OE0).call(this,B).then((D)=>Q(null,D),A);return}return H$(this,z$,"m",OE0).call(this,B)}async getCredentials(A){switch(at6.extname(A)){case".json":{let Q=await HzB(A,"utf8"),D=JSON.parse(Q),Z=D.private_key,G=D.client_email;if(!Z||!G)throw new ie("private_key and client_email are required.","MISSING_CREDENTIALS");return{privateKey:Z,clientEmail:G}}case".der":case".crt":case".pem":return{privateKey:await HzB(A,"utf8")};case".p12":case".pfx":throw new ie("*.p12 certificates are not supported after v6.1.2. Consider utilizing *.json format or converting *.p12 to *.pem using the OpenSSL CLI.","UNKNOWN_CERTIFICATE_TYPE");default:throw new ie("Unknown certificate type. Type is determined based on file extension. Current supported extensions are *.json, and *.pem.","UNKNOWN_CERTIFICATE_TYPE")}}revokeToken(A){if(A){H$(this,z$,"m",TE0).call(this).then(()=>A(),A);return}return H$(this,z$,"m",TE0).call(this)}}px.GoogleToken=EzB;pe=new WeakMap,z$=new WeakSet,OE0=async function A(B){if(H$(this,pe,"f")&&!B.forceRefresh)return H$(this,pe,"f");try{return await JzB(this,pe,H$(this,z$,"m",XzB).call(this,B),"f")}finally{JzB(this,pe,void 0,"f")}},XzB=async function A(B){if(this.isTokenExpiring()===!1&&B.forceRefresh===!1)return Promise.resolve(this.rawToken);if(!this.key&&!this.keyFile)throw new Error("No key or keyFile set.");if(!this.key&&this.keyFile){let Q=await this.getCredentials(this.keyFile);if(this.key=Q.privateKey,this.iss=Q.clientEmail||this.iss,!Q.clientEmail)H$(this,z$,"m",VzB).call(this)}return H$(this,z$,"m",CzB).call(this)},VzB=function A(){if(!this.iss)throw new ie("email is required.","MISSING_CREDENTIALS")},TE0=async function A(){if(!this.accessToken)throw new Error("No token to revoke.");let B=rt6+this.accessToken;await this.transporter.request({url:B,retry:!0}),H$(this,z$,"m",PE0).call(this,{email:this.iss,sub:this.sub,key:this.key,keyFile:this.keyFile,scope:this.scope,additionalClaims:this.additionalClaims})},PE0=function A(B={}){if(this.keyFile=B.keyFile,this.key=B.key,this.rawToken=void 0,this.iss=B.email||B.iss,this.sub=B.sub,this.additionalClaims=B.additionalClaims,typeof B.scope==="object")this.scope=B.scope.join(" ");else this.scope=B.scope;if(this.eagerRefreshThresholdMillis=B.eagerRefreshThresholdMillis,B.transporter)this.transporter=B.transporter},CzB=async function A(){var B,Q;let D=Math.floor(new Date().getTime()/1000),Z=this.additionalClaims||{},G=Object.assign({iss:this.iss,scope:this.scope,aud:zzB,exp:D+3600,iat:D,sub:this.sub},Z),F=nt6.sign({header:{alg:"RS256"},payload:G,secret:this.key});try{let I=await this.transporter.request({method:"POST",url:zzB,data:{grant_type:"urn:ietf:params:oauth:grant-type:jwt-bearer",assertion:F},headers:{"Content-Type":"application/x-www-form-urlencoded"},responseType:"json",retryConfig:{httpMethodsToRetry:["POST"]}});return this.rawToken=I.data,this.expiresAt=I.data.expires_in===null||I.data.expires_in===void 0?void 0:(D+I.data.expires_in)*1000,this.rawToken}catch(I){this.rawToken=void 0,this.tokenExpires=void 0;let Y=I.response&&((B=I.response)===null||B===void 0?void 0:B.data)?(Q=I.response)===null||Q===void 0?void 0:Q.data:{};if(Y.error){let W=Y.error_description?`: ${Y.error_description}`:"";I.message=`${Y.error}${W}`}throw I}}});
var VE0=E((wHB)=>{Object.defineProperty(wHB,"__esModule",{value:!0});wHB.LoginTicket=void 0;class UHB{constructor(A,B){this.envelope=A,this.payload=B}getEnvelope(){return this.envelope}getPayload(){return this.payload}getUserId(){let A=this.getPayload();if(A&&A.sub)return A.sub;return null}getAttributes(){return{envelope:this.getEnvelope(),payload:this.getPayload()}}}wHB.LoginTicket=UHB});
var WCB=E((ia6)=>{var jz0=Sz0();ia6.implementation=class A{constructor(B,Q,{doNotStripQMark:D=!1}){let Z=Q[0];if(this._list=[],this._url=null,!D&&typeof Z==="string"&&Z[0]==="?")Z=Z.slice(1);if(Array.isArray(Z))for(let G of Z){if(G.length!==2)throw new TypeError("Failed to construct 'URLSearchParams': parameter 1 sequence's element does not contain exactly two elements.");this._list.push([G[0],G[1]])}else if(typeof Z==="object"&&Object.getPrototypeOf(Z)===null)for(let G of Object.keys(Z)){let F=Z[G];this._list.push([G,F])}else this._list=jz0.parseUrlencodedString(Z)}_updateSteps(){if(this._url!==null){let B=jz0.serializeUrlencoded(this._list);if(B==="")B=null;this._url._url.query=B}}get size(){return this._list.length}append(B,Q){this._list.push([B,Q]),this._updateSteps()}delete(B,Q){let D=0;while(D<this._list.length)if(this._list[D][0]===B&&(Q===void 0||this._list[D][1]===Q))this._list.splice(D,1);else D++;this._updateSteps()}get(B){for(let Q of this._list)if(Q[0]===B)return Q[1];return null}getAll(B){let Q=[];for(let D of this._list)if(D[0]===B)Q.push(D[1]);return Q}has(B,Q){for(let D of this._list)if(D[0]===B&&(Q===void 0||D[1]===Q))return!0;return!1}set(B,Q){let D=!1,Z=0;while(Z<this._list.length)if(this._list[Z][0]===B)if(D)this._list.splice(Z,1);else D=!0,this._list[Z][1]=Q,Z++;else Z++;if(!D)this._list.push([B,Q]);this._updateSteps()}sort(){this._list.sort((B,Q)=>{if(B[0]<Q[0])return-1;if(B[0]>Q[0])return 1;return 0}),this._updateSteps()}[Symbol.iterator](){return this._list[Symbol.iterator]()}toString(){return jz0.serializeUrlencoded(this._list)}}});
var X$=E((IX)=>{var qr6=IX&&IX.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),Nr6=IX&&IX.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))qr6(B,A,Q)};Object.defineProperty(IX,"__esModule",{value:!0});IX.instance=IX.Gaxios=IX.GaxiosError=void 0;IX.request=Mr6;var YKB=IKB();Object.defineProperty(IX,"Gaxios",{enumerable:!0,get:function(){return YKB.Gaxios}});var Lr6=pz0();Object.defineProperty(IX,"GaxiosError",{enumerable:!0,get:function(){return Lr6.GaxiosError}});Nr6(iz0(),IX);IX.instance=new YKB.Gaxios;async function Mr6(A){return IX.instance.request(A)}});
var YCB=E((la6)=>{var ICB=sy1(),D_1=oy1();la6.convert=(A,B,{context:Q="The provided value"}={})=>{if(typeof B!=="function")throw new A.TypeError(Q+" is not a function");function D(...Z){let G=D_1.tryWrapperForImpl(this),F;for(let I=0;I<Z.length;I++)Z[I]=D_1.tryWrapperForImpl(Z[I]);return F=Reflect.apply(B,G,Z),F=ICB.any(F,{context:Q,globals:A}),F}return D.construct=(...Z)=>{for(let F=0;F<Z.length;F++)Z[F]=D_1.tryWrapperForImpl(Z[F]);let G=Reflect.construct(B,Z);return G=ICB.any(G,{context:Q,globals:A}),G},D[D_1.wrapperSymbol]=B,D.objectReference=B,D}});
var YE0=E((no5,WHB)=>{var w_1=ue().Buffer,GHB=DHB(),$_1=128,FHB=0,yo6=32,_o6=16,xo6=2,IHB=_o6|yo6|FHB<<6,q_1=xo6|FHB<<6;function vo6(A){return A.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function YHB(A){if(w_1.isBuffer(A))return A;else if(typeof A==="string")return w_1.from(A,"base64");throw new TypeError("ECDSA signature must be a Base64 string or a Buffer")}function bo6(A,B){A=YHB(A);var Q=GHB(B),D=Q+1,Z=A.length,G=0;if(A[G++]!==IHB)throw new Error('Could not find expected "seq"');var F=A[G++];if(F===($_1|1))F=A[G++];if(Z-G<F)throw new Error('"seq" specified length of "'+F+'", only "'+(Z-G)+'" remaining');if(A[G++]!==q_1)throw new Error('Could not find expected "int" for "r"');var I=A[G++];if(Z-G-2<I)throw new Error('"r" specified length of "'+I+'", only "'+(Z-G-2)+'" available');if(D<I)throw new Error('"r" specified length of "'+I+'", max of "'+D+'" is acceptable');var Y=G;if(G+=I,A[G++]!==q_1)throw new Error('Could not find expected "int" for "s"');var W=A[G++];if(Z-G!==W)throw new Error('"s" specified length of "'+W+'", expected "'+(Z-G)+'"');if(D<W)throw new Error('"s" specified length of "'+W+'", max of "'+D+'" is acceptable');var J=G;if(G+=W,G!==Z)throw new Error('Expected to consume entire buffer, but "'+(Z-G)+'" bytes remain');var X=Q-I,V=Q-W,C=w_1.allocUnsafe(X+I+V+W);for(G=0;G<X;++G)C[G]=0;A.copy(C,G,Y+Math.max(-X,0),Y+I),G=Q;for(var K=G;G<K+V;++G)C[G]=0;return A.copy(C,G,J+Math.max(-V,0),J+W),C=C.toString("base64"),C=vo6(C),C}function ZHB(A,B,Q){var D=0;while(B+D<Q&&A[B+D]===0)++D;var Z=A[B+D]>=$_1;if(Z)--D;return D}function fo6(A,B){A=YHB(A);var Q=GHB(B),D=A.length;if(D!==Q*2)throw new TypeError('"'+B+'" signatures must be "'+Q*2+'" bytes, saw "'+D+'"');var Z=ZHB(A,0,Q),G=ZHB(A,Q,A.length),F=Q-Z,I=Q-G,Y=2+F+1+1+I,W=Y<$_1,J=w_1.allocUnsafe((W?2:3)+Y),X=0;if(J[X++]=IHB,W)J[X++]=Y;else J[X++]=$_1|1,J[X++]=Y&255;if(J[X++]=q_1,J[X++]=F,Z<0)J[X++]=0,X+=A.copy(J,X,0,Q);else X+=A.copy(J,X,Z,Q);if(J[X++]=q_1,J[X++]=I,G<0)J[X++]=0,A.copy(J,X,Q);else A.copy(J,X,Q+G);return J}WHB.exports={derToJose:bo6,joseToDer:fo6}});
var YzB=E((Ft5,IzB)=>{var AzB=ue().Buffer,eHB=UE0(),St6=NE0(),jt6=J1("stream"),BzB=LE0(),kt6=J1("util"),yt6=/^[a-zA-Z0-9\-_]+?\.[a-zA-Z0-9\-_]+?\.([a-zA-Z0-9\-_]+)?$/;function _t6(A){return Object.prototype.toString.call(A)==="[object Object]"}function xt6(A){if(_t6(A))return A;try{return JSON.parse(A)}catch(B){return}}function QzB(A){var B=A.split(".",1)[0];return xt6(AzB.from(B,"base64").toString("binary"))}function vt6(A){return A.split(".",2).join(".")}function DzB(A){return A.split(".")[2]}function bt6(A,B){B=B||"utf8";var Q=A.split(".")[1];return AzB.from(Q,"base64").toString(B)}function ZzB(A){return yt6.test(A)&&!!QzB(A)}function GzB(A,B,Q){if(!B){var D=new Error("Missing algorithm parameter for jws.verify");throw D.code="MISSING_ALGORITHM",D}A=BzB(A);var Z=DzB(A),G=vt6(A),F=St6(B);return F.verify(G,Z,Q)}function FzB(A,B){if(B=B||{},A=BzB(A),!ZzB(A))return null;var Q=QzB(A);if(!Q)return null;var D=bt6(A);if(Q.typ==="JWT"||B.json)D=JSON.parse(D,B.encoding);return{header:Q,payload:D,signature:DzB(A)}}function le(A){A=A||{};var B=A.secret||A.publicKey||A.key,Q=new eHB(B);this.readable=!0,this.algorithm=A.algorithm,this.encoding=A.encoding,this.secret=this.publicKey=this.key=Q,this.signature=new eHB(A.signature),this.secret.once("close",function(){if(!this.signature.writable&&this.readable)this.verify()}.bind(this)),this.signature.once("close",function(){if(!this.secret.writable&&this.readable)this.verify()}.bind(this))}kt6.inherits(le,jt6);le.prototype.verify=function A(){try{var B=GzB(this.signature.buffer,this.algorithm,this.key.buffer),Q=FzB(this.signature.buffer,this.encoding);return this.emit("done",B,Q),this.emit("data",B),this.emit("end"),this.readable=!1,B}catch(D){this.readable=!1,this.emit("error",D),this.emit("close")}};le.decode=FzB;le.isValid=ZzB;le.verify=GzB;IzB.exports=le});
var ZB1=E((xh8,el0)=>{el0.exports=(A,B=process.argv)=>{let Q=A.startsWith("-")?"":A.length===1?"-":"--",D=B.indexOf(Q+A),Z=B.indexOf("--");return D!==-1&&(Z===-1||D<Z)}});
var ZE0=E((Jo6)=>{Jo6.byteLength=Zo6;Jo6.toByteArray=Fo6;Jo6.fromByteArray=Wo6;var OM=[],fE=[],Do6=typeof Uint8Array!=="undefined"?Uint8Array:Array,QE0="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(dx=0,DE0=QE0.length;dx<DE0;++dx)OM[dx]=QE0[dx],fE[QE0.charCodeAt(dx)]=dx;var dx,DE0;fE[45]=62;fE[95]=63;function gKB(A){var B=A.length;if(B%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var Q=A.indexOf("=");if(Q===-1)Q=B;var D=Q===B?0:4-Q%4;return[Q,D]}function Zo6(A){var B=gKB(A),Q=B[0],D=B[1];return(Q+D)*3/4-D}function Go6(A,B,Q){return(B+Q)*3/4-Q}function Fo6(A){var B,Q=gKB(A),D=Q[0],Z=Q[1],G=new Do6(Go6(A,D,Z)),F=0,I=Z>0?D-4:D,Y;for(Y=0;Y<I;Y+=4)B=fE[A.charCodeAt(Y)]<<18|fE[A.charCodeAt(Y+1)]<<12|fE[A.charCodeAt(Y+2)]<<6|fE[A.charCodeAt(Y+3)],G[F++]=B>>16&255,G[F++]=B>>8&255,G[F++]=B&255;if(Z===2)B=fE[A.charCodeAt(Y)]<<2|fE[A.charCodeAt(Y+1)]>>4,G[F++]=B&255;if(Z===1)B=fE[A.charCodeAt(Y)]<<10|fE[A.charCodeAt(Y+1)]<<4|fE[A.charCodeAt(Y+2)]>>2,G[F++]=B>>8&255,G[F++]=B&255;return G}function Io6(A){return OM[A>>18&63]+OM[A>>12&63]+OM[A>>6&63]+OM[A&63]}function Yo6(A,B,Q){var D,Z=[];for(var G=B;G<Q;G+=3)D=(A[G]<<16&16711680)+(A[G+1]<<8&65280)+(A[G+2]&255),Z.push(Io6(D));return Z.join("")}function Wo6(A){var B,Q=A.length,D=Q%3,Z=[],G=16383;for(var F=0,I=Q-D;F<I;F+=G)Z.push(Yo6(A,F,F+G>I?I:F+G));if(D===1)B=A[Q-1],Z.push(OM[B>>2]+OM[B<<4&63]+"==");else if(D===2)B=(A[Q-2]<<8)+A[Q-1],Z.push(OM[B>>10]+OM[B>>4&63]+OM[B<<2&63]+"=");return Z.join("")}});
var ZEB=E((ae)=>{var lP=ae&&ae.__classPrivateFieldGet||function(A,B,Q,D){if(Q==="a"&&!D)throw new TypeError("Private accessor was defined without a getter");if(typeof B==="function"?A!==B||!D:!B.has(A))throw new TypeError("Cannot read private member from an object whose class did not declare it");return Q==="m"?D:Q==="a"?D.call(A):D?D.value:B.get(A)},E$,rE0,BEB,QEB,j_1,oE0;Object.defineProperty(ae,"__esModule",{value:!0});ae.DefaultAwsSecurityCredentialsSupplier=void 0;class DEB{constructor(A){E$.add(this),this.regionUrl=A.regionUrl,this.securityCredentialsUrl=A.securityCredentialsUrl,this.imdsV2SessionTokenUrl=A.imdsV2SessionTokenUrl,this.additionalGaxiosOptions=A.additionalGaxiosOptions}async getAwsRegion(A){if(lP(this,E$,"a",j_1))return lP(this,E$,"a",j_1);let B={};if(!lP(this,E$,"a",j_1)&&this.imdsV2SessionTokenUrl)B["x-aws-ec2-metadata-token"]=await lP(this,E$,"m",rE0).call(this,A.transporter);if(!this.regionUrl)throw new Error('Unable to determine AWS region due to missing "options.credential_source.region_url"');let Q={...this.additionalGaxiosOptions,url:this.regionUrl,method:"GET",responseType:"text",headers:B},D=await A.transporter.request(Q);return D.data.substr(0,D.data.length-1)}async getAwsSecurityCredentials(A){if(lP(this,E$,"a",oE0))return lP(this,E$,"a",oE0);let B={};if(this.imdsV2SessionTokenUrl)B["x-aws-ec2-metadata-token"]=await lP(this,E$,"m",rE0).call(this,A.transporter);let Q=await lP(this,E$,"m",BEB).call(this,B,A.transporter),D=await lP(this,E$,"m",QEB).call(this,Q,B,A.transporter);return{accessKeyId:D.AccessKeyId,secretAccessKey:D.SecretAccessKey,token:D.Token}}}ae.DefaultAwsSecurityCredentialsSupplier=DEB;E$=new WeakSet,rE0=async function A(B){let Q={...this.additionalGaxiosOptions,url:this.imdsV2SessionTokenUrl,method:"PUT",responseType:"text",headers:{"x-aws-ec2-metadata-token-ttl-seconds":"300"}};return(await B.request(Q)).data},BEB=async function A(B,Q){if(!this.securityCredentialsUrl)throw new Error('Unable to determine AWS role name due to missing "options.credential_source.url"');let D={...this.additionalGaxiosOptions,url:this.securityCredentialsUrl,method:"GET",responseType:"text",headers:B};return(await Q.request(D)).data},QEB=async function A(B,Q,D){return(await D.request({...this.additionalGaxiosOptions,url:`${this.securityCredentialsUrl}/${B}`,responseType:"json",headers:Q})).data},j_1=function A(){return process.env.AWS_REGION||process.env.AWS_DEFAULT_REGION||null},oE0=function A(){if(process.env.AWS_ACCESS_KEY_ID&&process.env.AWS_SECRET_ACCESS_KEY)return{accessKeyId:process.env.AWS_ACCESS_KEY_ID,secretAccessKey:process.env.AWS_SECRET_ACCESS_KEY,token:process.env.AWS_SESSION_TOKEN};return null}});
var Ze1=E((EqA)=>{Object.defineProperty(EqA,"__esModule",{value:!0});EqA.default=void 0;var bwQ=fwQ(nQ1());function fwQ(A){return A&&A.__esModule?A:{default:A}}function hwQ(A){if(!bwQ.default(A))throw TypeError("Invalid UUID");let B,Q=new Uint8Array(16);return Q[0]=(B=parseInt(A.slice(0,8),16))>>>24,Q[1]=B>>>16&255,Q[2]=B>>>8&255,Q[3]=B&255,Q[4]=(B=parseInt(A.slice(9,13),16))>>>8,Q[5]=B&255,Q[6]=(B=parseInt(A.slice(14,18),16))>>>8,Q[7]=B&255,Q[8]=(B=parseInt(A.slice(19,23),16))>>>8,Q[9]=B&255,Q[10]=(B=parseInt(A.slice(24,36),16))/*************&255,Q[11]=B/**********&255,Q[12]=B>>>24&255,Q[13]=B>>>16&255,Q[14]=B>>>8&255,Q[15]=B&255,Q}var gwQ=hwQ;EqA.default=gwQ});
var _E0=E((RzB)=>{Object.defineProperty(RzB,"__esModule",{value:!0});RzB.UserRefreshClient=RzB.USER_REFRESH_ACCOUNT_TYPE=void 0;var Be6=$m(),Qe6=J1("querystring");RzB.USER_REFRESH_ACCOUNT_TYPE="authorized_user";class P_1 extends Be6.OAuth2Client{constructor(A,B,Q,D,Z){let G=A&&typeof A==="object"?A:{clientId:A,clientSecret:B,refreshToken:Q,eagerRefreshThresholdMillis:D,forceRefreshOnFailure:Z};super(G);this._refreshToken=G.refreshToken,this.credentials.refresh_token=G.refreshToken}async refreshTokenNoCache(A){return super.refreshTokenNoCache(this._refreshToken)}async fetchIdToken(A){return(await this.transporter.request({...P_1.RETRY_CONFIG,url:this.endpoints.oauth2TokenUrl,headers:{"Content-Type":"application/x-www-form-urlencoded"},method:"POST",data:Qe6.stringify({client_id:this._clientId,client_secret:this._clientSecret,grant_type:"refresh_token",refresh_token:this._refreshToken,target_audience:A})})).data.id_token}fromJSON(A){if(!A)throw new Error("Must pass in a JSON object containing the user refresh token");if(A.type!=="authorized_user")throw new Error('The incoming JSON object does not have the "authorized_user" type');if(!A.client_id)throw new Error("The incoming JSON object does not contain a client_id field");if(!A.client_secret)throw new Error("The incoming JSON object does not contain a client_secret field");if(!A.refresh_token)throw new Error("The incoming JSON object does not contain a refresh_token field");this._clientId=A.client_id,this._clientSecret=A.client_secret,this._refreshToken=A.refresh_token,this.credentials.refresh_token=A.refresh_token,this.quotaProjectId=A.quota_project_id,this.universeDomain=A.universe_domain||this.universeDomain}fromStream(A,B){if(B)this.fromStreamAsync(A).then(()=>B(),B);else return this.fromStreamAsync(A)}async fromStreamAsync(A){return new Promise((B,Q)=>{if(!A)return Q(new Error("Must pass in a stream containing the user refresh token."));let D="";A.setEncoding("utf8").on("error",Q).on("data",(Z)=>D+=Z).on("end",()=>{try{let Z=JSON.parse(D);return this.fromJSON(Z),B()}catch(Z){return Q(Z)}})})}static fromJSON(A){let B=new P_1;return B.fromJSON(A),B}}RzB.UserRefreshClient=P_1});
var _EB=E((pY)=>{var nx=pY&&pY.__classPrivateFieldGet||function(A,B,Q,D){if(Q==="a"&&!D)throw new TypeError("Private accessor was defined without a getter");if(typeof B==="function"?A!==B||!D:!B.has(A))throw new TypeError("Cannot read private member from an object whose class did not declare it");return Q==="m"?D:Q==="a"?D.call(A):D?D.value:B.get(A)},PEB=pY&&pY.__classPrivateFieldSet||function(A,B,Q,D,Z){if(D==="m")throw new TypeError("Private method is not writable");if(D==="a"&&!Z)throw new TypeError("Private accessor was defined without a setter");if(typeof B==="function"?A!==B||!Z:!B.has(A))throw new TypeError("Cannot write private member to an object whose class did not declare it");return D==="a"?Z.call(A,Q):Z?Z.value=Q:B.set(A,Q),Q},ax,te,ee,yEB;Object.defineProperty(pY,"__esModule",{value:!0});pY.GoogleAuth=pY.GoogleAuthExceptionMessages=pY.CLOUD_SDK_CLIENT_ID=void 0;var W18=J1("child_process"),FZ1=J1("fs"),ZZ1=nD1(),J18=J1("os"),CU0=J1("path"),X18=he(),V18=sD1(),C18=HE0(),K18=zE0(),H18=EE0(),re=yE0(),SEB=_E0(),oe=xE0(),z18=JU0(),GZ1=ix(),VU0=PM(),jEB=TEB(),kEB=lx();pY.CLOUD_SDK_CLIENT_ID="764086051850-6qr4p6gpi6hn506pt8ejuq83di341hur.apps.googleusercontent.com";pY.GoogleAuthExceptionMessages={API_KEY_WITH_CREDENTIALS:"API Keys and Credentials are mutually exclusive authentication methods and cannot be used together.",NO_PROJECT_ID_FOUND:`Unable to detect a Project Id in the current environment. 
To learn more about authentication and Google APIs, visit: 
https://cloud.google.com/docs/authentication/getting-started`,NO_CREDENTIALS_FOUND:`Unable to find credentials in current environment. 
To learn more about authentication and Google APIs, visit: 
https://cloud.google.com/docs/authentication/getting-started`,NO_ADC_FOUND:"Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.",NO_UNIVERSE_DOMAIN_FOUND:`Unable to detect a Universe Domain in the current environment.
To learn more about Universe Domain retrieval, visit: 
https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys`};class KU0{get isGCE(){return this.checkIsGCE}constructor(A={}){if(ax.add(this),this.checkIsGCE=void 0,this.jsonContent=null,this.cachedCredential=null,te.set(this,null),this.clientOptions={},this._cachedProjectId=A.projectId||null,this.cachedCredential=A.authClient||null,this.keyFilename=A.keyFilename||A.keyFile,this.scopes=A.scopes,this.clientOptions=A.clientOptions||{},this.jsonContent=A.credentials||null,this.apiKey=A.apiKey||this.clientOptions.apiKey||null,this.apiKey&&(this.jsonContent||this.clientOptions.credentials))throw new RangeError(pY.GoogleAuthExceptionMessages.API_KEY_WITH_CREDENTIALS);if(A.universeDomain)this.clientOptions.universeDomain=A.universeDomain}setGapicJWTValues(A){A.defaultServicePath=this.defaultServicePath,A.useJWTAccessWithScope=this.useJWTAccessWithScope,A.defaultScopes=this.defaultScopes}getProjectId(A){if(A)this.getProjectIdAsync().then((B)=>A(null,B),A);else return this.getProjectIdAsync()}async getProjectIdOptional(){try{return await this.getProjectId()}catch(A){if(A instanceof Error&&A.message===pY.GoogleAuthExceptionMessages.NO_PROJECT_ID_FOUND)return null;else throw A}}async findAndCacheProjectId(){let A=null;if(A||(A=await this.getProductionProjectId()),A||(A=await this.getFileProjectId()),A||(A=await this.getDefaultServiceProjectId()),A||(A=await this.getGCEProjectId()),A||(A=await this.getExternalAccountClientProjectId()),A)return this._cachedProjectId=A,A;else throw new Error(pY.GoogleAuthExceptionMessages.NO_PROJECT_ID_FOUND)}async getProjectIdAsync(){if(this._cachedProjectId)return this._cachedProjectId;if(!this._findProjectIdPromise)this._findProjectIdPromise=this.findAndCacheProjectId();return this._findProjectIdPromise}async getUniverseDomainFromMetadataServer(){var A;let B;try{B=await ZZ1.universe("universe-domain"),B||(B=VU0.DEFAULT_UNIVERSE)}catch(Q){if(Q&&((A=Q===null||Q===void 0?void 0:Q.response)===null||A===void 0?void 0:A.status)===404)B=VU0.DEFAULT_UNIVERSE;else throw Q}return B}async getUniverseDomain(){let A=kEB.originalOrCamelOptions(this.clientOptions).get("universe_domain");try{A!==null&&A!==void 0||(A=(await this.getClient()).universeDomain)}catch(B){A!==null&&A!==void 0||(A=VU0.DEFAULT_UNIVERSE)}return A}getAnyScopes(){return this.scopes||this.defaultScopes}getApplicationDefault(A={},B){let Q;if(typeof A==="function")B=A;else Q=A;if(B)this.getApplicationDefaultAsync(Q).then((D)=>B(null,D.credential,D.projectId),B);else return this.getApplicationDefaultAsync(Q)}async getApplicationDefaultAsync(A={}){if(this.cachedCredential)return await nx(this,ax,"m",ee).call(this,this.cachedCredential,null);let B;if(B=await this._tryGetApplicationCredentialsFromEnvironmentVariable(A),B){if(B instanceof re.JWT)B.scopes=this.scopes;else if(B instanceof GZ1.BaseExternalAccountClient)B.scopes=this.getAnyScopes();return await nx(this,ax,"m",ee).call(this,B)}if(B=await this._tryGetApplicationCredentialsFromWellKnownFile(A),B){if(B instanceof re.JWT)B.scopes=this.scopes;else if(B instanceof GZ1.BaseExternalAccountClient)B.scopes=this.getAnyScopes();return await nx(this,ax,"m",ee).call(this,B)}if(await this._checkIsGCE())return A.scopes=this.getAnyScopes(),await nx(this,ax,"m",ee).call(this,new C18.Compute(A));throw new Error(pY.GoogleAuthExceptionMessages.NO_ADC_FOUND)}async _checkIsGCE(){if(this.checkIsGCE===void 0)this.checkIsGCE=ZZ1.getGCPResidency()||await ZZ1.isAvailable();return this.checkIsGCE}async _tryGetApplicationCredentialsFromEnvironmentVariable(A){let B=process.env.GOOGLE_APPLICATION_CREDENTIALS||process.env.google_application_credentials;if(!B||B.length===0)return null;try{return this._getApplicationCredentialsFromFilePath(B,A)}catch(Q){if(Q instanceof Error)Q.message=`Unable to read the credential file specified by the GOOGLE_APPLICATION_CREDENTIALS environment variable: ${Q.message}`;throw Q}}async _tryGetApplicationCredentialsFromWellKnownFile(A){let B=null;if(this._isWindows())B=process.env.APPDATA;else{let D=process.env.HOME;if(D)B=CU0.join(D,".config")}if(B){if(B=CU0.join(B,"gcloud","application_default_credentials.json"),!FZ1.existsSync(B))B=null}if(!B)return null;return await this._getApplicationCredentialsFromFilePath(B,A)}async _getApplicationCredentialsFromFilePath(A,B={}){if(!A||A.length===0)throw new Error("The file path is invalid.");try{if(A=FZ1.realpathSync(A),!FZ1.lstatSync(A).isFile())throw new Error}catch(D){if(D instanceof Error)D.message=`The file at ${A} does not exist, or it is not a file. ${D.message}`;throw D}let Q=FZ1.createReadStream(A);return this.fromStream(Q,B)}fromImpersonatedJSON(A){var B,Q,D,Z;if(!A)throw new Error("Must pass in a JSON object containing an  impersonated refresh token");if(A.type!==oe.IMPERSONATED_ACCOUNT_TYPE)throw new Error(`The incoming JSON object does not have the "${oe.IMPERSONATED_ACCOUNT_TYPE}" type`);if(!A.source_credentials)throw new Error("The incoming JSON object does not contain a source_credentials field");if(!A.service_account_impersonation_url)throw new Error("The incoming JSON object does not contain a service_account_impersonation_url field");let G=this.fromJSON(A.source_credentials);if(((B=A.service_account_impersonation_url)===null||B===void 0?void 0:B.length)>256)throw new RangeError(`Target principal is too long: ${A.service_account_impersonation_url}`);let F=(D=(Q=/(?<target>[^/]+):(generateAccessToken|generateIdToken)$/.exec(A.service_account_impersonation_url))===null||Q===void 0?void 0:Q.groups)===null||D===void 0?void 0:D.target;if(!F)throw new RangeError(`Cannot extract target principal from ${A.service_account_impersonation_url}`);let I=(Z=this.getAnyScopes())!==null&&Z!==void 0?Z:[];return new oe.Impersonated({...A,sourceClient:G,targetPrincipal:F,targetScopes:Array.isArray(I)?I:[I]})}fromJSON(A,B={}){let Q,D=kEB.originalOrCamelOptions(B).get("universe_domain");if(A.type===SEB.USER_REFRESH_ACCOUNT_TYPE)Q=new SEB.UserRefreshClient(B),Q.fromJSON(A);else if(A.type===oe.IMPERSONATED_ACCOUNT_TYPE)Q=this.fromImpersonatedJSON(A);else if(A.type===GZ1.EXTERNAL_ACCOUNT_TYPE)Q=z18.ExternalAccountClient.fromJSON(A,B),Q.scopes=this.getAnyScopes();else if(A.type===jEB.EXTERNAL_ACCOUNT_AUTHORIZED_USER_TYPE)Q=new jEB.ExternalAccountAuthorizedUserClient(A,B);else B.scopes=this.scopes,Q=new re.JWT(B),this.setGapicJWTValues(Q),Q.fromJSON(A);if(D)Q.universeDomain=D;return Q}_cacheClientFromJSON(A,B){let Q=this.fromJSON(A,B);return this.jsonContent=A,this.cachedCredential=Q,Q}fromStream(A,B={},Q){let D={};if(typeof B==="function")Q=B;else D=B;if(Q)this.fromStreamAsync(A,D).then((Z)=>Q(null,Z),Q);else return this.fromStreamAsync(A,D)}fromStreamAsync(A,B){return new Promise((Q,D)=>{if(!A)throw new Error("Must pass in a stream containing the Google auth settings.");let Z=[];A.setEncoding("utf8").on("error",D).on("data",(G)=>Z.push(G)).on("end",()=>{try{try{let G=JSON.parse(Z.join("")),F=this._cacheClientFromJSON(G,B);return Q(F)}catch(G){if(!this.keyFilename)throw G;let F=new re.JWT({...this.clientOptions,keyFile:this.keyFilename});return this.cachedCredential=F,this.setGapicJWTValues(F),Q(F)}}catch(G){return D(G)}})})}fromAPIKey(A,B={}){return new re.JWT({...B,apiKey:A})}_isWindows(){let A=J18.platform();if(A&&A.length>=3){if(A.substring(0,3).toLowerCase()==="win")return!0}return!1}async getDefaultServiceProjectId(){return new Promise((A)=>{W18.exec("gcloud config config-helper --format json",(B,Q)=>{if(!B&&Q)try{let D=JSON.parse(Q).configuration.properties.core.project;A(D);return}catch(D){}A(null)})})}getProductionProjectId(){return process.env.GCLOUD_PROJECT||process.env.GOOGLE_CLOUD_PROJECT||process.env.gcloud_project||process.env.google_cloud_project}async getFileProjectId(){if(this.cachedCredential)return this.cachedCredential.projectId;if(this.keyFilename){let B=await this.getClient();if(B&&B.projectId)return B.projectId}let A=await this._tryGetApplicationCredentialsFromEnvironmentVariable();if(A)return A.projectId;else return null}async getExternalAccountClientProjectId(){if(!this.jsonContent||this.jsonContent.type!==GZ1.EXTERNAL_ACCOUNT_TYPE)return null;return await(await this.getClient()).getProjectId()}async getGCEProjectId(){try{return await ZZ1.project("project-id")}catch(A){return null}}getCredentials(A){if(A)this.getCredentialsAsync().then((B)=>A(null,B),A);else return this.getCredentialsAsync()}async getCredentialsAsync(){let A=await this.getClient();if(A instanceof oe.Impersonated)return{client_email:A.getTargetPrincipal()};if(A instanceof GZ1.BaseExternalAccountClient){let B=A.getServiceAccountEmail();if(B)return{client_email:B,universe_domain:A.universeDomain}}if(this.jsonContent)return{client_email:this.jsonContent.client_email,private_key:this.jsonContent.private_key,universe_domain:this.jsonContent.universe_domain};if(await this._checkIsGCE()){let[B,Q]=await Promise.all([ZZ1.instance("service-accounts/default/email"),this.getUniverseDomain()]);return{client_email:B,universe_domain:Q}}throw new Error(pY.GoogleAuthExceptionMessages.NO_CREDENTIALS_FOUND)}async getClient(){if(this.cachedCredential)return this.cachedCredential;PEB(this,te,nx(this,te,"f")||nx(this,ax,"m",yEB).call(this),"f");try{return await nx(this,te,"f")}finally{PEB(this,te,null,"f")}}async getIdTokenClient(A){let B=await this.getClient();if(!("fetchIdToken"in B))throw new Error("Cannot fetch ID token in this environment, use GCE or set the GOOGLE_APPLICATION_CREDENTIALS environment variable to a service account credentials JSON file.");return new K18.IdTokenClient({targetAudience:A,idTokenProvider:B})}async getAccessToken(){return(await(await this.getClient()).getAccessToken()).token}async getRequestHeaders(A){return(await this.getClient()).getRequestHeaders(A)}async authorizeRequest(A){A=A||{};let B=A.url||A.uri,D=await(await this.getClient()).getRequestHeaders(B);return A.headers=Object.assign(A.headers||{},D),A}async request(A){return(await this.getClient()).request(A)}getEnv(){return H18.getEnv()}async sign(A,B){let Q=await this.getClient(),D=await this.getUniverseDomain();if(B=B||`https://iamcredentials.${D}/v1/projects/-/serviceAccounts/`,Q instanceof oe.Impersonated)return(await Q.sign(A)).signedBlob;let Z=X18.createCrypto();if(Q instanceof re.JWT&&Q.key)return await Z.sign(Q.key,A);let G=await this.getCredentials();if(!G.client_email)throw new Error("Cannot sign data without `client_email`.");return this.signBlob(Z,G.client_email,A,B)}async signBlob(A,B,Q,D){let Z=new URL(D+`${B}:signBlob`);return(await this.request({method:"POST",url:Z.href,data:{payload:A.encodeBase64StringUtf8(Q)},retry:!0,retryConfig:{httpMethodsToRetry:["POST"]}})).data.signedBlob}}pY.GoogleAuth=KU0;te=new WeakMap,ax=new WeakSet,ee=async function A(B,Q=process.env.GOOGLE_CLOUD_QUOTA_PROJECT||null){let D=await this.getProjectIdOptional();if(Q)B.quotaProjectId=Q;return this.cachedCredential=B,{credential:B,projectId:D}},yEB=async function A(){if(this.jsonContent)return this._cacheClientFromJSON(this.jsonContent,this.clientOptions);else if(this.keyFilename){let B=CU0.resolve(this.keyFilename),Q=FZ1.createReadStream(B);return await this.fromStreamAsync(Q,this.clientOptions)}else if(this.apiKey){let B=await this.fromAPIKey(this.apiKey,this.clientOptions);B.scopes=this.scopes;let{credential:Q}=await nx(this,ax,"m",ee).call(this,B);return Q}else{let{credential:B}=await this.getApplicationDefaultAsync(this.clientOptions);return B}};KU0.DefaultTransporter=V18.DefaultTransporter});
var aE0=E((azB)=>{Object.defineProperty(azB,"__esModule",{value:!0});azB.IdentityPoolClient=void 0;var Te6=ix(),iE0=lx(),Pe6=czB(),Se6=nzB();class nE0 extends Te6.BaseExternalAccountClient{constructor(A,B){super(A,B);let Q=iE0.originalOrCamelOptions(A),D=Q.get("credential_source"),Z=Q.get("subject_token_supplier");if(!D&&!Z)throw new Error("A credential source or subject token supplier must be specified.");if(D&&Z)throw new Error("Only one of credential source or subject token supplier can be specified.");if(Z)this.subjectTokenSupplier=Z,this.credentialSourceType="programmatic";else{let G=iE0.originalOrCamelOptions(D),F=iE0.originalOrCamelOptions(G.get("format")),I=F.get("type")||"text",Y=F.get("subject_token_field_name");if(I!=="json"&&I!=="text")throw new Error(`Invalid credential_source format "${I}"`);if(I==="json"&&!Y)throw new Error("Missing subject_token_field_name for JSON credential_source format");let W=G.get("file"),J=G.get("url"),X=G.get("headers");if(W&&J)throw new Error('No valid Identity Pool "credential_source" provided, must be either file or url.');else if(W&&!J)this.credentialSourceType="file",this.subjectTokenSupplier=new Pe6.FileSubjectTokenSupplier({filePath:W,formatType:I,subjectTokenFieldName:Y});else if(!W&&J)this.credentialSourceType="url",this.subjectTokenSupplier=new Se6.UrlSubjectTokenSupplier({url:J,formatType:I,subjectTokenFieldName:Y,headers:X,additionalGaxiosOptions:nE0.RETRY_CONFIG});else throw new Error('No valid Identity Pool "credential_source" provided, must be either file or url.')}}async retrieveSubjectToken(){return this.subjectTokenSupplier.getSubjectToken(this.supplierContext)}}azB.IdentityPoolClient=nE0});
var aEB=E((v3)=>{Object.defineProperty(v3,"__esModule",{value:!0});v3.GoogleAuth=v3.auth=v3.DefaultTransporter=v3.PassThroughClient=v3.ExecutableError=v3.PluggableAuthClient=v3.DownscopedClient=v3.BaseExternalAccountClient=v3.ExternalAccountClient=v3.IdentityPoolClient=v3.AwsRequestSigner=v3.AwsClient=v3.UserRefreshClient=v3.LoginTicket=v3.ClientAuthentication=v3.OAuth2Client=v3.CodeChallengeMethod=v3.Impersonated=v3.JWT=v3.JWTAccess=v3.IdTokenClient=v3.IAMAuth=v3.GCPEnv=v3.Compute=v3.DEFAULT_UNIVERSE=v3.AuthClient=v3.gaxios=v3.gcpMetadata=void 0;var pEB=_EB();Object.defineProperty(v3,"GoogleAuth",{enumerable:!0,get:function(){return pEB.GoogleAuth}});v3.gcpMetadata=nD1();v3.gaxios=X$();var iEB=PM();Object.defineProperty(v3,"AuthClient",{enumerable:!0,get:function(){return iEB.AuthClient}});Object.defineProperty(v3,"DEFAULT_UNIVERSE",{enumerable:!0,get:function(){return iEB.DEFAULT_UNIVERSE}});var O18=HE0();Object.defineProperty(v3,"Compute",{enumerable:!0,get:function(){return O18.Compute}});var T18=EE0();Object.defineProperty(v3,"GCPEnv",{enumerable:!0,get:function(){return T18.GCPEnv}});var P18=fEB();Object.defineProperty(v3,"IAMAuth",{enumerable:!0,get:function(){return P18.IAMAuth}});var S18=zE0();Object.defineProperty(v3,"IdTokenClient",{enumerable:!0,get:function(){return S18.IdTokenClient}});var j18=jE0();Object.defineProperty(v3,"JWTAccess",{enumerable:!0,get:function(){return j18.JWTAccess}});var k18=yE0();Object.defineProperty(v3,"JWT",{enumerable:!0,get:function(){return k18.JWT}});var y18=xE0();Object.defineProperty(v3,"Impersonated",{enumerable:!0,get:function(){return y18.Impersonated}});var EU0=$m();Object.defineProperty(v3,"CodeChallengeMethod",{enumerable:!0,get:function(){return EU0.CodeChallengeMethod}});Object.defineProperty(v3,"OAuth2Client",{enumerable:!0,get:function(){return EU0.OAuth2Client}});Object.defineProperty(v3,"ClientAuthentication",{enumerable:!0,get:function(){return EU0.ClientAuthentication}});var _18=VE0();Object.defineProperty(v3,"LoginTicket",{enumerable:!0,get:function(){return _18.LoginTicket}});var x18=_E0();Object.defineProperty(v3,"UserRefreshClient",{enumerable:!0,get:function(){return x18.UserRefreshClient}});var v18=tE0();Object.defineProperty(v3,"AwsClient",{enumerable:!0,get:function(){return v18.AwsClient}});var b18=sE0();Object.defineProperty(v3,"AwsRequestSigner",{enumerable:!0,get:function(){return b18.AwsRequestSigner}});var f18=aE0();Object.defineProperty(v3,"IdentityPoolClient",{enumerable:!0,get:function(){return f18.IdentityPoolClient}});var h18=JU0();Object.defineProperty(v3,"ExternalAccountClient",{enumerable:!0,get:function(){return h18.ExternalAccountClient}});var g18=ix();Object.defineProperty(v3,"BaseExternalAccountClient",{enumerable:!0,get:function(){return g18.BaseExternalAccountClient}});var u18=mEB();Object.defineProperty(v3,"DownscopedClient",{enumerable:!0,get:function(){return u18.DownscopedClient}});var nEB=x_1();Object.defineProperty(v3,"PluggableAuthClient",{enumerable:!0,get:function(){return nEB.PluggableAuthClient}});Object.defineProperty(v3,"ExecutableError",{enumerable:!0,get:function(){return nEB.ExecutableError}});var m18=lEB();Object.defineProperty(v3,"PassThroughClient",{enumerable:!0,get:function(){return m18.PassThroughClient}});var d18=sD1();Object.defineProperty(v3,"DefaultTransporter",{enumerable:!0,get:function(){return d18.DefaultTransporter}});var c18=new pEB.GoogleAuth;v3.auth=c18});
var aQ1=E((XqA)=>{Object.defineProperty(XqA,"__esModule",{value:!0});XqA.default=void 0;XqA.unsafeStringify=JqA;var OwQ=TwQ(nQ1());function TwQ(A){return A&&A.__esModule?A:{default:A}}var $Y=[];for(let A=0;A<256;++A)$Y.push((A+256).toString(16).slice(1));function JqA(A,B=0){return $Y[A[B+0]]+$Y[A[B+1]]+$Y[A[B+2]]+$Y[A[B+3]]+"-"+$Y[A[B+4]]+$Y[A[B+5]]+"-"+$Y[A[B+6]]+$Y[A[B+7]]+"-"+$Y[A[B+8]]+$Y[A[B+9]]+"-"+$Y[A[B+10]]+$Y[A[B+11]]+$Y[A[B+12]]+$Y[A[B+13]]+$Y[A[B+14]]+$Y[A[B+15]]}function PwQ(A,B=0){let Q=JqA(A,B);if(!OwQ.default(Q))throw TypeError("Stringified UUID is invalid");return Q}var SwQ=PwQ;XqA.default=SwQ});
var az0=E((WKB,X_1)=>{(function(A){var B,Q=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,D=Math.ceil,Z=Math.floor,G="[BigNumber Error] ",F=G+"Number primitive has more than 15 significant digits: ",I=1********000000,Y=14,W=9007199254740991,J=[1,10,100,1000,1e4,1e5,1e6,1e7,1e8,1e9,1********00,1********000,1********0000,1********00000],X=1e7,V=1e9;function C(R){var T,j,f,k=G1.prototype={constructor:G1,toString:null,valueOf:null},c=new G1(1),h=20,n=4,a=-7,x=21,e=-1e7,W1=1e7,U1=!1,y1=1,W0=0,F0={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},g1="0123456789abcdefghijklmnopqrstuvwxyz",K1=!0;function G1(B1,A1){var I1,q1,P1,Q1,f1,l1,n1,V0,I0=this;if(!(I0 instanceof G1))return new G1(B1,A1);if(A1==null){if(B1&&B1._isBigNumber===!0){if(I0.s=B1.s,!B1.c||B1.e>W1)I0.c=I0.e=null;else if(B1.e<e)I0.c=[I0.e=0];else I0.e=B1.e,I0.c=B1.c.slice();return}if((l1=typeof B1=="number")&&B1*0==0){if(I0.s=1/B1<0?(B1=-B1,-1):1,B1===~~B1){for(Q1=0,f1=B1;f1>=10;f1/=10,Q1++);if(Q1>W1)I0.c=I0.e=null;else I0.e=Q1,I0.c=[B1];return}V0=String(B1)}else{if(!Q.test(V0=String(B1)))return f(I0,V0,l1);I0.s=V0.charCodeAt(0)==45?(V0=V0.slice(1),-1):1}if((Q1=V0.indexOf("."))>-1)V0=V0.replace(".","");if((f1=V0.search(/e/i))>0){if(Q1<0)Q1=f1;Q1+=+V0.slice(f1+1),V0=V0.substring(0,f1)}else if(Q1<0)Q1=V0.length}else{if($(A1,2,g1.length,"Base"),A1==10&&K1)return I0=new G1(B1),i1(I0,h+I0.e+1,n);if(V0=String(B1),l1=typeof B1=="number"){if(B1*0!=0)return f(I0,V0,l1,A1);if(I0.s=1/B1<0?(V0=V0.slice(1),-1):1,G1.DEBUG&&V0.replace(/^0\.0*|\./,"").length>15)throw Error(F+B1)}else I0.s=V0.charCodeAt(0)===45?(V0=V0.slice(1),-1):1;I1=g1.slice(0,A1),Q1=f1=0;for(n1=V0.length;f1<n1;f1++)if(I1.indexOf(q1=V0.charAt(f1))<0){if(q1=="."){if(f1>Q1){Q1=n1;continue}}else if(!P1){if(V0==V0.toUpperCase()&&(V0=V0.toLowerCase())||V0==V0.toLowerCase()&&(V0=V0.toUpperCase())){P1=!0,f1=-1,Q1=0;continue}}return f(I0,String(B1),l1,A1)}if(l1=!1,V0=j(V0,A1,10,I0.s),(Q1=V0.indexOf("."))>-1)V0=V0.replace(".","");else Q1=V0.length}for(f1=0;V0.charCodeAt(f1)===48;f1++);for(n1=V0.length;V0.charCodeAt(--n1)===48;);if(V0=V0.slice(f1,++n1)){if(n1-=f1,l1&&G1.DEBUG&&n1>15&&(B1>W||B1!==Z(B1)))throw Error(F+I0.s*B1);if((Q1=Q1-f1-1)>W1)I0.c=I0.e=null;else if(Q1<e)I0.c=[I0.e=0];else{if(I0.e=Q1,I0.c=[],f1=(Q1+1)%Y,Q1<0)f1+=Y;if(f1<n1){if(f1)I0.c.push(+V0.slice(0,f1));for(n1-=Y;f1<n1;)I0.c.push(+V0.slice(f1,f1+=Y));f1=Y-(V0=V0.slice(f1)).length}else f1-=n1;for(;f1--;V0+="0");I0.c.push(+V0)}}else I0.c=[I0.e=0]}G1.clone=C,G1.ROUND_UP=0,G1.ROUND_DOWN=1,G1.ROUND_CEIL=2,G1.ROUND_FLOOR=3,G1.ROUND_HALF_UP=4,G1.ROUND_HALF_DOWN=5,G1.ROUND_HALF_EVEN=6,G1.ROUND_HALF_CEIL=7,G1.ROUND_HALF_FLOOR=8,G1.EUCLID=9,G1.config=G1.set=function(B1){var A1,I1;if(B1!=null)if(typeof B1=="object"){if(B1.hasOwnProperty(A1="DECIMAL_PLACES"))I1=B1[A1],$(I1,0,V,A1),h=I1;if(B1.hasOwnProperty(A1="ROUNDING_MODE"))I1=B1[A1],$(I1,0,8,A1),n=I1;if(B1.hasOwnProperty(A1="EXPONENTIAL_AT"))if(I1=B1[A1],I1&&I1.pop)$(I1[0],-V,0,A1),$(I1[1],0,V,A1),a=I1[0],x=I1[1];else $(I1,-V,V,A1),a=-(x=I1<0?-I1:I1);if(B1.hasOwnProperty(A1="RANGE"))if(I1=B1[A1],I1&&I1.pop)$(I1[0],-V,-1,A1),$(I1[1],1,V,A1),e=I1[0],W1=I1[1];else if($(I1,-V,V,A1),I1)e=-(W1=I1<0?-I1:I1);else throw Error(G+A1+" cannot be zero: "+I1);if(B1.hasOwnProperty(A1="CRYPTO"))if(I1=B1[A1],I1===!!I1)if(I1)if(typeof crypto!="undefined"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))U1=I1;else throw U1=!I1,Error(G+"crypto unavailable");else U1=I1;else throw Error(G+A1+" not true or false: "+I1);if(B1.hasOwnProperty(A1="MODULO_MODE"))I1=B1[A1],$(I1,0,9,A1),y1=I1;if(B1.hasOwnProperty(A1="POW_PRECISION"))I1=B1[A1],$(I1,0,V,A1),W0=I1;if(B1.hasOwnProperty(A1="FORMAT"))if(I1=B1[A1],typeof I1=="object")F0=I1;else throw Error(G+A1+" not an object: "+I1);if(B1.hasOwnProperty(A1="ALPHABET"))if(I1=B1[A1],typeof I1=="string"&&!/^.?$|[+\-.\s]|(.).*\1/.test(I1))K1=I1.slice(0,10)=="0123456789",g1=I1;else throw Error(G+A1+" invalid: "+I1)}else throw Error(G+"Object expected: "+B1);return{DECIMAL_PLACES:h,ROUNDING_MODE:n,EXPONENTIAL_AT:[a,x],RANGE:[e,W1],CRYPTO:U1,MODULO_MODE:y1,POW_PRECISION:W0,FORMAT:F0,ALPHABET:g1}},G1.isBigNumber=function(B1){if(!B1||B1._isBigNumber!==!0)return!1;if(!G1.DEBUG)return!0;var A1,I1,q1=B1.c,P1=B1.e,Q1=B1.s;A:if({}.toString.call(q1)=="[object Array]"){if((Q1===1||Q1===-1)&&P1>=-V&&P1<=V&&P1===Z(P1)){if(q1[0]===0){if(P1===0&&q1.length===1)return!0;break A}if(A1=(P1+1)%Y,A1<1)A1+=Y;if(String(q1[0]).length==A1){for(A1=0;A1<q1.length;A1++)if(I1=q1[A1],I1<0||I1>=I||I1!==Z(I1))break A;if(I1!==0)return!0}}}else if(q1===null&&P1===null&&(Q1===null||Q1===1||Q1===-1))return!0;throw Error(G+"Invalid BigNumber: "+B1)},G1.maximum=G1.max=function(){return M1(arguments,-1)},G1.minimum=G1.min=function(){return M1(arguments,1)},G1.random=function(){var B1=9007199254740992,A1=Math.random()*B1&2097151?function(){return Z(Math.random()*B1)}:function(){return(Math.random()*1073741824|0)*8388608+(Math.random()*8388608|0)};return function(I1){var q1,P1,Q1,f1,l1,n1=0,V0=[],I0=new G1(c);if(I1==null)I1=h;else $(I1,0,V);if(f1=D(I1/Y),U1)if(crypto.getRandomValues){q1=crypto.getRandomValues(new Uint32Array(f1*=2));for(;n1<f1;)if(l1=q1[n1]*131072+(q1[n1+1]>>>11),l1>=9********0000000)P1=crypto.getRandomValues(new Uint32Array(2)),q1[n1]=P1[0],q1[n1+1]=P1[1];else V0.push(l1%1********000000),n1+=2;n1=f1/2}else if(crypto.randomBytes){q1=crypto.randomBytes(f1*=7);for(;n1<f1;)if(l1=(q1[n1]&31)*281474976710656+q1[n1+1]**************+q1[n1+2]***********+q1[n1+3]*16777216+(q1[n1+4]<<16)+(q1[n1+5]<<8)+q1[n1+6],l1>=9********0000000)crypto.randomBytes(7).copy(q1,n1);else V0.push(l1%1********000000),n1+=7;n1=f1/7}else throw U1=!1,Error(G+"crypto unavailable");if(!U1){for(;n1<f1;)if(l1=A1(),l1<9********0000000)V0[n1++]=l1%1********000000}if(f1=V0[--n1],I1%=Y,f1&&I1)l1=J[Y-I1],V0[n1]=Z(f1/l1)*l1;for(;V0[n1]===0;V0.pop(),n1--);if(n1<0)V0=[Q1=0];else{for(Q1=-1;V0[0]===0;V0.splice(0,1),Q1-=Y);for(n1=1,l1=V0[0];l1>=10;l1/=10,n1++);if(n1<Y)Q1-=Y-n1}return I0.e=Q1,I0.c=V0,I0}}(),G1.sum=function(){var B1=1,A1=arguments,I1=new G1(A1[0]);for(;B1<A1.length;)I1=I1.plus(A1[B1++]);return I1},j=function(){var B1="0123456789";function A1(I1,q1,P1,Q1){var f1,l1=[0],n1,V0=0,I0=I1.length;for(;V0<I0;){for(n1=l1.length;n1--;l1[n1]*=q1);l1[0]+=Q1.indexOf(I1.charAt(V0++));for(f1=0;f1<l1.length;f1++)if(l1[f1]>P1-1){if(l1[f1+1]==null)l1[f1+1]=0;l1[f1+1]+=l1[f1]/P1|0,l1[f1]%=P1}}return l1.reverse()}return function(I1,q1,P1,Q1,f1){var l1,n1,V0,I0,M0,YA,m0,SA,v2=I1.indexOf("."),Y2=h,N2=n;if(v2>=0)I0=W0,W0=0,I1=I1.replace(".",""),SA=new G1(q1),YA=SA.pow(I1.length-v2),W0=I0,SA.c=A1(O(H(YA.c),YA.e,"0"),10,P1,B1),SA.e=SA.c.length;m0=A1(I1,q1,P1,f1?(l1=g1,B1):(l1=B1,g1)),V0=I0=m0.length;for(;m0[--I0]==0;m0.pop());if(!m0[0])return l1.charAt(0);if(v2<0)--V0;else YA.c=m0,YA.e=V0,YA.s=Q1,YA=T(YA,SA,Y2,N2,P1),m0=YA.c,M0=YA.r,V0=YA.e;if(n1=V0+Y2+1,v2=m0[n1],I0=P1/2,M0=M0||n1<0||m0[n1+1]!=null,M0=N2<4?(v2!=null||M0)&&(N2==0||N2==(YA.s<0?3:2)):v2>I0||v2==I0&&(N2==4||M0||N2==6&&m0[n1-1]&1||N2==(YA.s<0?8:7)),n1<1||!m0[0])I1=M0?O(l1.charAt(1),-Y2,l1.charAt(0)):l1.charAt(0);else{if(m0.length=n1,M0){for(--P1;++m0[--n1]>P1;)if(m0[n1]=0,!n1)++V0,m0=[1].concat(m0)}for(I0=m0.length;!m0[--I0];);for(v2=0,I1="";v2<=I0;I1+=l1.charAt(m0[v2++]));I1=O(I1,V0,l1.charAt(0))}return I1}}(),T=function(){function B1(q1,P1,Q1){var f1,l1,n1,V0,I0=0,M0=q1.length,YA=P1%X,m0=P1/X|0;for(q1=q1.slice();M0--;)n1=q1[M0]%X,V0=q1[M0]/X|0,f1=m0*n1+V0*YA,l1=YA*n1+f1%X*X+I0,I0=(l1/Q1|0)+(f1/X|0)+m0*V0,q1[M0]=l1%Q1;if(I0)q1=[I0].concat(q1);return q1}function A1(q1,P1,Q1,f1){var l1,n1;if(Q1!=f1)n1=Q1>f1?1:-1;else for(l1=n1=0;l1<Q1;l1++)if(q1[l1]!=P1[l1]){n1=q1[l1]>P1[l1]?1:-1;break}return n1}function I1(q1,P1,Q1,f1){var l1=0;for(;Q1--;)q1[Q1]-=l1,l1=q1[Q1]<P1[Q1]?1:0,q1[Q1]=l1*f1+q1[Q1]-P1[Q1];for(;!q1[0]&&q1.length>1;q1.splice(0,1));}return function(q1,P1,Q1,f1,l1){var n1,V0,I0,M0,YA,m0,SA,v2,Y2,N2,b2,_B,W4,gA,X2,L2,lA,uA=q1.s==P1.s?1:-1,r2=q1.c,gB=P1.c;if(!r2||!r2[0]||!gB||!gB[0])return new G1(!q1.s||!P1.s||(r2?gB&&r2[0]==gB[0]:!gB)?NaN:r2&&r2[0]==0||!gB?uA*0:uA/0);if(v2=new G1(uA),Y2=v2.c=[],V0=q1.e-P1.e,uA=Q1+V0+1,!l1)l1=I,V0=K(q1.e/Y)-K(P1.e/Y),uA=uA/Y|0;for(I0=0;gB[I0]==(r2[I0]||0);I0++);if(gB[I0]>(r2[I0]||0))V0--;if(uA<0)Y2.push(1),M0=!0;else{if(gA=r2.length,L2=gB.length,I0=0,uA+=2,YA=Z(l1/(gB[0]+1)),YA>1)gB=B1(gB,YA,l1),r2=B1(r2,YA,l1),L2=gB.length,gA=r2.length;W4=L2,N2=r2.slice(0,L2),b2=N2.length;for(;b2<L2;N2[b2++]=0);if(lA=gB.slice(),lA=[0].concat(lA),X2=gB[0],gB[1]>=l1/2)X2++;do{if(YA=0,n1=A1(gB,N2,L2,b2),n1<0){if(_B=N2[0],L2!=b2)_B=_B*l1+(N2[1]||0);if(YA=Z(_B/X2),YA>1){if(YA>=l1)YA=l1-1;m0=B1(gB,YA,l1),SA=m0.length,b2=N2.length;while(A1(m0,N2,SA,b2)==1)YA--,I1(m0,L2<SA?lA:gB,SA,l1),SA=m0.length,n1=1}else{if(YA==0)n1=YA=1;m0=gB.slice(),SA=m0.length}if(SA<b2)m0=[0].concat(m0);if(I1(N2,m0,b2,l1),b2=N2.length,n1==-1)while(A1(gB,N2,L2,b2)<1)YA++,I1(N2,L2<b2?lA:gB,b2,l1),b2=N2.length}else if(n1===0)YA++,N2=[0];if(Y2[I0++]=YA,N2[0])N2[b2++]=r2[W4]||0;else N2=[r2[W4]],b2=1}while((W4++<gA||N2[0]!=null)&&uA--);if(M0=N2[0]!=null,!Y2[0])Y2.splice(0,1)}if(l1==I){for(I0=1,uA=Y2[0];uA>=10;uA/=10,I0++);i1(v2,Q1+(v2.e=I0+V0*Y-1)+1,f1,M0)}else v2.e=V0,v2.r=+M0;return v2}}();function L1(B1,A1,I1,q1){var P1,Q1,f1,l1,n1;if(I1==null)I1=n;else $(I1,0,8);if(!B1.c)return B1.toString();if(P1=B1.c[0],f1=B1.e,A1==null)n1=H(B1.c),n1=q1==1||q1==2&&(f1<=a||f1>=x)?N(n1,f1):O(n1,f1,"0");else if(B1=i1(new G1(B1),A1,I1),Q1=B1.e,n1=H(B1.c),l1=n1.length,q1==1||q1==2&&(A1<=Q1||Q1<=a)){for(;l1<A1;n1+="0",l1++);n1=N(n1,Q1)}else if(A1-=f1,n1=O(n1,Q1,"0"),Q1+1>l1){if(--A1>0)for(n1+=".";A1--;n1+="0");}else if(A1+=Q1-l1,A1>0){if(Q1+1==l1)n1+=".";for(;A1--;n1+="0");}return B1.s<0&&P1?"-"+n1:n1}function M1(B1,A1){var I1,q1,P1=1,Q1=new G1(B1[0]);for(;P1<B1.length;P1++)if(q1=new G1(B1[P1]),!q1.s||(I1=z(Q1,q1))===A1||I1===0&&Q1.s===A1)Q1=q1;return Q1}function a1(B1,A1,I1){var q1=1,P1=A1.length;for(;!A1[--P1];A1.pop());for(P1=A1[0];P1>=10;P1/=10,q1++);if((I1=q1+I1*Y-1)>W1)B1.c=B1.e=null;else if(I1<e)B1.c=[B1.e=0];else B1.e=I1,B1.c=A1;return B1}f=function(){var B1=/^(-?)0([xbo])(?=\w[\w.]*$)/i,A1=/^([^.]+)\.$/,I1=/^\.([^.]+)$/,q1=/^-?(Infinity|NaN)$/,P1=/^\s*\+(?=[\w.])|^\s+|\s+$/g;return function(Q1,f1,l1,n1){var V0,I0=l1?f1:f1.replace(P1,"");if(q1.test(I0))Q1.s=isNaN(I0)?null:I0<0?-1:1;else{if(!l1){if(I0=I0.replace(B1,function(M0,YA,m0){return V0=(m0=m0.toLowerCase())=="x"?16:m0=="b"?2:8,!n1||n1==V0?YA:M0}),n1)V0=n1,I0=I0.replace(A1,"$1").replace(I1,"0.$1");if(f1!=I0)return new G1(I0,V0)}if(G1.DEBUG)throw Error(G+"Not a"+(n1?" base "+n1:"")+" number: "+f1);Q1.s=null}Q1.c=Q1.e=null}}();function i1(B1,A1,I1,q1){var P1,Q1,f1,l1,n1,V0,I0,M0=B1.c,YA=J;if(M0){A:{for(P1=1,l1=M0[0];l1>=10;l1/=10,P1++);if(Q1=A1-P1,Q1<0)Q1+=Y,f1=A1,n1=M0[V0=0],I0=Z(n1/YA[P1-f1-1]%10);else if(V0=D((Q1+1)/Y),V0>=M0.length)if(q1){for(;M0.length<=V0;M0.push(0));n1=I0=0,P1=1,Q1%=Y,f1=Q1-Y+1}else break A;else{n1=l1=M0[V0];for(P1=1;l1>=10;l1/=10,P1++);Q1%=Y,f1=Q1-Y+P1,I0=f1<0?0:Z(n1/YA[P1-f1-1]%10)}if(q1=q1||A1<0||M0[V0+1]!=null||(f1<0?n1:n1%YA[P1-f1-1]),q1=I1<4?(I0||q1)&&(I1==0||I1==(B1.s<0?3:2)):I0>5||I0==5&&(I1==4||q1||I1==6&&(Q1>0?f1>0?n1/YA[P1-f1]:0:M0[V0-1])%10&1||I1==(B1.s<0?8:7)),A1<1||!M0[0]){if(M0.length=0,q1)A1-=B1.e+1,M0[0]=YA[(Y-A1%Y)%Y],B1.e=-A1||0;else M0[0]=B1.e=0;return B1}if(Q1==0)M0.length=V0,l1=1,V0--;else M0.length=V0+1,l1=YA[Y-Q1],M0[V0]=f1>0?Z(n1/YA[P1-f1]%YA[f1])*l1:0;if(q1)for(;;)if(V0==0){for(Q1=1,f1=M0[0];f1>=10;f1/=10,Q1++);f1=M0[0]+=l1;for(l1=1;f1>=10;f1/=10,l1++);if(Q1!=l1){if(B1.e++,M0[0]==I)M0[0]=1}break}else{if(M0[V0]+=l1,M0[V0]!=I)break;M0[V0--]=0,l1=1}for(Q1=M0.length;M0[--Q1]===0;M0.pop());}if(B1.e>W1)B1.c=B1.e=null;else if(B1.e<e)B1.c=[B1.e=0]}return B1}function E0(B1){var A1,I1=B1.e;if(I1===null)return B1.toString();return A1=H(B1.c),A1=I1<=a||I1>=x?N(A1,I1):O(A1,I1,"0"),B1.s<0?"-"+A1:A1}if(k.absoluteValue=k.abs=function(){var B1=new G1(this);if(B1.s<0)B1.s=1;return B1},k.comparedTo=function(B1,A1){return z(this,new G1(B1,A1))},k.decimalPlaces=k.dp=function(B1,A1){var I1,q1,P1,Q1=this;if(B1!=null){if($(B1,0,V),A1==null)A1=n;else $(A1,0,8);return i1(new G1(Q1),B1+Q1.e+1,A1)}if(!(I1=Q1.c))return null;if(q1=((P1=I1.length-1)-K(this.e/Y))*Y,P1=I1[P1])for(;P1%10==0;P1/=10,q1--);if(q1<0)q1=0;return q1},k.dividedBy=k.div=function(B1,A1){return T(this,new G1(B1,A1),h,n)},k.dividedToIntegerBy=k.idiv=function(B1,A1){return T(this,new G1(B1,A1),0,1)},k.exponentiatedBy=k.pow=function(B1,A1){var I1,q1,P1,Q1,f1,l1,n1,V0,I0,M0=this;if(B1=new G1(B1),B1.c&&!B1.isInteger())throw Error(G+"Exponent not an integer: "+E0(B1));if(A1!=null)A1=new G1(A1);if(l1=B1.e>14,!M0.c||!M0.c[0]||M0.c[0]==1&&!M0.e&&M0.c.length==1||!B1.c||!B1.c[0])return I0=new G1(Math.pow(+E0(M0),l1?B1.s*(2-L(B1)):+E0(B1))),A1?I0.mod(A1):I0;if(n1=B1.s<0,A1){if(A1.c?!A1.c[0]:!A1.s)return new G1(NaN);if(q1=!n1&&M0.isInteger()&&A1.isInteger(),q1)M0=M0.mod(A1)}else if(B1.e>9&&(M0.e>0||M0.e<-1||(M0.e==0?M0.c[0]>1||l1&&M0.c[1]>=240000000:M0.c[0]<8********00000||l1&&M0.c[0]<=99999750000000))){if(Q1=M0.s<0&&L(B1)?-0:0,M0.e>-1)Q1=1/Q1;return new G1(n1?1/Q1:Q1)}else if(W0)Q1=D(W0/Y+2);if(l1){if(I1=new G1(0.5),n1)B1.s=1;V0=L(B1)}else P1=Math.abs(+E0(B1)),V0=P1%2;I0=new G1(c);for(;;){if(V0){if(I0=I0.times(M0),!I0.c)break;if(Q1){if(I0.c.length>Q1)I0.c.length=Q1}else if(q1)I0=I0.mod(A1)}if(P1){if(P1=Z(P1/2),P1===0)break;V0=P1%2}else if(B1=B1.times(I1),i1(B1,B1.e+1,1),B1.e>14)V0=L(B1);else{if(P1=+E0(B1),P1===0)break;V0=P1%2}if(M0=M0.times(M0),Q1){if(M0.c&&M0.c.length>Q1)M0.c.length=Q1}else if(q1)M0=M0.mod(A1)}if(q1)return I0;if(n1)I0=c.div(I0);return A1?I0.mod(A1):Q1?i1(I0,W0,n,f1):I0},k.integerValue=function(B1){var A1=new G1(this);if(B1==null)B1=n;else $(B1,0,8);return i1(A1,A1.e+1,B1)},k.isEqualTo=k.eq=function(B1,A1){return z(this,new G1(B1,A1))===0},k.isFinite=function(){return!!this.c},k.isGreaterThan=k.gt=function(B1,A1){return z(this,new G1(B1,A1))>0},k.isGreaterThanOrEqualTo=k.gte=function(B1,A1){return(A1=z(this,new G1(B1,A1)))===1||A1===0},k.isInteger=function(){return!!this.c&&K(this.e/Y)>this.c.length-2},k.isLessThan=k.lt=function(B1,A1){return z(this,new G1(B1,A1))<0},k.isLessThanOrEqualTo=k.lte=function(B1,A1){return(A1=z(this,new G1(B1,A1)))===-1||A1===0},k.isNaN=function(){return!this.s},k.isNegative=function(){return this.s<0},k.isPositive=function(){return this.s>0},k.isZero=function(){return!!this.c&&this.c[0]==0},k.minus=function(B1,A1){var I1,q1,P1,Q1,f1=this,l1=f1.s;if(B1=new G1(B1,A1),A1=B1.s,!l1||!A1)return new G1(NaN);if(l1!=A1)return B1.s=-A1,f1.plus(B1);var n1=f1.e/Y,V0=B1.e/Y,I0=f1.c,M0=B1.c;if(!n1||!V0){if(!I0||!M0)return I0?(B1.s=-A1,B1):new G1(M0?f1:NaN);if(!I0[0]||!M0[0])return M0[0]?(B1.s=-A1,B1):new G1(I0[0]?f1:n==3?-0:0)}if(n1=K(n1),V0=K(V0),I0=I0.slice(),l1=n1-V0){if(Q1=l1<0)l1=-l1,P1=I0;else V0=n1,P1=M0;P1.reverse();for(A1=l1;A1--;P1.push(0));P1.reverse()}else{q1=(Q1=(l1=I0.length)<(A1=M0.length))?l1:A1;for(l1=A1=0;A1<q1;A1++)if(I0[A1]!=M0[A1]){Q1=I0[A1]<M0[A1];break}}if(Q1)P1=I0,I0=M0,M0=P1,B1.s=-B1.s;if(A1=(q1=M0.length)-(I1=I0.length),A1>0)for(;A1--;I0[I1++]=0);A1=I-1;for(;q1>l1;){if(I0[--q1]<M0[q1]){for(I1=q1;I1&&!I0[--I1];I0[I1]=A1);--I0[I1],I0[q1]+=I}I0[q1]-=M0[q1]}for(;I0[0]==0;I0.splice(0,1),--V0);if(!I0[0])return B1.s=n==3?-1:1,B1.c=[B1.e=0],B1;return a1(B1,I0,V0)},k.modulo=k.mod=function(B1,A1){var I1,q1,P1=this;if(B1=new G1(B1,A1),!P1.c||!B1.s||B1.c&&!B1.c[0])return new G1(NaN);else if(!B1.c||P1.c&&!P1.c[0])return new G1(P1);if(y1==9)q1=B1.s,B1.s=1,I1=T(P1,B1,0,3),B1.s=q1,I1.s*=q1;else I1=T(P1,B1,0,y1);if(B1=P1.minus(I1.times(B1)),!B1.c[0]&&y1==1)B1.s=P1.s;return B1},k.multipliedBy=k.times=function(B1,A1){var I1,q1,P1,Q1,f1,l1,n1,V0,I0,M0,YA,m0,SA,v2,Y2,N2=this,b2=N2.c,_B=(B1=new G1(B1,A1)).c;if(!b2||!_B||!b2[0]||!_B[0]){if(!N2.s||!B1.s||b2&&!b2[0]&&!_B||_B&&!_B[0]&&!b2)B1.c=B1.e=B1.s=null;else if(B1.s*=N2.s,!b2||!_B)B1.c=B1.e=null;else B1.c=[0],B1.e=0;return B1}if(q1=K(N2.e/Y)+K(B1.e/Y),B1.s*=N2.s,n1=b2.length,M0=_B.length,n1<M0)SA=b2,b2=_B,_B=SA,P1=n1,n1=M0,M0=P1;for(P1=n1+M0,SA=[];P1--;SA.push(0));v2=I,Y2=X;for(P1=M0;--P1>=0;){I1=0,YA=_B[P1]%Y2,m0=_B[P1]/Y2|0;for(f1=n1,Q1=P1+f1;Q1>P1;)V0=b2[--f1]%Y2,I0=b2[f1]/Y2|0,l1=m0*V0+I0*YA,V0=YA*V0+l1%Y2*Y2+SA[Q1]+I1,I1=(V0/v2|0)+(l1/Y2|0)+m0*I0,SA[Q1--]=V0%v2;SA[Q1]=I1}if(I1)++q1;else SA.splice(0,1);return a1(B1,SA,q1)},k.negated=function(){var B1=new G1(this);return B1.s=-B1.s||null,B1},k.plus=function(B1,A1){var I1,q1=this,P1=q1.s;if(B1=new G1(B1,A1),A1=B1.s,!P1||!A1)return new G1(NaN);if(P1!=A1)return B1.s=-A1,q1.minus(B1);var Q1=q1.e/Y,f1=B1.e/Y,l1=q1.c,n1=B1.c;if(!Q1||!f1){if(!l1||!n1)return new G1(P1/0);if(!l1[0]||!n1[0])return n1[0]?B1:new G1(l1[0]?q1:P1*0)}if(Q1=K(Q1),f1=K(f1),l1=l1.slice(),P1=Q1-f1){if(P1>0)f1=Q1,I1=n1;else P1=-P1,I1=l1;I1.reverse();for(;P1--;I1.push(0));I1.reverse()}if(P1=l1.length,A1=n1.length,P1-A1<0)I1=n1,n1=l1,l1=I1,A1=P1;for(P1=0;A1;)P1=(l1[--A1]=l1[A1]+n1[A1]+P1)/I|0,l1[A1]=I===l1[A1]?0:l1[A1]%I;if(P1)l1=[P1].concat(l1),++f1;return a1(B1,l1,f1)},k.precision=k.sd=function(B1,A1){var I1,q1,P1,Q1=this;if(B1!=null&&B1!==!!B1){if($(B1,1,V),A1==null)A1=n;else $(A1,0,8);return i1(new G1(Q1),B1,A1)}if(!(I1=Q1.c))return null;if(P1=I1.length-1,q1=P1*Y+1,P1=I1[P1]){for(;P1%10==0;P1/=10,q1--);for(P1=I1[0];P1>=10;P1/=10,q1++);}if(B1&&Q1.e+1>q1)q1=Q1.e+1;return q1},k.shiftedBy=function(B1){return $(B1,-W,W),this.times("1e"+B1)},k.squareRoot=k.sqrt=function(){var B1,A1,I1,q1,P1,Q1=this,f1=Q1.c,l1=Q1.s,n1=Q1.e,V0=h+4,I0=new G1("0.5");if(l1!==1||!f1||!f1[0])return new G1(!l1||l1<0&&(!f1||f1[0])?NaN:f1?Q1:1/0);if(l1=Math.sqrt(+E0(Q1)),l1==0||l1==1/0){if(A1=H(f1),(A1.length+n1)%2==0)A1+="0";if(l1=Math.sqrt(+A1),n1=K((n1+1)/2)-(n1<0||n1%2),l1==1/0)A1="5e"+n1;else A1=l1.toExponential(),A1=A1.slice(0,A1.indexOf("e")+1)+n1;I1=new G1(A1)}else I1=new G1(l1+"");if(I1.c[0]){if(n1=I1.e,l1=n1+V0,l1<3)l1=0;for(;;)if(P1=I1,I1=I0.times(P1.plus(T(Q1,P1,V0,1))),H(P1.c).slice(0,l1)===(A1=H(I1.c)).slice(0,l1)){if(I1.e<n1)--l1;if(A1=A1.slice(l1-3,l1+1),A1=="9999"||!q1&&A1=="4999"){if(!q1){if(i1(P1,P1.e+h+2,0),P1.times(P1).eq(Q1)){I1=P1;break}}V0+=4,l1+=4,q1=1}else{if(!+A1||!+A1.slice(1)&&A1.charAt(0)=="5")i1(I1,I1.e+h+2,1),B1=!I1.times(I1).eq(Q1);break}}}return i1(I1,I1.e+h+1,n,B1)},k.toExponential=function(B1,A1){if(B1!=null)$(B1,0,V),B1++;return L1(this,B1,A1,1)},k.toFixed=function(B1,A1){if(B1!=null)$(B1,0,V),B1=B1+this.e+1;return L1(this,B1,A1)},k.toFormat=function(B1,A1,I1){var q1,P1=this;if(I1==null)if(B1!=null&&A1&&typeof A1=="object")I1=A1,A1=null;else if(B1&&typeof B1=="object")I1=B1,B1=A1=null;else I1=F0;else if(typeof I1!="object")throw Error(G+"Argument not an object: "+I1);if(q1=P1.toFixed(B1,A1),P1.c){var Q1,f1=q1.split("."),l1=+I1.groupSize,n1=+I1.secondaryGroupSize,V0=I1.groupSeparator||"",I0=f1[0],M0=f1[1],YA=P1.s<0,m0=YA?I0.slice(1):I0,SA=m0.length;if(n1)Q1=l1,l1=n1,n1=Q1,SA-=Q1;if(l1>0&&SA>0){Q1=SA%l1||l1,I0=m0.substr(0,Q1);for(;Q1<SA;Q1+=l1)I0+=V0+m0.substr(Q1,l1);if(n1>0)I0+=V0+m0.slice(Q1);if(YA)I0="-"+I0}q1=M0?I0+(I1.decimalSeparator||"")+((n1=+I1.fractionGroupSize)?M0.replace(new RegExp("\\d{"+n1+"}\\B","g"),"$&"+(I1.fractionGroupSeparator||"")):M0):I0}return(I1.prefix||"")+q1+(I1.suffix||"")},k.toFraction=function(B1){var A1,I1,q1,P1,Q1,f1,l1,n1,V0,I0,M0,YA,m0=this,SA=m0.c;if(B1!=null){if(l1=new G1(B1),!l1.isInteger()&&(l1.c||l1.s!==1)||l1.lt(c))throw Error(G+"Argument "+(l1.isInteger()?"out of range: ":"not an integer: ")+E0(l1))}if(!SA)return new G1(m0);A1=new G1(c),V0=I1=new G1(c),q1=n1=new G1(c),YA=H(SA),Q1=A1.e=YA.length-m0.e-1,A1.c[0]=J[(f1=Q1%Y)<0?Y+f1:f1],B1=!B1||l1.comparedTo(A1)>0?Q1>0?A1:V0:l1,f1=W1,W1=1/0,l1=new G1(YA),n1.c[0]=0;for(;;){if(I0=T(l1,A1,0,1),P1=I1.plus(I0.times(q1)),P1.comparedTo(B1)==1)break;I1=q1,q1=P1,V0=n1.plus(I0.times(P1=V0)),n1=P1,A1=l1.minus(I0.times(P1=A1)),l1=P1}return P1=T(B1.minus(I1),q1,0,1),n1=n1.plus(P1.times(V0)),I1=I1.plus(P1.times(q1)),n1.s=V0.s=m0.s,Q1=Q1*2,M0=T(V0,q1,Q1,n).minus(m0).abs().comparedTo(T(n1,I1,Q1,n).minus(m0).abs())<1?[V0,q1]:[n1,I1],W1=f1,M0},k.toNumber=function(){return+E0(this)},k.toPrecision=function(B1,A1){if(B1!=null)$(B1,1,V);return L1(this,B1,A1,2)},k.toString=function(B1){var A1,I1=this,q1=I1.s,P1=I1.e;if(P1===null)if(q1){if(A1="Infinity",q1<0)A1="-"+A1}else A1="NaN";else{if(B1==null)A1=P1<=a||P1>=x?N(H(I1.c),P1):O(H(I1.c),P1,"0");else if(B1===10&&K1)I1=i1(new G1(I1),h+P1+1,n),A1=O(H(I1.c),I1.e,"0");else $(B1,2,g1.length,"Base"),A1=j(O(H(I1.c),P1,"0"),10,B1,q1,!0);if(q1<0&&I1.c[0])A1="-"+A1}return A1},k.valueOf=k.toJSON=function(){return E0(this)},k._isBigNumber=!0,R!=null)G1.set(R);return G1}function K(R){var T=R|0;return R>0||R===T?T:T-1}function H(R){var T,j,f=1,k=R.length,c=R[0]+"";for(;f<k;){T=R[f++]+"",j=Y-T.length;for(;j--;T="0"+T);c+=T}for(k=c.length;c.charCodeAt(--k)===48;);return c.slice(0,k+1||1)}function z(R,T){var j,f,k=R.c,c=T.c,h=R.s,n=T.s,a=R.e,x=T.e;if(!h||!n)return null;if(j=k&&!k[0],f=c&&!c[0],j||f)return j?f?0:-n:h;if(h!=n)return h;if(j=h<0,f=a==x,!k||!c)return f?0:!k^j?1:-1;if(!f)return a>x^j?1:-1;n=(a=k.length)<(x=c.length)?a:x;for(h=0;h<n;h++)if(k[h]!=c[h])return k[h]>c[h]^j?1:-1;return a==x?0:a>x^j?1:-1}function $(R,T,j,f){if(R<T||R>j||R!==Z(R))throw Error(G+(f||"Argument")+(typeof R=="number"?R<T||R>j?" out of range: ":" not an integer: ":" not a primitive number: ")+String(R))}function L(R){var T=R.c.length-1;return K(R.e/Y)==T&&R.c[T]%2!=0}function N(R,T){return(R.length>1?R.charAt(0)+"."+R.slice(1):R)+(T<0?"e":"e+")+T}function O(R,T,j){var f,k;if(T<0){for(k=j+".";++T;k+=j);R=k+R}else if(f=R.length,++T>f){for(k=j,T-=f;--T;k+=j);R+=k}else if(T<f)R=R.slice(0,T)+"."+R.slice(T);return R}if(B=C(),B.default=B.BigNumber=B,typeof define=="function"&&define.amd)define(function(){return B});else if(typeof X_1!="undefined"&&X_1.exports)X_1.exports=B;else{if(!A)A=typeof self!="undefined"&&self?self:window;A.BigNumber=B}})(WKB)});
var bHB=E((Qt5,vHB)=>{var tD1=J1("buffer").Buffer,wE0=J1("buffer").SlowBuffer;vHB.exports=M_1;function M_1(A,B){if(!tD1.isBuffer(A)||!tD1.isBuffer(B))return!1;if(A.length!==B.length)return!1;var Q=0;for(var D=0;D<A.length;D++)Q|=A[D]^B[D];return Q===0}M_1.install=function(){tD1.prototype.equal=wE0.prototype.equal=function A(B){return M_1(this,B)}};var Jt6=tD1.prototype.equal,Xt6=wE0.prototype.equal;M_1.restore=function(){tD1.prototype.equal=Jt6,wE0.prototype.equal=Xt6}});
var czB=E((mzB)=>{var mE0,dE0,cE0;Object.defineProperty(mzB,"__esModule",{value:!0});mzB.FileSubjectTokenSupplier=void 0;var lE0=J1("util"),pE0=J1("fs"),Me6=lE0.promisify((mE0=pE0.readFile)!==null&&mE0!==void 0?mE0:()=>{}),Re6=lE0.promisify((dE0=pE0.realpath)!==null&&dE0!==void 0?dE0:()=>{}),Oe6=lE0.promisify((cE0=pE0.lstat)!==null&&cE0!==void 0?cE0:()=>{});class uzB{constructor(A){this.filePath=A.filePath,this.formatType=A.formatType,this.subjectTokenFieldName=A.subjectTokenFieldName}async getSubjectToken(A){let B=this.filePath;try{if(B=await Re6(B),!(await Oe6(B)).isFile())throw new Error}catch(Z){if(Z instanceof Error)Z.message=`The file at ${B} does not exist, or it is not a file. ${Z.message}`;throw Z}let Q,D=await Me6(B,{encoding:"utf8"});if(this.formatType==="text")Q=D;else if(this.formatType==="json"&&this.subjectTokenFieldName)Q=JSON.parse(D)[this.subjectTokenFieldName];if(!Q)throw new Error("Unable to parse the subject_token from the credential_source file");return Q}}mzB.FileSubjectTokenSupplier=uzB});
var dKB=E((uKB)=>{Object.defineProperty(uKB,"__esModule",{value:!0});uKB.BrowserCrypto=void 0;var fe=ZE0(),Ko6=he();class E_1{constructor(){if(typeof window==="undefined"||window.crypto===void 0||window.crypto.subtle===void 0)throw new Error("SubtleCrypto not found. Make sure it's an https:// website.")}async sha256DigestBase64(A){let B=new TextEncoder().encode(A),Q=await window.crypto.subtle.digest("SHA-256",B);return fe.fromByteArray(new Uint8Array(Q))}randomBytesBase64(A){let B=new Uint8Array(A);return window.crypto.getRandomValues(B),fe.fromByteArray(B)}static padBase64(A){while(A.length%4!==0)A+="=";return A}async verify(A,B,Q){let D={name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}},Z=new TextEncoder().encode(B),G=fe.toByteArray(E_1.padBase64(Q)),F=await window.crypto.subtle.importKey("jwk",A,D,!0,["verify"]);return await window.crypto.subtle.verify(D,F,G,Z)}async sign(A,B){let Q={name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}},D=new TextEncoder().encode(B),Z=await window.crypto.subtle.importKey("jwk",A,Q,!0,["sign"]),G=await window.crypto.subtle.sign(Q,Z,D);return fe.fromByteArray(new Uint8Array(G))}decodeBase64StringUtf8(A){let B=fe.toByteArray(E_1.padBase64(A));return new TextDecoder().decode(B)}encodeBase64StringUtf8(A){let B=new TextEncoder().encode(A);return fe.fromByteArray(B)}async sha256DigestHex(A){let B=new TextEncoder().encode(A),Q=await window.crypto.subtle.digest("SHA-256",B);return Ko6.fromArrayBufferToHex(Q)}async signWithHmacSha256(A,B){let Q=typeof A==="string"?A:String.fromCharCode(...new Uint16Array(A)),D=new TextEncoder,Z=await window.crypto.subtle.importKey("raw",D.encode(Q),{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]);return window.crypto.subtle.sign("HMAC",Z,D.encode(B))}}uKB.BrowserCrypto=E_1});
var ey1=E((Ko5,gVB)=>{var{isASCIIHex:_VB}=$z0(),{utf8Encode:xVB}=ty1();function v6(A){return A.codePointAt(0)}function cn6(A){let B=A.toString(16).toUpperCase();if(B.length===1)B=`0${B}`;return`%${B}`}function vVB(A){let B=new Uint8Array(A.byteLength),Q=0;for(let D=0;D<A.byteLength;++D){let Z=A[D];if(Z!==37)B[Q++]=Z;else if(Z===37&&(!_VB(A[D+1])||!_VB(A[D+2])))B[Q++]=Z;else{let G=parseInt(String.fromCodePoint(A[D+1],A[D+2]),16);B[Q++]=G,D+=2}}return B.slice(0,Q)}function ln6(A){let B=xVB(A);return vVB(B)}function qz0(A){return A<=31||A>126}var pn6=new Set([v6(" "),v6('"'),v6("<"),v6(">"),v6("`")]);function in6(A){return qz0(A)||pn6.has(A)}var nn6=new Set([v6(" "),v6('"'),v6("#"),v6("<"),v6(">")]);function Nz0(A){return qz0(A)||nn6.has(A)}function an6(A){return Nz0(A)||A===v6("'")}var sn6=new Set([v6("?"),v6("`"),v6("{"),v6("}"),v6("^")]);function bVB(A){return Nz0(A)||sn6.has(A)}var rn6=new Set([v6("/"),v6(":"),v6(";"),v6("="),v6("@"),v6("["),v6("\\"),v6("]"),v6("|")]);function fVB(A){return bVB(A)||rn6.has(A)}var on6=new Set([v6("$"),v6("%"),v6("&"),v6("+"),v6(",")]);function tn6(A){return fVB(A)||on6.has(A)}var en6=new Set([v6("!"),v6("'"),v6("("),v6(")"),v6("~")]);function Aa6(A){return tn6(A)||en6.has(A)}function hVB(A,B){let Q=xVB(A),D="";for(let Z of Q)if(!B(Z))D+=String.fromCharCode(Z);else D+=cn6(Z);return D}function Ba6(A,B){return hVB(String.fromCodePoint(A),B)}function Qa6(A,B,Q=!1){let D="";for(let Z of A)if(Q&&Z===" ")D+="+";else D+=hVB(Z,B);return D}gVB.exports={isC0ControlPercentEncode:qz0,isFragmentPercentEncode:in6,isQueryPercentEncode:Nz0,isSpecialQueryPercentEncode:an6,isPathPercentEncode:bVB,isUserinfoPercentEncode:fVB,isURLEncodedPercentEncode:Aa6,percentDecodeString:ln6,percentDecodeBytes:vVB,utf8PercentEncodeString:Qa6,utf8PercentEncodeCodePoint:Ba6}});
var fCB=E((Lo5,bCB)=>{var RM=(A)=>A!==null&&typeof A==="object"&&typeof A.pipe==="function";RM.writable=(A)=>RM(A)&&A.writable!==!1&&typeof A._write==="function"&&typeof A._writableState==="object";RM.readable=(A)=>RM(A)&&A.readable!==!1&&typeof A._read==="function"&&typeof A._readableState==="object";RM.duplex=(A)=>RM.writable(A)&&RM.readable(A);RM.transform=(A)=>RM.duplex(A)&&typeof A._transform==="function";bCB.exports=RM});
var fE0=E((vzB)=>{Object.defineProperty(vzB,"__esModule",{value:!0});vzB.StsCredentials=void 0;var Xe6=X$(),Ve6=J1("querystring"),Ce6=sD1(),xzB=vE0();class bE0 extends xzB.OAuthClientAuthHandler{constructor(A,B){super(B);this.tokenExchangeEndpoint=A,this.transporter=new Ce6.DefaultTransporter}async exchangeToken(A,B,Q){var D,Z,G;let F={grant_type:A.grantType,resource:A.resource,audience:A.audience,scope:(D=A.scope)===null||D===void 0?void 0:D.join(" "),requested_token_type:A.requestedTokenType,subject_token:A.subjectToken,subject_token_type:A.subjectTokenType,actor_token:(Z=A.actingParty)===null||Z===void 0?void 0:Z.actorToken,actor_token_type:(G=A.actingParty)===null||G===void 0?void 0:G.actorTokenType,options:Q&&JSON.stringify(Q)};Object.keys(F).forEach((W)=>{if(typeof F[W]==="undefined")delete F[W]});let I={"Content-Type":"application/x-www-form-urlencoded"};Object.assign(I,B||{});let Y={...bE0.RETRY_CONFIG,url:this.tokenExchangeEndpoint.toString(),method:"POST",headers:I,data:Ve6.stringify(F),responseType:"json"};this.applyClientAuthenticationOptions(Y);try{let W=await this.transporter.request(Y),J=W.data;return J.res=W,J}catch(W){if(W instanceof Xe6.GaxiosError&&W.response)throw xzB.getErrorFromOAuthErrorResponse(W.response.data,W);throw W}}}vzB.StsCredentials=bE0});
var fEB=E((vEB)=>{Object.defineProperty(vEB,"__esModule",{value:!0});vEB.IAMAuth=void 0;class xEB{constructor(A,B){this.selector=A,this.token=B,this.selector=A,this.token=B}getRequestHeaders(){return{"x-goog-iam-authority-selector":this.selector,"x-goog-iam-authorization-token":this.token}}}vEB.IAMAuth=xEB});
var fqA=E((vqA)=>{Object.defineProperty(vqA,"__esModule",{value:!0});vqA.default=void 0;var _qA=xqA(yqA()),Z$Q=xqA(Ae1()),G$Q=aQ1();function xqA(A){return A&&A.__esModule?A:{default:A}}function F$Q(A,B,Q){if(_qA.default.randomUUID&&!B&&!A)return _qA.default.randomUUID();A=A||{};let D=A.random||(A.rng||Z$Q.default)();if(D[6]=D[6]&15|64,D[8]=D[8]&63|128,B){Q=Q||0;for(let Z=0;Z<16;++Z)B[Q+Z]=D[Z];return B}return G$Q.unsafeStringify(D)}var I$Q=F$Q;vqA.default=I$Q});
var hCB=E((Mo5,ts6)=>{ts6.exports={name:"gaxios",version:"6.7.1",description:"A simple common HTTP client specifically for Google APIs and services.",main:"build/src/index.js",types:"build/src/index.d.ts",files:["build/src"],scripts:{lint:"gts check",test:"c8 mocha build/test","presystem-test":"npm run compile","system-test":"mocha build/system-test --timeout 80000",compile:"tsc -p .",fix:"gts fix",prepare:"npm run compile",pretest:"npm run compile",webpack:"webpack","prebrowser-test":"npm run compile","browser-test":"node build/browser-test/browser-test-runner.js",docs:"compodoc src/","docs-test":"linkinator docs","predocs-test":"npm run docs","samples-test":"cd samples/ && npm link ../ && npm test && cd ../",prelint:"cd samples; npm link ../; npm install",clean:"gts clean",precompile:"gts clean"},repository:"googleapis/gaxios",keywords:["google"],engines:{node:">=14"},author:"Google, LLC",license:"Apache-2.0",devDependencies:{"@babel/plugin-proposal-private-methods":"^7.18.6","@compodoc/compodoc":"1.1.19","@types/cors":"^2.8.6","@types/express":"^4.16.1","@types/extend":"^3.0.1","@types/mocha":"^9.0.0","@types/multiparty":"0.0.36","@types/mv":"^2.1.0","@types/ncp":"^2.0.1","@types/node":"^20.0.0","@types/node-fetch":"^2.5.7","@types/sinon":"^17.0.0","@types/tmp":"0.2.6","@types/uuid":"^10.0.0","abort-controller":"^3.0.0",assert:"^2.0.0",browserify:"^17.0.0",c8:"^8.0.0",cheerio:"1.0.0-rc.10",cors:"^2.8.5",execa:"^5.0.0",express:"^4.16.4","form-data":"^4.0.0",gts:"^5.0.0","is-docker":"^2.0.0",karma:"^6.0.0","karma-chrome-launcher":"^3.0.0","karma-coverage":"^2.0.0","karma-firefox-launcher":"^2.0.0","karma-mocha":"^2.0.0","karma-remap-coverage":"^0.1.5","karma-sourcemap-loader":"^0.4.0","karma-webpack":"5.0.0",linkinator:"^3.0.0",mocha:"^8.0.0",multiparty:"^4.2.1",mv:"^2.1.1",ncp:"^2.0.0",nock:"^13.0.0","null-loader":"^4.0.0",puppeteer:"^19.0.0",sinon:"^18.0.0","stream-browserify":"^3.0.0",tmp:"0.2.3","ts-loader":"^8.0.0",typescript:"^5.1.6",webpack:"^5.35.0","webpack-cli":"^4.0.0"},dependencies:{extend:"^3.0.2","https-proxy-agent":"^7.0.1","is-stream":"^2.0.0","node-fetch":"^2.6.9",uuid:"^9.0.1"}}});
var he=E((aKB)=>{Object.defineProperty(aKB,"__esModule",{value:!0});aKB.createCrypto=wo6;aKB.hasBrowserCrypto=nKB;aKB.fromArrayBufferToHex=$o6;var Eo6=dKB(),Uo6=iKB();function wo6(){if(nKB())return new Eo6.BrowserCrypto;return new Uo6.NodeCrypto}function nKB(){return typeof window!=="undefined"&&typeof window.crypto!=="undefined"&&typeof window.crypto.subtle!=="undefined"}function $o6(A){return Array.from(new Uint8Array(A)).map((Q)=>{return Q.toString(16).padStart(2,"0")}).join("")}});
var iKB=E((lKB)=>{Object.defineProperty(lKB,"__esModule",{value:!0});lKB.NodeCrypto=void 0;var ge=J1("crypto");class cKB{async sha256DigestBase64(A){return ge.createHash("sha256").update(A).digest("base64")}randomBytesBase64(A){return ge.randomBytes(A).toString("base64")}async verify(A,B,Q){let D=ge.createVerify("RSA-SHA256");return D.update(B),D.end(),D.verify(A,Q,"base64")}async sign(A,B){let Q=ge.createSign("RSA-SHA256");return Q.update(B),Q.end(),Q.sign(A,"base64")}decodeBase64StringUtf8(A){return Buffer.from(A,"base64").toString("utf-8")}encodeBase64StringUtf8(A){return Buffer.from(A,"utf-8").toString("base64")}async sha256DigestHex(A){return ge.createHash("sha256").update(A).digest("hex")}async signWithHmacSha256(A,B){let Q=typeof A==="string"?A:zo6(A);return Ho6(ge.createHmac("sha256",Q).update(B).digest())}}lKB.NodeCrypto=cKB;function Ho6(A){return A.buffer.slice(A.byteOffset,A.byteOffset+A.byteLength)}function zo6(A){return Buffer.from(A)}});
var ix=E(($I)=>{var hE0=$I&&$I.__classPrivateFieldGet||function(A,B,Q,D){if(Q==="a"&&!D)throw new TypeError("Private accessor was defined without a getter");if(typeof B==="function"?A!==B||!D:!B.has(A))throw new TypeError("Cannot read private member from an object whose class did not declare it");return Q==="m"?D:Q==="a"?D.call(A):D?D.value:B.get(A)},fzB=$I&&$I.__classPrivateFieldSet||function(A,B,Q,D,Z){if(D==="m")throw new TypeError("Private method is not writable");if(D==="a"&&!Z)throw new TypeError("Private accessor was defined without a setter");if(typeof B==="function"?A!==B||!Z:!B.has(A))throw new TypeError("Cannot write private member to an object whose class did not declare it");return D==="a"?Z.call(A,Q):Z?Z.value=Q:B.set(A,Q),Q},gE0,ne,gzB;Object.defineProperty($I,"__esModule",{value:!0});$I.BaseExternalAccountClient=$I.DEFAULT_UNIVERSE=$I.CLOUD_RESOURCE_MANAGER=$I.EXTERNAL_ACCOUNT_TYPE=$I.EXPIRATION_TIME_OFFSET=void 0;var Ke6=J1("stream"),He6=PM(),ze6=fE0(),hzB=lx(),Ee6="urn:ietf:params:oauth:grant-type:token-exchange",Ue6="urn:ietf:params:oauth:token-type:access_token",uE0="https://www.googleapis.com/auth/cloud-platform",we6=3600;$I.EXPIRATION_TIME_OFFSET=300000;$I.EXTERNAL_ACCOUNT_TYPE="external_account";$I.CLOUD_RESOURCE_MANAGER="https://cloudresourcemanager.googleapis.com/v1/projects/";var $e6="//iam\\.googleapis\\.com/locations/[^/]+/workforcePools/[^/]+/providers/.+",qe6="https://sts.{universeDomain}/v1/token",Ne6=GE0(),Le6=PM();Object.defineProperty($I,"DEFAULT_UNIVERSE",{enumerable:!0,get:function(){return Le6.DEFAULT_UNIVERSE}});class S_1 extends He6.AuthClient{constructor(A,B){var Q;super({...A,...B});gE0.add(this),ne.set(this,null);let D=hzB.originalOrCamelOptions(A),Z=D.get("type");if(Z&&Z!==$I.EXTERNAL_ACCOUNT_TYPE)throw new Error(`Expected "${$I.EXTERNAL_ACCOUNT_TYPE}" type but received "${A.type}"`);let G=D.get("client_id"),F=D.get("client_secret"),I=(Q=D.get("token_url"))!==null&&Q!==void 0?Q:qe6.replace("{universeDomain}",this.universeDomain),Y=D.get("subject_token_type"),W=D.get("workforce_pool_user_project"),J=D.get("service_account_impersonation_url"),X=D.get("service_account_impersonation"),V=hzB.originalOrCamelOptions(X).get("token_lifetime_seconds");if(this.cloudResourceManagerURL=new URL(D.get("cloud_resource_manager_url")||`https://cloudresourcemanager.${this.universeDomain}/v1/projects/`),G)this.clientAuth={confidentialClientType:"basic",clientId:G,clientSecret:F};this.stsCredential=new ze6.StsCredentials(I,this.clientAuth),this.scopes=D.get("scopes")||[uE0],this.cachedAccessToken=null,this.audience=D.get("audience"),this.subjectTokenType=Y,this.workforcePoolUserProject=W;let C=new RegExp($e6);if(this.workforcePoolUserProject&&!this.audience.match(C))throw new Error("workforcePoolUserProject should not be set for non-workforce pool credentials.");if(this.serviceAccountImpersonationUrl=J,this.serviceAccountImpersonationLifetime=V,this.serviceAccountImpersonationLifetime)this.configLifetimeRequested=!0;else this.configLifetimeRequested=!1,this.serviceAccountImpersonationLifetime=we6;this.projectNumber=this.getProjectNumber(this.audience),this.supplierContext={audience:this.audience,subjectTokenType:this.subjectTokenType,transporter:this.transporter}}getServiceAccountEmail(){var A;if(this.serviceAccountImpersonationUrl){if(this.serviceAccountImpersonationUrl.length>256)throw new RangeError(`URL is too long: ${this.serviceAccountImpersonationUrl}`);let Q=/serviceAccounts\/(?<email>[^:]+):generateAccessToken$/.exec(this.serviceAccountImpersonationUrl);return((A=Q===null||Q===void 0?void 0:Q.groups)===null||A===void 0?void 0:A.email)||null}return null}setCredentials(A){super.setCredentials(A),this.cachedAccessToken=A}async getAccessToken(){if(!this.cachedAccessToken||this.isExpired(this.cachedAccessToken))await this.refreshAccessTokenAsync();return{token:this.cachedAccessToken.access_token,res:this.cachedAccessToken.res}}async getRequestHeaders(){let B={Authorization:`Bearer ${(await this.getAccessToken()).token}`};return this.addSharedMetadataHeaders(B)}request(A,B){if(B)this.requestAsync(A).then((Q)=>B(null,Q),(Q)=>{return B(Q,Q.response)});else return this.requestAsync(A)}async getProjectId(){let A=this.projectNumber||this.workforcePoolUserProject;if(this.projectId)return this.projectId;else if(A){let B=await this.getRequestHeaders(),Q=await this.transporter.request({...S_1.RETRY_CONFIG,headers:B,url:`${this.cloudResourceManagerURL.toString()}${A}`,responseType:"json"});return this.projectId=Q.data.projectId,this.projectId}return null}async requestAsync(A,B=!1){let Q;try{let D=await this.getRequestHeaders();if(A.headers=A.headers||{},D&&D["x-goog-user-project"])A.headers["x-goog-user-project"]=D["x-goog-user-project"];if(D&&D.Authorization)A.headers.Authorization=D.Authorization;Q=await this.transporter.request(A)}catch(D){let Z=D.response;if(Z){let G=Z.status,F=Z.config.data instanceof Ke6.Readable;if(!B&&(G===401||G===403)&&!F&&this.forceRefreshOnFailure)return await this.refreshAccessTokenAsync(),await this.requestAsync(A,!0)}throw D}return Q}async refreshAccessTokenAsync(){fzB(this,ne,hE0(this,ne,"f")||hE0(this,gE0,"m",gzB).call(this),"f");try{return await hE0(this,ne,"f")}finally{fzB(this,ne,null,"f")}}getProjectNumber(A){let B=A.match(/\/projects\/([^/]+)/);if(!B)return null;return B[1]}async getImpersonatedAccessToken(A){let B={...S_1.RETRY_CONFIG,url:this.serviceAccountImpersonationUrl,method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${A}`},data:{scope:this.getScopesArray(),lifetime:this.serviceAccountImpersonationLifetime+"s"},responseType:"json"},Q=await this.transporter.request(B),D=Q.data;return{access_token:D.accessToken,expiry_date:new Date(D.expireTime).getTime(),res:Q}}isExpired(A){let B=new Date().getTime();return A.expiry_date?B>=A.expiry_date-this.eagerRefreshThresholdMillis:!1}getScopesArray(){if(typeof this.scopes==="string")return[this.scopes];return this.scopes||[uE0]}getMetricsHeaderValue(){let A=process.version.replace(/^v/,""),B=this.serviceAccountImpersonationUrl!==void 0,Q=this.credentialSourceType?this.credentialSourceType:"unknown";return`gl-node/${A} auth/${Ne6.version} google-byoid-sdk source/${Q} sa-impersonation/${B} config-lifetime/${this.configLifetimeRequested}`}}$I.BaseExternalAccountClient=S_1;ne=new WeakMap,gE0=new WeakSet,gzB=async function A(){let B=await this.retrieveSubjectToken(),Q={grantType:Ee6,audience:this.audience,requestedTokenType:Ue6,subjectToken:B,subjectTokenType:this.subjectTokenType,scope:this.serviceAccountImpersonationUrl?[uE0]:this.getScopesArray()},D=!this.clientAuth&&this.workforcePoolUserProject?{userProject:this.workforcePoolUserProject}:void 0,Z={"x-goog-api-client":this.getMetricsHeaderValue()},G=await this.stsCredential.exchangeToken(Q,Z,D);if(this.serviceAccountImpersonationUrl)this.cachedAccessToken=await this.getImpersonatedAccessToken(G.access_token);else if(G.expires_in)this.cachedAccessToken={access_token:G.access_token,expiry_date:new Date().getTime()+G.expires_in*1000,res:G.res};else this.cachedAccessToken={access_token:G.access_token,res:G.res};return this.credentials={},Object.assign(this.credentials,this.cachedAccessToken),delete this.credentials.res,this.emit("tokens",{refresh_token:null,expiry_date:this.cachedAccessToken.expiry_date,access_token:this.cachedAccessToken.access_token,token_type:"Bearer",id_token:null}),this.cachedAccessToken}});
var iz0=E((sCB)=>{Object.defineProperty(sCB,"__esModule",{value:!0});sCB.GaxiosInterceptorManager=void 0;class aCB extends Set{}sCB.GaxiosInterceptorManager=aCB});
var jE0=E(($zB)=>{Object.defineProperty($zB,"__esModule",{value:!0});$zB.JWTAccess=void 0;var ot6=RE0(),tt6=lx(),wzB={alg:"RS256",typ:"JWT"};class SE0{constructor(A,B,Q,D){this.cache=new tt6.LRUCache({capacity:500,maxAge:3600000}),this.email=A,this.key=B,this.keyId=Q,this.eagerRefreshThresholdMillis=D!==null&&D!==void 0?D:300000}getCachedKey(A,B){let Q=A;if(B&&Array.isArray(B)&&B.length)Q=A?`${A}_${B.join("_")}`:`${B.join("_")}`;else if(typeof B==="string")Q=A?`${A}_${B}`:B;if(!Q)throw Error("Scopes or url must be provided");return Q}getRequestHeaders(A,B,Q){let D=this.getCachedKey(A,Q),Z=this.cache.get(D),G=Date.now();if(Z&&Z.expiration-G>this.eagerRefreshThresholdMillis)return Z.headers;let F=Math.floor(Date.now()/1000),I=SE0.getExpirationTime(F),Y;if(Array.isArray(Q))Q=Q.join(" ");if(Q)Y={iss:this.email,sub:this.email,scope:Q,exp:I,iat:F};else Y={iss:this.email,sub:this.email,aud:A,exp:I,iat:F};if(B){for(let C in Y)if(B[C])throw new Error(`The '${C}' property is not allowed when passing additionalClaims. This claim is included in the JWT by default.`)}let W=this.keyId?{...wzB,kid:this.keyId}:wzB,J=Object.assign(Y,B),V={Authorization:`Bearer ${ot6.sign({header:W,payload:J,secret:this.key})}`};return this.cache.set(D,{expiration:I*1000,headers:V}),V}static getExpirationTime(A){return A+3600}fromJSON(A){if(!A)throw new Error("Must pass in a JSON object containing the service account auth settings.");if(!A.client_email)throw new Error("The incoming JSON object does not contain a client_email field");if(!A.private_key)throw new Error("The incoming JSON object does not contain a private_key field");this.email=A.client_email,this.key=A.private_key,this.keyId=A.private_key_id,this.projectId=A.project_id}fromStream(A,B){if(B)this.fromStreamAsync(A).then(()=>B(),B);else return this.fromStreamAsync(A)}fromStreamAsync(A){return new Promise((B,Q)=>{if(!A)Q(new Error("Must pass in a stream containing the service account auth settings."));let D="";A.setEncoding("utf8").on("data",(Z)=>D+=Z).on("error",Q).on("end",()=>{try{let Z=JSON.parse(D);this.fromJSON(Z),B()}catch(Z){Q(Z)}})})}}$zB.JWTAccess=SE0});
var lEB=E((dEB)=>{Object.defineProperty(dEB,"__esModule",{value:!0});dEB.PassThroughClient=void 0;var M18=PM();class zU0 extends M18.AuthClient{async request(A){return this.transporter.request(A)}async getAccessToken(){return{}}async getRequestHeaders(){return{}}}dEB.PassThroughClient=zU0;var R18=new zU0;R18.getAccessToken()});
var lqA=E((dqA)=>{Object.defineProperty(dqA,"__esModule",{value:!0});dqA.default=void 0;var V$Q=mqA(Ge1()),C$Q=mqA(uqA());function mqA(A){return A&&A.__esModule?A:{default:A}}var K$Q=V$Q.default("v5",80,C$Q.default),H$Q=K$Q;dqA.default=H$Q});
var lx=E((cx)=>{var C$=cx&&cx.__classPrivateFieldGet||function(A,B,Q,D){if(Q==="a"&&!D)throw new TypeError("Private accessor was defined without a getter");if(typeof B==="function"?A!==B||!D:!B.has(A))throw new TypeError("Cannot read private member from an object whose class did not declare it");return Q==="m"?D:Q==="a"?D.call(A):D?D.value:B.get(A)},me,mP,WE0,JE0;Object.defineProperty(cx,"__esModule",{value:!0});cx.LRUCache=void 0;cx.snakeToCamel=JHB;cx.originalOrCamelOptions=ho6;function JHB(A){return A.replace(/([_][^_])/g,(B)=>B.slice(1).toUpperCase())}function ho6(A){function B(Q){var D;let Z=A||{};return(D=Z[Q])!==null&&D!==void 0?D:Z[JHB(Q)]}return{get:B}}class XHB{constructor(A){me.add(this),mP.set(this,new Map),this.capacity=A.capacity,this.maxAge=A.maxAge}set(A,B){C$(this,me,"m",WE0).call(this,A,B),C$(this,me,"m",JE0).call(this)}get(A){let B=C$(this,mP,"f").get(A);if(!B)return;return C$(this,me,"m",WE0).call(this,A,B.value),C$(this,me,"m",JE0).call(this),B.value}}cx.LRUCache=XHB;mP=new WeakMap,me=new WeakSet,WE0=function A(B,Q){C$(this,mP,"f").delete(B),C$(this,mP,"f").set(B,{value:Q,lastAccessed:Date.now()})},JE0=function A(){let B=this.maxAge?Date.now()-this.maxAge:0,Q=C$(this,mP,"f").entries().next();while(!Q.done&&(C$(this,mP,"f").size>this.capacity||Q.value[1].lastAccessed<B))C$(this,mP,"f").delete(Q.value[0]),Q=C$(this,mP,"f").entries().next()}});
var mCB=E((gCB)=>{Object.defineProperty(gCB,"__esModule",{value:!0});gCB.pkg=void 0;gCB.pkg=hCB()});
var mEB=E((gEB)=>{Object.defineProperty(gEB,"__esModule",{value:!0});gEB.DownscopedClient=gEB.EXPIRATION_TIME_OFFSET=gEB.MAX_ACCESS_BOUNDARY_RULES_COUNT=void 0;var E18=J1("stream"),U18=PM(),w18=fE0(),$18="urn:ietf:params:oauth:grant-type:token-exchange",q18="urn:ietf:params:oauth:token-type:access_token",N18="urn:ietf:params:oauth:token-type:access_token";gEB.MAX_ACCESS_BOUNDARY_RULES_COUNT=10;gEB.EXPIRATION_TIME_OFFSET=300000;class hEB extends U18.AuthClient{constructor(A,B,Q,D){super({...Q,quotaProjectId:D});if(this.authClient=A,this.credentialAccessBoundary=B,B.accessBoundary.accessBoundaryRules.length===0)throw new Error("At least one access boundary rule needs to be defined.");else if(B.accessBoundary.accessBoundaryRules.length>gEB.MAX_ACCESS_BOUNDARY_RULES_COUNT)throw new Error(`The provided access boundary has more than ${gEB.MAX_ACCESS_BOUNDARY_RULES_COUNT} access boundary rules.`);for(let Z of B.accessBoundary.accessBoundaryRules)if(Z.availablePermissions.length===0)throw new Error("At least one permission should be defined in access boundary rules.");this.stsCredential=new w18.StsCredentials(`https://sts.${this.universeDomain}/v1/token`),this.cachedDownscopedAccessToken=null}setCredentials(A){if(!A.expiry_date)throw new Error("The access token expiry_date field is missing in the provided credentials.");super.setCredentials(A),this.cachedDownscopedAccessToken=A}async getAccessToken(){if(!this.cachedDownscopedAccessToken||this.isExpired(this.cachedDownscopedAccessToken))await this.refreshAccessTokenAsync();return{token:this.cachedDownscopedAccessToken.access_token,expirationTime:this.cachedDownscopedAccessToken.expiry_date,res:this.cachedDownscopedAccessToken.res}}async getRequestHeaders(){let B={Authorization:`Bearer ${(await this.getAccessToken()).token}`};return this.addSharedMetadataHeaders(B)}request(A,B){if(B)this.requestAsync(A).then((Q)=>B(null,Q),(Q)=>{return B(Q,Q.response)});else return this.requestAsync(A)}async requestAsync(A,B=!1){let Q;try{let D=await this.getRequestHeaders();if(A.headers=A.headers||{},D&&D["x-goog-user-project"])A.headers["x-goog-user-project"]=D["x-goog-user-project"];if(D&&D.Authorization)A.headers.Authorization=D.Authorization;Q=await this.transporter.request(A)}catch(D){let Z=D.response;if(Z){let G=Z.status,F=Z.config.data instanceof E18.Readable;if(!B&&(G===401||G===403)&&!F&&this.forceRefreshOnFailure)return await this.refreshAccessTokenAsync(),await this.requestAsync(A,!0)}throw D}return Q}async refreshAccessTokenAsync(){var A;let B=(await this.authClient.getAccessToken()).token,Q={grantType:$18,requestedTokenType:q18,subjectToken:B,subjectTokenType:N18},D=await this.stsCredential.exchangeToken(Q,void 0,this.credentialAccessBoundary),Z=((A=this.authClient.credentials)===null||A===void 0?void 0:A.expiry_date)||null,G=D.expires_in?new Date().getTime()+D.expires_in*1000:Z;return this.cachedDownscopedAccessToken={access_token:D.access_token,expiry_date:G,res:D.res},this.credentials={},Object.assign(this.credentials,this.cachedDownscopedAccessToken),delete this.credentials.res,this.emit("tokens",{refresh_token:null,expiry_date:this.cachedDownscopedAccessToken.expiry_date,access_token:this.cachedDownscopedAccessToken.access_token,token_type:"Bearer",id_token:null}),this.cachedDownscopedAccessToken}isExpired(A){let B=new Date().getTime();return A.expiry_date?B>=A.expiry_date-this.eagerRefreshThresholdMillis:!1}}gEB.DownscopedClient=hEB});
var mc1=E((yh8,rl0)=>{function GM9(A){Q.debug=Q,Q.default=Q,Q.coerce=Y,Q.disable=F,Q.enable=Z,Q.enabled=I,Q.humanize=sl0(),Q.destroy=W,Object.keys(A).forEach((J)=>{Q[J]=A[J]}),Q.names=[],Q.skips=[],Q.formatters={};function B(J){let X=0;for(let V=0;V<J.length;V++)X=(X<<5)-X+J.charCodeAt(V),X|=0;return Q.colors[Math.abs(X)%Q.colors.length]}Q.selectColor=B;function Q(J){let X,V=null,C,K;function H(...z){if(!H.enabled)return;let $=H,L=Number(new Date),N=L-(X||L);if($.diff=N,$.prev=X,$.curr=L,X=L,z[0]=Q.coerce(z[0]),typeof z[0]!=="string")z.unshift("%O");let O=0;z[0]=z[0].replace(/%([a-zA-Z%])/g,(T,j)=>{if(T==="%%")return"%";O++;let f=Q.formatters[j];if(typeof f==="function"){let k=z[O];T=f.call($,k),z.splice(O,1),O--}return T}),Q.formatArgs.call($,z),($.log||Q.log).apply($,z)}if(H.namespace=J,H.useColors=Q.useColors(),H.color=Q.selectColor(J),H.extend=D,H.destroy=Q.destroy,Object.defineProperty(H,"enabled",{enumerable:!0,configurable:!1,get:()=>{if(V!==null)return V;if(C!==Q.namespaces)C=Q.namespaces,K=Q.enabled(J);return K},set:(z)=>{V=z}}),typeof Q.init==="function")Q.init(H);return H}function D(J,X){let V=Q(this.namespace+(typeof X==="undefined"?":":X)+J);return V.log=this.log,V}function Z(J){Q.save(J),Q.namespaces=J,Q.names=[],Q.skips=[];let X=(typeof J==="string"?J:"").trim().replace(" ",",").split(",").filter(Boolean);for(let V of X)if(V[0]==="-")Q.skips.push(V.slice(1));else Q.names.push(V)}function G(J,X){let V=0,C=0,K=-1,H=0;while(V<J.length)if(C<X.length&&(X[C]===J[V]||X[C]==="*"))if(X[C]==="*")K=C,H=V,C++;else V++,C++;else if(K!==-1)C=K+1,H++,V=H;else return!1;while(C<X.length&&X[C]==="*")C++;return C===X.length}function F(){let J=[...Q.names,...Q.skips.map((X)=>"-"+X)].join(",");return Q.enable(""),J}function I(J){for(let X of Q.skips)if(G(J,X))return!1;for(let X of Q.names)if(G(J,X))return!0;return!1}function Y(J){if(J instanceof Error)return J.stack||J.message;return J}function W(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return Q.enable(Q.load()),Q}rl0.exports=GM9});
var nCB=E((iCB)=>{Object.defineProperty(iCB,"__esModule",{value:!0});iCB.getRetryConfig=Qr6;async function Qr6(A){let B=pCB(A);if(!A||!A.config||!B&&!A.config.retry)return{shouldRetry:!1};B=B||{},B.currentRetryAttempt=B.currentRetryAttempt||0,B.retry=B.retry===void 0||B.retry===null?3:B.retry,B.httpMethodsToRetry=B.httpMethodsToRetry||["GET","HEAD","PUT","OPTIONS","DELETE"],B.noResponseRetries=B.noResponseRetries===void 0||B.noResponseRetries===null?2:B.noResponseRetries,B.retryDelayMultiplier=B.retryDelayMultiplier?B.retryDelayMultiplier:2,B.timeOfFirstRequest=B.timeOfFirstRequest?B.timeOfFirstRequest:Date.now(),B.totalTimeout=B.totalTimeout?B.totalTimeout:Number.MAX_SAFE_INTEGER,B.maxRetryDelay=B.maxRetryDelay?B.maxRetryDelay:Number.MAX_SAFE_INTEGER;let Q=[[100,199],[408,408],[429,429],[500,599]];if(B.statusCodesToRetry=B.statusCodesToRetry||Q,A.config.retryConfig=B,!await(B.shouldRetry||Dr6)(A))return{shouldRetry:!1,config:A.config};let Z=Zr6(B);A.config.retryConfig.currentRetryAttempt+=1;let G=B.retryBackoff?B.retryBackoff(A,Z):new Promise((F)=>{setTimeout(F,Z)});if(B.onRetryAttempt)B.onRetryAttempt(A);return await G,{shouldRetry:!0,config:A.config}}function Dr6(A){var B;let Q=pCB(A);if(A.name==="AbortError"||((B=A.error)===null||B===void 0?void 0:B.name)==="AbortError")return!1;if(!Q||Q.retry===0)return!1;if(!A.response&&(Q.currentRetryAttempt||0)>=Q.noResponseRetries)return!1;if(!A.config.method||Q.httpMethodsToRetry.indexOf(A.config.method.toUpperCase())<0)return!1;if(A.response&&A.response.status){let D=!1;for(let[Z,G]of Q.statusCodesToRetry){let F=A.response.status;if(F>=Z&&F<=G){D=!0;break}}if(!D)return!1}if(Q.currentRetryAttempt=Q.currentRetryAttempt||0,Q.currentRetryAttempt>=Q.retry)return!1;return!0}function pCB(A){if(A&&A.config&&A.config.retryConfig)return A.config.retryConfig;return}function Zr6(A){var B;let D=(A.currentRetryAttempt?0:(B=A.retryDelay)!==null&&B!==void 0?B:100)+(Math.pow(A.retryDelayMultiplier,A.currentRetryAttempt)-1)/2*1000,Z=A.totalTimeout-(Date.now()-A.timeOfFirstRequest);return Math.min(D,Z,A.maxRetryDelay)}});
var nD1=E((VQ)=>{var cr6=VQ&&VQ.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),lr6=VQ&&VQ.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))cr6(B,A,Q)};Object.defineProperty(VQ,"__esModule",{value:!0});VQ.gcpResidencyCache=VQ.METADATA_SERVER_DETECTION=VQ.HEADERS=VQ.HEADER_VALUE=VQ.HEADER_NAME=VQ.SECONDARY_HOST_ADDRESS=VQ.HOST_ADDRESS=VQ.BASE_PATH=void 0;VQ.instance=rr6;VQ.project=or6;VQ.universe=tr6;VQ.bulk=er6;VQ.isAvailable=Bo6;VQ.resetIsAvailableCache=Qo6;VQ.getGCPResidency=BE0;VQ.setGCPResidency=fKB;VQ.requestTimeout=hKB;var ez0=X$(),pr6=UKB(),ir6=sz0(),nr6=vKB();VQ.BASE_PATH="/computeMetadata/v1";VQ.HOST_ADDRESS="http://***************";VQ.SECONDARY_HOST_ADDRESS="http://metadata.google.internal.";VQ.HEADER_NAME="Metadata-Flavor";VQ.HEADER_VALUE="Google";VQ.HEADERS=Object.freeze({[VQ.HEADER_NAME]:VQ.HEADER_VALUE});var bKB=nr6.log("gcp metadata");VQ.METADATA_SERVER_DETECTION=Object.freeze({"assume-present":"don't try to ping the metadata server, but assume it's present",none:"don't try to ping the metadata server, but don't try to use it either","bios-only":"treat the result of a BIOS probe as canonical (don't fall back to pinging)","ping-only":"skip the BIOS probe, and go straight to pinging"});function AE0(A){if(!A)A=process.env.GCE_METADATA_IP||process.env.GCE_METADATA_HOST||VQ.HOST_ADDRESS;if(!/^https?:\/\//.test(A))A=`http://${A}`;return new URL(VQ.BASE_PATH,A).href}function ar6(A){Object.keys(A).forEach((B)=>{switch(B){case"params":case"property":case"headers":break;case"qs":throw new Error("'qs' is not a valid configuration option. Please use 'params' instead.");default:throw new Error(`'${B}' is not a valid configuration option.`)}})}async function iD1(A,B={},Q=3,D=!1){let Z="",G={},F={};if(typeof A==="object"){let J=A;Z=J.metadataKey,G=J.params||G,F=J.headers||F,Q=J.noResponseRetries||Q,D=J.fastFail||D}else Z=A;if(typeof B==="string")Z+=`/${B}`;else{if(ar6(B),B.property)Z+=`/${B.property}`;F=B.headers||F,G=B.params||G}let I=D?sr6:ez0.request,Y={url:`${AE0()}/${Z}`,headers:{...VQ.HEADERS,...F},retryConfig:{noResponseRetries:Q},params:G,responseType:"text",timeout:hKB()};bKB.info("instance request %j",Y);let W=await I(Y);if(bKB.info("instance metadata is %s",W.data),W.headers[VQ.HEADER_NAME.toLowerCase()]!==VQ.HEADER_VALUE)throw new Error(`Invalid response from metadata service: incorrect ${VQ.HEADER_NAME} header. Expected '${VQ.HEADER_VALUE}', got ${W.headers[VQ.HEADER_NAME.toLowerCase()]?`'${W.headers[VQ.HEADER_NAME.toLowerCase()]}'`:"no header"}`);if(typeof W.data==="string")try{return pr6.parse(W.data)}catch(J){}return W.data}async function sr6(A){var B;let Q={...A,url:(B=A.url)===null||B===void 0?void 0:B.toString().replace(AE0(),AE0(VQ.SECONDARY_HOST_ADDRESS))},D=!1,Z=ez0.request(A).then((F)=>{return D=!0,F}).catch((F)=>{if(D)return G;else throw D=!0,F}),G=ez0.request(Q).then((F)=>{return D=!0,F}).catch((F)=>{if(D)return Z;else throw D=!0,F});return Promise.race([Z,G])}function rr6(A){return iD1("instance",A)}function or6(A){return iD1("project",A)}function tr6(A){return iD1("universe",A)}async function er6(A){let B={};return await Promise.all(A.map((Q)=>{return(async()=>{let D=await iD1(Q),Z=Q.metadataKey;B[Z]=D})()})),B}function Ao6(){return process.env.DETECT_GCP_RETRIES?Number(process.env.DETECT_GCP_RETRIES):0}var z_1;async function Bo6(){if(process.env.METADATA_SERVER_DETECTION){let A=process.env.METADATA_SERVER_DETECTION.trim().toLocaleLowerCase();if(!(A in VQ.METADATA_SERVER_DETECTION))throw new RangeError(`Unknown \`METADATA_SERVER_DETECTION\` env variable. Got \`${A}\`, but it should be \`${Object.keys(VQ.METADATA_SERVER_DETECTION).join("`, `")}\`, or unset`);switch(A){case"assume-present":return!0;case"none":return!1;case"bios-only":return BE0();case"ping-only":}}try{if(z_1===void 0)z_1=iD1("instance",void 0,Ao6(),!(process.env.GCE_METADATA_IP||process.env.GCE_METADATA_HOST));return await z_1,!0}catch(A){let B=A;if(process.env.DEBUG_AUTH)console.info(B);if(B.type==="request-timeout")return!1;if(B.response&&B.response.status===404)return!1;else{if(!(B.response&&B.response.status===404)&&(!B.code||!["EHOSTDOWN","EHOSTUNREACH","ENETUNREACH","ENOENT","ENOTFOUND","ECONNREFUSED"].includes(B.code))){let Q="UNKNOWN";if(B.code)Q=B.code;process.emitWarning(`received unexpected error = ${B.message} code = ${Q}`,"MetadataLookupWarning")}return!1}}}function Qo6(){z_1=void 0}VQ.gcpResidencyCache=null;function BE0(){if(VQ.gcpResidencyCache===null)fKB();return VQ.gcpResidencyCache}function fKB(A=null){VQ.gcpResidencyCache=A!==null?A:ir6.detectGCPResidency()}function hKB(){return BE0()?0:3000}lr6(sz0(),VQ)});
var nQ1=E((YqA)=>{Object.defineProperty(YqA,"__esModule",{value:!0});YqA.default=void 0;var NwQ=LwQ(IqA());function LwQ(A){return A&&A.__esModule?A:{default:A}}function MwQ(A){return typeof A==="string"&&NwQ.default.test(A)}var RwQ=MwQ;YqA.default=RwQ});
var nqA=E((pqA)=>{Object.defineProperty(pqA,"__esModule",{value:!0});pqA.default=void 0;var z$Q="********-0000-0000-0000-********0000";pqA.default=z$Q});
var nzB=E((pzB)=>{Object.defineProperty(pzB,"__esModule",{value:!0});pzB.UrlSubjectTokenSupplier=void 0;class lzB{constructor(A){this.url=A.url,this.formatType=A.formatType,this.subjectTokenFieldName=A.subjectTokenFieldName,this.headers=A.headers,this.additionalGaxiosOptions=A.additionalGaxiosOptions}async getSubjectToken(A){let B={...this.additionalGaxiosOptions,url:this.url,method:"GET",headers:this.headers,responseType:this.formatType},Q;if(this.formatType==="text")Q=(await A.transporter.request(B)).data;else if(this.formatType==="json"&&this.subjectTokenFieldName)Q=(await A.transporter.request(B)).data[this.subjectTokenFieldName];if(!Q)throw new Error("Unable to parse the subject_token from the credential_source URL");return Q}}pzB.UrlSubjectTokenSupplier=lzB});
var oy1=E((JVB,XVB)=>{function xi6(A){return typeof A==="object"&&A!==null||typeof A==="function"}var ZVB=Function.prototype.call.bind(Object.prototype.hasOwnProperty);function vi6(A,B){for(let Q of Reflect.ownKeys(B)){let D=Reflect.getOwnPropertyDescriptor(B,Q);if(D&&!Reflect.defineProperty(A,Q,D))throw new TypeError(`Cannot redefine property: ${String(Q)}`)}}function bi6(A,B){let Q=IVB(A);return Object.defineProperties(Object.create(Q["%Object.prototype%"]),Object.getOwnPropertyDescriptors(B))}var GVB=Symbol("wrapper"),FVB=Symbol("impl"),je=Symbol("SameObject caches"),ry1=Symbol.for("[webidl2js] constructor registry"),fi6=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype);function IVB(A){if(ZVB(A,ry1))return A[ry1];let B=Object.create(null);B["%Object.prototype%"]=A.Object.prototype,B["%IteratorPrototype%"]=Object.getPrototypeOf(Object.getPrototypeOf(new A.Array()[Symbol.iterator]()));try{B["%AsyncIteratorPrototype%"]=Object.getPrototypeOf(Object.getPrototypeOf(A.eval("(async function* () {})").prototype))}catch{B["%AsyncIteratorPrototype%"]=fi6}return A[ry1]=B,B}function hi6(A,B,Q){if(!A[je])A[je]=Object.create(null);if(B in A[je])return A[je][B];return A[je][B]=Q(),A[je][B]}function YVB(A){return A?A[GVB]:null}function WVB(A){return A?A[FVB]:null}function gi6(A){let B=YVB(A);return B?B:A}function ui6(A){let B=WVB(A);return B?B:A}var mi6=Symbol("internal");function di6(A){if(typeof A!=="string")return!1;let B=A>>>0;if(B===4294967295)return!1;let Q=`${B}`;if(A!==Q)return!1;return!0}var ci6=Object.getOwnPropertyDescriptor(ArrayBuffer.prototype,"byteLength").get;function li6(A){try{return ci6.call(A),!0}catch(B){return!1}}function pi6([A,B],Q){let D;switch(Q){case"key":D=A;break;case"value":D=B;break;case"key+value":D=[A,B];break}return{value:D,done:!1}}var ii6=Symbol("supports property index"),ni6=Symbol("supported property indices"),ai6=Symbol("supports property name"),si6=Symbol("supported property names"),ri6=Symbol("indexed property get"),oi6=Symbol("indexed property set new"),ti6=Symbol("indexed property set existing"),ei6=Symbol("named property get"),An6=Symbol("named property set new"),Bn6=Symbol("named property set existing"),Qn6=Symbol("named property delete"),Dn6=Symbol("async iterator get the next iteration result"),Zn6=Symbol("async iterator return steps"),Gn6=Symbol("async iterator initialization steps"),Fn6=Symbol("async iterator end of iteration");XVB.exports=JVB={isObject:xi6,hasOwn:ZVB,define:vi6,newObjectInRealm:bi6,wrapperSymbol:GVB,implSymbol:FVB,getSameObject:hi6,ctorRegistrySymbol:ry1,initCtorRegistry:IVB,wrapperForImpl:YVB,implForWrapper:WVB,tryWrapperForImpl:gi6,tryImplForWrapper:ui6,iterInternalSymbol:mi6,isArrayBuffer:li6,isArrayIndexPropName:di6,supportsPropertyIndex:ii6,supportedPropertyIndices:ni6,supportsPropertyName:ai6,supportedPropertyNames:si6,indexedGet:ri6,indexedSetNew:oi6,indexedSetExisting:ti6,namedGet:ei6,namedSetNew:An6,namedSetExisting:Bn6,namedDelete:Qn6,asyncIteratorNext:Dn6,asyncIteratorReturn:Zn6,asyncIteratorInit:Gn6,asyncIteratorEOI:Fn6,iteratorResult:pi6}});
var pz0=E((vE)=>{var es6=vE&&vE.__importDefault||function(A){return A&&A.__esModule?A:{default:A}},dCB;Object.defineProperty(vE,"__esModule",{value:!0});vE.GaxiosError=vE.GAXIOS_ERROR_SYMBOL=void 0;vE.defaultErrorRedactor=lCB;var Ar6=J1("url"),cz0=mCB(),cCB=es6(Cz0());vE.GAXIOS_ERROR_SYMBOL=Symbol.for(`${cz0.pkg.name}-gaxios-error`);class lz0 extends Error{static[(dCB=vE.GAXIOS_ERROR_SYMBOL,Symbol.hasInstance)](A){if(A&&typeof A==="object"&&vE.GAXIOS_ERROR_SYMBOL in A&&A[vE.GAXIOS_ERROR_SYMBOL]===cz0.pkg.version)return!0;return Function.prototype[Symbol.hasInstance].call(lz0,A)}constructor(A,B,Q,D){var Z;super(A);if(this.config=B,this.response=Q,this.error=D,this[dCB]=cz0.pkg.version,this.config=cCB.default(!0,{},B),this.response)this.response.config=cCB.default(!0,{},this.response.config);if(this.response){try{this.response.data=Br6(this.config.responseType,(Z=this.response)===null||Z===void 0?void 0:Z.data)}catch(G){}this.status=this.response.status}if(D&&"code"in D&&D.code)this.code=D.code;if(B.errorRedactor)B.errorRedactor({config:this.config,response:this.response})}}vE.GaxiosError=lz0;function Br6(A,B){switch(A){case"stream":return B;case"json":return JSON.parse(JSON.stringify(B));case"arraybuffer":return JSON.parse(Buffer.from(B).toString("utf8"));case"blob":return JSON.parse(B.text());default:return B}}function lCB(A){function Q(G){if(!G)return;for(let F of Object.keys(G)){if(/^authentication$/i.test(F))G[F]="<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.";if(/^authorization$/i.test(F))G[F]="<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.";if(/secret/i.test(F))G[F]="<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>."}}function D(G,F){if(typeof G==="object"&&G!==null&&typeof G[F]==="string"){let I=G[F];if(/grant_type=/i.test(I)||/assertion=/i.test(I)||/secret/i.test(I))G[F]="<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>."}}function Z(G){if(typeof G==="object"&&G!==null){if("grant_type"in G)G.grant_type="<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.";if("assertion"in G)G.assertion="<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.";if("client_secret"in G)G.client_secret="<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>."}}if(A.config){Q(A.config.headers),D(A.config,"data"),Z(A.config.data),D(A.config,"body"),Z(A.config.body);try{let G=new Ar6.URL("",A.config.url);if(G.searchParams.has("token"))G.searchParams.set("token","<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.");if(G.searchParams.has("client_secret"))G.searchParams.set("client_secret","<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.");A.config.url=G.toString()}catch(G){}}if(A.response)lCB({config:A.response.config}),Q(A.response.headers),D(A.response,"data"),Z(A.response.data);return A}});
var qCB=E((Hs6)=>{var Cs6=$CB(),Ks6=yz0();Hs6.URL=Cs6;Hs6.URLSearchParams=Ks6});
var qVB=E((Wo5,$VB)=>{var En6=/[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0897-\u089F\u08CA-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C00-\u0C04\u0C3C\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0CF3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D81-\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1715\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u180F\u1885\u1886\u18A9\u1920-\u192B\u1930-\u193B\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F\u1AB0-\u1ACE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA82C\uA880\uA881\uA8B4-\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\u{101FD}\u{102E0}\u{10376}-\u{1037A}\u{10A01}-\u{10A03}\u{10A05}\u{10A06}\u{10A0C}-\u{10A0F}\u{10A38}-\u{10A3A}\u{10A3F}\u{10AE5}\u{10AE6}\u{10D24}-\u{10D27}\u{10D69}-\u{10D6D}\u{10EAB}\u{10EAC}\u{10EFC}-\u{10EFF}\u{10F46}-\u{10F50}\u{10F82}-\u{10F85}\u{11000}-\u{11002}\u{11038}-\u{11046}\u{11070}\u{11073}\u{11074}\u{1107F}-\u{11082}\u{110B0}-\u{110BA}\u{110C2}\u{11100}-\u{11102}\u{11127}-\u{11134}\u{11145}\u{11146}\u{11173}\u{11180}-\u{11182}\u{111B3}-\u{111C0}\u{111C9}-\u{111CC}\u{111CE}\u{111CF}\u{1122C}-\u{11237}\u{1123E}\u{11241}\u{112DF}-\u{112EA}\u{11300}-\u{11303}\u{1133B}\u{1133C}\u{1133E}-\u{11344}\u{11347}\u{11348}\u{1134B}-\u{1134D}\u{11357}\u{11362}\u{11363}\u{11366}-\u{1136C}\u{11370}-\u{11374}\u{113B8}-\u{113C0}\u{113C2}\u{113C5}\u{113C7}-\u{113CA}\u{113CC}-\u{113D0}\u{113D2}\u{113E1}\u{113E2}\u{11435}-\u{11446}\u{1145E}\u{114B0}-\u{114C3}\u{115AF}-\u{115B5}\u{115B8}-\u{115C0}\u{115DC}\u{115DD}\u{11630}-\u{11640}\u{116AB}-\u{116B7}\u{1171D}-\u{1172B}\u{1182C}-\u{1183A}\u{11930}-\u{11935}\u{11937}\u{11938}\u{1193B}-\u{1193E}\u{11940}\u{11942}\u{11943}\u{119D1}-\u{119D7}\u{119DA}-\u{119E0}\u{119E4}\u{11A01}-\u{11A0A}\u{11A33}-\u{11A39}\u{11A3B}-\u{11A3E}\u{11A47}\u{11A51}-\u{11A5B}\u{11A8A}-\u{11A99}\u{11C2F}-\u{11C36}\u{11C38}-\u{11C3F}\u{11C92}-\u{11CA7}\u{11CA9}-\u{11CB6}\u{11D31}-\u{11D36}\u{11D3A}\u{11D3C}\u{11D3D}\u{11D3F}-\u{11D45}\u{11D47}\u{11D8A}-\u{11D8E}\u{11D90}\u{11D91}\u{11D93}-\u{11D97}\u{11EF3}-\u{11EF6}\u{11F00}\u{11F01}\u{11F03}\u{11F34}-\u{11F3A}\u{11F3E}-\u{11F42}\u{11F5A}\u{13440}\u{13447}-\u{13455}\u{1611E}-\u{1612F}\u{16AF0}-\u{16AF4}\u{16B30}-\u{16B36}\u{16F4F}\u{16F51}-\u{16F87}\u{16F8F}-\u{16F92}\u{16FE4}\u{16FF0}\u{16FF1}\u{1BC9D}\u{1BC9E}\u{1CF00}-\u{1CF2D}\u{1CF30}-\u{1CF46}\u{1D165}-\u{1D169}\u{1D16D}-\u{1D172}\u{1D17B}-\u{1D182}\u{1D185}-\u{1D18B}\u{1D1AA}-\u{1D1AD}\u{1D242}-\u{1D244}\u{1DA00}-\u{1DA36}\u{1DA3B}-\u{1DA6C}\u{1DA75}\u{1DA84}\u{1DA9B}-\u{1DA9F}\u{1DAA1}-\u{1DAAF}\u{1E000}-\u{1E006}\u{1E008}-\u{1E018}\u{1E01B}-\u{1E021}\u{1E023}\u{1E024}\u{1E026}-\u{1E02A}\u{1E08F}\u{1E130}-\u{1E136}\u{1E2AE}\u{1E2EC}-\u{1E2EF}\u{1E4EC}-\u{1E4EF}\u{1E5EE}\u{1E5EF}\u{1E8D0}-\u{1E8D6}\u{1E944}-\u{1E94A}\u{E0100}-\u{E01EF}]/u,Un6=/[\u094D\u09CD\u0A4D\u0ACD\u0B4D\u0BCD\u0C4D\u0CCD\u0D3B\u0D3C\u0D4D\u0DCA\u0E3A\u0EBA\u0F84\u1039\u103A\u1714\u1715\u1734\u17D2\u1A60\u1B44\u1BAA\u1BAB\u1BF2\u1BF3\u2D7F\uA806\uA82C\uA8C4\uA953\uA9C0\uAAF6\uABED\u{10A3F}\u{11046}\u{11070}\u{1107F}\u{110B9}\u{11133}\u{11134}\u{111C0}\u{11235}\u{112EA}\u{1134D}\u{113CE}-\u{113D0}\u{11442}\u{114C2}\u{115BF}\u{1163F}\u{116B6}\u{1172B}\u{11839}\u{1193D}\u{1193E}\u{119E0}\u{11A34}\u{11A47}\u{11A99}\u{11C3F}\u{11D44}\u{11D45}\u{11D97}\u{11F41}\u{11F42}\u{1612F}]/u,wn6=/[\u0620\u0626\u0628\u062A-\u062E\u0633-\u063F\u0641-\u0647\u0649\u064A\u066E\u066F\u0678-\u0687\u069A-\u06BF\u06C1\u06C2\u06CC\u06CE\u06D0\u06D1\u06FA-\u06FC\u06FF\u0712-\u0714\u071A-\u071D\u071F-\u0727\u0729\u072B\u072D\u072E\u074E-\u0758\u075C-\u076A\u076D-\u0770\u0772\u0775-\u0777\u077A-\u077F\u07CA-\u07EA\u0841-\u0845\u0848\u084A-\u0853\u0855\u0860\u0862-\u0865\u0868\u0886\u0889-\u088D\u08A0-\u08A9\u08AF\u08B0\u08B3-\u08B8\u08BA-\u08C8\u1807\u1820-\u1878\u1887-\u18A8\u18AA\uA840-\uA872\u{10AC0}-\u{10AC4}\u{10ACD}\u{10AD3}-\u{10ADC}\u{10ADE}-\u{10AE0}\u{10AEB}-\u{10AEE}\u{10B80}\u{10B82}\u{10B86}-\u{10B88}\u{10B8A}\u{10B8B}\u{10B8D}\u{10B90}\u{10BAD}\u{10BAE}\u{10D00}-\u{10D21}\u{10D23}\u{10EC3}\u{10EC4}\u{10F30}-\u{10F32}\u{10F34}-\u{10F44}\u{10F51}-\u{10F53}\u{10F70}-\u{10F73}\u{10F76}-\u{10F81}\u{10FB0}\u{10FB2}\u{10FB3}\u{10FB8}\u{10FBB}\u{10FBC}\u{10FBE}\u{10FBF}\u{10FC1}\u{10FC4}\u{10FCA}\u{10FCB}\u{1E900}-\u{1E943}][\xAD\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u061C\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u070F\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0897-\u089F\u08CA-\u08E1\u08E3-\u0902\u093A\u093C\u0941-\u0948\u094D\u0951-\u0957\u0962\u0963\u0981\u09BC\u09C1-\u09C4\u09CD\u09E2\u09E3\u09FE\u0A01\u0A02\u0A3C\u0A41\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81\u0A82\u0ABC\u0AC1-\u0AC5\u0AC7\u0AC8\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01\u0B3C\u0B3F\u0B41-\u0B44\u0B4D\u0B55\u0B56\u0B62\u0B63\u0B82\u0BC0\u0BCD\u0C00\u0C04\u0C3C\u0C3E-\u0C40\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81\u0CBC\u0CBF\u0CC6\u0CCC\u0CCD\u0CE2\u0CE3\u0D00\u0D01\u0D3B\u0D3C\u0D41-\u0D44\u0D4D\u0D62\u0D63\u0D81\u0DCA\u0DD2-\u0DD4\u0DD6\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0F18\u0F19\u0F35\u0F37\u0F39\u0F71-\u0F7E\u0F80-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102D-\u1030\u1032-\u1037\u1039\u103A\u103D\u103E\u1058\u1059\u105E-\u1060\u1071-\u1074\u1082\u1085\u1086\u108D\u109D\u135D-\u135F\u1712-\u1714\u1732\u1733\u1752\u1753\u1772\u1773\u17B4\u17B5\u17B7-\u17BD\u17C6\u17C9-\u17D3\u17DD\u180B-\u180D\u180F\u1885\u1886\u18A9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193B\u1A17\u1A18\u1A1B\u1A56\u1A58-\u1A5E\u1A60\u1A62\u1A65-\u1A6C\u1A73-\u1A7C\u1A7F\u1AB0-\u1ACE\u1B00-\u1B03\u1B34\u1B36-\u1B3A\u1B3C\u1B42\u1B6B-\u1B73\u1B80\u1B81\u1BA2-\u1BA5\u1BA8\u1BA9\u1BAB-\u1BAD\u1BE6\u1BE8\u1BE9\u1BED\u1BEF-\u1BF1\u1C2C-\u1C33\u1C36\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE0\u1CE2-\u1CE8\u1CED\u1CF4\u1CF8\u1CF9\u1DC0-\u1DFF\u200B\u200E\u200F\u202A-\u202E\u2060-\u2064\u206A-\u206F\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302D\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA825\uA826\uA82C\uA8C4\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA951\uA980-\uA982\uA9B3\uA9B6-\uA9B9\uA9BC\uA9BD\uA9E5\uAA29-\uAA2E\uAA31\uAA32\uAA35\uAA36\uAA43\uAA4C\uAA7C\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEC\uAAED\uAAF6\uABE5\uABE8\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFEFF\uFFF9-\uFFFB\u{101FD}\u{102E0}\u{10376}-\u{1037A}\u{10A01}-\u{10A03}\u{10A05}\u{10A06}\u{10A0C}-\u{10A0F}\u{10A38}-\u{10A3A}\u{10A3F}\u{10AE5}\u{10AE6}\u{10D24}-\u{10D27}\u{10D69}-\u{10D6D}\u{10EAB}\u{10EAC}\u{10EFC}-\u{10EFF}\u{10F46}-\u{10F50}\u{10F82}-\u{10F85}\u{11001}\u{11038}-\u{11046}\u{11070}\u{11073}\u{11074}\u{1107F}-\u{11081}\u{110B3}-\u{110B6}\u{110B9}\u{110BA}\u{110C2}\u{11100}-\u{11102}\u{11127}-\u{1112B}\u{1112D}-\u{11134}\u{11173}\u{11180}\u{11181}\u{111B6}-\u{111BE}\u{111C9}-\u{111CC}\u{111CF}\u{1122F}-\u{11231}\u{11234}\u{11236}\u{11237}\u{1123E}\u{11241}\u{112DF}\u{112E3}-\u{112EA}\u{11300}\u{11301}\u{1133B}\u{1133C}\u{11340}\u{11366}-\u{1136C}\u{11370}-\u{11374}\u{113BB}-\u{113C0}\u{113CE}\u{113D0}\u{113D2}\u{113E1}\u{113E2}\u{11438}-\u{1143F}\u{11442}-\u{11444}\u{11446}\u{1145E}\u{114B3}-\u{114B8}\u{114BA}\u{114BF}\u{114C0}\u{114C2}\u{114C3}\u{115B2}-\u{115B5}\u{115BC}\u{115BD}\u{115BF}\u{115C0}\u{115DC}\u{115DD}\u{11633}-\u{1163A}\u{1163D}\u{1163F}\u{11640}\u{116AB}\u{116AD}\u{116B0}-\u{116B5}\u{116B7}\u{1171D}\u{1171F}\u{11722}-\u{11725}\u{11727}-\u{1172B}\u{1182F}-\u{11837}\u{11839}\u{1183A}\u{1193B}\u{1193C}\u{1193E}\u{11943}\u{119D4}-\u{119D7}\u{119DA}\u{119DB}\u{119E0}\u{11A01}-\u{11A0A}\u{11A33}-\u{11A38}\u{11A3B}-\u{11A3E}\u{11A47}\u{11A51}-\u{11A56}\u{11A59}-\u{11A5B}\u{11A8A}-\u{11A96}\u{11A98}\u{11A99}\u{11C30}-\u{11C36}\u{11C38}-\u{11C3D}\u{11C3F}\u{11C92}-\u{11CA7}\u{11CAA}-\u{11CB0}\u{11CB2}\u{11CB3}\u{11CB5}\u{11CB6}\u{11D31}-\u{11D36}\u{11D3A}\u{11D3C}\u{11D3D}\u{11D3F}-\u{11D45}\u{11D47}\u{11D90}\u{11D91}\u{11D95}\u{11D97}\u{11EF3}\u{11EF4}\u{11F00}\u{11F01}\u{11F36}-\u{11F3A}\u{11F40}\u{11F42}\u{11F5A}\u{13430}-\u{13440}\u{13447}-\u{13455}\u{1611E}-\u{16129}\u{1612D}-\u{1612F}\u{16AF0}-\u{16AF4}\u{16B30}-\u{16B36}\u{16F4F}\u{16F8F}-\u{16F92}\u{16FE4}\u{1BC9D}\u{1BC9E}\u{1BCA0}-\u{1BCA3}\u{1CF00}-\u{1CF2D}\u{1CF30}-\u{1CF46}\u{1D167}-\u{1D169}\u{1D173}-\u{1D182}\u{1D185}-\u{1D18B}\u{1D1AA}-\u{1D1AD}\u{1D242}-\u{1D244}\u{1DA00}-\u{1DA36}\u{1DA3B}-\u{1DA6C}\u{1DA75}\u{1DA84}\u{1DA9B}-\u{1DA9F}\u{1DAA1}-\u{1DAAF}\u{1E000}-\u{1E006}\u{1E008}-\u{1E018}\u{1E01B}-\u{1E021}\u{1E023}\u{1E024}\u{1E026}-\u{1E02A}\u{1E08F}\u{1E130}-\u{1E136}\u{1E2AE}\u{1E2EC}-\u{1E2EF}\u{1E4EC}-\u{1E4EF}\u{1E5EE}\u{1E5EF}\u{1E8D0}-\u{1E8D6}\u{1E944}-\u{1E94B}\u{E0001}\u{E0020}-\u{E007F}\u{E0100}-\u{E01EF}]*\u200C[\xAD\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u061C\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u070F\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0897-\u089F\u08CA-\u08E1\u08E3-\u0902\u093A\u093C\u0941-\u0948\u094D\u0951-\u0957\u0962\u0963\u0981\u09BC\u09C1-\u09C4\u09CD\u09E2\u09E3\u09FE\u0A01\u0A02\u0A3C\u0A41\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81\u0A82\u0ABC\u0AC1-\u0AC5\u0AC7\u0AC8\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01\u0B3C\u0B3F\u0B41-\u0B44\u0B4D\u0B55\u0B56\u0B62\u0B63\u0B82\u0BC0\u0BCD\u0C00\u0C04\u0C3C\u0C3E-\u0C40\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81\u0CBC\u0CBF\u0CC6\u0CCC\u0CCD\u0CE2\u0CE3\u0D00\u0D01\u0D3B\u0D3C\u0D41-\u0D44\u0D4D\u0D62\u0D63\u0D81\u0DCA\u0DD2-\u0DD4\u0DD6\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0F18\u0F19\u0F35\u0F37\u0F39\u0F71-\u0F7E\u0F80-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102D-\u1030\u1032-\u1037\u1039\u103A\u103D\u103E\u1058\u1059\u105E-\u1060\u1071-\u1074\u1082\u1085\u1086\u108D\u109D\u135D-\u135F\u1712-\u1714\u1732\u1733\u1752\u1753\u1772\u1773\u17B4\u17B5\u17B7-\u17BD\u17C6\u17C9-\u17D3\u17DD\u180B-\u180D\u180F\u1885\u1886\u18A9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193B\u1A17\u1A18\u1A1B\u1A56\u1A58-\u1A5E\u1A60\u1A62\u1A65-\u1A6C\u1A73-\u1A7C\u1A7F\u1AB0-\u1ACE\u1B00-\u1B03\u1B34\u1B36-\u1B3A\u1B3C\u1B42\u1B6B-\u1B73\u1B80\u1B81\u1BA2-\u1BA5\u1BA8\u1BA9\u1BAB-\u1BAD\u1BE6\u1BE8\u1BE9\u1BED\u1BEF-\u1BF1\u1C2C-\u1C33\u1C36\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE0\u1CE2-\u1CE8\u1CED\u1CF4\u1CF8\u1CF9\u1DC0-\u1DFF\u200B\u200E\u200F\u202A-\u202E\u2060-\u2064\u206A-\u206F\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302D\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA825\uA826\uA82C\uA8C4\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA951\uA980-\uA982\uA9B3\uA9B6-\uA9B9\uA9BC\uA9BD\uA9E5\uAA29-\uAA2E\uAA31\uAA32\uAA35\uAA36\uAA43\uAA4C\uAA7C\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEC\uAAED\uAAF6\uABE5\uABE8\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFEFF\uFFF9-\uFFFB\u{101FD}\u{102E0}\u{10376}-\u{1037A}\u{10A01}-\u{10A03}\u{10A05}\u{10A06}\u{10A0C}-\u{10A0F}\u{10A38}-\u{10A3A}\u{10A3F}\u{10AE5}\u{10AE6}\u{10D24}-\u{10D27}\u{10D69}-\u{10D6D}\u{10EAB}\u{10EAC}\u{10EFC}-\u{10EFF}\u{10F46}-\u{10F50}\u{10F82}-\u{10F85}\u{11001}\u{11038}-\u{11046}\u{11070}\u{11073}\u{11074}\u{1107F}-\u{11081}\u{110B3}-\u{110B6}\u{110B9}\u{110BA}\u{110C2}\u{11100}-\u{11102}\u{11127}-\u{1112B}\u{1112D}-\u{11134}\u{11173}\u{11180}\u{11181}\u{111B6}-\u{111BE}\u{111C9}-\u{111CC}\u{111CF}\u{1122F}-\u{11231}\u{11234}\u{11236}\u{11237}\u{1123E}\u{11241}\u{112DF}\u{112E3}-\u{112EA}\u{11300}\u{11301}\u{1133B}\u{1133C}\u{11340}\u{11366}-\u{1136C}\u{11370}-\u{11374}\u{113BB}-\u{113C0}\u{113CE}\u{113D0}\u{113D2}\u{113E1}\u{113E2}\u{11438}-\u{1143F}\u{11442}-\u{11444}\u{11446}\u{1145E}\u{114B3}-\u{114B8}\u{114BA}\u{114BF}\u{114C0}\u{114C2}\u{114C3}\u{115B2}-\u{115B5}\u{115BC}\u{115BD}\u{115BF}\u{115C0}\u{115DC}\u{115DD}\u{11633}-\u{1163A}\u{1163D}\u{1163F}\u{11640}\u{116AB}\u{116AD}\u{116B0}-\u{116B5}\u{116B7}\u{1171D}\u{1171F}\u{11722}-\u{11725}\u{11727}-\u{1172B}\u{1182F}-\u{11837}\u{11839}\u{1183A}\u{1193B}\u{1193C}\u{1193E}\u{11943}\u{119D4}-\u{119D7}\u{119DA}\u{119DB}\u{119E0}\u{11A01}-\u{11A0A}\u{11A33}-\u{11A38}\u{11A3B}-\u{11A3E}\u{11A47}\u{11A51}-\u{11A56}\u{11A59}-\u{11A5B}\u{11A8A}-\u{11A96}\u{11A98}\u{11A99}\u{11C30}-\u{11C36}\u{11C38}-\u{11C3D}\u{11C3F}\u{11C92}-\u{11CA7}\u{11CAA}-\u{11CB0}\u{11CB2}\u{11CB3}\u{11CB5}\u{11CB6}\u{11D31}-\u{11D36}\u{11D3A}\u{11D3C}\u{11D3D}\u{11D3F}-\u{11D45}\u{11D47}\u{11D90}\u{11D91}\u{11D95}\u{11D97}\u{11EF3}\u{11EF4}\u{11F00}\u{11F01}\u{11F36}-\u{11F3A}\u{11F40}\u{11F42}\u{11F5A}\u{13430}-\u{13440}\u{13447}-\u{13455}\u{1611E}-\u{16129}\u{1612D}-\u{1612F}\u{16AF0}-\u{16AF4}\u{16B30}-\u{16B36}\u{16F4F}\u{16F8F}-\u{16F92}\u{16FE4}\u{1BC9D}\u{1BC9E}\u{1BCA0}-\u{1BCA3}\u{1CF00}-\u{1CF2D}\u{1CF30}-\u{1CF46}\u{1D167}-\u{1D169}\u{1D173}-\u{1D182}\u{1D185}-\u{1D18B}\u{1D1AA}-\u{1D1AD}\u{1D242}-\u{1D244}\u{1DA00}-\u{1DA36}\u{1DA3B}-\u{1DA6C}\u{1DA75}\u{1DA84}\u{1DA9B}-\u{1DA9F}\u{1DAA1}-\u{1DAAF}\u{1E000}-\u{1E006}\u{1E008}-\u{1E018}\u{1E01B}-\u{1E021}\u{1E023}\u{1E024}\u{1E026}-\u{1E02A}\u{1E08F}\u{1E130}-\u{1E136}\u{1E2AE}\u{1E2EC}-\u{1E2EF}\u{1E4EC}-\u{1E4EF}\u{1E5EE}\u{1E5EF}\u{1E8D0}-\u{1E8D6}\u{1E944}-\u{1E94B}\u{E0001}\u{E0020}-\u{E007F}\u{E0100}-\u{E01EF}]*[\u0620\u0622-\u063F\u0641-\u064A\u066E\u066F\u0671-\u0673\u0675-\u06D3\u06D5\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u077F\u07CA-\u07EA\u0840-\u0858\u0860\u0862-\u0865\u0867-\u086A\u0870-\u0882\u0886\u0889-\u088E\u08A0-\u08AC\u08AE-\u08C8\u1807\u1820-\u1878\u1887-\u18A8\u18AA\uA840-\uA871\u{10AC0}-\u{10AC5}\u{10AC7}\u{10AC9}\u{10ACA}\u{10ACE}-\u{10AD6}\u{10AD8}-\u{10AE1}\u{10AE4}\u{10AEB}-\u{10AEF}\u{10B80}-\u{10B91}\u{10BA9}-\u{10BAE}\u{10D01}-\u{10D23}\u{10EC2}-\u{10EC4}\u{10F30}-\u{10F44}\u{10F51}-\u{10F54}\u{10F70}-\u{10F81}\u{10FB0}\u{10FB2}-\u{10FB6}\u{10FB8}-\u{10FBF}\u{10FC1}-\u{10FC4}\u{10FC9}\u{10FCA}\u{1E900}-\u{1E943}]/u,$n6=/[\u05BE\u05C0\u05C3\u05C6\u05D0-\u05EA\u05EF-\u05F4\u0600-\u0605\u0608\u060B\u060D\u061B-\u064A\u0660-\u0669\u066B-\u066F\u0671-\u06D5\u06DD\u06E5\u06E6\u06EE\u06EF\u06FA-\u070D\u070F\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07C0-\u07EA\u07F4\u07F5\u07FA\u07FE-\u0815\u081A\u0824\u0828\u0830-\u083E\u0840-\u0858\u085E\u0860-\u086A\u0870-\u088E\u0890\u0891\u08A0-\u08C9\u08E2\u200F\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBC2\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFC\uFE70-\uFE74\uFE76-\uFEFC\u{10800}-\u{10805}\u{10808}\u{1080A}-\u{10835}\u{10837}\u{10838}\u{1083C}\u{1083F}-\u{10855}\u{10857}-\u{1089E}\u{108A7}-\u{108AF}\u{108E0}-\u{108F2}\u{108F4}\u{108F5}\u{108FB}-\u{1091B}\u{10920}-\u{10939}\u{1093F}\u{10980}-\u{109B7}\u{109BC}-\u{109CF}\u{109D2}-\u{10A00}\u{10A10}-\u{10A13}\u{10A15}-\u{10A17}\u{10A19}-\u{10A35}\u{10A40}-\u{10A48}\u{10A50}-\u{10A58}\u{10A60}-\u{10A9F}\u{10AC0}-\u{10AE4}\u{10AEB}-\u{10AF6}\u{10B00}-\u{10B35}\u{10B40}-\u{10B55}\u{10B58}-\u{10B72}\u{10B78}-\u{10B91}\u{10B99}-\u{10B9C}\u{10BA9}-\u{10BAF}\u{10C00}-\u{10C48}\u{10C80}-\u{10CB2}\u{10CC0}-\u{10CF2}\u{10CFA}-\u{10D23}\u{10D30}-\u{10D39}\u{10D40}-\u{10D65}\u{10D6F}-\u{10D85}\u{10D8E}\u{10D8F}\u{10E60}-\u{10E7E}\u{10E80}-\u{10EA9}\u{10EAD}\u{10EB0}\u{10EB1}\u{10EC2}-\u{10EC4}\u{10F00}-\u{10F27}\u{10F30}-\u{10F45}\u{10F51}-\u{10F59}\u{10F70}-\u{10F81}\u{10F86}-\u{10F89}\u{10FB0}-\u{10FCB}\u{10FE0}-\u{10FF6}\u{1E800}-\u{1E8C4}\u{1E8C7}-\u{1E8CF}\u{1E900}-\u{1E943}\u{1E94B}\u{1E950}-\u{1E959}\u{1E95E}\u{1E95F}\u{1EC71}-\u{1ECB4}\u{1ED01}-\u{1ED3D}\u{1EE00}-\u{1EE03}\u{1EE05}-\u{1EE1F}\u{1EE21}\u{1EE22}\u{1EE24}\u{1EE27}\u{1EE29}-\u{1EE32}\u{1EE34}-\u{1EE37}\u{1EE39}\u{1EE3B}\u{1EE42}\u{1EE47}\u{1EE49}\u{1EE4B}\u{1EE4D}-\u{1EE4F}\u{1EE51}\u{1EE52}\u{1EE54}\u{1EE57}\u{1EE59}\u{1EE5B}\u{1EE5D}\u{1EE5F}\u{1EE61}\u{1EE62}\u{1EE64}\u{1EE67}-\u{1EE6A}\u{1EE6C}-\u{1EE72}\u{1EE74}-\u{1EE77}\u{1EE79}-\u{1EE7C}\u{1EE7E}\u{1EE80}-\u{1EE89}\u{1EE8B}-\u{1EE9B}\u{1EEA1}-\u{1EEA3}\u{1EEA5}-\u{1EEA9}\u{1EEAB}-\u{1EEBB}]/u,qn6=/[A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02B8\u02BB-\u02C1\u02D0\u02D1\u02E0-\u02E4\u02EE\u0370-\u0373\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0482\u048A-\u052F\u0531-\u0556\u0559-\u0589\u0903-\u0939\u093B\u093D-\u0940\u0949-\u094C\u094E-\u0950\u0958-\u0961\u0964-\u0980\u0982\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD-\u09C0\u09C7\u09C8\u09CB\u09CC\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E1\u09E6-\u09F1\u09F4-\u09FA\u09FC\u09FD\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3E-\u0A40\u0A59-\u0A5C\u0A5E\u0A66-\u0A6F\u0A72-\u0A74\u0A76\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD-\u0AC0\u0AC9\u0ACB\u0ACC\u0AD0\u0AE0\u0AE1\u0AE6-\u0AF0\u0AF9\u0B02\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B3E\u0B40\u0B47\u0B48\u0B4B\u0B4C\u0B57\u0B5C\u0B5D\u0B5F-\u0B61\u0B66-\u0B77\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE\u0BBF\u0BC1\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCC\u0BD0\u0BD7\u0BE6-\u0BF2\u0C01-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C41-\u0C44\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C66-\u0C6F\u0C77\u0C7F\u0C80\u0C82-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD-\u0CC4\u0CC6-\u0CC8\u0CCA\u0CCB\u0CD5\u0CD6\u0CDD\u0CDE\u0CE0\u0CE1\u0CE6-\u0CEF\u0CF1-\u0CF3\u0D02-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D40\u0D46-\u0D48\u0D4A-\u0D4C\u0D4E\u0D4F\u0D54-\u0D61\u0D66-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCF-\u0DD1\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2-\u0DF4\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E4F-\u0E5B\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00-\u0F17\u0F1A-\u0F34\u0F36\u0F38\u0F3E-\u0F47\u0F49-\u0F6C\u0F7F\u0F85\u0F88-\u0F8C\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE-\u0FDA\u1000-\u102C\u1031\u1038\u103B\u103C\u103F-\u1057\u105A-\u105D\u1061-\u1070\u1075-\u1081\u1083\u1084\u1087-\u108C\u108E-\u109C\u109E-\u10C5\u10C7\u10CD\u10D0-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1360-\u137C\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u167F\u1681-\u169A\u16A0-\u16F8\u1700-\u1711\u1715\u171F-\u1731\u1734-\u1736\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17B6\u17BE-\u17C5\u17C7\u17C8\u17D4-\u17DA\u17DC\u17E0-\u17E9\u1810-\u1819\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1923-\u1926\u1929-\u192B\u1930\u1931\u1933-\u1938\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A16\u1A19\u1A1A\u1A1E-\u1A55\u1A57\u1A61\u1A63\u1A64\u1A6D-\u1A72\u1A80-\u1A89\u1A90-\u1A99\u1AA0-\u1AAD\u1B04-\u1B33\u1B35\u1B3B\u1B3D-\u1B41\u1B43-\u1B4C\u1B4E-\u1B6A\u1B74-\u1B7F\u1B82-\u1BA1\u1BA6\u1BA7\u1BAA\u1BAE-\u1BE5\u1BE7\u1BEA-\u1BEC\u1BEE\u1BF2\u1BF3\u1BFC-\u1C2B\u1C34\u1C35\u1C3B-\u1C49\u1C4D-\u1C8A\u1C90-\u1CBA\u1CBD-\u1CC7\u1CD3\u1CE1\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5-\u1CF7\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200E\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u214F\u2160-\u2188\u2336-\u237A\u2395\u249C-\u24E9\u26AC\u2800-\u28FF\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D70\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u302E\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u3190-\u31BF\u31F0-\u321C\u3220-\u324F\u3260-\u327B\u327F-\u32B0\u32C0-\u32CB\u32D0-\u3376\u337B-\u33DD\u33E0-\u33FE\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA60C\uA610-\uA62B\uA640-\uA66E\uA680-\uA69D\uA6A0-\uA6EF\uA6F2-\uA6F7\uA722-\uA787\uA789-\uA7CD\uA7D0\uA7D1\uA7D3\uA7D5-\uA7DC\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA824\uA827\uA830-\uA837\uA840-\uA873\uA880-\uA8C3\uA8CE-\uA8D9\uA8F2-\uA8FE\uA900-\uA925\uA92E-\uA946\uA952\uA953\uA95F-\uA97C\uA983-\uA9B2\uA9B4\uA9B5\uA9BA\uA9BB\uA9BE-\uA9CD\uA9CF-\uA9D9\uA9DE-\uA9E4\uA9E6-\uA9FE\uAA00-\uAA28\uAA2F\uAA30\uAA33\uAA34\uAA40-\uAA42\uAA44-\uAA4B\uAA4D\uAA50-\uAA59\uAA5C-\uAA7B\uAA7D-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAAEB\uAAEE-\uAAF5\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB69\uAB70-\uABE4\uABE6\uABE7\uABE9-\uABEC\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uD800-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC\u{10000}-\u{1000B}\u{1000D}-\u{10026}\u{10028}-\u{1003A}\u{1003C}\u{1003D}\u{1003F}-\u{1004D}\u{10050}-\u{1005D}\u{10080}-\u{100FA}\u{10100}\u{10102}\u{10107}-\u{10133}\u{10137}-\u{1013F}\u{1018D}\u{1018E}\u{101D0}-\u{101FC}\u{10280}-\u{1029C}\u{102A0}-\u{102D0}\u{10300}-\u{10323}\u{1032D}-\u{1034A}\u{10350}-\u{10375}\u{10380}-\u{1039D}\u{1039F}-\u{103C3}\u{103C8}-\u{103D5}\u{10400}-\u{1049D}\u{104A0}-\u{104A9}\u{104B0}-\u{104D3}\u{104D8}-\u{104FB}\u{10500}-\u{10527}\u{10530}-\u{10563}\u{1056F}-\u{1057A}\u{1057C}-\u{1058A}\u{1058C}-\u{10592}\u{10594}\u{10595}\u{10597}-\u{105A1}\u{105A3}-\u{105B1}\u{105B3}-\u{105B9}\u{105BB}\u{105BC}\u{105C0}-\u{105F3}\u{10600}-\u{10736}\u{10740}-\u{10755}\u{10760}-\u{10767}\u{10780}-\u{10785}\u{10787}-\u{107B0}\u{107B2}-\u{107BA}\u{11000}\u{11002}-\u{11037}\u{11047}-\u{1104D}\u{11066}-\u{1106F}\u{11071}\u{11072}\u{11075}\u{11082}-\u{110B2}\u{110B7}\u{110B8}\u{110BB}-\u{110C1}\u{110CD}\u{110D0}-\u{110E8}\u{110F0}-\u{110F9}\u{11103}-\u{11126}\u{1112C}\u{11136}-\u{11147}\u{11150}-\u{11172}\u{11174}-\u{11176}\u{11182}-\u{111B5}\u{111BF}-\u{111C8}\u{111CD}\u{111CE}\u{111D0}-\u{111DF}\u{111E1}-\u{111F4}\u{11200}-\u{11211}\u{11213}-\u{1122E}\u{11232}\u{11233}\u{11235}\u{11238}-\u{1123D}\u{1123F}\u{11240}\u{11280}-\u{11286}\u{11288}\u{1128A}-\u{1128D}\u{1128F}-\u{1129D}\u{1129F}-\u{112A9}\u{112B0}-\u{112DE}\u{112E0}-\u{112E2}\u{112F0}-\u{112F9}\u{11302}\u{11303}\u{11305}-\u{1130C}\u{1130F}\u{11310}\u{11313}-\u{11328}\u{1132A}-\u{11330}\u{11332}\u{11333}\u{11335}-\u{11339}\u{1133D}-\u{1133F}\u{11341}-\u{11344}\u{11347}\u{11348}\u{1134B}-\u{1134D}\u{11350}\u{11357}\u{1135D}-\u{11363}\u{11380}-\u{11389}\u{1138B}\u{1138E}\u{11390}-\u{113B5}\u{113B7}-\u{113BA}\u{113C2}\u{113C5}\u{113C7}-\u{113CA}\u{113CC}\u{113CD}\u{113CF}\u{113D1}\u{113D3}-\u{113D5}\u{113D7}\u{113D8}\u{11400}-\u{11437}\u{11440}\u{11441}\u{11445}\u{11447}-\u{1145B}\u{1145D}\u{1145F}-\u{11461}\u{11480}-\u{114B2}\u{114B9}\u{114BB}-\u{114BE}\u{114C1}\u{114C4}-\u{114C7}\u{114D0}-\u{114D9}\u{11580}-\u{115B1}\u{115B8}-\u{115BB}\u{115BE}\u{115C1}-\u{115DB}\u{11600}-\u{11632}\u{1163B}\u{1163C}\u{1163E}\u{11641}-\u{11644}\u{11650}-\u{11659}\u{11680}-\u{116AA}\u{116AC}\u{116AE}\u{116AF}\u{116B6}\u{116B8}\u{116B9}\u{116C0}-\u{116C9}\u{116D0}-\u{116E3}\u{11700}-\u{1171A}\u{1171E}\u{11720}\u{11721}\u{11726}\u{11730}-\u{11746}\u{11800}-\u{1182E}\u{11838}\u{1183B}\u{118A0}-\u{118F2}\u{118FF}-\u{11906}\u{11909}\u{1190C}-\u{11913}\u{11915}\u{11916}\u{11918}-\u{11935}\u{11937}\u{11938}\u{1193D}\u{1193F}-\u{11942}\u{11944}-\u{11946}\u{11950}-\u{11959}\u{119A0}-\u{119A7}\u{119AA}-\u{119D3}\u{119DC}-\u{119DF}\u{119E1}-\u{119E4}\u{11A00}\u{11A07}\u{11A08}\u{11A0B}-\u{11A32}\u{11A39}\u{11A3A}\u{11A3F}-\u{11A46}\u{11A50}\u{11A57}\u{11A58}\u{11A5C}-\u{11A89}\u{11A97}\u{11A9A}-\u{11AA2}\u{11AB0}-\u{11AF8}\u{11B00}-\u{11B09}\u{11BC0}-\u{11BE1}\u{11BF0}-\u{11BF9}\u{11C00}-\u{11C08}\u{11C0A}-\u{11C2F}\u{11C3E}-\u{11C45}\u{11C50}-\u{11C6C}\u{11C70}-\u{11C8F}\u{11CA9}\u{11CB1}\u{11CB4}\u{11D00}-\u{11D06}\u{11D08}\u{11D09}\u{11D0B}-\u{11D30}\u{11D46}\u{11D50}-\u{11D59}\u{11D60}-\u{11D65}\u{11D67}\u{11D68}\u{11D6A}-\u{11D8E}\u{11D93}\u{11D94}\u{11D96}\u{11D98}\u{11DA0}-\u{11DA9}\u{11EE0}-\u{11EF2}\u{11EF5}-\u{11EF8}\u{11F02}-\u{11F10}\u{11F12}-\u{11F35}\u{11F3E}\u{11F3F}\u{11F41}\u{11F43}-\u{11F59}\u{11FB0}\u{11FC0}-\u{11FD4}\u{11FFF}-\u{12399}\u{12400}-\u{1246E}\u{12470}-\u{12474}\u{12480}-\u{12543}\u{12F90}-\u{12FF2}\u{13000}-\u{1343F}\u{13441}-\u{13446}\u{13460}-\u{143FA}\u{14400}-\u{14646}\u{16100}-\u{1611D}\u{1612A}-\u{1612C}\u{16130}-\u{16139}\u{16800}-\u{16A38}\u{16A40}-\u{16A5E}\u{16A60}-\u{16A69}\u{16A6E}-\u{16ABE}\u{16AC0}-\u{16AC9}\u{16AD0}-\u{16AED}\u{16AF5}\u{16B00}-\u{16B2F}\u{16B37}-\u{16B45}\u{16B50}-\u{16B59}\u{16B5B}-\u{16B61}\u{16B63}-\u{16B77}\u{16B7D}-\u{16B8F}\u{16D40}-\u{16D79}\u{16E40}-\u{16E9A}\u{16F00}-\u{16F4A}\u{16F50}-\u{16F87}\u{16F93}-\u{16F9F}\u{16FE0}\u{16FE1}\u{16FE3}\u{16FF0}\u{16FF1}\u{17000}-\u{187F7}\u{18800}-\u{18CD5}\u{18CFF}-\u{18D08}\u{1AFF0}-\u{1AFF3}\u{1AFF5}-\u{1AFFB}\u{1AFFD}\u{1AFFE}\u{1B000}-\u{1B122}\u{1B132}\u{1B150}-\u{1B152}\u{1B155}\u{1B164}-\u{1B167}\u{1B170}-\u{1B2FB}\u{1BC00}-\u{1BC6A}\u{1BC70}-\u{1BC7C}\u{1BC80}-\u{1BC88}\u{1BC90}-\u{1BC99}\u{1BC9C}\u{1BC9F}\u{1CCD6}-\u{1CCEF}\u{1CF50}-\u{1CFC3}\u{1D000}-\u{1D0F5}\u{1D100}-\u{1D126}\u{1D129}-\u{1D166}\u{1D16A}-\u{1D172}\u{1D183}\u{1D184}\u{1D18C}-\u{1D1A9}\u{1D1AE}-\u{1D1E8}\u{1D2C0}-\u{1D2D3}\u{1D2E0}-\u{1D2F3}\u{1D360}-\u{1D378}\u{1D400}-\u{1D454}\u{1D456}-\u{1D49C}\u{1D49E}\u{1D49F}\u{1D4A2}\u{1D4A5}\u{1D4A6}\u{1D4A9}-\u{1D4AC}\u{1D4AE}-\u{1D4B9}\u{1D4BB}\u{1D4BD}-\u{1D4C3}\u{1D4C5}-\u{1D505}\u{1D507}-\u{1D50A}\u{1D50D}-\u{1D514}\u{1D516}-\u{1D51C}\u{1D51E}-\u{1D539}\u{1D53B}-\u{1D53E}\u{1D540}-\u{1D544}\u{1D546}\u{1D54A}-\u{1D550}\u{1D552}-\u{1D6A5}\u{1D6A8}-\u{1D6C0}\u{1D6C2}-\u{1D6DA}\u{1D6DC}-\u{1D6FA}\u{1D6FC}-\u{1D714}\u{1D716}-\u{1D734}\u{1D736}-\u{1D74E}\u{1D750}-\u{1D76E}\u{1D770}-\u{1D788}\u{1D78A}-\u{1D7A8}\u{1D7AA}-\u{1D7C2}\u{1D7C4}-\u{1D7CB}\u{1D800}-\u{1D9FF}\u{1DA37}-\u{1DA3A}\u{1DA6D}-\u{1DA74}\u{1DA76}-\u{1DA83}\u{1DA85}-\u{1DA8B}\u{1DF00}-\u{1DF1E}\u{1DF25}-\u{1DF2A}\u{1E030}-\u{1E06D}\u{1E100}-\u{1E12C}\u{1E137}-\u{1E13D}\u{1E140}-\u{1E149}\u{1E14E}\u{1E14F}\u{1E290}-\u{1E2AD}\u{1E2C0}-\u{1E2EB}\u{1E2F0}-\u{1E2F9}\u{1E4D0}-\u{1E4EB}\u{1E4F0}-\u{1E4F9}\u{1E5D0}-\u{1E5ED}\u{1E5F0}-\u{1E5FA}\u{1E5FF}\u{1E7E0}-\u{1E7E6}\u{1E7E8}-\u{1E7EB}\u{1E7ED}\u{1E7EE}\u{1E7F0}-\u{1E7FE}\u{1F110}-\u{1F12E}\u{1F130}-\u{1F169}\u{1F170}-\u{1F1AC}\u{1F1E6}-\u{1F202}\u{1F210}-\u{1F23B}\u{1F240}-\u{1F248}\u{1F250}\u{1F251}\u{20000}-\u{2A6DF}\u{2A700}-\u{2B739}\u{2B740}-\u{2B81D}\u{2B820}-\u{2CEA1}\u{2CEB0}-\u{2EBE0}\u{2EBF0}-\u{2EE5D}\u{2F800}-\u{2FA1D}\u{30000}-\u{3134A}\u{31350}-\u{323AF}\u{F0000}-\u{FFFFD}\u{100000}-\u{10FFFD}]/u,Nn6=/[\u05BE\u05C0\u05C3\u05C6\u05D0-\u05EA\u05EF-\u05F4\u0608\u060B\u060D\u061B-\u064A\u066D-\u066F\u0671-\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u070D\u070F\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07C0-\u07EA\u07F4\u07F5\u07FA\u07FE-\u0815\u081A\u0824\u0828\u0830-\u083E\u0840-\u0858\u085E\u0860-\u086A\u0870-\u088E\u08A0-\u08C9\u200F\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBC2\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFC\uFE70-\uFE74\uFE76-\uFEFC\u{10800}-\u{10805}\u{10808}\u{1080A}-\u{10835}\u{10837}\u{10838}\u{1083C}\u{1083F}-\u{10855}\u{10857}-\u{1089E}\u{108A7}-\u{108AF}\u{108E0}-\u{108F2}\u{108F4}\u{108F5}\u{108FB}-\u{1091B}\u{10920}-\u{10939}\u{1093F}\u{10980}-\u{109B7}\u{109BC}-\u{109CF}\u{109D2}-\u{10A00}\u{10A10}-\u{10A13}\u{10A15}-\u{10A17}\u{10A19}-\u{10A35}\u{10A40}-\u{10A48}\u{10A50}-\u{10A58}\u{10A60}-\u{10A9F}\u{10AC0}-\u{10AE4}\u{10AEB}-\u{10AF6}\u{10B00}-\u{10B35}\u{10B40}-\u{10B55}\u{10B58}-\u{10B72}\u{10B78}-\u{10B91}\u{10B99}-\u{10B9C}\u{10BA9}-\u{10BAF}\u{10C00}-\u{10C48}\u{10C80}-\u{10CB2}\u{10CC0}-\u{10CF2}\u{10CFA}-\u{10D23}\u{10D4A}-\u{10D65}\u{10D6F}-\u{10D85}\u{10D8E}\u{10D8F}\u{10E80}-\u{10EA9}\u{10EAD}\u{10EB0}\u{10EB1}\u{10EC2}-\u{10EC4}\u{10F00}-\u{10F27}\u{10F30}-\u{10F45}\u{10F51}-\u{10F59}\u{10F70}-\u{10F81}\u{10F86}-\u{10F89}\u{10FB0}-\u{10FCB}\u{10FE0}-\u{10FF6}\u{1E800}-\u{1E8C4}\u{1E8C7}-\u{1E8CF}\u{1E900}-\u{1E943}\u{1E94B}\u{1E950}-\u{1E959}\u{1E95E}\u{1E95F}\u{1EC71}-\u{1ECB4}\u{1ED01}-\u{1ED3D}\u{1EE00}-\u{1EE03}\u{1EE05}-\u{1EE1F}\u{1EE21}\u{1EE22}\u{1EE24}\u{1EE27}\u{1EE29}-\u{1EE32}\u{1EE34}-\u{1EE37}\u{1EE39}\u{1EE3B}\u{1EE42}\u{1EE47}\u{1EE49}\u{1EE4B}\u{1EE4D}-\u{1EE4F}\u{1EE51}\u{1EE52}\u{1EE54}\u{1EE57}\u{1EE59}\u{1EE5B}\u{1EE5D}\u{1EE5F}\u{1EE61}\u{1EE62}\u{1EE64}\u{1EE67}-\u{1EE6A}\u{1EE6C}-\u{1EE72}\u{1EE74}-\u{1EE77}\u{1EE79}-\u{1EE7C}\u{1EE7E}\u{1EE80}-\u{1EE89}\u{1EE8B}-\u{1EE9B}\u{1EEA1}-\u{1EEA3}\u{1EEA5}-\u{1EEA9}\u{1EEAB}-\u{1EEBB}]/u,Ln6=/^[\0-\x08\x0E-\x1B!-@\[-`\{-\x84\x86-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02B9\u02BA\u02C2-\u02CF\u02D2-\u02DF\u02E5-\u02ED\u02EF-\u036F\u0374\u0375\u037E\u0384\u0385\u0387\u03F6\u0483-\u0489\u058A\u058D-\u058F\u0591-\u05C7\u05D0-\u05EA\u05EF-\u05F4\u0600-\u070D\u070F-\u074A\u074D-\u07B1\u07C0-\u07FA\u07FD-\u082D\u0830-\u083E\u0840-\u085B\u085E\u0860-\u086A\u0870-\u088E\u0890\u0891\u0897-\u0902\u093A\u093C\u0941-\u0948\u094D\u0951-\u0957\u0962\u0963\u0981\u09BC\u09C1-\u09C4\u09CD\u09E2\u09E3\u09F2\u09F3\u09FB\u09FE\u0A01\u0A02\u0A3C\u0A41\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81\u0A82\u0ABC\u0AC1-\u0AC5\u0AC7\u0AC8\u0ACD\u0AE2\u0AE3\u0AF1\u0AFA-\u0AFF\u0B01\u0B3C\u0B3F\u0B41-\u0B44\u0B4D\u0B55\u0B56\u0B62\u0B63\u0B82\u0BC0\u0BCD\u0BF3-\u0BFA\u0C00\u0C04\u0C3C\u0C3E-\u0C40\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C78-\u0C7E\u0C81\u0CBC\u0CCC\u0CCD\u0CE2\u0CE3\u0D00\u0D01\u0D3B\u0D3C\u0D41-\u0D44\u0D4D\u0D62\u0D63\u0D81\u0DCA\u0DD2-\u0DD4\u0DD6\u0E31\u0E34-\u0E3A\u0E3F\u0E47-\u0E4E\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0F18\u0F19\u0F35\u0F37\u0F39-\u0F3D\u0F71-\u0F7E\u0F80-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102D-\u1030\u1032-\u1037\u1039\u103A\u103D\u103E\u1058\u1059\u105E-\u1060\u1071-\u1074\u1082\u1085\u1086\u108D\u109D\u135D-\u135F\u1390-\u1399\u1400\u169B\u169C\u1712-\u1714\u1732\u1733\u1752\u1753\u1772\u1773\u17B4\u17B5\u17B7-\u17BD\u17C6\u17C9-\u17D3\u17DB\u17DD\u17F0-\u17F9\u1800-\u180F\u1885\u1886\u18A9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193B\u1940\u1944\u1945\u19DE-\u19FF\u1A17\u1A18\u1A1B\u1A56\u1A58-\u1A5E\u1A60\u1A62\u1A65-\u1A6C\u1A73-\u1A7C\u1A7F\u1AB0-\u1ACE\u1B00-\u1B03\u1B34\u1B36-\u1B3A\u1B3C\u1B42\u1B6B-\u1B73\u1B80\u1B81\u1BA2-\u1BA5\u1BA8\u1BA9\u1BAB-\u1BAD\u1BE6\u1BE8\u1BE9\u1BED\u1BEF-\u1BF1\u1C2C-\u1C33\u1C36\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE0\u1CE2-\u1CE8\u1CED\u1CF4\u1CF8\u1CF9\u1DC0-\u1DFF\u1FBD\u1FBF-\u1FC1\u1FCD-\u1FCF\u1FDD-\u1FDF\u1FED-\u1FEF\u1FFD\u1FFE\u200B-\u200D\u200F-\u2027\u202F-\u205E\u2060-\u2064\u206A-\u2070\u2074-\u207E\u2080-\u208E\u20A0-\u20C0\u20D0-\u20F0\u2100\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u2150-\u215F\u2189-\u218B\u2190-\u2335\u237B-\u2394\u2396-\u2429\u2440-\u244A\u2460-\u249B\u24EA-\u26AB\u26AD-\u27FF\u2900-\u2B73\u2B76-\u2B95\u2B97-\u2BFF\u2CE5-\u2CEA\u2CEF-\u2CF1\u2CF9-\u2CFF\u2D7F\u2DE0-\u2E5D\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFF\u3001-\u3004\u3008-\u3020\u302A-\u302D\u3030\u3036\u3037\u303D-\u303F\u3099-\u309C\u30A0\u30FB\u31C0-\u31E5\u31EF\u321D\u321E\u3250-\u325F\u327C-\u327E\u32B1-\u32BF\u32CC-\u32CF\u3377-\u337A\u33DE\u33DF\u33FF\u4DC0-\u4DFF\uA490-\uA4C6\uA60D-\uA60F\uA66F-\uA67F\uA69E\uA69F\uA6F0\uA6F1\uA700-\uA721\uA788\uA802\uA806\uA80B\uA825\uA826\uA828-\uA82C\uA838\uA839\uA874-\uA877\uA8C4\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA951\uA980-\uA982\uA9B3\uA9B6-\uA9B9\uA9BC\uA9BD\uA9E5\uAA29-\uAA2E\uAA31\uAA32\uAA35\uAA36\uAA43\uAA4C\uAA7C\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEC\uAAED\uAAF6\uAB6A\uAB6B\uABE5\uABE8\uABED\uFB1D-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBC2\uFBD3-\uFD8F\uFD92-\uFDC7\uFDCF\uFDF0-\uFE19\uFE20-\uFE52\uFE54-\uFE66\uFE68-\uFE6B\uFE70-\uFE74\uFE76-\uFEFC\uFEFF\uFF01-\uFF20\uFF3B-\uFF40\uFF5B-\uFF65\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFF9-\uFFFD\u{10101}\u{10140}-\u{1018C}\u{10190}-\u{1019C}\u{101A0}\u{101FD}\u{102E0}-\u{102FB}\u{10376}-\u{1037A}\u{10800}-\u{10805}\u{10808}\u{1080A}-\u{10835}\u{10837}\u{10838}\u{1083C}\u{1083F}-\u{10855}\u{10857}-\u{1089E}\u{108A7}-\u{108AF}\u{108E0}-\u{108F2}\u{108F4}\u{108F5}\u{108FB}-\u{1091B}\u{1091F}-\u{10939}\u{1093F}\u{10980}-\u{109B7}\u{109BC}-\u{109CF}\u{109D2}-\u{10A03}\u{10A05}\u{10A06}\u{10A0C}-\u{10A13}\u{10A15}-\u{10A17}\u{10A19}-\u{10A35}\u{10A38}-\u{10A3A}\u{10A3F}-\u{10A48}\u{10A50}-\u{10A58}\u{10A60}-\u{10A9F}\u{10AC0}-\u{10AE6}\u{10AEB}-\u{10AF6}\u{10B00}-\u{10B35}\u{10B39}-\u{10B55}\u{10B58}-\u{10B72}\u{10B78}-\u{10B91}\u{10B99}-\u{10B9C}\u{10BA9}-\u{10BAF}\u{10C00}-\u{10C48}\u{10C80}-\u{10CB2}\u{10CC0}-\u{10CF2}\u{10CFA}-\u{10D27}\u{10D30}-\u{10D39}\u{10D40}-\u{10D65}\u{10D69}-\u{10D85}\u{10D8E}\u{10D8F}\u{10E60}-\u{10E7E}\u{10E80}-\u{10EA9}\u{10EAB}-\u{10EAD}\u{10EB0}\u{10EB1}\u{10EC2}-\u{10EC4}\u{10EFC}-\u{10F27}\u{10F30}-\u{10F59}\u{10F70}-\u{10F89}\u{10FB0}-\u{10FCB}\u{10FE0}-\u{10FF6}\u{11001}\u{11038}-\u{11046}\u{11052}-\u{11065}\u{11070}\u{11073}\u{11074}\u{1107F}-\u{11081}\u{110B3}-\u{110B6}\u{110B9}\u{110BA}\u{110C2}\u{11100}-\u{11102}\u{11127}-\u{1112B}\u{1112D}-\u{11134}\u{11173}\u{11180}\u{11181}\u{111B6}-\u{111BE}\u{111C9}-\u{111CC}\u{111CF}\u{1122F}-\u{11231}\u{11234}\u{11236}\u{11237}\u{1123E}\u{11241}\u{112DF}\u{112E3}-\u{112EA}\u{11300}\u{11301}\u{1133B}\u{1133C}\u{11340}\u{11366}-\u{1136C}\u{11370}-\u{11374}\u{113BB}-\u{113C0}\u{113CE}\u{113D0}\u{113D2}\u{113E1}\u{113E2}\u{11438}-\u{1143F}\u{11442}-\u{11444}\u{11446}\u{1145E}\u{114B3}-\u{114B8}\u{114BA}\u{114BF}\u{114C0}\u{114C2}\u{114C3}\u{115B2}-\u{115B5}\u{115BC}\u{115BD}\u{115BF}\u{115C0}\u{115DC}\u{115DD}\u{11633}-\u{1163A}\u{1163D}\u{1163F}\u{11640}\u{11660}-\u{1166C}\u{116AB}\u{116AD}\u{116B0}-\u{116B5}\u{116B7}\u{1171D}\u{1171F}\u{11722}-\u{11725}\u{11727}-\u{1172B}\u{1182F}-\u{11837}\u{11839}\u{1183A}\u{1193B}\u{1193C}\u{1193E}\u{11943}\u{119D4}-\u{119D7}\u{119DA}\u{119DB}\u{119E0}\u{11A01}-\u{11A06}\u{11A09}\u{11A0A}\u{11A33}-\u{11A38}\u{11A3B}-\u{11A3E}\u{11A47}\u{11A51}-\u{11A56}\u{11A59}-\u{11A5B}\u{11A8A}-\u{11A96}\u{11A98}\u{11A99}\u{11C30}-\u{11C36}\u{11C38}-\u{11C3D}\u{11C92}-\u{11CA7}\u{11CAA}-\u{11CB0}\u{11CB2}\u{11CB3}\u{11CB5}\u{11CB6}\u{11D31}-\u{11D36}\u{11D3A}\u{11D3C}\u{11D3D}\u{11D3F}-\u{11D45}\u{11D47}\u{11D90}\u{11D91}\u{11D95}\u{11D97}\u{11EF3}\u{11EF4}\u{11F00}\u{11F01}\u{11F36}-\u{11F3A}\u{11F40}\u{11F42}\u{11F5A}\u{11FD5}-\u{11FF1}\u{13440}\u{13447}-\u{13455}\u{1611E}-\u{16129}\u{1612D}-\u{1612F}\u{16AF0}-\u{16AF4}\u{16B30}-\u{16B36}\u{16F4F}\u{16F8F}-\u{16F92}\u{16FE2}\u{16FE4}\u{1BC9D}\u{1BC9E}\u{1BCA0}-\u{1BCA3}\u{1CC00}-\u{1CCD5}\u{1CCF0}-\u{1CCF9}\u{1CD00}-\u{1CEB3}\u{1CF00}-\u{1CF2D}\u{1CF30}-\u{1CF46}\u{1D167}-\u{1D169}\u{1D173}-\u{1D182}\u{1D185}-\u{1D18B}\u{1D1AA}-\u{1D1AD}\u{1D1E9}\u{1D1EA}\u{1D200}-\u{1D245}\u{1D300}-\u{1D356}\u{1D6C1}\u{1D6DB}\u{1D6FB}\u{1D715}\u{1D735}\u{1D74F}\u{1D76F}\u{1D789}\u{1D7A9}\u{1D7C3}\u{1D7CE}-\u{1D7FF}\u{1DA00}-\u{1DA36}\u{1DA3B}-\u{1DA6C}\u{1DA75}\u{1DA84}\u{1DA9B}-\u{1DA9F}\u{1DAA1}-\u{1DAAF}\u{1E000}-\u{1E006}\u{1E008}-\u{1E018}\u{1E01B}-\u{1E021}\u{1E023}\u{1E024}\u{1E026}-\u{1E02A}\u{1E08F}\u{1E130}-\u{1E136}\u{1E2AE}\u{1E2EC}-\u{1E2EF}\u{1E2FF}\u{1E4EC}-\u{1E4EF}\u{1E5EE}\u{1E5EF}\u{1E800}-\u{1E8C4}\u{1E8C7}-\u{1E8D6}\u{1E900}-\u{1E94B}\u{1E950}-\u{1E959}\u{1E95E}\u{1E95F}\u{1EC71}-\u{1ECB4}\u{1ED01}-\u{1ED3D}\u{1EE00}-\u{1EE03}\u{1EE05}-\u{1EE1F}\u{1EE21}\u{1EE22}\u{1EE24}\u{1EE27}\u{1EE29}-\u{1EE32}\u{1EE34}-\u{1EE37}\u{1EE39}\u{1EE3B}\u{1EE42}\u{1EE47}\u{1EE49}\u{1EE4B}\u{1EE4D}-\u{1EE4F}\u{1EE51}\u{1EE52}\u{1EE54}\u{1EE57}\u{1EE59}\u{1EE5B}\u{1EE5D}\u{1EE5F}\u{1EE61}\u{1EE62}\u{1EE64}\u{1EE67}-\u{1EE6A}\u{1EE6C}-\u{1EE72}\u{1EE74}-\u{1EE77}\u{1EE79}-\u{1EE7C}\u{1EE7E}\u{1EE80}-\u{1EE89}\u{1EE8B}-\u{1EE9B}\u{1EEA1}-\u{1EEA3}\u{1EEA5}-\u{1EEA9}\u{1EEAB}-\u{1EEBB}\u{1EEF0}\u{1EEF1}\u{1F000}-\u{1F02B}\u{1F030}-\u{1F093}\u{1F0A0}-\u{1F0AE}\u{1F0B1}-\u{1F0BF}\u{1F0C1}-\u{1F0CF}\u{1F0D1}-\u{1F0F5}\u{1F100}-\u{1F10F}\u{1F12F}\u{1F16A}-\u{1F16F}\u{1F1AD}\u{1F260}-\u{1F265}\u{1F300}-\u{1F6D7}\u{1F6DC}-\u{1F6EC}\u{1F6F0}-\u{1F6FC}\u{1F700}-\u{1F776}\u{1F77B}-\u{1F7D9}\u{1F7E0}-\u{1F7EB}\u{1F7F0}\u{1F800}-\u{1F80B}\u{1F810}-\u{1F847}\u{1F850}-\u{1F859}\u{1F860}-\u{1F887}\u{1F890}-\u{1F8AD}\u{1F8B0}-\u{1F8BB}\u{1F8C0}\u{1F8C1}\u{1F900}-\u{1FA53}\u{1FA60}-\u{1FA6D}\u{1FA70}-\u{1FA7C}\u{1FA80}-\u{1FA89}\u{1FA8F}-\u{1FAC6}\u{1FACE}-\u{1FADC}\u{1FADF}-\u{1FAE9}\u{1FAF0}-\u{1FAF8}\u{1FB00}-\u{1FB92}\u{1FB94}-\u{1FBF9}\u{E0001}\u{E0020}-\u{E007F}\u{E0100}-\u{E01EF}]*$/u,Mn6=/[0-9\xB2\xB3\xB9\u05BE\u05C0\u05C3\u05C6\u05D0-\u05EA\u05EF-\u05F4\u0600-\u0605\u0608\u060B\u060D\u061B-\u064A\u0660-\u0669\u066B-\u066F\u0671-\u06D5\u06DD\u06E5\u06E6\u06EE-\u070D\u070F\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07C0-\u07EA\u07F4\u07F5\u07FA\u07FE-\u0815\u081A\u0824\u0828\u0830-\u083E\u0840-\u0858\u085E\u0860-\u086A\u0870-\u088E\u0890\u0891\u08A0-\u08C9\u08E2\u200F\u2070\u2074-\u2079\u2080-\u2089\u2488-\u249B\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBC2\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFC\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\u{102E1}-\u{102FB}\u{10800}-\u{10805}\u{10808}\u{1080A}-\u{10835}\u{10837}\u{10838}\u{1083C}\u{1083F}-\u{10855}\u{10857}-\u{1089E}\u{108A7}-\u{108AF}\u{108E0}-\u{108F2}\u{108F4}\u{108F5}\u{108FB}-\u{1091B}\u{10920}-\u{10939}\u{1093F}\u{10980}-\u{109B7}\u{109BC}-\u{109CF}\u{109D2}-\u{10A00}\u{10A10}-\u{10A13}\u{10A15}-\u{10A17}\u{10A19}-\u{10A35}\u{10A40}-\u{10A48}\u{10A50}-\u{10A58}\u{10A60}-\u{10A9F}\u{10AC0}-\u{10AE4}\u{10AEB}-\u{10AF6}\u{10B00}-\u{10B35}\u{10B40}-\u{10B55}\u{10B58}-\u{10B72}\u{10B78}-\u{10B91}\u{10B99}-\u{10B9C}\u{10BA9}-\u{10BAF}\u{10C00}-\u{10C48}\u{10C80}-\u{10CB2}\u{10CC0}-\u{10CF2}\u{10CFA}-\u{10D23}\u{10D30}-\u{10D39}\u{10D40}-\u{10D65}\u{10D6F}-\u{10D85}\u{10D8E}\u{10D8F}\u{10E60}-\u{10E7E}\u{10E80}-\u{10EA9}\u{10EAD}\u{10EB0}\u{10EB1}\u{10EC2}-\u{10EC4}\u{10F00}-\u{10F27}\u{10F30}-\u{10F45}\u{10F51}-\u{10F59}\u{10F70}-\u{10F81}\u{10F86}-\u{10F89}\u{10FB0}-\u{10FCB}\u{10FE0}-\u{10FF6}\u{1CCF0}-\u{1CCF9}\u{1D7CE}-\u{1D7FF}\u{1E800}-\u{1E8C4}\u{1E8C7}-\u{1E8CF}\u{1E900}-\u{1E943}\u{1E94B}\u{1E950}-\u{1E959}\u{1E95E}\u{1E95F}\u{1EC71}-\u{1ECB4}\u{1ED01}-\u{1ED3D}\u{1EE00}-\u{1EE03}\u{1EE05}-\u{1EE1F}\u{1EE21}\u{1EE22}\u{1EE24}\u{1EE27}\u{1EE29}-\u{1EE32}\u{1EE34}-\u{1EE37}\u{1EE39}\u{1EE3B}\u{1EE42}\u{1EE47}\u{1EE49}\u{1EE4B}\u{1EE4D}-\u{1EE4F}\u{1EE51}\u{1EE52}\u{1EE54}\u{1EE57}\u{1EE59}\u{1EE5B}\u{1EE5D}\u{1EE5F}\u{1EE61}\u{1EE62}\u{1EE64}\u{1EE67}-\u{1EE6A}\u{1EE6C}-\u{1EE72}\u{1EE74}-\u{1EE77}\u{1EE79}-\u{1EE7C}\u{1EE7E}\u{1EE80}-\u{1EE89}\u{1EE8B}-\u{1EE9B}\u{1EEA1}-\u{1EEA3}\u{1EEA5}-\u{1EEA9}\u{1EEAB}-\u{1EEBB}\u{1F100}-\u{1F10A}\u{1FBF0}-\u{1FBF9}][\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0897-\u089F\u08CA-\u08E1\u08E3-\u0902\u093A\u093C\u0941-\u0948\u094D\u0951-\u0957\u0962\u0963\u0981\u09BC\u09C1-\u09C4\u09CD\u09E2\u09E3\u09FE\u0A01\u0A02\u0A3C\u0A41\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81\u0A82\u0ABC\u0AC1-\u0AC5\u0AC7\u0AC8\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01\u0B3C\u0B3F\u0B41-\u0B44\u0B4D\u0B55\u0B56\u0B62\u0B63\u0B82\u0BC0\u0BCD\u0C00\u0C04\u0C3C\u0C3E-\u0C40\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81\u0CBC\u0CCC\u0CCD\u0CE2\u0CE3\u0D00\u0D01\u0D3B\u0D3C\u0D41-\u0D44\u0D4D\u0D62\u0D63\u0D81\u0DCA\u0DD2-\u0DD4\u0DD6\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0F18\u0F19\u0F35\u0F37\u0F39\u0F71-\u0F7E\u0F80-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102D-\u1030\u1032-\u1037\u1039\u103A\u103D\u103E\u1058\u1059\u105E-\u1060\u1071-\u1074\u1082\u1085\u1086\u108D\u109D\u135D-\u135F\u1712-\u1714\u1732\u1733\u1752\u1753\u1772\u1773\u17B4\u17B5\u17B7-\u17BD\u17C6\u17C9-\u17D3\u17DD\u180B-\u180D\u180F\u1885\u1886\u18A9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193B\u1A17\u1A18\u1A1B\u1A56\u1A58-\u1A5E\u1A60\u1A62\u1A65-\u1A6C\u1A73-\u1A7C\u1A7F\u1AB0-\u1ACE\u1B00-\u1B03\u1B34\u1B36-\u1B3A\u1B3C\u1B42\u1B6B-\u1B73\u1B80\u1B81\u1BA2-\u1BA5\u1BA8\u1BA9\u1BAB-\u1BAD\u1BE6\u1BE8\u1BE9\u1BED\u1BEF-\u1BF1\u1C2C-\u1C33\u1C36\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE0\u1CE2-\u1CE8\u1CED\u1CF4\u1CF8\u1CF9\u1DC0-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302D\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA825\uA826\uA82C\uA8C4\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA951\uA980-\uA982\uA9B3\uA9B6-\uA9B9\uA9BC\uA9BD\uA9E5\uAA29-\uAA2E\uAA31\uAA32\uAA35\uAA36\uAA43\uAA4C\uAA7C\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEC\uAAED\uAAF6\uABE5\uABE8\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\u{101FD}\u{102E0}\u{10376}-\u{1037A}\u{10A01}-\u{10A03}\u{10A05}\u{10A06}\u{10A0C}-\u{10A0F}\u{10A38}-\u{10A3A}\u{10A3F}\u{10AE5}\u{10AE6}\u{10D24}-\u{10D27}\u{10D69}-\u{10D6D}\u{10EAB}\u{10EAC}\u{10EFC}-\u{10EFF}\u{10F46}-\u{10F50}\u{10F82}-\u{10F85}\u{11001}\u{11038}-\u{11046}\u{11070}\u{11073}\u{11074}\u{1107F}-\u{11081}\u{110B3}-\u{110B6}\u{110B9}\u{110BA}\u{110C2}\u{11100}-\u{11102}\u{11127}-\u{1112B}\u{1112D}-\u{11134}\u{11173}\u{11180}\u{11181}\u{111B6}-\u{111BE}\u{111C9}-\u{111CC}\u{111CF}\u{1122F}-\u{11231}\u{11234}\u{11236}\u{11237}\u{1123E}\u{11241}\u{112DF}\u{112E3}-\u{112EA}\u{11300}\u{11301}\u{1133B}\u{1133C}\u{11340}\u{11366}-\u{1136C}\u{11370}-\u{11374}\u{113BB}-\u{113C0}\u{113CE}\u{113D0}\u{113D2}\u{113E1}\u{113E2}\u{11438}-\u{1143F}\u{11442}-\u{11444}\u{11446}\u{1145E}\u{114B3}-\u{114B8}\u{114BA}\u{114BF}\u{114C0}\u{114C2}\u{114C3}\u{115B2}-\u{115B5}\u{115BC}\u{115BD}\u{115BF}\u{115C0}\u{115DC}\u{115DD}\u{11633}-\u{1163A}\u{1163D}\u{1163F}\u{11640}\u{116AB}\u{116AD}\u{116B0}-\u{116B5}\u{116B7}\u{1171D}\u{1171F}\u{11722}-\u{11725}\u{11727}-\u{1172B}\u{1182F}-\u{11837}\u{11839}\u{1183A}\u{1193B}\u{1193C}\u{1193E}\u{11943}\u{119D4}-\u{119D7}\u{119DA}\u{119DB}\u{119E0}\u{11A01}-\u{11A06}\u{11A09}\u{11A0A}\u{11A33}-\u{11A38}\u{11A3B}-\u{11A3E}\u{11A47}\u{11A51}-\u{11A56}\u{11A59}-\u{11A5B}\u{11A8A}-\u{11A96}\u{11A98}\u{11A99}\u{11C30}-\u{11C36}\u{11C38}-\u{11C3D}\u{11C92}-\u{11CA7}\u{11CAA}-\u{11CB0}\u{11CB2}\u{11CB3}\u{11CB5}\u{11CB6}\u{11D31}-\u{11D36}\u{11D3A}\u{11D3C}\u{11D3D}\u{11D3F}-\u{11D45}\u{11D47}\u{11D90}\u{11D91}\u{11D95}\u{11D97}\u{11EF3}\u{11EF4}\u{11F00}\u{11F01}\u{11F36}-\u{11F3A}\u{11F40}\u{11F42}\u{11F5A}\u{13440}\u{13447}-\u{13455}\u{1611E}-\u{16129}\u{1612D}-\u{1612F}\u{16AF0}-\u{16AF4}\u{16B30}-\u{16B36}\u{16F4F}\u{16F8F}-\u{16F92}\u{16FE4}\u{1BC9D}\u{1BC9E}\u{1CF00}-\u{1CF2D}\u{1CF30}-\u{1CF46}\u{1D167}-\u{1D169}\u{1D17B}-\u{1D182}\u{1D185}-\u{1D18B}\u{1D1AA}-\u{1D1AD}\u{1D242}-\u{1D244}\u{1DA00}-\u{1DA36}\u{1DA3B}-\u{1DA6C}\u{1DA75}\u{1DA84}\u{1DA9B}-\u{1DA9F}\u{1DAA1}-\u{1DAAF}\u{1E000}-\u{1E006}\u{1E008}-\u{1E018}\u{1E01B}-\u{1E021}\u{1E023}\u{1E024}\u{1E026}-\u{1E02A}\u{1E08F}\u{1E130}-\u{1E136}\u{1E2AE}\u{1E2EC}-\u{1E2EF}\u{1E4EC}-\u{1E4EF}\u{1E5EE}\u{1E5EF}\u{1E8D0}-\u{1E8D6}\u{1E944}-\u{1E94A}\u{E0100}-\u{E01EF}]*$/u,Rn6=/[0-9\xB2\xB3\xB9\u06F0-\u06F9\u2070\u2074-\u2079\u2080-\u2089\u2488-\u249B\uFF10-\uFF19\u{102E1}-\u{102FB}\u{1CCF0}-\u{1CCF9}\u{1D7CE}-\u{1D7FF}\u{1F100}-\u{1F10A}\u{1FBF0}-\u{1FBF9}]/u,On6=/[\u0600-\u0605\u0660-\u0669\u066B\u066C\u06DD\u0890\u0891\u08E2\u{10D30}-\u{10D39}\u{10D40}-\u{10D49}\u{10E60}-\u{10E7E}]/u,Tn6=/^[\0-\x08\x0E-\x1B!-\x84\x86-\u0377\u037A-\u037F\u0384-\u038A\u038C\u038E-\u03A1\u03A3-\u052F\u0531-\u0556\u0559-\u058A\u058D-\u058F\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0606\u0607\u0609\u060A\u060C\u060E-\u061A\u064B-\u065F\u066A\u0670\u06D6-\u06DC\u06DE-\u06E4\u06E7-\u06ED\u06F0-\u06F9\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07F6-\u07F9\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0897-\u089F\u08CA-\u08E1\u08E3-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09FE\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A76\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AF1\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B77\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BFA\u0C00-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3C-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C5D\u0C60-\u0C63\u0C66-\u0C6F\u0C77-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDD\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1-\u0CF3\u0D00-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4F\u0D54-\u0D63\u0D66-\u0D7F\u0D81-\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2-\u0DF4\u0E01-\u0E3A\u0E3F-\u0E5B\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECE\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00-\u0F47\u0F49-\u0F6C\u0F71-\u0F97\u0F99-\u0FBC\u0FBE-\u0FCC\u0FCE-\u0FDA\u1000-\u10C5\u10C7\u10CD\u10D0-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u137C\u1380-\u1399\u13A0-\u13F5\u13F8-\u13FD\u1400-\u167F\u1681-\u169C\u16A0-\u16F8\u1700-\u1715\u171F-\u1736\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17DD\u17E0-\u17E9\u17F0-\u17F9\u1800-\u1819\u1820-\u1878\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1940\u1944-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u19DE-\u1A1B\u1A1E-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA0-\u1AAD\u1AB0-\u1ACE\u1B00-\u1B4C\u1B4E-\u1BF3\u1BFC-\u1C37\u1C3B-\u1C49\u1C4D-\u1C8A\u1C90-\u1CBA\u1CBD-\u1CC7\u1CD0-\u1CFA\u1D00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FC4\u1FC6-\u1FD3\u1FD6-\u1FDB\u1FDD-\u1FEF\u1FF2-\u1FF4\u1FF6-\u1FFE\u200B-\u200E\u2010-\u2027\u202F-\u205E\u2060-\u2064\u206A-\u2071\u2074-\u208E\u2090-\u209C\u20A0-\u20C0\u20D0-\u20F0\u2100-\u218B\u2190-\u2429\u2440-\u244A\u2460-\u2B73\u2B76-\u2B95\u2B97-\u2CF3\u2CF9-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D70\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2E5D\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFF\u3001-\u303F\u3041-\u3096\u3099-\u30FF\u3105-\u312F\u3131-\u318E\u3190-\u31E5\u31EF-\u321E\u3220-\uA48C\uA490-\uA4C6\uA4D0-\uA62B\uA640-\uA6F7\uA700-\uA7CD\uA7D0\uA7D1\uA7D3\uA7D5-\uA7DC\uA7F2-\uA82C\uA830-\uA839\uA840-\uA877\uA880-\uA8C5\uA8CE-\uA8D9\uA8E0-\uA953\uA95F-\uA97C\uA980-\uA9CD\uA9CF-\uA9D9\uA9DE-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA5C-\uAAC2\uAADB-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB6B\uAB70-\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uD800-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1E\uFB29\uFD3E-\uFD4F\uFDCF\uFDFD-\uFE19\uFE20-\uFE52\uFE54-\uFE66\uFE68-\uFE6B\uFEFF\uFF01-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFF9-\uFFFD\u{10000}-\u{1000B}\u{1000D}-\u{10026}\u{10028}-\u{1003A}\u{1003C}\u{1003D}\u{1003F}-\u{1004D}\u{10050}-\u{1005D}\u{10080}-\u{100FA}\u{10100}-\u{10102}\u{10107}-\u{10133}\u{10137}-\u{1018E}\u{10190}-\u{1019C}\u{101A0}\u{101D0}-\u{101FD}\u{10280}-\u{1029C}\u{102A0}-\u{102D0}\u{102E0}-\u{102FB}\u{10300}-\u{10323}\u{1032D}-\u{1034A}\u{10350}-\u{1037A}\u{10380}-\u{1039D}\u{1039F}-\u{103C3}\u{103C8}-\u{103D5}\u{10400}-\u{1049D}\u{104A0}-\u{104A9}\u{104B0}-\u{104D3}\u{104D8}-\u{104FB}\u{10500}-\u{10527}\u{10530}-\u{10563}\u{1056F}-\u{1057A}\u{1057C}-\u{1058A}\u{1058C}-\u{10592}\u{10594}\u{10595}\u{10597}-\u{105A1}\u{105A3}-\u{105B1}\u{105B3}-\u{105B9}\u{105BB}\u{105BC}\u{105C0}-\u{105F3}\u{10600}-\u{10736}\u{10740}-\u{10755}\u{10760}-\u{10767}\u{10780}-\u{10785}\u{10787}-\u{107B0}\u{107B2}-\u{107BA}\u{1091F}\u{10A01}-\u{10A03}\u{10A05}\u{10A06}\u{10A0C}-\u{10A0F}\u{10A38}-\u{10A3A}\u{10A3F}\u{10AE5}\u{10AE6}\u{10B39}-\u{10B3F}\u{10D24}-\u{10D27}\u{10D69}-\u{10D6E}\u{10EAB}\u{10EAC}\u{10EFC}-\u{10EFF}\u{10F46}-\u{10F50}\u{10F82}-\u{10F85}\u{11000}-\u{1104D}\u{11052}-\u{11075}\u{1107F}-\u{110C2}\u{110CD}\u{110D0}-\u{110E8}\u{110F0}-\u{110F9}\u{11100}-\u{11134}\u{11136}-\u{11147}\u{11150}-\u{11176}\u{11180}-\u{111DF}\u{111E1}-\u{111F4}\u{11200}-\u{11211}\u{11213}-\u{11241}\u{11280}-\u{11286}\u{11288}\u{1128A}-\u{1128D}\u{1128F}-\u{1129D}\u{1129F}-\u{112A9}\u{112B0}-\u{112EA}\u{112F0}-\u{112F9}\u{11300}-\u{11303}\u{11305}-\u{1130C}\u{1130F}\u{11310}\u{11313}-\u{11328}\u{1132A}-\u{11330}\u{11332}\u{11333}\u{11335}-\u{11339}\u{1133B}-\u{11344}\u{11347}\u{11348}\u{1134B}-\u{1134D}\u{11350}\u{11357}\u{1135D}-\u{11363}\u{11366}-\u{1136C}\u{11370}-\u{11374}\u{11380}-\u{11389}\u{1138B}\u{1138E}\u{11390}-\u{113B5}\u{113B7}-\u{113C0}\u{113C2}\u{113C5}\u{113C7}-\u{113CA}\u{113CC}-\u{113D5}\u{113D7}\u{113D8}\u{113E1}\u{113E2}\u{11400}-\u{1145B}\u{1145D}-\u{11461}\u{11480}-\u{114C7}\u{114D0}-\u{114D9}\u{11580}-\u{115B5}\u{115B8}-\u{115DD}\u{11600}-\u{11644}\u{11650}-\u{11659}\u{11660}-\u{1166C}\u{11680}-\u{116B9}\u{116C0}-\u{116C9}\u{116D0}-\u{116E3}\u{11700}-\u{1171A}\u{1171D}-\u{1172B}\u{11730}-\u{11746}\u{11800}-\u{1183B}\u{118A0}-\u{118F2}\u{118FF}-\u{11906}\u{11909}\u{1190C}-\u{11913}\u{11915}\u{11916}\u{11918}-\u{11935}\u{11937}\u{11938}\u{1193B}-\u{11946}\u{11950}-\u{11959}\u{119A0}-\u{119A7}\u{119AA}-\u{119D7}\u{119DA}-\u{119E4}\u{11A00}-\u{11A47}\u{11A50}-\u{11AA2}\u{11AB0}-\u{11AF8}\u{11B00}-\u{11B09}\u{11BC0}-\u{11BE1}\u{11BF0}-\u{11BF9}\u{11C00}-\u{11C08}\u{11C0A}-\u{11C36}\u{11C38}-\u{11C45}\u{11C50}-\u{11C6C}\u{11C70}-\u{11C8F}\u{11C92}-\u{11CA7}\u{11CA9}-\u{11CB6}\u{11D00}-\u{11D06}\u{11D08}\u{11D09}\u{11D0B}-\u{11D36}\u{11D3A}\u{11D3C}\u{11D3D}\u{11D3F}-\u{11D47}\u{11D50}-\u{11D59}\u{11D60}-\u{11D65}\u{11D67}\u{11D68}\u{11D6A}-\u{11D8E}\u{11D90}\u{11D91}\u{11D93}-\u{11D98}\u{11DA0}-\u{11DA9}\u{11EE0}-\u{11EF8}\u{11F00}-\u{11F10}\u{11F12}-\u{11F3A}\u{11F3E}-\u{11F5A}\u{11FB0}\u{11FC0}-\u{11FF1}\u{11FFF}-\u{12399}\u{12400}-\u{1246E}\u{12470}-\u{12474}\u{12480}-\u{12543}\u{12F90}-\u{12FF2}\u{13000}-\u{13455}\u{13460}-\u{143FA}\u{14400}-\u{14646}\u{16100}-\u{16139}\u{16800}-\u{16A38}\u{16A40}-\u{16A5E}\u{16A60}-\u{16A69}\u{16A6E}-\u{16ABE}\u{16AC0}-\u{16AC9}\u{16AD0}-\u{16AED}\u{16AF0}-\u{16AF5}\u{16B00}-\u{16B45}\u{16B50}-\u{16B59}\u{16B5B}-\u{16B61}\u{16B63}-\u{16B77}\u{16B7D}-\u{16B8F}\u{16D40}-\u{16D79}\u{16E40}-\u{16E9A}\u{16F00}-\u{16F4A}\u{16F4F}-\u{16F87}\u{16F8F}-\u{16F9F}\u{16FE0}-\u{16FE4}\u{16FF0}\u{16FF1}\u{17000}-\u{187F7}\u{18800}-\u{18CD5}\u{18CFF}-\u{18D08}\u{1AFF0}-\u{1AFF3}\u{1AFF5}-\u{1AFFB}\u{1AFFD}\u{1AFFE}\u{1B000}-\u{1B122}\u{1B132}\u{1B150}-\u{1B152}\u{1B155}\u{1B164}-\u{1B167}\u{1B170}-\u{1B2FB}\u{1BC00}-\u{1BC6A}\u{1BC70}-\u{1BC7C}\u{1BC80}-\u{1BC88}\u{1BC90}-\u{1BC99}\u{1BC9C}-\u{1BCA3}\u{1CC00}-\u{1CCF9}\u{1CD00}-\u{1CEB3}\u{1CF00}-\u{1CF2D}\u{1CF30}-\u{1CF46}\u{1CF50}-\u{1CFC3}\u{1D000}-\u{1D0F5}\u{1D100}-\u{1D126}\u{1D129}-\u{1D1EA}\u{1D200}-\u{1D245}\u{1D2C0}-\u{1D2D3}\u{1D2E0}-\u{1D2F3}\u{1D300}-\u{1D356}\u{1D360}-\u{1D378}\u{1D400}-\u{1D454}\u{1D456}-\u{1D49C}\u{1D49E}\u{1D49F}\u{1D4A2}\u{1D4A5}\u{1D4A6}\u{1D4A9}-\u{1D4AC}\u{1D4AE}-\u{1D4B9}\u{1D4BB}\u{1D4BD}-\u{1D4C3}\u{1D4C5}-\u{1D505}\u{1D507}-\u{1D50A}\u{1D50D}-\u{1D514}\u{1D516}-\u{1D51C}\u{1D51E}-\u{1D539}\u{1D53B}-\u{1D53E}\u{1D540}-\u{1D544}\u{1D546}\u{1D54A}-\u{1D550}\u{1D552}-\u{1D6A5}\u{1D6A8}-\u{1D7CB}\u{1D7CE}-\u{1DA8B}\u{1DA9B}-\u{1DA9F}\u{1DAA1}-\u{1DAAF}\u{1DF00}-\u{1DF1E}\u{1DF25}-\u{1DF2A}\u{1E000}-\u{1E006}\u{1E008}-\u{1E018}\u{1E01B}-\u{1E021}\u{1E023}\u{1E024}\u{1E026}-\u{1E02A}\u{1E030}-\u{1E06D}\u{1E08F}\u{1E100}-\u{1E12C}\u{1E130}-\u{1E13D}\u{1E140}-\u{1E149}\u{1E14E}\u{1E14F}\u{1E290}-\u{1E2AE}\u{1E2C0}-\u{1E2F9}\u{1E2FF}\u{1E4D0}-\u{1E4F9}\u{1E5D0}-\u{1E5FA}\u{1E5FF}\u{1E7E0}-\u{1E7E6}\u{1E7E8}-\u{1E7EB}\u{1E7ED}\u{1E7EE}\u{1E7F0}-\u{1E7FE}\u{1E8D0}-\u{1E8D6}\u{1E944}-\u{1E94A}\u{1EEF0}\u{1EEF1}\u{1F000}-\u{1F02B}\u{1F030}-\u{1F093}\u{1F0A0}-\u{1F0AE}\u{1F0B1}-\u{1F0BF}\u{1F0C1}-\u{1F0CF}\u{1F0D1}-\u{1F0F5}\u{1F100}-\u{1F1AD}\u{1F1E6}-\u{1F202}\u{1F210}-\u{1F23B}\u{1F240}-\u{1F248}\u{1F250}\u{1F251}\u{1F260}-\u{1F265}\u{1F300}-\u{1F6D7}\u{1F6DC}-\u{1F6EC}\u{1F6F0}-\u{1F6FC}\u{1F700}-\u{1F776}\u{1F77B}-\u{1F7D9}\u{1F7E0}-\u{1F7EB}\u{1F7F0}\u{1F800}-\u{1F80B}\u{1F810}-\u{1F847}\u{1F850}-\u{1F859}\u{1F860}-\u{1F887}\u{1F890}-\u{1F8AD}\u{1F8B0}-\u{1F8BB}\u{1F8C0}\u{1F8C1}\u{1F900}-\u{1FA53}\u{1FA60}-\u{1FA6D}\u{1FA70}-\u{1FA7C}\u{1FA80}-\u{1FA89}\u{1FA8F}-\u{1FAC6}\u{1FACE}-\u{1FADC}\u{1FADF}-\u{1FAE9}\u{1FAF0}-\u{1FAF8}\u{1FB00}-\u{1FB92}\u{1FB94}-\u{1FBF9}\u{20000}-\u{2A6DF}\u{2A700}-\u{2B739}\u{2B740}-\u{2B81D}\u{2B820}-\u{2CEA1}\u{2CEB0}-\u{2EBE0}\u{2EBF0}-\u{2EE5D}\u{2F800}-\u{2FA1D}\u{30000}-\u{3134A}\u{31350}-\u{323AF}\u{E0001}\u{E0020}-\u{E007F}\u{E0100}-\u{E01EF}\u{F0000}-\u{FFFFD}\u{100000}-\u{10FFFD}]*$/u,Pn6=/[0-9A-Za-z\xAA\xB2\xB3\xB5\xB9\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02B8\u02BB-\u02C1\u02D0\u02D1\u02E0-\u02E4\u02EE\u0370-\u0373\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0482\u048A-\u052F\u0531-\u0556\u0559-\u0589\u06F0-\u06F9\u0903-\u0939\u093B\u093D-\u0940\u0949-\u094C\u094E-\u0950\u0958-\u0961\u0964-\u0980\u0982\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD-\u09C0\u09C7\u09C8\u09CB\u09CC\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E1\u09E6-\u09F1\u09F4-\u09FA\u09FC\u09FD\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3E-\u0A40\u0A59-\u0A5C\u0A5E\u0A66-\u0A6F\u0A72-\u0A74\u0A76\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD-\u0AC0\u0AC9\u0ACB\u0ACC\u0AD0\u0AE0\u0AE1\u0AE6-\u0AF0\u0AF9\u0B02\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B3E\u0B40\u0B47\u0B48\u0B4B\u0B4C\u0B57\u0B5C\u0B5D\u0B5F-\u0B61\u0B66-\u0B77\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE\u0BBF\u0BC1\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCC\u0BD0\u0BD7\u0BE6-\u0BF2\u0C01-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C41-\u0C44\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C66-\u0C6F\u0C77\u0C7F\u0C80\u0C82-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD-\u0CC4\u0CC6-\u0CC8\u0CCA\u0CCB\u0CD5\u0CD6\u0CDD\u0CDE\u0CE0\u0CE1\u0CE6-\u0CEF\u0CF1-\u0CF3\u0D02-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D40\u0D46-\u0D48\u0D4A-\u0D4C\u0D4E\u0D4F\u0D54-\u0D61\u0D66-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCF-\u0DD1\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2-\u0DF4\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E4F-\u0E5B\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00-\u0F17\u0F1A-\u0F34\u0F36\u0F38\u0F3E-\u0F47\u0F49-\u0F6C\u0F7F\u0F85\u0F88-\u0F8C\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE-\u0FDA\u1000-\u102C\u1031\u1038\u103B\u103C\u103F-\u1057\u105A-\u105D\u1061-\u1070\u1075-\u1081\u1083\u1084\u1087-\u108C\u108E-\u109C\u109E-\u10C5\u10C7\u10CD\u10D0-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1360-\u137C\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u167F\u1681-\u169A\u16A0-\u16F8\u1700-\u1711\u1715\u171F-\u1731\u1734-\u1736\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17B6\u17BE-\u17C5\u17C7\u17C8\u17D4-\u17DA\u17DC\u17E0-\u17E9\u1810-\u1819\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1923-\u1926\u1929-\u192B\u1930\u1931\u1933-\u1938\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A16\u1A19\u1A1A\u1A1E-\u1A55\u1A57\u1A61\u1A63\u1A64\u1A6D-\u1A72\u1A80-\u1A89\u1A90-\u1A99\u1AA0-\u1AAD\u1B04-\u1B33\u1B35\u1B3B\u1B3D-\u1B41\u1B43-\u1B4C\u1B4E-\u1B6A\u1B74-\u1B7F\u1B82-\u1BA1\u1BA6\u1BA7\u1BAA\u1BAE-\u1BE5\u1BE7\u1BEA-\u1BEC\u1BEE\u1BF2\u1BF3\u1BFC-\u1C2B\u1C34\u1C35\u1C3B-\u1C49\u1C4D-\u1C8A\u1C90-\u1CBA\u1CBD-\u1CC7\u1CD3\u1CE1\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5-\u1CF7\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200E\u2070\u2071\u2074-\u2079\u207F-\u2089\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u214F\u2160-\u2188\u2336-\u237A\u2395\u2488-\u24E9\u26AC\u2800-\u28FF\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D70\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u302E\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u3190-\u31BF\u31F0-\u321C\u3220-\u324F\u3260-\u327B\u327F-\u32B0\u32C0-\u32CB\u32D0-\u3376\u337B-\u33DD\u33E0-\u33FE\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA60C\uA610-\uA62B\uA640-\uA66E\uA680-\uA69D\uA6A0-\uA6EF\uA6F2-\uA6F7\uA722-\uA787\uA789-\uA7CD\uA7D0\uA7D1\uA7D3\uA7D5-\uA7DC\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA824\uA827\uA830-\uA837\uA840-\uA873\uA880-\uA8C3\uA8CE-\uA8D9\uA8F2-\uA8FE\uA900-\uA925\uA92E-\uA946\uA952\uA953\uA95F-\uA97C\uA983-\uA9B2\uA9B4\uA9B5\uA9BA\uA9BB\uA9BE-\uA9CD\uA9CF-\uA9D9\uA9DE-\uA9E4\uA9E6-\uA9FE\uAA00-\uAA28\uAA2F\uAA30\uAA33\uAA34\uAA40-\uAA42\uAA44-\uAA4B\uAA4D\uAA50-\uAA59\uAA5C-\uAA7B\uAA7D-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAAEB\uAAEE-\uAAF5\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB69\uAB70-\uABE4\uABE6\uABE7\uABE9-\uABEC\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uD800-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFF10-\uFF19\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC\u{10000}-\u{1000B}\u{1000D}-\u{10026}\u{10028}-\u{1003A}\u{1003C}\u{1003D}\u{1003F}-\u{1004D}\u{10050}-\u{1005D}\u{10080}-\u{100FA}\u{10100}\u{10102}\u{10107}-\u{10133}\u{10137}-\u{1013F}\u{1018D}\u{1018E}\u{101D0}-\u{101FC}\u{10280}-\u{1029C}\u{102A0}-\u{102D0}\u{102E1}-\u{102FB}\u{10300}-\u{10323}\u{1032D}-\u{1034A}\u{10350}-\u{10375}\u{10380}-\u{1039D}\u{1039F}-\u{103C3}\u{103C8}-\u{103D5}\u{10400}-\u{1049D}\u{104A0}-\u{104A9}\u{104B0}-\u{104D3}\u{104D8}-\u{104FB}\u{10500}-\u{10527}\u{10530}-\u{10563}\u{1056F}-\u{1057A}\u{1057C}-\u{1058A}\u{1058C}-\u{10592}\u{10594}\u{10595}\u{10597}-\u{105A1}\u{105A3}-\u{105B1}\u{105B3}-\u{105B9}\u{105BB}\u{105BC}\u{105C0}-\u{105F3}\u{10600}-\u{10736}\u{10740}-\u{10755}\u{10760}-\u{10767}\u{10780}-\u{10785}\u{10787}-\u{107B0}\u{107B2}-\u{107BA}\u{11000}\u{11002}-\u{11037}\u{11047}-\u{1104D}\u{11066}-\u{1106F}\u{11071}\u{11072}\u{11075}\u{11082}-\u{110B2}\u{110B7}\u{110B8}\u{110BB}-\u{110C1}\u{110CD}\u{110D0}-\u{110E8}\u{110F0}-\u{110F9}\u{11103}-\u{11126}\u{1112C}\u{11136}-\u{11147}\u{11150}-\u{11172}\u{11174}-\u{11176}\u{11182}-\u{111B5}\u{111BF}-\u{111C8}\u{111CD}\u{111CE}\u{111D0}-\u{111DF}\u{111E1}-\u{111F4}\u{11200}-\u{11211}\u{11213}-\u{1122E}\u{11232}\u{11233}\u{11235}\u{11238}-\u{1123D}\u{1123F}\u{11240}\u{11280}-\u{11286}\u{11288}\u{1128A}-\u{1128D}\u{1128F}-\u{1129D}\u{1129F}-\u{112A9}\u{112B0}-\u{112DE}\u{112E0}-\u{112E2}\u{112F0}-\u{112F9}\u{11302}\u{11303}\u{11305}-\u{1130C}\u{1130F}\u{11310}\u{11313}-\u{11328}\u{1132A}-\u{11330}\u{11332}\u{11333}\u{11335}-\u{11339}\u{1133D}-\u{1133F}\u{11341}-\u{11344}\u{11347}\u{11348}\u{1134B}-\u{1134D}\u{11350}\u{11357}\u{1135D}-\u{11363}\u{11380}-\u{11389}\u{1138B}\u{1138E}\u{11390}-\u{113B5}\u{113B7}-\u{113BA}\u{113C2}\u{113C5}\u{113C7}-\u{113CA}\u{113CC}\u{113CD}\u{113CF}\u{113D1}\u{113D3}-\u{113D5}\u{113D7}\u{113D8}\u{11400}-\u{11437}\u{11440}\u{11441}\u{11445}\u{11447}-\u{1145B}\u{1145D}\u{1145F}-\u{11461}\u{11480}-\u{114B2}\u{114B9}\u{114BB}-\u{114BE}\u{114C1}\u{114C4}-\u{114C7}\u{114D0}-\u{114D9}\u{11580}-\u{115B1}\u{115B8}-\u{115BB}\u{115BE}\u{115C1}-\u{115DB}\u{11600}-\u{11632}\u{1163B}\u{1163C}\u{1163E}\u{11641}-\u{11644}\u{11650}-\u{11659}\u{11680}-\u{116AA}\u{116AC}\u{116AE}\u{116AF}\u{116B6}\u{116B8}\u{116B9}\u{116C0}-\u{116C9}\u{116D0}-\u{116E3}\u{11700}-\u{1171A}\u{1171E}\u{11720}\u{11721}\u{11726}\u{11730}-\u{11746}\u{11800}-\u{1182E}\u{11838}\u{1183B}\u{118A0}-\u{118F2}\u{118FF}-\u{11906}\u{11909}\u{1190C}-\u{11913}\u{11915}\u{11916}\u{11918}-\u{11935}\u{11937}\u{11938}\u{1193D}\u{1193F}-\u{11942}\u{11944}-\u{11946}\u{11950}-\u{11959}\u{119A0}-\u{119A7}\u{119AA}-\u{119D3}\u{119DC}-\u{119DF}\u{119E1}-\u{119E4}\u{11A00}\u{11A07}\u{11A08}\u{11A0B}-\u{11A32}\u{11A39}\u{11A3A}\u{11A3F}-\u{11A46}\u{11A50}\u{11A57}\u{11A58}\u{11A5C}-\u{11A89}\u{11A97}\u{11A9A}-\u{11AA2}\u{11AB0}-\u{11AF8}\u{11B00}-\u{11B09}\u{11BC0}-\u{11BE1}\u{11BF0}-\u{11BF9}\u{11C00}-\u{11C08}\u{11C0A}-\u{11C2F}\u{11C3E}-\u{11C45}\u{11C50}-\u{11C6C}\u{11C70}-\u{11C8F}\u{11CA9}\u{11CB1}\u{11CB4}\u{11D00}-\u{11D06}\u{11D08}\u{11D09}\u{11D0B}-\u{11D30}\u{11D46}\u{11D50}-\u{11D59}\u{11D60}-\u{11D65}\u{11D67}\u{11D68}\u{11D6A}-\u{11D8E}\u{11D93}\u{11D94}\u{11D96}\u{11D98}\u{11DA0}-\u{11DA9}\u{11EE0}-\u{11EF2}\u{11EF5}-\u{11EF8}\u{11F02}-\u{11F10}\u{11F12}-\u{11F35}\u{11F3E}\u{11F3F}\u{11F41}\u{11F43}-\u{11F59}\u{11FB0}\u{11FC0}-\u{11FD4}\u{11FFF}-\u{12399}\u{12400}-\u{1246E}\u{12470}-\u{12474}\u{12480}-\u{12543}\u{12F90}-\u{12FF2}\u{13000}-\u{1343F}\u{13441}-\u{13446}\u{13460}-\u{143FA}\u{14400}-\u{14646}\u{16100}-\u{1611D}\u{1612A}-\u{1612C}\u{16130}-\u{16139}\u{16800}-\u{16A38}\u{16A40}-\u{16A5E}\u{16A60}-\u{16A69}\u{16A6E}-\u{16ABE}\u{16AC0}-\u{16AC9}\u{16AD0}-\u{16AED}\u{16AF5}\u{16B00}-\u{16B2F}\u{16B37}-\u{16B45}\u{16B50}-\u{16B59}\u{16B5B}-\u{16B61}\u{16B63}-\u{16B77}\u{16B7D}-\u{16B8F}\u{16D40}-\u{16D79}\u{16E40}-\u{16E9A}\u{16F00}-\u{16F4A}\u{16F50}-\u{16F87}\u{16F93}-\u{16F9F}\u{16FE0}\u{16FE1}\u{16FE3}\u{16FF0}\u{16FF1}\u{17000}-\u{187F7}\u{18800}-\u{18CD5}\u{18CFF}-\u{18D08}\u{1AFF0}-\u{1AFF3}\u{1AFF5}-\u{1AFFB}\u{1AFFD}\u{1AFFE}\u{1B000}-\u{1B122}\u{1B132}\u{1B150}-\u{1B152}\u{1B155}\u{1B164}-\u{1B167}\u{1B170}-\u{1B2FB}\u{1BC00}-\u{1BC6A}\u{1BC70}-\u{1BC7C}\u{1BC80}-\u{1BC88}\u{1BC90}-\u{1BC99}\u{1BC9C}\u{1BC9F}\u{1CCD6}-\u{1CCF9}\u{1CF50}-\u{1CFC3}\u{1D000}-\u{1D0F5}\u{1D100}-\u{1D126}\u{1D129}-\u{1D166}\u{1D16A}-\u{1D172}\u{1D183}\u{1D184}\u{1D18C}-\u{1D1A9}\u{1D1AE}-\u{1D1E8}\u{1D2C0}-\u{1D2D3}\u{1D2E0}-\u{1D2F3}\u{1D360}-\u{1D378}\u{1D400}-\u{1D454}\u{1D456}-\u{1D49C}\u{1D49E}\u{1D49F}\u{1D4A2}\u{1D4A5}\u{1D4A6}\u{1D4A9}-\u{1D4AC}\u{1D4AE}-\u{1D4B9}\u{1D4BB}\u{1D4BD}-\u{1D4C3}\u{1D4C5}-\u{1D505}\u{1D507}-\u{1D50A}\u{1D50D}-\u{1D514}\u{1D516}-\u{1D51C}\u{1D51E}-\u{1D539}\u{1D53B}-\u{1D53E}\u{1D540}-\u{1D544}\u{1D546}\u{1D54A}-\u{1D550}\u{1D552}-\u{1D6A5}\u{1D6A8}-\u{1D6C0}\u{1D6C2}-\u{1D6DA}\u{1D6DC}-\u{1D6FA}\u{1D6FC}-\u{1D714}\u{1D716}-\u{1D734}\u{1D736}-\u{1D74E}\u{1D750}-\u{1D76E}\u{1D770}-\u{1D788}\u{1D78A}-\u{1D7A8}\u{1D7AA}-\u{1D7C2}\u{1D7C4}-\u{1D7CB}\u{1D7CE}-\u{1D9FF}\u{1DA37}-\u{1DA3A}\u{1DA6D}-\u{1DA74}\u{1DA76}-\u{1DA83}\u{1DA85}-\u{1DA8B}\u{1DF00}-\u{1DF1E}\u{1DF25}-\u{1DF2A}\u{1E030}-\u{1E06D}\u{1E100}-\u{1E12C}\u{1E137}-\u{1E13D}\u{1E140}-\u{1E149}\u{1E14E}\u{1E14F}\u{1E290}-\u{1E2AD}\u{1E2C0}-\u{1E2EB}\u{1E2F0}-\u{1E2F9}\u{1E4D0}-\u{1E4EB}\u{1E4F0}-\u{1E4F9}\u{1E5D0}-\u{1E5ED}\u{1E5F0}-\u{1E5FA}\u{1E5FF}\u{1E7E0}-\u{1E7E6}\u{1E7E8}-\u{1E7EB}\u{1E7ED}\u{1E7EE}\u{1E7F0}-\u{1E7FE}\u{1F100}-\u{1F10A}\u{1F110}-\u{1F12E}\u{1F130}-\u{1F169}\u{1F170}-\u{1F1AC}\u{1F1E6}-\u{1F202}\u{1F210}-\u{1F23B}\u{1F240}-\u{1F248}\u{1F250}\u{1F251}\u{1FBF0}-\u{1FBF9}\u{20000}-\u{2A6DF}\u{2A700}-\u{2B739}\u{2B740}-\u{2B81D}\u{2B820}-\u{2CEA1}\u{2CEB0}-\u{2EBE0}\u{2EBF0}-\u{2EE5D}\u{2F800}-\u{2FA1D}\u{30000}-\u{3134A}\u{31350}-\u{323AF}\u{F0000}-\u{FFFFD}\u{100000}-\u{10FFFD}][\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0897-\u089F\u08CA-\u08E1\u08E3-\u0902\u093A\u093C\u0941-\u0948\u094D\u0951-\u0957\u0962\u0963\u0981\u09BC\u09C1-\u09C4\u09CD\u09E2\u09E3\u09FE\u0A01\u0A02\u0A3C\u0A41\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81\u0A82\u0ABC\u0AC1-\u0AC5\u0AC7\u0AC8\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01\u0B3C\u0B3F\u0B41-\u0B44\u0B4D\u0B55\u0B56\u0B62\u0B63\u0B82\u0BC0\u0BCD\u0C00\u0C04\u0C3C\u0C3E-\u0C40\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81\u0CBC\u0CCC\u0CCD\u0CE2\u0CE3\u0D00\u0D01\u0D3B\u0D3C\u0D41-\u0D44\u0D4D\u0D62\u0D63\u0D81\u0DCA\u0DD2-\u0DD4\u0DD6\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0F18\u0F19\u0F35\u0F37\u0F39\u0F71-\u0F7E\u0F80-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102D-\u1030\u1032-\u1037\u1039\u103A\u103D\u103E\u1058\u1059\u105E-\u1060\u1071-\u1074\u1082\u1085\u1086\u108D\u109D\u135D-\u135F\u1712-\u1714\u1732\u1733\u1752\u1753\u1772\u1773\u17B4\u17B5\u17B7-\u17BD\u17C6\u17C9-\u17D3\u17DD\u180B-\u180D\u180F\u1885\u1886\u18A9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193B\u1A17\u1A18\u1A1B\u1A56\u1A58-\u1A5E\u1A60\u1A62\u1A65-\u1A6C\u1A73-\u1A7C\u1A7F\u1AB0-\u1ACE\u1B00-\u1B03\u1B34\u1B36-\u1B3A\u1B3C\u1B42\u1B6B-\u1B73\u1B80\u1B81\u1BA2-\u1BA5\u1BA8\u1BA9\u1BAB-\u1BAD\u1BE6\u1BE8\u1BE9\u1BED\u1BEF-\u1BF1\u1C2C-\u1C33\u1C36\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE0\u1CE2-\u1CE8\u1CED\u1CF4\u1CF8\u1CF9\u1DC0-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302D\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA825\uA826\uA82C\uA8C4\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA951\uA980-\uA982\uA9B3\uA9B6-\uA9B9\uA9BC\uA9BD\uA9E5\uAA29-\uAA2E\uAA31\uAA32\uAA35\uAA36\uAA43\uAA4C\uAA7C\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEC\uAAED\uAAF6\uABE5\uABE8\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\u{101FD}\u{102E0}\u{10376}-\u{1037A}\u{10A01}-\u{10A03}\u{10A05}\u{10A06}\u{10A0C}-\u{10A0F}\u{10A38}-\u{10A3A}\u{10A3F}\u{10AE5}\u{10AE6}\u{10D24}-\u{10D27}\u{10D69}-\u{10D6D}\u{10EAB}\u{10EAC}\u{10EFC}-\u{10EFF}\u{10F46}-\u{10F50}\u{10F82}-\u{10F85}\u{11001}\u{11038}-\u{11046}\u{11070}\u{11073}\u{11074}\u{1107F}-\u{11081}\u{110B3}-\u{110B6}\u{110B9}\u{110BA}\u{110C2}\u{11100}-\u{11102}\u{11127}-\u{1112B}\u{1112D}-\u{11134}\u{11173}\u{11180}\u{11181}\u{111B6}-\u{111BE}\u{111C9}-\u{111CC}\u{111CF}\u{1122F}-\u{11231}\u{11234}\u{11236}\u{11237}\u{1123E}\u{11241}\u{112DF}\u{112E3}-\u{112EA}\u{11300}\u{11301}\u{1133B}\u{1133C}\u{11340}\u{11366}-\u{1136C}\u{11370}-\u{11374}\u{113BB}-\u{113C0}\u{113CE}\u{113D0}\u{113D2}\u{113E1}\u{113E2}\u{11438}-\u{1143F}\u{11442}-\u{11444}\u{11446}\u{1145E}\u{114B3}-\u{114B8}\u{114BA}\u{114BF}\u{114C0}\u{114C2}\u{114C3}\u{115B2}-\u{115B5}\u{115BC}\u{115BD}\u{115BF}\u{115C0}\u{115DC}\u{115DD}\u{11633}-\u{1163A}\u{1163D}\u{1163F}\u{11640}\u{116AB}\u{116AD}\u{116B0}-\u{116B5}\u{116B7}\u{1171D}\u{1171F}\u{11722}-\u{11725}\u{11727}-\u{1172B}\u{1182F}-\u{11837}\u{11839}\u{1183A}\u{1193B}\u{1193C}\u{1193E}\u{11943}\u{119D4}-\u{119D7}\u{119DA}\u{119DB}\u{119E0}\u{11A01}-\u{11A06}\u{11A09}\u{11A0A}\u{11A33}-\u{11A38}\u{11A3B}-\u{11A3E}\u{11A47}\u{11A51}-\u{11A56}\u{11A59}-\u{11A5B}\u{11A8A}-\u{11A96}\u{11A98}\u{11A99}\u{11C30}-\u{11C36}\u{11C38}-\u{11C3D}\u{11C92}-\u{11CA7}\u{11CAA}-\u{11CB0}\u{11CB2}\u{11CB3}\u{11CB5}\u{11CB6}\u{11D31}-\u{11D36}\u{11D3A}\u{11D3C}\u{11D3D}\u{11D3F}-\u{11D45}\u{11D47}\u{11D90}\u{11D91}\u{11D95}\u{11D97}\u{11EF3}\u{11EF4}\u{11F00}\u{11F01}\u{11F36}-\u{11F3A}\u{11F40}\u{11F42}\u{11F5A}\u{13440}\u{13447}-\u{13455}\u{1611E}-\u{16129}\u{1612D}-\u{1612F}\u{16AF0}-\u{16AF4}\u{16B30}-\u{16B36}\u{16F4F}\u{16F8F}-\u{16F92}\u{16FE4}\u{1BC9D}\u{1BC9E}\u{1CF00}-\u{1CF2D}\u{1CF30}-\u{1CF46}\u{1D167}-\u{1D169}\u{1D17B}-\u{1D182}\u{1D185}-\u{1D18B}\u{1D1AA}-\u{1D1AD}\u{1D242}-\u{1D244}\u{1DA00}-\u{1DA36}\u{1DA3B}-\u{1DA6C}\u{1DA75}\u{1DA84}\u{1DA9B}-\u{1DA9F}\u{1DAA1}-\u{1DAAF}\u{1E000}-\u{1E006}\u{1E008}-\u{1E018}\u{1E01B}-\u{1E021}\u{1E023}\u{1E024}\u{1E026}-\u{1E02A}\u{1E08F}\u{1E130}-\u{1E136}\u{1E2AE}\u{1E2EC}-\u{1E2EF}\u{1E4EC}-\u{1E4EF}\u{1E5EE}\u{1E5EF}\u{1E8D0}-\u{1E8D6}\u{1E944}-\u{1E94A}\u{E0100}-\u{E01EF}]*$/u;$VB.exports={combiningMarks:En6,combiningClassVirama:Un6,validZWNJ:wn6,bidiDomain:$n6,bidiS1LTR:qn6,bidiS1RTL:Nn6,bidiS2:Ln6,bidiS3:Mn6,bidiS4EN:Rn6,bidiS4AN:On6,bidiS5:Tn6,bidiS6:Pn6}});
var rKB=E((sKB)=>{Object.defineProperty(sKB,"__esModule",{value:!0});sKB.validate=Mo6;function Mo6(A){let B=[{invalid:"uri",expected:"url"},{invalid:"json",expected:"data"},{invalid:"qs",expected:"params"}];for(let Q of B)if(A[Q.invalid]){let D=`'${Q.invalid}' is not a valid configuration option. Please use '${Q.expected}' instead. This library is using Axios for requests. Please see https://github.com/axios/axios to learn more about the valid request options.`;throw new Error(D)}}});
var rqA=E((aqA)=>{Object.defineProperty(aqA,"__esModule",{value:!0});aqA.default=void 0;var E$Q=U$Q(nQ1());function U$Q(A){return A&&A.__esModule?A:{default:A}}function w$Q(A){if(!E$Q.default(A))throw TypeError("Invalid UUID");return parseInt(A.slice(14,15),16)}var $$Q=w$Q;aqA.default=$$Q});
var sD1=E((tKB)=>{Object.defineProperty(tKB,"__esModule",{value:!0});tKB.DefaultTransporter=void 0;var To6=X$(),Po6=rKB(),So6=GE0(),oKB="google-api-nodejs-client";class aD1{constructor(){this.instance=new To6.Gaxios}configure(A={}){if(A.headers=A.headers||{},typeof window==="undefined"){let B=A.headers["User-Agent"];if(!B)A.headers["User-Agent"]=aD1.USER_AGENT;else if(!B.includes(`${oKB}/`))A.headers["User-Agent"]=`${B} ${aD1.USER_AGENT}`;if(!A.headers["x-goog-api-client"]){let Q=process.version.replace(/^v/,"");A.headers["x-goog-api-client"]=`gl-node/${Q}`}}return A}request(A){return A=this.configure(A),Po6.validate(A),this.instance.request(A).catch((B)=>{throw this.processError(B)})}get defaults(){return this.instance.defaults}set defaults(A){this.instance.defaults=A}processError(A){let B=A.response,Q=A,D=B?B.data:null;if(B&&D&&D.error&&B.status!==200)if(typeof D.error==="string")Q.message=D.error,Q.status=B.status;else if(Array.isArray(D.error.errors))Q.message=D.error.errors.map((Z)=>Z.message).join(`
`),Q.code=D.error.code,Q.errors=D.error.errors;else Q.message=D.error.message,Q.code=D.error.code;else if(B&&B.status>=400)Q.message=D,Q.status=B.status;return Q}}tKB.DefaultTransporter=aD1;aD1.USER_AGENT=`${oKB}/${So6.version}`});
var sE0=E((ezB)=>{Object.defineProperty(ezB,"__esModule",{value:!0});ezB.AwsRequestSigner=void 0;var ozB=he(),rzB="AWS4-HMAC-SHA256",je6="aws4_request";class tzB{constructor(A,B){this.getCredentials=A,this.region=B,this.crypto=ozB.createCrypto()}async getRequestOptions(A){if(!A.url)throw new Error('"url" is required in "amzOptions"');let B=typeof A.data==="object"?JSON.stringify(A.data):A.data,Q=A.url,D=A.method||"GET",Z=A.body||B,G=A.headers,F=await this.getCredentials(),I=new URL(Q),Y=await ye6({crypto:this.crypto,host:I.host,canonicalUri:I.pathname,canonicalQuerystring:I.search.substr(1),method:D,region:this.region,securityCredentials:F,requestPayload:Z,additionalAmzHeaders:G}),W=Object.assign(Y.amzDate?{"x-amz-date":Y.amzDate}:{},{Authorization:Y.authorizationHeader,host:I.host},G||{});if(F.token)Object.assign(W,{"x-amz-security-token":F.token});let J={url:Q,method:D,headers:W};if(typeof Z!=="undefined")J.body=Z;return J}}ezB.AwsRequestSigner=tzB;async function QZ1(A,B,Q){return await A.signWithHmacSha256(B,Q)}async function ke6(A,B,Q,D,Z){let G=await QZ1(A,`AWS4${B}`,Q),F=await QZ1(A,G,D),I=await QZ1(A,F,Z);return await QZ1(A,I,"aws4_request")}async function ye6(A){let B=A.additionalAmzHeaders||{},Q=A.requestPayload||"",D=A.host.split(".")[0],Z=new Date,G=Z.toISOString().replace(/[-:]/g,"").replace(/\.[0-9]+/,""),F=Z.toISOString().replace(/[-]/g,"").replace(/T.*/,""),I={};if(Object.keys(B).forEach((N)=>{I[N.toLowerCase()]=B[N]}),A.securityCredentials.token)I["x-amz-security-token"]=A.securityCredentials.token;let Y=Object.assign({host:A.host},I.date?{}:{"x-amz-date":G},I),W="",J=Object.keys(Y).sort();J.forEach((N)=>{W+=`${N}:${Y[N]}
`});let X=J.join(";"),V=await A.crypto.sha256DigestHex(Q),C=`${A.method}
${A.canonicalUri}
${A.canonicalQuerystring}
${W}
${X}
${V}`,K=`${F}/${A.region}/${D}/${je6}`,H=`${rzB}
${G}
${K}
`+await A.crypto.sha256DigestHex(C),z=await ke6(A.crypto,A.securityCredentials.secretAccessKey,F,A.region,D),$=await QZ1(A.crypto,z,H),L=`${rzB} Credential=${A.securityCredentials.accessKeyId}/${K}, SignedHeaders=${X}, Signature=${ozB.fromArrayBufferToHex($)}`;return{amzDate:I.date?void 0:G,authorizationHeader:L,canonicalQuerystring:A.canonicalQuerystring}}});
var sQ1=E(($w)=>{Object.defineProperty($w,"__esModule",{value:!0});Object.defineProperty($w,"NIL",{enumerable:!0,get:function(){return R$Q.default}});Object.defineProperty($w,"parse",{enumerable:!0,get:function(){return S$Q.default}});Object.defineProperty($w,"stringify",{enumerable:!0,get:function(){return P$Q.default}});Object.defineProperty($w,"v1",{enumerable:!0,get:function(){return q$Q.default}});Object.defineProperty($w,"v3",{enumerable:!0,get:function(){return N$Q.default}});Object.defineProperty($w,"v4",{enumerable:!0,get:function(){return L$Q.default}});Object.defineProperty($w,"v5",{enumerable:!0,get:function(){return M$Q.default}});Object.defineProperty($w,"validate",{enumerable:!0,get:function(){return T$Q.default}});Object.defineProperty($w,"version",{enumerable:!0,get:function(){return O$Q.default}});var q$Q=hO(zqA()),N$Q=hO(SqA()),L$Q=hO(fqA()),M$Q=hO(lqA()),R$Q=hO(nqA()),O$Q=hO(rqA()),T$Q=hO(nQ1()),P$Q=hO(aQ1()),S$Q=hO(Ze1());function hO(A){return A&&A.__esModule?A:{default:A}}});
var sl0=E((kh8,al0)=>{var hl=1000,gl=hl*60,ul=gl*60,Tf=ul*24,AM9=Tf*7,BM9=Tf*365.25;al0.exports=function(A,B){B=B||{};var Q=typeof A;if(Q==="string"&&A.length>0)return QM9(A);else if(Q==="number"&&isFinite(A))return B.long?ZM9(A):DM9(A);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(A))};function QM9(A){if(A=String(A),A.length>100)return;var B=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(A);if(!B)return;var Q=parseFloat(B[1]),D=(B[2]||"ms").toLowerCase();switch(D){case"years":case"year":case"yrs":case"yr":case"y":return Q*BM9;case"weeks":case"week":case"w":return Q*AM9;case"days":case"day":case"d":return Q*Tf;case"hours":case"hour":case"hrs":case"hr":case"h":return Q*ul;case"minutes":case"minute":case"mins":case"min":case"m":return Q*gl;case"seconds":case"second":case"secs":case"sec":case"s":return Q*hl;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return Q;default:return}}function DM9(A){var B=Math.abs(A);if(B>=Tf)return Math.round(A/Tf)+"d";if(B>=ul)return Math.round(A/ul)+"h";if(B>=gl)return Math.round(A/gl)+"m";if(B>=hl)return Math.round(A/hl)+"s";return A+"ms"}function ZM9(A){var B=Math.abs(A);if(B>=Tf)return $X1(A,B,Tf,"day");if(B>=ul)return $X1(A,B,ul,"hour");if(B>=gl)return $X1(A,B,gl,"minute");if(B>=hl)return $X1(A,B,hl,"second");return A+" ms"}function $X1(A,B,Q,D){var Z=B>=Q*1.5;return Math.round(A/Q)+" "+D+(Z?"s":"")}});
var sy1=E((QVB)=>{function F3(A,B,Q){if(Q.globals)A=Q.globals[A.name];return new A(`${Q.context?Q.context:"Value"} ${B}.`)}function Pe(A,B){if(typeof A==="bigint")throw F3(TypeError,"is a BigInt which cannot be converted to a number",B);if(!B.globals)return Number(A);return B.globals.Number(A)}function AVB(A){if(A>0&&A%1===0.5&&(A&1)===0||A<0&&A%1===-0.5&&(A&1)===1)return gD1(Math.floor(A));return gD1(Math.round(A))}function ay1(A){return gD1(Math.trunc(A))}function eXB(A){return A<0?-1:1}function Ii6(A,B){let Q=A%B;if(eXB(B)!==eXB(Q))return Q+B;return Q}function gD1(A){return A===0?0:A}function Se(A,{unsigned:B}){let Q,D;if(B)Q=0,D=2**A-1;else Q=-(2**(A-1)),D=2**(A-1)-1;let Z=2**A,G=2**(A-1);return(F,I={})=>{let Y=Pe(F,I);if(Y=gD1(Y),I.enforceRange){if(!Number.isFinite(Y))throw F3(TypeError,"is not a finite number",I);if(Y=ay1(Y),Y<Q||Y>D)throw F3(TypeError,`is outside the accepted range of ${Q} to ${D}, inclusive`,I);return Y}if(!Number.isNaN(Y)&&I.clamp)return Y=Math.min(Math.max(Y,Q),D),Y=AVB(Y),Y;if(!Number.isFinite(Y)||Y===0)return 0;if(Y=ay1(Y),Y>=Q&&Y<=D)return Y;if(Y=Ii6(Y,Z),!B&&Y>=G)return Y-Z;return Y}}function BVB(A,{unsigned:B}){let Q=Number.MAX_SAFE_INTEGER,D=B?0:Number.MIN_SAFE_INTEGER,Z=B?BigInt.asUintN:BigInt.asIntN;return(G,F={})=>{let I=Pe(G,F);if(I=gD1(I),F.enforceRange){if(!Number.isFinite(I))throw F3(TypeError,"is not a finite number",F);if(I=ay1(I),I<D||I>Q)throw F3(TypeError,`is outside the accepted range of ${D} to ${Q}, inclusive`,F);return I}if(!Number.isNaN(I)&&F.clamp)return I=Math.min(Math.max(I,D),Q),I=AVB(I),I;if(!Number.isFinite(I)||I===0)return 0;let Y=BigInt(ay1(I));return Y=Z(A,Y),Number(Y)}}QVB.any=(A)=>{return A};QVB.undefined=()=>{return};QVB.boolean=(A)=>{return Boolean(A)};QVB.byte=Se(8,{unsigned:!1});QVB.octet=Se(8,{unsigned:!0});QVB.short=Se(16,{unsigned:!1});QVB["unsigned short"]=Se(16,{unsigned:!0});QVB.long=Se(32,{unsigned:!1});QVB["unsigned long"]=Se(32,{unsigned:!0});QVB["long long"]=BVB(64,{unsigned:!1});QVB["unsigned long long"]=BVB(64,{unsigned:!0});QVB.double=(A,B={})=>{let Q=Pe(A,B);if(!Number.isFinite(Q))throw F3(TypeError,"is not a finite floating-point value",B);return Q};QVB["unrestricted double"]=(A,B={})=>{return Pe(A,B)};QVB.float=(A,B={})=>{let Q=Pe(A,B);if(!Number.isFinite(Q))throw F3(TypeError,"is not a finite floating-point value",B);if(Object.is(Q,-0))return Q;let D=Math.fround(Q);if(!Number.isFinite(D))throw F3(TypeError,"is outside the range of a single-precision floating-point value",B);return D};QVB["unrestricted float"]=(A,B={})=>{let Q=Pe(A,B);if(isNaN(Q))return Q;if(Object.is(Q,-0))return Q;return Math.fround(Q)};QVB.DOMString=(A,B={})=>{if(B.treatNullAsEmptyString&&A===null)return"";if(typeof A==="symbol")throw F3(TypeError,"is a symbol, which cannot be converted to a string",B);return(B.globals?B.globals.String:String)(A)};QVB.ByteString=(A,B={})=>{let Q=QVB.DOMString(A,B),D;for(let Z=0;(D=Q.codePointAt(Z))!==void 0;++Z)if(D>255)throw F3(TypeError,"is not a valid ByteString",B);return Q};QVB.USVString=(A,B={})=>{let Q=QVB.DOMString(A,B),D=Q.length,Z=[];for(let G=0;G<D;++G){let F=Q.charCodeAt(G);if(F<55296||F>57343)Z.push(String.fromCodePoint(F));else if(56320<=F&&F<=57343)Z.push(String.fromCodePoint(65533));else if(G===D-1)Z.push(String.fromCodePoint(65533));else{let I=Q.charCodeAt(G+1);if(56320<=I&&I<=57343){let Y=F&1023,W=I&1023;Z.push(String.fromCodePoint(65536+1024*Y+W)),++G}else Z.push(String.fromCodePoint(65533))}}return Z.join("")};QVB.object=(A,B={})=>{if(A===null||typeof A!=="object"&&typeof A!=="function")throw F3(TypeError,"is not an object",B);return A};var Yi6=Object.getOwnPropertyDescriptor(ArrayBuffer.prototype,"byteLength").get,Wi6=typeof SharedArrayBuffer==="function"?Object.getOwnPropertyDescriptor(SharedArrayBuffer.prototype,"byteLength").get:null;function Kz0(A){try{return Yi6.call(A),!0}catch{return!1}}function Oe(A){try{return Wi6.call(A),!0}catch{return!1}}function Te(A){try{return new Uint8Array(A),!1}catch{return!0}}QVB.ArrayBuffer=(A,B={})=>{if(!Kz0(A)){if(B.allowShared&&!Oe(A))throw F3(TypeError,"is not an ArrayBuffer or SharedArrayBuffer",B);throw F3(TypeError,"is not an ArrayBuffer",B)}if(Te(A))throw F3(TypeError,"is a detached ArrayBuffer",B);return A};var Ji6=Object.getOwnPropertyDescriptor(DataView.prototype,"byteLength").get;QVB.DataView=(A,B={})=>{try{Ji6.call(A)}catch(Q){throw F3(TypeError,"is not a DataView",B)}if(!B.allowShared&&Oe(A.buffer))throw F3(TypeError,"is backed by a SharedArrayBuffer, which is not allowed",B);if(Te(A.buffer))throw F3(TypeError,"is backed by a detached ArrayBuffer",B);return A};var Xi6=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(Uint8Array).prototype,Symbol.toStringTag).get;[Int8Array,Int16Array,Int32Array,Uint8Array,Uint16Array,Uint32Array,Uint8ClampedArray,Float32Array,Float64Array].forEach((A)=>{let{name:B}=A,Q=/^[AEIOU]/u.test(B)?"an":"a";QVB[B]=(D,Z={})=>{if(!ArrayBuffer.isView(D)||Xi6.call(D)!==B)throw F3(TypeError,`is not ${Q} ${B} object`,Z);if(!Z.allowShared&&Oe(D.buffer))throw F3(TypeError,"is a view on a SharedArrayBuffer, which is not allowed",Z);if(Te(D.buffer))throw F3(TypeError,"is a view on a detached ArrayBuffer",Z);return D}});QVB.ArrayBufferView=(A,B={})=>{if(!ArrayBuffer.isView(A))throw F3(TypeError,"is not a view on an ArrayBuffer or SharedArrayBuffer",B);if(!B.allowShared&&Oe(A.buffer))throw F3(TypeError,"is a view on a SharedArrayBuffer, which is not allowed",B);if(Te(A.buffer))throw F3(TypeError,"is a view on a detached ArrayBuffer",B);return A};QVB.BufferSource=(A,B={})=>{if(ArrayBuffer.isView(A)){if(!B.allowShared&&Oe(A.buffer))throw F3(TypeError,"is a view on a SharedArrayBuffer, which is not allowed",B);if(Te(A.buffer))throw F3(TypeError,"is a view on a detached ArrayBuffer",B);return A}if(!B.allowShared&&!Kz0(A))throw F3(TypeError,"is not an ArrayBuffer or a view on one",B);if(B.allowShared&&!Oe(A)&&!Kz0(A))throw F3(TypeError,"is not an ArrayBuffer, SharedArrayBuffer, or a view on one",B);if(Te(A))throw F3(TypeError,"is a detached ArrayBuffer",B);return A};QVB.DOMTimeStamp=QVB["unsigned long long"]});
var sz0=E((RKB)=>{Object.defineProperty(RKB,"__esModule",{value:!0});RKB.GCE_LINUX_BIOS_PATHS=void 0;RKB.isGoogleCloudServerless=qKB;RKB.isGoogleComputeEngineLinux=NKB;RKB.isGoogleComputeEngineMACAddress=LKB;RKB.isGoogleComputeEngine=MKB;RKB.detectGCPResidency=Sr6;var wKB=J1("fs"),$KB=J1("os");RKB.GCE_LINUX_BIOS_PATHS={BIOS_DATE:"/sys/class/dmi/id/bios_date",BIOS_VENDOR:"/sys/class/dmi/id/bios_vendor"};var Pr6=/^42:01/;function qKB(){return!!(process.env.CLOUD_RUN_JOB||process.env.FUNCTION_NAME||process.env.K_SERVICE)}function NKB(){if($KB.platform()!=="linux")return!1;try{wKB.statSync(RKB.GCE_LINUX_BIOS_PATHS.BIOS_DATE);let A=wKB.readFileSync(RKB.GCE_LINUX_BIOS_PATHS.BIOS_VENDOR,"utf8");return/Google/.test(A)}catch(A){return!1}}function LKB(){let A=$KB.networkInterfaces();for(let B of Object.values(A)){if(!B)continue;for(let{mac:Q}of B)if(Pr6.test(Q))return!0}return!1}function MKB(){return NKB()||LKB()}function Sr6(){return qKB()||MKB()}});
var tE0=E((se)=>{var _e6=se&&se.__classPrivateFieldGet||function(A,B,Q,D){if(Q==="a"&&!D)throw new TypeError("Private accessor was defined without a getter");if(typeof B==="function"?A!==B||!D:!B.has(A))throw new TypeError("Cannot read private member from an object whose class did not declare it");return Q==="m"?D:Q==="a"?D.call(A):D?D.value:B.get(A)},k_1,FEB;Object.defineProperty(se,"__esModule",{value:!0});se.AwsClient=void 0;var xe6=sE0(),ve6=ix(),be6=ZEB(),GEB=lx();class DZ1 extends ve6.BaseExternalAccountClient{constructor(A,B){super(A,B);let Q=GEB.originalOrCamelOptions(A),D=Q.get("credential_source"),Z=Q.get("aws_security_credentials_supplier");if(!D&&!Z)throw new Error("A credential source or AWS security credentials supplier must be specified.");if(D&&Z)throw new Error("Only one of credential source or AWS security credentials supplier can be specified.");if(Z)this.awsSecurityCredentialsSupplier=Z,this.regionalCredVerificationUrl=_e6(k_1,k_1,"f",FEB),this.credentialSourceType="programmatic";else{let G=GEB.originalOrCamelOptions(D);this.environmentId=G.get("environment_id");let F=G.get("region_url"),I=G.get("url"),Y=G.get("imdsv2_session_token_url");this.awsSecurityCredentialsSupplier=new be6.DefaultAwsSecurityCredentialsSupplier({regionUrl:F,securityCredentialsUrl:I,imdsV2SessionTokenUrl:Y}),this.regionalCredVerificationUrl=G.get("regional_cred_verification_url"),this.credentialSourceType="aws",this.validateEnvironmentId()}this.awsRequestSigner=null,this.region=""}validateEnvironmentId(){var A;let B=(A=this.environmentId)===null||A===void 0?void 0:A.match(/^(aws)(\d+)$/);if(!B||!this.regionalCredVerificationUrl)throw new Error('No valid AWS "credential_source" provided');else if(parseInt(B[2],10)!==1)throw new Error(`aws version "${B[2]}" is not supported in the current build.`)}async retrieveSubjectToken(){if(!this.awsRequestSigner)this.region=await this.awsSecurityCredentialsSupplier.getAwsRegion(this.supplierContext),this.awsRequestSigner=new xe6.AwsRequestSigner(async()=>{return this.awsSecurityCredentialsSupplier.getAwsSecurityCredentials(this.supplierContext)},this.region);let A=await this.awsRequestSigner.getRequestOptions({...k_1.RETRY_CONFIG,url:this.regionalCredVerificationUrl.replace("{region}",this.region),method:"POST"}),B=[],Q=Object.assign({"x-goog-cloud-target-resource":this.audience},A.headers);for(let D in Q)B.push({key:D,value:Q[D]});return encodeURIComponent(JSON.stringify({url:A.url,method:A.method,headers:B}))}}se.AwsClient=DZ1;k_1=DZ1;FEB={value:"https://sts.{region}.amazonaws.com?Action=GetCallerIdentity&Version=2011-06-15"};DZ1.AWS_EC2_METADATA_IPV4_ADDRESS="***************";DZ1.AWS_EC2_METADATA_IPV6_ADDRESS="fd00:ec2::254"});
var tHB=E((Gt5,oHB)=>{var Rt6=ue().Buffer,nHB=UE0(),Ot6=NE0(),Tt6=J1("stream"),aHB=LE0(),ME0=J1("util");function sHB(A,B){return Rt6.from(A,B).toString("base64").replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function Pt6(A,B,Q){Q=Q||"utf8";var D=sHB(aHB(A),"binary"),Z=sHB(aHB(B),Q);return ME0.format("%s.%s",D,Z)}function rHB(A){var{header:B,payload:Q}=A,D=A.secret||A.privateKey,Z=A.encoding,G=Ot6(B.alg),F=Pt6(B,Q,Z),I=G.sign(F,D);return ME0.format("%s.%s",F,I)}function R_1(A){var B=A.secret||A.privateKey||A.key,Q=new nHB(B);this.readable=!0,this.header=A.header,this.encoding=A.encoding,this.secret=this.privateKey=this.key=Q,this.payload=new nHB(A.payload),this.secret.once("close",function(){if(!this.payload.writable&&this.readable)this.sign()}.bind(this)),this.payload.once("close",function(){if(!this.secret.writable&&this.readable)this.sign()}.bind(this))}ME0.inherits(R_1,Tt6);R_1.prototype.sign=function A(){try{var B=rHB({header:this.header,payload:this.payload.buffer,secret:this.secret.buffer,encoding:this.encoding});return this.emit("done",B),this.emit("data",B),this.emit("end"),this.readable=!1,B}catch(Q){this.readable=!1,this.emit("error",Q),this.emit("close")}};R_1.sign=rHB;oHB.exports=R_1});
var tl0=E((ol0,NX1)=>{ol0.formatArgs=IM9;ol0.save=YM9;ol0.load=WM9;ol0.useColors=FM9;ol0.storage=JM9();ol0.destroy=(()=>{let A=!1;return()=>{if(!A)A=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}})();ol0.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function FM9(){if(typeof window!=="undefined"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator!=="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let A;return typeof document!=="undefined"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window!=="undefined"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator!=="undefined"&&navigator.userAgent&&(A=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(A[1],10)>=31||typeof navigator!=="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function IM9(A){if(A[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+A[0]+(this.useColors?"%c ":" ")+"+"+NX1.exports.humanize(this.diff),!this.useColors)return;let B="color: "+this.color;A.splice(1,0,B,"color: inherit");let Q=0,D=0;A[0].replace(/%[a-zA-Z%]/g,(Z)=>{if(Z==="%%")return;if(Q++,Z==="%c")D=Q}),A.splice(D,0,B)}ol0.log=console.debug||console.log||(()=>{});function YM9(A){try{if(A)ol0.storage.setItem("debug",A);else ol0.storage.removeItem("debug")}catch(B){}}function WM9(){let A;try{A=ol0.storage.getItem("debug")}catch(B){}if(!A&&typeof process!=="undefined"&&"env"in process)A=process.env.DEBUG;return A}function JM9(){try{return localStorage}catch(A){}}NX1.exports=mc1()(ol0);var{formatters:XM9}=NX1.exports;XM9.j=function(A){try{return JSON.stringify(A)}catch(B){return"[UnexpectedJSONParseError]: "+B.message}}});
var ty1=E((Co5,yVB)=>{var gn6=new TextEncoder,un6=new TextDecoder("utf-8",{ignoreBOM:!0});function mn6(A){return gn6.encode(A)}function dn6(A){return un6.decode(A)}yVB.exports={utf8Encode:mn6,utf8DecodeWithoutBOM:dn6}});
var ue=E((FE0,BHB)=>{/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */var U_1=J1("buffer"),TM=U_1.Buffer;function AHB(A,B){for(var Q in A)B[Q]=A[Q]}if(TM.from&&TM.alloc&&TM.allocUnsafe&&TM.allocUnsafeSlow)BHB.exports=U_1;else AHB(U_1,FE0),FE0.Buffer=wm;function wm(A,B,Q){return TM(A,B,Q)}wm.prototype=Object.create(TM.prototype);AHB(TM,wm);wm.from=function(A,B,Q){if(typeof A==="number")throw new TypeError("Argument must not be a number");return TM(A,B,Q)};wm.alloc=function(A,B,Q){if(typeof A!=="number")throw new TypeError("Argument must be a number");var D=TM(A);if(B!==void 0)if(typeof Q==="string")D.fill(B,Q);else D.fill(B);else D.fill(0);return D};wm.allocUnsafe=function(A){if(typeof A!=="number")throw new TypeError("Argument must be a number");return TM(A)};wm.allocUnsafeSlow=function(A){if(typeof A!=="number")throw new TypeError("Argument must be a number");return U_1.SlowBuffer(A)}});
var uqA=E((hqA)=>{Object.defineProperty(hqA,"__esModule",{value:!0});hqA.default=void 0;var Y$Q=W$Q(J1("crypto"));function W$Q(A){return A&&A.__esModule?A:{default:A}}function J$Q(A){if(Array.isArray(A))A=Buffer.from(A);else if(typeof A==="string")A=Buffer.from(A,"utf8");return Y$Q.default.createHash("sha1").update(A).digest()}var X$Q=J$Q;hqA.default=X$Q});
var vCB=E((J$,xCB)=>{Object.defineProperty(J$,"__esModule",{value:!0});function ve(A){return A&&typeof A==="object"&&"default"in A?A.default:A}var W$=ve(J1("stream")),TCB=ve(J1("http")),I_1=ve(J1("url")),PCB=ve(LCB()),bs6=ve(J1("https")),Hm=ve(J1("zlib")),fs6=W$.Readable,hP=Symbol("buffer"),xz0=Symbol("type");class _e{constructor(){this[xz0]="";let A=arguments[0],B=arguments[1],Q=[],D=0;if(A){let G=A,F=Number(G.length);for(let I=0;I<F;I++){let Y=G[I],W;if(Y instanceof Buffer)W=Y;else if(ArrayBuffer.isView(Y))W=Buffer.from(Y.buffer,Y.byteOffset,Y.byteLength);else if(Y instanceof ArrayBuffer)W=Buffer.from(Y);else if(Y instanceof _e)W=Y[hP];else W=Buffer.from(typeof Y==="string"?Y:String(Y));D+=W.length,Q.push(W)}}this[hP]=Buffer.concat(Q);let Z=B&&B.type!==void 0&&String(B.type).toLowerCase();if(Z&&!/[^\u0020-\u007E]/.test(Z))this[xz0]=Z}get size(){return this[hP].length}get type(){return this[xz0]}text(){return Promise.resolve(this[hP].toString())}arrayBuffer(){let A=this[hP],B=A.buffer.slice(A.byteOffset,A.byteOffset+A.byteLength);return Promise.resolve(B)}stream(){let A=new fs6;return A._read=function(){},A.push(this[hP]),A.push(null),A}toString(){return"[object Blob]"}slice(){let A=this.size,B=arguments[0],Q=arguments[1],D,Z;if(B===void 0)D=0;else if(B<0)D=Math.max(A+B,0);else D=Math.min(B,A);if(Q===void 0)Z=A;else if(Q<0)Z=Math.max(A+Q,0);else Z=Math.min(Q,A);let G=Math.max(Z-D,0),I=this[hP].slice(D,D+G),Y=new _e([],{type:arguments[2]});return Y[hP]=I,Y}}Object.defineProperties(_e.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}});Object.defineProperty(_e.prototype,Symbol.toStringTag,{value:"Blob",writable:!1,enumerable:!1,configurable:!0});function lY(A,B,Q){if(Error.call(this,A),this.message=A,this.type=B,Q)this.code=this.errno=Q.code;Error.captureStackTrace(this,this.constructor)}lY.prototype=Object.create(Error.prototype);lY.prototype.constructor=lY;lY.prototype.name="FetchError";var hz0;try{hz0=(()=>{throw new Error("Cannot require module "+"encoding");})().convert}catch(A){}var uP=Symbol("Body internals"),MCB=W$.PassThrough;function wI(A){var B=this,Q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},D=Q.size;let Z=D===void 0?0:D;var G=Q.timeout;let F=G===void 0?0:G;if(A==null)A=null;else if(SCB(A))A=Buffer.from(A.toString());else if(cD1(A));else if(Buffer.isBuffer(A));else if(Object.prototype.toString.call(A)==="[object ArrayBuffer]")A=Buffer.from(A);else if(ArrayBuffer.isView(A))A=Buffer.from(A.buffer,A.byteOffset,A.byteLength);else if(A instanceof W$);else A=Buffer.from(String(A));if(this[uP]={body:A,disturbed:!1,error:null},this.size=Z,this.timeout=F,A instanceof W$)A.on("error",function(I){let Y=I.name==="AbortError"?I:new lY(`Invalid response body while trying to fetch ${B.url}: ${I.message}`,"system",I);B[uP].error=Y})}wI.prototype={get body(){return this[uP].body},get bodyUsed(){return this[uP].disturbed},arrayBuffer(){return ke.call(this).then(function(A){return A.buffer.slice(A.byteOffset,A.byteOffset+A.byteLength)})},blob(){let A=this.headers&&this.headers.get("content-type")||"";return ke.call(this).then(function(B){return Object.assign(new _e([],{type:A.toLowerCase()}),{[hP]:B})})},json(){var A=this;return ke.call(this).then(function(B){try{return JSON.parse(B.toString())}catch(Q){return wI.Promise.reject(new lY(`invalid json response body at ${A.url} reason: ${Q.message}`,"invalid-json"))}})},text(){return ke.call(this).then(function(A){return A.toString()})},buffer(){return ke.call(this)},textConverted(){var A=this;return ke.call(this).then(function(B){return hs6(B,A.headers)})}};Object.defineProperties(wI.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0}});wI.mixIn=function(A){for(let B of Object.getOwnPropertyNames(wI.prototype))if(!(B in A)){let Q=Object.getOwnPropertyDescriptor(wI.prototype,B);Object.defineProperty(A,B,Q)}};function ke(){var A=this;if(this[uP].disturbed)return wI.Promise.reject(new TypeError(`body used already for: ${this.url}`));if(this[uP].disturbed=!0,this[uP].error)return wI.Promise.reject(this[uP].error);let B=this.body;if(B===null)return wI.Promise.resolve(Buffer.alloc(0));if(cD1(B))B=B.stream();if(Buffer.isBuffer(B))return wI.Promise.resolve(B);if(!(B instanceof W$))return wI.Promise.resolve(Buffer.alloc(0));let Q=[],D=0,Z=!1;return new wI.Promise(function(G,F){let I;if(A.timeout)I=setTimeout(function(){Z=!0,F(new lY(`Response timeout while trying to fetch ${A.url} (over ${A.timeout}ms)`,"body-timeout"))},A.timeout);B.on("error",function(Y){if(Y.name==="AbortError")Z=!0,F(Y);else F(new lY(`Invalid response body while trying to fetch ${A.url}: ${Y.message}`,"system",Y))}),B.on("data",function(Y){if(Z||Y===null)return;if(A.size&&D+Y.length>A.size){Z=!0,F(new lY(`content size at ${A.url} over limit: ${A.size}`,"max-size"));return}D+=Y.length,Q.push(Y)}),B.on("end",function(){if(Z)return;clearTimeout(I);try{G(Buffer.concat(Q,D))}catch(Y){F(new lY(`Could not create Buffer from response body for ${A.url}: ${Y.message}`,"system",Y))}})})}function hs6(A,B){if(typeof hz0!=="function")throw new Error("The package `encoding` must be installed to use the textConverted() function");let Q=B.get("content-type"),D="utf-8",Z,G;if(Q)Z=/charset=([^;]*)/i.exec(Q);if(G=A.slice(0,1024).toString(),!Z&&G)Z=/<meta.+?charset=(['"])(.+?)\1/i.exec(G);if(!Z&&G){if(Z=/<meta[\s]+?http-equiv=(['"])content-type\1[\s]+?content=(['"])(.+?)\2/i.exec(G),!Z){if(Z=/<meta[\s]+?content=(['"])(.+?)\1[\s]+?http-equiv=(['"])content-type\3/i.exec(G),Z)Z.pop()}if(Z)Z=/charset=(.*)/i.exec(Z.pop())}if(!Z&&G)Z=/<\?xml.+?encoding=(['"])(.+?)\1/i.exec(G);if(Z){if(D=Z.pop(),D==="gb2312"||D==="gbk")D="gb18030"}return hz0(A,"UTF-8",D).toString()}function SCB(A){if(typeof A!=="object"||typeof A.append!=="function"||typeof A.delete!=="function"||typeof A.get!=="function"||typeof A.getAll!=="function"||typeof A.has!=="function"||typeof A.set!=="function")return!1;return A.constructor.name==="URLSearchParams"||Object.prototype.toString.call(A)==="[object URLSearchParams]"||typeof A.sort==="function"}function cD1(A){return typeof A==="object"&&typeof A.arrayBuffer==="function"&&typeof A.type==="string"&&typeof A.stream==="function"&&typeof A.constructor==="function"&&typeof A.constructor.name==="string"&&/^(Blob|File)$/.test(A.constructor.name)&&/^(Blob|File)$/.test(A[Symbol.toStringTag])}function jCB(A){let B,Q,D=A.body;if(A.bodyUsed)throw new Error("cannot clone body after it is used");if(D instanceof W$&&typeof D.getBoundary!=="function")B=new MCB,Q=new MCB,D.pipe(B),D.pipe(Q),A[uP].body=B,D=Q;return D}function kCB(A){if(A===null)return null;else if(typeof A==="string")return"text/plain;charset=UTF-8";else if(SCB(A))return"application/x-www-form-urlencoded;charset=UTF-8";else if(cD1(A))return A.type||null;else if(Buffer.isBuffer(A))return null;else if(Object.prototype.toString.call(A)==="[object ArrayBuffer]")return null;else if(ArrayBuffer.isView(A))return null;else if(typeof A.getBoundary==="function")return`multipart/form-data;boundary=${A.getBoundary()}`;else if(A instanceof W$)return null;else return"text/plain;charset=UTF-8"}function yCB(A){let B=A.body;if(B===null)return 0;else if(cD1(B))return B.size;else if(Buffer.isBuffer(B))return B.length;else if(B&&typeof B.getLengthSync==="function"){if(B._lengthRetrievers&&B._lengthRetrievers.length==0||B.hasKnownLength&&B.hasKnownLength())return B.getLengthSync();return null}else return null}function gs6(A,B){let Q=B.body;if(Q===null)A.end();else if(cD1(Q))Q.stream().pipe(A);else if(Buffer.isBuffer(Q))A.write(Q),A.end();else Q.pipe(A)}wI.Promise=global.Promise;var _CB=/[^\^_`a-zA-Z\-0-9!#$%&'*+.|~]/,gz0=/[^\t\x20-\x7e\x80-\xff]/;function mD1(A){if(A=`${A}`,_CB.test(A)||A==="")throw new TypeError(`${A} is not a legal HTTP header name`)}function RCB(A){if(A=`${A}`,gz0.test(A))throw new TypeError(`${A} is not a legal HTTP header value`)}function ye(A,B){B=B.toLowerCase();for(let Q in A)if(Q.toLowerCase()===B)return Q;return}var DG=Symbol("map");class xE{constructor(){let A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:void 0;if(this[DG]=Object.create(null),A instanceof xE){let B=A.raw(),Q=Object.keys(B);for(let D of Q)for(let Z of B[D])this.append(D,Z);return}if(A==null);else if(typeof A==="object"){let B=A[Symbol.iterator];if(B!=null){if(typeof B!=="function")throw new TypeError("Header pairs must be iterable");let Q=[];for(let D of A){if(typeof D!=="object"||typeof D[Symbol.iterator]!=="function")throw new TypeError("Each header pair must be iterable");Q.push(Array.from(D))}for(let D of Q){if(D.length!==2)throw new TypeError("Each header pair must be a name/value tuple");this.append(D[0],D[1])}}else for(let Q of Object.keys(A)){let D=A[Q];this.append(Q,D)}}else throw new TypeError("Provided initializer must be an object")}get(A){A=`${A}`,mD1(A);let B=ye(this[DG],A);if(B===void 0)return null;return this[DG][B].join(", ")}forEach(A){let B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:void 0,Q=uz0(this),D=0;while(D<Q.length){var Z=Q[D];let G=Z[0],F=Z[1];A.call(B,F,G,this),Q=uz0(this),D++}}set(A,B){A=`${A}`,B=`${B}`,mD1(A),RCB(B);let Q=ye(this[DG],A);this[DG][Q!==void 0?Q:A]=[B]}append(A,B){A=`${A}`,B=`${B}`,mD1(A),RCB(B);let Q=ye(this[DG],A);if(Q!==void 0)this[DG][Q].push(B);else this[DG][A]=[B]}has(A){return A=`${A}`,mD1(A),ye(this[DG],A)!==void 0}delete(A){A=`${A}`,mD1(A);let B=ye(this[DG],A);if(B!==void 0)delete this[DG][B]}raw(){return this[DG]}keys(){return vz0(this,"key")}values(){return vz0(this,"value")}[Symbol.iterator](){return vz0(this,"key+value")}}xE.prototype.entries=xE.prototype[Symbol.iterator];Object.defineProperty(xE.prototype,Symbol.toStringTag,{value:"Headers",writable:!1,enumerable:!1,configurable:!0});Object.defineProperties(xE.prototype,{get:{enumerable:!0},forEach:{enumerable:!0},set:{enumerable:!0},append:{enumerable:!0},has:{enumerable:!0},delete:{enumerable:!0},keys:{enumerable:!0},values:{enumerable:!0},entries:{enumerable:!0}});function uz0(A){let B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"key+value";return Object.keys(A[DG]).sort().map(B==="key"?function(D){return D.toLowerCase()}:B==="value"?function(D){return A[DG][D].join(", ")}:function(D){return[D.toLowerCase(),A[DG][D].join(", ")]})}var mz0=Symbol("internal");function vz0(A,B){let Q=Object.create(dz0);return Q[mz0]={target:A,kind:B,index:0},Q}var dz0=Object.setPrototypeOf({next(){if(!this||Object.getPrototypeOf(this)!==dz0)throw new TypeError("Value of `this` is not a HeadersIterator");var A=this[mz0];let{target:B,kind:Q,index:D}=A,Z=uz0(B,Q),G=Z.length;if(D>=G)return{value:void 0,done:!0};return this[mz0].index=D+1,{value:Z[D],done:!1}}},Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]())));Object.defineProperty(dz0,Symbol.toStringTag,{value:"HeadersIterator",writable:!1,enumerable:!1,configurable:!0});function us6(A){let B=Object.assign({__proto__:null},A[DG]),Q=ye(A[DG],"Host");if(Q!==void 0)B[Q]=B[Q][0];return B}function ms6(A){let B=new xE;for(let Q of Object.keys(A)){if(_CB.test(Q))continue;if(Array.isArray(A[Q]))for(let D of A[Q]){if(gz0.test(D))continue;if(B[DG][Q]===void 0)B[DG][Q]=[D];else B[DG][Q].push(D)}else if(!gz0.test(A[Q]))B[DG][Q]=[A[Q]]}return B}var gx=Symbol("Response internals"),ds6=TCB.STATUS_CODES;class _E{constructor(){let A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};wI.call(this,A,B);let Q=B.status||200,D=new xE(B.headers);if(A!=null&&!D.has("Content-Type")){let Z=kCB(A);if(Z)D.append("Content-Type",Z)}this[gx]={url:B.url,status:Q,statusText:B.statusText||ds6[Q],headers:D,counter:B.counter}}get url(){return this[gx].url||""}get status(){return this[gx].status}get ok(){return this[gx].status>=200&&this[gx].status<300}get redirected(){return this[gx].counter>0}get statusText(){return this[gx].statusText}get headers(){return this[gx].headers}clone(){return new _E(jCB(this),{url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected})}}wI.mixIn(_E.prototype);Object.defineProperties(_E.prototype,{url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});Object.defineProperty(_E.prototype,Symbol.toStringTag,{value:"Response",writable:!1,enumerable:!1,configurable:!0});var gP=Symbol("Request internals"),cs6=I_1.URL||PCB.URL,ls6=I_1.parse,ps6=I_1.format;function bz0(A){if(/^[a-zA-Z][a-zA-Z\d+\-.]*:/.exec(A))A=new cs6(A).toString();return ls6(A)}var is6="destroy"in W$.Readable.prototype;function F_1(A){return typeof A==="object"&&typeof A[gP]==="object"}function ns6(A){let B=A&&typeof A==="object"&&Object.getPrototypeOf(A);return!!(B&&B.constructor.name==="AbortSignal")}class mx{constructor(A){let B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Q;if(!F_1(A)){if(A&&A.href)Q=bz0(A.href);else Q=bz0(`${A}`);A={}}else Q=bz0(A.url);let D=B.method||A.method||"GET";if(D=D.toUpperCase(),(B.body!=null||F_1(A)&&A.body!==null)&&(D==="GET"||D==="HEAD"))throw new TypeError("Request with GET/HEAD method cannot have body");let Z=B.body!=null?B.body:F_1(A)&&A.body!==null?jCB(A):null;wI.call(this,Z,{timeout:B.timeout||A.timeout||0,size:B.size||A.size||0});let G=new xE(B.headers||A.headers||{});if(Z!=null&&!G.has("Content-Type")){let I=kCB(Z);if(I)G.append("Content-Type",I)}let F=F_1(A)?A.signal:null;if("signal"in B)F=B.signal;if(F!=null&&!ns6(F))throw new TypeError("Expected signal to be an instanceof AbortSignal");this[gP]={method:D,redirect:B.redirect||A.redirect||"follow",headers:G,parsedURL:Q,signal:F},this.follow=B.follow!==void 0?B.follow:A.follow!==void 0?A.follow:20,this.compress=B.compress!==void 0?B.compress:A.compress!==void 0?A.compress:!0,this.counter=B.counter||A.counter||0,this.agent=B.agent||A.agent}get method(){return this[gP].method}get url(){return ps6(this[gP].parsedURL)}get headers(){return this[gP].headers}get redirect(){return this[gP].redirect}get signal(){return this[gP].signal}clone(){return new mx(this)}}wI.mixIn(mx.prototype);Object.defineProperty(mx.prototype,Symbol.toStringTag,{value:"Request",writable:!1,enumerable:!1,configurable:!0});Object.defineProperties(mx.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0}});function as6(A){let B=A[gP].parsedURL,Q=new xE(A[gP].headers);if(!Q.has("Accept"))Q.set("Accept","*/*");if(!B.protocol||!B.hostname)throw new TypeError("Only absolute URLs are supported");if(!/^https?:$/.test(B.protocol))throw new TypeError("Only HTTP(S) protocols are supported");if(A.signal&&A.body instanceof W$.Readable&&!is6)throw new Error("Cancellation of streamed requests with AbortSignal is not supported in node < 8");let D=null;if(A.body==null&&/^(POST|PUT)$/i.test(A.method))D="0";if(A.body!=null){let G=yCB(A);if(typeof G==="number")D=String(G)}if(D)Q.set("Content-Length",D);if(!Q.has("User-Agent"))Q.set("User-Agent","node-fetch/1.0 (+https://github.com/bitinn/node-fetch)");if(A.compress&&!Q.has("Accept-Encoding"))Q.set("Accept-Encoding","gzip,deflate");let Z=A.agent;if(typeof Z==="function")Z=Z(B);return Object.assign({},B,{method:A.method,headers:us6(Q),agent:Z})}function xe(A){Error.call(this,A),this.type="aborted",this.message=A,Error.captureStackTrace(this,this.constructor)}xe.prototype=Object.create(Error.prototype);xe.prototype.constructor=xe;xe.prototype.name="AbortError";var dD1=I_1.URL||PCB.URL,OCB=W$.PassThrough,ss6=function A(B,Q){let D=new dD1(Q).hostname,Z=new dD1(B).hostname;return D===Z||D[D.length-Z.length-1]==="."&&D.endsWith(Z)},rs6=function A(B,Q){let D=new dD1(Q).protocol,Z=new dD1(B).protocol;return D===Z};function ux(A,B){if(!ux.Promise)throw new Error("native promise missing, set fetch.Promise to your favorite alternative");return wI.Promise=ux.Promise,new ux.Promise(function(Q,D){let Z=new mx(A,B),G=as6(Z),F=(G.protocol==="https:"?bs6:TCB).request,I=Z.signal,Y=null,W=function K(){let H=new xe("The user aborted a request.");if(D(H),Z.body&&Z.body instanceof W$.Readable)fz0(Z.body,H);if(!Y||!Y.body)return;Y.body.emit("error",H)};if(I&&I.aborted){W();return}let J=function K(){W(),C()},X=F(G),V;if(I)I.addEventListener("abort",J);function C(){if(X.abort(),I)I.removeEventListener("abort",J);clearTimeout(V)}if(Z.timeout)X.once("socket",function(K){V=setTimeout(function(){D(new lY(`network timeout at: ${Z.url}`,"request-timeout")),C()},Z.timeout)});if(X.on("error",function(K){if(D(new lY(`request to ${Z.url} failed, reason: ${K.message}`,"system",K)),Y&&Y.body)fz0(Y.body,K);C()}),os6(X,function(K){if(I&&I.aborted)return;if(Y&&Y.body)fz0(Y.body,K)}),parseInt(process.version.substring(1))<14)X.on("socket",function(K){K.addListener("close",function(H){let z=K.listenerCount("data")>0;if(Y&&z&&!H&&!(I&&I.aborted)){let $=new Error("Premature close");$.code="ERR_STREAM_PREMATURE_CLOSE",Y.body.emit("error",$)}})});X.on("response",function(K){clearTimeout(V);let H=ms6(K.headers);if(ux.isRedirect(K.statusCode)){let O=H.get("Location"),R=null;try{R=O===null?null:new dD1(O,Z.url).toString()}catch(T){if(Z.redirect!=="manual"){D(new lY(`uri requested responds with an invalid redirect URL: ${O}`,"invalid-redirect")),C();return}}switch(Z.redirect){case"error":D(new lY(`uri requested responds with a redirect, redirect mode is set to error: ${Z.url}`,"no-redirect")),C();return;case"manual":if(R!==null)try{H.set("Location",R)}catch(j){D(j)}break;case"follow":if(R===null)break;if(Z.counter>=Z.follow){D(new lY(`maximum redirect reached at: ${Z.url}`,"max-redirect")),C();return}let T={headers:new xE(Z.headers),follow:Z.follow,counter:Z.counter+1,agent:Z.agent,compress:Z.compress,method:Z.method,body:Z.body,signal:Z.signal,timeout:Z.timeout,size:Z.size};if(!ss6(Z.url,R)||!rs6(Z.url,R))for(let j of["authorization","www-authenticate","cookie","cookie2"])T.headers.delete(j);if(K.statusCode!==303&&Z.body&&yCB(Z)===null){D(new lY("Cannot follow redirect with body being a readable stream","unsupported-redirect")),C();return}if(K.statusCode===303||(K.statusCode===301||K.statusCode===302)&&Z.method==="POST")T.method="GET",T.body=void 0,T.headers.delete("content-length");Q(ux(new mx(R,T))),C();return}}K.once("end",function(){if(I)I.removeEventListener("abort",J)});let z=K.pipe(new OCB),$={url:Z.url,status:K.statusCode,statusText:K.statusMessage,headers:H,size:Z.size,timeout:Z.timeout,counter:Z.counter},L=H.get("Content-Encoding");if(!Z.compress||Z.method==="HEAD"||L===null||K.statusCode===204||K.statusCode===304){Y=new _E(z,$),Q(Y);return}let N={flush:Hm.Z_SYNC_FLUSH,finishFlush:Hm.Z_SYNC_FLUSH};if(L=="gzip"||L=="x-gzip"){z=z.pipe(Hm.createGunzip(N)),Y=new _E(z,$),Q(Y);return}if(L=="deflate"||L=="x-deflate"){let O=K.pipe(new OCB);O.once("data",function(R){if((R[0]&15)===8)z=z.pipe(Hm.createInflate());else z=z.pipe(Hm.createInflateRaw());Y=new _E(z,$),Q(Y)}),O.on("end",function(){if(!Y)Y=new _E(z,$),Q(Y)});return}if(L=="br"&&typeof Hm.createBrotliDecompress==="function"){z=z.pipe(Hm.createBrotliDecompress()),Y=new _E(z,$),Q(Y);return}Y=new _E(z,$),Q(Y)}),gs6(X,Z)})}function os6(A,B){let Q;A.on("socket",function(D){Q=D}),A.on("response",function(D){let Z=D.headers;if(Z["transfer-encoding"]==="chunked"&&!Z["content-length"])D.once("close",function(G){if(Q&&Q.listenerCount("data")>0&&!G){let I=new Error("Premature close");I.code="ERR_STREAM_PREMATURE_CLOSE",B(I)}})})}function fz0(A,B){if(A.destroy)A.destroy(B);else A.emit("error",B),A.end()}ux.isRedirect=function(A){return A===301||A===302||A===303||A===307||A===308};ux.Promise=global.Promise;xCB.exports=J$=ux;Object.defineProperty(J$,"__esModule",{value:!0});J$.default=J$;J$.Headers=xE;J$.Request=mx;J$.Response=_E;J$.FetchError=lY;J$.AbortError=xe});
var vE0=E((yzB)=>{Object.defineProperty(yzB,"__esModule",{value:!0});yzB.OAuthClientAuthHandler=void 0;yzB.getErrorFromOAuthErrorResponse=We6;var jzB=J1("querystring"),Ie6=he(),Ye6=["PUT","POST","PATCH"];class kzB{constructor(A){this.clientAuthentication=A,this.crypto=Ie6.createCrypto()}applyClientAuthenticationOptions(A,B){if(this.injectAuthenticatedHeaders(A,B),!B)this.injectAuthenticatedRequestBody(A)}injectAuthenticatedHeaders(A,B){var Q;if(B)A.headers=A.headers||{},Object.assign(A.headers,{Authorization:`Bearer ${B}}`});else if(((Q=this.clientAuthentication)===null||Q===void 0?void 0:Q.confidentialClientType)==="basic"){A.headers=A.headers||{};let D=this.clientAuthentication.clientId,Z=this.clientAuthentication.clientSecret||"",G=this.crypto.encodeBase64StringUtf8(`${D}:${Z}`);Object.assign(A.headers,{Authorization:`Basic ${G}`})}}injectAuthenticatedRequestBody(A){var B;if(((B=this.clientAuthentication)===null||B===void 0?void 0:B.confidentialClientType)==="request-body"){let Q=(A.method||"GET").toUpperCase();if(Ye6.indexOf(Q)!==-1){let D,Z=A.headers||{};for(let G in Z)if(G.toLowerCase()==="content-type"&&Z[G]){D=Z[G].toLowerCase();break}if(D==="application/x-www-form-urlencoded"){A.data=A.data||"";let G=jzB.parse(A.data);Object.assign(G,{client_id:this.clientAuthentication.clientId,client_secret:this.clientAuthentication.clientSecret||""}),A.data=jzB.stringify(G)}else if(D==="application/json")A.data=A.data||{},Object.assign(A.data,{client_id:this.clientAuthentication.clientId,client_secret:this.clientAuthentication.clientSecret||""});else throw new Error(`${D} content-types are not supported with ${this.clientAuthentication.confidentialClientType} client authentication`)}else throw new Error(`${Q} HTTP method does not support ${this.clientAuthentication.confidentialClientType} client authentication`)}}static get RETRY_CONFIG(){return{retry:!0,retryConfig:{httpMethodsToRetry:["GET","PUT","POST","HEAD","OPTIONS","DELETE"]}}}}yzB.OAuthClientAuthHandler=kzB;function We6(A,B){let{error:Q,error_description:D,error_uri:Z}=A,G=`Error code ${Q}`;if(typeof D!=="undefined")G+=`: ${D}`;if(typeof Z!=="undefined")G+=` - ${Z}`;let F=new Error(G);if(B){let I=Object.keys(B);if(B.stack)I.push("stack");I.forEach((Y)=>{if(Y!=="message")Object.defineProperty(F,Y,{value:B[Y],writable:!1,enumerable:!0})})}return F}});
var vKB=E((Um)=>{var mr6=Um&&Um.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),dr6=Um&&Um.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))mr6(B,A,Q)};Object.defineProperty(Um,"__esModule",{value:!0});dr6(xKB(),Um)});
var wVB=E((Yo5,UVB)=>{var In6=/^xn--/,Yn6=/[^\0-\x7F]/,Wn6=/[\x2E\u3002\uFF0E\uFF61]/g,Jn6={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},LM=Math.floor,zz0=String.fromCharCode;function bx(A){throw new RangeError(Jn6[A])}function Xn6(A,B){let Q=[],D=A.length;while(D--)Q[D]=B(A[D]);return Q}function CVB(A,B){let Q=A.split("@"),D="";if(Q.length>1)D=Q[0]+"@",A=Q[1];A=A.replace(Wn6,".");let Z=A.split("."),G=Xn6(Z,B).join(".");return D+G}function KVB(A){let B=[],Q=0,D=A.length;while(Q<D){let Z=A.charCodeAt(Q++);if(Z>=55296&&Z<=56319&&Q<D){let G=A.charCodeAt(Q++);if((G&64512)==56320)B.push(((Z&1023)<<10)+(G&1023)+65536);else B.push(Z),Q--}else B.push(Z)}return B}var Vn6=(A)=>String.fromCodePoint(...A),Cn6=function(A){if(A>=48&&A<58)return 26+(A-48);if(A>=65&&A<91)return A-65;if(A>=97&&A<123)return A-97;return 36},VVB=function(A,B){return A+22+75*(A<26)-((B!=0)<<5)},HVB=function(A,B,Q){let D=0;A=Q?LM(A/700):A>>1,A+=LM(A/B);for(;A>455;D+=36)A=LM(A/35);return LM(D+36*A/(A+38))},zVB=function(A){let B=[],Q=A.length,D=0,Z=128,G=72,F=A.lastIndexOf("-");if(F<0)F=0;for(let I=0;I<F;++I){if(A.charCodeAt(I)>=128)bx("not-basic");B.push(A.charCodeAt(I))}for(let I=F>0?F+1:0;I<Q;){let Y=D;for(let J=1,X=36;;X+=36){if(I>=Q)bx("invalid-input");let V=Cn6(A.charCodeAt(I++));if(V>=36)bx("invalid-input");if(V>LM((2147483647-D)/J))bx("overflow");D+=V*J;let C=X<=G?1:X>=G+26?26:X-G;if(V<C)break;let K=36-C;if(J>LM(2147483647/K))bx("overflow");J*=K}let W=B.length+1;if(G=HVB(D-Y,W,Y==0),LM(D/W)>2147483647-Z)bx("overflow");Z+=LM(D/W),D%=W,B.splice(D++,0,Z)}return String.fromCodePoint(...B)},EVB=function(A){let B=[];A=KVB(A);let Q=A.length,D=128,Z=0,G=72;for(let Y of A)if(Y<128)B.push(zz0(Y));let F=B.length,I=F;if(F)B.push("-");while(I<Q){let Y=2147483647;for(let J of A)if(J>=D&&J<Y)Y=J;let W=I+1;if(Y-D>LM((2147483647-Z)/W))bx("overflow");Z+=(Y-D)*W,D=Y;for(let J of A){if(J<D&&++Z>2147483647)bx("overflow");if(J===D){let X=Z;for(let V=36;;V+=36){let C=V<=G?1:V>=G+26?26:V-G;if(X<C)break;let K=X-C,H=36-C;B.push(zz0(VVB(C+K%H,0))),X=LM(K/H)}B.push(zz0(VVB(X,0))),G=HVB(Z,W,I===F),Z=0,++I}}++Z,++D}return B.join("")},Kn6=function(A){return CVB(A,function(B){return In6.test(B)?zVB(B.slice(4).toLowerCase()):B})},Hn6=function(A){return CVB(A,function(B){return Yn6.test(B)?"xn--"+EVB(B):B})},zn6={version:"2.3.1",ucs2:{decode:KVB,encode:Vn6},decode:zVB,encode:EVB,toASCII:Hn6,toUnicode:Kn6};UVB.exports=zn6});
var xE0=E((PzB)=>{Object.defineProperty(PzB,"__esModule",{value:!0});PzB.Impersonated=PzB.IMPERSONATED_ACCOUNT_TYPE=void 0;var TzB=$m(),Ze6=X$(),Ge6=lx();PzB.IMPERSONATED_ACCOUNT_TYPE="impersonated_service_account";class BZ1 extends TzB.OAuth2Client{constructor(A={}){var B,Q,D,Z,G,F;super(A);if(this.credentials={expiry_date:1,refresh_token:"impersonated-placeholder"},this.sourceClient=(B=A.sourceClient)!==null&&B!==void 0?B:new TzB.OAuth2Client,this.targetPrincipal=(Q=A.targetPrincipal)!==null&&Q!==void 0?Q:"",this.delegates=(D=A.delegates)!==null&&D!==void 0?D:[],this.targetScopes=(Z=A.targetScopes)!==null&&Z!==void 0?Z:[],this.lifetime=(G=A.lifetime)!==null&&G!==void 0?G:3600,!Ge6.originalOrCamelOptions(A).get("universe_domain"))this.universeDomain=this.sourceClient.universeDomain;else if(this.sourceClient.universeDomain!==this.universeDomain)throw new RangeError(`Universe domain ${this.sourceClient.universeDomain} in source credentials does not match ${this.universeDomain} universe domain set for impersonated credentials.`);this.endpoint=(F=A.endpoint)!==null&&F!==void 0?F:`https://iamcredentials.${this.universeDomain}`}async sign(A){await this.sourceClient.getAccessToken();let B=`projects/-/serviceAccounts/${this.targetPrincipal}`,Q=`${this.endpoint}/v1/${B}:signBlob`,D={delegates:this.delegates,payload:Buffer.from(A).toString("base64")};return(await this.sourceClient.request({...BZ1.RETRY_CONFIG,url:Q,data:D,method:"POST"})).data}getTargetPrincipal(){return this.targetPrincipal}async refreshToken(){var A,B,Q,D,Z,G;try{await this.sourceClient.getAccessToken();let F="projects/-/serviceAccounts/"+this.targetPrincipal,I=`${this.endpoint}/v1/${F}:generateAccessToken`,Y={delegates:this.delegates,scope:this.targetScopes,lifetime:this.lifetime+"s"},W=await this.sourceClient.request({...BZ1.RETRY_CONFIG,url:I,data:Y,method:"POST"}),J=W.data;return this.credentials.access_token=J.accessToken,this.credentials.expiry_date=Date.parse(J.expireTime),{tokens:this.credentials,res:W}}catch(F){if(!(F instanceof Error))throw F;let I=0,Y="";if(F instanceof Ze6.GaxiosError)I=(Q=(B=(A=F===null||F===void 0?void 0:F.response)===null||A===void 0?void 0:A.data)===null||B===void 0?void 0:B.error)===null||Q===void 0?void 0:Q.status,Y=(G=(Z=(D=F===null||F===void 0?void 0:F.response)===null||D===void 0?void 0:D.data)===null||Z===void 0?void 0:Z.error)===null||G===void 0?void 0:G.message;if(I&&Y)throw F.message=`${I}: unable to impersonate: ${Y}`,F;else throw F.message=`unable to impersonate: ${F}`,F}}async fetchIdToken(A,B){var Q,D;await this.sourceClient.getAccessToken();let Z=`projects/-/serviceAccounts/${this.targetPrincipal}`,G=`${this.endpoint}/v1/${Z}:generateIdToken`,F={delegates:this.delegates,audience:A,includeEmail:(Q=B===null||B===void 0?void 0:B.includeEmail)!==null&&Q!==void 0?Q:!0,useEmailAzp:(D=B===null||B===void 0?void 0:B.includeEmail)!==null&&D!==void 0?D:!0};return(await this.sourceClient.request({...BZ1.RETRY_CONFIG,url:G,data:F,method:"POST"})).data.token}}PzB.Impersonated=BZ1});
var xKB=E((Y3)=>{var vr6=Y3&&Y3.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),br6=Y3&&Y3.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),SKB=Y3&&Y3.__importStar||function(A){if(A&&A.__esModule)return A;var B={};if(A!=null){for(var Q in A)if(Q!=="default"&&Object.prototype.hasOwnProperty.call(A,Q))vr6(B,A,Q)}return br6(B,A),B};Object.defineProperty(Y3,"__esModule",{value:!0});Y3.env=Y3.DebugLogBackendBase=Y3.placeholder=Y3.AdhocDebugLogger=Y3.LogSeverity=void 0;Y3.getNodeBackend=rz0;Y3.getDebugBackend=hr6;Y3.getStructuredBackend=gr6;Y3.setBackend=ur6;Y3.log=_KB;var fr6=J1("node:events"),lD1=SKB(J1("node:process")),jKB=SKB(J1("node:util")),fK=PKB(),V$;(function(A){A.DEFAULT="DEFAULT",A.DEBUG="DEBUG",A.INFO="INFO",A.WARNING="WARNING",A.ERROR="ERROR"})(V$||(Y3.LogSeverity=V$={}));class H_1 extends fr6.EventEmitter{constructor(A,B){super();this.namespace=A,this.upstream=B,this.func=Object.assign(this.invoke.bind(this),{instance:this,on:(Q,D)=>this.on(Q,D)}),this.func.debug=(...Q)=>this.invokeSeverity(V$.DEBUG,...Q),this.func.info=(...Q)=>this.invokeSeverity(V$.INFO,...Q),this.func.warn=(...Q)=>this.invokeSeverity(V$.WARNING,...Q),this.func.error=(...Q)=>this.invokeSeverity(V$.ERROR,...Q),this.func.sublog=(Q)=>_KB(Q,this.func)}invoke(A,...B){if(this.upstream)this.upstream(A,...B);this.emit("log",A,B)}invokeSeverity(A,...B){this.invoke({severity:A},...B)}}Y3.AdhocDebugLogger=H_1;Y3.placeholder=new H_1("",()=>{}).func;class pD1{constructor(){var A;this.cached=new Map,this.filters=[],this.filtersSet=!1;let B=(A=lD1.env[Y3.env.nodeEnables])!==null&&A!==void 0?A:"*";if(B==="all")B="*";this.filters=B.split(",")}log(A,B,...Q){try{if(!this.filtersSet)this.setFilters(),this.filtersSet=!0;let D=this.cached.get(A);if(!D)D=this.makeLogger(A),this.cached.set(A,D);D(B,...Q)}catch(D){console.error(D)}}}Y3.DebugLogBackendBase=pD1;class tz0 extends pD1{constructor(){super(...arguments);this.enabledRegexp=/.*/g}isEnabled(A){return this.enabledRegexp.test(A)}makeLogger(A){if(!this.enabledRegexp.test(A))return()=>{};return(B,...Q)=>{var D;let Z=`${fK.Colours.green}${A}${fK.Colours.reset}`,G=`${fK.Colours.yellow}${lD1.pid}${fK.Colours.reset}`,F;switch(B.severity){case V$.ERROR:F=`${fK.Colours.red}${B.severity}${fK.Colours.reset}`;break;case V$.INFO:F=`${fK.Colours.magenta}${B.severity}${fK.Colours.reset}`;break;case V$.WARNING:F=`${fK.Colours.yellow}${B.severity}${fK.Colours.reset}`;break;default:F=(D=B.severity)!==null&&D!==void 0?D:V$.DEFAULT;break}let I=jKB.formatWithOptions({colors:fK.Colours.enabled},...Q),Y=Object.assign({},B);delete Y.severity;let W=Object.getOwnPropertyNames(Y).length?JSON.stringify(Y):"",J=W?`${fK.Colours.grey}${W}${fK.Colours.reset}`:"";console.error("%s [%s|%s] %s%s",G,Z,F,I,W?` ${J}`:"")}}setFilters(){let B=this.filters.join(",").replace(/[|\\{}()[\]^$+?.]/g,"\\$&").replace(/\*/g,".*").replace(/,/g,"$|^");this.enabledRegexp=new RegExp(`^${B}$`,"i")}}function rz0(){return new tz0}class kKB extends pD1{constructor(A){super();this.debugPkg=A}makeLogger(A){let B=this.debugPkg(A);return(Q,...D)=>{B(D[0],...D.slice(1))}}setFilters(){var A;let B=(A=lD1.env.NODE_DEBUG)!==null&&A!==void 0?A:"";lD1.env.NODE_DEBUG=`${B}${B?",":""}${this.filters.join(",")}`}}function hr6(A){return new kKB(A)}class yKB extends pD1{constructor(A){var B;super();this.upstream=(B=A)!==null&&B!==void 0?B:new tz0}makeLogger(A){let B=this.upstream.makeLogger(A);return(Q,...D)=>{var Z;let G=(Z=Q.severity)!==null&&Z!==void 0?Z:V$.INFO,F=Object.assign({severity:G,message:jKB.format(...D)},Q),I=JSON.stringify(F);B(Q,I)}}setFilters(){this.upstream.setFilters()}}function gr6(A){return new yKB(A)}Y3.env={nodeEnables:"GOOGLE_SDK_NODE_LOGGING"};var oz0=new Map,bE=void 0;function ur6(A){bE=A,oz0.clear()}function _KB(A,B){if(!lD1.env[Y3.env.nodeEnables])return Y3.placeholder;if(!A)return Y3.placeholder;if(B)A=`${B.instance.namespace}:${A}`;let D=oz0.get(A);if(D)return D.func;if(bE===null)return Y3.placeholder;else if(bE===void 0)bE=rz0();let Z=(()=>{let G=void 0;return new H_1(A,(I,...Y)=>{if(G!==bE){if(bE===null)return;else if(bE===void 0)bE=rz0();G=bE}bE===null||bE===void 0||bE.log(A,I,...Y)})})();return oz0.set(A,Z),Z.func}});
var x_1=E((UEB)=>{Object.defineProperty(UEB,"__esModule",{value:!0});UEB.PluggableAuthClient=UEB.ExecutableError=void 0;var ne6=ix(),ae6=FU0(),se6=CEB();class WU0 extends Error{constructor(A,B){super(`The executable failed with exit code: ${B} and error message: ${A}.`);this.code=B,Object.setPrototypeOf(this,new.target.prototype)}}UEB.ExecutableError=WU0;var re6=30000,KEB=5000,HEB=120000,oe6="GOOGLE_EXTERNAL_ACCOUNT_ALLOW_EXECUTABLES",zEB=1;class EEB extends ne6.BaseExternalAccountClient{constructor(A,B){super(A,B);if(!A.credential_source.executable)throw new Error('No valid Pluggable Auth "credential_source" provided.');if(this.command=A.credential_source.executable.command,!this.command)throw new Error('No valid Pluggable Auth "credential_source" provided.');if(A.credential_source.executable.timeout_millis===void 0)this.timeoutMillis=re6;else if(this.timeoutMillis=A.credential_source.executable.timeout_millis,this.timeoutMillis<KEB||this.timeoutMillis>HEB)throw new Error(`Timeout must be between ${KEB} and ${HEB} milliseconds.`);this.outputFile=A.credential_source.executable.output_file,this.handler=new se6.PluggableAuthHandler({command:this.command,timeoutMillis:this.timeoutMillis,outputFile:this.outputFile}),this.credentialSourceType="executable"}async retrieveSubjectToken(){if(process.env[oe6]!=="1")throw new Error("Pluggable Auth executables need to be explicitly allowed to run by setting the GOOGLE_EXTERNAL_ACCOUNT_ALLOW_EXECUTABLES environment Variable to 1.");let A=void 0;if(this.outputFile)A=await this.handler.retrieveCachedResponse();if(!A){let B=new Map;if(B.set("GOOGLE_EXTERNAL_ACCOUNT_AUDIENCE",this.audience),B.set("GOOGLE_EXTERNAL_ACCOUNT_TOKEN_TYPE",this.subjectTokenType),B.set("GOOGLE_EXTERNAL_ACCOUNT_INTERACTIVE","0"),this.outputFile)B.set("GOOGLE_EXTERNAL_ACCOUNT_OUTPUT_FILE",this.outputFile);let Q=this.getServiceAccountEmail();if(Q)B.set("GOOGLE_EXTERNAL_ACCOUNT_IMPERSONATED_EMAIL",Q);A=await this.handler.retrieveResponseFromExecutable(B)}if(A.version>zEB)throw new Error(`Version of executable is not currently supported, maximum supported version is ${zEB}.`);if(!A.success)throw new WU0(A.errorMessage,A.errorCode);if(this.outputFile){if(!A.expirationTime)throw new ae6.InvalidExpirationTimeFieldError("The executable response must contain the `expiration_time` field for successful responses when an output_file has been specified in the configuration.")}if(A.isExpired())throw new Error("Executable response is expired.");return A.subjectToken}}UEB.PluggableAuthClient=EEB});
var yE0=E((LzB)=>{Object.defineProperty(LzB,"__esModule",{value:!0});LzB.JWT=void 0;var NzB=UzB(),et6=jE0(),Ae6=$m(),T_1=PM();class kE0 extends Ae6.OAuth2Client{constructor(A,B,Q,D,Z,G){let F=A&&typeof A==="object"?A:{email:A,keyFile:B,key:Q,keyId:G,scopes:D,subject:Z};super(F);this.email=F.email,this.keyFile=F.keyFile,this.key=F.key,this.keyId=F.keyId,this.scopes=F.scopes,this.subject=F.subject,this.additionalClaims=F.additionalClaims,this.credentials={refresh_token:"jwt-placeholder",expiry_date:1}}createScoped(A){let B=new kE0(this);return B.scopes=A,B}async getRequestMetadataAsync(A){A=this.defaultServicePath?`https://${this.defaultServicePath}/`:A;let B=!this.hasUserScopes()&&A||this.useJWTAccessWithScope&&this.hasAnyScopes()||this.universeDomain!==T_1.DEFAULT_UNIVERSE;if(this.subject&&this.universeDomain!==T_1.DEFAULT_UNIVERSE)throw new RangeError(`Service Account user is configured for the credential. Domain-wide delegation is not supported in universes other than ${T_1.DEFAULT_UNIVERSE}`);if(!this.apiKey&&B)if(this.additionalClaims&&this.additionalClaims.target_audience){let{tokens:Q}=await this.refreshToken();return{headers:this.addSharedMetadataHeaders({Authorization:`Bearer ${Q.id_token}`})}}else{if(!this.access)this.access=new et6.JWTAccess(this.email,this.key,this.keyId,this.eagerRefreshThresholdMillis);let Q;if(this.hasUserScopes())Q=this.scopes;else if(!A)Q=this.defaultScopes;let D=this.useJWTAccessWithScope||this.universeDomain!==T_1.DEFAULT_UNIVERSE,Z=await this.access.getRequestHeaders(A!==null&&A!==void 0?A:void 0,this.additionalClaims,D?Q:void 0);return{headers:this.addSharedMetadataHeaders(Z)}}else if(this.hasAnyScopes()||this.apiKey)return super.getRequestMetadataAsync(A);else return{headers:{}}}async fetchIdToken(A){let B=new NzB.GoogleToken({iss:this.email,sub:this.subject,scope:this.scopes||this.defaultScopes,keyFile:this.keyFile,key:this.key,additionalClaims:{target_audience:A},transporter:this.transporter});if(await B.getToken({forceRefresh:!0}),!B.idToken)throw new Error("Unknown error: Failed to fetch ID token");return B.idToken}hasUserScopes(){if(!this.scopes)return!1;return this.scopes.length>0}hasAnyScopes(){if(this.scopes&&this.scopes.length>0)return!0;if(this.defaultScopes&&this.defaultScopes.length>0)return!0;return!1}authorize(A){if(A)this.authorizeAsync().then((B)=>A(null,B),A);else return this.authorizeAsync()}async authorizeAsync(){let A=await this.refreshToken();if(!A)throw new Error("No result returned");return this.credentials=A.tokens,this.credentials.refresh_token="jwt-placeholder",this.key=this.gtoken.key,this.email=this.gtoken.iss,A.tokens}async refreshTokenNoCache(A){let B=this.createGToken(),D={access_token:(await B.getToken({forceRefresh:this.isTokenExpiring()})).access_token,token_type:"Bearer",expiry_date:B.expiresAt,id_token:B.idToken};return this.emit("tokens",D),{res:null,tokens:D}}createGToken(){if(!this.gtoken)this.gtoken=new NzB.GoogleToken({iss:this.email,sub:this.subject,scope:this.scopes||this.defaultScopes,keyFile:this.keyFile,key:this.key,additionalClaims:this.additionalClaims,transporter:this.transporter});return this.gtoken}fromJSON(A){if(!A)throw new Error("Must pass in a JSON object containing the service account auth settings.");if(!A.client_email)throw new Error("The incoming JSON object does not contain a client_email field");if(!A.private_key)throw new Error("The incoming JSON object does not contain a private_key field");this.email=A.client_email,this.key=A.private_key,this.keyId=A.private_key_id,this.projectId=A.project_id,this.quotaProjectId=A.quota_project_id,this.universeDomain=A.universe_domain||this.universeDomain}fromStream(A,B){if(B)this.fromStreamAsync(A).then(()=>B(),B);else return this.fromStreamAsync(A)}fromStreamAsync(A){return new Promise((B,Q)=>{if(!A)throw new Error("Must pass in a stream containing the service account auth settings.");let D="";A.setEncoding("utf8").on("error",Q).on("data",(Z)=>D+=Z).on("end",()=>{try{let Z=JSON.parse(D);this.fromJSON(Z),B()}catch(Z){Q(Z)}})})}fromAPIKey(A){if(typeof A!=="string")throw new Error("Must provide an API Key string.");this.apiKey=A}async getCredentials(){if(this.key)return{private_key:this.key,client_email:this.email};else if(this.keyFile){let B=await this.createGToken().getCredentials(this.keyFile);return{private_key:B.privateKey,client_email:B.clientEmail}}throw new Error("A key or a keyFile must be provided to getCredentials.")}}LzB.JWT=kE0});
var yqA=E((jqA)=>{Object.defineProperty(jqA,"__esModule",{value:!0});jqA.default=void 0;var B$Q=Q$Q(J1("crypto"));function Q$Q(A){return A&&A.__esModule?A:{default:A}}var D$Q={randomUUID:B$Q.default.randomUUID};jqA.default=D$Q});
var yz0=E((ra6)=>{var gV=sy1(),x3=oy1(),aa6=YCB(),JCB=x3.newObjectInRealm,QG=x3.implSymbol,XCB=x3.ctorRegistrySymbol;ra6.is=(A)=>{return x3.isObject(A)&&x3.hasOwn(A,QG)&&A[QG]instanceof hx.implementation};ra6.isImpl=(A)=>{return x3.isObject(A)&&A instanceof hx.implementation};ra6.convert=(A,B,{context:Q="The provided value"}={})=>{if(ra6.is(B))return x3.implForWrapper(B);throw new A.TypeError(`${Q} is not of type 'URLSearchParams'.`)};ra6.createDefaultIterator=(A,B,Q)=>{let Z=A[XCB]["URLSearchParams Iterator"],G=Object.create(Z);return Object.defineProperty(G,x3.iterInternalSymbol,{value:{target:B,kind:Q,index:0},configurable:!0}),G};function VCB(A,B){let Q;if(B!==void 0)Q=B.prototype;if(!x3.isObject(Q))Q=A[XCB].URLSearchParams.prototype;return Object.create(Q)}ra6.create=(A,B,Q)=>{let D=VCB(A);return ra6.setup(D,A,B,Q)};ra6.createImpl=(A,B,Q)=>{let D=ra6.create(A,B,Q);return x3.implForWrapper(D)};ra6._internalSetup=(A,B)=>{};ra6.setup=(A,B,Q=[],D={})=>{if(D.wrapper=A,ra6._internalSetup(A,B),Object.defineProperty(A,QG,{value:new hx.implementation(B,Q,D),configurable:!0}),A[QG][x3.wrapperSymbol]=A,hx.init)hx.init(A[QG]);return A};ra6.new=(A,B)=>{let Q=VCB(A,B);if(ra6._internalSetup(Q,A),Object.defineProperty(Q,QG,{value:Object.create(hx.implementation.prototype),configurable:!0}),Q[QG][x3.wrapperSymbol]=Q,hx.init)hx.init(Q[QG]);return Q[QG]};var sa6=new Set(["Window","Worker"]);ra6.install=(A,B)=>{if(!B.some((Z)=>sa6.has(Z)))return;let Q=x3.initCtorRegistry(A);class D{constructor(){let Z=[];{let G=arguments[0];if(G!==void 0)if(x3.isObject(G))if(G[Symbol.iterator]!==void 0)if(!x3.isObject(G))throw new A.TypeError("Failed to construct 'URLSearchParams': parameter 1 sequence is not an iterable object.");else{let F=[],I=G;for(let Y of I){if(!x3.isObject(Y))throw new A.TypeError("Failed to construct 'URLSearchParams': parameter 1 sequence's element is not an iterable object.");else{let W=[],J=Y;for(let X of J)X=gV.USVString(X,{context:"Failed to construct 'URLSearchParams': parameter 1 sequence's element's element",globals:A}),W.push(X);Y=W}F.push(Y)}G=F}else if(!x3.isObject(G))throw new A.TypeError("Failed to construct 'URLSearchParams': parameter 1 record is not an object.");else{let F=Object.create(null);for(let I of Reflect.ownKeys(G)){let Y=Object.getOwnPropertyDescriptor(G,I);if(Y&&Y.enumerable){let W=I;W=gV.USVString(W,{context:"Failed to construct 'URLSearchParams': parameter 1 record's key",globals:A});let J=G[I];J=gV.USVString(J,{context:"Failed to construct 'URLSearchParams': parameter 1 record's value",globals:A}),F[W]=J}}G=F}else G=gV.USVString(G,{context:"Failed to construct 'URLSearchParams': parameter 1",globals:A});else G="";Z.push(G)}return ra6.setup(Object.create(new.target.prototype),A,Z)}append(Z,G){let F=this!==null&&this!==void 0?this:A;if(!ra6.is(F))throw new A.TypeError("'append' called on an object that is not a valid instance of URLSearchParams.");if(arguments.length<2)throw new A.TypeError(`Failed to execute 'append' on 'URLSearchParams': 2 arguments required, but only ${arguments.length} present.`);let I=[];{let Y=arguments[0];Y=gV.USVString(Y,{context:"Failed to execute 'append' on 'URLSearchParams': parameter 1",globals:A}),I.push(Y)}{let Y=arguments[1];Y=gV.USVString(Y,{context:"Failed to execute 'append' on 'URLSearchParams': parameter 2",globals:A}),I.push(Y)}return x3.tryWrapperForImpl(F[QG].append(...I))}delete(Z){let G=this!==null&&this!==void 0?this:A;if(!ra6.is(G))throw new A.TypeError("'delete' called on an object that is not a valid instance of URLSearchParams.");if(arguments.length<1)throw new A.TypeError(`Failed to execute 'delete' on 'URLSearchParams': 1 argument required, but only ${arguments.length} present.`);let F=[];{let I=arguments[0];I=gV.USVString(I,{context:"Failed to execute 'delete' on 'URLSearchParams': parameter 1",globals:A}),F.push(I)}{let I=arguments[1];if(I!==void 0)I=gV.USVString(I,{context:"Failed to execute 'delete' on 'URLSearchParams': parameter 2",globals:A});F.push(I)}return x3.tryWrapperForImpl(G[QG].delete(...F))}get(Z){let G=this!==null&&this!==void 0?this:A;if(!ra6.is(G))throw new A.TypeError("'get' called on an object that is not a valid instance of URLSearchParams.");if(arguments.length<1)throw new A.TypeError(`Failed to execute 'get' on 'URLSearchParams': 1 argument required, but only ${arguments.length} present.`);let F=[];{let I=arguments[0];I=gV.USVString(I,{context:"Failed to execute 'get' on 'URLSearchParams': parameter 1",globals:A}),F.push(I)}return G[QG].get(...F)}getAll(Z){let G=this!==null&&this!==void 0?this:A;if(!ra6.is(G))throw new A.TypeError("'getAll' called on an object that is not a valid instance of URLSearchParams.");if(arguments.length<1)throw new A.TypeError(`Failed to execute 'getAll' on 'URLSearchParams': 1 argument required, but only ${arguments.length} present.`);let F=[];{let I=arguments[0];I=gV.USVString(I,{context:"Failed to execute 'getAll' on 'URLSearchParams': parameter 1",globals:A}),F.push(I)}return x3.tryWrapperForImpl(G[QG].getAll(...F))}has(Z){let G=this!==null&&this!==void 0?this:A;if(!ra6.is(G))throw new A.TypeError("'has' called on an object that is not a valid instance of URLSearchParams.");if(arguments.length<1)throw new A.TypeError(`Failed to execute 'has' on 'URLSearchParams': 1 argument required, but only ${arguments.length} present.`);let F=[];{let I=arguments[0];I=gV.USVString(I,{context:"Failed to execute 'has' on 'URLSearchParams': parameter 1",globals:A}),F.push(I)}{let I=arguments[1];if(I!==void 0)I=gV.USVString(I,{context:"Failed to execute 'has' on 'URLSearchParams': parameter 2",globals:A});F.push(I)}return G[QG].has(...F)}set(Z,G){let F=this!==null&&this!==void 0?this:A;if(!ra6.is(F))throw new A.TypeError("'set' called on an object that is not a valid instance of URLSearchParams.");if(arguments.length<2)throw new A.TypeError(`Failed to execute 'set' on 'URLSearchParams': 2 arguments required, but only ${arguments.length} present.`);let I=[];{let Y=arguments[0];Y=gV.USVString(Y,{context:"Failed to execute 'set' on 'URLSearchParams': parameter 1",globals:A}),I.push(Y)}{let Y=arguments[1];Y=gV.USVString(Y,{context:"Failed to execute 'set' on 'URLSearchParams': parameter 2",globals:A}),I.push(Y)}return x3.tryWrapperForImpl(F[QG].set(...I))}sort(){let Z=this!==null&&this!==void 0?this:A;if(!ra6.is(Z))throw new A.TypeError("'sort' called on an object that is not a valid instance of URLSearchParams.");return x3.tryWrapperForImpl(Z[QG].sort())}toString(){let Z=this!==null&&this!==void 0?this:A;if(!ra6.is(Z))throw new A.TypeError("'toString' called on an object that is not a valid instance of URLSearchParams.");return Z[QG].toString()}keys(){if(!ra6.is(this))throw new A.TypeError("'keys' called on an object that is not a valid instance of URLSearchParams.");return ra6.createDefaultIterator(A,this,"key")}values(){if(!ra6.is(this))throw new A.TypeError("'values' called on an object that is not a valid instance of URLSearchParams.");return ra6.createDefaultIterator(A,this,"value")}entries(){if(!ra6.is(this))throw new A.TypeError("'entries' called on an object that is not a valid instance of URLSearchParams.");return ra6.createDefaultIterator(A,this,"key+value")}forEach(Z){if(!ra6.is(this))throw new A.TypeError("'forEach' called on an object that is not a valid instance of URLSearchParams.");if(arguments.length<1)throw new A.TypeError("Failed to execute 'forEach' on 'iterable': 1 argument required, but only 0 present.");Z=aa6.convert(A,Z,{context:"Failed to execute 'forEach' on 'iterable': The callback provided as parameter 1"});let G=arguments[1],F=Array.from(this[QG]),I=0;while(I<F.length){let[Y,W]=F[I].map(x3.tryWrapperForImpl);Z.call(G,W,Y,this),F=Array.from(this[QG]),I++}}get size(){let Z=this!==null&&this!==void 0?this:A;if(!ra6.is(Z))throw new A.TypeError("'get size' called on an object that is not a valid instance of URLSearchParams.");return Z[QG].size}}Object.defineProperties(D.prototype,{append:{enumerable:!0},delete:{enumerable:!0},get:{enumerable:!0},getAll:{enumerable:!0},has:{enumerable:!0},set:{enumerable:!0},sort:{enumerable:!0},toString:{enumerable:!0},keys:{enumerable:!0},values:{enumerable:!0},entries:{enumerable:!0},forEach:{enumerable:!0},size:{enumerable:!0},[Symbol.toStringTag]:{value:"URLSearchParams",configurable:!0},[Symbol.iterator]:{value:D.prototype.entries,configurable:!0,writable:!0}}),Q.URLSearchParams=D,Q["URLSearchParams Iterator"]=Object.create(Q["%IteratorPrototype%"],{[Symbol.toStringTag]:{configurable:!0,value:"URLSearchParams Iterator"}}),x3.define(Q["URLSearchParams Iterator"],{next(){let Z=this&&this[x3.iterInternalSymbol];if(!Z)throw new A.TypeError("next() called on a value that is not a URLSearchParams iterator object");let{target:G,kind:F,index:I}=Z,Y=Array.from(G[QG]),W=Y.length;if(I>=W)return JCB(A,{value:void 0,done:!0});let J=Y[I];return Z.index=I+1,JCB(A,x3.iteratorResult(J.map(x3.tryWrapperForImpl),F))}}),Object.defineProperty(A,"URLSearchParams",{configurable:!0,writable:!0,value:D})};var hx=WCB()});
var zCB=E((Ds6)=>{var I3=Pz0(),HCB=Sz0(),Qs6=yz0();Ds6.implementation=class A{constructor(B,[Q,D]){let Z=null;if(D!==void 0){if(Z=I3.basicURLParse(D),Z===null)throw new TypeError(`Invalid base URL: ${D}`)}let G=I3.basicURLParse(Q,{baseURL:Z});if(G===null)throw new TypeError(`Invalid URL: ${Q}`);let F=G.query!==null?G.query:"";this._url=G,this._query=Qs6.createImpl(B,[F],{doNotStripQMark:!0}),this._query._url=this}static parse(B,Q,D){try{return new A(B,[Q,D])}catch{return null}}static canParse(B,Q){let D=null;if(Q!==void 0){if(D=I3.basicURLParse(Q),D===null)return!1}if(I3.basicURLParse(B,{baseURL:D})===null)return!1;return!0}get href(){return I3.serializeURL(this._url)}set href(B){let Q=I3.basicURLParse(B);if(Q===null)throw new TypeError(`Invalid URL: ${B}`);this._url=Q,this._query._list.splice(0);let{query:D}=Q;if(D!==null)this._query._list=HCB.parseUrlencodedString(D)}get origin(){return I3.serializeURLOrigin(this._url)}get protocol(){return`${this._url.scheme}:`}set protocol(B){I3.basicURLParse(`${B}:`,{url:this._url,stateOverride:"scheme start"})}get username(){return this._url.username}set username(B){if(I3.cannotHaveAUsernamePasswordPort(this._url))return;I3.setTheUsername(this._url,B)}get password(){return this._url.password}set password(B){if(I3.cannotHaveAUsernamePasswordPort(this._url))return;I3.setThePassword(this._url,B)}get host(){let B=this._url;if(B.host===null)return"";if(B.port===null)return I3.serializeHost(B.host);return`${I3.serializeHost(B.host)}:${I3.serializeInteger(B.port)}`}set host(B){if(I3.hasAnOpaquePath(this._url))return;I3.basicURLParse(B,{url:this._url,stateOverride:"host"})}get hostname(){if(this._url.host===null)return"";return I3.serializeHost(this._url.host)}set hostname(B){if(I3.hasAnOpaquePath(this._url))return;I3.basicURLParse(B,{url:this._url,stateOverride:"hostname"})}get port(){if(this._url.port===null)return"";return I3.serializeInteger(this._url.port)}set port(B){if(I3.cannotHaveAUsernamePasswordPort(this._url))return;if(B==="")this._url.port=null;else I3.basicURLParse(B,{url:this._url,stateOverride:"port"})}get pathname(){return I3.serializePath(this._url)}set pathname(B){if(I3.hasAnOpaquePath(this._url))return;this._url.path=[],I3.basicURLParse(B,{url:this._url,stateOverride:"path start"})}get search(){if(this._url.query===null||this._url.query==="")return"";return`?${this._url.query}`}set search(B){let Q=this._url;if(B===""){Q.query=null,this._query._list=[];return}let D=B[0]==="?"?B.substring(1):B;Q.query="",I3.basicURLParse(D,{url:Q,stateOverride:"query"}),this._query._list=HCB.parseUrlencodedString(D)}get searchParams(){return this._query}get hash(){if(this._url.fragment===null||this._url.fragment==="")return"";return`#${this._url.fragment}`}set hash(B){if(B===""){this._url.fragment=null;return}let Q=B[0]==="#"?B.substring(1):B;this._url.fragment="",I3.basicURLParse(Q,{url:this._url,stateOverride:"fragment"})}toJSON(){return this.href}}});
var zE0=E((SHB)=>{Object.defineProperty(SHB,"__esModule",{value:!0});SHB.IdTokenClient=void 0;var oo6=$m();class PHB extends oo6.OAuth2Client{constructor(A){super(A);this.targetAudience=A.targetAudience,this.idTokenProvider=A.idTokenProvider}async getRequestMetadataAsync(A){if(!this.credentials.id_token||!this.credentials.expiry_date||this.isTokenExpiring()){let Q=await this.idTokenProvider.fetchIdToken(this.targetAudience);this.credentials={id_token:Q,expiry_date:this.getIdTokenExpiryDate(Q)}}return{headers:{Authorization:"Bearer "+this.credentials.id_token}}}getIdTokenExpiryDate(A){let B=A.split(".")[1];if(B)return JSON.parse(Buffer.from(B,"base64").toString("ascii")).exp*1000}}SHB.IdTokenClient=PHB});
var zqA=E((KqA)=>{Object.defineProperty(KqA,"__esModule",{value:!0});KqA.default=void 0;var kwQ=_wQ(Ae1()),ywQ=aQ1();function _wQ(A){return A&&A.__esModule?A:{default:A}}var CqA,Be1,Qe1=0,De1=0;function xwQ(A,B,Q){let D=B&&Q||0,Z=B||new Array(16);A=A||{};let G=A.node||CqA,F=A.clockseq!==void 0?A.clockseq:Be1;if(G==null||F==null){let V=A.random||(A.rng||kwQ.default)();if(G==null)G=CqA=[V[0]|1,V[1],V[2],V[3],V[4],V[5]];if(F==null)F=Be1=(V[6]<<8|V[7])&16383}let I=A.msecs!==void 0?A.msecs:Date.now(),Y=A.nsecs!==void 0?A.nsecs:De1+1,W=I-Qe1+(Y-De1)/1e4;if(W<0&&A.clockseq===void 0)F=F+1&16383;if((W<0||I>Qe1)&&A.nsecs===void 0)Y=0;if(Y>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");Qe1=I,De1=Y,Be1=F,I+=12219292800000;let J=((I&268435455)*1e4+Y)%**********;Z[D++]=J>>>24&255,Z[D++]=J>>>16&255,Z[D++]=J>>>8&255,Z[D++]=J&255;let X=I/***********1e4&268435455;Z[D++]=X>>>8&255,Z[D++]=X&255,Z[D++]=X>>>24&15|16,Z[D++]=X>>>16&255,Z[D++]=F>>>8|128,Z[D++]=F&255;for(let V=0;V<6;++V)Z[D+V]=G[V];return B||ywQ.unsafeStringify(Z)}var vwQ=xwQ;KqA.default=vwQ});

module.exports = oEB;
